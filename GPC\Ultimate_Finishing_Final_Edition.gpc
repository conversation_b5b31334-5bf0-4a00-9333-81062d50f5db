
// ============  Ultimate Finishing Script ============//
//HOW it WORKs  :
//1-  when you are inside the 18 yards box of your opponent press Shot button and direction where you aim ,  
//    while nothing else pressed , like  no sprnt or L2 or L1 ..etc .
//2-  it will generate fixed power for inside box finishing .
//3-  <PERSON><PERSON><PERSON> will modify your aiming and lock it to the corner where you aim to grant a perfect aim as possible .

//4-  <PERSON><PERSON><PERSON> won't engage in case of Head and vollays , it will detect whenever you cross the ball and stop , 
//    because vollays needs more freedom , so you are completly on your own .
//4-  Long Shots designed not to be too long , just activate it around the box so you can get perfect results
//    all you need to do is press FinesseShot Button + Shot Button .
//    it will perform Finesse Shot with perfect power and aim assist that you need to grant scoring as much as possible .
//5-  Please Note that this is not a hack that will help you score each time . ENJOY !!
  
  //==================================================// 
  
 //IMPORTANT // MAKE SURE NO Shooting SCRIPTS Generated From FIFA Generator // 
 //==========================================================================//
 
 
 
   define CrossingBtn =               //Please Add the button you cross the ball with , in my case I use Circle PS4 , so it's PS4_CIRCLE
   
    
   //============ COPY THis just BEFORE the END MAIN SECTION ============//

        if(event_release(CrossingBtn)){
           
            combo_run(AFTER_CROSS);
 
         }
         
         
         
         
	 if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
	 if( event_press(ShotBtn) && !combo_running(AFTER_CROSS) ){
		    
     
            
            set_val(ShotBtn,0);
			combo_restart(Inside_Box_Finishing); 
	
	
	}
	}   
	
         
	
  	
  // ============ END OF COPY TO MAIN SECTION ============ //
  
  
  
  
   // ============ COPY THis TO COMBOS SECTION ============ //
   
   
   combo AFTER_CROSS { 
wait(4000);
}

   
  

combo OutSide_Box_Shoot {

    set_val(PS4_R3,100);
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(170);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, 100);
    wait(17);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    wait(15);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);            
}       


 combo Inside_Box_Finishing {

    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(211);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600); 
    

} 



 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }
