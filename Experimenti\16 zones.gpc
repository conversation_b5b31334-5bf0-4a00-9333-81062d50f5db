define MAX_RADIUS = 32767;
define REDUCED_RADIUS = 30000;

int radius;
int max_allowed_radius;

main {
    pass();
}

function pass() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
    // Check if either LT or RT is pressed
    if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
        max_allowed_radius = MAX_RADIUS;
    } else {
        max_allowed_radius = REDUCED_RADIUS;
    }
    
    set_polar(POLAR_LS, 
        quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
        min(calculate_radius(), max_allowed_radius) // Ensure radius is within allowed limit
    );
}

define AngleInterval_2 = 8;    // Changed from 12 to 30 for 16 zones
// This defines the number of zones.  A value of 16 creates 16 zones.

function quantize_angle(angle, interval) {
    // This function quantizes the angle into discrete zones.
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)); // Calculate radius using Pythagorean theorem
}