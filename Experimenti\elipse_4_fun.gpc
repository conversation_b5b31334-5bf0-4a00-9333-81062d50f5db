// Constants for stick identification
define stickX = XB1_LX;
define stickY = XB1_LY;

// Ellipse ratios
define X_RATIO = 100;
define Y_RATIO = 40;

int x, y;
int last_x = 0;    // Remember last stick position X
int last_y = 0;    // Remember last stick position Y
int cos_a;
int sin_a;
int rx;
int ry;

// Zone-based rotation matrix components
const int8 cos_table[] = {100, 71, 0, -71, -100, -71, 0, 71};
const int8 sin_table[] = {0, 71, 100, 71, 0, -71, -100, -71};

// Function to determine zone (0-7)
function get_zone(int x, int y) {
    if (abs(x) > abs(y) * 2) {
        if (x > 0) return 0;
        else return 4;
    } 
    else if (abs(y) > abs(x) * 2) {
        if (y > 0) return 2;
        else return 6;
    }
    else {
        if (x > 0) {
            if (y > 0) return 1;
            else return 7;
        }
        else {
            if (y > 0) return 3;
            else return 5;
        }
    }
    return 0;  // Add final fallback return
}
int zone;
function process_stick_movement() {
    x = get_val(stickX);
    y = get_val(stickY);
    
    if (abs(x) > 20 || abs(y) > 20) {
        zone = get_zone(x, y);
        cos_a = cos_table[zone];
        sin_a = sin_table[zone];
        
        // Elliptical scaling calculations
        rx = (x * cos_a + y * sin_a) / 100;
        ry = (-x * sin_a + y * cos_a) / 100;
        rx = (rx * X_RATIO) / 100;
        ry = (ry * Y_RATIO) / 100;
        x = (rx * cos_a - ry * sin_a) / 100;
        y = (rx * sin_a + ry * cos_a) / 100;
        
        // Store last position
        last_x = x;
        last_y = y;
    }
    else {
        // When stick is centered, output neutral values
        x = 0;
        y = 0;
    }
    
    set_val(stickX, x);
    set_val(stickY, y);
}

main {
    process_stick_movement();
} 