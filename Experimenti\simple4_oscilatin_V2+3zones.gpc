const string INFO1  = "SIMPLE 3";
const string INFO2 = "Minimal script";
const string LEFTP  = "PING";
const string RIGHTP = "AI ANGLE";
const string PHASEP = "PHASE";  // Add this line for phase display
const string ANGLEP = "ANGLE";  // Add this line for angle display
const string RADIUSP = "BASE";

int radius_base = 100;  // Starting value for base radius percentage

int invertActive; // Track if inversion is active
// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;
int virtmach = -9;

int leftStickRadius;
int rightStickRadius;
int targetRadius;
int baseRadius = 28701;
int randomVariation;
int fullRadius = 32767;

// Variables for random radius control
int random_update_timer;
int current_random_radius;
define REDUCED_RADIUS = 32767;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;
int radius;
int current_angle;
int max_allowed_radius;
int max_possible;

int AngleInterval = 8;
int AI_VALUES[5];
int AI_VALUES_COUNT;
int current_index = 0;

// Virtual Machine Speed
init {
    AI_VALUES[0] = 8;
    AI_VALUES[1] = 12;
    AI_VALUES[2] = 16;
    AI_VALUES[3] = 8;
    AI_VALUES[4] = 12;
    AI_VALUES_COUNT = 5;
}
init {
    AI_VALUES_COUNT = sizeof(AI_VALUES) / sizeof(AI_VALUES[0]);
}

// Function to quantize angle into discrete zones
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

// Calculate radius using Pythagorean theorem
function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2));
}

int phase_counter = 0;
int oscillation_factor;
define PHASE_MULTIPLIER = 10;
int phase_speed = 10;
int actual_phase;
int current_base;
int oscillation_range;

main {

    // Update the random timer
    if(random_update_timer) {
        random_update_timer -= get_rtime();
    }

    vm_tctrl(virtmach)
    // When RB is pressed
    if(get_val(XB1_LS)) {
        // Get current left stick angle and invert it
        set_polar(POLAR_LS, -180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
        invertActive = TRUE;
    } else {
        // When RB is released, stop modifying the stick
        if(invertActive) {
          set_polar(POLAR_LS, 0, 0);
            set_val(XB1_RB, 0);
            invertActive = FALSE;
        }
    }

    // Reset shared values at the start of each loop iteration
    rb_val = 0;
    lb_val = 0;

    // Only handle X and Y and A buttons if triggers are not pressed
    if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
        handle_rb_related_button(XB1_X);
        handle_rb_related_button(XB1_Y);
        handle_lb_related_button(XB1_A);
    }

    // **Directly handle XB1_RB press**
    if(get_ival(XB1_RB)) {
        rb_val = 100; // Activate XB1_RB directly
    }
    // **Directly handle XB1_LB press**
    if(get_ival(XB1_LB)) {
        lb_val = 100; // Activate XB1_LB directly
    }
    
    // Set shared buttons based on aggregated values
    set_val(XB1_RB, rb_val);
    set_val(XB1_LB, lb_val);
    



    pass();

// Finesse LB+B
if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
}

// Finesse with R3
if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
}

// Shots with B
if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);
		}
}
	
// Shots with RB+B
if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);
		}
}

if(get_ival(XB1_RT)){
        if(event_press(XB1_RIGHT)) {
            current_index = (current_index + 1) % AI_VALUES_COUNT;
            AngleInterval = AI_VALUES[current_index];
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), ANGLEP[0], AngleInterval);
            //print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
        }
        if(event_press(XB1_LEFT)) {
            current_index = (current_index - 1 + AI_VALUES_COUNT) % AI_VALUES_COUNT;
            AngleInterval = AI_VALUES[current_index];
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), ANGLEP[0], AngleInterval);
            //print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
}

if(get_ival(XB1_LT)){
        if(event_press(XB1_UP) && phase_speed < 20) {
            phase_speed += 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), PHASEP[0], phase_speed);
        }
        if(event_press(XB1_DOWN) && phase_speed > 1) {
            phase_speed -= 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), PHASEP[0], phase_speed);
        }
        set_val(PS4_UP, 0);
        set_val(PS4_DOWN, 0);
    
        if(event_press(XB1_RIGHT) && radius_base < 99) {
            radius_base += 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), RADIUSP[0], radius_base);
        }
        if(event_press(XB1_LEFT) && radius_base > 1) {
            radius_base -= 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), RADIUSP[0], radius_base);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
}

}

function pass() {
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
    if (radius < 2400) {
        set_polar(POLAR_LS, 0, 0);
        set_val(XB1_LB, 0);
    } else if (radius >= 2400 && radius < 8000){
    set_val(XB1_LB, 100);
        current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval);
        
        phase_counter = (phase_counter + phase_speed) % (360 * PHASE_MULTIPLIER);
        actual_phase = phase_counter / PHASE_MULTIPLIER;
        
        oscillation_factor = actual_phase;
        if (oscillation_factor > 180) {
            oscillation_factor = 360 - oscillation_factor;
        }
        oscillation_factor = ((oscillation_factor * 200) / 180) - 100;  // Convert to -100 to +100 range
        
        // Calculate 14% of current radius for oscillation range
        oscillation_range = (radius * 14) / 100;
        
        // Oscillate between current radius and (current radius - 14%)
        current_random_radius = radius * 3 - ((oscillation_range * (oscillation_factor - 220)) / 200);
        //current_random_radius = (radius * 3) + ((oscillation_range * oscillation_factor) / 100);
        
        if (current_random_radius > MAX_RADIUS) {
            current_random_radius = MAX_RADIUS;
        }
        if (current_random_radius < 0) {
            current_random_radius = 0;
        }
        
        set_polar(POLAR_LS, current_angle, current_random_radius);
    }

    else if (radius >= 8000){
        current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2);
        
        phase_counter = (phase_counter + phase_speed) % (360 * PHASE_MULTIPLIER);
        actual_phase = phase_counter / PHASE_MULTIPLIER;
        
        oscillation_factor = actual_phase;
        if (oscillation_factor > 180) {
            oscillation_factor = 360 - oscillation_factor;
        }
        oscillation_factor = ((oscillation_factor * 200) / 180) - 100;  // Convert to -100 to +100 range
        
        // Calculate 14% of current radius for oscillation range
        oscillation_range = (radius * 14) / 100;
        
        // Oscillate between current radius and (current radius - 14%)
        current_random_radius = radius * 3 - ((oscillation_range * (oscillation_factor - 220)) / 200);
        //current_random_radius = (radius * 3) + ((oscillation_range * oscillation_factor) / 100);
        
        if (current_random_radius > MAX_RADIUS) {
            current_random_radius = MAX_RADIUS;
        }
        if (current_random_radius < 0) {
            current_random_radius = 0;
        }
        
        set_polar(POLAR_LS, current_angle, current_random_radius);
    }
}

function handle_rb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            rb_val = 100; // Activate XB1_RB
            combo_run (stop_lb);
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

combo stop_lb {
	wait(100);
	set_val(XB1_LB, 0);
	wait(100);
}

function handle_lb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            lb_val = 100; // Activate XB1_LB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;

combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

int UltimatePower;

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function getPolar(Stick, AngleOrRadius) {
  if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
  return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));   
}

//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================

function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 

combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}

/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}

int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

int data_indx;

/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
}

int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
}

function set_ds4_led(colour) {
    set_led(LED_1, duint8 (colour * 4));
    set_led(LED_2, duint8 ((colour * 4) + 1));
    set_led(LED_3, duint8 ((colour * 4) + 2));
    set_led(LED_4, duint8 ((colour * 4) + 3));
}

int KS_EntireScript = FALSE;
function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}

function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);  
}

const string OFF   = "OFF";
const string ON    = "ON"; 