define None = 0;
define FA<PERSON>_SHOT_SKILL = 1;
define HEEL_TO_HEEL_FLICK_SKILL = 2;
define HEEL_FLICK_TURN_SKILL = 3;
define RAINBOW_SKILL = 4;
define DRAG_BACK_SOMBRERO_SKILL = 5;
define FAKE_PASS_SKILL = 6;
define DRAG_BACK_UNIVERSAL_SKILL = 7;
define STEP_OVER_FEINT_SKILL = 8;
define DRAG_TO_DRAG_SKILL = 9;
define HOCUS_POCUS_SKILL = 10;
define TRIPLE_ELASTICO_SKILL = 11;
define ELASTICO_SKILL = 12;
define REVERSE_ELASTICO_SKILL = 13;
define CRUYFF_TURN_SKILL = 14;
define LA_CROQUETA_SKILL = 15;
define RONALDO_CHOP_SKILL = 16;
define ROULETTE_SKILL = 17;
define FLAIR_ROULETTE_SKILL = 18;
define BALL_ROLL_SKILL = 19;
define BERBA_MCGEADY_SPIN_SKILL = 20;
define B<PERSON>ASIE_FLICK_SKILL = 21;
define TORNADO_SKILL = 22;
define THREE_TOUCH_ROULETTE_SKILL = 23;
define ALTERNATIVE_ELASTICO_CHOP_SKILL = 24;
define BALL_ROLL_CHOP_SKILL = 25;
define FEINT_AND_EXIT_SKILL = 26;
define FEINT_L_EXIT_R_SKILL = 27;
define LATERAL_HEEL_TO_HEEL_SKILL = 28;
define WAKA_WAKA_SKILL = 29;
define BODY_FEINT_SKILL = 30;
define DRAG_TO_HEEL = 31;
define BALL_ROLL_FAKE_TURN = 32;
define FEINT_FORWARD_AND_TURN = 33;
define TURN_BACK = 34;
define ADVANCED_CROQUETA = 35;
define CANCELED_THREE_TOUCH_ROULETTE_SKILL = 36;
define REVERSE_STEP_OVER_SKILL = 37;
define FAKE_DRAG_BACK_SKILL = 38;
define RAINBOW_TO_SCORPION_KICK_SKILL = 39;
define STEP_OVER_BOOST_SKILL = 40;
define CANCEL_SHOOT_SKILL = 41;
define DIRECTIONAL_NUTMEG_SKILL = 42;
define CANCELED_BERBA_SPIN_SKILL = 43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION = 44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL = 45;
define DRIBBLING_SKILL = 46;
define FOUR_TOUCH_TURN_SKILLS = 47;
define SKILLED_BRIDGE_SKILL = 48;
define SCOOP_TURN_FAKE_SKILL = 49;
define BALL_ROLL_STEP_OVER_SKILL = 50;
define CANCELED_4_TOUCH_TURN_SKILL = 51;
define FAKE_SHOT_CANCEL_SKILL = 52;
define OKKOSHA_FLICK_SKILL = 53;
define ADVANCED_RAINBOW_SKILL = 54;
define STOP_LA_CROQUETA_SKILL = 55;
define JUGGLING_RAINBOW_SKILL = 56;
define STOP_NEYMAR_ROLL_SKILL = 57;
define STOP_V_DRAG_SKILL = 58;
define REV_OR_ELASTICO_SKILL = 59;
define STOP_REV_OR_ELASTICO_SKILL = 60;
define DRAG_REV_OR_ELASTICO_SKILL = 61;
define FAKE_RABONA_SKILL = 62;
define RABONA_TO_REV_ELASTICO_SKILL = 63;
define RABONA_TO_ELASTICO_SKILL = 64;
define SOMBRERO_FLICK_SKILL = 65;
define JUGGLE_BACK_SOMBRERO_SKILL = 66;
define FAKE_BERBA_OPP_EXIT_SKILL = 67;
define DIAGONAL_HEEL_CHOP_SKILL = 68;
define FAKE_BERBA_FAKE_DRAG_SKILL = 69;
define ELASTICO_CHOP_SKILL = 70;
define BALL_ROLL_CUT_180_SKILL = 71;
define HEEL_TO_BALL_ROLL_SKILL = 72;
define STUTTER_FEINT_SKILL = 73;
define JOG_OPENUP_FAKE_SHOT = 74;
define SPIN_MOVE_LEFT_RIGHT_SKILL = 75;

define MOVE_X = PS4_LX;
define MOVE_Y = PS4_LY;
define SKILL_STICK_X = PS4_RX;
define SKILL_STICK_Y = PS4_RY;


const int order[] = {2, 1, 0, 7, 6, 5, 4, 3};
int index;
int sector;


function calc_zone()
{
  polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
  index = ((polar_LS + 22.5) % 360) / 45;
  zone_p = order[index];
  return zone_p;
}


int polar_angle;
function calc_RS()
{
  polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
  index = ((polar_angle + 22.5) % 360) / 45;
  zone_RS = order[index];
  return zone_RS;
}

int flick_rs;
int temp_zone;
function calc_temp_zone(user_zone)
{
  temp_zone = user_zone;
  if (temp_zone < 0)
    temp_zone = 8 - abs(user_zone);
  else if (temp_zone >= 8)
    temp_zone = user_zone - 8 return temp_zone;
}

function calc_relative_xy(d)
{
  if (d < 0)
    d = 8 - abs(d);
  else if (d >= 8)
    d = d - 8;
  move_lx = ZONE_P[d][0]; // X
  move_ly = ZONE_P[d][1]; // Y
}

int Get_LS_Output = TRUE;
int LX, LY; // Direction of Left Stick
int right_on;

int RS_Skills_Up;      // 0--
int RS_Skills_UpLeft;  // 1
int RS_Skills_UpRight; // 2--
int RS_Skills_LEFT;    // 3
int RS_Skills_RIGHT;   // 4--
int RS_Skills_DownL;   // 5
int RS_Skills_DownR;   // 6--
int RS_Skills_Down;    // 7
int rs_new_way_btn;    // 8

int dEnd;
int w_rstick = 50;
int ACTIVE;
int Sombrero;
int hold_btn = 200;
int LS_BlockOutput;


int pressStartTime = 0;



define LEFT_ai = 1; // Constant to represent the left direction
define RIGHT_ai = 2; // Constant to represent the right direction

const string INFO1  = "GAMESIR_NOV";
const string INFO2 = "NERVOUS contr";
const string LEFTP  = "PING";
const string RIGHTP = "VM SPEED";
int modMenu,editMenu; 
int modNameIdx,valNameIdx;
int case_indic = 0;

/* Display Variables / ScreenSaver / Strings/Text  */
int screenSaver,blankScreen;
int displayTitle = TRUE;
int updateDisplay;

define Off = 0;
define Dim_Blue = 1;
define Dim_Red = 2;
define Dim_Green = 3;
define Dim_Pink = 4;
define Dim_SkyBlue = 5;
define Dim_Yellow = 6;
define Dim_White = 7;
define Blue = 8;
define Red = 9;
define Green = 10;
define Pink = 11;
define SkyBlue = 12;
define Yellow = 13;
define White = 14;
define Bright_Blue = 15;
define Bright_Red = 16;
define Bright_Green = 17;
define Bright_Pink = 18;
define Bright_SkyBlue = 19;
define Bright_Yellow = 20;
define Bright_White = 21;

//define MOVE_X        = PS4_LX;        
//define MOVE_Y        = PS4_LY;        
//define SKILL_STICK_X = PS4_RX;        
//define SKILL_STICK_Y = PS4_RY;  

data (
0,0,0,0, // Off
1,0,0,0, // Dim Blue
0,1,0,0, // Dim Red
0,0,1,0, // Dim Green
0,0,0,1, // Dim Pink
1,0,1,0, // Dim SkyBlue
0,1,1,0, // Dim Yellow
1,1,1,1, // Dim White
2,0,0,0, // Blue
0,2,0,0, // Red
0,0,2,0, // Green
0,0,0,2, // Pink
2,0,2,0, // SkyBlue
0,2,2,0, // Yellow
2,2,2,2, // White
3,0,0,0, // Bright Blue
0,3,0,0, // Bright Red
0,0,3,0, // Bright Green
0,0,0,3, // Bright Pink
3,0,3,0, // Bright SkyBlue
0,3,3,0, // Bright Yellow
3,3,3,3  // Bright white 
); 

//define AngleInterval = 8; // (360 / 15 Degrees) = 24
//define AngleInterval_2 = 12;

define Stick_Press_LB_Threshold = 95;
int stick_swap_toggle;
int rightStickMagnitude;
int UltimatePower;
int DYN_Acc;

int virtmach = -9;
int VM_VALUES[5];
int VM_VALUES_COUNT;
int current_index = 0;

// Virtual Machine Speed
init {
    VM_VALUES[0] = -9;
    VM_VALUES[1] = -8;
    VM_VALUES[2] = -6;
    VM_VALUES[3] = -2;
    VM_VALUES[4] = 0;
    VM_VALUES_COUNT = 5;
}
init {
    VM_VALUES_COUNT = sizeof(VM_VALUES) / sizeof(VM_VALUES[0]);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;
//const string dd = "dd_value"; 

function icos(x) { return isin(x + 8192); }
function isin(x) {
	x = (x % 32767) << 17;
	if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
	x = x >> 17;
	return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggle_active;
int cos_angle, sin_angle;
	// Increase the frequency of peaks and dips
	int multiplier = 24;


function getPolar(Stick, AngleOrRadius) {
  if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
  return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));   
}

int r, a, dd;

int Stop_Detector;
int Stop_Ran = 0;  // Integer flag to check if combo has already been run
int leftStickRadius;
int rightStickRadius;
int targetRadius;
int baseRadius = 28701;
int randomVariation;
int fullRadius = 32767;
int turboBtn;

define ShotBtn       = XB1_B;   // Shot Btn           (default B/CIRCLE 
define PassBtn       = XB1_A;   // Short Pass Btn     (default A/CROSS)
define PlayerRun     = XB1_RB;  // Player Run        (default L1/LB) 
define FinesseShot   = XB1_LB;  // Finesse Shot      (default R1/RB)
define PaceCtrol     = XB1_LT;  // Protect Ball      (default L2/LT)
define SprintBtn     = XB1_RT;  // Sprint Btn        (default R2/RT)
define CrossBtn      = XB1_X;   // Cross Btn          (default X/SQUARE)
define ThroughBall   = XB1_Y;   // Through Ball Btn   (default Y/TRIANGLE) 
int LS_Angle;

int LS_Angle2;
int LS_Direction2;

int combo_active;

define AngleInterval = 12;      // Changed from 24 to 30 for 12 zones
define AngleInterval_2 = 16;    // Changed from 12 to 30 for 16 zones
define RADIUS_THRESHOLD_1 = 3000;
define RADIUS_THRESHOLD_2 = 6000;
define RADIUS_THRESHOLD_2A = 9000;
define RADIUS_THRESHOLD_3 = 11000;
define MAX_RADIUS = 32767;
int radius;

// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;

// Function to handle buttons that influence XB1_RB (excluding XB1_RB itself)
function handle_rb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            rb_val = 100; // Activate XB1_RB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

// Function to handle buttons that influence XB1_LB (excluding XB1_LB itself)
function handle_lb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            lb_val = 100; // Activate XB1_LB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

function pass() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);

set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
            min(calculate_radius(), MAX_RADIUS)
        );
}

// Helper function to quantize the angle
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

// Helper function to calculate the radius
function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2));
}

int pressTime = 0;
int comboStage = 0;

// ------------------------------
// Stick Sensitivity Adjustment
// ------------------------------

// Define base sensitivity value as a percentage (e.g., 100 = 100% sensitivity)
// Set this value between 100 and 400
int sensitivityValue = 300;

// Variables for sensitivity calculations (Global Scope)
int val_s;      // Stores sign of input value (+1 or -1)
int _val;       // Stores the modified input value
int baseSen;    // Calculated sensitivity scaling factor
int midVal;     // Midpoint value for response curve

// ------------------------------
// Custom Sensitivity Function
// ------------------------------
function _sensitivity(int id, int mid, int sen) {
    // Retrieve current stick value for the specified axis
    _val = get_val(id);

    // Clamp the stick value to the safe range to prevent unexpected behavior
    if(_val > 32767) {
        _val = 32767;
    }
    else if(_val < -32768) {
        _val = -32768;
    }

    // Check if a midpoint is specified
    if(mid != NOT_USE) {
        // Determine the sign of the input value (+1 for positive, -1 for negative)
        if(_val >= 0) {
            val_s = 1;
        }
        else {
            val_s = -1;
        }

        // Work with the absolute value for scaling
        _val = _val * val_s;

        // Apply two-part response curve based on the midpoint
        if(_val <= mid) {
            // Below midpoint: Scale input linearly from 0 to midpoint
            _val = (_val * 16384) / mid;
        }
        else {
            // Above midpoint: Scale input linearly from midpoint to max
            _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
        }

        // Restore the original sign after scaling
        _val = _val * val_s;
    }

    // Apply the sensitivity multiplier if specified
    if(sen != NOT_USE) {
        // Multiply by 'sen' and divide to normalize
        _val = (_val * sen) / 16384;
    }

    // Clamp the modified stick value to ensure it stays within valid range
    if(_val > 32767) {
        _val = 32767;
    }
    else if(_val < -32768) {
        _val = -32768;
    }

    // Set the modified value back to the stick axis
    set_val(id, _val);
} 

define AA_Size = 30;
define AA_Interval = 30;
int invertActive; // Track if inversion is active

main {
/*
    // When RB is pressed
    if(get_val(XB1_LS)) {
        // Get current left stick angle and invert it
        set_polar(POLAR_LS, -180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
        set_val(XB1_RB, 100);
        invertActive = TRUE;
    } else {
        // When RB is released, stop modifying the stick
        if(invertActive) {
          set_polar(POLAR_LS, 0, 0);
            set_val(XB1_RB, 0);
            invertActive = FALSE;
        }
    }
*/





if (Get_LS_Output)
{
    if (abs(get_ival(PS4_LX)) > 50 || abs(get_ival(PS4_LY)) > 50)
        {
        calc_zone();
        LX = ZONE_P[zone_p][0];
        LY = ZONE_P[zone_p][1];
        }
          //======================================================
}

set_val(TRACE_2,zone_p);
set_val(TRACE_1,zone_RS);

f_RS_New_Way();

  if (RS_Skills_Up == 0 && RS_Skills_UpLeft == 0 && RS_Skills_UpRight == 0 && RS_Skills_LEFT == 0 && RS_Skills_RIGHT == 0 && RS_Skills_DownL == 0 && RS_Skills_DownR == 0 && RS_Skills_Down == 0)
  {
    // Variables have not been loaded, initialize them with specific values
    RS_Skills_Up = 8;
    RS_Skills_UpLeft = 17;
    RS_Skills_UpRight = 17;
    RS_Skills_LEFT = 17;
    RS_Skills_RIGHT = 17;
    RS_Skills_DownL = 17;
    RS_Skills_DownR = 17;
    RS_Skills_Down = 67;
  }
  

  // Changed condition to check for both XB1_LT and XB1_RB pressed together
  if(get_ival(XB1_LB) && get_ival(XB1_LB))
    combo_run(AIM_ASSIST_ABUSE);
  else 
    combo_stop(AIM_ASSIST_ABUSE);


    // Calculate 'baseSen' to prevent overflow
    // Ensures that 'baseSen' scales correctly based on 'sensitivityValue'
    baseSen = (sensitivityValue * 16384) / 100;

    // Midpoint value for linear response (half of max stick value 32767)
    midVal = 16384;

    // Apply custom sensitivity to all stick axes (Left X, Left Y, Right X, Right Y)
    _sensitivity(POLAR_LX, midVal, baseSen);
    _sensitivity(POLAR_LY, midVal, baseSen);
    _sensitivity(POLAR_RX, midVal, baseSen);
    _sensitivity(POLAR_RY, midVal, baseSen);
    
    // Reset shared values at the start of each loop iteration
    rb_val = 0;
    lb_val = 0;

    // Only handle X and Y and A buttons if triggers are not pressed
    if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
        handle_rb_related_button(XB1_X);
        handle_rb_related_button(XB1_Y);
        handle_lb_related_button(XB1_A);
    }

    // **Directly handle XB1_RB press**
    if(get_ival(XB1_RB)) {
        rb_val = 100; // Activate XB1_RB directly
    }

    // **Directly handle XB1_LB press**
    if(get_ival(XB1_LB)) {
        lb_val = 100; // Activate XB1_LB directly
    }

    // Set shared buttons based on aggregated values
    set_val(XB1_RB, rb_val);
    set_val(XB1_LB, lb_val);
    
//set_val(XB1_SHARE,0);
//pass();

    if(get_val(XB1_A)){
        pass();
    }
    
        if(get_val(XB1_Y)){
        pass();
    }

        if(get_val(XB1_X)){
        pass();
    }

// Change to Slot 2

if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (2);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (2);
      }
      set_val(XB1_RIGHT,0);
	}

    // Retrieve the current angle of the left stick in polar coordinates
    LS_Angle2 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
    
    // Retrieve the current direction of the left stick and negate it
    LS_Direction2 = get_polar(POLAR_LS, POLAR_ANGLE) * -1;

   if(toggle_active)        set_ds4_led(Green); 
   else if(!toggle_active)   set_ds4_led(Red);
   
    if(!modMenu && !editMenu){
        // Display The Title Screen When we Are NOT in any Menu s
        if(displayTitle){ 
            cls_oled(0);
            displayTitle = FALSE;
            screenSaver  = TRUE;
            print(centerPosition(getStringLength(INFO1[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , INFO1[0]);
            print(centerPosition(getStringLength(INFO2[0]) ,OLED_FONT_SMALL_WIDTH), 18  ,OLED_FONT_SMALL , OLED_WHITE , INFO2[0]);
        }
        }
        
vm_tctrl(virtmach)

// LED_Color(Blue);
 if(time_to_clear_screen){
    time_to_clear_screen -= get_rtime();
    if(time_to_clear_screen <= 0)combo_run(CLEAR_SCREEN);
}

if(get_ival(XB1_RT)){
        if(event_press(XB1_RIGHT)) {
            current_index = (current_index + 1) % VM_VALUES_COUNT;
            virtmach = VM_VALUES[current_index];
            print(centerPosition(getStringLength(RIGHTP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, RIGHTP[0]);
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), virtmach[0], virtmach);
        }
        if(event_press(XB1_LEFT)) {
            current_index = (current_index - 1 + VM_VALUES_COUNT) % VM_VALUES_COUNT;
            virtmach = VM_VALUES[current_index];
            print(centerPosition(getStringLength(RIGHTP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, RIGHTP[0]);
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), virtmach[0], virtmach);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
}

if(get_ival(XB1_LT)){                              
        if(event_press(XB1_RIGHT)) {
            fs_value +=1;
            print(centerPosition(getStringLength( LEFTP[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFTP[0]);
            on_the_fly_display(centerPosition(sizeof(fs_value)- 1,OLED_FONT_MEDIUM_WIDTH),fs_value[0],fs_value);
            }
        if(event_press(XB1_LEFT)) {
            fs_value -=1;
            print(centerPosition(getStringLength( LEFTP[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFTP[0]);
            on_the_fly_display(centerPosition(sizeof(fs_value)- 1,OLED_FONT_MEDIUM_WIDTH),fs_value[0],fs_value);
            }
                set_val(PS4_RIGHT,0);
                set_val(PS4_LEFT ,0);
}

if(get_ival(XB1_LB)){                              
    if(event_press(XB1_RIGHT)) {
        tbp_value +=10;
        on_the_fly_display(centerPosition(sizeof(tbp_value)- 10,OLED_FONT_MEDIUM_WIDTH),tbp_value[0],tbp_value);
    }
    if(event_press(XB1_LEFT)) {
        tbp_value -=10;
        on_the_fly_display(centerPosition(sizeof(tbp_value)- 10,OLED_FONT_MEDIUM_WIDTH),tbp_value[0],tbp_value);
    }
    set_val(PS4_RIGHT,0);
    set_val(PS4_LEFT ,0);
}

/*===============================================================
 PASSES, SHOTS
=================================================================
*/

// Finesse LB+B
if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
}

// Finesse with R3
if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
}

// Shots with B
if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);
		}
}
	
// Shots with RB+B
if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);
		}
}

//}
   // else {LED_Color(Green); }
   
/*===============================================================
 END OF MAIN
=================================================================
*/   
}	// End of main

combo NERVOUS {
   set_polar(POLAR_LS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
	set_val(PlayerRun, 100);
}

combo NERVOUSEND {
	set_polar(POLAR_LS, 0, 0);
	set_val(XB1_RB, 0);
	wait(100);
}


combo AIM_ASSIST_ABUSE {
   set_val(POLAR_LX, min(get_val(POLAR_LX) + (AA_Size * 32767 / 100), 32767));
   wait(AA_Interval);
   set_val(POLAR_LY, min(get_val(POLAR_LY) + (AA_Size * 32767 / 100), 32767));
   wait(AA_Interval);
   set_val(POLAR_LX, max(-32768, get_val(POLAR_LX) - (AA_Size * 32768 / 100)));
   wait(AA_Interval);
   set_val(POLAR_LY, max(-32768, get_val(POLAR_LY) - (AA_Size * 32768 / 100)));
   wait(AA_Interval - get_rtime());
   set_val(POLAR_LY, max(-32768, get_val(POLAR_LY) - (AA_Size * 32768 / 100)));
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function set_ds4_led(colour) {
    set_led(LED_1, duint8 (colour * 4));
    set_led(LED_2, duint8 ((colour * 4) + 1));
    set_led(LED_3, duint8 ((colour * 4) + 2));
    set_led(LED_4, duint8 ((colour * 4) + 3));
}

int KS_EntireScript = FALSE;
function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}

function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);  
}

const string OFF   = "OFF";       
const string ON    = "ON";

//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================

function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 

combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}  

/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}

int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

int data_indx;

/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
}

int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
}

//--------------------------------------------------------------
//      Analog Functions
//--------------------------------------------------------------
int LS_Sens_Corect;
function RA(xx, yy)
{
  set_val(SKILL_STICK_X, xx);
  set_val(SKILL_STICK_Y, yy);
}
function LA(x, y)
{
  set_val(MOVE_X, x);
  set_val(MOVE_Y, y);
}
function LA_L_R()
{
  if (right_on)
  { // right
    set_val(MOVE_X, inv(LY));
    set_val(MOVE_Y, LX);
  }
  else
  { //  left
    set_val(MOVE_X, LY);
    set_val(MOVE_Y, inv(LX));
  }
}
function RA_L_R()
{
  if (right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_OPP()
{
  if (!right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_UP()
{
  set_val(SKILL_STICK_X, LX);
  set_val(SKILL_STICK_Y, LY);
}
function RA_DOWN()
{
  set_val(SKILL_STICK_X, inv(LX));
  set_val(SKILL_STICK_Y, inv(LY));
}
function RA_ZERO()
{
  set_val(SKILL_STICK_X, 0);
  set_val(SKILL_STICK_Y, 0);
}

function set_right_or_left()
{
  right_on = FALSE;
  if (zone_p == 4 || zone_p == 3 || zone_p == 7)
  {
    right_on = TRUE;
  } ///
}

function f_auto_skill_menu()
{
  // 1.1. RS = LS zone
  if (zone_RS == zone_p)
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Up)
      run_skill_combo(RS_Skills_Up);
  }

  // 1.4. RS = opposite of LS zone
  if (zone_RS == calc_temp_zone(zone_p + 4))
  { // right_on does not matter here
    // 1.1.0. if LS --> UP (zone 0)
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Down)
      run_skill_combo(RS_Skills_Down);
  }
  //-------------------
  // 1.2. RS = LS zone +1/-1
  if (zone_RS == calc_temp_zone(zone_p + 1))
  {
    right_on = TRUE;
    if (RS_Skills_UpRight)
      run_skill_combo(RS_Skills_UpRight);
  }
  if (zone_RS == calc_temp_zone(zone_p - 1))
  {
    right_on = FALSE;
    if (RS_Skills_UpLeft)
      run_skill_combo(RS_Skills_UpLeft);
  }

  // 1.3. RS = LS zone +2/-2
  if (zone_RS == calc_temp_zone(zone_p + 2))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_RIGHT)
      run_skill_combo(RS_Skills_RIGHT);
  }
  if (zone_RS == calc_temp_zone(zone_p - 2))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_LEFT)
      run_skill_combo(RS_Skills_LEFT);
  }
  if (zone_RS == calc_temp_zone(zone_p + 3))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_DownR)
      run_skill_combo(RS_Skills_DownR);
  }
  if (zone_RS == calc_temp_zone(zone_p - 3))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_DownL)
      run_skill_combo(RS_Skills_DownL);
  }
}

function f_RS_New_Way()
{

  if (!get_ival(XB1_RS) && !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot))
  { // all Skills mode ){
    if ((abs(get_ival(SKILL_STICK_X)) > 45 || abs(get_ival(SKILL_STICK_Y)) > 45) && !flick_rs)
    { // getting RS zones
      flick_rs = TRUE;
      calc_RS();
      RS_X = ZONE_P[zone_RS][0];
      RS_Y = ZONE_P[zone_RS][1];
      f_auto_skill_menu();
    }
    set_val(SKILL_STICK_X, 0);
    set_val(SKILL_STICK_Y, 0);
  }
  //--- reset when RS is release
  if (abs(get_ival(SKILL_STICK_X)) < 20 && abs(get_ival(SKILL_STICK_Y)) < 20)
  {
    flick_rs = FALSE;
  }
}

function zone_saver()
{
  dEnd = zone_p
      calc_relative_xy(dEnd);
  LX = move_lx;
  LY = move_ly;
}

const int ZONE_P[][] = {
    //  X,    Y
    {0, -100},   // 0 UP
    {70, -70},   // 1 Up-Right
    {100, 0},    // 2 Right
    {70, 70},    // 3 Down right
    {0, 100},    // 4 Down
    {-100, 100}, // 5 Down Left
    {-100, 0},   // 6 Left
    {-70, -70}   // 7 Left Up
};

int move_lx, move_ly, zone_p;
int RS_X, RS_Y, zone_RS;
int rs_val = 35;
int polar_LS;




int skill_sens;
function run_skill_combo(f_skill)
{
  //-----------------------------------------------------------------------
  skill_sens = 1;
  if (f_skill == FAKE_SHOT_SKILL)
  {
    ACTIVE = FAKE_SHOT_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_HEEL_FLICK_SKILL)
  {
    ACTIVE = HEEL_TO_HEEL_FLICK_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_FLICK_TURN_SKILL)
  {
    ACTIVE = HEEL_FLICK_TURN_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_SKILL)
  {
    ACTIVE = RAINBOW_SKILL;
    combo_run(RAINBOW);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_SOMBRERO_SKILL)
  {
    Sombrero = TRUE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_PASS_SKILL)
  {
    ACTIVE = FAKE_PASS_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_UNIVERSAL_SKILL)
  {
    Sombrero = FALSE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_FEINT_SKILL)
  {
    ACTIVE = STEP_OVER_FEINT_SKILL;
    combo_run(STEP_OVER_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_DRAG_SKILL)
  {
    ACTIVE = DRAG_TO_DRAG_SKILL;
    combo_run(DRAG_TO_DRAG);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HOCUS_POCUS_SKILL)
  {
    ACTIVE = HOCUS_POCUS_SKILL;
    combo_run(HOCUS_POCUS);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TRIPLE_ELASTICO_SKILL)
  {
    ACTIVE = TRIPLE_ELASTICO_SKILL;
    combo_run(TRIPLE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_SKILL)
  {
    ACTIVE = ELASTICO_SKILL;
    combo_run(ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_ELASTICO_SKILL)
  {
    ACTIVE = REVERSE_ELASTICO_SKILL;
    combo_run(REVERSE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CRUYFF_TURN_SKILL)
  {
    ACTIVE = CRUYFF_TURN_SKILL;
    combo_run(CRUYFF_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LA_CROQUETA_SKILL)
  {
    ACTIVE = LA_CROQUETA_SKILL;
    combo_run(LA_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RONALDO_CHOP_SKILL)
  {
    ACTIVE = RONALDO_CHOP_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ROULETTE_SKILL)
  {
    ACTIVE = ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FLAIR_ROULETTE_SKILL)
  {
    ACTIVE = FLAIR_ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_SKILL)
  {
    ACTIVE = BALL_ROLL_SKILL;
    combo_run(BALL_ROLL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BERBA_MCGEADY_SPIN_SKILL)
  {
    ACTIVE = BERBA_MCGEADY_SPIN_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BOLASIE_FLICK_SKILL)
  {
    ACTIVE = BOLASIE_FLICK_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TORNADO_SKILL)
  {
    ACTIVE = TORNADO_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = THREE_TOUCH_ROULETTE_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ALTERNATIVE_ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ALTERNATIVE_ELASTICO_CHOP_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CHOP_SKILL)
  {
    ACTIVE = BALL_ROLL_CHOP_SKILL;
    combo_run(BALL_ROLL_CHOP);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_AND_EXIT_SKILL)
  {
    ACTIVE = FEINT_AND_EXIT_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_L_EXIT_R_SKILL)
  {
    ACTIVE = FEINT_L_EXIT_R_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LATERAL_HEEL_TO_HEEL_SKILL)
  {
    ACTIVE = LATERAL_HEEL_TO_HEEL_SKILL;
    combo_run(LATERAL_HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == WAKA_WAKA_SKILL)
  {
    ACTIVE = WAKA_WAKA_SKILL;
    combo_run(WAKA_WAKA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BODY_FEINT_SKILL)
  {
    ACTIVE = BODY_FEINT_SKILL;
    combo_run(BODY_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_HEEL)
  {
    ACTIVE = DRAG_TO_HEEL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_FAKE_TURN)
  {
    ACTIVE = BALL_ROLL_FAKE_TURN;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_FORWARD_AND_TURN)
  {
    ACTIVE = FEINT_FORWARD_AND_TURN;
    combo_run(FEINT_FORWARD);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TURN_BACK)
  {
    ACTIVE = TURN_BACK;
    combo_run(TURN_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_CROQUETA)
  {
    ACTIVE = ADVANCED_CROQUETA;
    combo_run(ADVANCED_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = CANCELED_THREE_TOUCH_ROULETTE_SKILL;
    combo_run(CANCELED_THREE_TOUCH_ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_STEP_OVER_SKILL)
  {
    ACTIVE = REVERSE_STEP_OVER_SKILL;
    combo_run(REVERSE_STEP_OVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_DRAG_BACK_SKILL)
  {
    ACTIVE = FAKE_DRAG_BACK_SKILL;
    combo_run(FAKE_DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_TO_SCORPION_KICK_SKILL)
  {
    ACTIVE = RAINBOW_TO_SCORPION_KICK_SKILL;
    combo_run(RAINBOW_TO_SCORPION);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_BOOST_SKILL)
  {
    ACTIVE = STEP_OVER_BOOST_SKILL;
    combo_run(BOOSTED_STEPOVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCEL_SHOOT_SKILL)
  {
    ACTIVE = CANCEL_SHOOT_SKILL;
    combo_run(CANCEL_SHOOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIRECTIONAL_NUTMEG_SKILL)
  {
    ACTIVE = DIRECTIONAL_NUTMEG_SKILL;
    combo_run(NUTMEG_SKILL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_SKILL)
  {
    ACTIVE = CANCELED_BERBA_SPIN_SKILL;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_WITH_DIRECTION)
  {
    ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_TO_SCOOP_TURN_SKILL)
  {
    ACTIVE = BALL_ROLL_TO_SCOOP_TURN_SKILL;
    combo_run(BALL_ROLL_SCOOP_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRIBBLING_SKILL)
  {
    ACTIVE = DRIBBLING_SKILL;
    start = TRUE;
    combo_run(DRIBBLING_SKILL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FOUR_TOUCH_TURN_SKILLS)
  {
    ACTIVE = FOUR_TOUCH_TURN_SKILLS;
    combo_run(FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SKILLED_BRIDGE_SKILL)
  {
    ACTIVE = SKILLED_BRIDGE_SKILL;
    combo_run(SKILLED_BRIDGE_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SCOOP_TURN_FAKE_SKILL)
  {
    ACTIVE = SCOOP_TURN_FAKE_SKILL;
    combo_run(SCOOP_TURN_FAKE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_STEP_OVER_SKILL)
  {
    ACTIVE = BALL_ROLL_STEP_OVER_SKILL;
    combo_run(BALL_ROLL_STEP_OVER_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_4_TOUCH_TURN_SKILL)
  {
    ACTIVE = CANCELED_4_TOUCH_TURN_SKILL;
    combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_SHOT_CANCEL_SKILL)
  {
    ACTIVE = FAKE_SHOT_CANCEL_SKILL;
    combo_run(FAKE_SHOT_CANCEL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == OKKOSHA_FLICK_SKILL)
  {
    ACTIVE = OKKOSHA_FLICK_SKILL;
    combo_run(OKKOSHA_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_RAINBOW_SKILL)
  {
    ACTIVE = ADVANCED_RAINBOW_SKILL;
    combo_run(ADVANCED_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_LA_CROQUETA_SKILL)
  {
    ACTIVE = STOP_LA_CROQUETA_SKILL;
    combo_run(STOP_LA_CROQUETA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLING_RAINBOW_SKILL)
  {
    ACTIVE = JUGGLING_RAINBOW_SKILL;
    combo_run(JUGGLING_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_NEYMAR_ROLL_SKILL)
  {
    ACTIVE = STOP_NEYMAR_ROLL_SKILL;
    combo_run(STOP_NEYMAR_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_V_DRAG_SKILL)
  {
    ACTIVE = STOP_V_DRAG_SKILL;
    combo_run(STOP_V_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = REV_OR_ELASTICO_SKILL;
    combo_run(REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = STOP_REV_OR_ELASTICO_SKILL;
    combo_run(STOP_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = DRAG_REV_OR_ELASTICO_SKILL;
    combo_run(DRAG_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_RABONA_SKILL)
  {
    ACTIVE = FAKE_RABONA_SKILL;
    combo_run(FAKE_RABONA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_REV_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_REV_ELASTICO_SKILL;
    combo_run(RABONA_TO_REV_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_ELASTICO_SKILL;
    combo_run(RABONA_TO_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SOMBRERO_FLICK_SKILL)
  {
    ACTIVE = SOMBRERO_FLICK_SKILL;
    combo_run(SOMBRERO_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLE_BACK_SOMBRERO_SKILL)
  {
    ACTIVE = JUGGLE_BACK_SOMBRERO_SKILL;
    combo_run(JUGGLE_BACK_SOMBRERO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_OPP_EXIT_SKILL)
  {
    ACTIVE = FAKE_BERBA_OPP_EXIT_SKILL;
    combo_run(FAKE_BARBA_OPP_EXIT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIAGONAL_HEEL_CHOP_SKILL)
  {
    ACTIVE = DIAGONAL_HEEL_CHOP_SKILL;
    combo_run(DIAGONAL_HEEL_CHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_FAKE_DRAG_SKILL)
  {
    ACTIVE = FAKE_BERBA_FAKE_DRAG_SKILL;
    combo_run(FAKE_BARBA_TO_FAKE_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ELASTICO_CHOP_SKILL;
    combo_run(ELASTICO_SHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CUT_180_SKILL)
  {
    ACTIVE = BALL_ROLL_CUT_180_SKILL;
    combo_run(BAL_ROLL_CUT_180_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_BALL_ROLL_SKILL)
  {
    ACTIVE = HEEL_TO_BALL_ROLL_SKILL;
    combo_run(HEEL_to_BALL_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STUTTER_FEINT_SKILL)
  {
    ACTIVE = STUTTER_FEINT_SKILL;
    combo_run(STUTTER_FEINT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JOG_OPENUP_FAKE_SHOT)
  {
    ACTIVE = JOG_OPENUP_FAKE_SHOT;
    combo_run(JOG_OPENUP_FAKE_SHOT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SPIN_MOVE_LEFT_RIGHT_SKILL)
  {
    ACTIVE = SPIN_MOVE_LEFT_RIGHT_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
}

///////////////////////////////////////////////////////////////////
// 1. Fake Shot           ////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo FAKE_SHOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 3. RAINBOW   //////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo RAINBOW
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo DRAG_BACK
{
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(60);
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (Sombrero)
    set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(40);
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 2. STEP OVER  /////////////////////////////////////////////
//////////////////////////////////////////////////////////////
combo STEP_OVER_FEINT
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  // vm_tctrl(0);wait(300);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Drag to Drag
combo DRAG_TO_DRAG
{
  LA(0, 0);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo HOCUS_POCUS
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TRIPLE_ELASTICO
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO
{
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo REVERSE_ELASTICO
{
  right_on = FALSE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO_SHOP_cmb
{
  set_val(FinesseShot, 100);
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  set_val(FinesseShot, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(FinesseShot, 100);
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo BAL_ROLL_CUT_180_cmb
{
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // zero
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 1. Cruyff Turn
combo CRUYFF_TURN
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo LA_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(500); //
  Get_LS_Output = TRUE;
}
combo ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Ball Roll
function RA_L_UR()
{
  // If right_on is true, rotate right

  if (right_on == TRUE)
  {
    // angle_rotation = 90
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle + angle_rotation), 32767)
  }
  // If right_on is false, rotate left
  else if (right_on == FALSE)
  {
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle - angle_rotation), 32767)
  }
}

combo BALL_ROLL
{
  RA_L_R(); // Left or Right
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  wait(300);
  sensitivity(PS4_LX, 50, 150);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);

  Get_LS_Output = TRUE;
}

combo BALL_ROLL_orig
{
  RA_L_R(); // Left or Right
  set_val(SprintBtn, 0);
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  vm_tctrl(0);
  wait(310);
  sensitivity(PS4_LX, 50, 150);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin
combo TURN_AND_SPIN
{
  if (ACTIVE == BALL_ROLL_FAKE_TURN)
    hold_btn = 200; //  Ball Roll Fake Turn L2
  else
    hold_btn = 1;
  vm_tctrl(0);
  wait(hold_btn);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
////////////////////////////////////
//  Tornado Spin + L1
combo TORNADO_SPIN
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 25.  Ball Roll Chop
combo BALL_ROLL_CHOP
{
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(300);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_OPP(); // Left or Right
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FEINT_EXIT
{
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////////////
// 28. LATERAL HEEL to HEEL ///////////////////////////////////
//////////////////////////////////////////////////////////////
// + L1   PlayerRun
combo LATERAL_HEELtoHEEL
{
  set_val(PlayerRun, 100);
  RA_OPP();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_L_R();
  vm_tctrl(0);
  wait(60); //
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo WAKA_WAKA
{
  RA_OPP(); // L
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);

  LA(0, 0);
  RA_L_R() // L
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo BODY_FEINT
{
  RA_L_R(); // R
  vm_tctrl(0);
  wait(100);
  RA_ZERO();
  vm_tctrl(0);
  wait(80);
  LA_L_R();
  vm_tctrl(0);
  wait(600);
  vm_tctrl(0);
  wait(600);
  Get_LS_Output = TRUE;
}
combo FEINT_FORWARD
{
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TURN_BACK
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(80);
  Get_LS_Output = TRUE;
}
combo ADVANCED_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(300); //
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(800); // 800
  Get_LS_Output = TRUE;
}
combo CANCELED_THREE_TOUCH_ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  // LA_L_R();
  vm_tctrl(0);
  wait(300); // 800
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 37. REVERSE STEP OVER  ///////////////////////////
//////////////////////////////////////////////////////////////
combo REVERSE_STEP_OVER
{
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo FAKE_DRAG_BACK
{
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(200); // 350
  right_on = FALSE;
  LA_L_R();
  vm_tctrl(0);
  wait(50); // 120
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(540);
  Get_LS_Output = TRUE;
}

combo RAINBOW_TO_SCORPION
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(40);
  RA_UP(); // up
  vm_tctrl(0);
  wait(190);               // 200
  set_val(PaceCtrol, 100); // L2
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100); // L2
  set_val(ShotBtn, 100);   // Shoot
  vm_tctrl(0);
  wait(180);
  Get_LS_Output = TRUE;
}
combo CANCEL_SHOOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(290);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo NUTMEG_SKILL
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (right_on)
    dEnd = zone_p + 1;
  else
  {
    dEnd = zone_p - 1;
    if (dEnd < 0)
      dEnd = 7;
  }
  calc_relative_xy(dEnd);
  RA(move_lx, move_ly);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
combo CANCELED_TURN_AND_SPIN
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(200);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_SCOOP_TURN
{
  LS_BlockOutput = TRUE;
  RA_L_R();
  vm_tctrl(0);
  wait(250);
  RA_ZERO();
  vm_tctrl(0);
  wait(50) : right_on = !right_on;
  if (right_on)
    dEnd = zone_p - 2;
  else
    dEnd = zone_p + 2;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  LS_BlockOutput = TRUE;
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  set_val(ShotBtn, 100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(500);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_STEP_OVER_cmb
{
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(300);
  RA_UP();
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo CANCEL_FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  vm_tctrl(0);
  wait(400);
  set_val(PS4_L2, 100);
  set_val(PS4_L1, 100);
  set_val(PS4_R1, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(70);
  Get_LS_Output = TRUE;
}
combo FAKE_SHOT_CANCEL_cmb
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(140);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo OKKOSHA_FLICK_cmb
{
  set_val(PlayerRun, 100);
  RA_UP(); // <-/->
  vm_tctrl(0);
  wait(300); //
  Get_LS_Output = TRUE;
}
combo ADVANCED_RAINBOW_cmb
{
  LA(LX, LY);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(100);
  RA_ZERO(); // Zero
  LA(LX, LY);
  vm_tctrl(0);
  wait(40);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(320);
  LA(LX, LY);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(220);
  LA(LX, LY);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo STOP_LA_CROQUETA_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(LA_CROQUETA);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo JUGGLING_cmb
{
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo JUGGLING_RAINBOW_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(60);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(40);
  set_val(PaceCtrol, 100);
  RA_UP(); // up
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(800);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_NEYMAR_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  RA_L_R();
  vm_tctrl(0);
  wait(200);
  RA_L_R();
  LA(LX, LY);
  vm_tctrl(0);
  wait(125);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_V_DRAG_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(125);
  set_val(ShotBtn, 100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  RA_OPP(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(FEINT_EXIT);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo DRAG_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(DRAG_BACK);
  vm_tctrl(0);
  wait(280);
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_RABONA_cmb
{
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(0, 0);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}

combo RABONA_TO_REV_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_UP(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_DOWN() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  // RA functions rotation done
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo RABONA_TO_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_DOWN(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_UP() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo SOMBRERO_FLICK_cmb
{
  vm_tctrl(0);
  wait(100);
  LA(0, 0);
  RA_UP();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_UP()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_DOWN();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo JUGGLE_BACK_SOMBRERO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(100);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_BARBA_OPP_EXIT_cmb
{
  LS_BlockOutput = TRUE;
  LA(LX, LY);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo DIAGONAL_HEEL_CHOP_cmb
{

  if (right_on)
    dEnd = zone_p + 3;
  else
    dEnd = zone_p - 3;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FAKE_BARBA_TO_FAKE_DRAG_cmb
{
  LA(LX, LY);
  LS_BlockOutput = TRUE;
  set_val(XB1_LS, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  set_val(XB1_LS, 100);
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(220);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(40);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo HEEL_to_BALL_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  set_val(PlayerRun, 100);
  RA_UP();
  LA(0, 0); // up
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // ZERO
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(200);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STUTTER_FEINT_cmb
{
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo RS_SWITCH
{
  vm_tctrl(0);
  wait(45);
  set_val(PS4_RX, 0);
  set_val(PS4_RY, 0);
  vm_tctrl(0);
  wait(160);
}

combo JOG_OPENUP_FAKE_SHOT_cmb
{
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 0);
  set_val(PassBtn, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo STOP_PLAYER_cmb
{
  zone_saver();
  vm_tctrl(0);
  wait(20);
  vm_tctrl(0);
  wait(100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(40);
  vm_tctrl(0);
  wait(160);
  Get_LS_Output = TRUE;
}



int time_to_dblclick = 300 int tap;
combo ONE_TAP
{
  tap = TRUE;
  vm_tctrl(0);
  wait(time_to_dblclick); // wait for second tap
  tap = FALSE;
}
int start;
combo DRIBBLING_SKILL_cmb
{
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(375);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 0);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(800);
  start = FALSE;
  Get_LS_Output = TRUE;
}

combo DRIBBLING_SKILL_cmb_sideexit
{
  // set_val(FinesseShot,100);
  LA_L_R();
  // sensitivity(PS4_LX, 50, 90);
  // sensitivity(PS4_LY, 50, 90);
  vm_tctrl(0);
  wait(360);
  // wait(20);
  // LA_DOWN();
  // set_val(PlayerRun,100);
  vm_tctrl(0);
  wait(20)
      // wait(20);
      start = FALSE;
  Get_LS_Output = TRUE;
}

combo BOOSTED_STEPOVER
{
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R();
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(w_rstick);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SKILLED_BRIDGE_cmb
{
  set_val(PaceCtrol, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SCOOP_TURN_FAKE
{
  RA_L_R();
  vm_tctrl(0);
  wait(280);
  LA_L_R()
  set_val(ShotBtn, 100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(250);
  LA_L_R()
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
} 