define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_LB;     
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;  

main {

	        if(get_val(XB1_PR1)){
	        	        	set_val(PassBtn,100);
	            ground_pass_timer += get_rtime();
	        }
	        if(event_release(XB1_PR1)){
	            if( ground_pass_timer < Ground_Pass_MIN ){
	                GP_difference = Ground_Pass_MIN - ground_pass_timer;
	                                                
	                 combo_run(RB_Pass_MIN_cmb);
	            }
	            ground_pass_timer = 0;
	        }

}

int ground_pass_timer; 
int Ground_Pass_MIN = 140;
int Ground_Pass_MAX = 250;
int GP_difference;
int low_driver_pass = FALSE;



combo RB_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(GP_difference);
	combo_run(RB_PASS);
//	wait(100);  // 100 ms
//	set_val(XB1_RB,100);
//	wait(100);
}

combo RB_PASS {
	wait(100);  // 100 ms
	set_val(XB1_RB,100);
	wait(100);
}