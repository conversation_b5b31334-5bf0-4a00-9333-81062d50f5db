///SpinStick, by xenologer
//when L2 is held down, left stick rotates in an approximate circle
//intended for use with 'Selection Wheel' type game menus
/*
method:
Generate 2 Triangle Waveforms, with one out of phase by 1/4
use waveforms as coordinates for approximate circular motion
*/
 
define waittime=16;
//Change to longer wait values to slow down rotation
 
 
 
int x,y;
int vel=1;
int vel2=1;
int x2,y2;
 
main {
    vm_tctrl(-2);
 
 
    if(get_val(PS4_L2)){
        //L2 IS DOWN, spin the left stick
        combo_run(spin);
 
    }else{
        //L2 IS not Down, reset
        combo_stop(spin);
        y=-100;
        x=0;
        x2=x;
        y2=y;
        vel=1;
        vel2=1;
    }
 
}
 
combo spin{        
    set_val(PS4_LX,x2);
    set_val(PS4_LY,y2);
    wait(waittime);
    if(y>100)vel=-1;
    if(y<-100)vel=1;
    if(x>100)vel2=-1;
    if(x<-100)vel2=1;
    y=y+vel; //y bounces from -100,100 in a triangle waveform
    x=x+vel2;
 
//scale and clamp the output to round off triangle corners
    x2=(x*5)/4;
    y2=(y*5)/4;
    if(x2>100)x2=100;
    if(x2<-100)x2=-100;
    if(y2>100)y2=100;
    if(y2<-100)y2=-100;
 
}