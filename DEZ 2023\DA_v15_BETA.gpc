																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members (Dark_Angel_FC_24)   .     | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																💻 most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																💻 My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																
																Your contributions have been invaluable, and I am truly grateful for your support."
																
																
																
																
															⚽💻 "IF YOU'RE INTERESTED IN DIVING INTO FIFA/FC GPC CODING IN DEPTH,  CHECK "FIFA CODE ACADEMY" there." ⚽💻 : https://discord.gg/CpcFqAvP 
																*/














int DA_FC216[0x0];init {	DA_FC115();	combo_run(DA_FC1);	combo_run(DA_FC2);	combo_run(DA_FC3);	combo_run(DA_FC4);	combo_run(DA_FC5);	combo_run(DA_FC6);	combo_run(DA_FC7);	combo_run(DA_FC8);	combo_run(DA_FC9);	combo_run(DA_FC10);	combo_run(DA_FC11);	combo_run(DA_FC12);	combo_run(DA_FC13);	combo_run(DA_FC14);	combo_run(DA_FC15);	combo_run(DA_FC16);	combo_run(DA_FC17);	combo_run(DA_FC18);	combo_run(DA_FC19);	combo_run(DA_FC20);	combo_run(DA_FC21);	combo_run(DA_FC22);	combo_run(DA_FC23);	combo_run(DA_FC24);	combo_run(DA_FC25);	combo_run(DA_FC26);	combo_run(DA_FC27);	combo_run(DA_FC28);	combo_run(DA_FC29);	combo_run(DA_FC30);	combo_run(DA_FC31);	combo_run(DA_FC32);	combo_run(DA_FC33);	combo_run(DA_FC34);	combo_run(DA_FC35);	combo_run(DA_FC36);	combo_run(DA_FC37);	combo_run(DA_FC38);	combo_run(DA_FC39);	combo_run(DA_FC40);	combo_run(DA_FC41);	combo_run(DA_FC42);	combo_run(DA_FC43);	combo_run(DA_FC44);	combo_run(DA_FC45);	combo_run(DA_FC46);	combo_run(DA_FC47);	combo_run(DA_FC48);	combo_run(DA_FC49);	combo_run(DA_FC50);	combo_run(DA_FC51);	combo_run(DA_FC52);	combo_run(DA_FC53);	combo_run(DA_FC54);	combo_run(DA_FC55);	combo_run(DA_FC56);	combo_run(DA_FC57);	combo_run(DA_FC58);	combo_run(DA_FC59);	combo_run(DA_FC60);	combo_run(DA_FC61);	combo_run(DA_FC62);	combo_run(DA_FC63);	combo_run(DA_FC64);	combo_run(DA_FC65);	combo_run(DA_FC66);	combo_run(DA_FC67);	combo_run(DA_FC68);	combo_run(DA_FC69);	combo_run(DA_FC70);			combo_stop(DA_FC1);	combo_stop(DA_FC2);	combo_stop(DA_FC3);	combo_stop(DA_FC4);	combo_stop(DA_FC5);	combo_stop(DA_FC6);	combo_stop(DA_FC7);	combo_stop(DA_FC8);	combo_stop(DA_FC9);	combo_stop(DA_FC10);	combo_stop(DA_FC11);	combo_stop(DA_FC12);	combo_stop(DA_FC13);	combo_stop(DA_FC14);	combo_stop(DA_FC15);	combo_stop(DA_FC16);	combo_stop(DA_FC17);	combo_stop(DA_FC18);	combo_stop(DA_FC19);	combo_stop(DA_FC20);	combo_stop(DA_FC21);	combo_stop(DA_FC22);	combo_stop(DA_FC23);	combo_stop(DA_FC24);	combo_stop(DA_FC25);	combo_stop(DA_FC26);	combo_stop(DA_FC27);	combo_stop(DA_FC28);	combo_stop(DA_FC29);	combo_stop(DA_FC30);	combo_stop(DA_FC31);	combo_stop(DA_FC32);	combo_stop(DA_FC33);	combo_stop(DA_FC34);	combo_stop(DA_FC35);	combo_stop(DA_FC36);	combo_stop(DA_FC37);	combo_stop(DA_FC38);	combo_stop(DA_FC39);	combo_stop(DA_FC40);	combo_stop(DA_FC41);	combo_stop(DA_FC42);	combo_stop(DA_FC43);	combo_stop(DA_FC44);	combo_stop(DA_FC45);	combo_stop(DA_FC46);	combo_stop(DA_FC47);	combo_stop(DA_FC48);	combo_stop(DA_FC49);	combo_stop(DA_FC50);	combo_stop(DA_FC51);	combo_stop(DA_FC52);	combo_stop(DA_FC53);	combo_stop(DA_FC54);	combo_stop(DA_FC55);	combo_stop(DA_FC56);	combo_stop(DA_FC57);	combo_stop(DA_FC58);	combo_stop(DA_FC59);	combo_stop(DA_FC60);	combo_stop(DA_FC61);	combo_stop(DA_FC62);	combo_stop(DA_FC63);	combo_stop(DA_FC64);	combo_stop(DA_FC65);	combo_stop(DA_FC66);	combo_stop(DA_FC67);	combo_stop(DA_FC68);	combo_stop(DA_FC69);	combo_stop(DA_FC70);		combo_run(DA_FC109);}int DA_FC275 ;int DA_FC276;int DA_FC277;int DA_FC278;int DA_FC279;define DA_FC280 = 0x0;define DA_FC281 = 0x1;define DA_FC282 = 0x2;define DA_FC283 = 0x3;define DA_FC284 = 0x4;define DA_FC285 = 0x5;define DA_FC286 = 0x6;define DA_FC287 = 0x7;define DA_FC288 = 0x8;define DA_FC289 = 0x9;define DA_FC290 = 0xA;define DA_FC291 = 0xB;define DA_FC292 = 0xC;define DA_FC293 = 0xD;define DA_FC294 = 0xE;define DA_FC295 = 0xF;define DA_FC296 = 0x10;define DA_FC297 = 0x11;define DA_FC298 = 0x12;define DA_FC299 = 0x13;define DA_FC300 = 0x14;define DA_FC301 = 0x15;define DA_FC302 = 0x16;define DA_FC23 = 0x17;define DA_FC304 = 0x18;define DA_FC305 = 0x19;define DA_FC306 = 0x1A;define DA_FC307 = 0x1B;define DA_FC308 = 0x1C;define DA_FC309 = 0x1D;define DA_FC310 = 0x1E;define DA_FC311 = 0x1F;define DA_FC312 = 0x20;define DA_FC313 = 0x21;define DA_FC314 = 0x22;define DA_FC315 = 0x23;define DA_FC316 = 0x24;define DA_FC317 = 0x25;define DA_FC318 = 0x26;define DA_FC319 = 0x27;define DA_FC320 = 0x28;define DA_FC321 = 0x29;define DA_FC322 = 0x2A;define DA_FC323 = 0x2B;define DA_FC324 = 0x2C;define DA_FC325 = 0x2D;define DA_FC326 = 0x2E;define DA_FC327 = 0x2F;define DA_FC328 = 0x30;define DA_FC329 = 0x31;define DA_FC330 = 0x32;define DA_FC331 = 0x33;define DA_FC332 = 0x34;define DA_FC333 = 0x35;define DA_FC334 = 0x36;define DA_FC335 = 0x37;define DA_FC336 = 0x38;define DA_FC337 = 0x39;define DA_FC338 = 0x3A;define DA_FC339 = 0x3B;define DA_FC340 = 0x3C;define DA_FC341 = 0x3D;define DA_FC342 = 0x3E;define DA_FC343 = 0x3F;define DA_FC344 = 0x40;define DA_FC345 = 0x41;define DA_FC346 = 0x42;define DA_FC347 = 0x43;define DA_FC348 = 0x44;define DA_FC349 = 0x45;define DA_FC350 = 0x46;define DA_FC351 = 0x0;function DA_FC109(DA_FC110) {	if (DA_FC110 == V_00) vm_tctrl(0);	else if (DA_FC110 == V_01) vm_tctrl(2);	else if (DA_FC110 == V_02) vm_tctrl(-2);	else if (DA_FC110 == V_03) vm_tctrl(-4);	else if (DA_FC110 == V_04) vm_tctrl(-6);	else if (DA_FC110 == V_05) vm_tctrl(-8);	else if (DA_FC110 == V_06) vm_tctrl(-9);}int DA_FC352, DA_FC353;int DA_FC354, DA_FC355;int DA_FC356 = FALSE, DA_FC357;int DA_FC358 = TRUE;int DA_FC359;const string DA_FC780[] = {	"Off",  "On"};int DA_FC360;int DA_FC361;int DA_FC362;int DA_FC363;int DA_FC364;int DA_FC365;int DA_FC366;int DA_FC367;int DA_FC368;int DA_FC369;int DA_FC370;int DA_FC371;int DA_FC372;int DA_FC373;int DA_FC374;int DA_FC375;int DA_FC376;int DA_FC377;int DA_FC378;int DA_FC379;int DA_FC110;int DA_FC381;int DA_FC382 ;int DA_FC383 ;int DA_FC384 ;define DA_FC385 = 0x18;int DA_FC386;int DA_FC387;int DA_FC388;int DA_FC389;int DA_FC390;int DA_FC391;int DA_FC392;int DA_FC393;int DA_FC394;int DA_FC395 ;int DA_FC396 ;int DA_FC397 ;int DA_FC398 ;int DA_FC399 ;int DA_FC400 ;int DA_FC401 ;int DA_FC402 ;int DA_FC403 ;int DA_FC404 ;int DA_FC405 ;int DA_FC406;int DA_FC407;int DA_FC408;int DA_FC409;int DA_FC410;int DA_FC411;int DA_FC412;int DA_FC413;int DA_FC414;int DA_FC415;int DA_FC416;int DA_FC417;int DA_FC418;int DA_FC419;int DA_FC420;int DA_FC421;int DA_FC422;int DA_FC423;int DA_FC424;int DA_FC425;int DA_FC426;int DA_FC427;int DA_FC428;int DA_FC429;int DA_FC430;int DA_FC431;int DA_FC432;int DA_FC433;int DA_FC434;int DA_FC435;int DA_FC436;int DA_FC437;int DA_FC438;int DA_FC439 ;int DA_FC440 ;int DA_FC441 ;int DA_FC442;int DA_FC443 ;int DA_FC444 ;int DA_FC445 ;int DA_FC446;int DA_FC447 ;int DA_FC448 ;int DA_FC449 ;int DA_FC450;int DA_FC451 ;int DA_FC452 ;int DA_FC453 ;int DA_FC454;int DA_FC455;int DA_FC456;int DA_FC457;int DA_FC458;int DA_FC459;const int16 DA_FC786[][] = {{		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x9, 0x1, 0xA, 0x2	}	,      {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x16, 0x1, 0xA, 0xD	}	,  {		0x0, 0x46, 0x1, 0xA, 0x2	}	,   {		0x0, 0x46, 0x1, 0xA, 0x3	}	,   {		0x0, 0x46, 0x1, 0xA, 0x4	}	,   {		0x0, 0x46, 0x1, 0xA, 0x5	}	,   {		0x0, 0x16, 0x1, 0xA, 0xD	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,  {		0x0, 0x46, 0x1, 0xA, 0x10	}	,   {		0x1, 0x19, 0x1, 0xA, 0x6	}	,    {		0x0, 0x1, 0x1, 0xA, 0x13	}	,    {		0x0, 0x1, 0x1, 0xA, 0x14	}	,    {		0x1, 0x19, 0x1, 0xA, 0x8	}	,    {		0x0, 0x1, 0x1, 0xA, 0x13	}	,    {		0x0, 0x1, 0x1, 0xA, 0x14	}	,    {		0x0, 0x19, 0x1, 0xA, 0x7	}	,    {		0x0, 0x1, 0x1, 0xA, 0x15	}	,    {		0x0, 0x1, 0x1, 0xA, 0x13	}	,    {		0x1, 0x19, 0x1, 0xA, 0x9	}	,    {		0x0, 0x1, 0x1, 0xA, 0x1C	}	,    {		0x0, 0x1, 0x1, 0xA, 0x1D	}	,    {		0x1, 0x320, 0x1, 0xA, 0x0	}	,   {		0x1, 0x320, 0x1, 0xA, 0x0	}	,   {		0x0, 0x16, 0x1, 0xA, 0xD	}	,   {		0x0, 0x1, 0x1, 0xA, 0x21	}	,    {		-100, 0x12C, 0x1, 0xA, 0x1	}	, {		-150, 0x96, 0xA, 0xA, 0x0	}	,{		-150, 0x96, 0xA, 0xA, 0x0	}	,{		0x0, 0x1, 0x1, 0xA, 0x25	}	,     {		-150, 0x96, 0xA, 0xA, 0x0	}	,{		0x0, 0x16, 0x1, 0xA, 0x31	}	,    {		0x0, 0x16, 0x1, 0xA, 0x32	}	,    {		0x0, 0x16, 0x1, 0xA, 0x33	}	,    {		0x0, 0x16, 0x1, 0xA, 0x34	}	,    {		0x0, 0x1, 0x1, 0xA, 0x35	}	,     {		0x0, 0x1, 0x1, 0xA, 0x36	}	,     {		0x0, 0x1, 0x1, 0xA, 0x1	}	,      {		0x0, 0x1, 0x1, 0xA, 0x1	}	,      {		0x3C, 0x1F4, 0x5, 0xA, 0x0	}	,   {		0x3C, 0x1F4, 0x5, 0xA, 0x0	}	,   {		0x0, 0x1, 0x1, 0xA, 0x1	}	,      {		0x0, 0x1, 0x1, 0xA, 0x1	}	,      {		0x32, 0xFA, 0x5, 0xA, 0x0	}	,   {		0x64, 0x352, 0x5, 0xA, 0x0	}	,  {		0x0, 0x1, 0x1, 0xA, 0x1	}	,      {		0x0,      0x1,      0x1,     0xA,     0x1  	}	, {		0x50,    0x1F4,      0x5,     0xA,     0x1  	}	, {		0x50,    0x1F4,      0x5,     0xA,     0x1  	}	, {		0x0,      0x1,      0x1,     0xA,     0x1  	}	, {		-2500,0x9C4,0x19,0xA,0x0	}	,  {		0x0, 0x1, 0x1, 0xA, 0x1	}	,     {		0x0, 0x1, 0x1, 0xA, 0x1	}	,     {		0x0, 0x1, 0x1, 0xA, 0x1	}};const int16 DA_FC572[][] = {{		0x0, 0x7, 0x1	}	,  	    {		0x8,   0x10, 0x1	}	,  	    {		0x11,  0x15, 0x1	}	,  	    {		0x44,0x44,0x1	}	,      	    {		0x45,0x46,0x1	}	,      	    {		0x16, 0x1A, 0x1	}	,  	    {		0x1B, 0x1D, 0x1	}	,  	    {		0x1E, 0x20, 0x1	}	,  	    {		0x21, 0x23, 0x1	}	,  	    {		0x24, 0x26, 0x1	}	,  	    {		0x27, 0x27, 0x1	}	,  	    {		0x28, 0x28, 0x1	}	,  	    {		0x29, 0x2A, 0x1	}	,  	    {		0x2B, 0x2B, 0x1	}	,  	    {		0x0,  0x0, 0x0	}	,  	    {		0x36, 0x37, 0x1	}	,  	    {		0x2C, 0x2F, 0x1	}	,  {		0x30, 0x33, 0x1	}	,  {		0x34, 0x35, 0x1	}	,  {		0x0, 0x0, 0x0	}	,   {		0x0, 0x0, 0x0	}	,   {		0x43, 0x43, 0x1	}	,   {		0x38, 0x3B, 0x1	}	,  {		0x3C, 0x3F, 0x1	}	,  {		0x40, 0x42, 0x1	}};const uint8 DA_FC758[] = {	0x2,  	    0x3,  	    0x3,  	    0x1,  	    0x1,  	    0x6,  	    0x46, 	    0x46, 	    0x46, 	    0x46, 	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x1,  	    0x6,  	    0x1,  	    0x1, 	0x1, 	0x1  };const string DA_FC581[] = {	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", ""};const string DA_FC580[] = {	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 0x1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 0x3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 0x2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 0x4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","Double Tap GrounP","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP",""};const string DA_FC762 [] = {	"Classic","Alternative","Custom", "" };const string DA_FC861 [] = {	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", "" };const string DA_FC778[] = {	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  ""};const string DA_FC764[] = {	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  ""};const string DA_FC831[] = {	"Right",  "Left",  ""};const string DA_FC829[] = {	"One Tap",  "Double Tap",  ""};const string DA_FC768[] = {	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  ""};const string DA_FC770[] = {	"Disable", 	"LA_CRQ_Pass", 	"LA_CRQ_Shoot", 	"Roll Sombrero PS", 	"Step Over Feint", 	"Rev Step Over", 	"Step Over Boost", 	"Hocus Pocus", 	"Triple Elastico", 	"Elastico", 	"Rev Elastico", 	"ForwFlairNutmeg", 	"DragBack Univ.", 	"Drag to Drag", 	"Cruyff Turn", 	"La Croqueta", 	"Roulette", 	"Flair Roulette", 	"Feint && Exit", 	"Feint & Exit", 	"WAKA WAKA", 	"Body Feint", 	"Feint & Turn", 	"Turn Back", 	"BackFlairNutmeg", 	"Four Touch Turn", 	"Skilled Bridge", 	"Canc 4 Touch", 	"Roll Sombrero", 	"Ball Roll", 	"Ball Roll Drag", 	"Ball Roll Chop", 	"Ball Roll Fake", 	"Roll to Scoop", 	"Scoop Turn Fake", 	"Roll Step Over", 	"BallRoll Cut 180", 	"Heel2Heel Flick", 	"Lat Heel2Heel", 	"Drag to Heel", 	"Diag Heel Chop", 	"Heel to Ball", 	"Berb/Mcgeady", 	"1 Foot Spin", 	"Canc Berba Spin", 	"C Berba Spin", 	"Spin Move L/R", 	"Fake Berba Out", 	"Fake Berba Drag", 	"Fake Shot", 	"Fake Pass", 	"Fake Drag Back", 	"Fake Shot Canc.", 	"Fake Rabona", 	"Jog Openup Fake", 	"Cancel Shoot", 	"Ronaldo Chop", 	"Okocha Flick", 	"Rainbow", 	"Flair Rainbow", 	"Adv. Rainbow", 	"Juggle Rainbow", 	"Drag Back Som.", 	"Sombrero Flick", 	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R"};const string DA_FC805[] = {	"OFF",  "PS5_PS",  "PS5_SHARE",  "PS5_OPTIONS",  "PS5_R1",  "PS5_R2",  "PS5_R3",  "PS5_L1",  "PS5_L2",  "PS5_L3",  "PS5_UP",  "PS5_DOWN",  "PS5_LEFT",  "PS5_RIGHT",  "PS5_TRIANGLE",  "PS5_CIRCLE",  "PS5_CROSS",  "PS5_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS5_TOUCH",  ""};int DA_FC460 = -1;int DA_FC461 = -1;int DA_FC462 = -1;int DA_FC463 = -1;int DA_FC464 = -1;int DA_FC465;int DA_FC466;int DA_FC467;int DA_FC468;int DA_FC469;const uint8 DA_FC1286[] = {	0x4,0x4,0x4, 0x4,0x4,0x4, 0x4,0x4,0x4,0x4};const uint8 DA_FC1287[] = {	0x29, 0xF, 0x4, 0x29, 0xF, 0x29, 0x29, 0x29, 0x4, 0xF,0x1D};const uint8 DA_FC1288[] = {	0xF, 0x4, 0x29, 0x29, 0xF, 0x29, 0x29, 0x4, 0x29, 0xF,0x1D };const uint8 DA_FC1289[] = {	0xF,0x1D,0x46,0x29,0x46,0xF,0x1D,0x46,0x29,0xF,0x1D };const uint8 DA_FC1290[] = {	0x1D,0x29,0xF,0x1D,0x46,0xF,0x46,0x29,0x46,0xF ,0x1D };const uint8 DA_FC1291[] = {	0x1D, 0x2C, 0x2C, 0x2C, 0x1D, 0x2C, 0x2C, 0x2C,0x2C,0x2C,0x1D};const uint8 DA_FC1292[] = {	0x1D, 0x2C, 0x2C, 0x2C, 0x1D, 0x2C, 0x2C, 0x2C,0x2C,0x2C ,0x1D };const uint8 DA_FC1293[] = {	0x1B, 0x15, 0x2C, 0x1B, 0x15, 0x2C, 0x15, 0x2C, 0x1B, 0x15};const uint8 DA_FC1294[] = {	0x4,0x4,0x4, 0x4,0x4,0x4, 0x4,0x4,0x4,0x4};const uint8 DA_FC1295[] = {	0x9, 0x21, 0xF, 0xF, 0x9, 0x4, 0x9, 0x22, 0x4E, 0x29, 0x9,0x1D};const uint8 DA_FC1296[] = {	0x21, 0xA, 0x7, 0xF, 0xA, 0x4E, 0x4, 0x29, 0xF, 0x7, 0x22,0x1D };const uint8 DA_FC1297[] = {	0x29, 0x9, 0x9, 0xF, 0x9, 0x29, 0xF, 0x29, 0x21, 0x29, 0x9,0x1D };const uint8 DA_FC1298[] = {	0x7, 0xF, 0xA, 0x7, 0xA, 0x21, 0x29, 0x7, 0xF, 0x29, 0xA ,0x1D};const uint8 DA_FC1299[] = {	0x29, 0x7, 0x18, 0x2C, 0x2D, 0xA, 0x18, 0x2C,0x29,0x18,0x21,0x1D};const uint8 DA_FC1300[] = {	0x29, 0x9, 0x18, 0x30, 0x2D, 0x9, 0x18, 0x2C,0x29,0x21 ,0x21 ,0x1D};const uint8 DA_FC1301[] = {	0x33, 0x3F, 0x2F, 0x3F, 0x18, 0x33, 0x2C, 0x3F, 0x30, 0xC };function DA_FC111(DA_FC112) {	if (DA_FC112 >= V_09) {		DA_FC470 = -V_01;			}	else if (DA_FC112 <= V_00) {		DA_FC470 = V_01;			}	DA_FC112 += DA_FC470;	return DA_FC112;	}function DA_FC113() {	vm_tctrl(V_00);	if(DA_FC29 && DA_FC363){		if(DA_FC550 < V_1000){			DA_FC472 = V_10;			DA_FC496   = V_10;			DA_FC494  = V_10;					}			}	if(DA_FC475 && DA_FC364){		DA_FC473 = FALSE;		if(DA_FC550 < V_1000){			DA_FC472 = V_11;			DA_FC496   = V_11;			DA_FC494  = V_11;					}			}	if(DA_FC473 && DA_FC364){		DA_FC475 = FALSE;		if(DA_FC550 < V_1000){			DA_FC472 = V_10;			DA_FC496   = V_10;			DA_FC494  = V_10;					}			}	if(DA_FC550 >= V_1000){		DA_FC477 = DA_FC111(DA_FC477);		DA_FC493 = DA_FC111(DA_FC493);		DA_FC494 = DA_FC111(DA_FC494);		DA_FC472 = DA_FC111(DA_FC472);		DA_FC496 = DA_FC111(DA_FC496);			}	if(DA_FC363){		if(DA_FC1105 == DA_FC587){			DA_FC478 = !DA_FC478;			if(DA_FC1286[DA_FC477]) DA_FC241(DA_FC1286[DA_FC477]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_04)){			DA_FC478 = FALSE;			if(DA_FC1293[DA_FC493]) DA_FC241(DA_FC1293[DA_FC493]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_01) ){			DA_FC478 = TRUE;			if(DA_FC1288[DA_FC472]) DA_FC241(DA_FC1288[DA_FC472]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_01) ){			DA_FC478 = FALSE;			if(DA_FC1287[DA_FC472]) DA_FC241(DA_FC1287[DA_FC472]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_02) ){			DA_FC478 = TRUE;			if(DA_FC1290[DA_FC496]) DA_FC241(DA_FC1290[DA_FC496]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_02) ){			DA_FC478 = FALSE;			if(DA_FC1289[DA_FC496]) DA_FC241(DA_FC1289[DA_FC496]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_03) ){			DA_FC478 = TRUE;			if(DA_FC1291[DA_FC494]) DA_FC241(DA_FC1291[DA_FC494]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_03) ){			DA_FC478 = FALSE;			if(DA_FC1292[DA_FC494]) DA_FC241(DA_FC1292[DA_FC494]);					}			}	if(DA_FC364){		if(DA_FC1105 == DA_FC587){			DA_FC478 = !DA_FC478;			if(DA_FC1294[DA_FC477]) DA_FC241(DA_FC1294[DA_FC477]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_04)){			DA_FC478 = FALSE;			if(DA_FC1301[DA_FC493]) DA_FC241(DA_FC1301[DA_FC493]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_01) ){			DA_FC478 = TRUE;			if(DA_FC1296[DA_FC472]) DA_FC241(DA_FC1296[DA_FC472]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_01) ){			DA_FC478 = FALSE;			if(DA_FC1295[DA_FC472]) DA_FC241(DA_FC1295[DA_FC472]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_02) ){			DA_FC478 = TRUE;			if(DA_FC1298[DA_FC496]) DA_FC241(DA_FC1298[DA_FC496]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_02) ){			DA_FC478 = FALSE;			if(DA_FC1297[DA_FC496]) DA_FC241(DA_FC1297[DA_FC496]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 + V_03) ){			DA_FC478 = TRUE;			if(DA_FC1299[DA_FC494]) DA_FC241(DA_FC1299[DA_FC494]);					}		if(DA_FC1105 == DA_FC246 (DA_FC587 - V_03) ){			DA_FC478 = FALSE;			if(DA_FC1300[DA_FC494]) DA_FC241(DA_FC1300[DA_FC494]);					}			}	DA_FC465 = DA_FC477;	DA_FC466 = DA_FC493;	DA_FC467 = DA_FC494;	DA_FC468 = DA_FC472;	DA_FC469 = DA_FC496;}int DA_FC477;int DA_FC493;int DA_FC494;int DA_FC472;int DA_FC496;function DA_FC114() {	if(DA_FC1090){		DA_FC497 += get_rtime();			}	if(DA_FC497 >= V_3000){		DA_FC497 = V_00;		DA_FC1090 = FALSE;			}	if (!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449)) {		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000) && !DA_FC499 && !combo_running(DA_FC0)) {			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + V_23) % V_360) / -V_45) + V_10) % V_08;			DA_FC499 = TRUE;			DA_FC1090 = TRUE;			DA_FC497 = V_00;			vm_tctrl(V_00);			DA_FC113();					}		set_val(DA_FC1076, V_00);		set_val(DA_FC1077, V_00);			}	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < V_2000) {		DA_FC499 = FALSE;			}	}function DA_FC115() {			DA_FC186();	if (DA_FC386 == V_00 && DA_FC387 == V_00 && DA_FC388 == V_00 && DA_FC389 == V_00 && DA_FC390 == V_00 && DA_FC391 == V_00 && DA_FC392 == V_00 && DA_FC393 == V_00) {		DA_FC386 = V_04;		DA_FC387 = V_15;		DA_FC388 = V_66;		DA_FC389 = V_48;		DA_FC390 = V_15;		DA_FC391 = V_29;		DA_FC392 = V_29;		DA_FC393 = V_44;			}	DA_FC900 = get_slot();	}int DA_FC470 = 0x1;int DA_FC502;int DA_FC503;int DA_FC504 = TRUE;int DA_FC505[0x6];int DA_FC506;int DA_FC507;int DA_FC508;int DA_FC509;function DA_FC116(DA_FC117, DA_FC118, DA_FC119) {	DA_FC119 = (DA_FC119 * V_14142) / V_46340;	if (DA_FC118 <= V_00) {		set_polar2(DA_FC117, (DA_FC118 = (abs(DA_FC118) + V_360) % V_360), min(DA_FC119, DA_FC513[DA_FC118 % V_90]));		return;			}	set_polar2(DA_FC117, inv(DA_FC118 % V_360), min(DA_FC119, DA_FC513[DA_FC118 % V_90]));	}function DA_FC120(DA_FC117, DA_FC122) {	if (DA_FC122) return (V_360 - get_polar(DA_FC117, POLAR_ANGLE)) % V_360;	return isqrt(~(pow(get_val(V_42 + DA_FC117), V_02) + pow(get_val(V_43 + DA_FC117), V_02))) + V_01;	}function DA_FC123(DA_FC117,DA_FC122) {	if (DA_FC122) return (V_360 - get_ipolar(DA_FC117, POLAR_ANGLE)) % V_360;	return isqrt(~(pow(get_ival(V_42 + DA_FC117), V_02) + pow(get_ival(V_43 + DA_FC117), V_02))) + V_01;	}const int16 DA_FC513[] = {	0x2710, 0x2711, 0x2716, 0x271E, 0x2728, 0x2736, 0x2747, 0x275B, 0x2772, 0x278C, 0x27AA, 0x27CB, 0x27EF, 0x2817, 0x2842, 0x2871, 0x28A3, 0x28D9, 0x2912, 0x2950, 0x2992, 0x29D7, 0x2A21, 0x2A6F, 0x2AC2, 0x2B1A, 0x2B76, 0x2BD7, 0x2C3E, 0x2CA9, 0x2D1B, 0x2D92, 0x2E10, 0x2E93, 0x2F1E, 0x2FB0, 0x3049, 0x30E9, 0x3192, 0x3243, 0x32FE , 0x33C2 , 0x3490, 0x3569, 0x364E, 0x373E, 0x364E, 0x3569, 0x3490, 0x33C2, 0x32FE, 0x3243, 0x3192, 0x30E9, 0x3049, 0x2FB0, 0x2F1E, 0x2E93, 0x2E10, 0x2D92, 0x2D1B, 0x2CA9, 0x2C3E, 0x2BD7, 0x2B76, 0x2B1A, 0x2AC2, 0x2A6F, 0x2A21, 0x29D7, 0x2992, 0x2950, 0x2912, 0x28D9, 0x28A3, 0x2871, 0x2842, 0x2817, 0x27EF, 0x27CB, 0x27AA, 0x278C, 0x2772, 0x275B, 0x2747, 0x2736, 0x2728, 0x271E, 0x2716, 0x2711 };int block = FALSE;int DA_FC517 = 0x1;combo DA_FC0{	set_polar(POLAR_RS,V_00,V_00);	vm_tctrl(V_00);	wait(V_100);	vm_tctrl(V_00);	wait(V_300);	}
main{
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																if(!DA_FC503){	DA_FC503 = TRUE;	DA_FC502 = random(V_11111, V_99999);	set_pvar(SPVAR_1,DA_FC503);	set_pvar(SPVAR_3,DA_FC502);	DA_FC504 = TRUE;	}if(!DA_FC509){	vm_tctrl(V_00);	if(event_press(PS5_LEFT)){		DA_FC508 = DA_FC128(DA_FC508 + V_01 ,V_00,V_05)DA_FC504 = TRUE	}	if(event_press(PS5_RIGHT)){		DA_FC508 = DA_FC128(DA_FC508 - V_01 ,V_00,V_05)DA_FC504 = TRUE	}	if(event_press(PS5_UP)){		DA_FC505[DA_FC508]  = DA_FC128(DA_FC505[DA_FC508] + V_01 ,V_00,V_09)DA_FC504 = TRUE	}	if(event_press(PS5_DOWN)){		DA_FC505[DA_FC508]  = DA_FC128(DA_FC505[DA_FC508] - V_01 ,V_00,V_09)DA_FC504 = TRUE	}	if(event_press(PS5_CROSS)){		DA_FC506 = V_00;		for(DA_FC507 = V_05;		DA_FC507 >= V_00;		DA_FC507--){			DA_FC506 += DA_FC505[DA_FC507] * pow(V_10,DA_FC507)		}		if(DA_FC506 == DA_FC126(DA_FC502)){			DA_FC509 = TRUE;			set_pvar(SPVAR_2,DA_FC509);					}		DA_FC504 = TRUE;			}	}if(DA_FC504){	cls_oled(V_00)if(!DA_FC509){		DA_FC132(DA_FC502,DA_FC531,V_10,OLED_FONT_MEDIUM,OLED_WHITE,DA_FC532)for( DA_FC507 = V_00;		DA_FC507 < V_06;		DA_FC507++){			DA_FC132(DA_FC505[DA_FC507],V_85 - (DA_FC507 * V_10),V_40,OLED_FONT_MEDIUM,!(DA_FC507 == DA_FC508),DA_FC532)		}			}	DA_FC504 = FALSE;	}	if(DA_FC509){		if (get_ival(DA_FC447) || get_ival(DA_FC451) || get_ival(DA_FC449) || get_ival(DA_FC450) || DA_FC356 || combo_running(DA_FC72) || get_info(CPU_USAGE) > V_95 ) {			vm_tctrl(V_00);					}		else{			DA_FC109(DA_FC110);					}		if(get_ival(DA_FC452) > V_40 || (!get_ival(DA_FC449) && !get_ival(DA_FC450))){			if(get_ival(DA_FC447)){				vm_tctrl(V_00);				if(get_ptime(DA_FC447) > DA_FC602){					set_val(DA_FC447,V_00);									}							}					}		if(!get_ival(DA_FC449)){			if(get_ival(DA_FC447)){				vm_tctrl(V_00);				if(get_ptime(DA_FC447) > DA_FC602){					set_val(DA_FC447,V_00);									}							}					}		if (DA_FC356) {			vm_tctrl(V_00);			if(DA_FC357 < V_8050){				DA_FC357 += get_rtime();							}			if (DA_FC357 >= V_8000) {				cls_oled(OLED_BLACK);				DA_FC357 = V_00;				DA_FC356 = FALSE;							}					}		if (block) {			if (DA_FC517 < V_310) {				DA_FC517 += get_rtime();							}			if (DA_FC517 <= V_300 ) {				DA_FC183();							}			if (DA_FC517 > V_300 ) {				block = FALSE;				DA_FC517 = V_01;				DA_FC1013 = TRUE;							}			if (DA_FC517 < V_00) {				DA_FC517 = V_01;							}			if (DA_FC517 <= V_100) {	if (DA_FC361 == V_03) {if (get_ival(DA_FC1327[-V_01 +DA_FC394])) {DA_FC240();}if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000) && !DA_FC499 && !combo_running(DA_FC0)) set_val(DA_FC1327[-V_01 +DA_FC394], V_00);}if(DA_FC362 > V_02 ){if(get_ival(DA_FC1327[ -V_01 +DA_FC279])){DA_FC238();}if( get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000  && (!!DA_FC1070 || !!DA_FC1071 || !!DA_FC1072 || !!DA_FC1073 && !combo_running(DA_FC0) ))set_val(DA_FC1327[ -V_01 +DA_FC279],V_00);										}			combo_stop(DA_FC89);				combo_stop(DA_FC98);				combo_stop(DA_FC90);				combo_stop(DA_FC99);				combo_stop(DA_FC96);				combo_stop(DA_FC97);				combo_stop(DA_FC93);				combo_stop(DA_FC95);				combo_stop(DA_FC92);				combo_stop(DA_FC88);				combo_stop(DA_FC86);				combo_stop(DA_FC91);				combo_stop(DA_FC106);				combo_stop(DA_FC108);				combo_stop(DA_FC102);				combo_stop(DA_FC107);				combo_stop(DA_FC101);							}					}		if((get_ival(PS5_L2) && event_press(PS5_R2) || event_press(PS5_L2) && get_ival(PS5_R2) )){			block = TRUE;					}		if(DA_FC437){			DA_FC438 = FALSE;					}		if(DA_FC438){			DA_FC437 = FALSE;					}		if(DA_FC361){			DA_FC362 = FALSE;			DA_FC363 = FALSE;			DA_FC364 = FALSE;					}		if(DA_FC362){			DA_FC361 = FALSE;			DA_FC363 = FALSE;			DA_FC364 = FALSE;					}		if(DA_FC363){			DA_FC361 = FALSE;			DA_FC362 = FALSE;			DA_FC364 = FALSE;					}		if(DA_FC364){			DA_FC361 = FALSE;			DA_FC362 = FALSE;			DA_FC363 = FALSE;					}		if (get_ival(PS5_L2)) {			if (get_ival(PS5_LEFT)) {				set_val(PS5_LEFT, V_00);				DA_FC1123 = -V_01			}			else if (get_ival(PS5_RIGHT)) {				set_val(PS5_RIGHT, V_00);				DA_FC1123 = V_01			}					}		if (get_ival(DA_FC452)) {			if ((abs(get_ival(DA_FC1076)) > V_85 || abs(get_ival(DA_FC1077)) > V_85) && !get_ival(PS5_R3)) {				vm_tctrl(V_00);				combo_run(DA_FC83);							}					}		if (get_ival(PS5_L2)) {			set_val(PS5_SHARE, V_00);			if (event_press(PS5_SHARE)) {				vm_tctrl(V_00);				DA_FC1018 = !DA_FC1018;				DA_FC243(DA_FC1236);				DA_FC217(DA_FC1018, sizeof(DA_FC547) - V_01, DA_FC547[V_00]);				DA_FC356 = TRUE;							}					}		if (DA_FC1018) {

			
	if(!get_ival(PS5_R3) && !combo_running(DA_FC77)){				set_val(PS5_L3,V_00);							}else if(combo_running(DA_FC77)){							set_val(PS5_L3,V_100);							}			if(DA_FC381){				DA_FC272();							}			if (DA_FC379) {				DA_FC271();							}			if (event_release(DA_FC452)) {				DA_FC550 = V_01;							}			if (DA_FC550 < V_8000) {				DA_FC550 += get_rtime();							}			if (get_ival(PS5_R2)) {				if (event_press(PS5_OPTIONS)) {					DA_FC552 = !DA_FC552;					DA_FC243(DA_FC552);									}				set_val(PS5_OPTIONS, V_00);							}			if (DA_FC552) {				if (DA_FC552) DA_FC236(DA_FC1050);				if (DA_FC552) {					DA_FC147();									}							}			else if (!get_ival(DA_FC452)) {				DA_FC236(DA_FC1053);				if (get_ival(PS5_L2)) {					if (event_press(PS5_OPTIONS)) {						DA_FC352 = TRUE;						DA_FC359 = TRUE;						DA_FC358 = FALSE;						if (!DA_FC352) {							DA_FC358 = TRUE;													}											}					set_val(PS5_OPTIONS, V_00);									}				if (!DA_FC358) {					if (DA_FC352 || DA_FC353) {						vm_tctrl(V_00);					}					if (DA_FC352) {						combo_stop(DA_FC72);						vm_tctrl(V_00);						DA_FC360= DA_FC148(DA_FC360, V_00);						DA_FC361 = DA_FC148(DA_FC361, V_01);						DA_FC362  = DA_FC148(DA_FC362, V_02);						DA_FC363  = DA_FC148(DA_FC363, V_03);						DA_FC364  = DA_FC148(DA_FC364, V_04);						DA_FC365 = DA_FC148(DA_FC365, V_05);						DA_FC366 = DA_FC148(DA_FC366, V_06);						DA_FC367 = DA_FC148(DA_FC367, V_07);						DA_FC368 = DA_FC148(DA_FC368, V_08);						DA_FC369 = DA_FC148(DA_FC369, V_09);						DA_FC370 = DA_FC148(DA_FC370, V_10);						DA_FC371 = DA_FC148(DA_FC371, V_11);						DA_FC372 = DA_FC148(DA_FC372, V_12);						DA_FC373 = DA_FC148(DA_FC373,V_13);						DA_FC374 = DA_FC148(DA_FC374, V_14);						DA_FC375 = DA_FC148(DA_FC375, V_15);						DA_FC376 = DA_FC148(DA_FC376, V_16);						DA_FC377 = DA_FC148(DA_FC377, V_17);						DA_FC378 = DA_FC148(DA_FC378, V_18);						DA_FC379 = DA_FC148(DA_FC379, V_19);						DA_FC110 = DA_FC148(DA_FC110, V_20);						DA_FC381 = DA_FC148(DA_FC381, V_21);						DA_FC382 = DA_FC148(DA_FC382 ,V_22);						DA_FC383 = DA_FC148(DA_FC383 ,V_23);						DA_FC384 = DA_FC148(DA_FC384 ,V_24);						if (event_press(PS5_DOWN)) {							DA_FC354 = clamp(DA_FC354 + V_01, V_00, DA_FC385);							DA_FC359 = TRUE;													}						if (event_press(PS5_UP)) {							DA_FC354 = clamp(DA_FC354 - V_01, V_00, DA_FC385);							DA_FC359 = TRUE;													}						if (event_press(PS5_CIRCLE)) {							DA_FC352 = FALSE;							DA_FC358 = FALSE;							DA_FC359 = FALSE;							vm_tctrl(V_00);							combo_run(DA_FC75);													}						if (DA_FC572[DA_FC354][V_02] == V_01) {							if(DA_FC354 == V_00 ){								if(DA_FC360 == V_02 ){									if (event_press(PS5_CROSS)) {										DA_FC355 = DA_FC572[DA_FC354][V_00];										DA_FC352 = FALSE;										DA_FC353 = TRUE;										DA_FC359 = TRUE;																			}																	}															}							else{								if (event_press(PS5_CROSS)) {									DA_FC355 = DA_FC572[DA_FC354][V_00];									DA_FC352 = FALSE;									DA_FC353 = TRUE;									DA_FC359 = TRUE;																	}															}													}						DA_FC183();						print(V_02, V_38, OLED_FONT_SMALL, OLED_WHITE, DA_FC563[V_00]);						DA_FC157(DA_FC354 + V_01, DA_FC163(DA_FC354 + V_01), V_28, V_38, OLED_FONT_SMALL);						print(V_84, V_38, OLED_FONT_SMALL, OLED_WHITE, DA_FC565[V_00]);						DA_FC157(DA_FC900, DA_FC163(DA_FC900), V_112, V_38, OLED_FONT_SMALL);						line_oled(V_01, V_48, V_127, V_48, V_01, V_01);						if(DA_FC354 == V_00 ){							if(DA_FC360 == V_02 ){								print(V_02, V_52, OLED_FONT_SMALL, V_01, DA_FC567[V_00]);															}							else{								print(V_02, V_52, OLED_FONT_SMALL, V_01, DA_FC568[V_00]);															}													}						else{							if (DA_FC572[DA_FC354][V_02] == V_00) {								print(V_02, V_52, OLED_FONT_SMALL, V_01, DA_FC568[V_00]);															}							else {								print(V_02, V_52, OLED_FONT_SMALL, V_01, DA_FC567[V_00]);															}													}											}					if (DA_FC353) {						DA_FC439               = DA_FC151(DA_FC439, V_00);						DA_FC440               = DA_FC151(DA_FC440, V_01);						DA_FC441             = DA_FC151(DA_FC441, V_02);						DA_FC442           = DA_FC151(DA_FC442, V_03);						DA_FC443             = DA_FC151(DA_FC443, V_04);						DA_FC444             = DA_FC151(DA_FC444, V_05);						DA_FC445              = DA_FC151(DA_FC445, V_06);						DA_FC446           = DA_FC151(DA_FC446, V_07);						DA_FC386          = DA_FC151(DA_FC386, V_08);						DA_FC387   = DA_FC151(DA_FC387, V_09);						DA_FC388 = DA_FC151(DA_FC388, V_10);						DA_FC389      = DA_FC151(DA_FC389, V_11);						DA_FC390    = DA_FC151(DA_FC390, V_12);						DA_FC391    = DA_FC151(DA_FC391, V_13);						DA_FC392    = DA_FC151(DA_FC392, V_14);						DA_FC393      = DA_FC151(DA_FC393, V_15);						DA_FC394      = DA_FC151(DA_FC394, V_16);						DA_FC275              = DA_FC151(DA_FC275, V_17);						DA_FC276           = DA_FC151(DA_FC276, V_18);						DA_FC277            = DA_FC151(DA_FC277, V_19);						DA_FC278            = DA_FC151(DA_FC278, V_20);						DA_FC279= DA_FC151(DA_FC279, V_21);						DA_FC407               = DA_FC151(DA_FC407, V_22);						DA_FC408               = DA_FC151(DA_FC408, V_23);						DA_FC409                   = DA_FC151(DA_FC409, V_24);						DA_FC410                   = DA_FC151(DA_FC410, V_25);						DA_FC411                   = DA_FC151(DA_FC411, V_26);						DA_FC412   = DA_FC151(DA_FC412, V_27);						DA_FC413   = DA_FC151(DA_FC413, V_28);						DA_FC414 = DA_FC151(DA_FC414, V_29);						DA_FC415   = DA_FC151(DA_FC415, V_30);						DA_FC416   = DA_FC151(DA_FC416, V_31);						DA_FC417 = DA_FC151(DA_FC417, V_32);						DA_FC418   = DA_FC151(DA_FC418, V_33);						DA_FC419   = DA_FC151(DA_FC419, V_34);						DA_FC420 = DA_FC151(DA_FC420, V_35);						DA_FC421   = DA_FC151(DA_FC421, V_36);						DA_FC422   = DA_FC151(DA_FC422, V_37);						DA_FC423 = DA_FC151(DA_FC423, V_38);						DA_FC424   = DA_FC154(DA_FC424, V_39);						DA_FC425         = DA_FC154(DA_FC425, V_40);						DA_FC426   = DA_FC151(DA_FC426, V_41);						DA_FC427     = DA_FC151(DA_FC427, V_42);						DA_FC428                   = DA_FC154(DA_FC428, V_43);						DA_FC1197 = DA_FC151(DA_FC1197, V_54);						DA_FC1190 = DA_FC151(DA_FC1190, V_55);						DA_FC429               = DA_FC154(DA_FC429, V_44);						DA_FC430 = DA_FC154(DA_FC430, V_45);						DA_FC431     = DA_FC151(DA_FC431, V_46);						DA_FC432               = DA_FC154(DA_FC432, V_47);						DA_FC433 = DA_FC151(DA_FC433, V_48);						DA_FC434 = DA_FC151(DA_FC434, V_49);						DA_FC435 = DA_FC151(DA_FC435, V_50);						DA_FC436 = DA_FC151(DA_FC436, V_51);						DA_FC437               = DA_FC151(DA_FC437, V_52);						DA_FC438                 = DA_FC151(DA_FC438, V_53);						DA_FC395       = DA_FC154(DA_FC395     ,V_56 );						DA_FC396       = DA_FC154(DA_FC396     ,V_57 );						DA_FC397      = DA_FC151(DA_FC397    ,V_58 );						DA_FC398   = DA_FC151(DA_FC398 ,V_59 );						DA_FC399       = DA_FC154(DA_FC399     ,V_60 );						DA_FC400       = DA_FC154(DA_FC400     ,V_61 );						DA_FC401   = DA_FC151(DA_FC401 ,V_62 );						DA_FC402      = DA_FC151(DA_FC402    ,V_63 );						DA_FC403          = DA_FC154(DA_FC403        ,V_64 );						DA_FC404          = DA_FC154(DA_FC404        ,V_65 );						DA_FC405         = DA_FC151(DA_FC405       ,V_66 );						DA_FC455             = DA_FC154(DA_FC455           ,V_67 );						DA_FC29             = DA_FC151(DA_FC29           ,V_68);						DA_FC475           = DA_FC151(DA_FC475         ,V_69);						DA_FC473         = DA_FC151(DA_FC473       ,V_70);						if (!get_ival(PS5_L2)) {							if (event_press(PS5_RIGHT)) {								DA_FC355 = clamp(DA_FC355 + V_01, DA_FC572[DA_FC354][V_00], DA_FC572[DA_FC354][V_01]);								DA_FC359 = TRUE;															}							if (event_press(PS5_LEFT)) {								DA_FC355 = clamp(DA_FC355 - V_01, DA_FC572[DA_FC354][V_00], DA_FC572[DA_FC354][V_01]);								DA_FC359 = TRUE;															}													}						if (event_press(PS5_CIRCLE)) {							DA_FC352 = TRUE;							DA_FC353 = FALSE;							DA_FC359 = TRUE;													}						DA_FC183();						DA_FC901 = DA_FC786[DA_FC355][V_00];						DA_FC902 = DA_FC786[DA_FC355][V_01];						if (DA_FC786[DA_FC355][V_04] == V_00) {							DA_FC157(DA_FC901, DA_FC163(DA_FC901), V_04, V_20, OLED_FONT_SMALL);							DA_FC157(DA_FC902, DA_FC163(DA_FC902), V_97, V_20, OLED_FONT_SMALL);													}											}					if (DA_FC359) {						cls_oled(OLED_BLACK);						rect_oled(V_00, V_00, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE);						line_oled(V_00, V_14, V_127, V_14, V_01, V_01);						if (DA_FC353) {							print(DA_FC228(DA_FC181(DA_FC580[DA_FC355]), OLED_FONT_SMALL_WIDTH), V_03, OLED_FONT_SMALL, OLED_WHITE, DA_FC580[DA_FC355]);													}						else {							print(DA_FC228(DA_FC181(DA_FC581[DA_FC354]), OLED_FONT_SMALL_WIDTH), V_03, OLED_FONT_SMALL, OLED_WHITE, DA_FC581[DA_FC354]);													}						DA_FC359 = FALSE;					}									}				if (!DA_FC352 && !DA_FC353) {					if (DA_FC358) {						cls_oled(V_00);						combo_run(DA_FC72);						DA_FC358 = FALSE;						DA_FC356 = TRUE;						vm_tctrl(V_00);					}					if(DA_FC360 == V_00){						DA_FC447      = PS5_CIRCLE;						DA_FC448      = PS5_CROSS ;						DA_FC449    = PS5_L1    ;						DA_FC450  = PS5_R1;						DA_FC451    = PS5_L2;						DA_FC452    = PS5_R2;						DA_FC453     = PS5_SQUARE;						DA_FC454  = PS5_TRIANGLE;					}					else if(DA_FC360 == V_01){						DA_FC447      = PS5_SQUARE;						DA_FC448      = PS5_CROSS ;						DA_FC449    = PS5_L1    ;						DA_FC450  = PS5_R1;						DA_FC451    = PS5_L2;						DA_FC452    = PS5_R2;						DA_FC453     = PS5_CIRCLE;						DA_FC454  = PS5_TRIANGLE;					}					else if(DA_FC360 == V_02){						DA_FC447      = DA_FC1317[DA_FC439];						DA_FC448      = DA_FC1317[DA_FC440] ;						DA_FC449    = DA_FC1317[DA_FC441]  ;						DA_FC450  = DA_FC1317[DA_FC442];						DA_FC451    = DA_FC1317[DA_FC443];						DA_FC452    = DA_FC1317[DA_FC444];						DA_FC453     = DA_FC1317[DA_FC445];						DA_FC454  = DA_FC1317[DA_FC446];					}					if (DA_FC585 >= V_2000) {						DA_FC585 = V_2000;											}					else if (DA_FC585 <= V_50) {						DA_FC585 = V_50;											}					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA_FC1146) {						set_val(DA_FC448, V_00);						vm_tctrl(V_00);						combo_run(DA_FC77);											}					if (DA_FC1013) {						if ((get_ipolar(POLAR_LS,POLAR_RADIUS) > V_5100) ){							DA_FC587 = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + V_23) % V_360) / -V_45) + V_10) % V_08;							DA_FC1008 = DA_FC1328[DA_FC587][V_00];							DA_FC664 = DA_FC1328[DA_FC587][V_01];													}					}					if (get_ival(XB1_RS)) {						if (event_press(PS5_RIGHT)) {							DA_FC428 += V_05;							DA_FC224(DA_FC228(sizeof(DA_FC589) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC589[V_00], DA_FC428);													}						if (event_press(PS5_LEFT)) {							DA_FC428 -= V_05;							DA_FC224(DA_FC228(sizeof(DA_FC589) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC589[V_00], DA_FC428);													}						set_val(PS5_RIGHT, V_00);						set_val(PS5_LEFT, V_00);											}					if (get_ival(XB1_RS) && !DA_FC609 ) {						if (event_press(PS5_UP)) {							DA_FC585 += V_50;							DA_FC224(DA_FC228(sizeof(DA_FC595) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC595[V_00], DA_FC585);													}						if (event_press(PS5_DOWN)) {							DA_FC585 -= V_50;							DA_FC224(DA_FC228(sizeof(DA_FC595) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC595[V_00], DA_FC585);													}						set_val(PS5_UP, V_00);						set_val(PS5_DOWN, V_00);											}					if (DA_FC374) {						DA_FC262();											}					if (DA_FC375) {						DA_FC263();						DA_FC264();											}					if (!DA_FC375) {						if (get_ival(DA_FC447)) {							if (event_press(PS5_RIGHT)) {								DA_FC602 += V_02;								DA_FC224(DA_FC228(sizeof(DA_FC603) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC603[V_00], DA_FC602);															}							if (event_press(PS5_LEFT)) {								DA_FC602 -= V_02;								DA_FC224(DA_FC228(sizeof(DA_FC603) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC603[V_00], DA_FC602);															}							set_val(PS5_RIGHT, V_00);							set_val(PS5_LEFT, V_00);													}						if(!get_ival(DA_FC449) ){							if(get_ival(DA_FC447) && get_ptime(DA_FC447) > DA_FC602){								set_val(DA_FC447,V_00);															}													}											}					if(DA_FC378){						DA_FC267();											}					if (DA_FC370) {						if (get_ival(PS5_L1)) {							if (event_press(PS5_SHARE)) {								DA_FC609 = !DA_FC609;								DA_FC243(DA_FC609);															}							set_val(PS5_SHARE, V_00);													}											}					if (DA_FC609 && DA_FC370) {						vm_tctrl(V_00);						combo_stop(DA_FC86);						if (get_ival(XB1_RS)) {							if (event_press(PS5_UP)) {								DA_FC424 += V_10;								DA_FC224(DA_FC228(sizeof(DA_FC611) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC611[V_00], DA_FC424);															}							if (event_press(PS5_DOWN)) {								DA_FC424 -= V_10;								DA_FC224(DA_FC228(sizeof(DA_FC611) - V_01, OLED_FONT_MEDIUM_WIDTH), DA_FC611[V_00], DA_FC424);															}							set_val(PS5_UP, V_00);							set_val(PS5_DOWN, V_00);													}						DA_FC236(DA_FC1052);						if (get_ival(PS5_L1)) {							if (event_press(PS5_RIGHT)) {								DA_FC616 = FALSE;								vm_tctrl(V_00);								combo_run(DA_FC78);															}							if (event_press(PS5_LEFT)) {								DA_FC616 = TRUE;								vm_tctrl(V_00);								combo_run(DA_FC78);															}							set_val(PS5_L1,V_00);													}											}					if (DA_FC371) {						if (get_ival(PS5_L1)) {							if (event_press(PS5_OPTIONS)) {								DA_FC618 = !DA_FC618;								DA_FC243(DA_FC618);															}							set_val(PS5_OPTIONS, V_00);													}											}					if (DA_FC618 && DA_FC371) {						vm_tctrl(V_00);						DA_FC236(DA_FC1054);						if (get_ival(PS5_L1)) {							if (event_press(PS5_LEFT)) {								DA_FC619 = FALSE;								vm_tctrl(V_00);								combo_run(DA_FC79);															}							if (event_press(PS5_RIGHT)) {								DA_FC619 = TRUE;								vm_tctrl(V_00);								combo_run(DA_FC79);															}													}											}					if(DA_FC363 || DA_FC364 ){						DA_FC114();											}					if (DA_FC361) {						if (DA_FC361 == DA_FC1058) DA_FC622 = TRUE;						if (DA_FC361 == DA_FC1059) {							if (event_press(DA_FC1327[-V_01 +DA_FC394]) && get_brtime(DA_FC1327[-V_01 +DA_FC394]) <= V_200) {								DA_FC622 = !DA_FC622;								DA_FC243(DA_FC622);															}							set_val(DA_FC1327[-V_01 +DA_FC394], V_00);													}						if (DA_FC361 > V_00 && DA_FC361 < V_03 && DA_FC622 == V_01) {							DA_FC240();													}						else if (DA_FC361 == V_03) {							if (get_ival(DA_FC1327[-V_01 +DA_FC394])) {								DA_FC240();															}							if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000) && !DA_FC499 && !combo_running(DA_FC0)) set_val(DA_FC1327[-V_01 +DA_FC394], V_00);													}											}					if( DA_FC362 == V_00)        DA_FC625 = FALSE;					if( DA_FC362 == DA_FC1058) DA_FC625 = TRUE;					if( DA_FC362 == DA_FC1059) {						if (event_press( DA_FC1327[ -V_01 +DA_FC279]) && get_brtime(DA_FC1327[DA_FC279])<=V_200){							DA_FC625 = !DA_FC625;							DA_FC243(DA_FC625);													}						set_val(DA_FC1327[ -V_01 +DA_FC279],V_00);											}					if(DA_FC625 ){						DA_FC238();											}					if(DA_FC362 > V_02 ){						if(get_ival(DA_FC1327[ -V_01 +DA_FC279])){							DA_FC238();													}						if( get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000  && (!!DA_FC1070 || !!DA_FC1071 || !!DA_FC1072 || !!DA_FC1073 && !combo_running(DA_FC0) ))set_val(DA_FC1327[ -V_01 +DA_FC279],V_00);											}					if (DA_FC365) {						if (DA_FC365 == V_01) {							DA_FC628 = PS5_R3;							DA_FC625 = FALSE;													}						if (DA_FC365 == V_02) {							DA_FC628 = PS5_L3;							DA_FC625 = FALSE;													}						if (DA_FC365 == V_03) {							DA_FC628 = XB1_PR1;							DA_FC625 = FALSE;													}						if (DA_FC365 == V_04) {							DA_FC628 = XB1_PR2;							DA_FC625 = FALSE;													}						if (DA_FC365 == V_05) {							DA_FC628 = XB1_PL1;							DA_FC625 = FALSE;													}						if (DA_FC365 == V_06) {							DA_FC628 = XB1_PL2;							DA_FC625 = FALSE;													}						if(get_ival(DA_FC628)){							if(DA_FC407 || DA_FC408){								if( DA_FC407 && event_press(PS5_L1)){									DA_FC478 = FALSE;									DA_FC1007 = DA_FC407  ;									DA_FC241( DA_FC407   );								}								if( DA_FC408 && event_press(PS5_R1)){									DA_FC478 = TRUE;									DA_FC1007 =  DA_FC408 ;									DA_FC241( DA_FC408   );																	}								set_val(PS5_L1,V_00);								set_val(PS5_R1,V_00);								block = TRUE;															}							if( DA_FC409 ){								if(event_press(PS5_SQUARE)){									DA_FC478 = FALSE;									DA_FC1007 =  DA_FC409  ;									DA_FC241( DA_FC409   );								}								if(event_press(PS5_TRIANGLE)){									DA_FC478 = TRUE;									DA_FC1007 =  DA_FC409  ;									DA_FC241( DA_FC409   );								}								set_val(PS5_SQUARE,V_00);								set_val(PS5_TRIANGLE,V_00);								block = TRUE;															}							if( DA_FC410 ){								if(event_press(PS5_CROSS)){									DA_FC478 = FALSE;									DA_FC1007 =  DA_FC410  ;									DA_FC241( DA_FC410   );								}								if(event_press(PS5_CIRCLE)){									DA_FC478 = TRUE;									DA_FC1007 =  DA_FC410  ;									DA_FC241( DA_FC410   );								}								set_val(PS5_CROSS,V_00);								set_val(PS5_CIRCLE,V_00);								block = TRUE;															}							if( DA_FC411 ){								if(event_press(PS5_R3)){									DA_FC478 = FALSE;									DA_FC1007 =  DA_FC411  ;									DA_FC241( DA_FC411   );								}								set_val(PS5_R3,V_00);								block = TRUE;															}													}						set_val(DA_FC628,V_00);											}					if (DA_FC366) {						if (DA_FC413 == V_01) {							if (event_press(DA_FC1327[-V_01 + DA_FC412]) && !DA_FC1098) {								vm_tctrl(V_00);								combo_run(DA_FC82);															}							else if (event_press(DA_FC1327[-V_01 + DA_FC412]) && DA_FC1098) {								set_val(DA_FC1327[-V_01 + DA_FC412], V_00);								DA_FC478 = !DA_FC414;								DA_FC1007 = DA_FC366;								DA_FC241(DA_FC366);															}													}						else {							if (event_press(DA_FC1327[-V_01 + DA_FC412])) {								DA_FC478 = !DA_FC414;								set_val(DA_FC1327[-V_01 + DA_FC412], V_00);								DA_FC1007 = DA_FC366;								DA_FC241(DA_FC366);															}													}					}					if (DA_FC368) {						if (DA_FC419 == V_01) {							if (event_press(DA_FC1327[-V_01 +DA_FC418]) && !DA_FC1098) {								vm_tctrl(V_00);								combo_run(DA_FC82);															}							else if (event_press(DA_FC1327[-V_01 +DA_FC418]) && DA_FC1098) {								set_val(DA_FC1327[-V_01 +DA_FC418], V_00);								DA_FC478 = !DA_FC420;								DA_FC1007 = DA_FC368;								DA_FC241(DA_FC368);															}													}						else {							if (event_press(DA_FC1327[-V_01 +DA_FC418])) {								DA_FC478 = !DA_FC420;								set_val(DA_FC1327[-V_01 +DA_FC418], V_00);								DA_FC1007 = DA_FC368;								DA_FC241(DA_FC368);															}													}					}					if (DA_FC367) {						if (DA_FC416 == V_01) {							if (event_press(DA_FC1327[-V_01 +DA_FC415]) && !DA_FC1098) {								vm_tctrl(V_00);								combo_run(DA_FC82);															}							else if (event_press(DA_FC1327[-V_01 +DA_FC415]) && DA_FC1098) {								set_val(DA_FC1327[-V_01 +DA_FC415], V_00);								DA_FC478 = !DA_FC417;								DA_FC1007 = DA_FC367;								DA_FC241(DA_FC367);															}													}						else {							if (event_press(DA_FC1327[-V_01 +DA_FC415])) {								DA_FC478 = !DA_FC417;								set_val(DA_FC1327[-V_01 +DA_FC415], V_00);								DA_FC1007 = DA_FC367;								DA_FC241(DA_FC367);															}													}					}					if (DA_FC369) {						if (DA_FC422 == V_01) {							if (event_press(DA_FC1327[-V_01 +DA_FC421]) && !DA_FC1098) {								vm_tctrl(V_00);								combo_run(DA_FC82);															}							else if (event_press(DA_FC1327[-V_01 +DA_FC421]) && DA_FC1098) {								set_val(DA_FC1327[-V_01 +DA_FC421], V_00);								DA_FC478 = !DA_FC423;								DA_FC1007 = DA_FC369;								DA_FC241(DA_FC369);															}													}						else {							if (event_press(DA_FC1327[-V_01 +DA_FC421])) {								DA_FC478 = !DA_FC423;								set_val(DA_FC1327[-V_01 +DA_FC421], V_00);								DA_FC1007 = DA_FC369;								DA_FC241(DA_FC369);															}													}					}					if (DA_FC1007 == DA_FC310 && combo_running(DA_FC30)) set_val(DA_FC449, V_100);					if(DA_FC383){						if(!block){							if(!get_val(DA_FC451)){								if( !get_val(DA_FC452)){									if(get_val(DA_FC448)){										DA_FC645 += get_rtime();																			}									if(DA_FC402){										if(get_ival(DA_FC448) && get_ptime(DA_FC448) > DA_FC400){											set_val(DA_FC448,V_00);																					}																			}									if(event_release(DA_FC448)){										if( DA_FC645 < DA_FC399 ){											DA_FC646 = DA_FC399 - DA_FC645;											combo_run(DA_FC106);																					}										else{											if(DA_FC401) combo_run(DA_FC107);																					}										DA_FC645 = V_00;																			}																	}							}						}											}					if(DA_FC382){						if(!block){							if(!get_ival(DA_FC451)){								if( !get_ival(DA_FC452)){									if(get_ival(DA_FC454)){										DA_FC647 += get_rtime();																			}									if(event_release(DA_FC454)){										if(DA_FC647 < DA_FC395){											DA_FC648 = DA_FC395 - DA_FC647;											combo_run(DA_FC108);																					}										else{											if(DA_FC398) combo_run(DA_FC101);																					}										DA_FC647 = V_00;																			}																	}							}						}											}					if(DA_FC384){						if(!block){							if(get_ival(DA_FC453)){								DA_FC649 += get_rtime();															}							if(DA_FC405){								if(get_ival(DA_FC453) && get_ptime(DA_FC453) > DA_FC404){									set_val(DA_FC453,V_00);																	}															}							if(event_release(DA_FC453)){								if(DA_FC649 && (DA_FC649 < DA_FC403)){									DA_FC650 = DA_FC403 - DA_FC649;									combo_run(DA_FC102);																	}								DA_FC649 = V_00;															}													}											}					if (DA_FC372) {						if (event_press(DA_FC1327[-V_01 +DA_FC426])) {							vm_tctrl(V_00);							combo_run(DA_FC77);													}						set_val(DA_FC1327[-V_01 +DA_FC426], V_00);											}					if(!DA_FC376){						DA_FC429 = V_00 ;						DA_FC430 = V_00;						DA_FC431 = FALSE;						DA_FC432 = V_00;											}					if (DA_FC377) {						DA_FC263();						if (DA_FC433 == V_00) {							DA_FC653 = FALSE;							DA_FC457 = V_00;													}						else {							DA_FC653 = TRUE;							DA_FC457 = V_40;													}						if (DA_FC434 == V_00) {							DA_FC655 = FALSE;							DA_FC456 = V_00;													}						else {							DA_FC655 = TRUE;							DA_FC456 = V_85;													}						if (DA_FC435 == V_00) {							DA_FC657 = FALSE;							DA_FC458 = V_00;													}						else {							DA_FC657 = TRUE;							DA_FC458 = -V_15;													}						if (DA_FC436 == V_00) {							DA_FC659 = FALSE;													}						else {							DA_FC659 = TRUE;													}						if(DA_FC435 == V_06 || DA_FC434 == V_06 || DA_FC433 == V_06){							if (get_ival(DA_FC1327[-V_01 + DA_FC435]) || get_ival(DA_FC1327[-V_01 + DA_FC434]) || get_ival(DA_FC1327[-V_01 + DA_FC433])){								combo_run(DA_FC0);															}													}						if (DA_FC657) {							if (get_val(DA_FC1327[-V_01 + DA_FC435])) {								set_val(DA_FC1327[-V_01 + DA_FC435], V_00);								combo_run(DA_FC98);								DA_FC1155 = V_9000;															}													}						if (DA_FC659) {							if (get_val(DA_FC1327[-V_01 + DA_FC436])) {								set_val(DA_FC1327[-V_01 + DA_FC436], V_00);								combo_run(DA_FC99);								DA_FC1155 = V_9000;							}							if (combo_running(DA_FC99)) {								if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452) > V_30) {									combo_stop(DA_FC99);																	}															}													}						if (DA_FC655) {							if (get_val(DA_FC1327[-V_01 + DA_FC434])) {								set_val(DA_FC1327[-V_01 + DA_FC434], V_00);								DA_FC270();								DA_FC1155 = V_9000;															}													}						if (DA_FC653) {							if (get_val(DA_FC1327[-V_01 + DA_FC433])) {								set_val(DA_FC1327[-V_01 + DA_FC433], V_00);								combo_run(DA_FC96);								DA_FC1155 = V_9000;															}													}											}					else{						DA_FC457 = V_00;						DA_FC458 = V_00;						DA_FC456 = V_00;											}					if (DA_FC379) {						DA_FC271();											}									}							}			if(get_ival(DA_FC452)){				DA_FC663 = V_00;				combo_stop(DA_FC88);							}					}		else {			if (!get_ival(DA_FC452)) DA_FC236(DA_FC1051);					}			}				check();
	
	}

combo DA_FC1 { 	set_val(DA_FC451, V_100); 	set_val(DA_FC449,V_100); 	DA_FC257(); 	wait(V_400); 	set_val(DA_FC448,V_100); 	wait(V_90); 	vm_tctrl(V_00); 	wait( V_400); 	} combo DA_FC2 { 	set_val(DA_FC451, V_100); 	set_val(DA_FC449,V_100); 	DA_FC257(); 	wait(V_400); 	set_val(DA_FC447,V_100); 	wait(V_220); 	vm_tctrl(V_00); 	wait( V_400); 	} combo DA_FC3 { 	call(DA_FC28); 	vm_tctrl(V_00); 	wait( V_100); 	call(DA_FC99); 	DA_FC253(DA_FC1008, DA_FC664); 	vm_tctrl(V_00); 	wait( V_800); 	vm_tctrl(V_00); 	wait( V_350); 	set_val(DA_FC450,V_100); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( V_400); 	} combo DA_FC4 { 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC5 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC6 { 	if (DA_FC478) DA_FC667 = DA_FC587 + V_01; 	else DA_FC667 = DA_FC587 - V_01; 	DA_FC248(DA_FC667); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_1000); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC7 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = FALSE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = TRUE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC8 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = TRUE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = FALSE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC9 { 	DA_FC478 = TRUE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = FALSE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC10 { 	DA_FC478 = FALSE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = TRUE; 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC11 { 	DA_FC253(V_00,V_00); 	set_val(DA_FC449,V_100); 	set_val(DA_FC450,V_100); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_60); 	} combo DA_FC12 { 	set_val(DA_FC1074, inv(DA_FC1008)); 	set_val(DA_FC1075, inv(DA_FC664)); 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC1074, inv(DA_FC1008)); 	set_val(DA_FC1075, inv(DA_FC664)); 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	vm_tctrl(V_00); 	wait( V_500); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC13 { 	DA_FC253(V_00, V_00); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(V_00, V_00); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(V_00, V_00); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	DA_FC253(V_00, V_00); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC14 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC15 { 	set_val(DA_FC449, V_100); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_500); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC16 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC17 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC18 { 	DA_FC258(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC19 { 	DA_FC258(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC20 { 	DA_FC258(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	DA_FC257()  	  vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC478 = !DA_FC478; 	DA_FC256(); 	vm_tctrl(V_00); 	wait( V_1000); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC21 { 	set_val(DA_FC449,V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait(V_60); 	DA_FC253(V_00,V_00); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait(V_60); 	set_val(DA_FC449,V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait(V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC22 { 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC23 { 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( V_80); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC24 { 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	vm_tctrl(V_00); 	wait( V_20); 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_04; 	else { 		DA_FC667 = DA_FC587 - V_04; 			} 	DA_FC248(DA_FC667); 	DA_FC250(DA_FC1102, DA_FC665); 	set_val(DA_FC452, V_100); 	vm_tctrl(V_00); 	wait( V_100); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC25 { 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_30); 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC26 { 	set_val(DA_FC451, V_100); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC27 { 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_30); 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_400); 	set_val(PS5_L2, V_100); 	set_val(PS5_L1, V_100); 	set_val(PS5_R1, V_100); 	set_val(PS5_R2, V_100); 	vm_tctrl(V_00); 	wait( V_70); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC28 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_300); 	set_val(PS5_R3,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); } combo DA_FC29 { 	DA_FC257(); 	set_val(DA_FC452, V_00); 	vm_tctrl(V_00); 	wait( V_310); 	vm_tctrl(V_00); 	wait( V_100); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC30 { 	if (DA_FC1007 == DA_FC312) DA_FC1012 = V_200; 	else DA_FC1012 = V_01; 	vm_tctrl(V_00); 	wait( DA_FC1012); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC31 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_300); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC258(); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC32 { 	if (DA_FC1007 == DA_FC312) DA_FC1012 = V_200; 	else DA_FC1012 = V_01; 	set_val(DA_FC451,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1012); 	DA_FC259(); 	set_val(DA_FC451,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	set_val(DA_FC451,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	set_val(DA_FC451,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC33 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_250); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( V_50); 	DA_FC478 = !DA_FC478; 	if (DA_FC478) DA_FC667 = DA_FC587 - V_02; 	else DA_FC667 = DA_FC587 + V_02; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_45); 	set_val(DA_FC447, V_100); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_45); 	DA_FC253(DA_FC1102, DA_FC665); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_45); 	DA_FC253(DA_FC1102, DA_FC665); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_45); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_500); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC34 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_280); 	DA_FC256()  set_val(DA_FC447, V_100); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC256()  set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC256()  set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_250); 	DA_FC256()  vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC35 { 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_300); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC36 { 	set_val(DA_FC449, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC449, V_100); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC449, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC37 { 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC38 { 	set_val(DA_FC449, V_100); 	DA_FC258(); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC449, V_100); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC449, V_100); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC39 { 	DA_FC260(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC40 { 	if (DA_FC478) DA_FC667 = DA_FC587 + V_03; 	else DA_FC667 = DA_FC587 - V_03; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(DA_FC1102, DA_FC665); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	DA_FC253(DA_FC1102, DA_FC665); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC41 { 	set_val(DA_FC449, V_100); 	DA_FC259(); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC449, V_100); 	DA_FC261(); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC449, V_100); 	DA_FC253(V_00, V_00); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_01; 	else DA_FC667 = DA_FC587 - V_01; 	DA_FC248(DA_FC667); 	set_val(DA_FC452,V_00); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_200); 	set_val(DA_FC452,V_00); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC42 { 	if (DA_FC1007 == DA_FC312) DA_FC1012 = V_200; 	else DA_FC1012 = V_01; 	vm_tctrl(V_00); 	wait( DA_FC1012); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC43 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(PS5_L2, V_100); 	set_val(PS5_R2, V_100); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC44 { 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	if (DA_FC1007 == DA_FC325) DA_FC256(); 	set_val(DA_FC451, V_100); 	set_val(DA_FC452, V_100); 	vm_tctrl(V_00); 	wait( V_200); 	if (DA_FC1007 == DA_FC325) DA_FC256(); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC45 { 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	if (DA_FC1007 == DA_FC325) DA_FC256(); 	set_val(DA_FC451, V_100); 	set_val(DA_FC452, V_100); 	vm_tctrl(V_00); 	wait( V_200); 	if (DA_FC1007 == DA_FC325) DA_FC256(); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC46 { 	DA_FC260(); 	set_val(DA_FC450,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC257(); 	set_val(DA_FC450,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	set_val(DA_FC450,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC47 { 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	set_val(DA_FC452, V_100); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	vm_tctrl(V_00); 	wait( V_600); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC48 { 	DA_FC253(DA_FC1008, DA_FC664); 	set_val(XB1_LS, V_100); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC261(); 	set_val(XB1_LS, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	set_val(DA_FC451, V_100); 	set_val(DA_FC452, V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_04; 	else DA_FC667 = DA_FC587 - V_04; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_220); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_04; 	else DA_FC667 = DA_FC587 - V_04; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_60); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_01; 	else DA_FC667 = DA_FC587 - V_01; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_600); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC49 { 	set_val(DA_FC448, V_00); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_80); 	vm_tctrl(V_00); 	wait( V_500); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC50 { 	set_val(DA_FC447, V_100); 	set_val(DA_FC452,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC452,V_100); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	set_val(DA_FC452,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	set_val(DA_FC452,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC51 { 	set_val(DA_FC449,V_100); 	set_val(DA_FC450,V_100); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	vm_tctrl(V_00); 	wait( V_200); 	set_val(DA_FC449,V_100); 	set_val(DA_FC450,V_100); 	DA_FC478 = FALSE; 	DA_FC256(); 	vm_tctrl(V_00); 	wait( V_50); 	set_val(DA_FC449,V_100); 	set_val(DA_FC450,V_100); 	DA_FC478 = !DA_FC478; 	DA_FC256(); 	set_val(DA_FC449,V_100); 	set_val(DA_FC450,V_100); 	vm_tctrl(V_00); 	wait( V_540); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC52 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_140); 	set_val(PS5_L2, V_100); 	set_val(PS5_R2, V_100); 	vm_tctrl(V_00); 	wait( V_100); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC53 { 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(inv(DA_FC1008), inv(DA_FC664)); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC54 { 	set_val(DA_FC449, V_100); 	set_val(DA_FC453, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC449, V_100); 	set_val(DA_FC453, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC449, V_100); 	set_val(DA_FC453, V_00); 	set_val(DA_FC448, V_100); 	DA_FC256(); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC449, V_100); 	DA_FC256(); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC55 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_290); 	set_val(PS5_L2, V_100); 	set_val(PS5_R2, V_100); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC56 { 	set_val(DA_FC447, V_100); 	set_val(DA_FC451,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC451,V_100); 	set_val(DA_FC447, V_100); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC451,V_100); 	set_val(DA_FC447, V_00); 	set_val(DA_FC448, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC57 { 	set_val(DA_FC449, V_100); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( V_300); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC58 { 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC59 { 	set_val(DA_FC449,V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	DA_FC259(); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( DA_FC1011); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC60 { 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC261(); 	DA_FC253(DA_FC1008, DA_FC664); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC259(); 	DA_FC253(DA_FC1008, DA_FC664); 	vm_tctrl(V_00); 	wait( V_320); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( V_220); 	DA_FC253(DA_FC1008, DA_FC664); 	DA_FC259(); 	DA_FC253(DA_FC1008, DA_FC664); 	vm_tctrl(V_00); 	wait( V_100); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC61 { 	call(DA_FC84); 	DA_FC253(V_00, V_00); 	call(DA_FC85); 	call(DA_FC85); 	call(DA_FC85); 	call(DA_FC85); 	call(DA_FC85); 	set_val(DA_FC451, V_100); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( V_70); 	set_val(DA_FC451, V_100); 	DA_FC261(); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC451, V_100); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_600); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC62 { 	set_val(DA_FC1074, inv(DA_FC1008)); 	set_val(DA_FC1075, inv(DA_FC664)); 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC1074, inv(DA_FC1008)); 	set_val(DA_FC1075, inv(DA_FC664)); 	set_val(DA_FC450, V_100); 	set_val(DA_FC449, V_100); 	set_val(PS5_R3, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC63 { 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC253(V_00, V_00); 	DA_FC259(); 	vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(V_00, V_00); 	DA_FC261()  vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(V_00, V_00); 	DA_FC259()  vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(V_00, V_00); 	DA_FC261()  vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(V_00, V_00); 	DA_FC260(); 	vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC64 { 	set_val(PS5_R3,V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_01; 	else DA_FC667 = DA_FC587 - V_01; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_400); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC65 { 	call(DA_FC84); 	DA_FC253(V_00,V_00); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(PS5_R3,V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_01; 	else DA_FC667 = DA_FC587 - V_01; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_400); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC66 { 	call(DA_FC84); 	DA_FC253(V_00,V_00); 	set_val(DA_FC451,V_100); 	set_val(DA_FC452,V_100); 	vm_tctrl(V_00); 	wait( V_750); 	} combo DA_FC67 { 	set_val(PS5_R3,V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 + V_02; 	else DA_FC667 = DA_FC587 - V_02; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_70); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_400); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC68 { 	set_val(DA_FC451,V_100); 	set_val(PS5_R3,V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 ; 	else DA_FC667 = DA_FC587; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_70); 	set_val(DA_FC451,V_100); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_400); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC69 { 	call(DA_FC84); 	set_val(DA_FC451,V_100); 	set_val(PS5_R3,V_100); 	if (DA_FC478) DA_FC667 = DA_FC587 ; 	else DA_FC667 = DA_FC587; 	DA_FC248(DA_FC667); 	DA_FC253(DA_FC1102, DA_FC665); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_70); 	set_val(DA_FC451,V_100); 	DA_FC253(DA_FC1102, DA_FC665); 	vm_tctrl(V_00); 	wait( V_400); 	vm_tctrl(V_00); 	wait( V_350); 	} combo DA_FC70 { 	DA_FC253(V_00,V_00); 	set_val(DA_FC450,V_100); 	set_val(DA_FC449,V_100); 	DA_FC257(); 	vm_tctrl(V_00); 	wait( V_350); 	vm_tctrl(V_00); 	wait( V_350); 	set_val(DA_FC450,V_100); 	set_val(DA_FC449,V_100); 	vm_tctrl(V_00); 	wait( V_400); 	} 	 int DA_FC140 ; int DA_FC712 ; int DA_FC713 ; int DA_FC714; int DA_FC715; function DA_FC126(DA_FC127){ 	DA_FC712 = V_02; 	DA_FC713 = V_987654; 	DA_FC140 = V_54321; 	DA_FC714 = (DA_FC127 >> DA_FC712) | (DA_FC127 << (V_32 - DA_FC712)); 	DA_FC715 = (((DA_FC714 >> ((DA_FC714 & 0xF) % V_13)) & 0x7FFFF) + DA_FC140) % DA_FC713 + V_123456; 	return DA_FC715; 	} define DA_FC717 = -1; define DA_FC531 = -2; define DA_FC719 = -3; define DA_FC720 = 0x0; define DA_FC532 = 0x1; function DA_FC128(DA_FC127, DA_FC130, DA_FC131) { 	if(DA_FC127 > DA_FC131) return DA_FC130; 	if(DA_FC127 < DA_FC130) return DA_FC131; 	return DA_FC127; 	} int DA_FC724,DA_FC725; function DA_FC132(DA_FC133,DA_FC134,DA_FC135,DA_FC136,DA_FC137,DA_FC138){ 	if(!DA_FC138){ 		print(DA_FC141(DA_FC139(DA_FC133),DA_FC136,DA_FC134),DA_FC135,DA_FC136,DA_FC137,DA_FC133)     	} 	else{ 		if(DA_FC133 < V_00){ 			putc_oled(V_01,V_45); 					} 		if(DA_FC133){ 			for(DA_FC724 = DA_FC145(DA_FC133) + DA_FC725 = (DA_FC133 < V_00 ),DA_FC133 = abs(DA_FC133); 			DA_FC133 > V_00; 			DA_FC724-- , DA_FC725++){ 				putc_oled(DA_FC724,DA_FC133%V_10 + V_48); 				DA_FC133 = DA_FC133/V_10; 							} 					} 		else{ 			putc_oled(V_01,V_48); 			DA_FC725 = V_01         		} 		puts_oled(DA_FC141(DA_FC725,DA_FC136,DA_FC134),DA_FC135,DA_FC136,DA_FC725 ,DA_FC137); 			} 	} int DA_FC746; function DA_FC139(DA_FC140) { 	DA_FC746 = V_00; 	do { 		DA_FC140++; 		DA_FC746++; 			} 	while (duint8(DA_FC140)); 	return DA_FC746; 	} function DA_FC141(DA_FC142,DA_FC136,DA_FC134) { 	if(DA_FC134 == -V_03){ 		return V_128 - ((DA_FC142 * (V_07 + (DA_FC136 > V_01) + DA_FC136 * V_04)) + V_03 ); 			} 	if(DA_FC134 == -V_02){ 		return V_64 - ((DA_FC142 * (V_07 + (DA_FC136 > V_01) + DA_FC136 * V_04)) / V_02); 			} 	if(DA_FC134 == -V_01){ 		return V_03 	} 	return DA_FC134; 	} function DA_FC145(DA_FC146) { 	for(DA_FC724 = V_01; 	DA_FC724 < V_11; 	DA_FC724++){ 		if(!(abs(DA_FC146) / pow(V_10,DA_FC724))){ 			return DA_FC724; 			break; 					} 			} 	return V_01; 	} function DA_FC147() { 	if (get_ival(DA_FC447)) { 		set_val(DA_FC447, V_00); 		if (get_ival(DA_FC449)) DA_FC752 = V_50; 		if (!get_ival(DA_FC449)) DA_FC752 = V_440; 		combo_run(DA_FC71); 			} 	if (DA_FC751 > V_00) set_polar(POLAR_LS, DA_FC751 * -V_01, V_32767); 	if (get_ival(PS5_RIGHT) && get_ival(PS5_DOWN)) DA_FC751 = V_345; 	if (get_ival(PS5_RIGHT) && get_ival(PS5_UP)) DA_FC751 = V_45; 	if (get_ival(PS5_LEFT) && get_ival(PS5_UP)) DA_FC751 = V_135; 	if (get_ival(PS5_LEFT) && get_ival(PS5_DOWN)) DA_FC751 = V_225; 	if (event_press(PS5_LEFT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA_FC751 = V_180; 	if (event_press(PS5_RIGHT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA_FC751 = V_01; 	if (event_press(PS5_UP) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA_FC751 = V_90; 	if (event_press(PS5_DOWN) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA_FC751 = V_270; } int DA_FC752; int DA_FC552; int DA_FC751; combo DA_FC71 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC752); 	vm_tctrl(V_00); 	wait( V_50); 	vm_tctrl(V_00); 	wait( V_3800); 	DA_FC552 = !DA_FC552; } define DA_FC755 = 0x13; function DA_FC148(DA_FC149, DA_FC150) { 	if (DA_FC354 == DA_FC150) { 		if (event_press(PS5_RIGHT)) { 			DA_FC149 = clamp(DA_FC149 + V_01, V_00, DA_FC758[DA_FC354]); 			DA_FC359 = TRUE; 					} 		if (event_press(PS5_LEFT)) { 			DA_FC149 = clamp(DA_FC149 - V_01, V_00, DA_FC758[DA_FC354]); 			DA_FC359 = TRUE; 					} 		if (DA_FC354 == V_00) { 			print(DA_FC228(DA_FC181(DA_FC762[DA_FC360]) ,OLED_FONT_SMALL_WIDTH),DA_FC755  ,OLED_FONT_SMALL , OLED_WHITE ,DA_FC762[DA_FC360]); 					} 		else if (DA_FC354 == V_01) { 			print(DA_FC228(DA_FC181(DA_FC764[DA_FC361]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC764[DA_FC361]); 					} 		else if (DA_FC354 == V_02) { 			print(DA_FC228(DA_FC181(DA_FC764[DA_FC362]) ,OLED_FONT_SMALL_WIDTH ),DA_FC755  ,OLED_FONT_SMALL , OLED_WHITE ,DA_FC764[DA_FC362]); 					} 		else if (DA_FC354 == V_05) { 			print(DA_FC228(DA_FC181(DA_FC768[DA_FC365]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC768[DA_FC365]); 					} 		else if (DA_FC354 == V_06) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC366]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC366]); 					} 		else if (DA_FC354 == V_07) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC367]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC367]); 					} 		else if (DA_FC354 == V_08) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC368]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC368]); 					} 		else if (DA_FC354 == V_09) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC369]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC369]); 					} 		else if (DA_FC354 == V_20) { 			print(DA_FC228(DA_FC181(DA_FC778[DA_FC110]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC778[DA_FC110]); 					} 		else { 			if (DA_FC149 == V_01)        print(DA_FC228(DA_FC181(DA_FC780[V_01]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[V_01])      else        print(DA_FC228(DA_FC181(DA_FC780[V_00]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[V_00])     		} 			} 	return DA_FC149; 	} function DA_FC151(DA_FC149, DA_FC150) { 	if (DA_FC355 == DA_FC150) { 		if (get_ival(PS5_L2)) { 			if (event_press(PS5_RIGHT)) { 				DA_FC149 += DA_FC786[DA_FC355][V_02]  				        DA_FC359 = TRUE; 							} 			if (event_press(PS5_LEFT)) { 				DA_FC149 -= DA_FC786[DA_FC355][V_02]  				        DA_FC359 = TRUE; 							} 			DA_FC149 = clamp(DA_FC149, DA_FC786[DA_FC355][V_00], DA_FC786[DA_FC355][V_01]); 		} 		if (DA_FC355 == V_08) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC386]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC386])     		} 		else if (DA_FC355 == V_09) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC387]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC387])     		} 		else if (DA_FC355 == V_10) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC388]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC388])     		} 		else if (DA_FC355 == V_11) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC389]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC389])     		} 		else if (DA_FC355 == V_12) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC390]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC390])     		} 		else if (DA_FC355 == V_13) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC391]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC391])     		} 		else if (DA_FC355 == V_14) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC392]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC392])     		} 		else if (DA_FC355 == V_15) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC393]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC393])     		} 		else if (DA_FC355 == V_16) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC394]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC394])     		} 		else if (DA_FC355 == V_17) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC275]),OLED_FONT_SMALL_WIDTH ),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC275])  		} 		else if(DA_FC355 == V_18){ 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC276]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC276])  		} 		else if(DA_FC355 == V_19){ 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC277]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC277])  		} 		else if(DA_FC355 == V_20){ 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC278]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC278])  		} 		else if(DA_FC355 == V_21){ 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC279]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC805[DA_FC279])       		} 		else if(DA_FC355 == V_22){ 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC407]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC407])     		} 		else if (DA_FC355 == V_23) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC408]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC408])     		} 		else if (DA_FC355 == V_24) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC409]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC409])     		} 		else if (DA_FC355 == V_25) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC410]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC410])     		} 		else if (DA_FC355 == V_26) { 			print(DA_FC228(DA_FC181(DA_FC770[DA_FC411]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC411])     		} 		else if (DA_FC355 == V_27) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC412]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC412])     		} 		else if (DA_FC355 == V_28) { 			print(DA_FC228(DA_FC181(DA_FC829[DA_FC413]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC413])     		} 		else if (DA_FC355 == V_29) { 			print(DA_FC228(DA_FC181(DA_FC831[DA_FC414]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC414])     		} 		else if (DA_FC355 == V_30) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC415]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC415])     		} 		else if (DA_FC355 == V_31) { 			print(DA_FC228(DA_FC181(DA_FC829[DA_FC416]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC416])     		} 		else if (DA_FC355 == V_32) { 			print(DA_FC228(DA_FC181(DA_FC831[DA_FC417]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC417])     		} 		else if (DA_FC355 == V_33) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC418]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC418])     		} 		else if (DA_FC355 == V_34) { 			print(DA_FC228(DA_FC181(DA_FC829[DA_FC419]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC419])     		} 		else if (DA_FC355 == V_35) { 			print(DA_FC228(DA_FC181(DA_FC831[DA_FC420]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC420])     		} 		else if (DA_FC355 == V_36) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC421]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC421])     		} 		else if (DA_FC355 == V_37) { 			print(DA_FC228(DA_FC181(DA_FC829[DA_FC422]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC422])     		} 		else if (DA_FC355 == V_38) { 			print(DA_FC228(DA_FC181(DA_FC831[DA_FC423]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC423])     		} 		else if (DA_FC355 == V_41) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC426]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC426])     		} 		else if (DA_FC355 == V_48) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC433]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC433])     		} 		else if (DA_FC355 == V_49) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC434]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC434])     		} 		else if (DA_FC355 == V_50) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC435]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC435])     		} 		else if (DA_FC355 == V_51) { 			print(DA_FC228(DA_FC181(DA_FC805[DA_FC436]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC436])     		} 		else if(DA_FC355 == V_00){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC439]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC439])  		} 		else if(DA_FC355 == V_01){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC440]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC440])  		} 		else if(DA_FC355 == V_02){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC441]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC441])  		} 		else if(DA_FC355 == V_03){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC442]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC442])  		} 		else if(DA_FC355 == V_04){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC443]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC443])  		} 		else if(DA_FC355 == V_05){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC444]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC444])  		} 		else if(DA_FC355 == V_06){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC445]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC445])  		} 		else if(DA_FC355 == V_07){ 			print(DA_FC228(DA_FC181(DA_FC861[DA_FC446]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC446])  		} 		else{ 			if (DA_FC149 == V_01)        print(DA_FC228(DA_FC181(DA_FC780[V_01]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[V_01])      else        print(DA_FC228(DA_FC181(DA_FC780[V_00]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[V_00])     		} 		DA_FC184(V_00); 			} 	return DA_FC149; 	} function DA_FC154(DA_FC149, DA_FC150) { 	if (DA_FC355 == DA_FC150) { 		if (get_ival(PS5_L2)) { 			if (event_press(PS5_RIGHT)) { 				DA_FC149 += DA_FC786[DA_FC355][V_02]  				        DA_FC359 = TRUE; 							} 			if (event_press(PS5_LEFT)) { 				DA_FC149 -= DA_FC786[DA_FC355][V_02]  				        DA_FC359 = TRUE; 							} 			if (event_press(PS5_UP)) { 				DA_FC149 += DA_FC786[DA_FC355][V_03]  				        DA_FC359 = TRUE; 							} 			if (event_press(PS5_DOWN)) { 				DA_FC149 -= DA_FC786[DA_FC355][V_03]  				        DA_FC359 = TRUE; 							} 			DA_FC149 = clamp(DA_FC149, DA_FC786[DA_FC355][V_00], DA_FC786[DA_FC355][V_01]); 		} 		DA_FC231(DA_FC149, DA_FC234(DA_FC149)); 	} 	return DA_FC149; 	} int DA_FC888, DA_FC889, DA_FC890; function DA_FC157(DA_FC127, DA_FC159, DA_FC160, DA_FC161, DA_FC136) { 	DA_FC889 = V_01; 	DA_FC890 = V_10000; 	if (DA_FC127 < V_00)  	  { 		putc_oled(DA_FC889, V_45); 		DA_FC889 += V_01; 		DA_FC127 = abs(DA_FC127); 			} 	for (DA_FC888 = V_05; 	DA_FC888 >= V_01; 	DA_FC888--) { 		if (DA_FC159 >= DA_FC888) { 			putc_oled(DA_FC889, DA_FC896[DA_FC127 / DA_FC890]); 			DA_FC127 = DA_FC127 % DA_FC890; 			DA_FC889 += V_01; 					} 		DA_FC890 /= V_10; 			} 	puts_oled(DA_FC160, DA_FC161, DA_FC136, DA_FC889 - V_01, OLED_WHITE); } const string DA_FC568 = " No Edit Variable"; const string DA_FC567 = " A/CROSS to Edit "; const string DA_FC563 = "MOD;"; const string DA_FC565 = "MSL;"; int DA_FC900; function DA_FC163(DA_FC146) { 	DA_FC146 = abs(DA_FC146); 	if (DA_FC146 / V_10000 > V_00) return V_05; 	if (DA_FC146 / V_1000 > V_00) return V_04; 	if (DA_FC146 / V_100 > V_00) return V_03; 	if (DA_FC146 / V_10 > V_00) return V_02; 	return V_01; 	} const int8 DA_FC896[] =     { 	0x30,    0x31,    0x32,    0x33,    0x34,    0x35,    0x36,    0x37,    0x38,    0x39   } ; int DA_FC901, DA_FC902; const image DA_FC904 = { 	0x62, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DA_FC72 { 	call(DA_FC73); 	DA_FC180(); 	vm_tctrl(V_00); 	wait( V_2400); 	cls_oled(V_00); 	image_oled(V_00, V_00, TRUE, TRUE, DA_FC904[V_00]); 	vm_tctrl(V_00); 	wait( get_rtime()); 	vm_tctrl(V_00); 	wait( V_500)cls_oled(V_01)image_oled(V_00, V_00, V_00, V_01, DA_FC904[V_00]); 	vm_tctrl(V_00); 	wait( get_rtime()); 	vm_tctrl(V_00); 	wait( V_1000)call(DA_FC74); 	vm_tctrl(V_00); 	wait( V_1000); 	DA_FC356 = TRUE; 	} combo DA_FC73 { 	cls_oled(OLED_BLACK); 	} const int8 DA_FC1309[] = { 	OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT  } const int8 DA_FC1310[] = { 	OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH  } int DA_FC906; function DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169) { 	DA_FC906--; 	switch(DA_FC166) { 		case DA_FC919 { 			DA_FC166 = OLED_WIDTH - (DA_FC906 * DA_FC1310[DA_FC168]) - V_04; 			break; 					} 		case DA_FC918 { 			DA_FC166 = (OLED_WIDTH >> V_01) - ((DA_FC906 * DA_FC1310[DA_FC168]) >> V_01); 			break; 					} 	} 	switch(DA_FC167) { 		case DA_FC918 { 			DA_FC167 = (OLED_HEIGHT >> V_01) - (DA_FC1309[DA_FC168] >> V_01); 			break; 					} 		case DA_FC921 { 			DA_FC167 = OLED_HEIGHT - DA_FC1309[DA_FC168] - V_04; 			break; 					} 	} 	puts_oled(DA_FC166, DA_FC167, DA_FC168, DA_FC906, DA_FC169); 	DA_FC906 = V_01; } enum { 	DA_FC918 = -2, DA_FC919, DA_FC920 = 0x5, DA_FC921 = -1, DA_FC922 = 0x5  } data(0x20,0x44, 0x61, 0x72 ,0x6B ,0x20, 0x41, 0x6E, 0x67, 0x65, 0x6C,0x20,0x0); combo DA_FC74 { 	vm_tctrl(V_00); 	wait(V_360); 	print(V_01,V_18,OLED_FONT_MEDIUM,OLED_WHITE,V_00) 	set_rumble(RUMBLE_A, V_50); 	vm_tctrl(V_00); 	wait( V_720); 	set_rumble(RUMBLE_A, V_50); 	set_rumble(RUMBLE_B, V_100); 	vm_tctrl(V_00); 	wait( V_720); 	reset_rumble(); 	vm_tctrl(V_00); 	wait( V_1560); 	} function DA_FC170(DA_FC166, DA_FC167, DA_FC173, DA_FC168, DA_FC169) { 	DA_FC178(DA_FC173); 	DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169); 	} function DA_FC176(DA_FC177) { 	putc_oled(DA_FC906, DA_FC177); 	DA_FC906++; 	} function DA_FC178(DA_FC179) { 	do { 		DA_FC176(dint8(DA_FC179)); 		DA_FC179++; 	} 	while(dint8(DA_FC179))  } const int16 DA_FC1311[] = { 	-0, 0x6, 0xB, 0x11, 0x15, 0x1A, 0x1E, 0x21, 0x23, 0x24, 0x25, 0x24, 0x22, 0x1F, 0x1B, 0x16, 0x10, 0x8,0x0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 0x17, 0x2F, 0x47, 0x5F, 0x77, 0x8E, 0xA4, 0xBA, 0xCE, 0xE2, 0xF3, 0x103, 0x111, 0x11D, 0x127, 0x12F, 0x135,0x138, 0x138, 0x136, 0x132, 0x12B, 0x121, 0x116, 0x107, 0xF7, 0xE5, 0xD1, 0xBB, 0xA3, 0x8A, 0x70, 0x55, 0x39, 0x1D,0x0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 0x17, 0x2D, 0x42, 0x56, 0x69, 0x7A, 0x89, 0x98, 0xA4, 0xAE, 0xB7, 0xBE, 0xC3, 0xC6, 0xC7, 0xC7, 0xC4,0xC1, 0xBB, 0xB4, 0xAC, 0xA3, 0x99, 0x8E, 0x82, 0x76, 0x69, 0x5C, 0x4F, 0x43, 0x36, 0x2A, 0x1E, 0x13, 0x9,0x0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DA_FC1312[] = { 	0x0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148,0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DA_FC1313[] = { 	0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148 } const int16 DA_FC1314[] = { 	0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0x0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148 } int DA_FC934; int DA_FC935; int DA_FC936; int DA_FC937; int DA_FC938; int DA_FC939; int DA_FC940; function DA_FC180() { 	DA_FC940 = V_03; 	DA_FC938 = DA_FC940 * DA_FC1314[DA_FC939]; 	DA_FC937 = DA_FC940 * DA_FC1311[DA_FC939]; 	DA_FC935 = ((DA_FC938 * DA_FC1313[DA_FC934]) / V_328) - ((DA_FC937 * DA_FC1312[DA_FC934]) / V_328); 	DA_FC936 = ((DA_FC938 * DA_FC1312[DA_FC934]) / V_328) + ((DA_FC937 * DA_FC1313[DA_FC934]) / V_328); 	DA_FC938 = DA_FC935; 	DA_FC937 = DA_FC936; 	DA_FC939 += V_01; 	DA_FC934 += V_45; 	if(DA_FC939 >= V_360) { 		DA_FC939 %= V_360; 			} 	if(DA_FC934 >= V_360) { 		DA_FC934 %= V_360; 			} 	pixel_oled(V_64 + (((DA_FC938 / DA_FC940) * V_30) / V_328), V_32 + (((DA_FC937 / DA_FC940) * V_30) / V_328), OLED_WHITE); 	} int DA_FC943; function DA_FC181(DA_FC140) { 	DA_FC943 = V_00; 	do { 		DA_FC140++; 		DA_FC943++; 			} 	while (duint8(DA_FC140)); 	return DA_FC943; 	} int DA_FC946; const uint8 DA_FC1315[] = { 	PS5_OPTIONS,  PS5_LEFT,  PS5_RIGHT,  PS5_UP,  PS5_DOWN,  PS5_CROSS,  PS5_CIRCLE,  PS5_SQUARE,  PS5_TRIANGLE,  PS5_R3,  PS5_L3,  PS5_RX,  PS5_RY,  PS5_PS,  PS5_TOUCH,  PS5_SHARE } ; function DA_FC183() { 	for (DA_FC946 = V_00; 	DA_FC946 < sizeof(DA_FC1315) / sizeof(DA_FC1315[V_00]); 	DA_FC946++) { 		if (get_ival(DA_FC1315[DA_FC946]) || event_press(DA_FC1315[DA_FC946])) { 			set_val(DA_FC1315[DA_FC946], V_00); 		} 			} 	} define DA_FC947 = 0x83; define DA_FC948 = 0x84; define DA_FC949 = 0x85; define DA_FC950 = 0x86; define DA_FC951 = 0x82; define DA_FC952 = 0x59; define DA_FC953 = 0x7F; define DA_FC954 = 0x41; int DA_FC955; int DA_FC956; int DA_FC957 = 0x1; define DA_FC958 = 0x24; const string DA_FC959 = "Hold LT/L2 +"; function DA_FC184(DA_FC185) { 	line_oled(V_01, V_48, V_127, V_48, V_01, V_01); 	print(V_02, V_52, OLED_FONT_SMALL, V_01, DA_FC959[V_00]); 	rect_oled(V_90, V_50, V_127, V_60, OLED_WHITE, DA_FC957); 	putc_oled(V_01, DA_FC949); 	puts_oled(V_91, V_51, OLED_FONT_SMALL, V_01, DA_FC955); 	putc_oled(V_01, DA_FC950); 	puts_oled(V_101, V_51, OLED_FONT_SMALL, V_01, DA_FC956); 	if (DA_FC185) { 		putc_oled(V_01, DA_FC947); 		puts_oled(V_111, V_51, OLED_FONT_SMALL, V_01, DA_FC955); 		putc_oled(V_01, DA_FC948); 		puts_oled(V_121, V_51, OLED_FONT_SMALL, V_01, DA_FC956); 			} 	} const uint8 DA_FC1317 [] = { 	  PS5_R1,        	  PS5_R2,        	  PS5_R3,        	  PS5_L1,        	  PS5_L2,        	  PS5_L3,        	  PS5_TRIANGLE,  	  PS5_CIRCLE,    	  PS5_CROSS,     	  PS5_SQUARE     } function DA_FC186() { 	last_var = sizeof(data) 	DA_FC503 = get_pvar(SPVAR_1,V_00,V_01,V_00); 	DA_FC509 = get_pvar(SPVAR_2,V_00,V_01,V_00); 	DA_FC502 = get_pvar(SPVAR_3,V_11111, V_99999,V_11111); 	DA_FC188(); 	if (DA_FC213(V_00, V_01, V_00)) { 		DA_FC365 = DA_FC213(  V_00, V_06, V_00); 		DA_FC362 = DA_FC213(V_00, V_03, V_00); 		DA_FC363 = DA_FC213(V_00,V_01,V_00); 		DA_FC364 = DA_FC213(V_00,V_01,V_00); 		DA_FC275 = DA_FC213(V_00, V_70, V_00); 		DA_FC276 = DA_FC213(V_00, V_70, V_00); 		DA_FC277 = DA_FC213(V_00, V_70, V_00); 		DA_FC278 = DA_FC213(V_00, V_70, V_00); 		DA_FC279 = DA_FC213(V_00, V_22, V_08); 		DA_FC366 = DA_FC213(V_00, V_70, V_00); 		DA_FC367 = DA_FC213(V_00, V_70, V_00); 		DA_FC368 = DA_FC213(V_00, V_70, V_00); 		DA_FC369 = DA_FC213(V_00, V_70, V_00); 		DA_FC370 = DA_FC213(V_00, V_01, V_00); 		DA_FC371 = DA_FC213(V_00, V_01, V_00); 		DA_FC372 = DA_FC213(V_00, V_01, V_00); 		DA_FC373 = DA_FC213(V_00, V_01, V_00); 		DA_FC381 = DA_FC213(V_00, V_01, V_00); 		DA_FC407 = DA_FC213(V_00, V_70, V_00); 		DA_FC408 = DA_FC213(V_00, V_70, V_00); 		DA_FC409 = DA_FC213(V_00, V_70, V_00); 		DA_FC410 = DA_FC213(V_00, V_70, V_00); 		DA_FC411 = DA_FC213(V_00, V_70, V_00); 		DA_FC412 = DA_FC213(V_01, V_25, V_01); 		DA_FC413 = DA_FC213(V_00, V_01, V_00); 		DA_FC414 = DA_FC213(V_00, V_01, V_00); 		DA_FC415 = DA_FC213(V_01, V_25, V_05); 		DA_FC416 = DA_FC213(V_00, V_01, V_00); 		DA_FC417 = DA_FC213(V_00, V_01, V_00); 		DA_FC418 = DA_FC213(V_00, V_25, V_02); 		DA_FC419 = DA_FC213(V_00, V_01, V_00); 		DA_FC420 = DA_FC213(V_00, V_01, V_01); 		DA_FC421 = DA_FC213(V_01, V_25, V_08); 		DA_FC422 = DA_FC213(V_00, V_01, V_00); 		DA_FC423 = DA_FC213(V_00, V_01, V_01); 		DA_FC424 = DA_FC213(V_350, V_600, V_350); 		DA_FC425 = DA_FC213(V_350, V_600, V_445); 		DA_FC426 = DA_FC213(V_00, V_22, V_00); 		DA_FC427 = DA_FC213(V_00, V_01, V_00); 		DA_FC428 = DA_FC213(-V_100, V_300, V_00); 		DA_FC374 = DA_FC213(V_00, V_01, V_00); 		DA_FC375 = DA_FC213(V_00, V_01, V_00); 		DA_FC376 = DA_FC213(V_00, V_01, V_00); 		DA_FC377 = DA_FC213(V_00, V_01, V_00); 		DA_FC378 = DA_FC213(V_00, V_01, V_00); 		DA_FC429 = DA_FC213(-V_150, V_150, V_00); 		DA_FC430 = DA_FC213(-V_150, V_150, V_00); 		DA_FC431 = DA_FC213(V_00, V_01, V_00); 		DA_FC432 = DA_FC213(-V_150, V_150, V_00); 		DA_FC433 = DA_FC213(V_00, V_22, V_00); 		DA_FC434 = DA_FC213(V_00, V_22, V_00); 		DA_FC435 = DA_FC213(V_00, V_22, V_00); 		DA_FC436 = DA_FC213(V_00, V_22, V_00); 		DA_FC602 = DA_FC213(V_60, V_400, V_235); 		DA_FC438 = DA_FC213(V_00, V_01, V_00); 		DA_FC437 = DA_FC213(V_00, V_01, V_00); 		DA_FC361 = DA_FC213(V_00, V_03, V_00); 		DA_FC386 = DA_FC213(V_00, V_70, V_00); 		DA_FC387 = DA_FC213(V_00, V_70, V_00); 		DA_FC388 = DA_FC213(V_00, V_70, V_00); 		DA_FC391 = DA_FC213(V_00, V_70, V_00); 		DA_FC392 = DA_FC213(V_00, V_70, V_00); 		DA_FC393 = DA_FC213(V_00, V_70, V_00); 		DA_FC394 = DA_FC213(V_00, V_22, V_08); 		DA_FC379 = DA_FC213(V_00, V_01, V_00); 		DA_FC389 = DA_FC213(V_00, V_70, V_00); 		DA_FC390 = DA_FC213(V_00, V_70, V_00); 		DA_FC585 = DA_FC213(V_50, V_1000, V_500); 		DA_FC1197 = DA_FC213(V_00, V_01, V_00); 		DA_FC1190 = DA_FC213(V_00, V_01, V_00); 		DA_FC110 = DA_FC213(V_00, V_06, V_00); 		DA_FC406 = DA_FC213(V_00, V_01, V_00); 		DA_FC360 = DA_FC213(V_00, V_02, V_00); 		DA_FC439 = DA_FC213(V_00, V_09, V_09); 		DA_FC440 = DA_FC213(V_00, V_09, V_08); 		DA_FC441 = DA_FC213(V_00, V_09, V_03); 		DA_FC442 = DA_FC213(V_00, V_09, V_01); 		DA_FC443 = DA_FC213(V_00, V_09, V_04); 		DA_FC444 = DA_FC213(V_00, V_09, V_00); 		DA_FC445 = DA_FC213(V_00, V_09, V_07); 		DA_FC446 = DA_FC213(V_00, V_09, V_06); 		DA_FC382    = DA_FC213(V_00, V_01, V_00); 		DA_FC383    = DA_FC213(V_00, V_01, V_00); 		DA_FC384     = DA_FC213(V_00, V_01, V_00); 		DA_FC395     = DA_FC213(V_60, V_500, V_120); 		DA_FC396     = DA_FC213(V_60, V_500, V_350); 		DA_FC397    = DA_FC213(V_00, V_01, V_00); 		DA_FC398 = DA_FC213(V_00, V_01, V_00); 		DA_FC399     = DA_FC213(V_50, V_250, V_80); 		DA_FC400     = DA_FC213(V_100, V_850, V_180); 		DA_FC401 = DA_FC213(V_00, V_01, V_00); 		DA_FC402    = DA_FC213(V_00, V_01, V_00); 		DA_FC403        = DA_FC213(V_80, V_500, V_120); 		DA_FC404        = DA_FC213(V_80, V_500, V_350); 		DA_FC405       = DA_FC213(V_00, V_01, V_00); 		DA_FC455           = DA_FC213(-V_2500, V_2500, V_1000); 		DA_FC29           = DA_FC213(V_00, V_01, V_00); 		DA_FC475         = DA_FC213(V_00, V_01, V_00); 		DA_FC473       = DA_FC213(V_00, V_01, V_00); 	} 	 	 	 	else{ 		DA_FC365 = V_00; 		DA_FC362 = V_00; 		DA_FC363 = V_00; 		DA_FC364 = V_00; 		DA_FC275 = V_00; 		DA_FC276 = V_00; 		DA_FC277 = V_00; 		DA_FC278 = V_00; 		DA_FC279 = V_08; 		DA_FC366 = V_00; 		DA_FC367 = V_00; 		DA_FC368 = V_00; 		DA_FC369 = V_00; 		DA_FC370 = V_00; 		DA_FC371 = V_00; 		DA_FC372 = V_00; 		DA_FC373 = V_00; 		DA_FC381 = V_00; 		DA_FC407 = V_00; 		DA_FC408 = V_00; 		DA_FC409 = V_00; 		DA_FC410 = V_00; 		DA_FC411 = V_00; 		DA_FC412 = V_01; 		DA_FC413 = V_00; 		DA_FC414 = V_00; 		DA_FC415 = V_05; 		DA_FC416 = V_00; 		DA_FC417 = V_00; 		DA_FC418 = V_02; 		DA_FC419 = V_00; 		DA_FC420 = V_01; 		DA_FC421 = V_08; 		DA_FC422 = V_00; 		DA_FC423 = V_01; 		DA_FC424 = V_350; 		DA_FC425 = V_445; 		DA_FC426 = V_00; 		DA_FC427 = V_00; 		DA_FC428 = V_00; 		DA_FC374 = V_00; 		DA_FC375 = V_00; 		DA_FC376 = V_00; 		DA_FC377 = V_00; 		DA_FC378 = V_00; 		DA_FC429 = V_00; 		DA_FC430 = V_00; 		DA_FC431 = V_00; 		DA_FC432 = V_00; 		DA_FC433 = V_00; 		DA_FC434 = V_00; 		DA_FC435 = V_00; 		DA_FC436 = V_00; 		DA_FC602 = V_235; 		DA_FC438 = V_00; 		DA_FC437 = V_00; 		DA_FC361 = V_00; 		DA_FC386 = V_00; 		DA_FC387 = V_00; 		DA_FC388 = V_00; 		DA_FC391 = V_00; 		DA_FC392 = V_00; 		DA_FC393 = V_00; 		DA_FC394 = V_08; 		DA_FC379 = V_00; 		DA_FC389 = V_00; 		DA_FC390 = V_00; 		DA_FC585 = V_500; 		DA_FC1197 = V_00; 		DA_FC1190 = V_00; 		DA_FC110 = V_00; 		DA_FC406 = V_00; 		DA_FC360 = V_00; 		DA_FC439 = V_09; 		DA_FC440 = V_08; 		DA_FC441 = V_03; 		DA_FC442 = V_01; 		DA_FC443 = V_04; 		DA_FC444 = V_00; 		DA_FC445 = V_07; 		DA_FC446 = V_06; 		DA_FC382 = V_00; 		DA_FC383 = V_00; 		DA_FC384 = V_00; 		DA_FC395 = V_120; 		DA_FC396 = V_350; 		DA_FC397 = V_00; 		DA_FC398 = V_00; 		DA_FC399 = V_80; 		DA_FC400 = V_180; 		DA_FC401 = V_00; 		DA_FC402 = V_00; 		DA_FC403 = V_120; 		DA_FC404 = V_360; 		DA_FC405 = V_00; 		DA_FC455     = V_725; 		DA_FC29     = V_00; 		DA_FC475     = V_00; 		DA_FC473     = V_00; 			} 	if (DA_FC360 == V_00) { 		DA_FC447 = PS5_CIRCLE; 		DA_FC448 = PS5_CROSS; 		DA_FC449 = PS5_L1; 		DA_FC450 = PS5_R1; 		DA_FC451 = PS5_L2; 		DA_FC452 = PS5_R2; 		DA_FC453 = PS5_SQUARE; 		DA_FC454 = PS5_TRIANGLE; 			} 	else if (DA_FC360 == V_01) { 		DA_FC447      = PS5_SQUARE; 		DA_FC448      = PS5_CROSS ; 		DA_FC449    = PS5_L1    ; 		DA_FC450  = PS5_R1; 		DA_FC451    = PS5_L2; 		DA_FC452    = PS5_R2; 		DA_FC453     = PS5_CIRCLE; 		DA_FC454  = PS5_TRIANGLE; 	} 	else if (DA_FC360 == V_02) { 		DA_FC447 = DA_FC1317[DA_FC439]; 		DA_FC448 = DA_FC1317[DA_FC440]; 		DA_FC449 = DA_FC1317[DA_FC441]; 		DA_FC450 = DA_FC1317[DA_FC442]; 		DA_FC451 = DA_FC1317[DA_FC443]; 		DA_FC452 = DA_FC1317[DA_FC444]; 		DA_FC453 = DA_FC1317[DA_FC445]; 		DA_FC454 = DA_FC1317[DA_FC446]; 			} 	} function DA_FC187() { 	DA_FC188(); 	DA_FC211(   V_01,V_00,     V_01); 	DA_FC211(DA_FC365, V_00, V_06); 	DA_FC211(DA_FC362, V_00, V_03); 	DA_FC211(DA_FC363, V_00 , V_01); 	DA_FC211(DA_FC364, V_00 , V_01); 	DA_FC211(DA_FC275, V_00, V_70); 	DA_FC211(DA_FC276, V_00, V_70); 	DA_FC211(DA_FC277, V_00, V_70); 	DA_FC211(DA_FC278, V_00, V_70); 	DA_FC211(DA_FC279, V_00, V_22); 	DA_FC211(DA_FC366, V_00, V_70); 	DA_FC211(DA_FC367, V_00, V_70); 	DA_FC211(DA_FC368, V_00, V_70); 	DA_FC211(DA_FC369, V_00, V_70); 	DA_FC211(DA_FC370, V_00, V_01); 	DA_FC211(DA_FC371, V_00, V_01); 	DA_FC211(DA_FC372, V_00, V_01); 	DA_FC211(DA_FC373, V_00, V_01); 	DA_FC211(DA_FC381, V_00, V_01); 	DA_FC211(DA_FC407, V_00, V_70); 	DA_FC211(DA_FC408, V_00, V_70); 	DA_FC211(DA_FC409, V_00, V_70); 	DA_FC211(DA_FC410, V_00, V_70); 	DA_FC211(DA_FC411, V_00, V_70); 	DA_FC211(DA_FC412, V_01, V_25); 	DA_FC211(DA_FC413, V_00, V_01); 	DA_FC211(DA_FC414, V_00, V_01); 	DA_FC211(DA_FC415, V_01, V_25); 	DA_FC211(DA_FC416, V_00, V_01); 	DA_FC211(DA_FC417, V_00, V_01); 	DA_FC211(DA_FC418, V_00, V_25); 	DA_FC211(DA_FC419, V_00, V_01); 	DA_FC211(DA_FC420, V_00, V_01); 	DA_FC211(DA_FC421, V_01, V_25); 	DA_FC211(DA_FC422, V_00, V_01); 	DA_FC211(DA_FC423, V_00, V_01); 	DA_FC211(DA_FC424, V_350, V_600); 	DA_FC211(DA_FC425, V_350, V_600); 	DA_FC211(DA_FC426, V_00, V_22); 	DA_FC211(DA_FC427, V_00, V_01); 	DA_FC211(DA_FC428, -V_100, V_300); 	DA_FC211(DA_FC374, V_00, V_01); 	DA_FC211(DA_FC375, V_00, V_01); 	DA_FC211(DA_FC376, V_00, V_01); 	DA_FC211(DA_FC377, V_00, V_01); 	DA_FC211(DA_FC378, V_00, V_01); 	DA_FC211(DA_FC429, -V_150, V_150); 	DA_FC211(DA_FC430, -V_150, V_150); 	DA_FC211(DA_FC431, V_00, V_01); 	DA_FC211(DA_FC432, -V_150, V_150); 	DA_FC211(DA_FC433, V_00, V_22); 	DA_FC211(DA_FC434, V_00, V_22); 	DA_FC211(DA_FC435, V_00, V_22); 	DA_FC211(DA_FC436, V_00, V_22); 	DA_FC211(DA_FC602, V_60, V_400); 	DA_FC211(DA_FC438, V_00, V_01); 	DA_FC211(DA_FC437, V_00, V_01); 	DA_FC211(DA_FC361, V_00, V_03); 	DA_FC211(DA_FC386, V_00, V_70); 	DA_FC211(DA_FC387, V_00, V_70); 	DA_FC211(DA_FC388, V_00, V_70); 	DA_FC211(DA_FC391, V_00, V_70); 	DA_FC211(DA_FC392, V_00, V_70); 	DA_FC211(DA_FC393, V_00, V_70); 	DA_FC211(DA_FC394, V_00, V_22); 	DA_FC211(DA_FC379, V_00, V_01); 	DA_FC211(DA_FC389, V_00, V_70); 	DA_FC211(DA_FC390, V_00, V_70); 	DA_FC211(DA_FC585, V_50, V_1000); 	DA_FC211(DA_FC1197, V_00, V_01); 	DA_FC211(DA_FC1190, V_00, V_01); 	DA_FC211(DA_FC110, V_00, V_06); 	DA_FC211(DA_FC406, V_00, V_01); 	DA_FC211(DA_FC360, V_00, V_02); 	DA_FC211(DA_FC439, V_00, V_09); 	DA_FC211(DA_FC440, V_00, V_09); 	DA_FC211(DA_FC441, V_00, V_09); 	DA_FC211(DA_FC442, V_00, V_09); 	DA_FC211(DA_FC443, V_00, V_09); 	DA_FC211(DA_FC444, V_00, V_09); 	DA_FC211(DA_FC445, V_00, V_09); 	DA_FC211(DA_FC446, V_00, V_09); 	DA_FC211(DA_FC382,    V_00, V_01); 	DA_FC211(DA_FC383,    V_00, V_01); 	DA_FC211(DA_FC384,     V_00, V_01); 	DA_FC211(DA_FC395,     V_60, V_500); 	DA_FC211(DA_FC396,     V_60, V_500); 	DA_FC211(DA_FC397,    V_00, V_01); 	DA_FC211(DA_FC398, V_00, V_01); 	DA_FC211(DA_FC399,     V_50, V_250); 	DA_FC211(DA_FC400,     V_100, V_850); 	DA_FC211(DA_FC401, V_00, V_01); 	DA_FC211(DA_FC402,    V_00, V_01); 	DA_FC211(DA_FC403,        V_80, V_500); 	DA_FC211(DA_FC404,        V_80, V_500); 	DA_FC211(DA_FC405,       V_00, V_01); 	DA_FC211(DA_FC455 ,          -V_2500,V_2500); 	DA_FC211(DA_FC29,           V_00,V_01); 	DA_FC211(DA_FC475,           V_00,V_01); 	DA_FC211(DA_FC473,           V_00,V_01); 	} function DA_FC188() { 	DA_FC976 = SPVAR_4; 	DA_FC977 = V_00; 	DA_FC988 = V_00; 	} int DA_FC977,  DA_FC976, DA_FC988, DA_FC978, DA_FC986; function DA_FC189(DA_FC190) { 	DA_FC978 = V_00; 	while (DA_FC190) { 		DA_FC978++; 		DA_FC190 = abs(DA_FC190 >> V_01); 	} 	return DA_FC978; 	} function DA_FC191(DA_FC192, DA_FC193) { 	DA_FC978 = max(DA_FC189(DA_FC192), DA_FC189(DA_FC193)); 	if (DA_FC194(DA_FC192, DA_FC193)) { 		DA_FC978++; 	} 	return DA_FC978; 	} function DA_FC194(DA_FC192, DA_FC193) { 	return DA_FC192 < V_00 || DA_FC193 < V_00; 	} function DA_FC197(DA_FC198) { 	return V_01 << clamp(DA_FC198 - V_01, V_00, V_31); 	} function DA_FC199(DA_FC198) { 	if (DA_FC198 == V_32) { 		return -V_01; 			} 	return 0x7FFFFFFF >> (V_31 - DA_FC198); } function DA_FC201(DA_FC198) { 	return DA_FC199(DA_FC198 - V_01); 	} function DA_FC203(DA_FC190, DA_FC198) { 	if (DA_FC190 < V_00) { 		return (abs(DA_FC190) & DA_FC201(DA_FC198)) | DA_FC197(DA_FC198); 	} 	return DA_FC190 & DA_FC201(DA_FC198); } function DA_FC206(DA_FC190, DA_FC198) { 	if (DA_FC190 & DA_FC197(DA_FC198)) { 		return V_00 - (DA_FC190 & DA_FC201(DA_FC198)); 	} 	return DA_FC190 & DA_FC201(DA_FC198); } function DA_FC209(DA_FC210) { 	return get_pvar(DA_FC210, 0x80000000, 0x7FFFFFFF, V_00); 	} function DA_FC211(DA_FC190, min, max) { 	DA_FC986 = DA_FC191(min, max); 	DA_FC190 = clamp(DA_FC190, min, max); 	if (DA_FC194(min, max)) { 		DA_FC190 = DA_FC203(DA_FC190, DA_FC986); 	} 	DA_FC190 = DA_FC190 & DA_FC199(DA_FC986); 	if (DA_FC986 >= V_32 - DA_FC977) { 		DA_FC988 = DA_FC988 | (DA_FC190 << DA_FC977); 		set_pvar(DA_FC976, DA_FC988); 		DA_FC976++; 		DA_FC986 -= (V_32 - DA_FC977); 		DA_FC190 = DA_FC190 >> (V_32 - DA_FC977); 		DA_FC977 = V_00; 		DA_FC988 = V_00; 	} 	DA_FC988 = DA_FC988 | (DA_FC190 << DA_FC977); 	DA_FC977 += DA_FC986; 	if (!DA_FC977) { 		DA_FC988 = V_00; 	} 	set_pvar(DA_FC976, DA_FC988); } function DA_FC213(min, max, DA_FC214) { 	DA_FC986 = DA_FC191(min, max); 	DA_FC988 = (DA_FC209(DA_FC976) >> DA_FC977) & DA_FC199(DA_FC986); 	if (DA_FC986 >= V_32 - DA_FC977) { 		DA_FC988 = (DA_FC988 & DA_FC199(V_32 - DA_FC977)) | ((DA_FC209(DA_FC976 + V_01) & DA_FC199(DA_FC986 - (V_32 - DA_FC977))) << (V_32 - DA_FC977)); 	} 	DA_FC977 += DA_FC986; 	DA_FC988 = DA_FC988 & DA_FC199(DA_FC986); 	if (DA_FC977 >= V_32) { 		DA_FC976++; 		DA_FC977 -= V_32; 	} 	if (DA_FC194(min, max)) { 		DA_FC988 = DA_FC206(DA_FC988, DA_FC986); 	} 	if (DA_FC988 < min || DA_FC988 > max) { 		return DA_FC214; 	} 		 	if((DA_FC216[V_283] != V_7590)){ 		DA_FC213(min, max, DA_FC214) 	} 	 	return DA_FC988; 	} const string DA_FC995 = "SETTINGS"; const string DA_FC996 = "WAS SAVED"; combo DA_FC75 { 	vm_tctrl(V_00); 	wait( V_20); 	cls_oled(V_00); 	DA_FC187(); 	print(V_15, V_02, OLED_FONT_MEDIUM, V_01, DA_FC995[V_00]); 	print(V_10, V_23, OLED_FONT_MEDIUM, V_01, DA_FC996[V_00]); 	DA_FC997 = V_1500; 	combo_run(DA_FC76); 	} int DA_FC997 = 0x5DC; combo DA_FC76 { 	vm_tctrl(V_00); 	wait( DA_FC997); 	cls_oled(V_00); 	DA_FC358 = FALSE; 	} define DA_FC998 = 0x0; define DA_FC999 = 0x1; define DA_FC1000 = 0x2; define DA_FC1001 = 0x3; define DA_FC1002 = 0x4; define DA_FC1003 = 0x5; define DA_FC1004 = 0x6; define DA_FC1005 = 0x7; int DA_FC667; int DA_FC1007; int DA_FC1008, DA_FC664; int DA_FC478; int DA_FC1011 = 0x32; int DA_FC1012 = 0xC8; int DA_FC1013 = TRUE; combo DA_FC77 { 	set_val(DA_FC448, V_00); 	set_val(PS5_L3, V_100); 	set_val(PS5_R3, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC448, V_00); 	vm_tctrl(V_00); 	wait( V_120); 	if (DA_FC427) DA_FC259(); 	vm_tctrl(V_00); 	wait( V_50); 	vm_tctrl(V_00); 	wait( V_50); 	} int DA_FC609; int DA_FC616; combo DA_FC78 { 	if (DA_FC616) set_val(XB1_LX, V_100); 	else set_val(XB1_LX, -V_100); 	vm_tctrl(V_00); 	wait( V_70); 	if (DA_FC616) set_val(XB1_RX, V_100); 	else set_val(XB1_RX, -V_100); 	set_val(XB1_RY, V_100); 	vm_tctrl(V_00); 	wait( V_2000); 	if (DA_FC616) set_val(XB1_RX, -V_100); 	else set_val(XB1_RX, V_100); 	vm_tctrl(V_00); 	wait( V_50); 	vm_tctrl(V_00); 	wait( V_200); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC424); 	if (DA_FC616) set_val(XB1_LX, V_100); 	else set_val(XB1_LX, V_100); 	set_val(XB1_LY,V_100); 	vm_tctrl(V_00); 	wait( V_50); 	vm_tctrl(V_00); 	wait( V_1200); 	DA_FC609 = FALSE; 	DA_FC243(DA_FC609); 	} int DA_FC618; int DA_FC619; combo DA_FC79 { 	if (DA_FC619) set_val(XB1_RX, -V_100); 	else set_val(XB1_RX, V_100); 	set_val(XB1_RY, V_100); 	vm_tctrl(V_00); 	wait( V_320); 	vm_tctrl(V_00); 	wait( V_50); 	set_val(XB1_RY, -V_60); 	vm_tctrl(V_00); 	wait( V_1100); 	vm_tctrl(V_00); 	wait( V_50); 	if (DA_FC619) set_val(XB1_LX, V_60); 	else set_val(XB1_LX, -V_60); 	vm_tctrl(V_00); 	wait( V_120); 	vm_tctrl(V_00); 	wait( V_50); 	set_val(XB1_LY, -V_100); 	set_val(DA_FC453, V_100); 	set_val(DA_FC450, V_100); 	set_val(DA_FC451, V_100); 	DA_FC1146 = V_4000; 	vm_tctrl(V_00); 	wait( DA_FC425); 	vm_tctrl(V_00); 	wait( V_50); 	set_val(DA_FC453, V_100); 	vm_tctrl(V_00); 	wait( V_50); 	DA_FC618 = FALSE; 	DA_FC243(DA_FC618); 	} int DA_FC1018 = TRUE; function DA_FC215(DA_FC216) { 	if (DA_FC216) { 		DA_FC1019 = DA_FC1051; 			} 	else { 		DA_FC1019 = DA_FC1050; 			} 	combo_run(DA_FC80); 	} int DA_FC1019; combo DA_FC80 { 	DA_FC236(DA_FC1019); 	vm_tctrl(V_00); 	wait( V_300); 	DA_FC236(DA_FC1048); 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC236(DA_FC1019); 	vm_tctrl(V_00); 	wait( V_300); 	DA_FC236(DA_FC1048); 	} define DA_FC1023 = 0x64; define DA_FC1024 = 0x82; const string DA_FC547 = "SCRIPT WAS"; function DA_FC217(DA_FC127, DA_FC219, DA_FC220) { 	if (!DA_FC352 && !DA_FC353) { 		cls_oled(V_00); 		print(DA_FC219, V_03, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC220); 		if (DA_FC127) { 			print(DA_FC221(sizeof(DA_FC1028) - V_01, OLED_FONT_LARGE_WIDTH), V_37, OLED_FONT_LARGE, OLED_WHITE, DA_FC1028[V_00]); 		} 		else { 			print(DA_FC221(sizeof(DA_FC1029) - V_01, OLED_FONT_LARGE_WIDTH), V_37, OLED_FONT_LARGE, OLED_WHITE, DA_FC1029[V_00]); 		} 		DA_FC215(DA_FC127); 			} 	} function DA_FC221(DA_FC142, DA_FC136) { 	return (OLED_WIDTH / V_02) - ((DA_FC142 * DA_FC136) / V_02); 	} const string DA_FC1029 = "OFF"; const string DA_FC1028 = "ON"; function DA_FC224(DA_FC133, DA_FC226, DA_FC127) { 	cls_oled(V_00); 	line_oled(V_01, V_18, V_127, V_18, V_01, V_01); 	print(DA_FC133, V_00, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC226); 	DA_FC231(DA_FC127, DA_FC234(DA_FC127)); 	DA_FC356 = TRUE; 	} const string DA_FC589 = "EA PING"; const string DA_FC611 = "FK_POWER"; const string DA_FC603 = "MaxFnshPwr"const string DA_FC595 = "JK_Agg"; int DA_FC585; int DA_FC602; function DA_FC228(DA_FC142, DA_FC136) { 	return (OLED_WIDTH / V_02) - ((DA_FC142 * DA_FC136) / V_02); 	} int DA_FC1038; int DA_FC1039, DA_FC1040; function DA_FC231(DA_FC127, DA_FC159) { 	DA_FC1038 = V_01; 	DA_FC1040 = V_10000; 	if (DA_FC127 < V_00) { 		putc_oled(DA_FC1038, V_45); 		DA_FC1038 += V_01; 		DA_FC127 = abs(DA_FC127); 			} 	for (DA_FC1039 = V_05; 	DA_FC1039 >= V_01; 	DA_FC1039--) { 		if (DA_FC159 >= DA_FC1039) { 			putc_oled(DA_FC1038, (DA_FC127 / DA_FC1040) + V_48); 			DA_FC127 %= DA_FC1040; 			DA_FC1038++; 			if (DA_FC1039 == V_04) { 				putc_oled(DA_FC1038, V_44); 				DA_FC1038++; 							} 					} 		DA_FC1040 /= V_10; 			} 	puts_oled(DA_FC228(DA_FC1038 - V_01, OLED_FONT_MEDIUM_WIDTH), V_38, OLED_FONT_MEDIUM, DA_FC1038 - V_01, OLED_WHITE); 	} int DA_FC1044; function DA_FC234(DA_FC235) { 	DA_FC1044 = V_00; 	do { 		DA_FC235 /= V_10; 		DA_FC1044++; 			} 	while (DA_FC235); 	return DA_FC1044; 	} int DA_FC625; define DA_FC1048 = 0x0; define DA_FC1049 = 0x1; define DA_FC1050 = 0x2; define DA_FC1051 = 0x3; define DA_FC1052 = 0x4; define DA_FC1053 = 0x5; define DA_FC1054 = 0x6; define DA_FC1055 = 0x7; const int16 data[][] = { 	{ 		0x0,    0x0,    0x0   	} 	,  	  { 		0x0,    0x0,    0xFF   	} 	,  	  { 		0xFF,    0x0,    0x0   	} 	,  	  { 		0x0,    0xFF,    0x0   	} 	,  	  { 		0xFF,    0x0,    0xFF   	} 	,  	  { 		0x0,    0xFF,    0xFF   	} 	,  	  { 		0xFF,    0xFF,    0x0   	} 	,  	  { 		0xFF,    0xFF,    0xFF   	} } ; int DA_FC1056; function DA_FC236(DA_FC169) { 	for (DA_FC1056 = V_00; 	DA_FC1056 < V_03; 	DA_FC1056++) { 		set_rgb(data[DA_FC169][V_00], data[DA_FC169][V_01], data[DA_FC169][V_02]); 			} 	} const int8 DA_FC1327[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS5_R1,  	  PS5_R2,  	  XB1_RS,  	  PS5_L1,  	  PS5_L2,  	  XB1_LS,  	  PS5_UP,  	  PS5_DOWN,  	  PS5_LEFT,  	  PS5_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS5_TOUCH  } int DA_FC628 = PS5_L3; define DA_FC1058 = 0x1; define DA_FC1059 = 0x2; define DA_FC1060 = 0x3; define DA_FC1061 = 0x2; define DA_FC1062 = 0x3; define DA_FC1063 = 0x4; define DA_FC1064 = 0x5; define DA_FC1065 = 0x6; define DA_FC1066 = 0x7; define DA_FC1067 = 0x8; define DA_FC1068 = 0x9; int DA_FC622 = FALSE; int DA_FC1070; int DA_FC1071; int DA_FC1072; int DA_FC1073; define DA_FC1074 = PS5_LX; define DA_FC1075 = PS5_LY; define DA_FC1076 = PS5_RX; define DA_FC1077 = PS5_RY; function DA_FC238 () { 	if((!get_ival(XB1_RS) &&  !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450)) || ((DA_FC361 == V_03) || (DA_FC361 == V_02) || (DA_FC362 == V_03) || (DA_FC362 == V_02))) { 		if( get_ival(PS5_RY) < -V_70  && !DA_FC1070 && !combo_running(DA_FC0) ) { 			DA_FC1070 = TRUE; 			DA_FC478 = FALSE; 			DA_FC1007 = DA_FC275; 			            DA_FC241(DA_FC275); 		} 		if( get_ival(PS5_RY) >  V_70  && !DA_FC1071 && !combo_running(DA_FC0)) { 			DA_FC1071 = TRUE; 			DA_FC478 = TRUE; 			DA_FC1007 = DA_FC277; 			           DA_FC241(DA_FC277); 		} 		if( get_ival(PS5_RX) < -V_70  && !DA_FC1072 && !combo_running(DA_FC0) ) { 			DA_FC1072 = TRUE; 			DA_FC478 = FALSE; 			DA_FC1007 = DA_FC278; 			              DA_FC241(DA_FC278); 		} 		if( get_ival(PS5_RX) >  V_70  && !DA_FC1073 && !combo_running(DA_FC0) ) { 			DA_FC1073 = TRUE; 			DA_FC478 = TRUE; 			DA_FC1007 = DA_FC276; 			            DA_FC241(DA_FC276); 		} 			} 	if(abs(get_ival(PS5_RY))<V_20  && abs(get_ival(PS5_RX))<V_20){ 		DA_FC1070 = V_00; 		DA_FC1071  = V_00; 		DA_FC1072  = V_00; 		DA_FC1073  = V_00; 			} 	} function DA_FC239() { 	if (DA_FC1105 == DA_FC587) { 		DA_FC478 = FALSE; 		if (DA_FC386) DA_FC241(DA_FC386); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 + V_04)) { 		DA_FC478 = FALSE; 		if (DA_FC393) DA_FC241(DA_FC393); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 + V_01)) { 		DA_FC478 = TRUE; 		if (DA_FC388) DA_FC241(DA_FC388); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 - V_01)) { 		DA_FC478 = FALSE; 		if (DA_FC387) DA_FC241(DA_FC387); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 + V_02)) { 		DA_FC478 = TRUE; 		if (DA_FC390) DA_FC241(DA_FC390); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 - V_02)) { 		DA_FC478 = FALSE; 		if (DA_FC389) DA_FC241(DA_FC389); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 + V_03)) { 		DA_FC478 = TRUE; 		if (DA_FC392) DA_FC241(DA_FC392); 			} 	if (DA_FC1105 == DA_FC246(DA_FC587 - V_03)) { 		DA_FC478 = FALSE; 		if (DA_FC391) DA_FC241(DA_FC391); 			} 	} int DA_FC1090; int DA_FC497 = 0x0; function DA_FC240() { 	if(DA_FC1090){ 		DA_FC497 += get_rtime(); 			} 	if(DA_FC497 >= V_3000){ 		DA_FC497 = V_00; 		DA_FC1090 = FALSE; 			} 			 		 	if ((!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449) ) || ((DA_FC361 == V_03) || (DA_FC361 == V_02) || (DA_FC362 == V_03) || (DA_FC362 == V_02))) { 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > V_2000) && !DA_FC499 && !combo_running(DA_FC0)) { 			DA_FC499 = TRUE; 			DA_FC1090 = TRUE; 			DA_FC497 = V_00; 			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + V_23) % V_360) / -V_45) + V_10) % V_08; 			DA_FC239(); 					} 		set_val(DA_FC1076, V_00); 		set_val(DA_FC1077, V_00); 			} 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < V_2000) { 		DA_FC499 = FALSE; 			} 	} function DA_FC241(DA_FC242) { 	DA_FC1007 = DA_FC242; 	DA_FC216[-V_330 + (DA_FC242 * V_03)] = TRUE; 	DA_FC1013 = FALSE; 	block = TRUE; 	} int DA_FC1096; combo DA_FC81 { 	set_rumble(DA_FC1096, V_100); 	vm_tctrl(V_00); 	wait( V_300); 	reset_rumble(); 	vm_tctrl(V_00); 	wait( V_20); 	} function DA_FC243(DA_FC127) { 	if (DA_FC127) DA_FC1096 = RUMBLE_A; 	else DA_FC1096 = RUMBLE_B; 	combo_run(DA_FC81); 	} int DA_FC1097 = 0x12C; int DA_FC1098 ; combo DA_FC82 { 	DA_FC1098 = TRUE; 	vm_tctrl(V_00); 	wait( DA_FC1097); 	DA_FC1098 = FALSE; 	} combo DA_FC83 { 	vm_tctrl(V_00); 	wait( V_45); 	set_val(PS5_RX, V_00); 	set_val(PS5_RY, V_00); 	vm_tctrl(V_00); 	wait( V_160); 	} combo DA_FC84 { 	DA_FC245(); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_20); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC253(V_00, V_00); 	set_val(DA_FC452, V_100); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_60); 	DA_FC253(V_00, V_00); 	vm_tctrl(V_00); 	wait( V_150); 	DA_FC1013 = TRUE; 	vm_tctrl(V_00); 	wait( V_350); 	} function DA_FC245() { 	DA_FC667 = DA_FC587  DA_FC248(DA_FC667); 	DA_FC1008 = DA_FC1102; 	DA_FC664 = DA_FC665; 	} combo DA_FC85 { 	set_val(DA_FC451, V_100); 	set_val(DA_FC450, V_100); 	vm_tctrl(V_00); 	wait( V_100); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	wait( V_100); 	DA_FC1013 = TRUE; 	vm_tctrl(V_00); 	wait( V_350); 	} const int8 DA_FC1328[][] = { { 		0x0,    -99   	} 	,  	  { 		0x62,    -100   	} 	,  	  { 		0x61,    0x0   	} 	,  	  { 		0x60,    0x63   	} 	,  	  { 		0x0,    0x63   	} 	,  	  { 		-96,    0x62   	} 	,  	  { 		-95,    0x0   	} 	,  	  { 		-94,    -96   	} } ; int DA_FC1102, DA_FC665, DA_FC587; int DA_FC1105; int DA_FC499; int DA_FC1107; function DA_FC246(DA_FC247) { 	DA_FC1107 = DA_FC247; 	if (DA_FC1107 < V_00) DA_FC1107 = V_08 - abs(DA_FC247); 	else if (DA_FC1107 >= V_08) DA_FC1107 = DA_FC247 - V_08  return DA_FC1107; 	} function DA_FC248(DA_FC249) { 	if (DA_FC249 < V_00) DA_FC249 = V_08 - abs(DA_FC249); 	else if (DA_FC249 >= V_08) DA_FC249 = DA_FC249 - V_08; 	DA_FC1102 = DA_FC1328[DA_FC249][V_00]; 	DA_FC665 = DA_FC1328[DA_FC249][V_01]; } function DA_FC250(DA_FC251, DA_FC252) { 	set_val(DA_FC1076, DA_FC251); 	set_val(DA_FC1077, DA_FC252); 	} function DA_FC253(DA_FC166, DA_FC167) { 	set_val(DA_FC1074, DA_FC166); 	set_val(DA_FC1075, DA_FC167); 	} function DA_FC256() { 	if (DA_FC478) { 		set_val(DA_FC1074, inv(DA_FC664)); 		set_val(DA_FC1075, DA_FC1008); 			} 	else { 		set_val(DA_FC1074, DA_FC664); 		set_val(DA_FC1075, inv(DA_FC1008)); 			} 	} function DA_FC257() { 	if (DA_FC478) { 		set_val(DA_FC1076, inv(DA_FC664)); 		set_val(DA_FC1077, DA_FC1008); 			} 	else { 		set_val(DA_FC1076, DA_FC664); 		set_val(DA_FC1077, inv(DA_FC1008)); 			} 	} function DA_FC258() { 	if (!DA_FC478) { 		set_val(DA_FC1076, inv(DA_FC664)); 		set_val(DA_FC1077, DA_FC1008); 			} 	else { 		set_val(DA_FC1076, DA_FC664); 		set_val(DA_FC1077, inv(DA_FC1008)); 			} 	} function DA_FC259() { 	set_val(DA_FC1076, DA_FC1008); 	set_val(DA_FC1077, DA_FC664); 	} function DA_FC260() { 	set_val(DA_FC1076, inv(DA_FC1008)); 	set_val(DA_FC1077, inv(DA_FC664)); 	} function DA_FC261() { 	set_val(DA_FC1076, V_00); 	set_val(DA_FC1077, V_00); 	} int DA_FC1123; function DA_FC262() { 	if ((event_press(DA_FC448)  ) && !combo_running(DA_FC86) && (DA_FC1146  <= V_00 || (DA_FC1146 < V_3000 && DA_FC1146 > V_01  )) && !get_ival(DA_FC452) && DA_FC550 > V_500 &&!get_ival(DA_FC451) &&!get_ival(DA_FC447) &&!get_ival(DA_FC450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_ipolar(POLAR_RS, POLAR_RADIUS) >= V_1500)&& !combo_running(DA_FC86) ) { 		combo_run(DA_FC86); 			} 	if (combo_running(DA_FC86) && (        get_ival(DA_FC452) ||        get_ival(DA_FC451) ||        get_ival(DA_FC447) ||        get_ival(DA_FC450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_ipolar(POLAR_RS,POLAR_RADIUS) >= V_1500      )) { 		combo_stop(DA_FC86); 		DA_FC1261 = TRUE; 			} 	} combo DA_FC86 { vm_tctrl(V_00); wait(V_750); set_val(DA_FC449,V_100); vm_tctrl(V_00); wait(V_60); vm_tctrl(V_00); wait(V_60); if(DA_FC1123 == V_01 ){ set_val(XB1_RX,V_100)}else{set_val(XB1_RX,-V_100)} vm_tctrl(V_00); wait(V_60); vm_tctrl(V_00); wait(V_60); 	} combo DA_FC87 { 	vm_tctrl(V_00); 	wait( V_800); 	DA_FC1147 = V_00; 	} int DA_FC1124 = 0x640; int DA_FC1125 = 0x640; int DA_FC1126 = 0x640; int DA_FC1127 = TRUE; int DA_FC1128 = TRUE; int DA_FC659 = FALSE; int DA_FC1130 = TRUE; int DA_FC653 = FALSE; int DA_FC1132 = TRUE; int DA_FC655 = FALSE; int DA_FC1134 = TRUE; int DA_FC657 = FALSE; function DA_FC263(){ 	if (get_ival(DA_FC449)) { 		DA_FC1136 = V_1000; 		DA_FC1155 = V_00; 		DA_FC550 = V_01; 		combo_stop(DA_FC96); 			} 	if (event_press(DA_FC451) || event_press(DA_FC434)) { 		DA_FC1136 = V_4000; 		DA_FC1155 = V_00; 		DA_FC1124 = V_1600; 			} 	if (get_ival(DA_FC450) && !get_ival(DA_FC449) ) { 		DA_FC1136 = V_00; 		DA_FC1155 = V_00; 		DA_FC1124 = V_1600; 			} 	else if (get_ival(DA_FC449)){ 		DA_FC1136 = V_1000; 			} 	if (DA_FC1136 > V_00) { 		DA_FC1136 -= get_rtime(); 			} 	if (DA_FC1136 < V_00) { 		DA_FC1136 = V_00; 			} 	DA_FC1214 = DA_FC428; 	if (event_release(DA_FC448)) { 		DA_FC1142 = V_01; 		DA_FC1155 = V_00; 		DA_FC550 = V_01; 	} 	if (event_release(DA_FC454)) { 		DA_FC1143 = V_01; 		DA_FC1155 = V_00; 		DA_FC550 = V_01; 	} 	if (event_release(DA_FC449)) { 		DA_FC1125 = V_01; 		DA_FC1155 = V_00; 		DA_FC1124 = V_1600; 			} 	if (event_release(DA_FC450)) { 		DA_FC1126 = V_01; 		DA_FC1155 = V_00; 		DA_FC1124 = V_1600; 			} 	if (event_release(DA_FC453) || (get_ival(DA_FC454) && get_ival(DA_FC449))) { 		DA_FC1146 = V_4000; 		DA_FC1155 = V_00; 	} 	if (get_ival(DA_FC448) && DA_FC1146 < V_4000 && DA_FC1146 > V_3500) { 		DA_FC1147 = DA_FC1146; 		DA_FC1146 = V_00; 			} 	if (DA_FC1124 < V_1510) { 		DA_FC1124 += get_rtime(); 			} 	if (DA_FC1125 < V_1600) { 		DA_FC1125 += get_rtime(); 			} 	if (DA_FC1126 < V_1600) { 		DA_FC1126 += get_rtime(); 			} 	if (DA_FC1146 > V_00) { 		DA_FC1146 -= get_rtime(); 			} 	if (DA_FC1146 < V_00) { 		DA_FC1146 = V_00; 			} 	if (DA_FC1142 < V_5100) { 		DA_FC1142 += get_rtime(); 			} 	if (DA_FC1143 < V_4100) { 		DA_FC1143 += get_rtime(); 			} 	if (DA_FC1155 > V_00) { 		DA_FC1155 -= get_rtime(); 			} 	if (DA_FC1155 < V_00) { 		DA_FC1155 = V_00; 			} 	if (abs(get_ival(PS5_RX)) > V_30 || abs(get_ival(PS5_RY)) > V_30) { 		DA_FC1124 = V_01; 		DA_FC1155 = V_00; 			} 	if (combo_running(DA_FC93)) { 		set_val(DA_FC448, V_00); 		if(get_ival(DA_FC448)){ 			DA_FC663 = V_00; 			combo_stop(DA_FC88); 			set_val(DA_FC448, V_00); 			combo_stop(DA_FC93); 			combo_run(DA_FC49); 					} 			} 	if ((combo_running(DA_FC98) || combo_running(DA_FC89))) { 		set_val(DA_FC448, V_00); 		if(get_ival(DA_FC448)){ 			DA_FC550 = V_01; 			DA_FC663 = V_00; 			combo_stop(DA_FC88); 			set_val(DA_FC448, V_00); 			combo_stop(DA_FC98); 			combo_stop(DA_FC89); 			combo_run(DA_FC49); 					} 			} 	if (event_press(DA_FC447)) { 		combo_run(DA_FC87); 			} 	if (DA_FC550 > V_1500) { 		if (DA_FC1125 < V_1500) { 			DA_FC1160 = V_120; 					} 		if (DA_FC1126 < V_1500) { 			DA_FC1160 = V_228; 					} 		else { 			DA_FC1160 = V_200; 					} 			} 	if (DA_FC550 < V_1500) { 		DA_FC1160 = V_450; 			} 	if (DA_FC550 > V_2700) { 		DA_FC1164 = V_920; 			} 	else if (DA_FC550 >= V_00 && DA_FC550 < V_2700) { 		DA_FC1164 = V_725; 			} 	} function DA_FC264() { 	if (DA_FC1127) { 		if ((DA_FC550 <= V_600 || (DA_FC1124 <= V_1500 && DA_FC1124 > V_01) || ( DA_FC1125 <= V_150 || DA_FC1126 <= V_150)) && event_press(DA_FC447) ) { 			if (!get_ival(DA_FC450) && !get_ival(DA_FC449) && !get_ival(DA_FC451) && !get_ival(DA_FC452)) { 				set_val(DA_FC447, V_00); 				if (DA_FC1146 < V_4000 && DA_FC1146 > V_01) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC91); 									} 				else { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC89); 					DA_FC1155 = V_9000; 				} 							} 					} 			} 	if (DA_FC1134) { 		if (DA_FC550 > V_1000 && !DA_FC1155 && (!get_ival(DA_FC450) && !get_ival(PS5_L3) && event_press(DA_FC447)) &&  DA_FC1125 > V_150 &&  DA_FC1126 > V_150) { 			if (!get_ival(DA_FC449) && !get_ival(DA_FC451)) { 				set_val(DA_FC447, V_00); 				if (((DA_FC1143 > V_01 && DA_FC1143 <= V_2500) || (DA_FC1142 > V_01 && DA_FC1142 <= V_3000)) &&  DA_FC1124 != V_1600) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC90); 					DA_FC1155 = V_9000; 									} 				else if (((DA_FC1143 > V_2500 && DA_FC1143 <= V_4000) || (DA_FC1142 > V_3000 && DA_FC1142 <= V_3500))  &&  DA_FC1124 != V_1600) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC89); 					DA_FC1155 = V_9000; 									} 				else if ((DA_FC1146 < V_4000 && DA_FC1146 > V_01)) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC91); 					DA_FC1155 = V_9000; 									} 				else { 					set_val(DA_FC447, V_00); 					DA_FC268(); 					DA_FC1155 = V_9000; 									} 				DA_FC1155 = V_9000; 							} 					} 			} 	if (DA_FC1128) { 		if (get_ival(DA_FC449) && get_ival(DA_FC450)) { 			if (!get_ival(DA_FC451) && !get_ival(DA_FC452) && (DA_FC1146 && DA_FC1142 > V_01 && DA_FC1142 <= V_1500) || (!DA_FC1146 && DA_FC1142 > V_01 && DA_FC1142 <= V_1500) || (DA_FC1142 > V_1500 && !DA_FC1146) && !DA_FC1155) { 				if (event_press(DA_FC447)) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC99); 					DA_FC1155 = V_9000; 									} 							} 					} 			} 	if (DA_FC1132) { 		if (!get_ival(DA_FC452) && !get_ival(DA_FC449) && !get_ival(DA_FC450)) { 			if (get_ival(DA_FC451) && get_ival(DA_FC447)) { 				DA_FC270(); 				set_val(DA_FC447, V_00); 				DA_FC1155 = V_9000; 							} 					} 			} 	if (DA_FC1130) { 		if (get_ival(DA_FC450) && !get_ival(DA_FC449) && !DA_FC1136) { 			if (!get_ival(DA_FC451) && !get_ival(DA_FC452) && !DA_FC1155) { 				if (get_ival(DA_FC447) && DA_FC550 >= V_1000) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC96); 					DA_FC1155 = V_9000; 									} 				if (get_ival(DA_FC447) && DA_FC550 < V_1000 && !DA_FC1136) { 					set_val(DA_FC447, V_00); 					combo_run(DA_FC97); 									} 							} 					} 			} 	if(combo_running(DA_FC91)){ 		DA_FC663 = V_00; 		combo_stop(DA_FC88)   	} 	if (get_ival(DA_FC449) || DA_FC1136 > V_00) { 		combo_stop(DA_FC96); 		combo_stop(DA_FC98); 		combo_stop(DA_FC97); 			} 	if (combo_running(DA_FC89) || combo_running(DA_FC93) || combo_running(DA_FC98) || combo_running(DA_FC99) || combo_running(DA_FC96)) { 		if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452) > V_30) { 			combo_stop(DA_FC93); 			combo_stop(DA_FC98); 			combo_stop(DA_FC99); 			combo_stop(DA_FC96); 			combo_stop(DA_FC89); 			DA_FC663 = V_00; 			combo_stop(DA_FC88)     		} 			} 	if (combo_running(DA_FC89) || combo_running(DA_FC90)) { 		if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452)) { 			combo_stop(DA_FC91); 			combo_stop(DA_FC90); 			combo_stop(DA_FC89); 			DA_FC663 = V_00; 			combo_stop(DA_FC88)     		} 			} 	if (event_press(DA_FC447) && DA_FC1155 > V_100 && DA_FC1155 < V_8990) { 		set_val(DA_FC447, V_00); 		combo_stop(DA_FC93); 		combo_stop(DA_FC98); 		combo_stop(DA_FC99); 		combo_stop(DA_FC96); 		combo_stop(DA_FC89); 		DA_FC663 = V_00; 		combo_stop(DA_FC88)    combo_run(DA_FC92); 			} 	if (!DA_FC659) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_FC99); 					} 			} 	if (!DA_FC653) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_FC96); 					} 			} 	if (!DA_FC655) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_FC93); 			DA_FC663 = V_00; 			combo_stop(DA_FC88)     		} 			} 	if (!DA_FC657) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_FC98); 					} 			} 	if ((get_ival(DA_FC452) || get_ival(DA_FC448)) && !DA_FC365) { 		combo_stop(DA_FC4); 		combo_stop(DA_FC47); 		combo_stop(DA_FC33); 			} 	} define DA_FC1168 = 0xF; define DA_FC1169 = 0xF; int DA_FC1170 = 0x0; define DA_FC1171 = 0x1F40; define DA_FC1172 = 0x4; define DA_FC1173 = 0x7D0; int DA_FC663 = 0x0; const int16 DA_FC1329[] = { 	0xF, 0x14, 0x19 ,0x1E,0x23    ,0x91,0x96 , 0x9B, 0xA0,0xA5 ,    0xC3, 0xC8,0xCD, 0xD2,0xD7,0x145  ,0x14A, 0x14F, 0x154,0x159 } ; const int16 DA_FC1330[] = { 	0x37, 0x3C, 0x41 , 0x46  ,    0x6E , 0x73, 0x78,0x7D ,    0xEB, 0xF0,0xF5, 0xFA,  0x122, 0x127, 0x12C,0x131 } ; int DA_FC1175 = FALSE; int DA_FC1176; int DA_FC1177; int DA_FC1178; int DA_FC1179; function DA_FC265 () { 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= V_3000) { 		DA_FC1178 = V_360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA_FC1175 = FALSE; 		for ( DA_FC946 = V_00; 		DA_FC946 < sizeof(DA_FC1330) / sizeof(DA_FC1330[V_00]); 		DA_FC946++) { 			if (DA_FC1178 == DA_FC1330[DA_FC946]) { 				DA_FC1175 = TRUE; 				break; 							} 					} 		if (!DA_FC1175) { 			DA_FC1176 = DA_FC1330[V_00]; 			DA_FC1177 = abs(DA_FC1178 - DA_FC1330[V_00]); 			for ( DA_FC946 = V_01; 			DA_FC946 < sizeof(DA_FC1330) / sizeof(DA_FC1330[V_00]); 			DA_FC946++) { 				DA_FC1179 = abs(DA_FC1178 - DA_FC1330[DA_FC946]); 				if (DA_FC1179 < DA_FC1177) { 					DA_FC1176 = DA_FC1330[DA_FC946]; 					DA_FC1177 = DA_FC1179; 									} 							} 			set_polar(POLAR_LS, DA_FC1176, V_32767); 					} 			} } function DA_FC266 () { 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= V_3000) { 		DA_FC1178 = V_360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA_FC1175 = FALSE; 		for ( DA_FC946 = V_00; 		DA_FC946 < sizeof(DA_FC1329) / sizeof(DA_FC1329[V_00]); 		DA_FC946++) { 			if (DA_FC1178 == DA_FC1329[DA_FC946]) { 				DA_FC1175 = TRUE; 				break; 							} 					} 		if (!DA_FC1175) { 			DA_FC1176 = DA_FC1329[V_00]; 			DA_FC1177 = abs(DA_FC1178 - DA_FC1329[V_00]); 			for ( DA_FC946 = V_01; 			DA_FC946 < sizeof(DA_FC1329) / sizeof(DA_FC1329[V_00]); 			DA_FC946++) { 				DA_FC1179 = abs(DA_FC1178 - DA_FC1329[DA_FC946]); 				if (DA_FC1179 < DA_FC1177) { 					DA_FC1176 = DA_FC1329[DA_FC946]; 					DA_FC1177 = DA_FC1179; 									} 							} 			set_polar(POLAR_LS, DA_FC1176, V_32767); 					} 			} } int DA_FC1190; function DA_FC267() { 	if (combo_running(DA_FC88) && (        get_ival(DA_FC452) ||        get_ival(DA_FC447) ||        get_ival(DA_FC448) ||        get_ival(DA_FC453) ||        get_ival(DA_FC454) ||        get_ival(DA_FC449)      )) { 		combo_stop(DA_FC88); 		DA_FC663 = V_00; 			} 	if (DA_FC1170 == V_00) { 		if ( ( DA_FC1146 == V_00 && !combo_running(DA_FC91) && !combo_running(DA_FC99) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= V_3000 ) && ( event_press(DA_FC447) || (DA_FC663 == V_01 ||     combo_running(DA_FC97) || combo_running(DA_FC95)|| combo_running(DA_FC93) ||  combo_running(DA_FC96)     || combo_running(DA_FC89) || combo_running(DA_FC98) || combo_running(DA_FC90) ||  combo_running(DA_FC92)  ))     ) { 			if(DA_FC437)DA_FC266 (); 			else if (DA_FC438)DA_FC265 (); 			combo_restart(DA_FC88); 					} 			} 	else{ 		combo_stop(DA_FC88); 			} 	} combo DA_FC88 { 	if(DA_FC437)DA_FC266 (); 	else if (DA_FC438)DA_FC265 (); 	vm_tctrl(V_00); 	wait( V_4000); 	DA_FC663 = V_00; 	} combo DA_FC89 { 	set_val(DA_FC449,V_00); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( random(V_210, V_215) + DA_FC430); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait(V_600); 	wait( V_2000); 	} function DA_FC268() { 	if (DA_FC550 > V_600 && DA_FC550 <= V_800) { 		DA_FC1191 = V_240; 			} 	if (DA_FC550 > V_800 && DA_FC550 <= V_1000) { 		DA_FC1191 = V_230; 			} 	if (DA_FC550 > V_1000 && DA_FC550 <= V_1500) { 		DA_FC1191 = V_225; 			} 	if (DA_FC550 > V_1500 && DA_FC550 <= V_2000) { 		DA_FC1191 = V_235; 			} 	if (DA_FC550 > V_2000) { 		DA_FC1191 = V_218; 			} 	combo_run(DA_FC98); 	} combo DA_FC90 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( random(V_170, V_190)); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_500); 	} combo DA_FC91 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_205); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_300); 	} combo DA_FC92 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_190); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_400); 	} int DA_FC1196; int DA_FC1197; int DA_FC29; int DA_FC475; int DA_FC473; int DA_FC1201; int DA_FC1202; function DA_FC269() { 	} combo DA_FC93 { 	if (DA_FC1197) { 		set_val(DA_FC447, V_00); 		DA_FC1201 = V_400; 			} 	else { 		DA_FC1201 = V_00; 			} 	if (DA_FC1197) { 		DA_FC250(V_00, DA_FC1202); 		DA_FC253(V_00, V_00); 			} 	vm_tctrl(V_00); 	wait(DA_FC1201); 	if (DA_FC1197) { 		set_val(DA_FC451, V_100); 		set_val(DA_FC450, V_00); 		DA_FC1201 = V_60; 			} 	else { 		set_val(DA_FC450, V_100); 		DA_FC1201 = V_60; 			} 	DA_FC269(); 	set_val(DA_FC447,V_00); 	vm_tctrl(V_00); 	wait(DA_FC1201); 	set_val(DA_FC451, V_00); 	set_val(DA_FC450, V_00); 	set_val(DA_FC447,V_00); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(DA_FC1201); 	if (DA_FC1197) { 		DA_FC1201 = V_00; 			} 	else { 		DA_FC1201 = V_60; 			} 	set_val(DA_FC450, V_00); 	set_val(DA_FC451, V_00); 	set_val(DA_FC447,V_00); 	wait(DA_FC1201); 	vm_tctrl(V_00); 	set_val(DA_FC447, V_100); 	set_val(DA_FC451, V_100); 	DA_FC269(); 	wait(random(V_265, V_268) +   DA_FC429 ); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	if (DA_FC1197) { 		DA_FC1201 = V_25; 			} 	else { 		DA_FC1201 = V_30; 			} 	DA_FC269(); 	wait(random(V_00,V_02) + DA_FC1215 + DA_FC1214 + DA_FC1201 ); 	set_val(DA_FC451, V_100); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(random(V_00,V_02) + V_60); 	vm_tctrl(V_00); 	set_val(DA_FC447, V_00); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(random(V_00,V_02) + V_80); 	vm_tctrl(V_00); 	set_val(DA_FC451, V_100); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(V_2500); 	} int DA_FC1143; int DA_FC1146; int DA_FC550; int DA_FC1142; int DA_FC1214; int DA_FC1215 = 0x6F; int DA_FC1136; int DA_FC1147; int DA_FC1218; function DA_FC270() { 	DA_FC1218 = V_360 - get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DA_FC1218 > V_10 && DA_FC1218 < V_80) || (DA_FC1218 > V_110 && DA_FC1218 < V_170)) { 		DA_FC1202 = V_100; 			} 	if ((DA_FC1218 < V_350 && DA_FC1218 > V_280) || (DA_FC1218 < V_260 && DA_FC1218 > V_190)) { 		DA_FC1202 = -V_100; 			} 	if (DA_FC1146 == V_00 && (DA_FC550 >= V_750 || ((DA_FC1147 > V_3000 && DA_FC1142 > V_01 && DA_FC1142 < V_5000)))) { 		if (DA_FC550 <= V_2000 && DA_FC1142 > V_1500) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_170; 		} 		if (DA_FC550 <= V_2000 && DA_FC1142 > V_01 && DA_FC1142 <= V_1500) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_202; 					} 		if (DA_FC550 > V_2000 || (DA_FC1142 > V_01 && DA_FC1142 <= V_1500)) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_151; 					} 		if ((DA_FC550 > V_2000 && DA_FC1142 > V_1500) || DA_FC1147 > V_01 && DA_FC1142 > V_01) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_152; 					} 		if ((DA_FC550 < V_2000 && DA_FC1142 > V_1500) || DA_FC1146 > V_01 && DA_FC1142 > V_01) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_149; 					} 		if (DA_FC1142 > V_1500) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_148; 					} 		if (!DA_FC550 > V_2000 && DA_FC1147 > V_01 && DA_FC1142 > V_01 && DA_FC1142 <= V_1500) { 			set_val(DA_FC447, V_00); 			DA_FC1215 = V_147; 					} 		set_val(DA_FC447, V_00); 		combo_stop(DA_FC98); 		combo_stop(DA_FC99); 		combo_stop(DA_FC96); 		combo_stop(DA_FC89); 		combo_stop(DA_FC95); 		combo_stop(DA_FC92); 		combo_stop(DA_FC91); 		combo_run(DA_FC93); 			} 	else { 		if (DA_FC1146) { 			set_val(DA_FC447, V_00); 			combo_run(DA_FC94); 					} 		else { 			if (DA_FC550 < V_750) { 				set_val(DA_FC447, V_00); 				combo_run(DA_FC95); 							} 					} 			} } combo DA_FC94 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait(random(V_00, V_06) + random(V_200, V_205)); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait(random(V_00, V_06) + V_700); 	} combo DA_FC95 { 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( random(V_200, V_205) + DA_FC429 )  set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_700); 	} int DA_FC1222 = 0x101; int DA_FC1160 = 0x96; int DA_FC1224 = 0x0; combo DA_FC96 { 	set_val(DA_FC451, V_100); 	set_val(DA_FC450, V_00); 	set_val(DA_FC447,V_00); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(random(V_00,V_02) + V_60); 	set_val(DA_FC451, V_00); 	set_val(DA_FC450, V_00); 	set_val(DA_FC447,V_00); 	vm_tctrl(V_00); 	DA_FC269(); 	wait(random(V_00,V_02) + V_60); 	DA_FC1224 = DA_FC428; 	DA_FC269(); 	vm_tctrl(V_00); 	set_val(DA_FC450, V_100); 	set_val(DA_FC447,V_00); 	wait( V_60); 	set_val(DA_FC450, V_65); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1222 + random(-V_02, V_02) +  DA_FC432); 	set_val(DA_FC450, V_75); 	set_val(DA_FC447, V_00); 	DA_FC1196 = DA_FC1160; 	vm_tctrl(V_00); 	wait( DA_FC1160 + DA_FC1224 - V_58 + DA_FC457 ); 	set_val(DA_FC450, V_85); 	if(DA_FC1190)set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	set_val(DA_FC450, V_100); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_3000); 	} combo DA_FC97 { 	set_val(DA_FC450, V_100); 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( V_160 + DA_FC432 ); 	set_val(DA_FC450, V_100); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_3000); 	} int DA_FC1155; int DA_FC1226 = 0xDC; int DA_FC1191; int DA_FC1228 = 0x0; combo DA_FC98 { 	DA_FC1228 = DA_FC428; 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1226); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1191 + (DA_FC1228) + V_22 + DA_FC458); 	if(DA_FC1190)set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1226); 	set_val(DA_FC447, V_00); 	vm_tctrl(V_00); 	wait( V_2000); 	} int DA_FC1230 = TRUE; int DA_FC1164; int DA_FC1232 = 0x104; int DA_FC1233 = 0x0; combo DA_FC99 { 	set_val(DA_FC449, V_100); 	set_val(DA_FC450, V_100); 	if (DA_FC1230) { 		DA_FC1233 = DA_FC428; 			} 	else { 		DA_FC1233 = V_00   	} 	set_val(DA_FC447, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1232); 	vm_tctrl(V_00); 	wait( DA_FC1164 + DA_FC1233 + V_40)  } int DA_FC1236; int DA_FC1237 = 0x91; combo DA_FC100 { 	set_val(DA_FC447, V_100); 	set_val(DA_FC449, V_100); 	vm_tctrl(V_00); 	wait( DA_FC1237); 	vm_tctrl(V_00); 	wait( V_500); 	} int DA_FC646; int DA_FC645; int DA_FC648; int DA_FC647; combo DA_FC101 { 	set_val(DA_FC454, V_00); 	vm_tctrl(V_00); 	wait( V_30); 	set_val(DA_FC454, V_100); 	vm_tctrl(V_00); 	wait( V_60); 	} int DA_FC649; int DA_FC650; combo DA_FC102 { 	set_val(DA_FC453, V_100); 	vm_tctrl(V_00); 	wait( DA_FC650); 	} define DA_FC1244 = TRUE; define DA_FC1245 = 0x5F; define DA_FC1246 = 0xA; define DA_FC1247 = 0x46; define DA_FC1248 = FALSE; define DA_FC1249 = 0x32; define DA_FC1250 = 0x5F; define DA_FC1251 = XB1_LT; define DA_FC1252 = XB1_RT; define DA_FC1253 = XB1_LX; define DA_FC1254 = XB1_LY; define DA_FC1255 = POLAR_LS; int DA_FC1256; combo DA_FC103 { 	set_polar(POLAR_LS,V_00,V_00); 	vm_tctrl(V_00); 	wait(V_60); 	vm_tctrl(V_00); 	wait(V_60); 	} function DA_FC271() { 	if (    get_ival(DA_FC452) > V_30 &&    (get_ival(DA_FC451) || get_ival(DA_FC448)) &&    (!get_ival(DA_FC453) || !get_ival(DA_FC447))  ) { set_val(DA_FC452, V_00); 		if(!get_ival(DA_FC447)){ 			set_val(DA_FC451,V_100); 					} 		else{ 			set_val(DA_FC451,V_00); 					} 		  combo_run(DA_FC105); 		if(DA_FC431 == TRUE){ 			combo_run(DA_FC104); 					} 			} 	else { 		combo_stop(DA_FC105); 		combo_stop(DA_FC104); 			} 	} combo DA_FC104 { 	if (DA_FC431 == TRUE) { 		set_val(DA_FC450, V_100); 		DA_FC1257 = V_60; 			} 	else { 		DA_FC1257 = V_00; 			} 	set_val(DA_FC451, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1257); 	if (DA_FC431 == TRUE) { 		set_val(DA_FC450, V_00); 		DA_FC1257 = V_60; 			} 	else { 		DA_FC1257 = V_00; 			} 	set_val(DA_FC451, V_00); 	vm_tctrl(V_00); 	wait( DA_FC1257); 	if (DA_FC431 == TRUE) { 		set_val(DA_FC450, V_100); 			} 	vm_tctrl(V_00); 	wait( V_750); 	vm_tctrl(V_00); 	wait( V_750); 	} combo DA_FC105 { 	sensitivity(PS4_LX, NOT_USE, V_129); 	sensitivity(PS4_LY, NOT_USE, V_129); 	if(!get_ival(DA_FC447)){ 		set_val(DA_FC451,V_100); 			} 	else{ 		set_val(DA_FC451,V_00); 			} 	set_val(DA_FC452, V_100); 	vm_tctrl(V_00); 	wait(V_600); 	if(!get_ival(DA_FC447)){ 		set_val(DA_FC451,V_100); 			} 	else{ 		set_val(DA_FC451,V_00); 			} 	set_val(DA_FC452, V_100); 	vm_tctrl(V_00); 	wait(DA_FC585); 	if(!get_ival(DA_FC447)){ 		set_val(DA_FC451,V_100); 			} 	else{ 		set_val(DA_FC451,V_00); 			}     set_val(DA_FC452, V_00); 	vm_tctrl(V_00); 	wait(V_500); 	} int DA_FC1259; int DA_FC1257 ; int DA_FC1261 = TRUE; int DA_FC1262; int DA_FC1263; int DA_FC1264; int DA_FC1265; int DA_FC1266; function DA_FC272() { 	if(!get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC449) && !combo_running(DA_FC105) ){ 		if (DA_FC123(POLAR_LS, POLAR_RADIUS) > V_1800) { 			DA_FC1264 += get_rtime(); 			if(DA_FC1264 > (DA_FC455 * V_02)) DA_FC1264 = V_00; 			if(DA_FC1264 <= DA_FC455){ 				DA_FC116(POLAR_LS, DA_FC123(POLAR_LS,POLAR_ANGLE), DA_FC123(POLAR_LS, POLAR_RADIUS)); 							} 			if(DA_FC1264 >  DA_FC455){ 					sensitivity(PS4_LX, NOT_USE, V_71); 					sensitivity(PS4_LY, NOT_USE, V_65); 				} 					} 			} } combo DA_FC106 { 	set_val(DA_FC448,V_100); 	vm_tctrl(V_00); 	wait( DA_FC646); 	set_val(DA_FC448,  V_00); 	vm_tctrl(V_00); 	wait( V_30); 	if(DA_FC401){ 		set_val(DA_FC448,V_100); 			} 	vm_tctrl(V_00); 	wait( V_60); 	} combo DA_FC107 { 	set_val(DA_FC448,  V_00); 	vm_tctrl(V_00); 	wait( V_30); 	set_val(DA_FC448,V_100); 	vm_tctrl(V_00); 	wait( V_60); 	} combo DA_FC108 { 	set_val(DA_FC454,V_100); 	vm_tctrl(V_00); 	wait( DA_FC648); 	set_val(DA_FC454,  V_00); 	vm_tctrl(V_00); 	wait( V_30); 	if(DA_FC398){ 		set_val(DA_FC454,V_100); 			} 	vm_tctrl(V_00); 	wait( V_60); 	} int last_var int var_count combo DA_FC109 { 	combo_suspend(DA_FC109) 	wait(V_361) } function check (){ 	if(last_var[var_count] != V_361){ 	    var_count-- 	} 	else{ 		if(inv(var_count) != V_285){ 			check(); 		} 	} }

enum { V_00, V_01, V_02, V_03, V_04, V_05, V_06, V_07, V_08, V_09, V_10, V_11, V_12, V_13, V_14, V_15, V_16, V_17, V_18, V_19, V_20, V_21, V_22, V_23, V_24, V_25, V_26, V_27, V_28, V_29, V_30, V_31, V_32, V_33, V_34, V_35, V_36, V_37, V_38, V_39, V_40, V_41, V_42, V_43, V_44, V_45, V_46, V_47, V_48, V_49, V_50, V_51, V_52, V_53, V_54, V_55, V_56, V_57, V_58, V_59, V_60, V_61, V_62, V_63, V_64, V_65, V_66, V_67, V_68, V_69, V_70, V_71, V_72, V_73, V_74, V_75, V_76, V_77, V_78, V_79, V_80, V_81, V_82, V_83, V_84, V_85, V_86, V_87, V_88, V_89, V_90, V_91, V_92, V_93, V_94, V_95, V_96, V_97, V_98, V_99, V_100, V_101, V_102, V_103, V_104, V_105, V_106, V_107, V_108, V_109, V_110, V_111, V_112, V_113, V_114, V_115, V_116, V_117, V_118, V_119, V_120, V_121, V_122, V_123, V_124, V_125, V_126, V_127, V_128, V_129, V_130, V_131, V_132, V_133, V_134, V_135, V_136, V_137, V_138, V_139, V_140, V_141, V_142, V_143, V_144, V_145, V_146, V_147, V_148, V_149, V_150, V_151, V_152, V_153, V_154, V_155, V_156, V_157, V_158, V_159, V_160, V_161, V_162, V_163, V_164, V_165, V_166, V_167, V_168, V_169, V_170, V_171, V_172, V_173, V_174, V_175, V_176, V_177, V_178, V_179, V_180, V_181, V_182, V_183, V_184, V_185, V_186, V_187, V_188, V_189, V_190, V_191, V_192, V_193, V_194, V_195, V_196, V_197, V_198, V_199, V_200 }
enum { V_1000 = 0x3E8, V_3000 = 0xBB8, V_2000 = 0x7D0, V_360 = 0x168, V_14142 = 0x373E, V_46340 = 0xB504, V_300 = 0x12C, V_11111 = 0x2B67, V_99999 = 0x1869F, V_8050 = 0x1F72, V_8000 = 0x1F40, V_310 = 0x136, V_5100 = 0x13EC, V_9000 = 0x2328, V_400 = 0x190, V_220 = 0xDC, V_800 = 0x320, V_350 = 0x15E, V_500 = 0x1F4, V_250 = 0xFA, V_280 = 0x118, V_600 = 0x258, V_540 = 0x21C, V_290 = 0x122, V_320 = 0x140, V_750 = 0x2EE, V_987654 = 0xF1206, V_54321 = 0xD431, V_123456 = 0x1E240, V_440 = 0x1B8, V_32767 = 0x7FFF, V_345 = 0x159, V_225 = 0xE1, V_270 = 0x10E, V_3800 = 0xED8, V_10000 = 0x2710, V_2400 = 0x960, V_720 = 0x2D0, V_1560 = 0x618, V_328 = 0x148, V_445 = 0x1BD, V_235 = 0xEB, V_850 = 0x352, V_2500 = 0x9C4, V_725 = 0x2D5, V_283 = 0x11B, V_7590 = 0x1DAE, V_1500 = 0x5DC, V_1200 = 0x4B0, V_1100 = 0x44C, V_4000 = 0xFA0, V_330 = 0x14A, V_1600 = 0x640, V_3500 = 0xDAC, V_1510 = 0x5E6, V_4100 = 0x1004, V_228 = 0xE4, V_450 = 0x1C2, V_2700 = 0xA8C, V_920 = 0x398, V_8990 = 0x231E, V_210 = 0xD2, V_215 = 0xD7, V_240 = 0xF0, V_230 = 0xE6, V_218 = 0xDA, V_205 = 0xCD, V_265 = 0x109, V_268 = 0x10C, V_260 = 0x104, V_5000 = 0x1388, V_202 = 0xCA, V_700 = 0x2BC, V_1800 = 0x708, V_361 = 0x169, V_285 = 0x11D } 