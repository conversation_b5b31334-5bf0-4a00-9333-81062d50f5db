// Define variables to store the left stick's angle and direction
int LS_Angle;
int LS_Direction;

main {
    // Retrieve the current angle of the left stick in polar coordinates
    LS_Angle = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
    
    // Retrieve the current direction of the left stick and negate it
    LS_Direction = get_polar(POLAR_LS, POLAR_ANGLE) * -1;

    // --- Handle XB1_PL1 (Left) Button Press ---
    if(get_val(XB1_PL1)) { // Check if XB1_PL1 is pressed
        // Reset the XB1_PL1 button state to prevent repeated triggers
        set_val(XB1_PL1, 0);

        // Determine if the left stick is pointing to the left or right side
        if(LS_Angle > 90 && LS_Angle < 270) { // Left side
            // Move the right stick to the left relative to the left stick's direction
            set_polar(POLAR_RS, LS_Direction + 90, 32767);set_polar(POLAR_LS, 0,0);
        }
        else { // Right side
            // Move the right stick to the left relative to the left stick's direction
            set_polar(P<PERSON>AR_RS, LS_Direction - 90, 32767);set_polar(POLAR_LS, 0,0);
        }
    }

    // --- Handle XB1_PL2 (Right) Button Press ---
    if(get_val(XB1_PL2)) { // Check if XB1_PL2 is pressed
        // Reset the XB1_PL2 button state to prevent repeated triggers
        set_val(XB1_PL2, 0);

        // Determine if the left stick is pointing to the left or right side
        if(LS_Angle > 90 && LS_Angle < 270) { // Left side
            // Move the right stick to the right relative to the left stick's direction
            set_polar(POLAR_RS, LS_Direction - 90, 32767);set_polar(POLAR_LS, 0,0);
        }
        else { // Right side
            // Move the right stick to the right relative to the left stick's direction
            set_polar(POLAR_RS, LS_Direction + 90, 32767);set_polar(POLAR_LS, 0,0);
        }
    }
}
 