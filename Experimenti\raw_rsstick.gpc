// Script to normalize right stick movement to a fixed magnitude when exceeding a threshold
// This creates a more consistent stick response for precise aiming

// Constants for stick behavior
define RIGHT_MAG_THRESHOLD = 17;  // If stick magnitude exceeds 17%, normalize it
define RIGHT_MAG_TARGET    = 100; // Target magnitude to normalize to (100%)

// Variables for storing stick values
int rxVal;    // Raw X-axis value from right stick
int ryVal;    // Raw Y-axis value from right stick
int magnitude; // Calculated magnitude of stick movement

// Variables for scaled output
int scaledRX; // Scaled X-axis value
int scaledRY; // Scaled Y-axis value
int result;   // Used in sqrt calculation
int temp;     // Temporary variable for sqrt calculation

// Fast integer square root implementation using Newton's method
function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;  // <PERSON>'s iteration
    } while (temp > result);
    
    return result;
}

main {
    // 1. Read raw stick values
    rxVal = get_val(XB1_RX);
    ryVal = get_val(XB1_RY);

    // 2. Calculate the magnitude of stick movement using Pythagorean theorem
    magnitude = sqrt(rxVal * rxVal + ryVal * ryVal);

    // 3. If magnitude exceeds threshold, normalize the stick values
    if(magnitude >= RIGHT_MAG_THRESHOLD) {
        // Only scale if magnitude isn't zero (prevent division by zero)
        if(magnitude != 0) {
            // Scale both axes proportionally to maintain stick direction
            // while forcing the magnitude to RIGHT_MAG_TARGET
            scaledRX = (rxVal * RIGHT_MAG_TARGET) / magnitude;
            scaledRY = (ryVal * RIGHT_MAG_TARGET) / magnitude;
            
            // 4. Apply the scaled values to the right stick
            set_val(XB1_RX, scaledRX);
            set_val(XB1_RY, scaledRY);
        }
    }
    // If magnitude is below threshold, stick values remain unchanged
}