# Port Functions

port_status
port_status - Status of an I/O port

Description
uint8 port_status(uint8 port, uint8 *protocol, uint8 *device);
Get the current status of a port identified by the first argument. Additional information, related to the port, can be obtained utilizing the arguments protocol and device.

Parameters
port: An valid port identifier, accordingly table 1.
protocol: Address of an uint8 variable to receive the protocol identifier in use. Refer to table 2 for more details. This argument can be NULL.
device: Address of an uint8 variable to receive the device identifier in use. Refer to table 2 for more details. This argument can be NULL.
Table 1
Index	Port Designator	Port Label
1	PORT_USB_A	Input-A
2	PORT_USB_B	Input-B
0	PORT_USB_C	Output
3	PORT_USB_D	Prog
4	PORT_BT_A	
5	PORT_BT_B	
6	PORT_BT_C	
Table 2
Protocol	Device
0	1	2	0x40	0x80
PROTOCOL_PS3	Regular	Navigation			
PROTOCOL_PS4	Regular	Third Party	V2		
PROTOCOL_XB360	Regular				
PROTOCOL_XB360_WRC	Regular				
PROTOCOL_XB1	Regular	Third Party	Elite		
PROTOCOL_XB1_WRC	Regular		Elite		
PROTOCOL_WII					
PROTOCOL_SWITCH	Pro Controller				
PROTOCOL_HID		Generic		Mouse	Keyboard
PROTOCOL_STEAM	Regular	Wireless			
Return Value
The status value of the port, accordingly table 3.

Table 3
Status	Value	Bitmask
DIR_OUTPUT	0x80	0xC0
DIR_INPUT	0x40	0xC0
PASSTHRU_NONE	0x00	0x03
PASSTHRU_FULL	0x01	0x03
PASSTHRU_PARTIAL	0x02	0x03
Examples
Example #1 port_status() example
// Function to find if there is an Xbox One Elite controller connected
int8 find_xb1_elite() {
    uint8 status, protocol, device;
    uint8 i;
 
    for(i = 0; i <= 3; ++i) {
        status = port_status(i, &protocol, &device);
        if(status & DIR_INPUT) {
            if(protocol == PROTOCOL_XB1) {
                if(device == 2) { // Elite Controller
                    return(i);
                }
            }
        }        
    }
    return(-1);
}

port_connect
port_connect - Connect output connection

Description
void port_connect();
void port_connect(uint8 port, <PROTOCOL_IDENTIFIER>);
Try establish an output connection utilizing the default output protocol configured by the user, or using the output protocol specified by the optional arguments.

Optional Parameters
port: An valid output port identifier, accordingly table 1.
<PROTOCOL_IDENTIFIER>: An valid output protocol identifier, accordingly table 2.
Table 1
Index	Port Designator
0	PORT_USB_C
6	PORT_BT_C
Table 2
Protocol Identifiers
PROTOCOL_AUTO
PROTOCOL_PS3
PROTOCOL_PS4
PROTOCOL_XB360
PROTOCOL_XB1
PROTOCOL_SWITCH
PROTOCOL_HID
PROTOCOL_WHEEL
PROTOCOL_WII
Return Value
No value is returned.

Examples
Example #1 port_connect() example
init {
    // Change the output protocol to USB Multi Interface HID.
    port_connect(PORT_USB_C, PROTOCOL_HID);
}

port_disconnect
port_disconnect - Disconnect output connection

Description
void port_disconnect();
Disconnect the active output connection.

Only a single output connection can exist at time on either PORT_USB_C or PORT_BT_C.
The effects of port_disconnect() remains after unload the script that called it, until the next power cycle. Use port_connect() to reconnect a disconnected port.
Return Value
No value is returned.

Examples
Example #1 port_disconnect() example
init {
    // If USB OUTPUT is connected ...
    if(port_status(PORT_USB_C, NULL, NULL) & DIR_OUTPUT) {
        // ... disconnect it.
        port_disconnect();
    }
}

port_inhibit_ffb
port_inhibit_ffb - Disable FFB on a port

Description
void port_inhibit_ffb(uint8 port);
Disable Force Feedback (FFB - controller vibration) on the port specified by the argument.

While the script is loaded and running, this command overwrites the FFB settings configured by the user.
Parameters
port: An valid port identifier, accordingly table 1.
Table 1
Index	Port Designator
1	PORT_USB_A
2	PORT_USB_B
0	PORT_USB_C
3	PORT_USB_D
4	PORT_BT_A
5	PORT_BT_B
Return Value
No value is returned.

Examples
Example #1 port_inhibit_ffb() example
init {
    // Disable FFB on all ports
    port_inhibit_ffb(PORT_USB_A);
    port_inhibit_ffb(PORT_USB_B);
    port_inhibit_ffb(PORT_USB_C);
    port_inhibit_ffb(PORT_USB_D);
    port_inhibit_ffb(PORT_BT_A);
    port_inhibit_ffb(PORT_BT_B);
}

port_permit_ffb
port_permit_ffb - Enable FFB on a port

Description
void port_permit_ffb(uint8 port);
Enable Force Feedback (FFB - controller vibration) on the port specified by the argument.

While the script is loaded and running, this command overwrites the FFB settings configured by the user.
Parameters
port: An valid port identifier, accordingly table 1.
Table 1
Index	Port Designator
1	PORT_USB_A
2	PORT_USB_B
0	PORT_USB_C
3	PORT_USB_D
4	PORT_BT_A
5	PORT_BT_B
Return Value
No value is returned.

Examples
Example #1 port_permit_ffb() example
init {
    // Enable FFB on all ports
    port_permit_ffb(PORT_USB_A);
    port_permit_ffb(PORT_USB_B);
    port_permit_ffb(PORT_USB_C);
    port_permit_ffb(PORT_USB_D);
    port_permit_ffb(PORT_BT_A);
    port_permit_ffb(PORT_BT_B);
}

port_turnoff
port_turnoff - Turn off the device on a port

Description
void port_turnoff(uint8 port);
If supported, issue the command to turn off the input device connected on the port specified by the argument.

This command is mainly aimed to disconnect wireless connections. However, some wired devices supports soft turn off, such as the Xbox One controller.
Parameters
port: An valid port identifier, accordingly table 1.
Table 1
Index	Port Designator
1	PORT_USB_A
2	PORT_USB_B
0	PORT_USB_C
3	PORT_USB_D
4	PORT_BT_A
5	PORT_BT_B
Return Value
No value is returned.

Examples
Example #1 port_turnoff() example
init {
    // If there is an device connected to Bluetooth-A ...
    if(port_status(PORT_BT_A, NULL, NULL)) {
        // ... disconnect it.
        port_turnoff(PORT_BT_A);
    }
}

port_usb_poweron
port_usb_poweron - Power on an USB input port

Description
void port_usb_poweron(uint8 port);
Switch on the power of the USB input port identified by the argument.

This command does not affects ports that are operating in output mode (USB device).
Parameters
port: An valid USB input port identifier, accordingly table 1.
Table 1
Index	Port Designator
1	PORT_USB_A
2	PORT_USB_B
0	PORT_USB_C
3	PORT_USB_D
Return Value
No value is returned.

Examples
Example #1 port_usb_poweron() example
#include <keyboard.gph>
 
bool power_status;
 
main {
    // F5 switches OFF the power of INPUT-B
    if(key_status(KEY_F5) && power_status) {
        port_usb_poweroff(PORT_USB_B);
        power_status = 0;
    }
    // and F6 switches ON the power of INPUT-B
    else if(key_status(KEY_F6) && !power_status) {
        port_usb_poweron(PORT_USB_B);
        power_status = 1;
    }
}

port_usb_poweroff
port_usb_poweroff - Power off an USB input port

Description
void port_usb_poweroff(uint8 port);
Switch off the power of the USB input port identified by the argument.

This command does not affects ports that are operating in output mode (USB device).
The effects of port_usb_poweroff() remains after unload the script that called it, until the next power cycle. Use port_usb_poweron() to revert the effects of port_usb_poweroff().
Parameters
port: An valid USB input port identifier, accordingly table 1.
Table 1
Index	Port Designator
1	PORT_USB_A
2	PORT_USB_B
0	PORT_USB_C
3	PORT_USB_D
Return Value
No value is returned.

Examples
Example #1 port_usb_poweroff() example
#include <keyboard.gph>
 
bool power_status;
 
main {
    // F5 switches OFF the power of INPUT-B
    if(key_status(KEY_F5) && power_status) {
        port_usb_poweroff(PORT_USB_B);
        power_status = 0;
    }
    // and F6 switches ON the power of INPUT-B
    else if(key_status(KEY_F6) && !power_status) {
        port_usb_poweron(PORT_USB_B);
        power_status = 1;
    }
}