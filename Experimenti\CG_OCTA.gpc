//=============================================================================
// GLOBAL VARIABLES
//=============================================================================
int LX, LY;             // Raw stick inputs
int scaled_x, scaled_y; // Outputs after octagonal dead zone

// Tune these as desired:
int DEADZONE  = 30;     // Inner octagonal dead zone threshold
int MAX_INPUT = 100;    // Maximum raw stick value

// Approximation for octagon shape
// Using a balanced value for clear octagon edges
int INV_SQRT2_NUM = 700;  // Middle ground between circle (725) and square (680)
int INV_SQRT2_DEN = 1024;

// Temporary variables (all global)
int sign_x, sign_y;
int X, Y;
int L_inf, L_1;
int diff, diffScaled;
int octNorm, scaledNorm;

//=============================================================================
// MAIN
//=============================================================================
main {
    // 1) Read raw inputs
    LX = get_val(PS4_LX);
    LY = get_val(PS4_LY);

    // 2) Apply octagonal dead zone
    map_octagonal_deadzone();  

    // 3) Write adjusted values back
    set_val(PS4_LX, scaled_x);
    set_val(PS4_LY, scaled_y);
}

//=============================================================================
// FUNCTION: map_octagonal_deadzone()
// Uses the global LX, LY as input
// Writes to scaled_x, scaled_y as output
//=============================================================================
function map_octagonal_deadzone() {

    // (A) Determine signs
    if(LX >= 0) sign_x = 1; else sign_x = -1;
    if(LY >= 0) sign_y = 1; else sign_y = -1;

    // (B) Work in absolute values for the math
    X = abs(LX);
    Y = abs(LY);

    // (C) Compute L_inf (max) and L_1 (sum)
    L_inf = max(X, Y);
    L_1   = X + Y;

    // (D) Scale up L_1 to match L_inf at diagonals
    diffScaled = (L_1 * 1024) / (1024 + INV_SQRT2_NUM);
    octNorm = max(L_inf, diffScaled);

    // (E) Dead zone check
    if(octNorm <= DEADZONE) {
        scaled_x = 0;
        scaled_y = 0;
        return;
    }

    // (F) Clamp if you expect LX/LY might exceed MAX_INPUT
    if(octNorm > MAX_INPUT) {
        octNorm = MAX_INPUT;
    }

    // (G) Scale from [DEADZONE..MAX_INPUT] -> [0..MAX_INPUT]
    scaledNorm = (octNorm - DEADZONE) * MAX_INPUT / (MAX_INPUT - DEADZONE);

    // (H) Compute scaled_x, scaled_y so the new "octNorm" is scaledNorm
    //     x' = X * (scaledNorm / octNorm)
    //     y' = Y * (scaledNorm / octNorm)
    scaled_x = X * scaledNorm / octNorm;
    scaled_y = Y * scaledNorm / octNorm;

    // (I) Restore sign
    scaled_x *= sign_x;
    scaled_y *= sign_y;
}