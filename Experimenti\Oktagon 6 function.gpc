// Constants for stick identification and scaling
define stickX = XB1_LX;
define stickY = XB1_LY;
define o_MAX_VAL = 95;     // Adjust this to scale octagon size (1-100)
// 100 = full size, 60 = 60% size, etc.
define o_INV_SQRT2_NUM = 707;    // Approximately 1/√2 * 1000
define o_INV_SQRT2_DEN = 1000;   // Denominator for diagonal scaling

// Global variables
int x, y;
int o_abs_x, o_abs_y;
int o_L_inf, o_L_1;      // For L-infinity and L1 norms
int o_octNorm;         // Octagonal norm
int o_scaled_L1;       // Scaled L1 norm
int o_output_x, o_output_y;
int o_rotated_x, o_rotated_y;   // For rotated outputs

main {
octagonus()
}

function octagonus()  {

    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Compute absolute values
    o_abs_x = abs(x);
    o_abs_y = abs(y);

    // Calculate L-infinity (max) norm and L1 (Manhattan) norm
    if (o_abs_x > o_abs_y) {
        o_L_inf = o_abs_x;
    } else {
        o_L_inf = o_abs_y;
    }
    o_L_1 = o_abs_x + o_abs_y;
    
    // Scale L1 norm for octagonal shape
    o_scaled_L1 = (o_L_1 * o_INV_SQRT2_NUM) / o_INV_SQRT2_DEN;
    
    // Octagonal norm is the maximum of o_L_inf and o_scaled_L1
    if (o_L_inf > o_scaled_L1) {
        o_octNorm = o_L_inf;
    } else {
        o_octNorm = o_scaled_L1;
    }
    
    // If the stick is moved (nonzero input)
    if (o_octNorm > 0) {
        // Scale outputs while maintaining direction and apply o_MAX_VAL scaling
        o_output_x = (x * o_MAX_VAL) / o_octNorm;
        o_output_y = (y * o_MAX_VAL) / o_octNorm;
        
        // Clamp outputs to o_MAX_VAL limits
        if (o_output_x > o_MAX_VAL) o_output_x = o_MAX_VAL;
        if (o_output_x < -o_MAX_VAL) o_output_x = -o_MAX_VAL;
        if (o_output_y > o_MAX_VAL) o_output_y = o_MAX_VAL;
        if (o_output_y < -o_MAX_VAL) o_output_y = -o_MAX_VAL;
    } else {
        o_output_x = 0;
        o_output_y = 0;
    }
    
    // Apply a 45-degree rotation (clockwise) to the output vector.
    // Rotation matrix for 45° clockwise is:
    // [ cos45   sin45 ]
    // [-sin45   cos45 ]
    // With cos45 = sin45 ≈ 707/1000.
    o_rotated_x = ((o_output_x + o_output_y) * o_INV_SQRT2_NUM) / o_INV_SQRT2_DEN;
    o_rotated_y = ((o_output_y - o_output_x) * o_INV_SQRT2_NUM) / o_INV_SQRT2_DEN;
    
    // Set final rotated values to the stick outputs
    set_val(stickX, o_rotated_x);
    set_val(stickY, o_rotated_y);
    }
 