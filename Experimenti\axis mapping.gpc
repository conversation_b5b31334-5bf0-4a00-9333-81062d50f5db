// Axis Mapping Script for Cronus ZEN
// This script provides custom deadzone and sensitivity adjustments for controller analog sticks
// It remaps the input values to provide smoother and more precise control

// Internal variables used for axis mapping calculations
int axisVal,   // Holds the final calculated axis value
    sign,      // Stores the direction (+1 or -1)
    step1,     // Calculation step for deadzone adjustment
    step2,     // Calculation step for sensitivity scaling
    step3,     // Calculation step for input processing
    step4;     // Final calculation step before output

// Configurable settings
int console_deadzone = 25;    // The game's built-in deadzone (in percent)
int custom_deadzone = 25;     // Your custom deadzone setting (in percent)
int custom_sensitivity = 200; // Sensitivity multiplier (100 = normal, 200 = double, etc.)

// Main axis mapping function
// Parameters:
//   cdz: Console/game deadzone
//   axis: Which axis to modify (PS4_LX, PS4_LY, etc.)
//   dz: Custom deadzone value
//   sens: Sensitivity multiplier
function axis(int cdz, int axis, int dz, int sens)
{
    // Prevent invalid deadzone values that could occur at high sensitivities
    if (dz >= 100 * 100 / sens)
        dz = (100 * 100 / sens) - 1;

    // Only process input if it's outside the deadzone
    if (abs(get_ival(axis)) > dz)
    {
        // Determine input direction (+1 or -1)
        sign = get_ival(axis) / abs(get_ival(axis));
        
        // Calculate the range of motion after console deadzone
        step1 = 100 - cdz;
        
        // Calculate the input range considering sensitivity
        step2 = 100 * 100 / sens - dz;
        if (step2 < 0)
            step2 = 1;
            
        // Apply deadzone offset to input
        step3 = get_ival(axis) - sign * dz;
        
        // Scale the input to the new range
        step4 = step3 * step1 / step2;
        
        // Combine console deadzone with scaled input
        axisVal = sign * cdz + step4;
        set_val(axis, axisVal);
    }
    else
    {
        // If input is within deadzone, output zero
        set_val(axis, 0);
    }
}

main
{
    // Apply axis mapping to left analog stick X-axis
    axis(console_deadzone, PS4_LX, custom_deadzone, custom_sensitivity);

    // Apply axis mapping to left analog stick Y-axis
    axis(console_deadzone, PS4_LY, custom_deadzone, custom_sensitivity);

    // Output the processed values to debug traces
    // These can be viewed in the Zen Studio software for debugging
    set_val(TRACE_1, get_val(PS4_LX));  // Show processed X-axis value
    set_val(TRACE_2, get_val(PS4_LY));  // Show processed Y-axis value
}