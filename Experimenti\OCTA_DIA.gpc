int LX, LY;            
int scaled_x, scaled_y;////////////////////////////////////////////////////////////////////////////////
// GLOBAL
////////////////////////////////////////////////////////////////////////////////
const int8 anglesMax[] = {
   // Index: 0..9
   100, 99, 98, 97, 96, 95, 94, 94, 93, 92,
   // 10..19
    91, 90, 89, 88, 87, 86, 85, 85, 84, 83,
   // 20..29
    82, 78, 70, 78, 82, 83, 84, 85, 85, 86,
   // 30..39
    87, 88, 89, 90, 91, 92, 93, 94, 94, 95,
   // 40..44
    96, 97, 98, 99, 100
};

int angle, radius;

const string INFO1  = "OCTAGONE";
const string INFO2 = "CONVEX";
const string LEFTP  = "PING";
const string RIGHTP = "AI ANGLE";
const string PHASEP = "PHASE";  // Add this line for phase display
const string ANGLEP = "ANGLE";  // Add this line for angle display
const string RADIUSP = "BASE";

define RIGHT_MAG_THRESHOLD = 17;
// Random seed will be updated each frame
int randomSeed = 0;

int radius_base = 100;  // Starting value for base radius percentage

// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;
int virtmach = -9;

// Variables for random radius control
int random_update_timer;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;

int AngleInterval = 165;
int AI_VALUES[7];
int AI_VALUES_COUNT;
int current_index = 0;

// Virtual Machine Speed
init {
    AI_VALUES[0] = 160;
    AI_VALUES[1] = 165;
    AI_VALUES[2] = 170;
    AI_VALUES[3] = 180;
    AI_VALUES[4] = 190;
    AI_VALUES[5] = 200;
    AI_VALUES[6] = 150;
    AI_VALUES_COUNT = 7;
}
init {
    AI_VALUES_COUNT = sizeof(AI_VALUES) / sizeof(AI_VALUES[0]);
}

define PHASE_MULTIPLIER = 10;
int phase_speed = 10;

int lt_counter;    // For LT oscillation
int going_up;      // For LT direction
int combo_active;  // Track if LT+RT is pressed
int rt_timer;      // For RT spam
int rb_timer;      // For RB timing

init {
    lt_counter = 50;  // Start at 50 for LT
    going_up = 1;
    combo_active = 0;
    rt_timer = 0;
    rb_timer = 0;
}

////////////////////////////////////////////////////////////////////////////////
// Global Variables (all int declarations outside functions)
////////////////////////////////////////////////////////////////////////////////
//int LX, LY;              // Raw stick inputs
//int scaled_x, scaled_y;  // Final outputs after shaping

int DEADZONE   = 30;          // Quadratische Deadzone
int MAX_INPUT  = 70;         // Maximaler Wert je Achse
int MAX_SUM    = 140;         // Sum(|x|+|y|) -> Grenze fürs Oktagon

// Helper globals for deadzone scaling
int sign, abs_val, output, sum, scale_factor;

// Helper globals for circular clamp
int mag_sq, limit_sq, mag;

// Variables for the integer square-root function
int iSqrtValue, iSqrtRes, iSqrtBit, iSqrtTemp;

main {


	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (2);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (2);
      }
      set_val(XB1_RIGHT,0);
	}

    //   DISABLE / ENABLE ENTIRE SCRIPT
    if(get_ival(XB1_LT)){
    if(event_press(XB1_VIEW)){ 
        KS_EntireScript = !KS_EntireScript;
        f_set_notify (KS_EntireScript);
    }
    set_val(XB1_VIEW,0);
    }
                                  
    //   DISABLE / ENABLE ENTIRE SCRIPT
    if( !KS_EntireScript){  
    set_rgb(0,0,255); // BLUE

    // Update the random timer
    if(random_update_timer) {
        random_update_timer -= get_rtime();
    }

    LX = get_val(XB1_LX);  // Get stick values first
    LY = get_val(XB1_LY);

    // Only process stick inputs if both triggers are pressed or both triggers are released
    if((get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0) ||
       (get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0)) {
        // Execute FAKE_SHOT combo when either X or LS is pressed and triggers are active
        if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
            if(event_press(XB1_X) || event_press(XB1_LS)) {
            set_val(XB1_LT, 0);set_val(XB1_RT, 0);set_val(XB1_X, 0);
                combo_run(FAKE_SHOT);
            }
        }
        angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
        radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

        if (get_val(XB1_LS)) {
        set_val(XB1_LT, 100);set_val(XB1_RT, 100);set_val(XB1_LS, 0);
        // After setting triggers, check if X is pressed to run combo
            if(get_val(XB1_X)) {
                combo_run(FAKE_SHOT);
            }
            //map_convex_octagon(LX, LY);  // Apply octagon mapping when XB1_RS is active
            //set_val(XB1_LX, scaled_x);  // Apply the scaled values
            //set_val(XB1_LY, scaled_y);
            set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
        } else if (get_val(XB1_RS)) {set_val(XB1_RS, 0);
            map_convex_octagon(LX, LY);  // Apply octagon mapping when XB1_RS is active
            set_val(XB1_LX, scaled_x);  // Apply the scaled values
            set_val(XB1_LY, scaled_y);
        } else {
            // Apply set_polar2 with anglesMax when XB1_LS is not active
            set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
        }
    } else {
        scaled_x = LX;
        scaled_y = LY;
    }

    vm_tctrl(virtmach)
 
     // Polarisierungs-Werte abfragen
    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

    // Reset shared values at the start of each loop iteration
    rb_val = 0;
    lb_val = 0;

    // Handle radius-based LB behavior first
    if(radius < 2000) {
        if(get_ival(XB1_LB)) {
            set_val(XB1_LT, 100);
            set_val(XB1_RT, 100);
            set_val(XB1_LB, 0);

            if(!combo_running(LeftRightCombo)) {
                combo_run(LeftRightCombo);
            }
        }
        else {
            combo_stop(LeftRightCombo);
            set_val(XB1_LT, 0);
            set_val(XB1_RT, 0);
        }
    }
    // When radius >= 2000, we keep the original LB value
    else {
        if(get_ival(XB1_LB)) {
            lb_val = 100;
        } else {
            lb_val = 0;
        }
    }

    // Only handle X and Y and A buttons if triggers are not pressed
    if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
        handle_rb_related_button(XB1_X);
        handle_rb_related_button(XB1_Y);
        handle_lb_related_button(XB1_A);
    }

    // **Directly handle XB1_RB press**
    if(get_ival(XB1_RB)) {
        rb_val = 100;
    }

    // Set RB based on aggregated value
    set_val(XB1_RB, rb_val);
    
    // Only set LB from lb_val if radius >= 2000
    if(radius >= 2000) {
        set_val(XB1_LB, lb_val);
    }

    //pass();
 	/*
 		if(get_val(XB1_RS)) {
        combo_run(FAKE_SHOT);set_val(XB1_RS,0);
    }
    */

    // Finesse LB+B
    if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
    }
	/*
    // Finesse with R3
    if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
    }
	*/
	
    // Shots with B
    if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);
		}
    }
	
    // Shots with RB+B
    if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);
		}
    }

    if(get_ival(XB1_RT)){
        if(event_press(XB1_RIGHT)) {
            current_index = (current_index + 1) % AI_VALUES_COUNT;
            AngleInterval = AI_VALUES[current_index];
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), ANGLEP[0], AngleInterval);
            //print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
        }
        if(event_press(XB1_LEFT)) {
            current_index = (current_index - 1 + AI_VALUES_COUNT) % AI_VALUES_COUNT;
            AngleInterval = AI_VALUES[current_index];
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), ANGLEP[0], AngleInterval);
            //print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
    }

    if(get_ival(XB1_LT)){
        if(event_press(XB1_UP) && phase_speed < 20) {
            phase_speed += 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), PHASEP[0], phase_speed);
        }
        if(event_press(XB1_DOWN) && phase_speed > 1) {
            phase_speed -= 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), PHASEP[0], phase_speed);
        }
        set_val(PS4_UP, 0);
        set_val(PS4_DOWN, 0);
    
        if(event_press(XB1_RIGHT) && radius_base < 99) {
            radius_base += 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), RADIUSP[0], radius_base);
        }
        if(event_press(XB1_LEFT) && radius_base > 1) {
            radius_base -= 1;
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), RADIUSP[0], radius_base);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
    }
    }// Entire Script ON/OFF 
    else {set_rgb(255,0,0); }

}

function pass() {
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
    angle = get_polar(POLAR_LS, POLAR_ANGLE);
    
    if (radius < 2400) {
        set_polar(POLAR_LS, 0, 0);
        set_val(XB1_LB, 0);
    } else if (radius >= 2400 && radius < 8000) {
        set_val(XB1_LB, 100);
        
        if (!get_val(XB1_LS)) {  // Only apply when XB1_LS is not active
            set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
        } else {
            set_polar2(POLAR_LS, angle, 8000);
        }
    } else if (radius >= 8000) {
        if (!get_val(XB1_LS)) {  // Only apply when XB1_LS is not active
            set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
        } else {
            set_polar2(POLAR_LS, angle, 10000);
        }
    }
    
    if (!get_val(XB1_LS)) {  // Only apply octagon mapping when XB1_LS is not active
        LX = get_val(XB1_LX);
        LY = get_val(XB1_LY);
        map_circular_octagon(LX, LY);
        set_val(XB1_LX, scaled_x);
        set_val(XB1_LY, scaled_y);
    }
}

function handle_rb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            rb_val = 100; // Activate XB1_RB
            combo_run (stop_lb);
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

combo stop_lb {
	wait(100);
	set_val(XB1_LB, 0);
	wait(100);
}

function handle_lb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);lb_val = 100;
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            lb_val = 100; // Activate XB1_LB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;

combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

int UltimatePower;

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function getPolar(Stick, AngleOrRadius) {
  if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
  return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));   
}

//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================

function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 

combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}

/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}

int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

int data_indx;

/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
}

int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
}

function set_ds4_led(colour) {
    set_led(LED_1, duint8 (colour * 4));
    set_led(LED_2, duint8 ((colour * 4) + 1));
    set_led(LED_3, duint8 ((colour * 4) + 2));
    set_led(LED_4, duint8 ((colour * 4) + 3));
}

int KS_EntireScript = FALSE;
function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}

function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);  
}

const string OFF   = "OFF";
const string ON    = "ON";

function defend(){

    // Check for LT+RT combo
    if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
        combo_active = 1;
    } else {
        combo_active = 0;
        // Reset everything when combo is released
        set_val(XB1_LT, get_val(XB1_LT));
        set_val(XB1_RT, get_val(XB1_RT));
        set_val(XB1_LB, get_val(XB1_LB));
        lt_counter = 50;
        rt_timer = 0;
        rb_timer = 0;
    }

    if(combo_active) {
        // Handle LT oscillation (50-100)
        set_val(XB1_LT, lt_counter);
        if(going_up) {
            lt_counter = lt_counter + 1.5;  // Much slower oscillation
            if(lt_counter >= 100) {
                going_up = 0;
            }
        } else {
            lt_counter = lt_counter - 1.5;  // Much slower oscillation
            if(lt_counter <= 50) {
                going_up = 1;
            }
        }

        // Handle RT timing (1 second on, 500ms off)
        rt_timer = rt_timer + 1;
        if(rt_timer <= 60) {  // 1 second on (100 ticks)
            set_val(XB1_RT, 100);
        } else if(rt_timer <= 80) {  // 500ms off (50 ticks)
            set_val(XB1_RT, 0);
        } else {
            rt_timer = 0;  // Reset cycle
        }

        // Handle RB timing (3 seconds on, 1 second off)
        rb_timer = rb_timer + 1;
        if(rb_timer <= 210) {  // 3 seconds on (300 ticks)
            set_val(XB1_LB, 100);
        } else if(rb_timer <= 220) {  // 1 second off (100 ticks)
            set_val(XB1_LB, 0);
        } else {
            rb_timer = 0;  // Reset cycle
        }
    }
    }

/*
function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;
    } while (temp > result);
    
    return result;
}
*/

// Function to generate a pseudo-random number
function getRandomNumber() {
    randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
    return randomSeed;
}

// Get random number between min and max
function getRandomRange(min, max) {
    return (getRandomNumber() % (max - min + 1)) + min;
}


////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////
//int angle, radius;

////////////////////////////////////////////////////////////////////////////////
// COMBOS
////////////////////////////////////////////////////////////////////////////////
combo LeftRightCombo {
    // 1) Linken Stick nach links
    set_polar(POLAR_LS, 0, 32000);
    //set_val(XB1_LX, -100);
    wait(AngleInterval);

    // 2) Linken Stick nach rechts
    set_polar(POLAR_LS, 180, 32000);
    //set_val(XB1_LX, 100);
    wait(AngleInterval);
}



combo FAKE_SHOT
{
  set_val(XB1_B, 100);
  vm_tctrl(0);
  wait(40);
  set_val(XB1_B, 100);
  set_val(XB1_A, 100);
  vm_tctrl(0);
  wait(60);
  set_val(XB1_B, 0);
  set_val(XB1_A, 100);
  vm_tctrl(0);
  wait(60);
  //Get_LS_Output = TRUE;
}

////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
//   - Must not declare local int variables here, so we use the global ones
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes   = 0;
    iSqrtBit   = 1 << 14; // 2^14 = 16384 (enough for up to ~20000)

    // Shift down until bit <= value
    while(iSqrtBit > iSqrtValue) {
        iSqrtBit = iSqrtBit >> 2;
    }

    while(iSqrtBit != 0) {
        iSqrtTemp = iSqrtRes + iSqrtBit;
        if(iSqrtValue >= iSqrtTemp) {
            iSqrtValue = iSqrtValue - iSqrtTemp;
            iSqrtRes   = iSqrtRes + (iSqrtBit << 1);
        }
        iSqrtRes = iSqrtRes >> 1;
        iSqrtBit = iSqrtBit >> 2;
    }
    return iSqrtRes;
}


////////////////////////////////////////////////////////////////////////////////
// Function: apply_one_axis_deadzone(val)
//   - Applies a square deadzone on a single axis: [DEADZONE..100] -> [0..100]
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(int val) {
    // Vorzeichen
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }

    abs_val = abs(val);

    // Innerhalb der Deadzone -> 0
    if(abs_val <= DEADZONE) {
        return 0;
    }

    // Begrenzen, falls > MAX_INPUT
    if(abs_val > MAX_INPUT) {
        abs_val = MAX_INPUT;
    }

    // Linear von [DEADZONE..MAX_INPUT] nach [0..MAX_INPUT] skalieren
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);

    return (sign * output);
}


// Hauptfunktion zum Erzeugen des konvexen Oktagons
function map_convex_octagon(int x, int y) {
    // 1) Square Deadzone pro Achse
    scaled_x = apply_one_axis_deadzone(x);
    scaled_y = apply_one_axis_deadzone(y);

    // 2) Sum-of-abs Clamp -> |x| + |y| <= 200
    sum = abs(scaled_x) + abs(scaled_y);
    if(sum > MAX_SUM) {
        scale_factor = (MAX_SUM * 1000) / sum;
        scaled_x = (scaled_x * scale_factor) / 1000;
        scaled_y = (scaled_y * scale_factor) / 1000;
    }

    // 3) X/Y zusätzlich auf ±100 beschränken
    if(scaled_x >  MAX_INPUT) scaled_x =  MAX_INPUT;
    if(scaled_x < -MAX_INPUT) scaled_x = -MAX_INPUT;
    if(scaled_y >  MAX_INPUT) scaled_y =  MAX_INPUT;
    if(scaled_y < -MAX_INPUT) scaled_y = -MAX_INPUT;
} 