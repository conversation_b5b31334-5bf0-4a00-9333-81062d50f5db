define Stick_Press_LB_Threshold = 95;

int stick_swap_toggle;
int rightStickMagnitude; // Declare rightStickMagnitude at the top

int UltimatePower;
int DYN_Acc;

main {

vm_tctrl(-9)

    if(get_ival(XB1_LT)) {
        set_val(XB1_DOWN, 0);
        if(event_press(XB1_DOWN))
            stick_swap_toggle = !stick_swap_toggle;
    }
 
     if(stick_swap_toggle) {
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
            set_val(POLAR_LX, get_val(POLAR_RX));
            set_val(POLAR_LY, get_val(POLAR_RY));
        }
        if(rightStickMagnitude >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
            combo_run(Press_LB);
        // Keep right stick active
    }
 
  if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (4);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (3);
      }
      set_val(XB1_RIGHT,0);
	}

    if(get_val(XB1_LT) && get_val(XB1_B)) {
            combo_run(trivela);
        }

    // Check if LT is not pressed
    if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
        combo_run(TapB);
    
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
        set_val(XB1_B,0);
        if (event_press(XB1_B)) combo_run(PressB);
    	}
	}
	
	if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(banks);
    } else if(combo_running(banks)) {
        combo_stop(banks);
    }
    
    if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
        combo_run(TapB180);
    
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
        set_val(XB1_B,0);
        if (event_press(XB1_B)) combo_run(PressB99);
    	}
	}
	
	if(get_val(XB1_LT) ) {
        combo_run(pressing)} 
 
  // Trivela Shot
  if (!get_val(XB1_LT)) {
   if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
    UltimatePower = random(265,270);
    DYN_Acc = random(130,135);
    set_val(XB1_B,0);
    combo_run(OutSideBox_Finishing_cmb);
    }
 }
/*
	// CHIP SHOT
	if (!get_val(XB1_LT)) {
	if(event_press(XB1_RS)){ 
		   vm_tctrl(0);combo_run(CHIP_SHOT);   
	}                       
	set_val(XB1_RS,0); 
	}
*/	
}

combo banks {
    set_val(XB1_A, 100);
    set_val(XB1_RT, 100);
}

combo PressB {
    set_val(XB1_B, 100);
    wait(100);
}

combo TapB {
    set_val(XB1_B, 0);
    wait(250);
}

combo trivela {
    set_val(XB1_B, 100);
    wait(260);
    set_val(XB1_B, 0);
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100); //750
	wait(440);
    set_val(XB1_LB, 100); // Press XB1_LB
    wait(80);             // Wait for 80ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(40);             // Wait for 40ms pause
    set_val(XB1_LB, 100); // Press XB1_LB again
    wait(250);            // Wait for 150ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(250);  
}

combo CHIP_SHOT {
    set_val(XB1_B,100);
    set_val(XB1_RB,100);
    set_val(PS4_L3,100);
    wait( 80);
    set_val(XB1_B,100);
    set_val(XB1_RB,100);
    wait(100);    
}

combo PressB99 {
    set_val(XB1_B, 100);
    set_val(PS4_L3,100);
    wait(100);
}

combo TapB180 {
    set_val(XB1_B, 0);
    set_val(PS4_L3,100);
    wait(180);
}

combo Press_LB {
    set_val(XB1_LB, 100);
    set_val(XB1_RB, 100);
    wait(100);
    wait(20);
}

   combo OutSideBox_Finishing_cmb { 
    set_val(XB1_B, 0);
    set_val(XB1_LT, 0);
    set_val(PS4_L3,0);
    INSIDE_BOX_AIM(37,100);
    wait(160);
    INSIDE_BOX_AIM(37,100);
    set_val(PS4_L3,0);
    set_val(XB1_LT, 100);
    set_val(XB1_B, 100); 
    wait(UltimatePower); ///// 
    INSIDE_BOX_AIM(37,100);
    set_val(XB1_LT, 100);
    set_val(XB1_B, 0);
    /*
    wait(DYN_Acc); 
    INSIDE_BOX_AIM(37,100);
    set_val(XB1_LT, 100);
    set_val(XB1_B, 100);
    wait(60);   // TIMED Action
    INSIDE_BOX_AIM(37,100);
    set_val(XB1_LB,0);
    set_val(XB1_B, 0); 
    set_val(XB1_LT, 100); 
    wait(80);
    INSIDE_BOX_AIM(37,100);
    set_val(XB1_LT, 100);
    wait(900);
    */
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {     
 if(get_ival(PS4_LX) >= 12) AIM_X = f_LX ; 
 else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX) ;  
               
 if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY ;  
 else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}   