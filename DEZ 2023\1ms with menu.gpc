define AdsBtn		  = XB1_LT;
define FireBtn		  = XB1_RT;
define CrouchBtn 	  = XB1_RS;  
define JumpBtn  	  = XB1_LB;
define SprintBtn	  = XB1_LS;
define PingBtn		  = XB1_UP; 

const string Misc[] = { "Title Screen", "On", "Off","Menu Template","Made By","Taylordrift21","KillSwitch","Enabled","Disabled","" };  // Add More Strings for Misc Display Text, Example Add More to the Title Screen Display \\

const string ModNames[] = { "Anti Recoil","Aim Assist", "Auto YY",""};  // This is for the Mod Menu , this is where we display Mods that are added to the script \\
// ModName Number             0       1       2              3

const string ValNames[] = { "Vertical","Horizontal","Radius","Steps","Interval",""}; // This for editable values in the script for Edit Menu, change the values \\
// ValName Number              0      1      2         3        4        5

define AmountOfValues = 5;  // If You Add More Editable Values, You need To Increase The Value Here...

// Mixed Variables Needed For The Script To Function \\ 
int DisplayTitle = TRUE; // Display Title When The Script Loads, This Needs TO Be TRUE. 

int ScreenSaver,BlankScreen,ModMenu,DisplayMenu,ModNameIdx,Toggle,EditMenu,ValNameIdx,KillSwitch; // Menu Variables \\

int AntiRecoil,AimAssist,AutoYY; // Mod Menu/Toggle Variables

int Vertical,Horizontal,Radius,Steps,Interval; // Edit Menu /Editable Variables/Values


define Stick_Press_LB_Threshold = 95;
function icos(x) { return isin(x + 8192); }
function isin(x) {
	x = (x % 32767) << 17;
	if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
	x = x >> 17;
	return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggle_active;
int cos_angle, sin_angle;
	// Increase the frequency of peaks and dips
	int multiplier = 18;
function set_polar_dd(stick, angle, radius, dd_factor){
	dd_factor = (dd_factor * 32767) / 80;
	radius = (radius * 32767) / 10000;
	angle = (360 - angle) * 91;

	sin_angle = isin(angle); 
	cos_angle = icos(angle);


	angle = (angle * multiplier) % 32767;

	// Adjusted radius calculation for more peaks and dips
	radius = radius * (32767 - ((angle * dd_factor) >> 15)) >> 15;

	set_val(42 + stick, clamp((radius * cos_angle) >> 15, -32767, 32767));
	set_val(43 + stick, clamp((radius * sin_angle) >> 15, -32767, 32767));

	return;
}

int r, a, dd;


main {

      /* Enter The Mod Menu */ 
        if(get_ival(AdsBtn))  // AdsBtn , This is so that if you play flipped and define your button above you can still enter the menu \\ 
        {
        	if(event_press(PS4_OPTIONS))
        	{
        		ModMenu     = TRUE;      // Mod Menu Will Activate \\
        		DisplayMenu = TRUE;      // Display The Mod Menu \\  
        		EditMenu    = FALSE;     // Make Sure We Don't Enter Edit Menu when  Entering Mod Menu \\
        		
        		if(!ModMenu)             // If NOT in the Mod Menu, Display Title \\
        	    	DisplayTitle = TRUE; 
        	}
        	if(event_press(PS4_SHARE)) 
        	{ 
        		KillSwitch =! KillSwitch;
        		combo_run(KillSwitch) 
        	} 	
        	set_val(PS4_OPTIONS,0); set_val(PS4_SHARE,0); // We Block these Buttons from being Activated IN GAME \\ 
        }
        if(!DisplayTitle)                // If NOT on The Display Title, we are In Mod Menu Instead \\
        {
	        if(ModMenu)                  // If we are In Mod Menu \\
	        { 
	            // Menu Navigation Is One Function To Navigate The Menu , 0,2 < <  0 is the minimum Mods Available , 2 is the Maximum Mods Available \\
	            
	        	ModNameIdx        = MenuNavigation(ModNameIdx,0,2);   // ModNameIdx = Variable To Identify So The Script knows Which Menu We Are In \\
	        	
	        	// Toggles For Mods On/Off Press Up/Down , ModNameIdx == 0 , < < This is so we know which mod you are turning ON/OFF
	        	
	        	AntiRecoil              = ToggleSwitch(ModNameIdx == 0,AntiRecoil);
	        	AimAssist              = ToggleSwitch(ModNameIdx == 1,AimAssist);
	        	AutoYY              = ToggleSwitch(ModNameIdx == 2,AutoYY);
	        }
	        if(EditMenu)   // We are In Edit Menu \\
	        {
	            // Menu Navigation Is One Function To Navigate The Menu , 0,2 < <  0 is the minimum Editable Values Available , 2 is the Maximum Editble Values Available \\
	            
	        	ValNameIdx        = MenuNavigation(ValNameIdx,0,4); // ValNameIdx = Variable To Identify So The Script knows Which Menu We Are In \\
	        	
	        	// Edit Menu, This is where we Edit Values , 0 , 99 < < 0 Is the Minimum Editble Value , 99 < < Is the Maximum Value for That mod \\
	        	
	        	Vertical              = EditValues(ValNameIdx , 0,Vertical          , 0, 99); 
	            Horizontal             = EditValues(ValNameIdx , 1,Horizontal          , 0, 99); 
	            Radius              = EditValues(ValNameIdx , 2,Radius          , 0, 99);
	            Steps              = EditValues(ValNameIdx , 3,Steps          , 0, 99);
	            Interval              = EditValues(ValNameIdx , 4,Interval          , 0, 99);
	      
	      
	            // Limit Values Per Mods , For example AntiRecoil - Vertical - Horizontal , 0 , 1 which is used with the first ModNameIdx == 0 
	            
	            if(ModNameIdx == 0) ValNameIdx = cycle(ValNameIdx,0,1);
	            if(ModNameIdx == 1) ValNameIdx = cycle(ValNameIdx,2,4);
	        }
	    } 
        if(DisplayMenu) // Display The Menu \\ 
        {
        	cls_oled(OLED_BLACK);  // Clear The OLED Display \\ 
        	rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); // Display The Rectangle Around The Text 
        	line_oled(0, 27, OLED_WIDTH - 1, 27, 1, OLED_WHITE); // Display The Line In Between Text Top/Bottom 
        	
        	if(EditMenu)  // If EditMenu Display The Editable Mods \\ 
        	{
        		printf(center_x(get_valName_chars(ValNameIdx), OLED_FONT_MEDIUM_WIDTH),7,OLED_FONT_MEDIUM,OLED_WHITE,ValNames[ValNameIdx]);
        		
        		// 7 is the position of the text, 7 Pixels from the top of the OLED Screen, Change this value if you want the text lower or higher \\
        		
        		//printf(center_x(get_valName_chars(ValNameIdx), OLED_FONT_SMALL_WIDTH),7,OLED_FONT_SMALL,OLED_WHITE,ValNames[ValNameIdx]); If you want smaller Text \\
        	} 
        	else if(ModMenu) // If Mod Menu Display Mods
        	{
        		printf(center_x(get_modName_chars(ModNameIdx), OLED_FONT_MEDIUM_WIDTH),7,OLED_FONT_MEDIUM,OLED_WHITE,ModNames[ModNameIdx]); // Display ModNames ( Medium Text ) 
        		
        		// 7 is the position of the text, 7 Pixels from the top of the OLED Screen, Change this value if you want the text lower or higher \\
        		
        		//printf(center_x(get_modName_chars(ModNameIdx), OLED_FONT_SMALL_WIDTH),7,OLED_FONT_SMALL,OLED_WHITE,ModNames[ModNameIdx]); // If you want smaller Text = Longer String Names \\
        	
        	    // While In Mod Menu Display On/Off Toggles For Mods \\
        	
        	    if(Toggle == 1) // If Toggle Is ON = Display ON 
        	    
        	         // 37 is the position of the text, 37 Pixels from the top of the OLED Screen, Change this value if you want the text lower or higher \\
        	         
    				printf(center_x(get_misc_chars(1), OLED_FONT_MEDIUM_WIDTH),37,OLED_FONT_MEDIUM,OLED_WHITE,Misc[1]);	// Display ON Medium Text \\ // Misc[1] < This is number string in Misc String List
    				
    				//printf(center_x(get_misc_chars(1), OLED_FONT_SMALL_WIDTH),46,OLED_FONT_SMALL,OLED_WHITE,Misc[1]);	// Display ON Small Text \\ // Misc[1] < This is number string in Misc String List
    				
           		else if(Toggle == 0) // If Toggle Is OFF =  Display OFF 
           		
           			// 37 is the position of the text, 37 Pixels from the top of the OLED Screen, Change this value if you want the text lower or higher \\
           			
            		printf(center_x(get_misc_chars(2), OLED_FONT_MEDIUM_WIDTH),37,OLED_FONT_MEDIUM,OLED_WHITE,Misc[2]);  /// Display OFF Medium Text \\ // Misc[2] < This is number string in Misc String List
            		
            		//printf(center_x(get_misc_chars(2), OLED_FONT_SMALL_WIDTH),46,OLED_FONT_SMALL,OLED_WHITE,Misc[2]); // Display OFF Small Text \\ // Misc[2] < This is number string in Misc String List
        	}
        	Toggle = FALSE; 
            DisplayMenu = FALSE;
            
        } // End Of Display Menu \\

    // Only execute this block if the toggle is active
    if(toggle_active) {
        stickize(POLAR_LX, POLAR_LY, 32767);
        r = get_polar(POLAR_LS, POLAR_RADIUS);
        a = get_polar(POLAR_LS, POLAR_ANGLE);
        dd = 30; // Range -50 < - > 100 
        set_polar_dd(POLAR_LS, a, r, dd);
    }


} // End of Main

// Menu Navigation Function For BOTH Menu's \\    
function MenuNavigation(f_modorval,f_min,f_max) { 


	if(!get_val(AdsBtn))
	{
		if(event_press(PS4_RIGHT))   // Press Right To See Next Mod/Value
		{     
	        f_modorval = cycle(f_modorval + 1, f_min , f_max  );
	        DisplayMenu = TRUE;   
   		}     
		if(event_press(PS4_LEFT))    // Press Left To See Next Mod/Value 
		{  
	        f_modorval = cycle(f_modorval - 1, f_min , f_max  );
	        DisplayMenu = TRUE;
		} 
        set_val(PS4_RIGHT,0); set_val(PS4_LEFT,0);  
	}
	// Example \\
	
	//if(f_modorval <= AmountOfEditableValues)  At the Top Of The Script " define AmountOfEditbleValues = 4 " Example..
	
	if(f_modorval == 0)  // If ModNameIdx == 0  \\ IF You Have Mutiple Mods That Can Be Edited \\ You Could Do This... 
	{
		if(event_press(PS4_CROSS))  // Enter Edit Menu  \\
    	{ 
        	ValNameIdx  = AmountOfValues + 1; // Total Amount Of Values Available In Edit Menu \\ 
        	EditMenu    = TRUE;
        	ModMenu     = FALSE;
        	DisplayMenu = TRUE;
    	}
    	set_val(PS4_CROSS,0); 
    } 
    if(f_modorval == 1)  // If ModNameIdx == 0  \\ IF You Have Mutiple Mods That Can Be Edited \\ You Could Do This... 
	{
		if(event_press(PS4_CROSS))  // Enter Edit Menu  \\
    	{ 
        	ValNameIdx  = AmountOfValues + 1; // Total Amount Of Values Available In Edit Menu \\ 
        	EditMenu    = TRUE;
        	ModMenu     = FALSE;
        	DisplayMenu = TRUE;
    	}
    	set_val(PS4_CROSS,0); 
    }
    if(ModMenu)  // When In Mod Menu  \\
    {
    	if(event_press(PS4_CIRCLE))  // Press Circle To Exit The Menu && Display The Title Screen \\  
    	{    
        	DisplayTitle = TRUE;
        	ModMenu      = FALSE; 
        	DisplayMenu  = FALSE; 
        	combo_run(RumbleOff);     
    	} 
    	set_val(PS4_CIRCLE,0); 
    }
    if(EditMenu) // When In Edit Menu \\
    {
    	if(event_press(PS4_CIRCLE))  // Press Circle To Exit The Edit Menu && Display The Mod Menu \\
    	{       
        	EditMenu     = FALSE; 
       		ModMenu      = TRUE;
        	DisplayMenu  = TRUE;   	
    	}
    	set_val(PS4_CIRCLE,0); 
    } 
    return f_modorval; 
} 
// Toggle Function \\ 
function ToggleSwitch(f_name,f_val) { 
	
    if(f_name) 
    {       
		if(event_press(PS4_UP) || event_press(PS4_DOWN)) // Press UP Or Down TO Turn Mods ON/OFF 
		{
			f_val =! f_val;
			DisplayMenu = TRUE;       // Display The Toggle Change 
			combo_run(ToggleVibrate); // Change Led Colour/Vibrate 
		}
		Toggle = f_val
		set_val(PS4_UP,0); set_val(PS4_DOWN,0); // Block Buttons 
    }
    return f_val;
}
// Editable Values Function, Adjust & Display The Values \\ 
function EditValues(f_idx,f_num,ValueIndex,ValueIndexMin,ValueIndexMax) {  
    if(f_num == f_idx) 
    { 
        if(get_ival(AdsBtn))  // Hold AdsBtn When Changing Values 
        {
            if(event_press(PS4_RIGHT))  // While Holding L2 Press Right To Increse The Value IN Edit Menu 
            {
                ValueIndex = cycle(ValueIndex + 1, ValueIndexMin,ValueIndexMax); 
                DisplayMenu = TRUE;     // Display The Value
            }
            if(event_press(PS4_LEFT))   // While Holding L2 Press Left To Increase The Value IN Edit Menu  
            {
                ValueIndex = cycle(ValueIndex - 1, ValueIndexMin,ValueIndexMax); 
                DisplayMenu = TRUE;     // Display The Value
            }
            set_val(PS4_RIGHT,0); set_val(PS4_LEFT,0); // Block The Buttons In The Menu 
        }
        number_to_string(ValueIndex,find_digits(ValueIndex));  // Display The Number On OLED Display  
    }
    return ValueIndex;                
 } 
// Used Combo's and other Functions In The Script \\
combo KillSwitch { 
cls_oled(OLED_BLACK); 
if(KillSwitch){  
printf(center_x(get_misc_chars(6), OLED_FONT_SMALL_WIDTH),15,OLED_FONT_SMALL,OLED_WHITE,Misc[6]);
printf(center_x(get_misc_chars(7), OLED_FONT_SMALL_WIDTH),30,OLED_FONT_SMALL,OLED_WHITE,Misc[7]);}
if(!KillSwitch){ 
printf(center_x(get_misc_chars(6), OLED_FONT_SMALL_WIDTH),15,OLED_FONT_SMALL,OLED_WHITE,Misc[6]);
printf(center_x(get_misc_chars(8), OLED_FONT_SMALL_WIDTH),30,OLED_FONT_SMALL,OLED_WHITE,Misc[8]);} 
wait(1500);
DisplayTitle = TRUE;
} 
combo ToggleVibrate {
if(Toggle)
set_ledx(2, 2);
set_rumble(RUMBLE_B, 50);
wait(150);
reset_rumble();
wait(100);
set_rumble(RUMBLE_B, 50);
wait(150);
reset_rumble();
reset_leds();
if(!Toggle) 
set_ledx(1, 1);
set_rumble(RUMBLE_A, 50);
wait(300);
reset_rumble();
wait(400);
reset_leds();
}
combo RumbleOff {						
set_ledx(1, 1);
set_rumble(RUMBLE_A, 50);
wait(300);
reset_rumble();
wait(400);
reset_leds();
}


function cycle(f_val, f_lo, f_hi) {
	if(f_val > f_hi) return f_lo;
	if(f_val < f_lo) return f_hi;
	return f_val;
}
function get_misc_chars(f_idx){
	if (Misc[f_idx + 1] != -1) 
		return Misc[f_idx + 1] - Misc[f_idx] - 1;
	return sizeof(Misc) - Misc[f_idx];		
}
function get_modName_chars(f_idx){
	if (ModNames[f_idx + 1] != -1) 
		return ModNames[f_idx + 1] - ModNames[f_idx] - 1;
	return sizeof(ModNames) - ModNames[f_idx];		
}
function get_valName_chars(f_idx){
	if (ValNames[f_idx + 1] != -1) 
		return ValNames[f_idx + 1] - ValNames[f_idx] - 1;
	return sizeof(ValNames) - ValNames[f_idx];		
}
function center_x(f_chars,f_font) {


    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
} 
function number_to_string(value,f_digits){


    i = 1;  c_val = 10000;
    
	if(value < 0) 
	{          
         putc_oled(i,45);    
         i += 1;
         value = abs(value);
	} 
	for(c = 5; c >= 1; c--) 
	{
	    if(f_digits >= c) 
	    {
            putc_oled(i,ASCII_NUM[value / c_val]);
            value = value % c_val;
            i +=  1; 
            if(c == 4) 
            {
                putc_oled(i,44);   
                i += 1;
            }
        }
        c_val /= 10;
    }   
    puts_oled(center_x(i - 1,OLED_FONT_LARGE_WIDTH),37,OLED_FONT_LARGE,i - 1,OLED_WHITE); // Position/Size Of The Number Shown In Edit Menu \\ 
}  
function find_digits(f_num) {
    f_num = abs(f_num);
    if(f_num / 10000 > 0) return 5;
    if(f_num /  1000 > 0) return 4;
    if(f_num /   100 > 0) return 3;
    if(f_num /    10 > 0) return 2;
                          return 1;
}
// Constant bytes - Convert numbers array - ASCII_NUM[column number]
	const uint8 ASCII_NUM[] = 
//	  0  1  2  3  4  5  6  7  8  9  (column numbers)
  	{48,49,50,51,52,53,54,55,56,57};
// Variables for function number_to_string()\\ 
	int i;
	int c,c_val;
data(
//  0 Blue    1 Pink   2 SkyBlue  3 Green         
    1,0,0,0,  0,0,0,1,  1,0,1,0,  0,0,1,0,
//  4 Yellow  5 White   6 Red      7 OFF         
    0,1,1,0,  1,1,1,0,  0,1,0,0,  0,0,0,0 ); 