int pressStartTime = 0;
int isPressed = FALSE;
int LS_Angle2;
int LS_Direction2;

main {
    // Retrieve the current angle of the left stick in polar coordinates
    LS_Angle2 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
    
    // Retrieve the current direction of the left stick and negate it
    LS_Direction2 = get_polar(POLAR_LS, POLAR_ANGLE) * -1;

    if (event_press(XB1_PL1)) {
        pressStartTime = get_rtime();
        isPressed = TRUE;
        combo_run(ButtonPressLT);
    }
    
    if (isPressed && !get_val(XB1_PL1) && (get_rtime() - pressStartTime >= 300)) {
        isPressed = FALSE;
        //set_val(XB1_PL1, 0);
    }
    
    // Handle XB1_PL1 (Left) Button Press
    if (get_val(XB1_PL1)) {
        // Determine if the left stick is pointing to the left or right side
        if (LS_Angle2 > 90 && LS_Angle2 < 270) { // Left side
            set_val(XB1_LX, 0);
            set_val(XB1_LY, 0);
            // Move the right stick to the left relative to the left stick's direction
            set_polar(POLAR_RS, LS_Direction2 + 90, 32767);
        }
        else { // Right side
            set_val(XB1_LX, 0);
            set_val(XB1_LY, 0);
            // Move the right stick to the left relative to the left stick's direction
            set_polar(POLAR_RS, LS_Direction2 - 90, 32767);
        }
    } else {
        // Reset right stick when XB1_PL1 is not pressed
        set_val(XB1_RX, 0);
        set_val(XB1_RY, 0);
    }
}

combo ButtonPressLT {
    set_val(XB1_PL1, 100);
    wait(300);
    // The combo will automatically stop when the button is released
}