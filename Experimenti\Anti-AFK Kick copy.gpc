/*
    Anti-AFK Kick v1.1
    Created By Fadexz
*/

define AFK_Timeout = 10000;
define MIN_WAIT = 2000;
define MAX_WAIT = 4000;
define STICK_THRESHOLD = 1000;

// Random number generator constants
define RANDOM_MULTIPLIER = 1103515245;
define RANDOM_INCREMENT = 12345;
define RANDOM_MODULUS = 2147483647;

int
    timer,
    input_idx,
    random_wait,
    random_seed = 123456789;

// Random number generator functions
function rand() {
    random_seed = (RANDOM_MULTIPLIER * random_seed + RANDOM_INCREMENT) % RANDOM_MODULUS;
    return random_seed;
}

function rand_range(min, max) {
    return min + (abs(rand()) % (max - min + 1));
}

main {
    // -- If either stick is moved with a radius of 10% or more reset the timer
    if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= STICK_THRESHOLD ||
        get_ipolar(POLAR_RS, POLAR_RADIUS) >= STICK_THRESHOLD)
        timer = 0;

    // -- Check all inputs except sticks
    for (input_idx = 1; input_idx <= 20; input_idx++)
    {
        if (input_idx == 9) input_idx = 13; // Skip stick axes
        if (get_ival(input_idx) != get_lval(input_idx))
            timer = 0;
    }

    if (timer < AFK_Timeout)
    {
        timer += get_rtime();
        if (combo_running(Anti_AFK_Kick))
        {
            reset_rumble();
            combo_stop(Anti_AFK_Kick);
        }
    }
    else
    {
        // Only block inputs during combo execution
        if (!combo_running(Anti_AFK_Kick))
        {
            combo_run(Anti_AFK_Kick);
        }
    }
}

combo Anti_AFK_Kick {
    // Generate random wait time between MIN_WAIT and MAX_WAIT
    random_wait = rand_range(MIN_WAIT, MAX_WAIT);
    
    // More natural looking movement pattern
    set_val(XB1_LT, rand_range(50, 100)); // 50-100
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_LX, rand_range(-100, 100)); // -100 to 100
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_LS, 100);
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_RY, rand_range(-100, 100)); // -100 to 100
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_LT, rand_range(50, 100));
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_LY, rand_range(-100, 100)); // -100 to 100
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_LS, 100);
    wait(rand_range(20, 50));
    wait(random_wait);
    
    set_val(XB1_RX, rand_range(-100, 100)); // -100 to 100
    wait(rand_range(20, 50));
    wait(random_wait);
    
    // Allow brief user input between cycles
    block_all_inputs();
    wait(rand_range(500, 1000));
    block_all_inputs();
}