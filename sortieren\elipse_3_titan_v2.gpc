#include <titanone.gph>
#pragma METAINFO("Ellipse Movement V2", 1, 0, "Assistant")

// Constants for ellipse shape
define X_SCALE = 100;  // X axis scale (percentage)
define Y_SCALE = 40;   // Y axis scale (percentage)
define MIN_MOVE = 3000;

// Global variables
int stick_x, stick_y;
int out_x, out_y;
int current_angle;

// Fast trigonometry functions using integer math
function fast_sin(angle) {
    return isin(angle);
}

function fast_cos(angle) {
    return icos(angle);
}

init {
    // Initialize virtual mode
    vm_tctrl(-2);
}

main {
    // Get stick input
    stick_x = get_val(STICK_1_X);
    stick_y = get_val(STICK_1_Y);
    
    // Check if stick is moved enough
    if(abs(stick_x) > MIN_MOVE || abs(stick_y) > MIN_MOVE) {
        // Calculate magnitude
        int magnitude = abs(stick_x);
        if(abs(stick_y) > magnitude) magnitude = abs(stick_y);
        
        // Calculate ellipse position
        out_x = (magnitude * fast_cos(current_angle) * X_SCALE) / 100;
        out_y = (magnitude * fast_sin(current_angle) * Y_SCALE) / 100;
        
        // Apply to stick
        set_val(STICK_1_X, out_x);
        set_val(STICK_1_Y, out_y);
        
        // Update angle for next frame (using integer degrees)
        current_angle = (current_angle + 5) % 360;
    } else {
        // Pass through small movements
        set_val(STICK_1_X, stick_x);
        set_val(STICK_1_Y, stick_y);
    }
}





