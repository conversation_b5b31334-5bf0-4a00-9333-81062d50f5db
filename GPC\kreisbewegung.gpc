

//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_R1; // Finesse Shot
define PlayerRun     = PS4_L1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY; 

///////////////////////////////////////////////
//VARIABLES
int LeftStickSens = 90;
int CrossDribbleSens = 120;

//////////////////////////////////////////////////////////////////
// variables 
int ShootPower    = 225; // Shot Power  

init {
     ShootPower = 225;  
    colorled(0,0,2,0);   //  clLime
}
main{

	/////////////////////////////////////////////////////////////////////
    //   Shot Power Switch                                                            
    if ( get_val(XB1_LB)) {                                                               
          if(event_press(XB1_UP))    {ShootPower = 225;  colorled(0,0,2,0); }   //  clLime
          if(event_press(XB1_RIGHT)) {ShootPower = 270;  colorled(0,2,0,0); }   //  clRed
          if(event_press(XB1_DOWN))  {ShootPower = 300;  colorled(0,0,0,2); }   //  clFuchsia
          if(event_press(XB1_LEFT))  {ShootPower = 350;  colorled(0,2,2,0);}   //  clSkyBlue
          set_val(13,0);                  
          set_val(14,0);                 
          set_val(15,0);                
          set_val(16,0);                 
                                         
                                                               
     }
     
    //----------------------------------------------------------------------------- 
    //  Finesse Power Restriction                                                      
	if (get_val(FinesseShot) ) {                                                     
    	  if(get_val(ShotBtn) && get_ptime(ShotBtn) > 220 )
    		set_val(ShotBtn,0);                           
    }             
    
    if(get_val(ShotBtn)) set_val(SprintBtn,0);          
    //----------------------------------------------------------------------------- 
    //                                                                               
    if(!get_val(FinesseShot) && get_val(ShotBtn) && get_ptime(ShotBtn)>= ShootPower) {  // Shot Power        
              set_val(ShotBtn,  0);                                               
    }                            

	if(get_val(XB1_PL1)){
	
		sensitivity(PS4_LX,50,LeftStickSens);
		sensitivity(PS4_LY,50,LeftStickSens);
	}
	if(get_val(XB1_PR1)){
	
		sensitivity(PS4_LX,50,CrossDribbleSens);
		sensitivity(PS4_LY,50,CrossDribbleSens);
	}
	
	if(get_val(ShotBtn)){
		CORNER ();
	}

}
function CORNER () {    
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    { 
    	set_val(PS4_LX, 100); 
		set_val(PS4_LY, -50); 
    }
      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{ 
		set_val(PS4_LX, 100); 
		set_val(PS4_LY, 50); 
	}
  
 
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {
	    set_val(PS4_LY, -100); 
	    set_val(PS4_LX, -50); 
    }
    
   if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
   { 
		set_val(PS4_LY, 100);  
        set_val(PS4_LX, -50); 
   }
  
}
function colorled(a,b,c,d) {                         
 set_led(LED_1,a);                                    
 set_led(LED_2,b);                                    
 set_led(LED_3,c);                                    
 set_led(LED_4,d);                                    
 }// func end     