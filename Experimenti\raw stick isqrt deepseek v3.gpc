define MAG_THRESHOLD = 17 * 32767 / 100;
define MAG_TARGET    = 32767;
int currentRX;
int currentRY;
int vectorMagnitude;
int scaledRX;
int scaledRY;
main {
    currentRX = get_val(POLAR_RX);
    currentRY = get_val(POLAR_RY);
    vectorMagnitude = isqrt(currentRX * currentRX + currentRY * currentRY);
    if(vectorMagnitude >= MAG_THRESHOLD) {
        scaledRX = (currentRX * MAG_TARGET) / vectorMagnitude;
        scaledRY = (currentRY * MAG_TARGET) / vectorMagnitude;
        set_val(POLAR_RX, scaledRX);
        set_val(POLAR_RY, scaledRY);
    }
}