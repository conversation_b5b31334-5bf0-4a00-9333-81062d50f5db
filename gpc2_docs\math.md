# Math
abs
abs - Absolute value

Description
int32 abs(int32 num);
fix32 abs(fix32 num);
Returns the absolute value of num.

Parameters
num: The numeric value to process.
Return Value
The absolute value of num. If the argument number is of type fix32, the return type is also fix32, otherwise it is integer.

Examples
Example #1 abs() example
init {
    printf("%.1f", abs(-4.2)); // 4.2 (fix32)
    printf("%d", abs(5));      // 5 (integer)
    printf("%d", abs(-5));     // 5 (integer)
}

acos
acos — Arc cosine

Description
fix32 acos(fix32 num);
Returns the arc cosine of num in radians. acos() is the complementary function of cos(), which means that
num == cos(acos(num)).

Parameters
num: The number to process.
Return Value
The arc cosine of num in radians.

Examples
Example #1 acos() example
init {
    printf("%.6f", acos(0.5)); // ~1.0471975512
}

asin
asin — Arc sine

Description
fix32 asin(fix32 num);
Returns the arc sine of num in radians. asin() is the complementary function of sin(), which means that
num == sin(asin(num)).

Parameters
num: The number to process.
Return Value
The arc sine of num in radians.

Examples
Example #1 asin() example
init {
    printf("%.6f", asin(0.5)); // ~0.523598775598
}

atan
atan — Arc tangent

Description
fix32 atan(fix32 num);
Returns the arc tangent of num in radians. atan() is the complementary function of tan(), which means that
num == tan(atan(num)).

Parameters
num: The number to process.
Return Value
The arc tangent of num in radians.

Examples
Example #1 atan() example
init {
    printf("%.6f", atan(0.5)); // ~0.463647609001
}

atan2
atan2 — Arc tangent of two variables

Description
fix32 atan2(fix32 y, fix32 x);
This function calculates the arc tangent of the two variables x and y. It is similar to calculating the arc tangent of y / x, except that the signs of both arguments are used to determine the quadrant of the result.

The function returns the result in radians, which is between -PI and PI (inclusive).

Parameters
y: Dividend parameter.
x: Divisor parameter.
Return Value
The arc tangent of y / x in radians.

Examples
Example #1 atan2() example
init {
    printf("%.6f", atan2(1.0, 2.0)); // ~0.463647609001
}

ceil
ceil — Round fractions up

Description
fix32 ceil(fix32 num);
Returns the next highest integer value (as fix32) by rounding up num if necessary.

Parameters
num: The numeric value to round.
Return Value
Returns num rounded to the next highest integer. The return value of floor() is still of type fix32 because the value range of fix32 is usually bigger than that of integer.

Examples
Example #1 ceil() example
init {
    printf("%.2f", ceil(4.3));   //  5.00
    printf("%.2f", ceil(9.999)); // 10.00
    printf("%.2f", ceil(-3.14)); // -3.00
}

clamp
clamp - Restricts a value to be within a specified range

Description
int32 clamp(int32 num, int32 lo, int32 hi);
fix32 clamp(fix32 num, fix32 lo, fix32 hi);
Computes the value of num clamped to the range defined by lo and hi.

Parameters
num: The value to clamp.
lo: The minimum value. If num is less than lo, lo will be returned.
hi: The maximum value. If num is greater than hi, hi will be returned.
Return Value
The clamped value of num.

Examples
Example #1 clamp() example
init {
    printf("%d", clamp(55, 1, 10));        // 10
    printf("%d", clamp(-15, 1, 10));       // 1
    printf("%.1f", clamp(4.5, 1.0, 10.0)); // 4.5
}

cos
cos — Cosine

Description
fix32 cos(fix32 rad);
Returns the cosine of the rad parameter. The rad parameter is in radians.

Parameters
rad: A value in radians.
Return Value
The cosine of rad.

Examples
Example #1 cos() example
init {
    printf("%.6f", cos(PI)); // ~(-1)
}

deg2rad
deg2rad — Converts the number in degrees to the radian equivalent

Description
fix32 deg2rad(fix32 deg);
This function converts deg from degrees to the radian equivalent.

Parameters
deg: Angular value in degrees.
Return Value
The radian equivalent of deg.

Examples
Example #1 deg2rad() example
init {
    printf("%.6f", deg2rad(45.0)); // ~0.785398163397
}

exp
exp — Calculates the exponent of e

Description
fix32 exp(fix32 num);
Returns e raised to the power of num.

Note: e is the base of the natural system of logarithms, approximately 2.718282
Parameters
num: The number to process.
Return Value
e raised to the power of num.

Examples
Example #1 exp() example
init {
    printf("%.6f", exp(0.5)); // ~1.6487212707
    printf("%.6f", exp(5.7)); // ~298.87
}

floor
floor — Round fractions down

Description
fix32 floor(fix32 num);
Returns the next lowest integer value (as fix32) by rounding down num if necessary.

Parameters
num: The numeric value to round.
Return Value
Returns num rounded to the next lowest integer. The return value of floor() is still of type fix32 because the value range of fix32 is usually bigger than that of integer.

Examples
Example #1 floor() example
init {
    printf("%.2f", floor(4.3));   // 4.00
    printf("%.2f", floor(9.999)); // 9.00
    printf("%.2f", floor(-3.14)); //-4.00
}

inv
inv - Inverse value

Description
int32 inv(int32 num);
fix32 inv(fix32 num);
Returns the inverse value of num.

Parameters
num: The numeric value to process.
Return Value
The inverse value of num. If the argument number is of type fix32, the return type is also fix32, otherwise it is integer.

Examples
Example #1 inv() example
init {
    printf("%.1f", inv(-4.2)); // 4.2 (fix32)
    printf("%d", inv(5));      // -5 (integer)
    printf("%d", inv(-5));     // 5 (integer)
}

lerp
lerp — Performs a linear interpolation

Description
fix32 lerp(fix32 p0, fix32 p1, fix32 fract);
Returns the linear interpolation of p0 and p1 based on fract.
(p0 * (1 - fract)) + (p1 * fract)

Parameters
p0: The first fix32 point value.
p1: The second fix32 point value.
fract: A value between 0.0 and 1.0 that linearly interpolates between p0 and p1.
Return Value
The result of the linear interpolation.

Examples
Example #1 lerp() example
init {
    printf("%.2f", lerp(1.0, 10.0, 0.5)); // 5.5
}

log
log — Natural logarithm

Description
fix32 log(fix32 num);
Returns the natural logarithm of num.

Parameters
num: The value to calculate the logarithm for.
Return Value
The natural logarithm of num.

Examples
Example #1 log() example
init {
    printf("%.6f", log(1.648721)); // ~0.5
    printf("%.6f", log(298.87));   // ~5.7
}

log2
log2 — Binary logarithm (base-2)

Description
fix32 log2(fix32 num);
Computes the base 2 logarithm of num.

Parameters
num: Value whose logarithm is calculated.
Return Value
The binary logarithm of num.

Examples
Example #1 log2() example
init {
    printf("%.6f", log2(1024.0)); // 10.000000
    printf("%.6f", log2(0.125));  // -3.000000
}

max
max — Find highest value

Description
int32 max(int32 numA, int32 numB);
fix32 max(fix32 numA, fix32 numB);
Returns the largest of numA and numB. If both are equivalent, numA is returned.

Parameters
numA, numB: Values to compare.
Return Value
The largest of the values passed as arguments.

Examples
Example #1 max() example
init {
    printf("%d", max(1, 2));         // 2
    printf("%d", max(2, 1));         // 2
    printf("%c", max('a', 'z'));     // z
    printf("%.2f", max(3.14, 2.72)); // 3.14
}

min
min — Find lowest value

Description
int32 min(int32 numA, int32 numB);
fix32 min(fix32 numA, fix32 numB);
Returns the smallest of numA and numB. If both are equivalent, numA is returned.

Parameters
numA, numB: Values to compare.
Return Value
The lesser of the values passed as arguments.

Examples
Example #1 min() example
init {
    printf("%d", min(1, 2));         // 1
    printf("%d", min(2, 1));         // 1
    printf("%c", min('a', 'z'));     // a
    printf("%.2f", min(3.14, 2.72)); // 2.72
}

mod
mod — Remainder of a fix32 division

Description
fix32 mod(fix32 num, fix32 div);
Returns the fix32 remainder of dividing the dividend num by the divisor div.

Parameters
num: The dividend.
div: The divisor.
Return Value
The fix32 remainder of num / div.

Examples
Example #1 mod() example
init {
    printf("%.1f", mod(5.7, 1.3)); // 0.5, because 4 * 1.3 + 0.5 = 5.7
}

pow
pow — Raise to power

Description
int32 pow(int32 numA, uint32 numB);
fix32 pow(fix32 numA, fix32 numB);
Computes the value of numA raised to the power numB, numAnumB.

Parameters
numA: The base value.
numB: The power value.
Return Value
The result of raising numA to the power numB.

Examples
Example #1 pow() example
init {
    printf("%d", pow(2, 24)); // 16,777,216
    printf("%.6f", pow(3.05, 1.98)); // ~9.09732441014
}

rad2deg
rad2deg — Converts the radian number to the equivalent number in degrees

Description
fix32 rad2deg(fix32 rad);
This function converts rad from radian to degrees.

Parameters
rad: A radian value.
Return Value
The equivalent of rad in degrees.

Examples
Example #1 rad2deg() example
init {
    printf("%.6f", rad2deg(PI / 4.0)); // ~45
}

rand
rand - Generate a random number

Description
fix32 rand();
Generate a random decimal number between 0.0 and 1.0.

Return Value
Returns a pseudo-random fix32 between 0.0 and 1.0.

Examples
Example #1 abs() example
init {
    printf("%.6f", rand()); // Random number between 0.0 and 1.0
}
Example #2 int16 example
init {
    printf("%d", irand(200,500)); // Random number between 200 and 500
}
 
int16 irand(int16 vmin, int16 vmax) {
    return(((int16)(rand() * (fix32)(vmax + 1 - vmin))) + vmin);
}

round
round — Rounds a fix32 number

Description
fix32 round(fix32 num);
Returns the rounded value of num to the closest integer (as fix32).

Parameters
num: The numeric value to round.
Return Value
The rounded value. The return value of round() is still of type fix32 because the value range of fix32 is usually bigger than that of integer.

Examples
Example #1 round() example
init {
    printf("%.2f", round(3.4));  // 3.00
    printf("%.2f", round(3.5));  // 4.00
    printf("%.2f", round(3.6));  // 4.00
    printf("%.2f", round(-3.4)); //-3.00
}

sin
sin — Sine

Description
fix32 sin(fix32 rad);
Returns the sine of the rad parameter. The rad parameter is in radians.

Parameters
rad: A value in radians.
Return Value
The sine of rad.

Examples
Example #1 sin() example
init {
    printf("%.6f", sin(45.0));          // ~0.850903524534
    printf("%.6f", sin(deg2rad(45.0))); // ~0.707106781187
}

sq
sq — Square

Description
fix32 sq(fix32 num);
Returns the square of num (num²).
num * num

Parameters
num: The number to process.
Return Value
The square of num.

Examples
Example #1 sq() example
init {
    printf("%.6f", sq(3.0));      //   9.0
    printf("%.6f", sq(3.162277)); // ~10.0
}

sqrt
sqrt — Square root

Description
fix32 sqrt(fix32 num);
Returns the square root of num.

Parameters
num: The number to process.
Return Value
The square root of num.

Examples
Example #1 sqrt() example
init {
    printf("%.6f", sqrt(9.0));  // 3.000000
    printf("%.6f", sqrt(10.0)); // 3.162277
}

tan
tan — Tangent

Description
fix32 tan(fix32 rad);
Returns the tangent of the rad parameter. The rad parameter is in radians.

Parameters
rad: A value in radians.
Return Value
The tangent of rad.

Examples
Example #1 tan() example
init {
    printf("%.6f", tan(PI / 4.0)); // ~1
}