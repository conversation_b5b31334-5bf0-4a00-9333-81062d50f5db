[![Website logo](https://archbee-image-uploads.s3.amazonaws.com/IwUlH55FdaPsVELSqm1Fc/nuSw6YqerwC7liEa8mRlX_cronuslogodark.png)](https://guide.cronus.support/gpc/<https:/beta.cronusmax.com>)
[Cronus Shop](https://guide.cronus.support/gpc/<https:/shop.cronusmax.com>)[GamePacks](https://guide.cronus.support/gpc/<https:/cronus.support/gamepacks>)[Forums](https://guide.cronus.support/gpc/<https:/cronus.support/forums>)[Discord](https://guide.cronus.support/gpc/<https:/cronus.support/discord>)[YouTube](https://guide.cronus.support/gpc/<https:/cronus.support/youtube>)[Cronus ZEN Marketplace](https://guide.cronus.support/gpc/<https:/marketplace.cmindapi.com/>)
📘Cronus Zen Guide
📗GamePacks
📗GPC Script Guide
GPC Developer Guides
# Basic Syntax
This is basic syntax of a .gpc script:
GPC
1define Cronus = 120; 2int ZEN = 100; 3 4main { 5 if (get_val(XB1_A)) { 6 combo_run(jump); 7 } 8} 9 10combo jump { 11 set_val(XB1_A, 100); 12 wait(Cronus); 13 set_val(XB1_A, 0); 14 wait(ZEN); 15}
define Cronus = 120; int ZEN = 100; main { if (get_val(XB1_A)) { combo_run(jump); } } combo jump { set_val(XB1_A, 100); wait(Cronus); set_val(XB1_A, 0); wait(ZEN); }
﻿
# Instruction Separation[](https://guide.cronus.support/gpc/<#9wQau>)
﻿
As in C, GPC requires instructions to be terminated with a semicolon at the end of each statement. However, the closing tag of a block code automatically implies a semicolon, so a semicolon is not needed when terminating the last line of a GPC block.
GPC
1main { 2 3 sensitivity(XB1_LY, NOT_USE, 80); 4 a = b * ( c + 20 ) 5 6}
main { sensitivity(XB1_LY, NOT_USE, 80); a = b * ( c + 20 ) }
﻿
Although the semicolon is not required in the final line of a block, it is considered good practice to use one so it is not missed should you expand on the code at a later date.
# Nesting Code[](https://guide.cronus.support/gpc/<#HNJ5B>)
﻿
Nesting code, or creating a logic block, binds code together. A Block starts with a { and end with a }. What this does is nest the code with the { and } meaning that the code is only executed when the statement before it is active.
GPC
1main 2{ //Main Start 3 if(get_val(PS4_R2)) 4 { //Block 1 Start 5 if(get_val(PS4_L2)) 6 { //Block 2 Start 7 combo_run(RAPID_FIRE_ADS); 8 } //Block 2 End 9 else 10 { //Block 3 Start 11 combo_run(RAPID_FIRE); 12 } //Block 3 End 13 } //Block 1 End 14} //Main End
main { //Main Start if(get_val(PS4_R2)) { //Block 1 Start if(get_val(PS4_L2)) { //Block 2 Start combo_run(RAPID_FIRE_ADS); } //Block 2 End else { //Block 3 Start combo_run(RAPID_FIRE); } //Block 3 End } //Block 1 End } //Main End
﻿
In this example, Blocks 2 & 3 are ignored unless Block 1 is active. 
So if the R2 button is not pressed, nothing happens. If R2 is pressed, then the Cronus Zen looks at Block 2. If L2 is pressed, it will run the combo RAPID_FIRE_ADS and ignore Block 3. However, if L2 is not pressed, it will ignore Block 2 and instead execute the code in Block 3 and then run combo RAPID_FIRE.
Nesting is implied if you only have one line of code after a statement. As in this example;
GPC
1main { 2 if(get_val(XB1_RT) > 95) 3 combo_run(RAPID_FIRE); 4}
main { if(get_val(XB1_RT) > 95) combo_run(RAPID_FIRE); }
﻿
When compiled, the line combo_run(RAPID_FIRE); will automatically be nested within the if statement. If you wish for more than one line of code to only be executed when the statement before them is active, then you must use { and }.
# Commenting Code[](https://guide.cronus.support/gpc/<#Ee7UB>)
﻿
A comment is text which is ignored by the compiler. Comments are usually used to annotate code for future reference or to add notes for others looking at the code. However, they can also be used to make certain lines of code inactive to aid when debugging scripts. If you have programmed in C before then GPC comments will be familiar to you as it uses the same style.
There are two types of comments, the Single Line Comment and the Multi Line Comment.
## Single Line Comment[](https://guide.cronus.support/gpc/<#22bcL>)
﻿
The // (two slashes) characters create a single line comment and can be followed by any sequence of characters. A new line terminates this form of comment. As shown below
GPC
1main { 2 // A single line comment 3 if(get_val(XB1_RT) > 95) 4 // Another single line comment 5 combo_run(RAPID_FIRE); 6}
main { // A single line comment if(get_val(XB1_RT) > 95) // Another single line comment combo_run(RAPID_FIRE); }
﻿
## Multi Line Comment[](https://guide.cronus.support/gpc/<#zXtcC>)
﻿
The /* (slash, asterisk) characters start a multi-line comment and can also be followed by any sequence of characters. The multi-line comment terminates when the first */ (asterisk, slash) is found. As shown below:
GPC
1main { 2 /* A multi line comment 3 if(get_val(XB1_RT) > 95) 4 combo_run(RAPID_FIRE); 5 */ 6}
main { /* A multi line comment if(get_val(XB1_RT) > 95) combo_run(RAPID_FIRE); */ }
﻿
As the comment terminates when a */ (asterisk, slash) is found, this style of commenting cannot be nested. As shown below
GPC
1main { 2 /* A multi line comment 3 if(get_val(XB1_RT) > 95) 4 combo_run(RAPID_FIRE); /*This will cause a problem*/ 5 */ 6}
main { /* A multi line comment if(get_val(XB1_RT) > 95) combo_run(RAPID_FIRE); /*This will cause a problem*/ */ }
﻿
﻿
[PREVIOUSNew 32-bit Updates](https://guide.cronus.support/gpc/</gpc/new-32bit-updates> "New 32-bit Updates")[NEXTBasic GPC Structure](https://guide.cronus.support/gpc/</gpc/basic-gpc-structure-with-gpc-scripting> "Basic GPC Structure")
[Docs powered by Archbee](https://guide.cronus.support/gpc/<https:/www.archbee.com/?utm_campaign=hosted-docs&utm_medium=referral&utm_source=guide.cronus.support>)
TABLE OF CONTENTS
[Instruction Separation](https://guide.cronus.support/gpc/<#9wQau> "Instruction Separation")
[Nesting Code](https://guide.cronus.support/gpc/<#HNJ5B> "Nesting Code")
[Commenting Code](https://guide.cronus.support/gpc/<#Ee7UB> "Commenting Code")
[Single Line Comment](https://guide.cronus.support/gpc/<#22bcL> "Single Line Comment")
[Multi Line Comment](https://guide.cronus.support/gpc/<#zXtcC> "Multi Line Comment")
[Docs powered by Archbee](https://guide.cronus.support/gpc/<https:/www.archbee.com/?utm_campaign=hosted-docs&utm_medium=referral&utm_source=guide.cronus.support>)
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
