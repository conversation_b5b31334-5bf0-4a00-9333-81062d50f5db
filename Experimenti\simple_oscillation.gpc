// Simple oscillation test script
int current_angle = 0;
int update_time = 0;
int stick_radius;

main {
    // Get current stick position
    stick_radius = get_polar(POLAR_LS, POLAR_RADIUS);

    // Only apply oscillation when stick is moved
    if (stick_radius > 1000) {
        // Update angle every 50ms
        if (get_rtime() - update_time >= 50) {
            // Oscillate between -45 and 45 degrees
            if (current_angle >= 45) {
                current_angle = -45;
            } else {
                current_angle = 45;
            }
            update_time = get_rtime();
        }

        // Apply the oscillating angle to the stick
        set_polar(POLAR_LS, current_angle, stick_radius);
    }
}
