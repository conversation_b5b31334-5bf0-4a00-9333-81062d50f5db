																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/










































































 int T_OTS191[0]; init { 	T_OTS115(); 	combo_run(T_OTS1); 	combo_run(T_OTS2); 	combo_run(T_OTS3); 	combo_run(T_OTS4); 	combo_run(T_OTS5); 	combo_run(T_OTS6); 	combo_run(T_OTS7); 	combo_run(T_OTS8); 	combo_run(T_OTS9); 	combo_run(T_OTS10); 	combo_run(T_OTS11); 	combo_run(T_OTS12); 	combo_run(T_OTS13); 	combo_run(T_OTS14); 	combo_run(T_OTS15); 	combo_run(T_OTS16); 	combo_run(T_OTS17); 	combo_run(T_OTS18); 	combo_run(T_OTS19); 	combo_run(T_OTS20); 	combo_run(T_OTS21); 	combo_run(T_OTS22); 	combo_run(T_OTS23); 	combo_run(T_OTS24); 	combo_run(T_OTS25); 	combo_run(T_OTS26); 	combo_run(T_OTS27); 	combo_run(T_OTS28); 	combo_run(T_OTS29); 	combo_run(T_OTS30); 	combo_run(T_OTS31); 	combo_run(T_OTS32); 	combo_run(T_OTS33); 	combo_run(T_OTS34); 	combo_run(T_OTS35); 	combo_run(T_OTS36); 	combo_run(T_OTS37); 	combo_run(T_OTS38); 	combo_run(T_OTS39); 	combo_run(T_OTS40); 	combo_run(T_OTS41); 	combo_run(T_OTS42); 	combo_run(T_OTS43); 	combo_run(T_OTS44); 	combo_run(T_OTS45); 	combo_run(T_OTS46); 	combo_run(T_OTS47); 	combo_run(T_OTS48); 	combo_run(T_OTS49); 	combo_run(T_OTS50); 	combo_run(T_OTS51); 	combo_run(T_OTS52); 	combo_run(T_OTS53); 	combo_run(T_OTS54); 	combo_run(T_OTS55); 	combo_run(T_OTS56); 	combo_run(T_OTS57); 	combo_run(T_OTS58); 	combo_run(T_OTS59); 	combo_run(T_OTS60); 	combo_run(T_OTS61); 	combo_run(T_OTS62); 	combo_run(T_OTS63); 	combo_run(T_OTS64); 	combo_run(T_OTS65); 	combo_run(T_OTS66); 	combo_run(T_OTS67); 	combo_run(T_OTS68); 	combo_run(T_OTS69); 	combo_run(T_OTS70); 	combo_stop(T_OTS1); 	combo_stop(T_OTS2); 	combo_stop(T_OTS3); 	combo_stop(T_OTS4); 	combo_stop(T_OTS5); 	combo_stop(T_OTS6); 	combo_stop(T_OTS7); 	combo_stop(T_OTS8); 	combo_stop(T_OTS9); 	combo_stop(T_OTS10); 	combo_stop(T_OTS11); 	combo_stop(T_OTS12); 	combo_stop(T_OTS13); 	combo_stop(T_OTS14); 	combo_stop(T_OTS15); 	combo_stop(T_OTS16); 	combo_stop(T_OTS17); 	combo_stop(T_OTS18); 	combo_stop(T_OTS19); 	combo_stop(T_OTS20); 	combo_stop(T_OTS21); 	combo_stop(T_OTS22); 	combo_stop(T_OTS23); 	combo_stop(T_OTS24); 	combo_stop(T_OTS25); 	combo_stop(T_OTS26); 	combo_stop(T_OTS27); 	combo_stop(T_OTS28); 	combo_stop(T_OTS29); 	combo_stop(T_OTS30); 	combo_stop(T_OTS31); 	combo_stop(T_OTS32); 	combo_stop(T_OTS33); 	combo_stop(T_OTS34); 	combo_stop(T_OTS35); 	combo_stop(T_OTS36); 	combo_stop(T_OTS37); 	combo_stop(T_OTS38); 	combo_stop(T_OTS39); 	combo_stop(T_OTS40); 	combo_stop(T_OTS41); 	combo_stop(T_OTS42); 	combo_stop(T_OTS43); 	combo_stop(T_OTS44); 	combo_stop(T_OTS45); 	combo_stop(T_OTS46); 	combo_stop(T_OTS47); 	combo_stop(T_OTS48); 	combo_stop(T_OTS49); 	combo_stop(T_OTS50); 	combo_stop(T_OTS51); 	combo_stop(T_OTS52); 	combo_stop(T_OTS53); 	combo_stop(T_OTS54); 	combo_stop(T_OTS55); 	combo_stop(T_OTS56); 	combo_stop(T_OTS57); 	combo_stop(T_OTS58); 	combo_stop(T_OTS59); 	combo_stop(T_OTS60); 	combo_stop(T_OTS61); 	combo_stop(T_OTS62); 	combo_stop(T_OTS63); 	combo_stop(T_OTS64); 	combo_stop(T_OTS65); 	combo_stop(T_OTS66); 	combo_stop(T_OTS67); 	combo_stop(T_OTS68); 	combo_stop(T_OTS69); 	combo_stop(T_OTS70); 	combo_run(T_OTS107); } int T_OTS264 ; int T_OTS265; int T_OTS266; int T_OTS267; int T_OTS268; define T_OTS269 = 0; define T_OTS270 = 1; define T_OTS271 = 2; define T_OTS272 = 3; define T_OTS273 = 4; define T_OTS274 = 5; define T_OTS275 = 6; define T_OTS276 = 7; define T_OTS277 = 8; define T_OTS278 = 9; define T_OTS279 = 10; define T_OTS280 = 11; define T_OTS281 = 12; define T_OTS282 = 13; define T_OTS283 = 14; define T_OTS284 = 15; define T_OTS285 = 16; define T_OTS286 = 17; define T_OTS287 = 18; define T_OTS288 = 19; define T_OTS289 = 20; define T_OTS290 = 21; define T_OTS291 = 22; define T_OTS23 = 23; define T_OTS293 = 24; define T_OTS294 = 25; define T_OTS295 = 26; define T_OTS296 = 27; define T_OTS297 = 28; define T_OTS298 = 29; define T_OTS299 = 30; define T_OTS300 = 31; define T_OTS301 = 32; define T_OTS302 = 33; define T_OTS303 = 34; define T_OTS304 = 35; define T_OTS305 = 36; define T_OTS306 = 37; define T_OTS307 = 38; define T_OTS308 = 39; define T_OTS309 = 40; define T_OTS310 = 41; define T_OTS311 = 42; define T_OTS312 = 43; define T_OTS313 = 44; define T_OTS314 = 45; define T_OTS315 = 46; define T_OTS316 = 47; define T_OTS317 = 48; define T_OTS318 = 49; define T_OTS319 = 50; define T_OTS320 = 51; define T_OTS321 = 52; define T_OTS322 = 53; define T_OTS323 = 54; define T_OTS324 = 55; define T_OTS325 = 56; define T_OTS326 = 57; define T_OTS327 = 58; define T_OTS328 = 59; define T_OTS329 = 60; define T_OTS330 = 61; define T_OTS331 = 62; define T_OTS332 = 63; define T_OTS333 = 64; define T_OTS334 = 65; define T_OTS335 = 66; define T_OTS336 = 67; define T_OTS337 = 68; define T_OTS338 = 69; define T_OTS339 = 70; define T_OTS340 = 0; function T_OTS109(T_OTS110) { 	if (T_OTS110 == 0) vm_tctrl(-0); 	else if (T_OTS110 == 1) vm_tctrl(-1); 	else if (T_OTS110 == 2) vm_tctrl(-2); 	else if (T_OTS110 == 3) vm_tctrl(-3); 	else if (T_OTS110 == 4) vm_tctrl(-4); 	else if (T_OTS110 == 5) vm_tctrl(-5); 	else if (T_OTS110 == 6) vm_tctrl(-6); 	else if (T_OTS110 == 7) vm_tctrl(-7); 	else if (T_OTS110 == 8) vm_tctrl(-8); 	else if (T_OTS110 == 9) vm_tctrl(-9); } int T_OTS341, T_OTS342; int T_OTS343, T_OTS344; int T_OTS345 = FALSE, T_OTS346; int T_OTS347 = TRUE; int T_OTS348; const string T_OTS791[] = { 	"Off",  "On" } ; int T_OTS349; int T_OTS350; int T_OTS351; int T_OTS352; int T_OTS353; int T_OTS354; int T_OTS355; int T_OTS356; int T_OTS357; int T_OTS358; int T_OTS359; int T_OTS360; int T_OTS361; int T_OTS362; int T_OTS363; int T_OTS364; int T_OTS365; int T_OTS366; int T_OTS367; int T_OTS368; int T_OTS110; int T_OTS370; int T_OTS371 ; int T_OTS372 ; int T_OTS373 ; define T_OTS374 = 24; int T_OTS375; int T_OTS376; int T_OTS377; int T_OTS378; int T_OTS379; int T_OTS380; int T_OTS381; int T_OTS382; int T_OTS383; int T_OTS384 ; int T_OTS385 ; int T_OTS386 ; int T_OTS387 ; int T_OTS388 ; int T_OTS389 ; int T_OTS390 ; int T_OTS391 ; int T_OTS392 ; int T_OTS393 ; int T_OTS394 ; int T_OTS395 ; int T_OTS396; int T_OTS397; int T_OTS398; int T_OTS399; int T_OTS400; int T_OTS401; int T_OTS402; int T_OTS403; int T_OTS404; int T_OTS405; int T_OTS406; int T_OTS407; int T_OTS408; int T_OTS409; int T_OTS410; int T_OTS411; int T_OTS412; int T_OTS413; int T_OTS414; int T_OTS415; int T_OTS416; int T_OTS417; int T_OTS418; int T_OTS419; int T_OTS420; int T_OTS421; int T_OTS422; int T_OTS423; int T_OTS424; int T_OTS425; int T_OTS426; int T_OTS427; int T_OTS428; int T_OTS429; int T_OTS430 ; int T_OTS431 ; int T_OTS432 ; int T_OTS433; int T_OTS434 ; int T_OTS435 ; int T_OTS436 ; int T_OTS437; int T_OTS438 ; int T_OTS439 ; int T_OTS440 ; int T_OTS441; int T_OTS442 ; int T_OTS443 ; int T_OTS444 ; int T_OTS445; int T_OTS446; const int16 T_OTS797[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 5, 0 	} 	, { 		-150, 150, 10, 5, 0 	} 	, { 		-150, 150, 10, 5, 0 	} 	,      { 		-150, 150, 10, 5, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       	{ 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		3,60,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 T_OTS548[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		70,70,1 	} 	,       	    { 		71,72,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 56, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		73, 73, 1 	} 	,    { 		0, 0, 0 	} 	,    { 		69, 69, 1 	} 	,    { 		57, 60, 1 	} 	,   { 		61, 65, 1 	} 	,   { 		66, 68, 1 	} } ; const uint8 T_OTS769[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string T_OTS558[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string T_OTS557[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- Normal Finish ",  "+/- PowerShot",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed", "TR to PS" ,  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","First Touch","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","AI_Support","" } ; const string T_OTS773 [] = { 	"Classic","Alternative","Custom", ""  } ; const string T_OTS872 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string T_OTS789[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string T_OTS775[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string T_OTS842[] = { 	"Right",  "Left",  "" } ; const string T_OTS840[] = { 	"One Tap",  "Double Tap",  "" } ; const string T_OTS779[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string T_OTS781[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Spin Move",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Lane Change",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"STP-rev-Elastco",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_PowerShot","FL_Nutmg_L_R" } ; const string T_OTS816[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; const uint8 T_OTS1306[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 T_OTS1307[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 T_OTS1308[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 T_OTS1309[] = { 	41,42,70,41,70,41,14,70,41,41,29  } ; const uint8 T_OTS1310[] = { 	42,41,41,14,70,41,70,41,70,41 ,29  } ; const uint8 T_OTS1311[] = { 	18, 44, 18, 44, 18, 44, 18, 44,18,44,29 } ; const uint8 T_OTS1312[] = { 	18, 44, 18, 44, 18, 44, 18, 44,18,44,29 } ; const uint8 T_OTS1313[] = { 	27, 23, 44, 27, 23, 44, 23, 44, 27, 23,29 } ; const uint8 T_OTS1314[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 T_OTS1315[] = {     9, 51, 41, 62, 14, 70, 9, 41, 62, 51, 33, 29 }; const uint8 T_OTS1316[] = {     7, 10, 70, 41, 62, 51, 7, 70, 41, 10, 33, 29 }; const uint8 T_OTS1317[] = {     41, 9, 70, 20, 62, 14, 51, 51, 14, 51, 33, 29 }; const uint8 T_OTS1318[] = {     41, 7, 51, 70, 14, 51, 7, 51, 20, 62, 33, 29 }; const uint8 T_OTS1319[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 33, 29 } ; const uint8 T_OTS1320[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 33, 29 } ; const uint8 T_OTS1321[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 33, 29  } ; function T_OTS111(T_OTS112) { if (T_OTS112 > 8 ) { 		T_OTS112 = 0; 			} 	T_OTS112 += T_OTS481; 	return T_OTS112; 	} function T_OTS113() { 	vm_tctrl(0); 	if(T_OTS29 && T_OTS352){ 		if(T_OTS524 < 1000){ 			T_OTS448 = 10; 			T_OTS474   = 10; 			T_OTS472  = 10; 					} 			} 	if(T_OTS451 && T_OTS353){ 		T_OTS449 = FALSE; 		if(T_OTS524 < 1000){ 			T_OTS448 = 11; 			T_OTS474   = 11; 			T_OTS472  = 11; 					} 			} 	if(T_OTS449 && T_OTS353){ 		T_OTS451 = FALSE; 		if(T_OTS524 < 1000){ 			T_OTS448 = 10; 			T_OTS474   = 10; 			T_OTS472  = 10; 					} 			} 			       if(T_OTS524 >= 1000){     T_OTS453 = T_OTS111(T_OTS453);     T_OTS471 = T_OTS111(T_OTS471);     T_OTS472 = T_OTS111(T_OTS472);     T_OTS448 = T_OTS111(T_OTS448);     T_OTS474 = T_OTS111(T_OTS474);     } 	if(T_OTS352){ 		if(T_OTS477 == T_OTS562){ 			T_OTS454 = !T_OTS454; 			if(T_OTS1306[T_OTS453]) T_OTS216(T_OTS1306[T_OTS453]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 4)){ 			T_OTS454 = FALSE; 			if(T_OTS1313[T_OTS471]) T_OTS216(T_OTS1313[T_OTS471]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 1) ){ 			T_OTS454 = TRUE; 			if(T_OTS1308[T_OTS448]) T_OTS216(T_OTS1308[T_OTS448]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 1) ){ 			T_OTS454 = FALSE; 			if(T_OTS1307[T_OTS448]) T_OTS216(T_OTS1307[T_OTS448]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 2) ){ 			T_OTS454 = TRUE; 			if(T_OTS1310[T_OTS474]) T_OTS216(T_OTS1310[T_OTS474]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 2) ){ 			T_OTS454 = FALSE; 			if(T_OTS1309[T_OTS474]) T_OTS216(T_OTS1309[T_OTS474]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 3) ){ 			T_OTS454 = TRUE; 			if(T_OTS1311[T_OTS472]) T_OTS216(T_OTS1311[T_OTS472]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 3) ){ 			T_OTS454 = FALSE; 			if(T_OTS1312[T_OTS472]) T_OTS216(T_OTS1312[T_OTS472]); 					} 			} 	else if(T_OTS353){ 		if(T_OTS477 == T_OTS562){ 			T_OTS454 = !T_OTS454; 			if(T_OTS1314[T_OTS453]) T_OTS216(T_OTS1314[T_OTS453]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 4)){ 			T_OTS454 = FALSE; 			if(T_OTS1321[T_OTS471]) T_OTS216(T_OTS1321[T_OTS471]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 1) ){ 			T_OTS454 = TRUE; 			if(T_OTS1316[T_OTS448]) T_OTS216(T_OTS1316[T_OTS448]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 1) ){ 			T_OTS454 = FALSE; 			if(T_OTS1315[T_OTS448]) T_OTS216(T_OTS1315[T_OTS448]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 2) ){ 			T_OTS454 = TRUE; 			if(T_OTS1318[T_OTS474]) T_OTS216(T_OTS1318[T_OTS474]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 2) ){ 			T_OTS454 = FALSE; 			if(T_OTS1317[T_OTS474]) T_OTS216(T_OTS1317[T_OTS474]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 + 3) ){ 			T_OTS454 = TRUE; 			if(T_OTS1319[T_OTS472]) T_OTS216(T_OTS1319[T_OTS472]); 					} 		if(T_OTS477 == T_OTS221 (T_OTS562 - 3) ){ 			T_OTS454 = FALSE; 			if(T_OTS1320[T_OTS472]) T_OTS216(T_OTS1320[T_OTS472]); 					} 			} } int T_OTS453; int T_OTS471; int T_OTS472; int T_OTS448; int T_OTS474; function T_OTS114() { 	if(T_OTS1098){ 		T_OTS475 += get_rtime(); 			} 	if(T_OTS475 >= 3000){ 		T_OTS475 = 0; 		T_OTS1098 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(T_OTS442)  && !get_ival(T_OTS443) && !get_ival(T_OTS441) && !get_ival(T_OTS440)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !T_OTS478 && !combo_running(T_OTS0)) { 			T_OTS477 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			T_OTS478 = TRUE; 			T_OTS1098 = TRUE; 			T_OTS475 = 0; 			vm_tctrl(0); 			T_OTS113(); 					} 		set_val(T_OTS1079, 0); 		set_val(T_OTS1080, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		T_OTS478 = FALSE; 			} 	} function T_OTS115() { 	T_OTS161(); 	if (T_OTS375 == 0 && T_OTS376 == 0 && T_OTS377 == 0 && T_OTS378 == 0 && T_OTS379 == 0 && T_OTS380 == 0 && T_OTS381 == 0 && T_OTS382 == 0) { 		T_OTS375 = 4; 		T_OTS376 = 9; 		T_OTS377 = 10; 		T_OTS378 = 14; 		T_OTS379 = 14; 		T_OTS380 = 47; 		T_OTS381 = 47; 		T_OTS382 = 47; 			} 	T_OTS911 = get_slot(); 	} int T_OTS481 = 1; int T_OTS482; int T_OTS483; int T_OTS484 = TRUE; int T_OTS485[6]; int T_OTS486; int T_OTS487; int T_OTS488; int T_OTS489; int block = FALSE; int T_OTS490 = 1; fcombo T_OTS0{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ if(get_val(XB1_LS)) {combo_run(glichshot);} T_OTS109(T_OTS110); 	if(!T_OTS483){ 		T_OTS483 = TRUE; 		T_OTS482 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,T_OTS483); 		set_pvar(SPVAR_3,T_OTS482); 		T_OTS484 = TRUE; 			} 	if(!T_OTS489){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			T_OTS488 = T_OTS118(T_OTS488 + 1 ,0,5)T_OTS484 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			T_OTS488 = T_OTS118(T_OTS488 - 1 ,0,5)T_OTS484 = TRUE 		} 		if(event_press(PS4_UP)){ 			T_OTS485[T_OTS488]  = T_OTS118(T_OTS485[T_OTS488] + 1 ,0,9)T_OTS484 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			T_OTS485[T_OTS488]  = T_OTS118(T_OTS485[T_OTS488] - 1 ,0,9)T_OTS484 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			T_OTS486 = 0; 			for(T_OTS487 = 5; 			T_OTS487 >= 0; 			T_OTS487--){ 				T_OTS486 += T_OTS485[T_OTS487] * pow(10,T_OTS487) 			} 			if(T_OTS486 == T_OTS116(T_OTS482)){ 				T_OTS489 = TRUE; 				set_pvar(SPVAR_2,T_OTS489)  			} 			T_OTS484 = TRUE; 					} 			} 	if(T_OTS484){ 		cls_oled(0)if(!T_OTS489){ 			T_OTS122(T_OTS482,T_OTS505,10,OLED_FONT_MEDIUM,OLED_WHITE,T_OTS506)for( T_OTS487 = 0; 			T_OTS487 < 6; 			T_OTS487++){ 				T_OTS122(T_OTS485[T_OTS487],85 - (T_OTS487 * 10),40,OLED_FONT_MEDIUM,!(T_OTS487 == T_OTS488),T_OTS506) 			} 					} 		T_OTS484 = FALSE; 			} 	if(T_OTS489){ 		if (get_ival(T_OTS438) || get_ival(T_OTS442) > 30 || get_ival(T_OTS440) || get_ival(T_OTS441) || T_OTS345 || combo_running(T_OTS72) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			T_OTS109(T_OTS110); 					} 		if(get_ival(T_OTS443) > 40 || (!get_ival(T_OTS440) && !get_ival(T_OTS441))){ 			if(get_ival(T_OTS438)){ 				vm_tctrl(0); 				if(get_ptime(T_OTS438) > T_OTS577){ 					set_val(T_OTS438,0); 									} 							} 					} 		if(!get_ival(T_OTS440)){ 			if(get_ival(T_OTS438)){ 				vm_tctrl(0); 				if(get_ptime(T_OTS438) > T_OTS577){ 					set_val(T_OTS438,0); 									} 							} 					} 		if (T_OTS345) { 			vm_tctrl(0); 			if(T_OTS346 < 8050){ 				T_OTS346 += get_rtime(); 							} 			if (T_OTS346 >= 8000) { 				cls_oled(OLED_BLACK); 				T_OTS346 = 0; 				T_OTS345 = FALSE; 							} 					} 		if (block) { 		if (T_OTS110 > 7)combo_run(T_OTS108); 			if (T_OTS490 < 310) { 				T_OTS490 += get_rtime(); 							} 			if (T_OTS490 <= 300 ) { 				T_OTS158(); 							} 			if (T_OTS490 > 300 ) { 				block = FALSE; 				T_OTS490 = 1; 				T_OTS686 = TRUE; 							} 			if (T_OTS490 < 0) { 				T_OTS490 = 1; 							} 			if (T_OTS490 <= 100) { 				combo_stop(T_OTS88); 				combo_stop(T_OTS97); 				combo_stop(T_OTS89); 				combo_stop(T_OTS98); 				combo_stop(T_OTS95); 				combo_stop(T_OTS96); 				combo_stop(T_OTS92); 				combo_stop(T_OTS94); 				combo_stop(T_OTS91); 				combo_stop(T_OTS87); 				combo_stop(T_OTS85); 				combo_stop(T_OTS90); 				combo_stop(T_OTS104); 				combo_stop(T_OTS106); 				combo_stop(T_OTS100); 				combo_stop(T_OTS105); 				combo_stop(T_OTS99); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(T_OTS428){ 			T_OTS429 = FALSE; 					} 		if(T_OTS429){ 			T_OTS428 = FALSE; 					} 		if(T_OTS350){ 			T_OTS351 = FALSE; 			T_OTS352 = FALSE; 			T_OTS353 = FALSE; 					} 		if(T_OTS351){ 			T_OTS350 = FALSE; 			T_OTS352 = FALSE; 			T_OTS353 = FALSE; 					} 		if(T_OTS352){ 			T_OTS350 = FALSE; 			T_OTS351 = FALSE; 			T_OTS353 = FALSE; 					} 		if(T_OTS353){ 			T_OTS350 = FALSE; 			T_OTS351 = FALSE; 			T_OTS352 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				T_OTS1133 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				T_OTS1133 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				T_OTS1021 = !T_OTS1021; 				T_OTS218(T_OTS1251); 				T_OTS192(T_OTS1021, sizeof(T_OTS521) - 1, T_OTS521[0]); 				T_OTS345 = TRUE; 							} 					} 		if (T_OTS1021) { 				if(T_OTS422 == TRUE){ 			if(get_ival(T_OTS443) && ( combo_running(T_OTS102) && !get_ival(T_OTS441)) ) {set_val(T_OTS441,0);combo_run(T_OTS101);} 					} 					if(combo_running(T_OTS101)){ 						if(event_press(T_OTS441) || event_press(T_OTS438) || event_press(T_OTS445) || event_press(T_OTS444) || event_press(T_OTS443) ){combo_stop(T_OTS101);} 					} 			if(T_OTS370){ 				T_OTS255(); 			} 			if (T_OTS368) { 				T_OTS245(); 							} 			if (event_release(T_OTS443)) { 				T_OTS524 = 1; 							} 			if (T_OTS524 < 8000) { 				T_OTS524 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					T_OTS526 = !T_OTS526; 					T_OTS218(T_OTS526); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (T_OTS526) { 				if (T_OTS526) T_OTS211(T_OTS1053); 				if (T_OTS526) { 					T_OTS137(); 									} 							} 			else if (!get_ival(T_OTS443)) { 				T_OTS211(T_OTS1056); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						T_OTS341 = TRUE; 						T_OTS348 = TRUE; 						T_OTS347 = FALSE; 						if (!T_OTS341) { 							T_OTS347 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!T_OTS347) { 					if (T_OTS341 || T_OTS342) { 						vm_tctrl(0); 					} 					if (T_OTS341) { 						combo_stop(T_OTS72); 						vm_tctrl(0); 						T_OTS349= T_OTS138(T_OTS349,0  ); 						T_OTS350 = T_OTS138(T_OTS350, 1); 						T_OTS351  = T_OTS138(T_OTS351   ,2  ); 						T_OTS352  = T_OTS138(T_OTS352 , 3); 						T_OTS353  = T_OTS138(T_OTS353 , 4); 						T_OTS354 = T_OTS138(T_OTS354, 5); 						T_OTS355 = T_OTS138(T_OTS355, 6); 						T_OTS356 = T_OTS138(T_OTS356, 7); 						T_OTS357 = T_OTS138(T_OTS357, 8); 						T_OTS358 = T_OTS138(T_OTS358, 9); 						T_OTS359 = T_OTS138(T_OTS359, 10); 						T_OTS360 = T_OTS138(T_OTS360, 11); 						T_OTS361 = T_OTS138(T_OTS361, 12); 						T_OTS362 = T_OTS138(T_OTS362,13); 						T_OTS363 = T_OTS138(T_OTS363, 14); 						T_OTS364 = T_OTS138(T_OTS364, 15); 						T_OTS365 = T_OTS138(T_OTS365, 16); 						T_OTS366 = T_OTS138(T_OTS366, 17); 						T_OTS367 = T_OTS138(T_OTS367, 18); 						T_OTS368 = T_OTS138(T_OTS368, 19); 						T_OTS110 = T_OTS138(T_OTS110, 20); 						T_OTS370 = T_OTS138(T_OTS370, 21); 						T_OTS371              = T_OTS138(T_OTS371              ,22  ); 						T_OTS372              = T_OTS138(T_OTS372              ,23  ); 						T_OTS373               = T_OTS138(T_OTS373               ,24  ); 						if (event_press(PS4_DOWN)) { 							T_OTS343 = clamp(T_OTS343 + 1, 0, T_OTS374); 							T_OTS348 = TRUE; 													} 						if (event_press(PS4_UP)) { 							T_OTS343 = clamp(T_OTS343 - 1, 0, T_OTS374); 							T_OTS348 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							T_OTS341 = FALSE; 							T_OTS347 = FALSE; 							T_OTS348 = FALSE; 							vm_tctrl(0); 							combo_run(T_OTS75); 													} 						if (T_OTS548[T_OTS343][2] == 1) { 							if(T_OTS343 == 0 ){ 								if(T_OTS349 == 2 ){ 									if (event_press(PS4_CROSS)) { 										T_OTS344 = T_OTS548[T_OTS343][0]; 										T_OTS341 = FALSE; 										T_OTS342 = TRUE; 										T_OTS348 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									T_OTS344 = T_OTS548[T_OTS343][0]; 									T_OTS341 = FALSE; 									T_OTS342 = TRUE; 									T_OTS348 = TRUE; 																	} 															} 													} 						T_OTS158(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, T_OTS538[0]); 						T_OTS147(T_OTS343 + 1, T_OTS153(T_OTS343 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, T_OTS540[0]); 						T_OTS147(T_OTS911, T_OTS153(T_OTS911), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(T_OTS343 == 0 ){ 							if(T_OTS349 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, T_OTS542[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, T_OTS543[0]); 															} 													} 						else{ 							if (T_OTS548[T_OTS343][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, T_OTS543[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, T_OTS542[0]); 															} 													} 											} 					if (T_OTS342) { 						T_OTS430               = T_OTS141(T_OTS430, 0); 						T_OTS431               = T_OTS141(T_OTS431, 1); 						T_OTS432             = T_OTS141(T_OTS432, 2); 						T_OTS433           = T_OTS141(T_OTS433, 3); 						T_OTS434             = T_OTS141(T_OTS434, 4); 						T_OTS435             = T_OTS141(T_OTS435, 5); 						T_OTS436              = T_OTS141(T_OTS436, 6); 						T_OTS437           = T_OTS141(T_OTS437, 7); 						T_OTS375          = T_OTS141(T_OTS375, 8); 						T_OTS376   = T_OTS141(T_OTS376, 9); 						T_OTS377 = T_OTS141(T_OTS377, 10); 						T_OTS378      = T_OTS141(T_OTS378, 11); 						T_OTS379    = T_OTS141(T_OTS379, 12); 						T_OTS380    = T_OTS141(T_OTS380, 13); 						T_OTS381    = T_OTS141(T_OTS381, 14); 						T_OTS382      = T_OTS141(T_OTS382, 15); 						T_OTS383      = T_OTS141(T_OTS383, 16); 						T_OTS264              = T_OTS141(T_OTS264, 17); 						T_OTS265           = T_OTS141(T_OTS265, 18); 						T_OTS266            = T_OTS141(T_OTS266, 19); 						T_OTS267            = T_OTS141(T_OTS267, 20); 						T_OTS268= T_OTS141(T_OTS268, 21); 						T_OTS397               = T_OTS141(T_OTS397, 22); 						T_OTS398               = T_OTS141(T_OTS398, 23); 						T_OTS399                   = T_OTS141(T_OTS399, 24); 						T_OTS400                   = T_OTS141(T_OTS400, 25); 						T_OTS401                   = T_OTS141(T_OTS401, 26); 						T_OTS402   = T_OTS141(T_OTS402, 27); 						T_OTS403   = T_OTS141(T_OTS403, 28); 						T_OTS404 = T_OTS141(T_OTS404, 29); 						T_OTS405   = T_OTS141(T_OTS405, 30); 						T_OTS406   = T_OTS141(T_OTS406, 31); 						T_OTS407 = T_OTS141(T_OTS407, 32); 						T_OTS408   = T_OTS141(T_OTS408, 33); 						T_OTS409   = T_OTS141(T_OTS409, 34); 						T_OTS410 = T_OTS141(T_OTS410, 35); 						T_OTS411   = T_OTS141(T_OTS411, 36); 						T_OTS412   = T_OTS141(T_OTS412, 37); 						T_OTS413 = T_OTS141(T_OTS413, 38); 						T_OTS414   = T_OTS144(T_OTS414, 39); 						T_OTS415         = T_OTS144(T_OTS415, 40); 						T_OTS416   = T_OTS141(T_OTS416, 41); 						T_OTS417     = T_OTS141(T_OTS417, 42); 						T_OTS418                   = T_OTS144(T_OTS418, 43); 						T_OTS1209 = T_OTS141(T_OTS1209, 54); 						T_OTS1203 = T_OTS141(T_OTS1203, 55); 						T_OTS1210              = T_OTS141(T_OTS1210            ,56); 						T_OTS419               = T_OTS144(T_OTS419, 44); 						T_OTS420 = T_OTS144(T_OTS420, 45); 						T_OTS421                 = T_OTS144(T_OTS421, 46); 						T_OTS423               = T_OTS144(T_OTS423, 47); 						T_OTS424 = T_OTS141(T_OTS424, 48); 						T_OTS425 = T_OTS141(T_OTS425, 49); 						T_OTS426 = T_OTS141(T_OTS426, 50); 						T_OTS427 = T_OTS141(T_OTS427, 51); 						T_OTS428               = T_OTS141(T_OTS428, 52); 						T_OTS429                 = T_OTS141(T_OTS429, 53); 						T_OTS384       = T_OTS144(T_OTS384     ,57 ); 						T_OTS385       = T_OTS144(T_OTS385     ,58 ); 						T_OTS386      = T_OTS141(T_OTS386    ,59 ); 						T_OTS387   = T_OTS141(T_OTS387 ,60 ); 						T_OTS388       = T_OTS144(T_OTS388     ,61 ); 						T_OTS389       = T_OTS144(T_OTS389     ,62 ); 						T_OTS390   = T_OTS141(T_OTS390 ,63 ); 						T_OTS391   = T_OTS141(T_OTS391 ,64 ); 						T_OTS392      = T_OTS141(T_OTS392    ,65 ); 						T_OTS393          = T_OTS144(T_OTS393        ,66 ); 						T_OTS394          = T_OTS144(T_OTS394        ,67 ); 						T_OTS395         = T_OTS141(T_OTS395       ,68 ); 						T_OTS446             = T_OTS144(T_OTS446           ,69); 						T_OTS29             = T_OTS141(T_OTS29           ,70); 						T_OTS451           = T_OTS141(T_OTS451         ,71); 						T_OTS449         = T_OTS141(T_OTS449       ,72); 						T_OTS422		  = T_OTS141(T_OTS422      ,73); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								T_OTS344 = clamp(T_OTS344 + 1, T_OTS548[T_OTS343][0], T_OTS548[T_OTS343][1]); 								T_OTS348 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								T_OTS344 = clamp(T_OTS344 - 1, T_OTS548[T_OTS343][0], T_OTS548[T_OTS343][1]); 								T_OTS348 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							T_OTS341 = TRUE; 							T_OTS342 = FALSE; 							T_OTS348 = TRUE; 													} 						T_OTS158(); 						T_OTS913 = T_OTS797[T_OTS344][0]; 						T_OTS914 = T_OTS797[T_OTS344][1]; 						if (T_OTS797[T_OTS344][4] == 0) { 							T_OTS147(T_OTS913, T_OTS153(T_OTS913), 4, 20, OLED_FONT_SMALL); 							T_OTS147(T_OTS914, T_OTS153(T_OTS914), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (T_OTS348) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (T_OTS342) { 							print(T_OTS203(T_OTS156(T_OTS557[T_OTS344]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, T_OTS557[T_OTS344]); 													} 						else { 							print(T_OTS203(T_OTS156(T_OTS558[T_OTS343]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, T_OTS558[T_OTS343]); 													} 						T_OTS348 = FALSE; 					} 									} 				if (!T_OTS341 && !T_OTS342) { 					if (T_OTS347) { 						cls_oled(0); 						combo_run(T_OTS72); 						T_OTS347 = FALSE; 						T_OTS345 = TRUE; 						vm_tctrl(0); 					} 					if(T_OTS349 == 0){ 						T_OTS438      = PS4_CIRCLE; 						T_OTS439      = PS4_CROSS ; 						T_OTS440    = PS4_L1    ; 						T_OTS441  = PS4_R1; 						T_OTS442    = PS4_L2; 						T_OTS443    = PS4_R2; 						T_OTS444     = PS4_SQUARE; 						T_OTS445  = PS4_TRIANGLE; 					} 					else if(T_OTS349 == 1){ 						T_OTS438      = PS4_SQUARE; 						T_OTS439      = PS4_CROSS ; 						T_OTS440    = PS4_L1    ; 						T_OTS441  = PS4_R1; 						T_OTS442    = PS4_L2; 						T_OTS443    = PS4_R2; 						T_OTS444     = PS4_CIRCLE; 						T_OTS445  = PS4_TRIANGLE; 					} 					else if(T_OTS349 == 2){ 						T_OTS438      = T_OTS1334[T_OTS430]; 						T_OTS439      = T_OTS1334[T_OTS431] ; 						T_OTS440    = T_OTS1334[T_OTS432]  ; 						T_OTS441  = T_OTS1334[T_OTS433]; 						T_OTS442    = T_OTS1334[T_OTS434]; 						T_OTS443    = T_OTS1334[T_OTS435]; 						T_OTS444     = T_OTS1334[T_OTS436]; 						T_OTS445  = T_OTS1334[T_OTS437]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !T_OTS1157) { 						set_val(T_OTS439, 0); 						vm_tctrl(0); 						combo_run(T_OTS77); 											} 					if (T_OTS686) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 2500 ) ){ 							T_OTS562 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							T_OTS1011 = T_OTS1345[T_OTS562][0]; 							T_OTS637 = T_OTS1345[T_OTS562][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							T_OTS418 += 5; 							T_OTS199(T_OTS203(sizeof(T_OTS564) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS564[0], T_OTS418); 													} 						if (event_press(PS4_LEFT)) { 							T_OTS418 -= 5; 							T_OTS199(T_OTS203(sizeof(T_OTS564) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS564[0], T_OTS418); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !T_OTS584 ) { 						if (event_press(PS4_UP)) { 							T_OTS569 += 25; 							T_OTS199(T_OTS203(sizeof(T_OTS570) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS570[0], T_OTS569); 													} 						if (event_press(PS4_DOWN)) { 							T_OTS569 -= 25; 							T_OTS199(T_OTS203(sizeof(T_OTS570) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS570[0], T_OTS569); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (T_OTS363) { 						T_OTS237(); 											} 					if (T_OTS364) { 						T_OTS238(); 						T_OTS239(); 											} 					if (!T_OTS364) { 						if (get_ival(T_OTS438)) { 							if (event_press(PS4_RIGHT)) { 								T_OTS577 += 2; 								T_OTS199(T_OTS203(sizeof(T_OTS578) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS578[0], T_OTS577); 															} 							if (event_press(PS4_LEFT)) { 								T_OTS577 -= 2; 								T_OTS199(T_OTS203(sizeof(T_OTS578) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS578[0], T_OTS577); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(T_OTS440) ){ 							if(get_ival(T_OTS438) && get_ptime(T_OTS438) > T_OTS577){ 								set_val(T_OTS438,0); 															} 													} 											} 					if(T_OTS367){ 						T_OTS242(); 											} 					if (T_OTS359) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								T_OTS584 = !T_OTS584; 								T_OTS218(T_OTS584); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (T_OTS584 && T_OTS359) { 						vm_tctrl(0); 						combo_stop(T_OTS85); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								T_OTS414 += 10; 								T_OTS199(T_OTS203(sizeof(T_OTS586) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS586[0], T_OTS414); 															} 							if (event_press(PS4_DOWN)) { 								T_OTS414 -= 10; 								T_OTS199(T_OTS203(sizeof(T_OTS586) - 1, OLED_FONT_MEDIUM_WIDTH), T_OTS586[0], T_OTS414); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						T_OTS211(T_OTS1055); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								T_OTS591 = FALSE; 								vm_tctrl(0); 								combo_run(T_OTS78); 															} 							if (event_press(PS4_LEFT)) { 								T_OTS591 = TRUE; 								vm_tctrl(0); 								combo_run(T_OTS78); 															} 							set_val(PS4_L1,0); 													} 											} 					if (T_OTS360) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								T_OTS593 = !T_OTS593; 								T_OTS218(T_OTS593); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (T_OTS593 && T_OTS360) { 						vm_tctrl(0); 						T_OTS211(T_OTS1057); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								T_OTS594 = FALSE; 								vm_tctrl(0); 								combo_run(T_OTS79); 															} 							if (event_press(PS4_RIGHT)) { 								T_OTS594 = TRUE; 								vm_tctrl(0); 								combo_run(T_OTS79); 															} 													} 											} 					if(T_OTS352 || T_OTS353 ){ 						T_OTS114(); 											} 					if (T_OTS350) { 						if (T_OTS350 == T_OTS1061) T_OTS597 = TRUE; 						if (T_OTS350 == T_OTS1062) { 							if (event_press(T_OTS1344[-1 +T_OTS383]) && get_brtime(T_OTS1344[-1 +T_OTS383]) <= 200) { 								T_OTS597 = !T_OTS597; 								T_OTS218(T_OTS597); 															} 							set_val(T_OTS1344[-1 +T_OTS383], 0); 													} 						if (T_OTS350 > 0 && T_OTS350 < 3 && T_OTS597 == 1) { 							T_OTS215(); 													} 						else if (T_OTS350 == 3) { 							if (get_ival(T_OTS1344[-1 +T_OTS383])) { 								T_OTS215(); 															} 							set_val(T_OTS1344[-1 +T_OTS383], 0); 													} 											} 									if (T_OTS351) { 						if (T_OTS351 == T_OTS1061) T_OTS600 = TRUE; 						if (T_OTS351 == T_OTS1062) { 							if (event_press(T_OTS1344[-1 +T_OTS268]) && get_brtime(T_OTS1344[-1 +T_OTS268]) <= 200) { 								T_OTS600 = !T_OTS600; 								T_OTS218(T_OTS600); 															} 							set_val(T_OTS1344[-1 +T_OTS268], 0); 													} 						if (T_OTS351 > 0 && T_OTS351 < 3 && T_OTS600 == 1) { 							T_OTS213(); 													} 						else if (T_OTS351 == 3) { 							if (get_ival(T_OTS1344[-1 +T_OTS268])) { 								T_OTS213(); 															} 							set_val(T_OTS1344[-1 +T_OTS268], 0); 													} 											} 					if (T_OTS354) { 						if (T_OTS354 == 1) { 							T_OTS603 = PS4_R3; 							T_OTS600 = FALSE; 													} 						if (T_OTS354 == 2) { 							T_OTS603 = PS4_L3; 							T_OTS600 = FALSE; 													} 						if (T_OTS354 == 3) { 							T_OTS603 = XB1_PR1; 							T_OTS600 = FALSE; 													} 						if (T_OTS354 == 4) { 							T_OTS603 = XB1_PR2; 							T_OTS600 = FALSE; 													} 						if (T_OTS354 == 5) { 							T_OTS603 = XB1_PL1; 							T_OTS600 = FALSE; 													} 						if (T_OTS354 == 6) { 							T_OTS603 = XB1_PL2; 							T_OTS600 = FALSE; 													} 						if(get_ival(T_OTS603)){ 							if(T_OTS397 || T_OTS398){ 								if( T_OTS397 && event_press(PS4_L1)){ 									T_OTS454 = FALSE; 									T_OTS1010 = T_OTS397  ; 									T_OTS216( T_OTS397   ); 								} 								if( T_OTS398 && event_press(PS4_R1)){ 									T_OTS454 = TRUE; 									T_OTS1010 =  T_OTS398 ; 									T_OTS216( T_OTS398   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( T_OTS399 ){ 								if(event_press(PS4_SQUARE)){ 									T_OTS454 = FALSE; 									T_OTS1010 =  T_OTS399  ; 													combo_stop(T_OTS88); 				combo_stop(T_OTS97); 				combo_stop(T_OTS89); 				combo_stop(T_OTS98); 				combo_stop(T_OTS95); 				combo_stop(T_OTS96); 				combo_stop(T_OTS92); 				combo_stop(T_OTS94); 				combo_stop(T_OTS91); 				combo_stop(T_OTS87); 				combo_stop(T_OTS85); 				combo_stop(T_OTS90); 				combo_stop(T_OTS104); 				combo_stop(T_OTS106); 				combo_stop(T_OTS100); 				combo_stop(T_OTS105); 				combo_stop(T_OTS99); 									T_OTS216( T_OTS399   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									T_OTS454 = TRUE; 									T_OTS1010 =  T_OTS399  ; 									T_OTS216( T_OTS399   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( T_OTS400 ){ 								if(event_press(PS4_CROSS)){ 									T_OTS454 = FALSE; 									T_OTS1010 =  T_OTS400  ; 									T_OTS216( T_OTS400   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(T_OTS88); 				combo_stop(T_OTS97); 				combo_stop(T_OTS89); 				combo_stop(T_OTS98); 				combo_stop(T_OTS95); 				combo_stop(T_OTS96); 				combo_stop(T_OTS92); 				combo_stop(T_OTS94); 				combo_stop(T_OTS91); 				combo_stop(T_OTS87); 				combo_stop(T_OTS85); 				combo_stop(T_OTS90); 				combo_stop(T_OTS104); 				combo_stop(T_OTS106); 				combo_stop(T_OTS100); 				combo_stop(T_OTS105); 				combo_stop(T_OTS99); 									T_OTS454 = TRUE; 									T_OTS1010 =  T_OTS400  ; 									T_OTS216( T_OTS400   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( T_OTS401 ){ 								if(event_press(PS4_R3)){ 									T_OTS454 = FALSE; 									T_OTS1010 =  T_OTS401  ; 									T_OTS216( T_OTS401   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(T_OTS603,0); 											} 					if (T_OTS355) { 						if (T_OTS403 == 1) { 							if (event_press(T_OTS1344[-1 + T_OTS402]) && !T_OTS1108) { 								vm_tctrl(0); 								combo_run(T_OTS82); 															} 							else if (event_press(T_OTS1344[-1 + T_OTS402]) && T_OTS1108) { 								set_val(T_OTS1344[-1 + T_OTS402], 0); 								T_OTS454 = !T_OTS404; 								T_OTS1010 = T_OTS355; 								T_OTS216(T_OTS355); 															} 													} 						else { 							if (event_press(T_OTS1344[-1 + T_OTS402])) { 								T_OTS454 = !T_OTS404; 								set_val(T_OTS1344[-1 + T_OTS402], 0); 								T_OTS1010 = T_OTS355; 								T_OTS216(T_OTS355); 															} 													} 					} 					if (T_OTS357) { 						if (T_OTS409 == 1) { 							if (event_press(T_OTS1344[-1 +T_OTS408]) && !T_OTS1108) { 								vm_tctrl(0); 								combo_run(T_OTS82); 															} 							else if (event_press(T_OTS1344[-1 +T_OTS408]) && T_OTS1108) { 								set_val(T_OTS1344[-1 +T_OTS408], 0); 								T_OTS454 = !T_OTS410; 								T_OTS1010 = T_OTS357; 								T_OTS216(T_OTS357); 															} 													} 						else { 							if (event_press(T_OTS1344[-1 +T_OTS408])) { 								T_OTS454 = !T_OTS410; 								set_val(T_OTS1344[-1 +T_OTS408], 0); 								T_OTS1010 = T_OTS357; 								T_OTS216(T_OTS357); 															} 													} 					} 					if (T_OTS356) { 						if (T_OTS406 == 1) { 							if (event_press(T_OTS1344[-1 +T_OTS405]) && !T_OTS1108) { 								vm_tctrl(0); 								combo_run(T_OTS82); 															} 							else if (event_press(T_OTS1344[-1 +T_OTS405]) && T_OTS1108) { 								set_val(T_OTS1344[-1 +T_OTS405], 0); 								T_OTS454 = !T_OTS407; 								T_OTS1010 = T_OTS356; 								T_OTS216(T_OTS356); 															} 													} 						else { 							if (event_press(T_OTS1344[-1 +T_OTS405])) { 								T_OTS454 = !T_OTS407; 								set_val(T_OTS1344[-1 +T_OTS405], 0); 								T_OTS1010 = T_OTS356; 								T_OTS216(T_OTS356); 															} 													} 					} 					if (T_OTS358) { 						if (T_OTS412 == 1) { 							if (event_press(T_OTS1344[-1 +T_OTS411]) && !T_OTS1108) { 								vm_tctrl(0); 								combo_run(T_OTS82); 															} 							else if (event_press(T_OTS1344[-1 +T_OTS411]) && T_OTS1108) { 								set_val(T_OTS1344[-1 +T_OTS411], 0); 								T_OTS454 = !T_OTS413; 								T_OTS1010 = T_OTS358; 								T_OTS216(T_OTS358); 															} 													} 						else { 							if (event_press(T_OTS1344[-1 +T_OTS411])) { 								T_OTS454 = !T_OTS413; 								set_val(T_OTS1344[-1 +T_OTS411], 0); 								T_OTS1010 = T_OTS358; 								T_OTS216(T_OTS358); 															} 													} 					} 					if (T_OTS1010 == T_OTS299 && combo_running(T_OTS30)) set_val(T_OTS440, 100); 					if(T_OTS372){ 						if(!block){ 							if(!get_val(T_OTS442)){ 								if( !get_val(T_OTS443)){ 									if(get_val(T_OTS439)){ 										T_OTS619 += get_rtime(); 																			} 									if(T_OTS392){ 										if(get_ival(T_OTS439) && get_ptime(T_OTS439) > T_OTS389){ 											set_val(T_OTS439,0); 																					} 																			} 									if(event_release(T_OTS439)){ 										if( T_OTS619 < T_OTS388 ){ 											T_OTS620 = T_OTS388 - T_OTS619; 											combo_run(T_OTS104); 																					} 										else{ 											if(T_OTS390 || T_OTS391) combo_restart(T_OTS105); 																					} 										T_OTS619 = 0; 																			} 																	} 							} 						} 											} 					if(T_OTS371){ 						if(!block){ 							if(get_ival(T_OTS442) < 30){ 								if( !get_ival(T_OTS443)){ 									if(get_ival(T_OTS445)){ 										T_OTS621 += get_rtime(); 																			} 									if(event_release(T_OTS445)){ 										if(T_OTS621 < T_OTS384){ 											T_OTS622 = T_OTS384 - T_OTS621; 											combo_run(T_OTS106); 																					} 										else{ 											if(T_OTS387) combo_run(T_OTS99); 																					} 										T_OTS621 = 0; 																			} 																	} 							} 						} 											} 					if(T_OTS373){ 						if(!block){ 							if(get_ival(T_OTS444)){ 								T_OTS623 += get_rtime(); 															} 							if(T_OTS395){ 								if(get_ival(T_OTS444) && get_ptime(T_OTS444) > T_OTS394){ 									set_val(T_OTS444,0); 																	} 															} 							if(event_release(T_OTS444)){ 								if(T_OTS623 && (T_OTS623 < T_OTS393)){ 									T_OTS624 = T_OTS393 - T_OTS623; 									combo_run(T_OTS100); 																	} 								T_OTS623 = 0; 															} 													} 											} 					if (T_OTS361) { 						if (event_press(T_OTS1344[-1 +T_OTS416])) { 							vm_tctrl(0); 							combo_run(T_OTS77); 													} 						set_val(T_OTS1344[-1 +T_OTS416], 0); 											} 					if(!T_OTS365){ 						T_OTS419 = 0 ; 						T_OTS420 = 0; 						T_OTS423 = 0; 						T_OTS421 = 0; 											} 					if (T_OTS366) { 						T_OTS238(); 						if (T_OTS424 == 0) { 							T_OTS627 = FALSE; 													} 						else { 							T_OTS627 = TRUE; 													} 						if (T_OTS425 == 0) { 							T_OTS629 = FALSE; 													} 						else { 							T_OTS629 = TRUE; 													} 						if (T_OTS426 == 0) { 							T_OTS631 = FALSE; 													} 						else { 							T_OTS631 = TRUE; 													} 						if (T_OTS427 == 0) { 							T_OTS633 = FALSE; 													} 						else { 							T_OTS633 = TRUE; 													} 						if(T_OTS426 == 6 || T_OTS425 == 6 || T_OTS424 == 6){ 							if (get_ival(T_OTS1344[-1 + T_OTS426]) || get_ival(T_OTS1344[-1 + T_OTS425]) || get_ival(T_OTS1344[-1 + T_OTS424])){ 								combo_run(T_OTS0); 															} 													} 						if (T_OTS631) { 							if (get_val(T_OTS1344[-1 + T_OTS426])) { 								set_val(T_OTS1344[-1 + T_OTS426], 0); 								combo_run(T_OTS97); 								T_OTS1166 = 9000; 															} 													} 						if (T_OTS633) { 							if (get_val(T_OTS1344[-1 + T_OTS427])) { 								set_val(T_OTS1344[-1 + T_OTS427], 0); 								combo_run(T_OTS98); 								T_OTS1166 = 9000; 							} 							if (combo_running(T_OTS98)) { 								if(event_press(T_OTS438)){ 									combo_stop(T_OTS98); 									set_val(T_OTS438,100); 							} 								if (get_ival(T_OTS439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(T_OTS443) > 30) { 									combo_stop(T_OTS98); 																	} 															} 													} 						if (T_OTS629) { 							if (get_val(T_OTS1344[-1 + T_OTS425])) { 								set_val(T_OTS1344[-1 + T_OTS425], 0); 								T_OTS244(); 								T_OTS1166 = 9000; 															} 													} 						if (T_OTS627) { 							if (get_val(T_OTS1344[-1 + T_OTS424])) { 								set_val(T_OTS1344[-1 + T_OTS424], 0); 								combo_run(T_OTS95); 								T_OTS1166 = 9000; 															} 													} 											} 					if (T_OTS368) { 						T_OTS245(); 											} 									} 				}else if(get_ival(T_OTS443)){ 				combo_stop(T_OTS97); 				combo_stop(T_OTS89); 				combo_stop(T_OTS98); 				combo_stop(T_OTS95); 				combo_stop(T_OTS96); 				combo_stop(T_OTS92); 				combo_stop(T_OTS94); 				combo_stop(T_OTS91); 				combo_stop(T_OTS87); 				combo_stop(T_OTS85); 				combo_stop(T_OTS90); 				combo_stop(T_OTS104); 				combo_stop(T_OTS106); 				combo_stop(T_OTS100); 				combo_stop(T_OTS105); 				combo_stop(T_OTS99); 				} 					} 		else { 			if (!get_ival(T_OTS443)) T_OTS211(T_OTS1054); 					} 			} 			T_OTS257(); 	} fcombo T_OTS1 { 	set_val(T_OTS442, 100); 	set_val(T_OTS440,100); 	T_OTS232(); 	wait(400); 	set_val(T_OTS439,100); 	wait(90); 	wait( 400); 	} fcombo T_OTS2 { 	set_val(T_OTS442, 100); 	set_val(T_OTS440,100); 	T_OTS232(); 	wait(400); 	set_val(T_OTS438,100); 	wait(220); 	wait( 400); 	} fcombo T_OTS3 { 	call(T_OTS28); 	wait( 100); 	call(T_OTS98); 	T_OTS228(T_OTS1011, T_OTS637); 	wait( 800); 	wait( 350); 	set_val(T_OTS441,100); 	set_val(T_OTS440,100); 	wait( 400); 	} fcombo T_OTS4 { 	T_OTS234(); 	wait(32); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS225(T_OTS1112,T_OTS639); 	wait(32); 	T_OTS232(); 	wait(32); 	vm_tctrl(0); 	wait(350); 	} fcombo T_OTS5 { 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS6 { 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 1000); 	wait( 350); 	} fcombo T_OTS7 { 	T_OTS235(); 	T_OTS454 = FALSE; 	wait(T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait(T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait(T_OTS1014 + random(1,5)); 	T_OTS454 = TRUE; 	T_OTS232(); 	wait(T_OTS1014 + random(1,5)); 	wait(350); 	} fcombo T_OTS8 { 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS454 = TRUE; 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS454 = FALSE; 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS9 { 	T_OTS454 = TRUE; 	T_OTS232(); 	wait(T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait(T_OTS1014 + random(1,5)); 	T_OTS454 = FALSE; 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS10 { 	T_OTS454 = FALSE; 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS454 = TRUE; 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS11 { 	T_OTS228(0,0); 	set_val(T_OTS440,100); 	set_val(T_OTS441,100); 	T_OTS234(); 	wait( 60); 	wait( 60); 	} fcombo T_OTS12 { 	set_val(T_OTS1077, inv(T_OTS1011)); 	set_val(T_OTS1078, inv(T_OTS637)); 	set_val(T_OTS441, 100); 	set_val(T_OTS440, 100); 	wait( 60); 	set_val(T_OTS1077, inv(T_OTS1011)); 	set_val(T_OTS1078, inv(T_OTS637)); 	set_val(T_OTS441, 100); 	set_val(T_OTS440, 100); 	wait( 500); 	wait( 350); 	} fcombo T_OTS13 { 	T_OTS228(0, 0); 	set_val(T_OTS442, 100); 	wait( 60); 	T_OTS228(0, 0); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 100); 	wait( 60); 	T_OTS228(0, 0); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 80); 	T_OTS228(0, 0); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	wait( 350); 	} fcombo T_OTS14 { 	set_val(T_OTS441,100); 	set_val(T_OTS442,100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS441,100); 	set_val(T_OTS442,100); 	T_OTS232(); 	set_val(T_OTS440,100); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS441,100); 	set_val(T_OTS442,100); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS441,100); 	set_val(T_OTS442,100); 	wait( 350); 	} fcombo T_OTS15 { 	set_val(T_OTS440, 100); 	T_OTS232(); 	wait( 500); 	wait( 350); 	} fcombo T_OTS16 { 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	if(T_OTS454) T_OTS655 = T_OTS562 + 3; 	else  T_OTS655 = T_OTS562 - 3; 	T_OTS223(T_OTS655); 	T_OTS225(T_OTS1112,T_OTS639); 	wait(T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	if(T_OTS454) T_OTS655 = T_OTS562 + 1; 	else  T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS225(T_OTS1112,T_OTS639); 	wait(T_OTS1014 + random(1,5)); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS17 { 	set_val(T_OTS440,100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	set_val(T_OTS440,100); 	wait( T_OTS1014 + random(1,5)); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS18 { 	set_val(T_OTS442,100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442,100); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442,100); 	T_OTS232(); 	set_val(T_OTS442,100); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	set_val(T_OTS442,100); 	set_val(T_OTS443,100); 	wait(50); 	wait(350); 	} fcombo T_OTS19 { 	set_val(T_OTS442,100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442,100); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442,100); 	T_OTS232(); 	set_val(T_OTS442,100); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS20 { 	T_OTS233(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	T_OTS232()    	wait( T_OTS1014 + random(1,5)); 	T_OTS454 = !T_OTS454; 	T_OTS231(); 	wait( 1000); 	wait( 350); 	} fcombo T_OTS21 { 	set_val(T_OTS440,100); 	T_OTS235(); 	wait(50); 	T_OTS228(0,0); 	set_val(T_OTS440,100); 	wait(50); 	set_val(T_OTS440,100); 	T_OTS235(); 	wait(50); 	wait( 350); 	} fcombo T_OTS22 { 	T_OTS228(0, 0); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS23 { 	T_OTS235(); 	wait(T_OTS1014 + random(1,5)); 	T_OTS236()wait(T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait(T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS24 { 	set_val(T_OTS441, 100); 	set_val(T_OTS440, 100); 	wait( 20); 	set_val(T_OTS441, 100); 	set_val(T_OTS440, 100); 	if (T_OTS454) T_OTS655 = T_OTS562 + 4; 	else { 		T_OTS655 = T_OTS562 - 4; 			} 	T_OTS223(T_OTS655); 	T_OTS225(T_OTS1112, T_OTS639); 	set_val(T_OTS443, 100); 	wait( 100); 	wait( 350); 	} fcombo T_OTS25 { 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442, 100); 	wait( 30); 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS26 { 	set_val(T_OTS442, 100); 	T_OTS234(); 	wait( 70); 	set_val(T_OTS442, 100); 	T_OTS236(); 	wait( 60); 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( 60); 	wait( 350); 	} fcombo T_OTS27 { 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442, 100); 	wait( 30); 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} fcombo T_OTS28 { 	T_OTS232(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } fcombo T_OTS29 { 	T_OTS232(); 	set_val(T_OTS443, 0); 	wait(350); 	wait( 350); 	} fcombo T_OTS30 { 	if (T_OTS1010 == T_OTS301) T_OTS1015 = 200; 	else T_OTS1015 = 1; 	wait( T_OTS1015); 	T_OTS234(); 	wait( 70); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( 70); 	wait( 350); 	} fcombo T_OTS31 { 	set_val(T_OTS440, 100)T_OTS234(); 	T_OTS228(T_OTS1011,T_OTS637); 	wait( 50); 	set_val(T_OTS440, 100)T_OTS236(); 	T_OTS228(T_OTS1011,T_OTS637); 	wait( 50); 	set_val(T_OTS440, 100)T_OTS232(); 	T_OTS228(T_OTS1011,T_OTS637); 	wait( 50); 	T_OTS228(T_OTS1011,T_OTS637); 	wait(465); 	T_OTS228(T_OTS1011,T_OTS637); 	set_val(T_OTS442, 100); 	set_val(T_OTS443, 100); 	wait(50); 	if (T_OTS454) T_OTS655 = T_OTS562 - 1; 	else T_OTS655 = T_OTS562 + 1; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 50); 	if (T_OTS454) T_OTS655 = T_OTS562 + 4; 	else T_OTS655 = T_OTS562 - 4; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 700); 	wait( 350); 	} fcombo T_OTS32 { 	if (T_OTS1010 == T_OTS301) T_OTS1015 = 200; 	else T_OTS1015 = 1; 	set_val(T_OTS442,100); 	wait( T_OTS1015); 	T_OTS234(); 	set_val(T_OTS442,100); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	set_val(T_OTS442,100); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	set_val(T_OTS442,100); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS33 { 	if (T_OTS454) T_OTS655 = T_OTS562 - 2; 	else T_OTS655 = T_OTS562 + 2; 	T_OTS223(T_OTS655); 	T_OTS225(T_OTS1112, T_OTS639); 	wait( 280); 	T_OTS236(); 	wait( 50); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 45); 	set_val(T_OTS438, 100); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 45); 	T_OTS228(T_OTS1112, T_OTS639); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 45); 	T_OTS228(T_OTS1112, T_OTS639); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 45); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 100); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 500); 	wait( 350); 	} fcombo T_OTS34 { 	T_OTS232(); 	wait( 280); 	T_OTS231()  set_val(T_OTS438, 100); 	set_val(T_OTS442, 100); 	wait( 60); 	T_OTS231()  set_val(T_OTS442, 100); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 60); 	T_OTS231()  set_val(T_OTS442, 100); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	wait( 250); 	T_OTS231()   	wait( 300); 	wait( 350); 	} fcombo T_OTS35 { 	T_OTS232(); 	wait( 300); 	T_OTS234(); 	wait( 60); 	wait( 350); 	} fcombo T_OTS36 { 	set_val(T_OTS440, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS440, 100); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS440, 100); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS37 { 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS38 { 	set_val(T_OTS440, 100); 	T_OTS233(); 	wait( 60); 	set_val(T_OTS440, 100); 	T_OTS236(); 	wait( 60); 	set_val(T_OTS440, 100); 	T_OTS232(); 	wait( 60); 	wait( 300); 	wait( 350); 	} fcombo T_OTS39 { 	T_OTS235(); 	set_val(T_OTS440,100); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	set_val(T_OTS440,100); 	wait( 70); 	T_OTS232(); 	set_val(T_OTS440,100); 	wait( 70); 	wait( 350); 	} fcombo T_OTS40 { 	if (T_OTS454) T_OTS655 = T_OTS562 + 3; 	else T_OTS655 = T_OTS562 - 3; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	set_val(T_OTS438, 100); 	set_val(T_OTS442,100); 	wait( 60); 	set_val(T_OTS442,100); 	T_OTS228(T_OTS1112, T_OTS639); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 80); 	set_val(T_OTS442,100); 	T_OTS228(T_OTS1112, T_OTS639); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	set_val(T_OTS442,100); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 300); 	wait( 350); 	} fcombo T_OTS41 { 	set_val(T_OTS440, 100); 	T_OTS234(); 	T_OTS228(0, 0); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS440, 100); 	T_OTS236(); 	T_OTS228(0, 0); 	wait( 65); 	set_val(T_OTS440, 100); 	T_OTS228(0, 0); 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	set_val(T_OTS443,0); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 200); 	set_val(T_OTS443,0); 	wait( 350); 	} fcombo T_OTS42 { 	if (T_OTS1010 == T_OTS301) T_OTS1015 = 200; 	else T_OTS1015 = 1; 	wait( T_OTS1015); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	wait( 350); 	} fcombo T_OTS43 { 	T_OTS235(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} fcombo T_OTS44 { 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	if (T_OTS1010 == T_OTS314) T_OTS231(); 	set_val(T_OTS442, 100); 	set_val(T_OTS443, 100); 	wait( 200); 	if (T_OTS1010 == T_OTS314) T_OTS231(); 	wait( 300); 	wait( 350); 	} fcombo T_OTS45 { 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS236(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	if (T_OTS1010 == T_OTS314) T_OTS231(); 	set_val(T_OTS442, 100); 	set_val(T_OTS443, 100); 	wait( 200); 	if (T_OTS1010 == T_OTS314) T_OTS231(); 	wait( 300); 	wait( 350); 	} fcombo T_OTS46 { 	call(T_OTS33)call(T_OTS35); 	} fcombo T_OTS47 {    T_OTS686 = FALSE; 	T_OTS228(T_OTS1011, T_OTS637); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(T_OTS1011, T_OTS637); 	T_OTS236(); 	T_OTS686 = FALSE; 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(T_OTS1011, T_OTS637); 	T_OTS232(); 	T_OTS686 = FALSE; 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442, 100); 	set_val(T_OTS443, 100); 	T_OTS228(inv(T_OTS1011), inv(T_OTS637)); 	T_OTS686 = FALSE; 	wait( 400); 	wait( 350); 	T_OTS686 = TRUE; 	} fcombo T_OTS48 { 	T_OTS228(T_OTS1011, T_OTS637); 	set_val(XB1_LS, 100); 	T_OTS234(); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(T_OTS1011, T_OTS637); 	T_OTS236(); 	set_val(XB1_LS, 100); 	wait( T_OTS1014 + random(1,5)); 	T_OTS228(T_OTS1011, T_OTS637); 	T_OTS232(); 	wait( T_OTS1014 + random(1,5)); 	set_val(T_OTS442, 100); 	set_val(T_OTS443, 100); 	if (T_OTS454) T_OTS655 = T_OTS562 + 4; 	else T_OTS655 = T_OTS562 - 4; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 220); 	if (T_OTS454) T_OTS655 = T_OTS562 + 4; 	else T_OTS655 = T_OTS562 - 4; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 60); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 600); 	wait( 350); 	} fcombo T_OTS49 { 	set_val(T_OTS439, 0); 	set_val(T_OTS438, 100); 	wait( 80); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 80); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} fcombo T_OTS50 { 	set_val(T_OTS438, 100); 	set_val(T_OTS443,100); 	wait( 60); 	set_val(T_OTS443,100); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	set_val(T_OTS443,100); 	wait( 60); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	set_val(T_OTS443,100); 	wait( 60); 	wait( 350); 	} fcombo T_OTS51 { 	set_val(T_OTS440,100); 	set_val(T_OTS441,100); 	T_OTS228(inv(T_OTS1011), inv(T_OTS637)); 	wait(278); 	T_OTS228(0,0); 	wait(200); 	if (T_OTS454) T_OTS655 = T_OTS562 - 2; 	else T_OTS655 = T_OTS562 + 2; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait(55); 	if (T_OTS454) T_OTS655 = T_OTS562 + 2; 	else T_OTS655 = T_OTS562 - 2; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	wait(600); 	wait( 350); 	} fcombo T_OTS52 { 	set_val(T_OTS438, 100); 	wait( 60); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 60); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} fcombo T_OTS53 { 	T_OTS228(inv(T_OTS1011), inv(T_OTS637)); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 100); 	wait( 60); 	T_OTS228(inv(T_OTS1011), inv(T_OTS637)); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 60); 	T_OTS228(inv(T_OTS1011), inv(T_OTS637)); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	T_OTS228(0, 0); 	wait( 300); 	wait( 350); 	} fcombo T_OTS54 { 	set_val(T_OTS442, 100); 	T_OTS232() 	wait(400); 	wait( 350); 	} fcombo T_OTS55 { 	set_val(T_OTS438, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} fcombo T_OTS56 { 	set_val(T_OTS438, 100); 	set_val(T_OTS442,100); 	wait( 60); 	set_val(T_OTS442,100); 	set_val(T_OTS438, 100); 	set_val(T_OTS439, 100); 	wait( 60); 	set_val(T_OTS442,100); 	set_val(T_OTS438, 0); 	set_val(T_OTS439, 100); 	wait( 60); 	wait( 350); 	} fcombo T_OTS57 { 	set_val(T_OTS440, 100); 	T_OTS234(); 	wait( 300); 	wait( 350); 	} fcombo T_OTS58 { 	T_OTS235(); 	wait( 70); 	T_OTS236(); 	wait( 70); 	T_OTS234(); 	wait( 70); 	wait( 350); 	} fcombo T_OTS59 { 	set_val(T_OTS440,100); 	T_OTS235(); 	wait( 70); 	set_val(T_OTS440,100); 	T_OTS236(); 	wait( 70); 	T_OTS234(); 	set_val(T_OTS440,100); 	wait(50); 	wait( 350); 	} fcombo T_OTS60 {   T_OTS228(0,0);   set_val(T_OTS440,100);   set_val(T_OTS443,100);   wait(200);   T_OTS228(0,0);   wait(200);   T_OTS233();   vm_tctrl(0);   wait(T_OTS1014);   T_OTS235();    vm_tctrl(0);   wait(T_OTS1014);   T_OTS232();    vm_tctrl(0);   wait(T_OTS1014);   T_OTS234();    vm_tctrl(0);   wait(T_OTS1014);   T_OTS454 = !T_OTS454   T_OTS232();    vm_tctrl(0);   wait(T_OTS1014);   T_OTS686 = TRUE;wait(350); 	} fcombo T_OTS61 { 	call(T_OTS83); 	T_OTS228(0, 0); 	call(T_OTS84); 	call(T_OTS84); 	call(T_OTS84); 	call(T_OTS84); 	call(T_OTS84); 	set_val(T_OTS442, 100); 	T_OTS235(); 	wait( 70); 	set_val(T_OTS442, 100); 	T_OTS236(); 	wait( 60); 	set_val(T_OTS442, 100); 	T_OTS234(); 	wait( 60); 	set_val(T_OTS442, 100); 	wait( 600); 	wait( 350); 	} fcombo T_OTS62 {   set_val(T_OTS442,100);   set_val(T_OTS441,100);   T_OTS233();   vm_tctrl(0);   wait(T_OTS1014);   T_OTS235();    set_val(T_OTS442,100);   set_val(T_OTS441,100);   vm_tctrl(0);   wait(T_OTS1014);   set_val(T_OTS442,100);   set_val(T_OTS441,100);   T_OTS232();    vm_tctrl(0);   wait(T_OTS1014); 	} fcombo T_OTS63 { 	wait( 100); 	T_OTS228(0, 0); 	T_OTS234(); 	wait( 70); 	T_OTS228(0, 0); 	T_OTS236()   	wait( 70); 	T_OTS228(0, 0); 	T_OTS234()   	wait( 70); 	T_OTS228(0, 0); 	T_OTS236()   	wait( 70); 	T_OTS228(0, 0); 	T_OTS235(); 	wait( 70); 	T_OTS228(0, 0); 	wait( 350); 	} fcombo T_OTS64 { 	set_val(PS4_R3,100); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 70); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 400); 	wait( 350); 	} fcombo T_OTS65 { 	call(T_OTS83); 	T_OTS228(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (T_OTS454) T_OTS655 = T_OTS562 + 1; 	else T_OTS655 = T_OTS562 - 1; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 70); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 400); 	wait( 350); 	} fcombo T_OTS66 { 	call(T_OTS83); 	T_OTS228(0,0); 	set_val(T_OTS442,100); 	set_val(T_OTS443,100); 	wait( 750); 	} fcombo T_OTS67 { 	set_val(PS4_R3,100); 	if (T_OTS454) T_OTS655 = T_OTS562 + 2; 	else T_OTS655 = T_OTS562 - 2; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 70); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 400); 	wait( 350); 	} fcombo T_OTS68 { 	set_val(T_OTS442,100); 	set_val(PS4_R3,100); 	if (T_OTS454) T_OTS655 = T_OTS562 ; 	else T_OTS655 = T_OTS562; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 70); 	set_val(T_OTS442,100); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 400); 	wait( 350); 	} fcombo T_OTS69 { 	set_val(T_OTS442,100); 	set_val(PS4_R3,100); 	if (T_OTS454) T_OTS655 = T_OTS562 ; 	else T_OTS655 = T_OTS562; 	T_OTS223(T_OTS655); 	T_OTS228(T_OTS1112, T_OTS639); 	T_OTS228(T_OTS1112, T_OTS639); 	wait( 70); 	set_val(T_OTS442,100); 	T_OTS228(T_OTS1112, T_OTS639); 	wait(800); 	call(T_OTS98); 	} fcombo T_OTS70 { 	T_OTS228(0,0); 	set_val(T_OTS441,100); 	set_val(T_OTS440,100); 	T_OTS232(); 	wait( 350); 	wait( 350); 	set_val(T_OTS441,100); 	set_val(T_OTS440,100); 	wait( 400); 	} int T_OTS130 ; int T_OTS722 ; int T_OTS723 ; int T_OTS724; int T_OTS725; function T_OTS116(T_OTS117){ 	T_OTS722 = 2; 	T_OTS723 = 987654; 	T_OTS130 = 54321; 	T_OTS724 = (T_OTS117 >> T_OTS722) | (T_OTS117 << (32 - T_OTS722)); 	T_OTS725 = (((T_OTS724 >> ((T_OTS724 & 0xF) % 13)) & 0x7FFFF) + T_OTS130) % T_OTS723 + 123456; 	return T_OTS725; 	} define T_OTS727 = -1; define T_OTS505 = -2; define T_OTS729 = -3; define T_OTS730 = 0; define T_OTS506 = 1; function T_OTS118(T_OTS117, T_OTS120, T_OTS121) { 	if(T_OTS117 > T_OTS121) return T_OTS120; 	if(T_OTS117 < T_OTS120) return T_OTS121; 	return T_OTS117; 	} int T_OTS734,T_OTS735; function T_OTS122(T_OTS123,T_OTS124,T_OTS125,T_OTS126,T_OTS127,T_OTS128){ 	if(!T_OTS128){ 		print(T_OTS131(T_OTS129(T_OTS123),T_OTS126,T_OTS124),T_OTS125,T_OTS126,T_OTS127,T_OTS123)     	} 	else{ 		if(T_OTS123 < 0){ 			putc_oled(1,45); 					} 		if(T_OTS123){ 			for(T_OTS734 = T_OTS135(T_OTS123) + T_OTS735 = (T_OTS123 < 0 ),T_OTS123 = abs(T_OTS123); 			T_OTS123 > 0; 			T_OTS734-- , T_OTS735++){ 				putc_oled(T_OTS734,T_OTS123%10 + 48); 				T_OTS123 = T_OTS123/10; 							} 					} 		else{ 			putc_oled(1,48); 			T_OTS735 = 1         		} 		puts_oled(T_OTS131(T_OTS735,T_OTS126,T_OTS124),T_OTS125,T_OTS126,T_OTS735 ,T_OTS127); 			} 	} int T_OTS756; function T_OTS129(T_OTS130) { 	T_OTS756 = 0; 	do { 		T_OTS130++; 		T_OTS756++; 			} 	while (duint8(T_OTS130)); 	return T_OTS756; 	} function T_OTS131(T_OTS132,T_OTS126,T_OTS124) { 	if(T_OTS124 == -3){ 		return 128 - ((T_OTS132 * (7 + (T_OTS126 > 1) + T_OTS126 * 4)) + 3 ); 			} 	if(T_OTS124 == -2){ 		return 64 - ((T_OTS132 * (7 + (T_OTS126 > 1) + T_OTS126 * 4)) / 2); 			} 	if(T_OTS124 == -1){ 		return 3 	} 	return T_OTS124; 	} function T_OTS135(T_OTS136) { 	for(T_OTS734 = 1; 	T_OTS734 < 11; 	T_OTS734++){ 		if(!(abs(T_OTS136) / pow(10,T_OTS734))){ 			return T_OTS734; 			break; 					} 			} 	return 1; 	} function T_OTS137() { 	if (get_ival(T_OTS438)) { 		set_val(T_OTS438, 0); 		if (get_ival(T_OTS440)) T_OTS763 = 50; 		if (!get_ival(T_OTS440)) T_OTS763 = 410; 		combo_run(T_OTS71); 			} 	if (T_OTS762 > 0) set_polar(POLAR_LS, T_OTS762 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) T_OTS762 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) T_OTS762 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) T_OTS762 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) T_OTS762 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) T_OTS762 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) T_OTS762 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) T_OTS762 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) T_OTS762 = 270; } int T_OTS763; int T_OTS526; int T_OTS762; fcombo T_OTS71 { 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( T_OTS763); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	T_OTS526 = !T_OTS526; } define T_OTS766 = 19; function T_OTS138(T_OTS139, T_OTS140) { 	if (T_OTS343 == T_OTS140) { 		if (event_press(PS4_RIGHT)) { 			T_OTS139 = clamp(T_OTS139 + 1, 0, T_OTS769[T_OTS343]); 			T_OTS348 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			T_OTS139 = clamp(T_OTS139 - 1, 0, T_OTS769[T_OTS343]); 			T_OTS348 = TRUE; 					} 		if (T_OTS343 == 0) { 			print(T_OTS203(T_OTS156(T_OTS773[T_OTS349]) ,OLED_FONT_SMALL_WIDTH),T_OTS766  ,OLED_FONT_SMALL , OLED_WHITE ,T_OTS773[T_OTS349]); 					} 		else if (T_OTS343 == 1) { 			print(T_OTS203(T_OTS156(T_OTS775[T_OTS350]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS775[T_OTS350]); 					} 		else if (T_OTS343 == 2) { 			print(T_OTS203(T_OTS156(T_OTS775[T_OTS351]) ,OLED_FONT_SMALL_WIDTH ),T_OTS766  ,OLED_FONT_SMALL , OLED_WHITE ,T_OTS775[T_OTS351]); 					} 		else if (T_OTS343 == 5) { 			print(T_OTS203(T_OTS156(T_OTS779[T_OTS354]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS779[T_OTS354]); 					} 		else if (T_OTS343 == 6) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS355]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS355]); 					} 		else if (T_OTS343 == 7) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS356]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS356]); 					} 		else if (T_OTS343 == 8) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS357]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS357]); 					} 		else if (T_OTS343 == 9) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS358]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS358]); 					} 		else if (T_OTS343 == 20) { 			print(T_OTS203(T_OTS156(T_OTS789[T_OTS110]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS789[T_OTS110]); 					} 		else { 			if (T_OTS139 == 1)        print(T_OTS203(T_OTS156(T_OTS791[1]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS791[1])      else        print(T_OTS203(T_OTS156(T_OTS791[0]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS791[0])     		} 			} 	return T_OTS139; 	} function T_OTS141(T_OTS139, T_OTS140) { 	if (T_OTS344 == T_OTS140) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				T_OTS139 += T_OTS797[T_OTS344][2]  				        T_OTS348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				T_OTS139 -= T_OTS797[T_OTS344][2]  				        T_OTS348 = TRUE; 							} 			T_OTS139 = clamp(T_OTS139, T_OTS797[T_OTS344][0], T_OTS797[T_OTS344][1]); 		} 		if (T_OTS344 == 8) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS375]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS375])     		} 		else if (T_OTS344 == 9) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS376]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS376])     		} 		else if (T_OTS344 == 10) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS377]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS377])     		} 		else if (T_OTS344 == 11) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS378]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS378])     		} 		else if (T_OTS344 == 12) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS379]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS379])     		} 		else if (T_OTS344 == 13) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS380]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS380])     		} 		else if (T_OTS344 == 14) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS381]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS381])     		} 		else if (T_OTS344 == 15) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS382]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS382])     		} 		else if (T_OTS344 == 16) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS383]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS383])     		} 		else if (T_OTS344 == 17) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS264]),OLED_FONT_SMALL_WIDTH ),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS781[T_OTS264])  		} 		else if(T_OTS344 == 18){ 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS265]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS781[T_OTS265])  		} 		else if(T_OTS344 == 19){ 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS266]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS781[T_OTS266])  		} 		else if(T_OTS344 == 20){ 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS267]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS781[T_OTS267])  		} 		else if(T_OTS344 == 21){ 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS268]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS816[T_OTS268])       		} 		else if(T_OTS344 == 22){ 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS397]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS397])     		} 		else if (T_OTS344 == 23) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS398]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS398])     		} 		else if (T_OTS344 == 24) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS399]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS399])     		} 		else if (T_OTS344 == 25) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS400]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS400])     		} 		else if (T_OTS344 == 26) { 			print(T_OTS203(T_OTS156(T_OTS781[T_OTS401]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS781[T_OTS401])     		} 		else if (T_OTS344 == 27) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS402]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS402])     		} 		else if (T_OTS344 == 28) { 			print(T_OTS203(T_OTS156(T_OTS840[T_OTS403]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS840[T_OTS403])     		} 		else if (T_OTS344 == 29) { 			print(T_OTS203(T_OTS156(T_OTS842[T_OTS404]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS842[T_OTS404])     		} 		else if (T_OTS344 == 30) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS405]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS405])     		} 		else if (T_OTS344 == 31) { 			print(T_OTS203(T_OTS156(T_OTS840[T_OTS406]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS840[T_OTS406])     		} 		else if (T_OTS344 == 32) { 			print(T_OTS203(T_OTS156(T_OTS842[T_OTS407]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS842[T_OTS407])     		} 		else if (T_OTS344 == 33) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS408]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS408])     		} 		else if (T_OTS344 == 34) { 			print(T_OTS203(T_OTS156(T_OTS840[T_OTS409]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS840[T_OTS409])     		} 		else if (T_OTS344 == 35) { 			print(T_OTS203(T_OTS156(T_OTS842[T_OTS410]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS842[T_OTS410])     		} 		else if (T_OTS344 == 36) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS411]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS411])     		} 		else if (T_OTS344 == 37) { 			print(T_OTS203(T_OTS156(T_OTS840[T_OTS412]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS840[T_OTS412])     		} 		else if (T_OTS344 == 38) { 			print(T_OTS203(T_OTS156(T_OTS842[T_OTS413]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS842[T_OTS413])     		} 		else if (T_OTS344 == 41) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS416]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS416])     		} 		else if (T_OTS344 == 48) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS424]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS424])     		} 		else if (T_OTS344 == 49) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS425]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS425])     		} 		else if (T_OTS344 == 50) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS426]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS426])     		} 		else if (T_OTS344 == 51) { 			print(T_OTS203(T_OTS156(T_OTS816[T_OTS427]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS816[T_OTS427])     		} 		else if(T_OTS344 == 0){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS430]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS430])  		} 		else if(T_OTS344 == 1){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS431]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS431])  		} 		else if(T_OTS344 == 2){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS432]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS432])  		} 		else if(T_OTS344 == 3){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS433]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS433])  		} 		else if(T_OTS344 == 4){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS434]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS434])  		} 		else if(T_OTS344 == 5){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS435]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS435])  		} 		else if(T_OTS344 == 6){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS436]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS436])  		} 		else if(T_OTS344 == 7){ 			print(T_OTS203(T_OTS156(T_OTS872[T_OTS437]),OLED_FONT_SMALL_WIDTH),T_OTS766,OLED_FONT_SMALL,OLED_WHITE,T_OTS872[T_OTS437])  		} 		else{ 			if (T_OTS139 == 1)        print(T_OTS203(T_OTS156(T_OTS791[1]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS791[1])      else        print(T_OTS203(T_OTS156(T_OTS791[0]), OLED_FONT_SMALL_WIDTH), T_OTS766, OLED_FONT_SMALL, OLED_WHITE, T_OTS791[0])     		} 		T_OTS159(0); 			} 	return T_OTS139; 	} function T_OTS144(T_OTS139, T_OTS140) { 	if (T_OTS344 == T_OTS140) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				T_OTS139 += T_OTS797[T_OTS344][2]  				        T_OTS348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				T_OTS139 -= T_OTS797[T_OTS344][2]  				        T_OTS348 = TRUE; 							} 			if (event_press(PS4_UP)) { 				T_OTS139 += T_OTS797[T_OTS344][3]  				        T_OTS348 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				T_OTS139 -= T_OTS797[T_OTS344][3]  				        T_OTS348 = TRUE; 							} 			T_OTS139 = clamp(T_OTS139, T_OTS797[T_OTS344][0], T_OTS797[T_OTS344][1]); 		} 		T_OTS206(T_OTS139, T_OTS209(T_OTS139)); 	} 	return T_OTS139; 	} int T_OTS899, T_OTS900, T_OTS901; function T_OTS147(T_OTS117, T_OTS149, T_OTS150, T_OTS151, T_OTS126) { 	T_OTS900 = 1; 	T_OTS901 = 10000; 	if (T_OTS117 < 0)  	  { 		putc_oled(T_OTS900, 45); 		T_OTS900 += 1; 		T_OTS117 = abs(T_OTS117); 			} 	for (T_OTS899 = 5; 	T_OTS899 >= 1; 	T_OTS899--) { 		if (T_OTS149 >= T_OTS899) { 			putc_oled(T_OTS900, T_OTS907[T_OTS117 / T_OTS901]); 			T_OTS117 = T_OTS117 % T_OTS901; 			T_OTS900 += 1; 					} 		T_OTS901 /= 10; 			} 	puts_oled(T_OTS150, T_OTS151, T_OTS126, T_OTS900 - 1, OLED_WHITE); } const string T_OTS543 = " No Edit Variable"; const string T_OTS542 = " A/CROSS to Edit "; const string T_OTS538 = "MOD;"; const string T_OTS540 = "MSL;"; int T_OTS911; function T_OTS153(T_OTS136) { 	T_OTS136 = abs(T_OTS136); 	if (T_OTS136 / 10000 > 0) return 5; 	if (T_OTS136 / 1000 > 0) return 4; 	if (T_OTS136 / 100 > 0) return 3; 	if (T_OTS136 / 10 > 0) return 2; 	return 1; 	} const int8 T_OTS907[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int T_OTS913, T_OTS914; const image T_OTS916 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; fcombo T_OTS72 { 	call(T_OTS73); 	T_OTS155(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, T_OTS916[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, T_OTS916[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(T_OTS74); 	vm_tctrl(0);wait( 1000); 	T_OTS345 = TRUE; 	} fcombo T_OTS73 { 	cls_oled(OLED_BLACK); 	} enum { 	T_OTS918 = -2, T_OTS919, T_OTS920 = 5, T_OTS921 = -1, T_OTS922 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); fcombo T_OTS74 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 T_OTS1328[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 T_OTS1329[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 T_OTS1330[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 T_OTS1331[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int T_OTS923; int T_OTS924; int T_OTS925; int T_OTS926; int T_OTS927; int T_OTS928; int T_OTS929; function T_OTS155() { 	T_OTS929 = 3; 	T_OTS927 = T_OTS929 * T_OTS1331[T_OTS928]; 	T_OTS926 = T_OTS929 * T_OTS1328[T_OTS928]; 	T_OTS924 = ((T_OTS927 * T_OTS1330[T_OTS923]) / 328) - ((T_OTS926 * T_OTS1329[T_OTS923]) / 328); 	T_OTS925 = ((T_OTS927 * T_OTS1329[T_OTS923]) / 328) + ((T_OTS926 * T_OTS1330[T_OTS923]) / 328); 	T_OTS927 = T_OTS924; 	T_OTS926 = T_OTS925; 	T_OTS928 += 1; 	T_OTS923 += 45; 	if(T_OTS928 >= 360) { 		T_OTS928 %= 360; 			} 	if(T_OTS923 >= 360) { 		T_OTS923 %= 360; 			} 	pixel_oled(64 + (((T_OTS927 / T_OTS929) * 30) / 328), 32 + (((T_OTS926 / T_OTS929) * 30) / 328), OLED_WHITE); 	} int T_OTS933; function T_OTS156(T_OTS130) { 	T_OTS933 = 0; 	do { 		T_OTS130++; 		T_OTS933++; 			} 	while (duint8(T_OTS130)); 	return T_OTS933; 	} int T_OTS936; const uint8 T_OTS1332[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function T_OTS158() { 	for (T_OTS936 = 0; 	T_OTS936 < sizeof(T_OTS1332) / sizeof(T_OTS1332[0]); 	T_OTS936++) { 		if (get_ival(T_OTS1332[T_OTS936]) || event_press(T_OTS1332[T_OTS936])) { 			set_val(T_OTS1332[T_OTS936], 0); 		} 			} 	} define T_OTS937 = 131; define T_OTS938 = 132; define T_OTS939 = 133; define T_OTS940 = 134; define T_OTS941 = 130; define T_OTS942 = 89; define T_OTS943 = 127; define T_OTS944 = 65; int T_OTS945; int T_OTS946; int T_OTS947 = 1; define T_OTS948 = 36; const string T_OTS949 = "Hold LT/L2 +"; function T_OTS159(T_OTS160) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, T_OTS949[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, T_OTS947); 	putc_oled(1, T_OTS939); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, T_OTS945); 	putc_oled(1, T_OTS940); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, T_OTS946); 	if (T_OTS160) { 		putc_oled(1, T_OTS937); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, T_OTS945); 		putc_oled(1, T_OTS938); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, T_OTS946); 			} 	} const uint8 T_OTS1334 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function T_OTS161() { 	T_OTS959 = sizeof(data); 	T_OTS483 = get_pvar(SPVAR_1,0,1,0); 	T_OTS489 = get_pvar(SPVAR_2,0,1,0); 	T_OTS482 = get_pvar(SPVAR_3,0x2B67, 0x1869F,0x2B67); 	T_OTS163(); 	if (T_OTS188(0, 1, 0)) { 		T_OTS354 = T_OTS188(  0, 6, 0); 		T_OTS351 = T_OTS188(0, 3, 0); 		T_OTS352 = T_OTS188(0,1,0); 		T_OTS353 = T_OTS188(0,1,0); 		T_OTS264 = T_OTS188(0, 70, 0); 		T_OTS265 = T_OTS188(0, 70, 0); 		T_OTS266 = T_OTS188(0, 70, 0); 		T_OTS267 = T_OTS188(0, 70, 0); 		T_OTS268 = T_OTS188(0, 22, 8); 		T_OTS355 = T_OTS188(0, 70, 0); 		T_OTS356 = T_OTS188(0, 70, 0); 		T_OTS357 = T_OTS188(0, 70, 0); 		T_OTS358 = T_OTS188(0, 70, 0); 		T_OTS359 = T_OTS188(0, 1, 0); 		T_OTS360 = T_OTS188(0, 1, 0); 		T_OTS361 = T_OTS188(0, 1, 0); 		T_OTS362 = T_OTS188(0, 1, 0); 		T_OTS370 = T_OTS188(0, 1, 0); 		T_OTS397 = T_OTS188(0, 70, 0); 		T_OTS398 = T_OTS188(0, 70, 0); 		T_OTS399 = T_OTS188(0, 70, 0); 		T_OTS400 = T_OTS188(0, 70, 0); 		T_OTS401 = T_OTS188(0, 70, 0); 		T_OTS402 = T_OTS188(1, 25, 1); 		T_OTS403 = T_OTS188(0, 1, 0); 		T_OTS404 = T_OTS188(0, 1, 0); 		T_OTS405 = T_OTS188(1, 25, 5); 		T_OTS406 = T_OTS188(0, 1, 0); 		T_OTS407 = T_OTS188(0, 1, 0); 		T_OTS408 = T_OTS188(0, 25, 2); 		T_OTS409 = T_OTS188(0, 1, 0); 		T_OTS410 = T_OTS188(0, 1, 1); 		T_OTS411 = T_OTS188(1, 25, 8); 		T_OTS412 = T_OTS188(0, 1, 0); 		T_OTS413 = T_OTS188(0, 1, 1); 		T_OTS414 = T_OTS188(350, 600, 350); 		T_OTS415 = T_OTS188(350, 600, 430); 		T_OTS416 = T_OTS188(0, 22, 0); 		T_OTS417 = T_OTS188(0, 1, 0); 		T_OTS418 = T_OTS188(-100, 300, 0); 		T_OTS363 = T_OTS188(0, 1, 0); 		T_OTS364 = T_OTS188(0, 1, 0); 		T_OTS365 = T_OTS188(0, 1, 0); 		T_OTS366 = T_OTS188(0, 1, 0); 		T_OTS367 = T_OTS188(0, 1, 0); 		T_OTS419 = T_OTS188(-150, 150, 0); 		T_OTS420 = T_OTS188(-150, 150, 0); 		T_OTS421 = T_OTS188(-150, 150, 0); 		T_OTS422 = T_OTS188(0, 1, 0); 		T_OTS423 = T_OTS188(-150, 150, 0); 		T_OTS424 = T_OTS188(0, 22, 0); 		T_OTS425 = T_OTS188(0, 22, 0); 		T_OTS426 = T_OTS188(0, 22, 0); 		T_OTS427 = T_OTS188(0, 22, 0); 		T_OTS577 = T_OTS188(60, 400, 235); 		T_OTS429 = T_OTS188(0, 1, 0); 		T_OTS428 = T_OTS188(0, 1, 0); 		T_OTS350 = T_OTS188(0, 3, 0); 		T_OTS375 = T_OTS188(0, 70, 0); 		T_OTS376 = T_OTS188(0, 70, 0); 		T_OTS377 = T_OTS188(0, 70, 0); 		T_OTS380 = T_OTS188(0, 70, 0); 		T_OTS381 = T_OTS188(0, 70, 0); 		T_OTS382 = T_OTS188(0, 70, 0); 		T_OTS383 = T_OTS188(0, 22, 8); 		T_OTS368 = T_OTS188(0, 1, 0); 		T_OTS378 = T_OTS188(0, 70, 0); 		T_OTS379 = T_OTS188(0, 70, 0); 		T_OTS569 = T_OTS188(0, 2500, 400); 		T_OTS1209 = T_OTS188(0, 1, 0); 		T_OTS1210 = T_OTS188(0, 1,  0); 		T_OTS1203 = T_OTS188(0, 1, 0); 		T_OTS110 = T_OTS188(0, 10, 0); 		T_OTS396 = T_OTS188(0, 1, 0); 		T_OTS349 = T_OTS188(0, 2, 0); 		T_OTS430 = T_OTS188(0, 9, 9); 		T_OTS431 = T_OTS188(0, 9, 8); 		T_OTS432 = T_OTS188(0, 9, 3); 		T_OTS433 = T_OTS188(0, 9, 1); 		T_OTS434 = T_OTS188(0, 9, 4); 		T_OTS435 = T_OTS188(0, 9, 0); 		T_OTS436 = T_OTS188(0, 9, 7); 		T_OTS437 = T_OTS188(0, 9, 6); 		T_OTS371    = T_OTS188(0, 1, 0); 		T_OTS372    = T_OTS188(0, 1, 0); 		T_OTS373     = T_OTS188(0, 1, 0); 		T_OTS384     = T_OTS188(60, 500, 120); 		T_OTS385     = T_OTS188(60, 500, 350); 		T_OTS386    = T_OTS188(0, 1, 0); 		T_OTS387 = T_OTS188(0, 1, 0); 		T_OTS388     = T_OTS188(50, 250, 80); 		T_OTS389     = T_OTS188(100, 850, 180); 		T_OTS390 = T_OTS188(0, 1, 0); 		T_OTS391 = T_OTS188(0, 1, 0); 		T_OTS392    = T_OTS188(0, 1, 0); 		T_OTS393        = T_OTS188(80, 500, 120); 		T_OTS394        = T_OTS188(80, 500, 350); 		T_OTS395       = T_OTS188(0, 1, 0); 		T_OTS446           = T_OTS188(3, 60, 3); 		T_OTS29           = T_OTS188(0, 1, 0); 		T_OTS451         = T_OTS188(0, 1, 0); 		T_OTS449       = T_OTS188(0, 1, 0); 	} 	else{ 		T_OTS354 = 0; 		T_OTS351 = 0; 		T_OTS352 = 0; 		T_OTS353 = 0; 		T_OTS264 = 0; 		T_OTS265 = 0; 		T_OTS266 = 0; 		T_OTS267 = 0; 		T_OTS268 = 8; 		T_OTS355 = 0; 		T_OTS356 = 0; 		T_OTS357 = 0; 		T_OTS358 = 0; 		T_OTS359 = 0; 		T_OTS360 = 0; 		T_OTS361 = 0; 		T_OTS362 = 0; 		T_OTS370 = 0; 		T_OTS397 = 0; 		T_OTS398 = 0; 		T_OTS399 = 0; 		T_OTS400 = 0; 		T_OTS401 = 0; 		T_OTS402 = 1; 		T_OTS403 = 0; 		T_OTS404 = 0; 		T_OTS405 = 5; 		T_OTS406 = 0; 		T_OTS407 = 0; 		T_OTS408 = 2; 		T_OTS409 = 0; 		T_OTS410 = 1; 		T_OTS411 = 8; 		T_OTS412 = 0; 		T_OTS413 = 1; 		T_OTS414 = 350; 		T_OTS415 = 430; 		T_OTS416 = 0; 		T_OTS417 = 0; 		T_OTS418 = 0; 		T_OTS363 = 0; 		T_OTS364 = 0; 		T_OTS365 = 0; 		T_OTS366 = 0; 		T_OTS367 = 0; 		T_OTS419 = 0; 		T_OTS420 = 0; 		T_OTS421 = 0; 		T_OTS422 = 0; 		T_OTS423 = 0; 		T_OTS424 = 0; 		T_OTS425 = 0; 		T_OTS426 = 0; 		T_OTS427 = 0; 		T_OTS577 = 235; 		T_OTS429 = 0; 		T_OTS428 = 0; 		T_OTS350 = 0; 		T_OTS375 = 0; 		T_OTS376 = 0; 		T_OTS377 = 0; 		T_OTS380 = 0; 		T_OTS381 = 0; 		T_OTS382 = 0; 		T_OTS383 = 8; 		T_OTS368 = 0; 		T_OTS378 = 0; 		T_OTS379 = 0; 		T_OTS569 = 0; 		T_OTS1209 = 0; 		T_OTS1210 = 0; 		T_OTS1203 = 0; 		T_OTS110 = 0; 		T_OTS396 = 0; 		T_OTS349 = 0; 		T_OTS430 = 9; 		T_OTS431 = 8; 		T_OTS432 = 3; 		T_OTS433 = 1; 		T_OTS434 = 4; 		T_OTS435 = 0; 		T_OTS436 = 7; 		T_OTS437 = 6; 		T_OTS371 = 0; 		T_OTS372 = 0; 		T_OTS373 = 0; 		T_OTS384 = 120; 		T_OTS385 = 350; 		T_OTS386 = 0; 		T_OTS387 = 0; 		T_OTS388 = 80; 		T_OTS389 = 180; 		T_OTS390 = 0; 		T_OTS391 = 0; 		T_OTS392 = 0; 		T_OTS393 = 120; 		T_OTS394 = 360; 		T_OTS395 = 0; 		T_OTS446     = 3; 		T_OTS29     = 0; 		T_OTS451     = 0; 		T_OTS449     = 0; 			} 	if (T_OTS349 == 0) { 		T_OTS438 = PS4_CIRCLE; 		T_OTS439 = PS4_CROSS; 		T_OTS440 = PS4_L1; 		T_OTS441 = PS4_R1; 		T_OTS442 = PS4_L2; 		T_OTS443 = PS4_R2; 		T_OTS444 = PS4_SQUARE; 		T_OTS445 = PS4_TRIANGLE; 			} 	else if (T_OTS349 == 1) { 		T_OTS438      = PS4_SQUARE; 		T_OTS439      = PS4_CROSS ; 		T_OTS440    = PS4_L1    ; 		T_OTS441  = PS4_R1; 		T_OTS442    = PS4_L2; 		T_OTS443    = PS4_R2; 		T_OTS444     = PS4_CIRCLE; 		T_OTS445  = PS4_TRIANGLE; 	} 	else if (T_OTS349 == 2) { 		T_OTS438 = T_OTS1334[T_OTS430]; 		T_OTS439 = T_OTS1334[T_OTS431]; 		T_OTS440 = T_OTS1334[T_OTS432]; 		T_OTS441 = T_OTS1334[T_OTS433]; 		T_OTS442 = T_OTS1334[T_OTS434]; 		T_OTS443 = T_OTS1334[T_OTS435]; 		T_OTS444 = T_OTS1334[T_OTS436]; 		T_OTS445 = T_OTS1334[T_OTS437]; 			} 	} function T_OTS162() { 	T_OTS163(); 	T_OTS186(   1,0,     1); 	T_OTS186(T_OTS354, 0, 6); 	T_OTS186(T_OTS351, 0, 3); 	T_OTS186(T_OTS352, 0 , 1); 	T_OTS186(T_OTS353, 0 , 1); 	T_OTS186(T_OTS264, 0, 70); 	T_OTS186(T_OTS265, 0, 70); 	T_OTS186(T_OTS266, 0, 70); 	T_OTS186(T_OTS267, 0, 70); 	T_OTS186(T_OTS268, 0, 22); 	T_OTS186(T_OTS355, 0, 70); 	T_OTS186(T_OTS356, 0, 70); 	T_OTS186(T_OTS357, 0, 70); 	T_OTS186(T_OTS358, 0, 70); 	T_OTS186(T_OTS359, 0, 1); 	T_OTS186(T_OTS360, 0, 1); 	T_OTS186(T_OTS361, 0, 1); 	T_OTS186(T_OTS362, 0, 1); 	T_OTS186(T_OTS370, 0, 1); 	T_OTS186(T_OTS397, 0, 70); 	T_OTS186(T_OTS398, 0, 70); 	T_OTS186(T_OTS399, 0, 70); 	T_OTS186(T_OTS400, 0, 70); 	T_OTS186(T_OTS401, 0, 70); 	T_OTS186(T_OTS402, 1, 25); 	T_OTS186(T_OTS403, 0, 1); 	T_OTS186(T_OTS404, 0, 1); 	T_OTS186(T_OTS405, 1, 25); 	T_OTS186(T_OTS406, 0, 1); 	T_OTS186(T_OTS407, 0, 1); 	T_OTS186(T_OTS408, 0, 25); 	T_OTS186(T_OTS409, 0, 1); 	T_OTS186(T_OTS410, 0, 1); 	T_OTS186(T_OTS411, 1, 25); 	T_OTS186(T_OTS412, 0, 1); 	T_OTS186(T_OTS413, 0, 1); 	T_OTS186(T_OTS414, 350, 600); 	T_OTS186(T_OTS415, 350, 600); 	T_OTS186(T_OTS416, 0, 22); 	T_OTS186(T_OTS417, 0, 1); 	T_OTS186(T_OTS418, -100, 300); 	T_OTS186(T_OTS363, 0, 1); 	T_OTS186(T_OTS364, 0, 1); 	T_OTS186(T_OTS365, 0, 1); 	T_OTS186(T_OTS366, 0, 1); 	T_OTS186(T_OTS367, 0, 1); 	T_OTS186(T_OTS419, -150, 150); 	T_OTS186(T_OTS420, -150, 150); 	T_OTS186(T_OTS421, -150, 150); 	T_OTS186(T_OTS422, 0, 1); 	T_OTS186(T_OTS423, -150, 150); 	T_OTS186(T_OTS424, 0, 22); 	T_OTS186(T_OTS425, 0, 22); 	T_OTS186(T_OTS426, 0, 22); 	T_OTS186(T_OTS427, 0, 22); 	T_OTS186(T_OTS577, 60, 400); 	T_OTS186(T_OTS429, 0, 1); 	T_OTS186(T_OTS428, 0, 1); 	T_OTS186(T_OTS350, 0, 3); 	T_OTS186(T_OTS375, 0, 70); 	T_OTS186(T_OTS376, 0, 70); 	T_OTS186(T_OTS377, 0, 70); 	T_OTS186(T_OTS380, 0, 70); 	T_OTS186(T_OTS381, 0, 70); 	T_OTS186(T_OTS382, 0, 70); 	T_OTS186(T_OTS383, 0, 22); 	T_OTS186(T_OTS368, 0, 1); 	T_OTS186(T_OTS378, 0, 70); 	T_OTS186(T_OTS379, 0, 70); 	T_OTS186(T_OTS569, 0, 2500); 	T_OTS186(T_OTS1209, 0, 1); 	T_OTS186(T_OTS1210, 0, 1); 	T_OTS186(T_OTS1203, 0, 1); 	T_OTS186(T_OTS110, 0, 10); 	T_OTS186(T_OTS396, 0, 1); 	T_OTS186(T_OTS349, 0, 2); 	T_OTS186(T_OTS430, 0, 9); 	T_OTS186(T_OTS431, 0, 9); 	T_OTS186(T_OTS432, 0, 9); 	T_OTS186(T_OTS433, 0, 9); 	T_OTS186(T_OTS434, 0, 9); 	T_OTS186(T_OTS435, 0, 9); 	T_OTS186(T_OTS436, 0, 9); 	T_OTS186(T_OTS437, 0, 9); 	T_OTS186(T_OTS371,    0, 1); 	T_OTS186(T_OTS372,    0, 1); 	T_OTS186(T_OTS373,     0, 1); 	T_OTS186(T_OTS384,     60, 500); 	T_OTS186(T_OTS385,     60, 500); 	T_OTS186(T_OTS386,    0, 1); 	T_OTS186(T_OTS387, 0, 1); 	T_OTS186(T_OTS388,     50, 250); 	T_OTS186(T_OTS389,     100, 850); 	T_OTS186(T_OTS390, 0, 1); 	T_OTS186(T_OTS391, 0, 1); 	T_OTS186(T_OTS392,    0, 1); 	T_OTS186(T_OTS393,        80, 500); 	T_OTS186(T_OTS394,        80, 500); 	T_OTS186(T_OTS395,       0, 1); 	T_OTS186(T_OTS446 ,         3,60); 	T_OTS186(T_OTS29,           0,1); 	T_OTS186(T_OTS451,           0,1); 	T_OTS186(T_OTS449,           0,1); 	} function T_OTS163() { 	T_OTS966 = SPVAR_4; 	T_OTS967 = 0; 	T_OTS969 = 0; 	} int T_OTS967,  T_OTS966, T_OTS969, T_OTS970, T_OTS971; function T_OTS164(T_OTS165) { 	T_OTS970 = 0; 	while (T_OTS165) { 		T_OTS970++; 		T_OTS165 = abs(T_OTS165 >> 1); 	} 	return T_OTS970; 	} function T_OTS166(T_OTS167, T_OTS168) { 	T_OTS970 = max(T_OTS164(T_OTS167), T_OTS164(T_OTS168)); 	if (T_OTS169(T_OTS167, T_OTS168)) { 		T_OTS970++; 	} 	return T_OTS970; 	} function T_OTS169(T_OTS167, T_OTS168) { 	return T_OTS167 < 0 || T_OTS168 < 0; 	} function T_OTS172(T_OTS173) { 	return 1 << clamp(T_OTS173 - 1, 0, 31); 	} function T_OTS174(T_OTS173) { 	if (T_OTS173 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - T_OTS173); } function T_OTS176(T_OTS173) { 	return T_OTS174(T_OTS173 - 1); 	} function T_OTS178(T_OTS165, T_OTS173) { 	if (T_OTS165 < 0) { 		return (abs(T_OTS165) & T_OTS176(T_OTS173)) | T_OTS172(T_OTS173); 	} 	return T_OTS165 & T_OTS176(T_OTS173); } function T_OTS181(T_OTS165, T_OTS173) { 	if (T_OTS165 & T_OTS172(T_OTS173)) { 		return 0 - (T_OTS165 & T_OTS176(T_OTS173)); 	} 	return T_OTS165 & T_OTS176(T_OTS173); } function T_OTS184(T_OTS185) { 	return get_pvar(T_OTS185, 0x80000000, 0x7FFFFFFF, 0); 	} function T_OTS186(T_OTS165, min, max) { 	T_OTS971 = T_OTS166(min, max); 	T_OTS165 = clamp(T_OTS165, min, max); 	if (T_OTS169(min, max)) { 		T_OTS165 = T_OTS178(T_OTS165, T_OTS971); 	} 	T_OTS165 = T_OTS165 & T_OTS174(T_OTS971); 	if (T_OTS971 >= 32 - T_OTS967) { 		T_OTS969 = T_OTS969 | (T_OTS165 << T_OTS967); 		set_pvar(T_OTS966, T_OTS969); 		T_OTS966++; 		T_OTS971 -= (32 - T_OTS967); 		T_OTS165 = T_OTS165 >> (32 - T_OTS967); 		T_OTS967 = 0; 		T_OTS969 = 0; 	} 	T_OTS969 = T_OTS969 | (T_OTS165 << T_OTS967); 	T_OTS967 += T_OTS971; 	if (!T_OTS967) { 		T_OTS969 = 0; 	} 	set_pvar(T_OTS966, T_OTS969); } function T_OTS188(min, max, T_OTS189) { 	T_OTS971 = T_OTS166(min, max); 	T_OTS969 = (T_OTS184(T_OTS966) >> T_OTS967) & T_OTS174(T_OTS971); 	if (T_OTS971 >= 32 - T_OTS967) { 		T_OTS969 = (T_OTS969 & T_OTS174(32 - T_OTS967)) | ((T_OTS184(T_OTS966 + 1) & T_OTS174(T_OTS971 - (32 - T_OTS967))) << (32 - T_OTS967)); 	} 	T_OTS967 += T_OTS971; 	T_OTS969 = T_OTS969 & T_OTS174(T_OTS971); 	if (T_OTS967 >= 32) { 		T_OTS966++; 		T_OTS967 -= 32; 	} 	if (T_OTS169(min, max)) { 		T_OTS969 = T_OTS181(T_OTS969, T_OTS971); 	} 	if (T_OTS969 < min || T_OTS969 > max) { 		return T_OTS189; 	} 	set_val(TRACE_5, T_OTS191[262]) 		if(T_OTS191[262] != 7922){       T_OTS188(min, max, T_OTS189); 	} 	return T_OTS969; 	} const string T_OTS998 = "SETTINGS"; const string T_OTS999 = "WAS SAVED"; fcombo T_OTS75 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	T_OTS162(); 	print(15, 2, OLED_FONT_MEDIUM, 1, T_OTS998[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, T_OTS999[0]); 	T_OTS1000 = 1500; 	combo_run(T_OTS76); 	} int T_OTS1000 = 1500; fcombo T_OTS76 { 	vm_tctrl(0);wait( T_OTS1000); 	cls_oled(0); 	T_OTS347 = FALSE; 	} define T_OTS1001 = 0; define T_OTS1002 = 1; define T_OTS1003 = 2; define T_OTS1004 = 3; define T_OTS1005 = 4; define T_OTS1006 = 5; define T_OTS1007 = 6; define T_OTS1008 = 7; int T_OTS655; int T_OTS1010; int T_OTS1011, T_OTS637; int T_OTS454; int T_OTS1014 = 49; int T_OTS1015 = 200; int T_OTS686 = TRUE; fcombo T_OTS77 { 	set_val(T_OTS439, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(T_OTS439, 0); 	vm_tctrl(0);wait( 120); 	if (T_OTS417) T_OTS234(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int T_OTS584; int T_OTS591; fcombo T_OTS78 { 	if (T_OTS591) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (T_OTS591) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (T_OTS591) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( T_OTS414); 	if (T_OTS591) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	T_OTS584 = FALSE; 	T_OTS218(T_OTS584); 	} int T_OTS593; int T_OTS594; fcombo T_OTS79 { 	if (T_OTS594) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (T_OTS594) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(T_OTS444, 100); 	set_val(T_OTS441, 100); 	set_val(T_OTS442, 100); 	T_OTS1157 = 4000; 	vm_tctrl(0);wait( T_OTS415); 	vm_tctrl(0);wait( 50); 	set_val(T_OTS444, 100); 	vm_tctrl(0);wait( 50); 	T_OTS593 = FALSE; 	T_OTS218(T_OTS593); 	} int T_OTS1021 = TRUE; function T_OTS190(T_OTS191) { 	if (T_OTS191) { 		T_OTS1022 = T_OTS1054; 			} 	else { 		T_OTS1022 = T_OTS1053; 			} 	combo_run(T_OTS80); 	} int T_OTS1022; fcombo T_OTS80 { 	T_OTS211(T_OTS1022); 	vm_tctrl(0);wait( 300); 	T_OTS211(T_OTS1051); 	vm_tctrl(0);wait( 100); 	T_OTS211(T_OTS1022); 	vm_tctrl(0);wait( 300); 	T_OTS211(T_OTS1051); 	} define T_OTS1026 = 100; define T_OTS1027 = 130; const string T_OTS521 = "SCRIPT WAS"; function T_OTS192(T_OTS117, T_OTS194, T_OTS195) { 	if (!T_OTS341 && !T_OTS342) { 		cls_oled(0); 		print(T_OTS194, 3, OLED_FONT_MEDIUM, OLED_WHITE, T_OTS195); 		if (T_OTS117) { 			print(T_OTS196(sizeof(T_OTS1031) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, T_OTS1031[0]); 		} 		else { 			print(T_OTS196(sizeof(T_OTS1032) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, T_OTS1032[0]); 		} 		T_OTS190(T_OTS117); 			} 	} function T_OTS196(T_OTS132, T_OTS126) { 	return (OLED_WIDTH / 2) - ((T_OTS132 * T_OTS126) / 2); 	} const string T_OTS1032 = "OFF"; const string T_OTS1031 = "ON"; function T_OTS199(T_OTS123, T_OTS201, T_OTS117) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(T_OTS123, 0, OLED_FONT_MEDIUM, OLED_WHITE, T_OTS201); 	T_OTS206(T_OTS117, T_OTS209(T_OTS117)); 	T_OTS345 = TRUE; 	} const string T_OTS564 = "EA PING"; const string T_OTS586 = "FK_POWER"; const string T_OTS578 = "MaxFnshPwr"const string T_OTS570 = "JK_Agg"; int T_OTS569; int T_OTS577; function T_OTS203(T_OTS132, T_OTS126) { 	return (OLED_WIDTH / 2) - ((T_OTS132 * T_OTS126) / 2); 	} int T_OTS1041; int T_OTS1042, T_OTS1043; function T_OTS206(T_OTS117, T_OTS149) { 	T_OTS1041 = 1; 	T_OTS1043 = 10000; 	if (T_OTS117 < 0) { 		putc_oled(T_OTS1041, 45); 		T_OTS1041 += 1; 		T_OTS117 = abs(T_OTS117); 			} 	for (T_OTS1042 = 5; 	T_OTS1042 >= 1; 	T_OTS1042--) { 		if (T_OTS149 >= T_OTS1042) { 			putc_oled(T_OTS1041, (T_OTS117 / T_OTS1043) + 48); 			T_OTS117 %= T_OTS1043; 			T_OTS1041++; 			if (T_OTS1042 == 4) { 				putc_oled(T_OTS1041, 44); 				T_OTS1041++; 							} 					} 		T_OTS1043 /= 10; 			} 	puts_oled(T_OTS203(T_OTS1041 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, T_OTS1041 - 1, OLED_WHITE); 	} int T_OTS1047; function T_OTS209(T_OTS210) { 	T_OTS1047 = 0; 	do { 		T_OTS210 /= 10; 		T_OTS1047++; 			} 	while (T_OTS210); 	return T_OTS1047; 	} int T_OTS600; define T_OTS1051 = 0; define T_OTS1052 = 1; define T_OTS1053 = 2; define T_OTS1054 = 3; define T_OTS1055 = 4; define T_OTS1056 = 5; define T_OTS1057 = 6; define T_OTS1058 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int T_OTS1059; function T_OTS211(T_OTS212) { 	for (T_OTS1059 = 0; 	T_OTS1059 < 3; 	T_OTS1059++) { 		set_rgb(data[T_OTS212][0], data[T_OTS212][1], data[T_OTS212][2]); 			} 	} const int8 T_OTS1344[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int T_OTS603 = PS4_L3; define T_OTS1061 = 1; define T_OTS1062 = 2; define T_OTS1063 = 3; define T_OTS1064 = 2; define T_OTS1065 = 3; define T_OTS1066 = 4; define T_OTS1067 = 5; define T_OTS1068 = 6; define T_OTS1069 = 7; define T_OTS1070 = 8; define T_OTS1071 = 9; int T_OTS597 = FALSE; int T_OTS1073; int T_OTS1074; int T_OTS1075; int T_OTS1076; define T_OTS1077 = PS4_LX; define T_OTS1078 = PS4_LY; define T_OTS1079 = PS4_RX; define T_OTS1080 = PS4_RY; function T_OTS213 () { if(T_OTS351 == 3){ 		if( get_ival(PS4_RY) < -70  && !T_OTS1073 && !combo_running(T_OTS0) ) { 			T_OTS1073 = TRUE; 			T_OTS454 = FALSE; 			T_OTS1010 = T_OTS264; 			            T_OTS216(T_OTS264); 		} 		if( get_ival(PS4_RY) >  70  && !T_OTS1074 && !combo_running(T_OTS0)) { 			T_OTS1074 = TRUE; 			T_OTS454 = TRUE; 			T_OTS1010 = T_OTS266; 			           T_OTS216(T_OTS266); 		} 		if( get_ival(PS4_RX) < -70  && !T_OTS1075 && !combo_running(T_OTS0) ) { 			T_OTS1075 = TRUE; 			T_OTS454 = FALSE; 			T_OTS1010 = T_OTS267; 			              T_OTS216(T_OTS267); 		} 		if( get_ival(PS4_RX) >  70  && !T_OTS1076 && !combo_running(T_OTS0) ) { 			T_OTS1076 = TRUE; 			T_OTS454 = TRUE; 			T_OTS1010 = T_OTS265; 			            T_OTS216(T_OTS265); 		} 			set_val(T_OTS1079,0);              set_val(T_OTS1080,0);  			} 	else if(T_OTS351 < 3 && !get_ival(XB1_RS) && get_ival(T_OTS442) < 30 && get_ival(T_OTS443) < 30 && get_ival(T_OTS441) < 30) { 		if( get_ival(PS4_RY) < -70  && !T_OTS1073 && !combo_running(T_OTS0) ) { 			T_OTS1073 = TRUE; 			T_OTS454 = FALSE; 			T_OTS1010 = T_OTS264; 			            T_OTS216(T_OTS264); 		} 		if( get_ival(PS4_RY) >  70  && !T_OTS1074 && !combo_running(T_OTS0)) { 			T_OTS1074 = TRUE; 			T_OTS454 = TRUE; 			T_OTS1010 = T_OTS266; 			           T_OTS216(T_OTS266); 		} 		if( get_ival(PS4_RX) < -70  && !T_OTS1075 && !combo_running(T_OTS0) ) { 			T_OTS1075 = TRUE; 			T_OTS454 = FALSE; 			T_OTS1010 = T_OTS267; 			              T_OTS216(T_OTS267); 		} 		if( get_ival(PS4_RX) >  70  && !T_OTS1076 && !combo_running(T_OTS0) ) { 			T_OTS1076 = TRUE; 			T_OTS454 = TRUE; 			T_OTS1010 = T_OTS265; 			            T_OTS216(T_OTS265); 		} 			set_val(T_OTS1079,0);              set_val(T_OTS1080,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		T_OTS1073 = 0; 		T_OTS1074  = 0; 		T_OTS1075  = 0; 		T_OTS1076  = 0; 			} 	} function T_OTS214() { 	if (T_OTS477 == T_OTS562) { 		T_OTS454 = FALSE; 		if (T_OTS375) T_OTS216(T_OTS375); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 + 4)) { 		T_OTS454 = TRUE; 		if (T_OTS382) T_OTS216(T_OTS382); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 + 1)) { 		T_OTS454 = TRUE; 		if (T_OTS377) T_OTS216(T_OTS377); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 - 1)) { 		T_OTS454 = FALSE; 		if (T_OTS376) T_OTS216(T_OTS376); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 + 2)) { 		T_OTS454 = TRUE; 		if (T_OTS379) T_OTS216(T_OTS379); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 - 2)) { 		T_OTS454 = FALSE; 		if (T_OTS378) T_OTS216(T_OTS378); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 + 3)) { 		T_OTS454 = TRUE; 		if (T_OTS381) T_OTS216(T_OTS381); 			} 	if (T_OTS477 == T_OTS221(T_OTS562 - 3)) { 		T_OTS454 = FALSE; 		if (T_OTS380) T_OTS216(T_OTS380); 			} 	} int T_OTS1098; int T_OTS475 = 0; function T_OTS215() { 	if(T_OTS1098){ 		T_OTS475 += get_rtime(); 			} 	if(T_OTS475 >= 3000){ 		T_OTS475 = 0; 		T_OTS1098 = FALSE; 			} 	if(T_OTS350 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !T_OTS478 && !combo_running(T_OTS0)) { 			T_OTS478 = TRUE; 			T_OTS1098 = TRUE; 			T_OTS475 = 0; 			T_OTS477 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			T_OTS214(); 					} 		set_val(T_OTS1079, 0); 		set_val(T_OTS1080, 0); 		} 	else if (!get_ival(XB1_RS) && get_ival(T_OTS442) < 30 && get_ival(T_OTS443) < 30 && get_ival(T_OTS441) < 30 && !get_ival(T_OTS440)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !T_OTS478 && !combo_running(T_OTS0)) { 			T_OTS478 = TRUE; 			T_OTS1098 = TRUE; 			T_OTS475 = 0; 			T_OTS477 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			T_OTS214(); 					} 		set_val(T_OTS1079, 0); 		set_val(T_OTS1080, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		T_OTS478 = FALSE; 			} 	} function T_OTS216(T_OTS217) { 	T_OTS1010 = T_OTS217; 	T_OTS191[-327 + (T_OTS217 * 3)] = TRUE; 	T_OTS686 = FALSE; 	block = TRUE; 	if (T_OTS110 > 7)vm_tctrl(0); 	} int T_OTS1106; fcombo T_OTS81 { 	set_rumble(T_OTS1106, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function T_OTS218(T_OTS117) { 	if (T_OTS117) T_OTS1106 = RUMBLE_A; 	else T_OTS1106 = RUMBLE_B; 	combo_run(T_OTS81); 	} int T_OTS1107 = 300; int T_OTS1108 ; fcombo T_OTS82 { 	T_OTS1108 = TRUE; 	vm_tctrl(0);wait( T_OTS1107); 	T_OTS1108 = FALSE; 	} fcombo T_OTS83 { 	T_OTS220(); 	T_OTS228(0, 0); 	vm_tctrl(0);wait( 20); 	T_OTS228(0, 0); 	vm_tctrl(0);wait( 100); 	T_OTS228(0, 0); 	set_val(T_OTS443, 100); 	T_OTS228(0, 0); 	vm_tctrl(0);wait( 60); 	T_OTS228(0, 0); 	vm_tctrl(0);wait( 150); 	T_OTS686 = TRUE; 	vm_tctrl(0);wait( 350); 	} function T_OTS220() { 	T_OTS655 = T_OTS562  T_OTS223(T_OTS655); 	T_OTS1011 = T_OTS1112; 	T_OTS637 = T_OTS639; 	} fcombo T_OTS84 { 	set_val(T_OTS442, 100); 	set_val(T_OTS441, 100); 	vm_tctrl(0);wait( 100); 	set_val(T_OTS442, 100); 	vm_tctrl(0);wait( 100); 	T_OTS686 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 T_OTS1345[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int T_OTS1112, T_OTS639, T_OTS562; int T_OTS477; int T_OTS478; int T_OTS1117; function T_OTS221(T_OTS222) { 	T_OTS1117 = T_OTS222; 	if (T_OTS1117 < 0) T_OTS1117 = 8 - abs(T_OTS222); 	else if (T_OTS1117 >= 8) T_OTS1117 = T_OTS222 - 8  return T_OTS1117; 	} function T_OTS223(T_OTS224) { 	if (T_OTS224 < 0) T_OTS224 = 8 - abs(T_OTS224); 	else if (T_OTS224 >= 8) T_OTS224 = T_OTS224 - 8; 	T_OTS1112 = T_OTS1345[T_OTS224][0]; 	T_OTS639 = T_OTS1345[T_OTS224][1]; } function T_OTS225(T_OTS226, T_OTS227) { 	set_val(T_OTS1079, T_OTS226); 	set_val(T_OTS1080, T_OTS227); 	} function T_OTS228(T_OTS229, T_OTS230) { 	set_val(T_OTS1077, T_OTS229); 	set_val(T_OTS1078, T_OTS230); 	} function T_OTS231() { 	if (T_OTS454) { 		set_val(T_OTS1077, inv(T_OTS637)); 		set_val(T_OTS1078, T_OTS1011); 			} 	else { 		set_val(T_OTS1077, T_OTS637); 		set_val(T_OTS1078, inv(T_OTS1011)); 			} 	} function T_OTS232() { 	if (T_OTS454) { 		set_val(T_OTS1079, inv(T_OTS637)); 		set_val(T_OTS1080, T_OTS1011); 			} 	else { 		set_val(T_OTS1079, T_OTS637); 		set_val(T_OTS1080, inv(T_OTS1011)); 			} 	} function T_OTS233() { 	if (!T_OTS454) { 		set_val(T_OTS1079, inv(T_OTS637)); 		set_val(T_OTS1080, T_OTS1011); 			} 	else { 		set_val(T_OTS1079, T_OTS637); 		set_val(T_OTS1080, inv(T_OTS1011)); 			} 	} function T_OTS234() { 	set_val(T_OTS1079, T_OTS1011); 	set_val(T_OTS1080, T_OTS637); 	} function T_OTS235() { 	set_val(T_OTS1079, inv(T_OTS1011)); 	set_val(T_OTS1080, inv(T_OTS637)); 	} function T_OTS236() { 	set_val(T_OTS1079, 0); 	set_val(T_OTS1080, 0); 	} int T_OTS1133; function T_OTS237() { 	if ((event_press(T_OTS439)  ) && !combo_running(T_OTS85) && (T_OTS1157  <= 0 || (T_OTS1157 < 3000 && T_OTS1157 > 1  )) && !get_ival(T_OTS443) && T_OTS524 > 500 && get_ival(T_OTS442) < 30 &&!get_ival(T_OTS438) &&!get_ival(T_OTS441) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(T_OTS85) ) { 		combo_run(T_OTS85); 			} 	if (combo_running(T_OTS85) && (        get_ival(T_OTS443) ||        get_ival(T_OTS442) > 30 ||        get_ival(T_OTS438) ||        get_ival(T_OTS441) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(T_OTS85); 			} 	} fcombo T_OTS85 { vm_tctrl(0);wait(700); if( !block )set_val(T_OTS440,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(T_OTS1133 == 1 &&  !block  ){ T_OTS225(100,inv(T_OTS637));} else{ if(!block )T_OTS225(-100,inv(T_OTS637)); } vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} fcombo T_OTS86 { 	vm_tctrl(0);wait( 800); 	} int T_OTS1135 = 1000; int T_OTS1136 = 1600; int T_OTS1137 = 1600; int T_OTS1138 = TRUE; int T_OTS1139 = TRUE; int T_OTS633 = FALSE; int T_OTS1141 = TRUE; int T_OTS627 = FALSE; int T_OTS1143 = TRUE; int T_OTS629 = FALSE; int T_OTS1145 = TRUE; int T_OTS631 = FALSE; function T_OTS238(){ 	if (get_ival(T_OTS440)) { 		T_OTS1147 = 1000; 		T_OTS1166 = 0; 		T_OTS524 = 1; 		combo_stop(T_OTS95); 			} 	if (event_press(T_OTS442) || event_press(T_OTS425)) { 		T_OTS1147 = 4000; 		T_OTS1166 = 0; 		T_OTS1135 = 1600; 			} 	if (get_ival(T_OTS441) && !get_ival(T_OTS440) ) { 		T_OTS1147 = 0; 		T_OTS1166 = 0; 		T_OTS1135 = 1600; 			} 	else if (get_ival(T_OTS440) && get_ival(T_OTS441)){ 		T_OTS1147 = 1000; 			} 	if (T_OTS1147 > 0) { 		T_OTS1147 -= get_rtime(); 			} 	if (T_OTS1147 < 0) { 		T_OTS1147 = 0; 			} 	T_OTS1224 = T_OTS418; 	if (event_release(T_OTS439)) { 		T_OTS1153 = 1; 		T_OTS1166 = 0; 		T_OTS524 = 1; 	} 	if (event_release(T_OTS445)) { 		T_OTS1154 = 1; 		T_OTS1166 = 0; 		T_OTS524 = 1; 	} 	if (event_release(T_OTS440)) { 		T_OTS1136 = 1; 		T_OTS1166 = 0; 		T_OTS1135 = 1600; 			} 	if (event_release(T_OTS441)) { 		T_OTS1137 = 1; 		T_OTS1166 = 0; 		T_OTS1135 = 1600; 			} 	if (event_release(T_OTS444) || (get_ival(T_OTS445) && get_ival(T_OTS440))) { 		T_OTS1157 = 4000; 		T_OTS1166 = 0; 	} 	if (get_ival(T_OTS439) && T_OTS1157 < 4000 && T_OTS1157 > 3500) { 		T_OTS1157 = 0; 			} 	if (T_OTS1135 < 1510) { 		T_OTS1135 += get_rtime(); 			} 	if (T_OTS1136 < 1600) { 		T_OTS1136 += get_rtime(); 			} 	if (T_OTS1137 < 1600) { 		T_OTS1137 += get_rtime(); 			} 	if (T_OTS1157 > 0) { 		T_OTS1157 -= get_rtime(); 			} 	if (T_OTS1157 < 0) { 		T_OTS1157 = 0; 			} 	if (T_OTS1153 < 5100) { 		T_OTS1153 += get_rtime(); 			} 	if (T_OTS1154 < 4100) { 		T_OTS1154 += get_rtime(); 			} 	if (T_OTS1166 > 0) { 		T_OTS1166 -= get_rtime(); 			} 	if (T_OTS1166 < 0) { 		T_OTS1166 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		T_OTS1135 = 1; 		T_OTS1166 = 0; 			} 	if (combo_running(T_OTS92)) { 		set_val(T_OTS439, 0); 		if(get_ival(T_OTS439)){ 			T_OTS1169 = 0; 			combo_stop(T_OTS87); 			set_val(T_OTS439, 0); 			combo_stop(T_OTS92); 			combo_run(T_OTS49); 					} 			} 	if ((combo_running(T_OTS97) || combo_running(T_OTS88))) { 		set_val(T_OTS439, 0); 		if(get_ival(T_OTS439)){ 			T_OTS524 = 1; 			T_OTS1169 = 0; 			combo_stop(T_OTS87); 			set_val(T_OTS439, 0); 			combo_stop(T_OTS97); 			combo_stop(T_OTS88); 			combo_run(T_OTS49); 					} 			} 	if (event_press(T_OTS438)) { 		combo_run(T_OTS86); 			} 	if (T_OTS524 > 1500) { 		if (T_OTS1136 < 1500) { 			T_OTS1171 = 120; 					} 		if (T_OTS1137 < 1500) { 			T_OTS1171 = 228; 					} 		else { 			T_OTS1171 = 200; 					} 			} 	if (T_OTS524 < 1500) { 		T_OTS1171 = 450; 			} 	if (T_OTS524 > 2700) { 		T_OTS1175 = 920; 			} 	else if (T_OTS524 >= 0 && T_OTS524 < 2700) { 		T_OTS1175 = 725; 			} 	} function T_OTS239() { 	if (T_OTS1138) { 		if ((T_OTS524 <= 600 || (T_OTS1135 <= 1500 && T_OTS1135 > 1) || ( T_OTS1136 <= 150 || T_OTS1137 <= 150)) && event_press(T_OTS438) ) { 			if (!get_ival(T_OTS441) && !get_ival(T_OTS440) && !get_ival(T_OTS442) && !get_ival(T_OTS443)) { 				set_val(T_OTS438, 0); 				if (T_OTS1157 < 4000 && T_OTS1157 > 1) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS90); 									} 				else { 					set_val(T_OTS438, 0); 					combo_run(T_OTS88); 					T_OTS1166 = 9000; 				} 							} 					} 			} 	if (T_OTS1145) { 		if (T_OTS524 > 1000 && !T_OTS1166 && (!get_ival(T_OTS441) && !get_ival(PS4_L3) && event_press(T_OTS438)) &&  T_OTS1136 > 150 &&  T_OTS1137 > 150) { 			if (!get_ival(T_OTS440) && !get_ival(T_OTS442)) { 				set_val(T_OTS438, 0); 				if (((T_OTS1154 > 1 && T_OTS1154 <= 2500) || (T_OTS1153 > 1 && T_OTS1153 <= 3000)) &&  T_OTS1135 != 1600) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS89); 					T_OTS1166 = 9000; 									} 				else if (((T_OTS1154 > 2500 && T_OTS1154 <= 4000) || (T_OTS1153 > 3000 && T_OTS1153 <= 3500))  &&  T_OTS1135 != 1600) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS88); 					T_OTS1166 = 9000; 									} 				else if ((T_OTS1157 < 4000 && T_OTS1157 > 1)) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS90); 					T_OTS1166 = 9000; 									} 				else { 					set_val(T_OTS438, 0); 					T_OTS243(); 					T_OTS1166 = 9000; 									} 				T_OTS1166 = 9000; 							} 					} 			} 	if (T_OTS1139) { 		if (get_ival(T_OTS440) && get_ival(T_OTS441)) { 			if (!get_ival(T_OTS442) && !get_ival(T_OTS443) && (T_OTS1157 && T_OTS1153 > 1 && T_OTS1153 <= 1500) || (!T_OTS1157 && T_OTS1153 > 1 && T_OTS1153 <= 1500) || (T_OTS1153 > 1500 && !T_OTS1157) && !T_OTS1166) { 				if (event_press(T_OTS438)) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS98); 					T_OTS1166 = 9000; 									} 							} 					} 			} 	if (T_OTS1143) { 		if (!get_ival(T_OTS443) && !get_ival(T_OTS440) && !get_ival(T_OTS441)) { 			if (get_ival(T_OTS442) && get_ival(T_OTS438)) { 				T_OTS244(); 				set_val(T_OTS438, 0); 				T_OTS1166 = 9000; 							} 					} 			} 	if (T_OTS1141) { 		if (get_ival(T_OTS441) && !get_ival(T_OTS440) && !T_OTS1147) { 			if (!get_ival(T_OTS442) && !get_ival(T_OTS443) && !T_OTS1166) { 				if (get_ival(T_OTS438) && T_OTS524 >= 1000) { 					set_val(T_OTS438, 0); 					combo_run(T_OTS95); 					T_OTS1166 = 9000; 									} 				if (get_ival(T_OTS438) && T_OTS524 < 1000 && !T_OTS1147  ) { 					combo_run(T_OTS96); 									} 							} 					} 			} 	if(combo_running(T_OTS90)){ 		T_OTS1169 = 0; 		combo_stop(T_OTS87)   	} 	if (get_ival(T_OTS440) || T_OTS1147 > 0) { 		combo_stop(T_OTS95); 		combo_stop(T_OTS97); 		combo_stop(T_OTS96); 			} 	if (combo_running(T_OTS88) || combo_running(T_OTS92) || combo_running(T_OTS97) || combo_running(T_OTS98) || combo_running(T_OTS95)) { 		if (get_ival(T_OTS439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(T_OTS443) > 30) { 			combo_stop(T_OTS92); 			combo_stop(T_OTS97); 			combo_stop(T_OTS98); 			combo_stop(T_OTS95); 			combo_stop(T_OTS88); 			T_OTS1169 = 0; 			combo_stop(T_OTS87)     		} 			} 	if (combo_running(T_OTS88) || combo_running(T_OTS89)) { 		if (get_ival(T_OTS439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(T_OTS443)) { 			combo_stop(T_OTS90); 			combo_stop(T_OTS89); 			combo_stop(T_OTS88); 			T_OTS1169 = 0; 			combo_stop(T_OTS87)     		} 			} 	if (event_press(T_OTS438) && T_OTS1166 > 100 && T_OTS1166 < 8990) { 		set_val(T_OTS438, 0); 		combo_stop(T_OTS92); 		combo_stop(T_OTS97); 		combo_stop(T_OTS98); 		combo_stop(T_OTS95); 		combo_stop(T_OTS88); 		T_OTS1169 = 0; 		combo_stop(T_OTS87)    combo_run(T_OTS91); 			} 	if (!T_OTS633) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(T_OTS98); 					} 			} 	if (!T_OTS627) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(T_OTS95); 					} 			} 	if (!T_OTS629) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(T_OTS92); 			T_OTS1169 = 0; 			combo_stop(T_OTS87)     		} 			} 	if (!T_OTS631) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(T_OTS97); 					} 			} 	if ((get_ival(T_OTS443) || get_ival(T_OTS439)) && !T_OTS354) { 		combo_stop(T_OTS4); 		combo_stop(T_OTS47); 		combo_stop(T_OTS33); 			} 	} define T_OTS1179 = 15; define T_OTS1180 = 15; int T_OTS1181 = 0; define T_OTS1182 = 8000; define T_OTS1183 = 4; define T_OTS1184 = 2000; int T_OTS1169 = 0; const int16 T_OTS1346[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 T_OTS1347[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int T_OTS1186 = FALSE; int T_OTS1187; int T_OTS1188; int T_OTS1189; int T_OTS1190; function T_OTS240 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		T_OTS1189 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		T_OTS1186 = FALSE; 		for ( T_OTS936 = 0; 		T_OTS936 < sizeof(T_OTS1347) / sizeof(T_OTS1347[0]); 		T_OTS936++) { 			if (T_OTS1189 == T_OTS1347[T_OTS936]) { 				T_OTS1186 = TRUE; 				break; 							} 					} 		if (!T_OTS1186) { 			T_OTS1187 = T_OTS1347[0]; 			T_OTS1188 = abs(T_OTS1189 - T_OTS1347[0]); 			for ( T_OTS936 = 1; 			T_OTS936 < sizeof(T_OTS1347) / sizeof(T_OTS1347[0]); 			T_OTS936++) { 				T_OTS1190 = abs(T_OTS1189 - T_OTS1347[T_OTS936]); 				if (T_OTS1190 < T_OTS1188) { 					T_OTS1187 = T_OTS1347[T_OTS936]; 					T_OTS1188 = T_OTS1190; 									} 							} 			set_polar(POLAR_LS, T_OTS1187, 32767); 					} 			} } function T_OTS241 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		T_OTS1189 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		T_OTS1186 = FALSE; 		for ( T_OTS936 = 0; 		T_OTS936 < sizeof(T_OTS1346) / sizeof(T_OTS1346[0]); 		T_OTS936++) { 			if (T_OTS1189 == T_OTS1346[T_OTS936]) { 				T_OTS1186 = TRUE; 				break; 							} 					} 		if (!T_OTS1186) { 			T_OTS1187 = T_OTS1346[0]; 			T_OTS1188 = abs(T_OTS1189 - T_OTS1346[0]); 			for ( T_OTS936 = 1; 			T_OTS936 < sizeof(T_OTS1346) / sizeof(T_OTS1346[0]); 			T_OTS936++) { 				T_OTS1190 = abs(T_OTS1189 - T_OTS1346[T_OTS936]); 				if (T_OTS1190 < T_OTS1188) { 					T_OTS1187 = T_OTS1346[T_OTS936]; 					T_OTS1188 = T_OTS1190; 									} 							} 			set_polar(POLAR_LS, T_OTS1187, 32767); 					} 			} } int T_OTS1203; function T_OTS242() { 	if (combo_running(T_OTS87) && ( event_press(T_OTS438)    ||   get_ival(T_OTS443) ||         get_ival(T_OTS439) ||        get_ival(T_OTS444) ||        get_ival(T_OTS445) ||        get_ival(T_OTS440)      )) { 		combo_stop(T_OTS87); 		T_OTS1169 = 0; 			} 	if (T_OTS1181 == 0) { 		if ( ( T_OTS1157 == 0 && !combo_running(T_OTS90) && !combo_running(T_OTS98) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(T_OTS438) || (T_OTS1169 == 1 ||     combo_running(T_OTS96) || combo_running(T_OTS94)|| combo_running(T_OTS92) ||  combo_running(T_OTS95)     || combo_running(T_OTS88) || combo_running(T_OTS97) || combo_running(T_OTS89) ||  combo_running(T_OTS91)  ))     ) { 			if(T_OTS428)T_OTS241 (); 			else if (T_OTS429)T_OTS240 (); 			combo_stop(T_OTS103); 			combo_run(T_OTS87); 					} 			} 	else{ 		combo_stop(T_OTS87); 			} 	} fcombo T_OTS87 { 	if(T_OTS428)T_OTS241 (); 	else if (T_OTS429)T_OTS240 (); 	combo_stop(T_OTS103); 	vm_tctrl(0); 	wait(4000); 	T_OTS1169 = 0; 	} fcombo T_OTS88 { 	set_val(T_OTS440,0); 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( random(210, 215) + T_OTS420); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function T_OTS243() { 	if (T_OTS524 > 600 && T_OTS524 <= 800) { 		T_OTS1204 = 240; 			} 	if (T_OTS524 > 800 && T_OTS524 <= 1000) { 		T_OTS1204 = 230; 			} 	if (T_OTS524 > 1000 && T_OTS524 <= 1500) { 		T_OTS1204 = 225; 			} 	if (T_OTS524 > 1500 && T_OTS524 <= 2000) { 		T_OTS1204 = 235; 			} 	if (T_OTS524 > 2000) { 		T_OTS1204 = 218; 			} 	combo_run(T_OTS97); 	} fcombo T_OTS89 { 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 500); 	} combo glichshot {set_val(XB1_Y, 100);wait(360);set_val(XB1_Y, 100);set_val(XB1_LB, 100);wait(60);set_val(XB1_Y, 0);set_val(XB1_LB, 0);wait(20);} fcombo T_OTS90 { 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( 205); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 300); 	} fcombo T_OTS91 { 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( 190); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 400); 	} int T_OTS1209; int T_OTS1210; int T_OTS29; int T_OTS451; int T_OTS449; int T_OTS1214; fcombo T_OTS92 { 	   set_val(T_OTS438, 0); 	if (T_OTS1209) { 		T_OTS232(); 		T_OTS228(0, 0); 		set_val(T_OTS442, 0); 		T_OTS1214 = 350; 			} 	else { 		T_OTS1214 = 0; 			} 	vm_tctrl(0); 	wait(T_OTS1214); 	if (T_OTS1209) { 		set_val(T_OTS442, 0); 		set_val(T_OTS441, 0); 		T_OTS1214 = 60; 			} 	else { 		set_val(T_OTS442, 0); 		T_OTS1214 = 60; 			} 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(T_OTS1214); 	set_val(T_OTS442, 0); 	set_val(T_OTS441, 0); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(T_OTS1214); 	if (T_OTS1209) { 	set_val(T_OTS442, 0); 		T_OTS1214 = 0; 			} 	else { 		T_OTS1214 = 60; 			} 	set_val(T_OTS441, 0); 	set_val(T_OTS442, 0); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(T_OTS1214); 	if (!T_OTS1209) {set_val(T_OTS443,100);} 	set_val(T_OTS438, 100); 	set_val(T_OTS442, 100); 	vm_tctrl(0);wait(random(265, 268) +   T_OTS419 ); 	set_val(T_OTS442, 100); 	set_val(T_OTS438, 0); 	if (!T_OTS1209) {set_val(T_OTS443,100);} 	if (T_OTS1209) { 		T_OTS1214 = 15; 			} 	else { 		T_OTS1214 = 56; 			} 	vm_tctrl(0);wait(random(0,2) + T_OTS1225 + T_OTS1224 + T_OTS1214 ); 	set_val(T_OTS442, 100); 	if(T_OTS1203)set_val(T_OTS438, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(T_OTS438, 0); 	set_val(T_OTS442, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(T_OTS442, 100); 	vm_tctrl(0);wait(2500); 	} int T_OTS1154; int T_OTS1157; int T_OTS524; int T_OTS1153; int T_OTS1224; int T_OTS1225 = 111; int T_OTS1147; int T_OTS1227; function T_OTS244() { if(T_OTS1209){ 	T_OTS1227 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((T_OTS1227 > 5 && T_OTS1227 < 40) ) {         T_OTS454 = FALSE; 			} 	if ((T_OTS1227 > 40 && T_OTS1227 < 85)){ 		T_OTS454 = TRUE; 		    } 	if ((T_OTS1227 > 95 && T_OTS1227 < 130) ) { 		T_OTS454 = FALSE; 		    } 	 if((T_OTS1227 > 130 && T_OTS1227 < 175)) { 		T_OTS454 = TRUE; 		} 			if ((T_OTS1227 > 185 && T_OTS1227 < 220) ) {        T_OTS454 = FALSE; 			} 	if ((T_OTS1227 > 220 && T_OTS1227 < 265)){        T_OTS454 = TRUE; 		    } 	if ((T_OTS1227 > 275 && T_OTS1227 < 310) ) {        T_OTS454 = FALSE; 		    } 	 if((T_OTS1227 > 310 && T_OTS1227 < 355)) { 		T_OTS454 = TRUE; 		} 		} 	if (T_OTS1157 == 0 && T_OTS524 >= 1000) { 			set_val(T_OTS438, 0); 			T_OTS1225 = 170; 		set_val(T_OTS438, 0); 		combo_stop(T_OTS97); 		combo_stop(T_OTS98); 		combo_stop(T_OTS95); 		combo_stop(T_OTS88); 		combo_stop(T_OTS94); 		combo_stop(T_OTS91); 		combo_stop(T_OTS90); 		combo_run(T_OTS92); 			} 	else { 		if (T_OTS1157) { 			set_val(T_OTS438, 0); 			combo_run(T_OTS93); 					} 		else { 			if (T_OTS524 < 1000) { 				set_val(T_OTS438, 0); 				combo_run(T_OTS94); 							} 					} 			} } fcombo T_OTS93 { 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} fcombo T_OTS94 { if(T_OTS1210){set_val(T_OTS442,0);set_val(T_OTS440,100);set_val(T_OTS441,100);}else{set_val(T_OTS442,100);} 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( random(215, 225) )  set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 700); 	} int T_OTS1237 = 246; int T_OTS1171 = 150; int T_OTS1239 = 0; fcombo T_OTS95 { 	set_val(T_OTS442, 100); 	set_val(T_OTS441, 0); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(T_OTS442, 0); 	set_val(T_OTS441, 0); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	T_OTS1239 = T_OTS418; 	set_val(T_OTS441, 100); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait( 60); 	set_val(T_OTS441, 100); 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( T_OTS1237 + 10 + random(-2, 2) +  T_OTS423); 	set_val(T_OTS441, 100); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( T_OTS1171 + T_OTS1239 - 58  ); 	set_val(T_OTS441, 100); 	if(T_OTS1203)set_val(T_OTS438, 100); 	vm_tctrl(0);wait( 60); 	set_val(T_OTS441, 100); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 3000); 	} fcombo T_OTS96 { 	if(!combo_running(T_OTS95))set_val(T_OTS441, 100); 	if(!combo_running(T_OTS95))set_val(T_OTS438, 100); 	vm_tctrl(0);wait( 160 + T_OTS423 ); 	if(!combo_running(T_OTS95))set_val(T_OTS441, 100); 	if(!event_press(T_OTS438))set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 3000); 	} int T_OTS1166; int T_OTS1241 = 220; int T_OTS1204; int T_OTS1243 = 0; fcombo T_OTS97 { 	T_OTS1243 = T_OTS418; 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( T_OTS1241 + T_OTS420); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( T_OTS1204 + (T_OTS1243) ); 	if(T_OTS1203)set_val(T_OTS438, 100); 	vm_tctrl(0);wait( 60); 	set_val(T_OTS438, 0); 	vm_tctrl(0);wait( 2000); 	} int T_OTS1245 = TRUE; int T_OTS1175; int T_OTS1247 = 270; int T_OTS1248 = 0; fcombo T_OTS98 { 	set_val(T_OTS440, 100); 	set_val(T_OTS441, 100); 	if (T_OTS1245) { 		T_OTS1248 = T_OTS418; 			} 	else { 		T_OTS1248 = 0   	} 	set_val(T_OTS438, 100); 	vm_tctrl(0);wait( T_OTS1247 + T_OTS421); 	set_val(T_OTS438,0); 	vm_tctrl(0);wait(60)  } int T_OTS1251; int T_OTS620; int T_OTS619; int T_OTS622; int T_OTS621; fcombo T_OTS99 { 	set_val(T_OTS445, 0); 	vm_tctrl(0);wait( 30); 	set_val(T_OTS445, 100); 	vm_tctrl(0);wait( 60); 	} int T_OTS623; int T_OTS624; fcombo T_OTS100 { 	set_val(T_OTS444, 100); 	vm_tctrl(0);wait( T_OTS624); 	} define T_OTS1258 = TRUE; define T_OTS1259 = 95; define T_OTS1260 = 10; define T_OTS1261 = 70; define T_OTS1262 = FALSE; define T_OTS1263 = 50; define T_OTS1264 = 95; define T_OTS1265 = XB1_LT; define T_OTS1266 = XB1_RT; define T_OTS1267 = XB1_LX; define T_OTS1268 = XB1_LY; define T_OTS1269 = POLAR_LS; function T_OTS245() { 				if(T_OTS422 == TRUE){ 			if( combo_running(T_OTS102) && !get_ival(T_OTS441) ){set_val(T_OTS441,0);combo_run(T_OTS101);} 					} 					if(combo_running(T_OTS101)){ 						if(event_press(T_OTS441) || event_press(T_OTS438) || event_press(T_OTS445) || event_press(T_OTS444) || event_press(T_OTS443) ){combo_stop(T_OTS101);} 					} 	if ( get_ival(T_OTS443) > 30 &&    (get_ival(T_OTS442) > 30 ) &&    (!get_ival(T_OTS444) || !get_ival(T_OTS438))  ) { set_val(T_OTS443, 0); 		if(!get_ival(T_OTS438)){ 			set_val(T_OTS442,100); 					} 		else{ 			set_val(T_OTS442,0); 			set_val(T_OTS443,100); 					} 		  combo_run(T_OTS102); 		  if(T_OTS422 == TRUE){ 		  if(!get_ival(T_OTS441)){combo_run(T_OTS101);}else{combo_stop(T_OTS101);} 		  } 			} 	else { 		combo_stop(T_OTS102); 		combo_stop(T_OTS101); 			} 	} fcombo T_OTS101 { set_val(T_OTS441, 100); vm_tctrl(0); wait(600); vm_tctrl(0); wait(100); 	} fcombo T_OTS102 { 	if(!get_ival(T_OTS438)){ 		set_val(T_OTS442,100); 			} 	else{ 		set_val(T_OTS442,0); 			} 	set_val(T_OTS443, 100); 	vm_tctrl(-2);wait(T_OTS569); 	if(!get_ival(T_OTS438)){ 		set_val(T_OTS442,100); 			} 	else{ 		set_val(T_OTS442,0); 			}     set_val(T_OTS443, 0); 	vm_tctrl(-2); 	wait(200); 	} function T_OTS246(T_OTS229) { return T_OTS248(T_OTS229 + 8192); } function T_OTS248(T_OTS229) {   T_OTS229 = (T_OTS229 % 32767) << 17;   if((T_OTS229 ^ (T_OTS229 * 2)) < 0) { T_OTS229 = (-2147483648) - T_OTS229; }   T_OTS229 = T_OTS229 >> 17;   return T_OTS229 * ((98304) - (T_OTS229 * T_OTS229) >> 11) >> 14; } int T_OTS1272, T_OTS1273; function T_OTS250(T_OTS251, T_OTS252, T_OTS253, T_OTS254){   T_OTS254 = (T_OTS254 * 32767) / 100;   T_OTS253 = (T_OTS253 * 32767) / 10000;   T_OTS252 = (360 - T_OTS252) * 91;   T_OTS1273 = T_OTS248(T_OTS252); T_OTS1272 = T_OTS246(T_OTS252);   T_OTS252 = 32767 - T_OTS246(abs(abs(T_OTS1273) - abs(T_OTS1272)));   T_OTS253 = T_OTS253 * (32767 - ((T_OTS252 * T_OTS254) >> 15)) >> 15;   set_val(42 + T_OTS251, clamp((T_OTS253 * T_OTS1272) >> 15, -32767, 32767));   set_val(43 + T_OTS251, clamp((T_OTS253 * T_OTS1273) >> 15, -32767, 32767));   return; } int T_OTS1278, T_OTS1279; function T_OTS255() {    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && get_ival(T_OTS442) < 30 && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2)      && !get_ival(T_OTS440) &&  !get_ival(T_OTS443) && !get_ival(T_OTS439) && !get_ival(T_OTS441) && !get_ival(T_OTS445) && !get_ival(T_OTS438)){        combo_run(T_OTS103);    }else{    combo_stop(T_OTS103);    } } function T_OTS256(){   stickize(POLAR_LX, POLAR_LY, 141);   T_OTS1278 = get_ipolar(POLAR_LS, POLAR_RADIUS);   T_OTS1279 = get_ipolar(POLAR_LS, POLAR_ANGLE);   T_OTS250(POLAR_LS,  T_OTS1279,  T_OTS1278, T_OTS446 + random(0,1));   } fcombo T_OTS103 {  vm_tctrl(0); wait(750); T_OTS256(); vm_tctrl(0); wait(200); 	} fcombo T_OTS104 { 	set_val(T_OTS439,100); 	vm_tctrl(0);wait( T_OTS620); 	set_val(T_OTS439,  0); 	vm_tctrl(0);wait( 30); 	if(T_OTS390){ 		set_val(T_OTS441,100); 			} 	vm_tctrl(0);wait( 60); 	} fcombo T_OTS105 { 	if(T_OTS390)set_val(T_OTS441,  100); 	vm_tctrl(0);wait( 60);     wait( 60);     vm_tctrl(0);wait(850);     if(T_OTS391 && !get_ival(T_OTS438) && !get_ival(T_OTS443) && !get_ival(T_OTS439) && !block && !get_ival(T_OTS441) && get_ival(T_OTS442) < 30 )set_val(T_OTS442,  100);     wait(500); 	} fcombo T_OTS106 { 	set_val(T_OTS445,100); 	vm_tctrl(0);wait( T_OTS622); 	set_val(T_OTS445,  0); 	vm_tctrl(0);wait( 30); 	if(T_OTS387){ 		set_val(T_OTS445,100); 			} 	vm_tctrl(0);wait( 60); 	} int T_OTS959 int T_OTS1284 fcombo T_OTS107 { 	combo_suspend(T_OTS107) 	vm_tctrl(0);wait(361) } function T_OTS257 (){ 	if(T_OTS959[T_OTS1284] != 361){ 	    T_OTS1284-- 	} 	else{ 		if(inv(T_OTS1284) != 267){ 			T_OTS257(); 		} 	} } int T_OTS165; function T_OTS258(T_OTS259, T_OTS260, T_OTS261) {   T_OTS165 = get_ipolar(T_OTS259, POLAR_RADIUS);   if(T_OTS260) {     if(T_OTS165 <= T_OTS260) T_OTS165 = (T_OTS165 * 5000) / T_OTS260;     else T_OTS165 = ((5000 * (T_OTS165 - T_OTS260)) / (10000 - T_OTS260)) + 5000;   }   if (T_OTS261) T_OTS165 = (T_OTS165 * T_OTS261) / 10000;   set_polar2(T_OTS259, get_ipolar(T_OTS259, POLAR_ANGLE), min(T_OTS165, 14142));   if (T_OTS259 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 141);   else stickize(ANALOG_LX, ANALOG_LY, 141);   return; } fcombo T_OTS108{ } 