// Concentric2 Aim Assist Pattern
// Activated by XB1_Left and XB1_Right

define POLAR_MAX = 32767;     // Maximum polar value
define POLAR_MIN = -32768;    // Minimum polar value
define ANGLE_STEP = 8;        // Angle increment per frame
define RADIUS_STEP = 500;     // Radius increment
define MAX_RADIUS = 20000;    // Maximum radius
define MIN_RADIUS = 2000;     // Minimum radius

int active;                   // Pattern active flag
int current_angle;           // Current angle
int current_radius;         // Current radius
int radius_direction;       // 1 for increasing, -1 for decreasing

init {
    active = 0;
    current_angle = 0;
    current_radius = MIN_RADIUS;
    radius_direction = 1;
}

main {
    // Check if both XB1_Left and XB1_Right are pressed
    if (get_val(XB1_LT) && get_val(XB1_RT)) {
        active = 1;
    } else {
        active = 0;
    }

    // Apply the Concentric2 pattern when active
    if (active) {
        // Update angle (continuous rotation)
        current_angle = (current_angle + ANGLE_STEP) % 360;
        
        // Update radius (oscillating between MIN_RADIUS and MAX_RADIUS)
        current_radius += RADIUS_STEP * radius_direction;
        
        // Change direction when reaching limits
        if (current_radius >= MAX_RADIUS) {
            radius_direction = -1;
            current_radius = MAX_RADIUS;
        } else if (current_radius <= MIN_RADIUS) {
            radius_direction = 1;
            current_radius = MIN_RADIUS;
        }

        // Apply the polar pattern to left stick
        set_polar(POLAR_LS, current_angle, current_radius);
    }
}
