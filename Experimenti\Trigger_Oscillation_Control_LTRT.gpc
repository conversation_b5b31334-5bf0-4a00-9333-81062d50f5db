// ===========================
//      Global Variables
// ===========================
int triggerTimer_RT = 0;            // Timer to manage RT oscillation timing
int triggerValue_RT = 70;        // Initial trigger value for RT (starts at 70)
int increasing_RT = TRUE;           // Start by increasing triggerValue_RT
int triggerTimer_LT = 0;            // Timer to manage LT oscillation timing
int triggerValue_LT = 100;       // Initial trigger value for LT (starts at 100)
int increasing_LT = FALSE;          // Start by decreasing triggerValue_LT

define TriggerControl_RT = XB1_RT; // Right Trigger
define TriggerControl_LT = XB1_LT; // Left Trigger
// It can be any button that supports a range of values.
// For example, L2 or R2 can have values between 0 and 100,
// while R1 and L1 are either 0 or 100, meaning no oscillation is needed for them.

// Speed Control: Change oscillation speed .
// Lower values = faster oscillation, higher values = slower oscillation
int speedControl = 20;
int rt_pressed;
int lt_pressed;
// ===========================
//        Main Block
// ===========================
main {

    rt_pressed = get_ival(TriggerControl_RT);
    lt_pressed = get_ival(TriggerControl_LT);

    // Handle Right Trigger
    if (rt_pressed > 0) {
        Trigger_Oscillation_Control_RT(); // Calculate next oscillation value for RT
        set_val(TriggerControl_RT, triggerValue_RT); // Apply the oscillating value
    } else {
        Reset_Trigger_Oscillation_RT(); // Reset RT oscillation state
        set_val(TriggerControl_RT, 0); // Set RT output to 0 when not pressed
    }

    // Handle Left Trigger
    if (lt_pressed > 0) {
        Trigger_Oscillation_Control_LT(); // Calculate next oscillation value for LT
        set_val(TriggerControl_LT, triggerValue_LT); // Apply the oscillating value
    } else {
        Reset_Trigger_Oscillation_LT(); // Reset LT oscillation state
        set_val(TriggerControl_LT, 0); // Set LT output to 0 when not pressed
    }
}

// ===========================
//   Trigger_Oscillation_Control_RT
// ===========================
// Oscillates RT value between 70 and 100.
function Trigger_Oscillation_Control_RT() {
    // Call get_rtime() once and store the value to track elapsed time
    triggerTimer_RT = triggerTimer_RT + get_rtime();

    // Ensure triggerValue_RT stays within valid range
    if (triggerValue_RT < 70) { triggerValue_RT = 70; }
    if (triggerValue_RT > 100) { triggerValue_RT = 100; }

    // Check if it's time to update the triggerValue
    if (triggerTimer_RT >= speedControl) {
        triggerTimer_RT = triggerTimer_RT - speedControl;

        // Update triggerValue_RT based on direction
        if (increasing_RT) {
            triggerValue_RT = triggerValue_RT + 1;
            if (triggerValue_RT >= 100) {
                triggerValue_RT = 100;
                increasing_RT = FALSE;
            }
        } else {
            triggerValue_RT = triggerValue_RT - 1;
            if (triggerValue_RT <= 70) {
                triggerValue_RT = 70;
                increasing_RT = TRUE;
            }
        }
    }
    // Note: set_val is now handled in the main loop
}

// ===========================
//   Trigger_Oscillation_Control_LT
// ===========================
// Oscillates LT value between 70 and 100.
function Trigger_Oscillation_Control_LT() {
    // Call get_rtime() once and store the value to track elapsed time
    triggerTimer_LT = triggerTimer_LT + get_rtime();

    // Ensure triggerValue_LT stays within valid range
    if (triggerValue_LT < 70) { triggerValue_LT = 70; }
    if (triggerValue_LT > 100) { triggerValue_LT = 100; }

    // Check if it's time to update the triggerValue
    if (triggerTimer_LT >= speedControl) {
        triggerTimer_LT = triggerTimer_LT - speedControl;

        // Update triggerValue_LT based on direction
        if (increasing_LT) {
            triggerValue_LT = triggerValue_LT + 1;
            if (triggerValue_LT >= 100) {
                triggerValue_LT = 100;
                increasing_LT = FALSE;
            }
        } else {
            triggerValue_LT = triggerValue_LT - 1;
            if (triggerValue_LT <= 70) {
                triggerValue_LT = 70;
                increasing_LT = TRUE;
            }
        }
    }
    // Note: set_val is now handled in the main loop
}

// ===========================
// Reset_Trigger_Oscillation_RT
// ===========================
// Resets the RT triggerValue and direction to the initial state.
function Reset_Trigger_Oscillation_RT() {
    triggerValue_RT = 70;     // Reset RT to initial state
    increasing_RT = TRUE;     // Reset RT to start increasing
    triggerTimer_RT = 0;      // Reset RT timer
}

// ===========================
// Reset_Trigger_Oscillation_LT
// ===========================
// Resets the LT triggerValue and direction to the initial state.
function Reset_Trigger_Oscillation_LT() {
    triggerValue_LT = 100;    // Reset LT to initial state (start at max)
    increasing_LT = FALSE;    // Reset LT to start decreasing
    triggerTimer_LT = 0;      // Reset LT timer
}