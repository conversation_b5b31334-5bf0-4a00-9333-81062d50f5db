int LRInterval = 9000;
int startDirection; // Variable to store the random starting direction
int randomSeed = 12345; // Initial seed value
int isRunning = 0; // Flag to track if combo is running

init {
    // Initialize random seed using system time
    randomSeed = get_rtime();
}

main {
    if(get_val(XB1_LS) && !isRunning) {
        startDirection = getRandomRange(0, 1); // Random value: 0 or 1
        isRunning = 1;
        combo_run(aaabbbccc);
    }
    
    if(event_release(XB1_LS)) {
        combo_stop(aaabbbccc);
        isRunning = 0;
        // Reset stick position
        set_val(XB1_LX, 0);
        set_val(XB1_LY, 0);
    }
}

// Function to generate a pseudo-random number
function getRandomNumber() {
    randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
    return randomSeed;
}

// Get random number between min and max
function getRandomRange(min, max) {
    return (getRandomNumber() % (max - min + 1)) + min;
}

combo aaabbbccc {
    if(startDirection == 0) {
        // Start with left (-45)
         set_val(XB1_LB, 100);

        set_polar(POLAR_LS, -70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    } else {
        // Start with right (45)
        set_val(XB1_LB, 100);

        set_polar(POLAR_LS, 70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    }
    wait(LRInterval);
    
    if(startDirection == 0) {
    set_val(XB1_LB, 100);

        set_polar(POLAR_LS, 70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    } else {
    set_val(XB1_LB, 100);

        set_polar(POLAR_LS, -70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    }
    wait(LRInterval);
}