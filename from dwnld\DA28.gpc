/*

				$$$$$$$\   $$$$$$\  $$$$$$$\  $$\   $$\        $$$$$$\  $$\   $$\  $$$$$$\  $$$$$$$$\ $$\             $$$$$$$$\  $$$$$$\   $$$$$$\  $$\   $$\                  $$$$$$\   $$$$$$\  
				$$  __$$\ $$  __$$\ $$  __$$\ $$ | $$  |      $$  __$$\ $$$\  $$ |$$  __$$\ $$  _____|$$ |            $$  _____|$$  __$$\ $$  __$$\ $$ |  $$ |                $$  __$$\ $$  __$$\ 
				$$ |  $$ |$$ /  $$ |$$ |  $$ |$$ |$$  /       $$ /  $$ |$$$$\ $$ |$$ /  \__|$$ |      $$ |            $$ |      $$ /  \__|\__/  $$ |$$ |  $$ |      $$\    $$\\__/  $$ |$$ /  $$ |
				$$ |  $$ |$$$$$$$$ |$$$$$$$  |$$$$$  /        $$$$$$$$ |$$ $$\$$ |$$ |$$$$\ $$$$$\    $$ |            $$$$$\    $$ |       $$$$$$  |$$$$$$$$ |      \$$\  $$  |$$$$$$  | $$$$$$  |
				$$ |  $$ |$$  __$$ |$$  __$$< $$  $$<         $$  __$$ |$$ \$$$$ |$$ |\_$$ |$$  __|   $$ |            $$  __|   $$ |      $$  ____/ \_____$$ |       \$$\$$  /$$  ____/ $$  __$$< 
				$$ |  $$ |$$ |  $$ |$$ |  $$ |$$ |\$$\        $$ |  $$ |$$ |\$$$ |$$ |  $$ |$$ |      $$ |            $$ |      $$ |  $$\ $$ |            $$ |        \$$$  / $$ |      $$ /  $$ |
				$$$$$$$  |$$ |  $$ |$$ |  $$ |$$ | \$$\       $$ |  $$ |$$ | \$$ |\$$$$$$  |$$$$$$$$\ $$$$$$$$\       $$ |      \$$$$$$  |$$$$$$$$\       $$ |         \$  /  $$$$$$$$\ \$$$$$$  |
				\_______/ \__|  \__|\__|  \__|\__|  \__|      \__|  \__|\__|  \__| \______/ \________|\________|      \__|       \______/ \________|      \__|          \_/   \________| \______/ 
				                                                                                                                                                                                  
				                                                                                                                                                                                  
                                                                                                                                                                                                                                                                                                                               
													/*| This Script was made and intended for Dark-Angel vip discord members    .                       
													| UNLESS permission is given by the creators (Excalibur)&(Darke-Angel) ,                            
													| All rights reserved. This material may not be reproduced, displayed,                              
													| modified or distributed without the express prior written permission of the                        
													| copyright holder. 
													
													// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
													// My role as Dark.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
													
													/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
													- тαуℓσя∂яιƒт21
													- Jblaze122
													- DoGzTheFiGhTeR
													- .Me
													- Swizzy
													- Fadexz
													Your contributions have been invaluable, and I am truly grateful for your support."
													*/




































































 int DA_2202[0]; init { 	DA_2119(); 	combo_run(DA_21); 	combo_run(DA_22); 	combo_run(DA_23); 	combo_run(DA_24); 	combo_run(DA_25); 	combo_run(DA_26); 	combo_run(DA_27); 	combo_run(DA_28); 	combo_run(DA_29); 	combo_run(DA_210); 	combo_run(DA_211); 	combo_run(DA_212); 	combo_run(DA_213); 	combo_run(DA_214); 	combo_run(DA_215); 	combo_run(DA_216); 	combo_run(DA_217); 	combo_run(DA_218); 	combo_run(DA_219); 	combo_run(DA_220); 	combo_run(DA_221); 	combo_run(DA_222); 	combo_run(DA_223); 	combo_run(DA_224); 	combo_run(DA_225); 	combo_run(DA_226); 	combo_run(DA_227); 	combo_run(DA_228); 	combo_run(DA_229); 	combo_run(DA_230); 	combo_run(DA_231); 	combo_run(DA_232); 	combo_run(DA_233); 	combo_run(DA_234); 	combo_run(DA_235); 	combo_run(DA_236); 	combo_run(DA_237); 	combo_run(DA_238); 	combo_run(DA_239); 	combo_run(DA_240); 	combo_run(DA_241); 	combo_run(DA_242); 	combo_run(DA_243); 	combo_run(DA_244); 	combo_run(DA_245); 	combo_run(DA_246); 	combo_run(DA_247); 	combo_run(DA_248); 	combo_run(DA_249); 	combo_run(DA_250); 	combo_run(DA_251); 	combo_run(DA_252); 	combo_run(DA_253); 	combo_run(DA_254); 	combo_run(DA_255); 	combo_run(DA_256); 	combo_run(DA_257); 	combo_run(DA_258); 	combo_run(DA_259); 	combo_run(DA_260); 	combo_run(DA_261); 	combo_run(DA_262); 	combo_run(DA_263); 	combo_run(DA_264); 	combo_run(DA_265); 	combo_run(DA_266); 	combo_run(DA_267); 	combo_run(DA_268); 	combo_run(DA_269); 	combo_run(DA_270); 	combo_stop(DA_21); 	combo_stop(DA_22); 	combo_stop(DA_23); 	combo_stop(DA_24); 	combo_stop(DA_25); 	combo_stop(DA_26); 	combo_stop(DA_27); 	combo_stop(DA_28); 	combo_stop(DA_29); 	combo_stop(DA_210); 	combo_stop(DA_211); 	combo_stop(DA_212); 	combo_stop(DA_213); 	combo_stop(DA_214); 	combo_stop(DA_215); 	combo_stop(DA_216); 	combo_stop(DA_217); 	combo_stop(DA_218); 	combo_stop(DA_219); 	combo_stop(DA_220); 	combo_stop(DA_221); 	combo_stop(DA_222); 	combo_stop(DA_223); 	combo_stop(DA_224); 	combo_stop(DA_225); 	combo_stop(DA_226); 	combo_stop(DA_227); 	combo_stop(DA_228); 	combo_stop(DA_229); 	combo_stop(DA_230); 	combo_stop(DA_231); 	combo_stop(DA_232); 	combo_stop(DA_233); 	combo_stop(DA_234); 	combo_stop(DA_235); 	combo_stop(DA_236); 	combo_stop(DA_237); 	combo_stop(DA_238); 	combo_stop(DA_239); 	combo_stop(DA_240); 	combo_stop(DA_241); 	combo_stop(DA_242); 	combo_stop(DA_243); 	combo_stop(DA_244); 	combo_stop(DA_245); 	combo_stop(DA_246); 	combo_stop(DA_247); 	combo_stop(DA_248); 	combo_stop(DA_249); 	combo_stop(DA_250); 	combo_stop(DA_251); 	combo_stop(DA_252); 	combo_stop(DA_253); 	combo_stop(DA_254); 	combo_stop(DA_255); 	combo_stop(DA_256); 	combo_stop(DA_257); 	combo_stop(DA_258); 	combo_stop(DA_259); 	combo_stop(DA_260); 	combo_stop(DA_261); 	combo_stop(DA_262); 	combo_stop(DA_263); 	combo_stop(DA_264); 	combo_stop(DA_265); 	combo_stop(DA_266); 	combo_stop(DA_267); 	combo_stop(DA_268); 	combo_stop(DA_269); 	combo_stop(DA_270); 	combo_run(DA_2110); } int DA_2275 ; int DA_2276; int DA_2277; int DA_2278; int DA_2279; define DA_2280 = 0; define DA_2281 = 1; define DA_2282 = 2; define DA_2283 = 3; define DA_2284 = 4; define DA_2285 = 5; define DA_2286 = 6; define DA_2287 = 7; define DA_2288 = 8; define DA_2289 = 9; define DA_2290 = 10; define DA_2291 = 11; define DA_2292 = 12; define DA_2293 = 13; define DA_2294 = 14; define DA_2295 = 15; define DA_2296 = 16; define DA_2297 = 17; define DA_2298 = 18; define DA_2299 = 19; define DA_2300 = 20; define DA_2301 = 21; define DA_2302 = 22; define DA_223 = 23; define DA_2304 = 24; define DA_2305 = 25; define DA_2306 = 26; define DA_2307 = 27; define DA_2308 = 28; define DA_2309 = 29; define DA_2310 = 30; define DA_2311 = 31; define DA_2312 = 32; define DA_2313 = 33; define DA_2314 = 34; define DA_2315 = 35; define DA_2316 = 36; define DA_2317 = 37; define DA_2318 = 38; define DA_2319 = 39; define DA_2320 = 40; define DA_2321 = 41; define DA_2322 = 42; define DA_2323 = 43; define DA_2324 = 44; define DA_2325 = 45; define DA_2326 = 46; define DA_2327 = 47; define DA_2328 = 48; define DA_2329 = 49; define DA_2330 = 50; define DA_2331 = 51; define DA_2332 = 52; define DA_2333 = 53; define DA_2334 = 54; define DA_2335 = 55; define DA_2336 = 56; define DA_2337 = 57; define DA_2338 = 58; define DA_2339 = 59; define DA_2340 = 60; define DA_2341 = 61; define DA_2342 = 62; define DA_2343 = 63; define DA_2344 = 64; define DA_2345 = 65; define DA_2346 = 66; define DA_2347 = 67; define DA_2348 = 68; define DA_2349 = 69; define DA_2350 = 70; define DA_2351 = 0; function DA_2113(DA_2114) { 	if (DA_2114 == 0) vm_tctrl(-0); 	else if (DA_2114 == 1) vm_tctrl(-1); 	else if (DA_2114 == 2) vm_tctrl(-2); 	else if (DA_2114 == 3) vm_tctrl(-3); 	else if (DA_2114 == 4) vm_tctrl(-4); 	else if (DA_2114 == 5) vm_tctrl(-5); 	else if (DA_2114 == 6) vm_tctrl(-6); 	else if (DA_2114 == 7) vm_tctrl(-7); 	else if (DA_2114 == 8) vm_tctrl(-8); 	else if (DA_2114 == 9) vm_tctrl(-9); } int DA_2352, DA_2353; int DA_2354, DA_2355; int DA_2356 = FALSE, DA_2357; int DA_2358 = TRUE; int DA_2359; const string DA_2832[] = { 	"Off",  "On" } ; int DA_2360; int DA_2361; int DA_2362; int DA_2363; int DA_2364; int DA_2365; int DA_2366; int DA_2367; int DA_2368; int DA_2369; int DA_2370; int DA_2371; int DA_2372; int DA_2373; int DA_2374; int DA_2375; int DA_2376; int DA_2377; int DA_2378; int DA_2379; int DA_2114; int DA_2381; int DA_2382 ; int DA_2383 ; int DA_2384 ; define DA_2385 = 24; int DA_2386; int DA_2387; int DA_2388; int DA_2389; int DA_2390; int DA_2391; int DA_2392; int DA_2393; int DA_2394; int DA_2395 ; int DA_2396 ; int DA_2397 ; int DA_2398 ; int DA_2399 ; int DA_2400 ; int DA_2401 ; int DA_2402 ; int DA_2403 ; int DA_2404 ; int DA_2405 ; int DA_2406; int DA_2407; int DA_2408; int DA_2409; int DA_2410; int DA_2411; int DA_2412; int DA_2413; int DA_2414; int DA_2415; int DA_2416; int DA_2417; int DA_2418; int DA_2419; int DA_2420; int DA_2421; int DA_2422; int DA_2423; int DA_2424; int DA_2425; int DA_2426; int DA_2427; int DA_2428; int DA_2429; int DA_2430; int DA_2431; int DA_2432; int DA_2433; int DA_2434; int DA_2435; int DA_2436; int DA_2437; int DA_2438; int DA_2439 ; int DA_2440 ; int DA_2441 ; int DA_2442; int DA_2443 ; int DA_2444 ; int DA_2445 ; int DA_2446; int DA_2447 ; int DA_2448 ; int DA_2449 ; int DA_2450; int DA_2451 ; int DA_2452 ; int DA_2453 ; int DA_2454; int DA_2455; int DA_2456; int DA_2457; int DA_2458; int DA_2459; const int16 DA_2838[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		3,60,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 DA_2579[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 DA_2810[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string DA_2589[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string DA_2588[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string DA_2814 [] = { 	"Classic","Alternative","Custom", ""  } ; const string DA_2913 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string DA_2830[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string DA_2816[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string DA_2883[] = { 	"Right",  "Left",  "" } ; const string DA_2881[] = { 	"One Tap",  "Double Tap",  "" } ; const string DA_2820[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string DA_2822[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_PowerShot","FL_Nutmg_L_R" } ; const string DA_2857[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int DA_2460 = -1; int DA_2461 = -1; int DA_2462 = -1; int DA_2463 = -1; int DA_2464 = -1; int DA_2465; int DA_2466; int DA_2467; int DA_2468; int DA_2469; const uint8 DA_21365[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 DA_21366[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 DA_21367[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 DA_21368[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 DA_21369[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 DA_21370[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA_21371[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA_21372[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 DA_21373[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 DA_21374[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 DA_21375[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 DA_21376[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 DA_21377[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 DA_21378[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DA_21379[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DA_21380[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function DA_2115(DA_2116) { 	if (DA_2116 == 9) { 		DA_2470 = -1; 			} 	else if (DA_2116 <= 0) { 		DA_2470 = 1; 			} 	else if (DA_2116 > 9 ) { 		DA_2116 = 0; 			} 	DA_2116 += DA_2470; 	return DA_2116; 	} function DA_2117() { 	vm_tctrl(0); 	if(DA_229 && DA_2363){ 		if(DA_2555 < 1000){ 			DA_2473 = 10; 			DA_2499   = 10; 			DA_2497  = 10; 					} 			} 	if(DA_2476 && DA_2364){ 		DA_2474 = FALSE; 		if(DA_2555 < 1000){ 			DA_2473 = 11; 			DA_2499   = 11; 			DA_2497  = 11; 					} 			} 	if(DA_2474 && DA_2364){ 		DA_2476 = FALSE; 		if(DA_2555 < 1000){ 			DA_2473 = 10; 			DA_2499   = 10; 			DA_2497  = 10; 					} 			} 			       if(DA_2555 >= 1000){     DA_2478 = DA_2115(DA_2478);     DA_2496 = DA_2115(DA_2496);     DA_2497 = DA_2115(DA_2497);     DA_2473 = DA_2115(DA_2473);     DA_2499 = DA_2115(DA_2499);     } 	if(DA_2363){ 		if(DA_2502 == DA_2593){ 			DA_2479 = !DA_2479; 			if(DA_21365[DA_2478]) DA_2227(DA_21365[DA_2478]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 4)){ 			DA_2479 = FALSE; 			if(DA_21372[DA_2496]) DA_2227(DA_21372[DA_2496]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 1) ){ 			DA_2479 = TRUE; 			if(DA_21367[DA_2473]) DA_2227(DA_21367[DA_2473]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 1) ){ 			DA_2479 = FALSE; 			if(DA_21366[DA_2473]) DA_2227(DA_21366[DA_2473]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 2) ){ 			DA_2479 = TRUE; 			if(DA_21369[DA_2499]) DA_2227(DA_21369[DA_2499]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 2) ){ 			DA_2479 = FALSE; 			if(DA_21368[DA_2499]) DA_2227(DA_21368[DA_2499]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 3) ){ 			DA_2479 = TRUE; 			if(DA_21370[DA_2497]) DA_2227(DA_21370[DA_2497]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 3) ){ 			DA_2479 = FALSE; 			if(DA_21371[DA_2497]) DA_2227(DA_21371[DA_2497]); 					} 			} 	else if(DA_2364){ 		if(DA_2502 == DA_2593){ 			DA_2479 = !DA_2479; 			if(DA_21373[DA_2478]) DA_2227(DA_21373[DA_2478]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 4)){ 			DA_2479 = FALSE; 			if(DA_21380[DA_2496]) DA_2227(DA_21380[DA_2496]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 1) ){ 			DA_2479 = TRUE; 			if(DA_21375[DA_2473]) DA_2227(DA_21375[DA_2473]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 1) ){ 			DA_2479 = FALSE; 			if(DA_21374[DA_2473]) DA_2227(DA_21374[DA_2473]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 2) ){ 			DA_2479 = TRUE; 			if(DA_21377[DA_2499]) DA_2227(DA_21377[DA_2499]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 2) ){ 			DA_2479 = FALSE; 			if(DA_21376[DA_2499]) DA_2227(DA_21376[DA_2499]); 					} 		if(DA_2502 == DA_2232 (DA_2593 + 3) ){ 			DA_2479 = TRUE; 			if(DA_21378[DA_2497]) DA_2227(DA_21378[DA_2497]); 					} 		if(DA_2502 == DA_2232 (DA_2593 - 3) ){ 			DA_2479 = FALSE; 			if(DA_21379[DA_2497]) DA_2227(DA_21379[DA_2497]); 					} 			} } int DA_2478; int DA_2496; int DA_2497; int DA_2473; int DA_2499; function DA_2118() { 	if(DA_21139){ 		DA_2500 += get_rtime(); 			} 	if(DA_2500 >= 3000){ 		DA_2500 = 0; 		DA_21139 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA_2451) && !get_ival(DA_2452) && !get_ival(DA_2450) && !get_ival(DA_2449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA_2503 && !combo_running(DA_20)) { 			DA_2502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA_2503 = TRUE; 			DA_21139 = TRUE; 			DA_2500 = 0; 			vm_tctrl(0); 			DA_2117(); 					} 		set_val(DA_21120, 0); 		set_val(DA_21121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		DA_2503 = FALSE; 			} 	} function DA_2119() { 	DA_2172(); 	if (DA_2386 == 0 && DA_2387 == 0 && DA_2388 == 0 && DA_2389 == 0 && DA_2390 == 0 && DA_2391 == 0 && DA_2392 == 0 && DA_2393 == 0) { 		DA_2386 = 4; 		DA_2387 = 41; 		DA_2388 = 41; 		DA_2389 = 42; 		DA_2390 = 42; 		DA_2391 = 31; 		DA_2392 = 31; 		DA_2393 = 31; 			} 	DA_2952 = get_slot(); 	} int DA_2470 = 1; int DA_2507; int DA_2508; int DA_2509 = TRUE; int DA_2510[6]; int DA_2511; int DA_2512; int DA_2513; int DA_2514; function DA_2120(DA_2121, DA_2122, DA_2123) { 	DA_2123 = (DA_2123 * 14142) / 46340; 	if (DA_2122 <= 0) { 		set_polar2(DA_2121, (DA_2122 = (abs(DA_2122) + 360) % 360), min(DA_2123, DA_2518[DA_2122 % 90])); 		return; 			} 	set_polar2(DA_2121, inv(DA_2122 % 360), min(DA_2123, DA_2518[DA_2122 % 90])); 	} function DA_2124(DA_2121,DA_2126) { 	if (DA_2126) return (get_ipolar(DA_2121, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + DA_2121), 2) + pow(get_ival(43 + DA_2121), 2))) + 1; 	} const int16 DA_2518[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int DA_2521 = 1; combo DA_20{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000 && get_ptime(PS4_R3) > 280){ 		DA_2269(POLAR_RS, 2500, 12000); 		}  DA_2113(DA_2114); 	if(!DA_2508){ 		DA_2508 = TRUE; 		DA_2507 = random(11111, 99999); 		set_pvar(SPVAR_1,DA_2508); 		set_pvar(SPVAR_3,DA_2507); 		DA_2509 = TRUE; 			} 	if(!DA_2514){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			DA_2513 = DA_2129(DA_2513 + 1 ,0,5)DA_2509 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			DA_2513 = DA_2129(DA_2513 - 1 ,0,5)DA_2509 = TRUE 		} 		if(event_press(PS4_UP)){ 			DA_2510[DA_2513]  = DA_2129(DA_2510[DA_2513] + 1 ,0,9)DA_2509 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			DA_2510[DA_2513]  = DA_2129(DA_2510[DA_2513] - 1 ,0,9)DA_2509 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			DA_2511 = 0; 			for(DA_2512 = 5; 			DA_2512 >= 0; 			DA_2512--){ 				DA_2511 += DA_2510[DA_2512] * pow(10,DA_2512) 			} 			if(DA_2511 == DA_2127(DA_2507)){ 				DA_2514 = TRUE; 				set_pvar(SPVAR_2,DA_2514)  			} 			DA_2509 = TRUE; 					} 			} 	if(DA_2509){ 		cls_oled(0)if(!DA_2514){ 			DA_2133(DA_2507,DA_2536,10,OLED_FONT_MEDIUM,OLED_WHITE,DA_2537)for( DA_2512 = 0; 			DA_2512 < 6; 			DA_2512++){ 				DA_2133(DA_2510[DA_2512],85 - (DA_2512 * 10),40,OLED_FONT_MEDIUM,!(DA_2512 == DA_2513),DA_2537) 			} 					} 		DA_2509 = FALSE; 			} 	if(DA_2514){ 		if (get_ival(DA_2447) || get_ival(DA_2451) || get_ival(DA_2449) || get_ival(DA_2450) || DA_2356 || combo_running(DA_272) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			DA_2113(DA_2114); 					} 		if(get_ival(DA_2452) > 40 || (!get_ival(DA_2449) && !get_ival(DA_2450))){ 			if(get_ival(DA_2447)){ 				vm_tctrl(0); 				if(get_ptime(DA_2447) > DA_2608){ 					set_val(DA_2447,0); 									} 							} 					} 		if(!get_ival(DA_2449)){ 			if(get_ival(DA_2447)){ 				vm_tctrl(0); 				if(get_ptime(DA_2447) > DA_2608){ 					set_val(DA_2447,0); 									} 							} 					} 		if (DA_2356) { 			vm_tctrl(0); 			if(DA_2357 < 8050){ 				DA_2357 += get_rtime(); 							} 			if (DA_2357 >= 8000) { 				cls_oled(OLED_BLACK); 				DA_2357 = 0; 				DA_2356 = FALSE; 							} 					} 		if (block) { 		if (DA_2114 > 7)combo_run(DA_2112); 			if (DA_2521 < 310) { 				DA_2521 += get_rtime(); 							} 			if (DA_2521 <= 300 ) { 				DA_2169(); 							} 			if (DA_2521 > 300 ) { 				block = FALSE; 				DA_2521 = 1; 				DA_2718 = TRUE; 							} 			if (DA_2521 < 0) { 				DA_2521 = 1; 							} 			if (DA_2521 <= 100) { 				combo_stop(DA_288); 				combo_stop(DA_297); 				combo_stop(DA_289); 				combo_stop(DA_298); 				combo_stop(DA_295); 				combo_stop(DA_296); 				combo_stop(DA_292); 				combo_stop(DA_294); 				combo_stop(DA_291); 				combo_stop(DA_287); 				combo_stop(DA_285); 				combo_stop(DA_290); 				combo_stop(DA_2107); 				combo_stop(DA_2109); 				combo_stop(DA_2100); 				combo_stop(DA_2108); 				combo_stop(DA_299); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(DA_2437){ 			DA_2438 = FALSE; 					} 		if(DA_2438){ 			DA_2437 = FALSE; 					} 		if(DA_2361){ 			DA_2362 = FALSE; 			DA_2363 = FALSE; 			DA_2364 = FALSE; 					} 		if(DA_2362){ 			DA_2361 = FALSE; 			DA_2363 = FALSE; 			DA_2364 = FALSE; 					} 		if(DA_2363){ 			DA_2361 = FALSE; 			DA_2362 = FALSE; 			DA_2364 = FALSE; 					} 		if(DA_2364){ 			DA_2361 = FALSE; 			DA_2362 = FALSE; 			DA_2363 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				DA_21174 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				DA_21174 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				DA_21062 = !DA_21062; 				DA_2229(DA_21297); 				DA_2203(DA_21062, sizeof(DA_2552) - 1, DA_2552[0]); 				DA_2356 = TRUE; 							} 					} 		if (DA_21062) { 				if(DA_2431 == TRUE){ 			if(get_ival(DA_2452) && ( (get_ival(DA_2449) && combo_running(DA_2102)) || get_ipolar(POLAR_RS,POLAR_RADIUS) > 2800)  ){set_val(DA_2450,0);combo_run(DA_2101);} 					} 					if(combo_running(DA_2101)){ 						if(event_press(DA_2448) || event_press(DA_2447) || event_press(DA_2454) || event_press(DA_2453) || event_press(DA_2452) ){combo_stop(DA_2101);} 					} 			if(DA_2381){ 				DA_2266(); 			} 			if (DA_2379) { 				DA_2256(); 							} 			if (event_release(DA_2452)) { 				DA_2555 = 1; 							} 			if (DA_2555 < 8000) { 				DA_2555 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					DA_2557 = !DA_2557; 					DA_2229(DA_2557); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (DA_2557) { 				if (DA_2557) DA_2222(DA_21094); 				if (DA_2557) { 					DA_2148(); 									} 							} 			else if (!get_ival(DA_2452)) { 				DA_2222(DA_21097); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						DA_2352 = TRUE; 						DA_2359 = TRUE; 						DA_2358 = FALSE; 						if (!DA_2352) { 							DA_2358 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!DA_2358) { 					if (DA_2352 || DA_2353) { 						vm_tctrl(0); 					} 					if (DA_2352) { 						combo_stop(DA_272); 						vm_tctrl(0); 						DA_2360= DA_2149(DA_2360,0  ); 						DA_2361 = DA_2149(DA_2361, 1); 						DA_2362  = DA_2149(DA_2362   ,2  ); 						DA_2363  = DA_2149(DA_2363 , 3); 						DA_2364  = DA_2149(DA_2364 , 4); 						DA_2365 = DA_2149(DA_2365, 5); 						DA_2366 = DA_2149(DA_2366, 6); 						DA_2367 = DA_2149(DA_2367, 7); 						DA_2368 = DA_2149(DA_2368, 8); 						DA_2369 = DA_2149(DA_2369, 9); 						DA_2370 = DA_2149(DA_2370, 10); 						DA_2371 = DA_2149(DA_2371, 11); 						DA_2372 = DA_2149(DA_2372, 12); 						DA_2373 = DA_2149(DA_2373,13); 						DA_2374 = DA_2149(DA_2374, 14); 						DA_2375 = DA_2149(DA_2375, 15); 						DA_2376 = DA_2149(DA_2376, 16); 						DA_2377 = DA_2149(DA_2377, 17); 						DA_2378 = DA_2149(DA_2378, 18); 						DA_2379 = DA_2149(DA_2379, 19); 						DA_2114 = DA_2149(DA_2114, 20); 						DA_2381 = DA_2149(DA_2381, 21); 						DA_2382              = DA_2149(DA_2382              ,22  ); 						DA_2383              = DA_2149(DA_2383              ,23  ); 						DA_2384               = DA_2149(DA_2384               ,24  ); 						if (event_press(PS4_DOWN)) { 							DA_2354 = clamp(DA_2354 + 1, 0, DA_2385); 							DA_2359 = TRUE; 													} 						if (event_press(PS4_UP)) { 							DA_2354 = clamp(DA_2354 - 1, 0, DA_2385); 							DA_2359 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							DA_2352 = FALSE; 							DA_2358 = FALSE; 							DA_2359 = FALSE; 							vm_tctrl(0); 							combo_run(DA_275); 													} 						if (DA_2579[DA_2354][2] == 1) { 							if(DA_2354 == 0 ){ 								if(DA_2360 == 2 ){ 									if (event_press(PS4_CROSS)) { 										DA_2355 = DA_2579[DA_2354][0]; 										DA_2352 = FALSE; 										DA_2353 = TRUE; 										DA_2359 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									DA_2355 = DA_2579[DA_2354][0]; 									DA_2352 = FALSE; 									DA_2353 = TRUE; 									DA_2359 = TRUE; 																	} 															} 													} 						DA_2169(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DA_2569[0]); 						DA_2158(DA_2354 + 1, DA_2164(DA_2354 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DA_2571[0]); 						DA_2158(DA_2952, DA_2164(DA_2952), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(DA_2354 == 0 ){ 							if(DA_2360 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, DA_2573[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, DA_2574[0]); 															} 													} 						else{ 							if (DA_2579[DA_2354][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, DA_2574[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, DA_2573[0]); 															} 													} 											} 					if (DA_2353) { 						DA_2439               = DA_2152(DA_2439, 0); 						DA_2440               = DA_2152(DA_2440, 1); 						DA_2441             = DA_2152(DA_2441, 2); 						DA_2442           = DA_2152(DA_2442, 3); 						DA_2443             = DA_2152(DA_2443, 4); 						DA_2444             = DA_2152(DA_2444, 5); 						DA_2445              = DA_2152(DA_2445, 6); 						DA_2446           = DA_2152(DA_2446, 7); 						DA_2386          = DA_2152(DA_2386, 8); 						DA_2387   = DA_2152(DA_2387, 9); 						DA_2388 = DA_2152(DA_2388, 10); 						DA_2389      = DA_2152(DA_2389, 11); 						DA_2390    = DA_2152(DA_2390, 12); 						DA_2391    = DA_2152(DA_2391, 13); 						DA_2392    = DA_2152(DA_2392, 14); 						DA_2393      = DA_2152(DA_2393, 15); 						DA_2394      = DA_2152(DA_2394, 16); 						DA_2275              = DA_2152(DA_2275, 17); 						DA_2276           = DA_2152(DA_2276, 18); 						DA_2277            = DA_2152(DA_2277, 19); 						DA_2278            = DA_2152(DA_2278, 20); 						DA_2279= DA_2152(DA_2279, 21); 						DA_2407               = DA_2152(DA_2407, 22); 						DA_2408               = DA_2152(DA_2408, 23); 						DA_2409                   = DA_2152(DA_2409, 24); 						DA_2410                   = DA_2152(DA_2410, 25); 						DA_2411                   = DA_2152(DA_2411, 26); 						DA_2412   = DA_2152(DA_2412, 27); 						DA_2413   = DA_2152(DA_2413, 28); 						DA_2414 = DA_2152(DA_2414, 29); 						DA_2415   = DA_2152(DA_2415, 30); 						DA_2416   = DA_2152(DA_2416, 31); 						DA_2417 = DA_2152(DA_2417, 32); 						DA_2418   = DA_2152(DA_2418, 33); 						DA_2419   = DA_2152(DA_2419, 34); 						DA_2420 = DA_2152(DA_2420, 35); 						DA_2421   = DA_2152(DA_2421, 36); 						DA_2422   = DA_2152(DA_2422, 37); 						DA_2423 = DA_2152(DA_2423, 38); 						DA_2424   = DA_2155(DA_2424, 39); 						DA_2425         = DA_2155(DA_2425, 40); 						DA_2426   = DA_2152(DA_2426, 41); 						DA_2427     = DA_2152(DA_2427, 42); 						DA_2428                   = DA_2155(DA_2428, 43); 						DA_21252 = DA_2152(DA_21252, 54); 						DA_21245 = DA_2152(DA_21245, 55); 						DA_2429               = DA_2155(DA_2429, 44); 						DA_2430 = DA_2155(DA_2430, 45); 						DA_2431     = DA_2152(DA_2431, 46); 						DA_2432               = DA_2155(DA_2432, 47); 						DA_2433 = DA_2152(DA_2433, 48); 						DA_2434 = DA_2152(DA_2434, 49); 						DA_2435 = DA_2152(DA_2435, 50); 						DA_2436 = DA_2152(DA_2436, 51); 						DA_2437               = DA_2152(DA_2437, 52); 						DA_2438                 = DA_2152(DA_2438, 53); 						DA_2395       = DA_2155(DA_2395     ,56 ); 						DA_2396       = DA_2155(DA_2396     ,57 ); 						DA_2397      = DA_2152(DA_2397    ,58 ); 						DA_2398   = DA_2152(DA_2398 ,59 ); 						DA_2399       = DA_2155(DA_2399     ,60 ); 						DA_2400       = DA_2155(DA_2400     ,61 ); 						DA_2401   = DA_2152(DA_2401 ,62 ); 						DA_2402      = DA_2152(DA_2402    ,63 ); 						DA_2403          = DA_2155(DA_2403        ,64 ); 						DA_2404          = DA_2155(DA_2404        ,65 ); 						DA_2405         = DA_2152(DA_2405       ,66 ); 						DA_2455             = DA_2155(DA_2455           ,67 ); 						DA_229             = DA_2152(DA_229           ,68); 						DA_2476           = DA_2152(DA_2476         ,69); 						DA_2474         = DA_2152(DA_2474       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								DA_2355 = clamp(DA_2355 + 1, DA_2579[DA_2354][0], DA_2579[DA_2354][1]); 								DA_2359 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								DA_2355 = clamp(DA_2355 - 1, DA_2579[DA_2354][0], DA_2579[DA_2354][1]); 								DA_2359 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							DA_2352 = TRUE; 							DA_2353 = FALSE; 							DA_2359 = TRUE; 													} 						DA_2169(); 						DA_2954 = DA_2838[DA_2355][0]; 						DA_2955 = DA_2838[DA_2355][1]; 						if (DA_2838[DA_2355][4] == 0) { 							DA_2158(DA_2954, DA_2164(DA_2954), 4, 20, OLED_FONT_SMALL); 							DA_2158(DA_2955, DA_2164(DA_2955), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (DA_2359) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (DA_2353) { 							print(DA_2214(DA_2167(DA_2588[DA_2355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA_2588[DA_2355]); 													} 						else { 							print(DA_2214(DA_2167(DA_2589[DA_2354]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA_2589[DA_2354]); 													} 						DA_2359 = FALSE; 					} 									} 				if (!DA_2352 && !DA_2353) { 					if (DA_2358) { 						cls_oled(0); 						combo_run(DA_272); 						DA_2358 = FALSE; 						DA_2356 = TRUE; 						vm_tctrl(0); 					} 					if(DA_2360 == 0){ 						DA_2447      = PS4_CIRCLE; 						DA_2448      = PS4_CROSS ; 						DA_2449    = PS4_L1    ; 						DA_2450  = PS4_R1; 						DA_2451    = PS4_L2; 						DA_2452    = PS4_R2; 						DA_2453     = PS4_SQUARE; 						DA_2454  = PS4_TRIANGLE; 					} 					else if(DA_2360 == 1){ 						DA_2447      = PS4_SQUARE; 						DA_2448      = PS4_CROSS ; 						DA_2449    = PS4_L1    ; 						DA_2450  = PS4_R1; 						DA_2451    = PS4_L2; 						DA_2452    = PS4_R2; 						DA_2453     = PS4_CIRCLE; 						DA_2454  = PS4_TRIANGLE; 					} 					else if(DA_2360 == 2){ 						DA_2447      = DA_21394[DA_2439]; 						DA_2448      = DA_21394[DA_2440] ; 						DA_2449    = DA_21394[DA_2441]  ; 						DA_2450  = DA_21394[DA_2442]; 						DA_2451    = DA_21394[DA_2443]; 						DA_2452    = DA_21394[DA_2444]; 						DA_2453     = DA_21394[DA_2445]; 						DA_2454  = DA_21394[DA_2446]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA_21199) { 						set_val(DA_2448, 0); 						vm_tctrl(0); 						combo_run(DA_277); 											} 					if (DA_2718) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							DA_2593 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							DA_21052 = DA_21405[DA_2593][0]; 							DA_2669 = DA_21405[DA_2593][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							DA_2428 += 5; 							DA_2210(DA_2214(sizeof(DA_2595) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2595[0], DA_2428); 													} 						if (event_press(PS4_LEFT)) { 							DA_2428 -= 5; 							DA_2210(DA_2214(sizeof(DA_2595) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2595[0], DA_2428); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !DA_2615 ) { 						if (event_press(PS4_UP)) { 							DA_2600 += 25; 							DA_2210(DA_2214(sizeof(DA_2601) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2601[0], DA_2600); 													} 						if (event_press(PS4_DOWN)) { 							DA_2600 -= 25; 							DA_2210(DA_2214(sizeof(DA_2601) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2601[0], DA_2600); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (DA_2374) { 						DA_2248(); 											} 					if (DA_2375) { 						DA_2249(); 						DA_2250(); 											} 					if (!DA_2375) { 						if (get_ival(DA_2447)) { 							if (event_press(PS4_RIGHT)) { 								DA_2608 += 2; 								DA_2210(DA_2214(sizeof(DA_2609) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2609[0], DA_2608); 															} 							if (event_press(PS4_LEFT)) { 								DA_2608 -= 2; 								DA_2210(DA_2214(sizeof(DA_2609) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2609[0], DA_2608); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(DA_2449) ){ 							if(get_ival(DA_2447) && get_ptime(DA_2447) > DA_2608){ 								set_val(DA_2447,0); 															} 													} 											} 					if(DA_2378){ 						DA_2253(); 											} 					if (DA_2370) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								DA_2615 = !DA_2615; 								DA_2229(DA_2615); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (DA_2615 && DA_2370) { 						vm_tctrl(0); 						combo_stop(DA_285); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								DA_2424 += 10; 								DA_2210(DA_2214(sizeof(DA_2617) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2617[0], DA_2424); 															} 							if (event_press(PS4_DOWN)) { 								DA_2424 -= 10; 								DA_2210(DA_2214(sizeof(DA_2617) - 1, OLED_FONT_MEDIUM_WIDTH), DA_2617[0], DA_2424); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						DA_2222(DA_21096); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								DA_2622 = FALSE; 								vm_tctrl(0); 								combo_run(DA_278); 															} 							if (event_press(PS4_LEFT)) { 								DA_2622 = TRUE; 								vm_tctrl(0); 								combo_run(DA_278); 															} 							set_val(PS4_L1,0); 													} 											} 					if (DA_2371) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								DA_2624 = !DA_2624; 								DA_2229(DA_2624); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (DA_2624 && DA_2371) { 						vm_tctrl(0); 						DA_2222(DA_21098); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								DA_2625 = FALSE; 								vm_tctrl(0); 								combo_run(DA_279); 															} 							if (event_press(PS4_RIGHT)) { 								DA_2625 = TRUE; 								vm_tctrl(0); 								combo_run(DA_279); 															} 													} 											} 					if(DA_2363 || DA_2364 ){ 						DA_2118(); 											} 					if (DA_2361) { 						if (DA_2361 == DA_21102) DA_2628 = TRUE; 						if (DA_2361 == DA_21103) { 							if (event_press(DA_21404[-1 +DA_2394]) && get_brtime(DA_21404[-1 +DA_2394]) <= 200) { 								DA_2628 = !DA_2628; 								DA_2229(DA_2628); 															} 							set_val(DA_21404[-1 +DA_2394], 0); 													} 						if (DA_2361 > 0 && DA_2361 < 3 && DA_2628 == 1) { 							DA_2226(); 													} 						else if (DA_2361 == 3) { 							if (get_ival(DA_21404[-1 +DA_2394])) { 								DA_2226(); 															} 							set_val(DA_21404[-1 +DA_2394], 0); 													} 											} 									if (DA_2362) { 						if (DA_2362 == DA_21102) DA_2631 = TRUE; 						if (DA_2362 == DA_21103) { 							if (event_press(DA_21404[-1 +DA_2279]) && get_brtime(DA_21404[-1 +DA_2279]) <= 200) { 								DA_2631 = !DA_2631; 								DA_2229(DA_2631); 															} 							set_val(DA_21404[-1 +DA_2279], 0); 													} 						if (DA_2362 > 0 && DA_2362 < 3 && DA_2631 == 1) { 							DA_2224(); 													} 						else if (DA_2362 == 3) { 							if (get_ival(DA_21404[-1 +DA_2279])) { 								DA_2224(); 															} 							set_val(DA_21404[-1 +DA_2279], 0); 													} 											} 					if (DA_2365) { 						if (DA_2365 == 1) { 							DA_2634 = PS4_R3; 							DA_2631 = FALSE; 													} 						if (DA_2365 == 2) { 							DA_2634 = PS4_L3; 							DA_2631 = FALSE; 													} 						if (DA_2365 == 3) { 							DA_2634 = XB1_PR1; 							DA_2631 = FALSE; 													} 						if (DA_2365 == 4) { 							DA_2634 = XB1_PR2; 							DA_2631 = FALSE; 													} 						if (DA_2365 == 5) { 							DA_2634 = XB1_PL1; 							DA_2631 = FALSE; 													} 						if (DA_2365 == 6) { 							DA_2634 = XB1_PL2; 							DA_2631 = FALSE; 													} 						if(get_ival(DA_2634)){ 							if(DA_2407 || DA_2408){ 								if( DA_2407 && event_press(PS4_L1)){ 									DA_2479 = FALSE; 									DA_21051 = DA_2407  ; 									DA_2227( DA_2407   ); 								} 								if( DA_2408 && event_press(PS4_R1)){ 									DA_2479 = TRUE; 									DA_21051 =  DA_2408 ; 									DA_2227( DA_2408   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( DA_2409 ){ 								if(event_press(PS4_SQUARE)){ 									DA_2479 = FALSE; 									DA_21051 =  DA_2409  ; 													combo_stop(DA_288); 				combo_stop(DA_297); 				combo_stop(DA_289); 				combo_stop(DA_298); 				combo_stop(DA_295); 				combo_stop(DA_296); 				combo_stop(DA_292); 				combo_stop(DA_294); 				combo_stop(DA_291); 				combo_stop(DA_287); 				combo_stop(DA_285); 				combo_stop(DA_290); 				combo_stop(DA_2107); 				combo_stop(DA_2109); 				combo_stop(DA_2100); 				combo_stop(DA_2108); 				combo_stop(DA_299); 									DA_2227( DA_2409   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									DA_2479 = TRUE; 									DA_21051 =  DA_2409  ; 									DA_2227( DA_2409   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( DA_2410 ){ 								if(event_press(PS4_CROSS)){ 									DA_2479 = FALSE; 									DA_21051 =  DA_2410  ; 									DA_2227( DA_2410   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(DA_288); 				combo_stop(DA_297); 				combo_stop(DA_289); 				combo_stop(DA_298); 				combo_stop(DA_295); 				combo_stop(DA_296); 				combo_stop(DA_292); 				combo_stop(DA_294); 				combo_stop(DA_291); 				combo_stop(DA_287); 				combo_stop(DA_285); 				combo_stop(DA_290); 				combo_stop(DA_2107); 				combo_stop(DA_2109); 				combo_stop(DA_2100); 				combo_stop(DA_2108); 				combo_stop(DA_299); 									DA_2479 = TRUE; 									DA_21051 =  DA_2410  ; 									DA_2227( DA_2410   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( DA_2411 ){ 								if(event_press(PS4_R3)){ 									DA_2479 = FALSE; 									DA_21051 =  DA_2411  ; 									DA_2227( DA_2411   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(DA_2634,0); 											} 					if (DA_2366) { 						if (DA_2413 == 1) { 							if (event_press(DA_21404[-1 + DA_2412]) && !DA_21149) { 								vm_tctrl(0); 								combo_run(DA_282); 															} 							else if (event_press(DA_21404[-1 + DA_2412]) && DA_21149) { 								set_val(DA_21404[-1 + DA_2412], 0); 								DA_2479 = !DA_2414; 								DA_21051 = DA_2366; 								DA_2227(DA_2366); 															} 													} 						else { 							if (event_press(DA_21404[-1 + DA_2412])) { 								DA_2479 = !DA_2414; 								set_val(DA_21404[-1 + DA_2412], 0); 								DA_21051 = DA_2366; 								DA_2227(DA_2366); 															} 													} 					} 					if (DA_2368) { 						if (DA_2419 == 1) { 							if (event_press(DA_21404[-1 +DA_2418]) && !DA_21149) { 								vm_tctrl(0); 								combo_run(DA_282); 															} 							else if (event_press(DA_21404[-1 +DA_2418]) && DA_21149) { 								set_val(DA_21404[-1 +DA_2418], 0); 								DA_2479 = !DA_2420; 								DA_21051 = DA_2368; 								DA_2227(DA_2368); 															} 													} 						else { 							if (event_press(DA_21404[-1 +DA_2418])) { 								DA_2479 = !DA_2420; 								set_val(DA_21404[-1 +DA_2418], 0); 								DA_21051 = DA_2368; 								DA_2227(DA_2368); 															} 													} 					} 					if (DA_2367) { 						if (DA_2416 == 1) { 							if (event_press(DA_21404[-1 +DA_2415]) && !DA_21149) { 								vm_tctrl(0); 								combo_run(DA_282); 															} 							else if (event_press(DA_21404[-1 +DA_2415]) && DA_21149) { 								set_val(DA_21404[-1 +DA_2415], 0); 								DA_2479 = !DA_2417; 								DA_21051 = DA_2367; 								DA_2227(DA_2367); 															} 													} 						else { 							if (event_press(DA_21404[-1 +DA_2415])) { 								DA_2479 = !DA_2417; 								set_val(DA_21404[-1 +DA_2415], 0); 								DA_21051 = DA_2367; 								DA_2227(DA_2367); 															} 													} 					} 					if (DA_2369) { 						if (DA_2422 == 1) { 							if (event_press(DA_21404[-1 +DA_2421]) && !DA_21149) { 								vm_tctrl(0); 								combo_run(DA_282); 															} 							else if (event_press(DA_21404[-1 +DA_2421]) && DA_21149) { 								set_val(DA_21404[-1 +DA_2421], 0); 								DA_2479 = !DA_2423; 								DA_21051 = DA_2369; 								DA_2227(DA_2369); 															} 													} 						else { 							if (event_press(DA_21404[-1 +DA_2421])) { 								DA_2479 = !DA_2423; 								set_val(DA_21404[-1 +DA_2421], 0); 								DA_21051 = DA_2369; 								DA_2227(DA_2369); 															} 													} 					} 					if (DA_21051 == DA_2310 && combo_running(DA_230)) set_val(DA_2449, 100); 					if(DA_2383){ 						if(!block){ 							if(!get_val(DA_2451)){ 								if( !get_val(DA_2452)){ 									if(get_val(DA_2448)){ 										DA_2650 += get_rtime(); 																			} 									if(DA_2402){ 										if(get_ival(DA_2448) && get_ptime(DA_2448) > DA_2400){ 											set_val(DA_2448,0); 																					} 																			} 									if(event_release(DA_2448)){ 										if( DA_2650 < DA_2399 ){ 											DA_2651 = DA_2399 - DA_2650; 											combo_run(DA_2107); 																					} 										else{ 											if(DA_2401) combo_run(DA_2108); 																					} 										DA_2650 = 0; 																			} 																	} 							} 						} 											} 					if(DA_2382){ 						if(!block){ 							if(!get_ival(DA_2451)){ 								if( !get_ival(DA_2452)){ 									if(get_ival(DA_2454)){ 										DA_2652 += get_rtime(); 																			} 									if(event_release(DA_2454)){ 										if(DA_2652 < DA_2395){ 											DA_2653 = DA_2395 - DA_2652; 											combo_run(DA_2109); 																					} 										else{ 											if(DA_2398) combo_run(DA_299); 																					} 										DA_2652 = 0; 																			} 																	} 							} 						} 											} 					if(DA_2384){ 						if(!block){ 							if(get_ival(DA_2453)){ 								DA_2654 += get_rtime(); 															} 							if(DA_2405){ 								if(get_ival(DA_2453) && get_ptime(DA_2453) > DA_2404){ 									set_val(DA_2453,0); 																	} 															} 							if(event_release(DA_2453)){ 								if(DA_2654 && (DA_2654 < DA_2403)){ 									DA_2655 = DA_2403 - DA_2654; 									combo_run(DA_2100); 																	} 								DA_2654 = 0; 															} 													} 											} 					if (DA_2372) { 						if (event_press(DA_21404[-1 +DA_2426])) { 							vm_tctrl(0); 							combo_run(DA_277); 													} 						set_val(DA_21404[-1 +DA_2426], 0); 											} 					if(!DA_2376){ 						DA_2429 = 0 ; 						DA_2430 = 0; 						DA_2431 = FALSE; 						DA_2432 = 0; 											} 					if (DA_2377) { 						DA_2249(); 						if (DA_2433 == 0) { 							DA_2658 = FALSE; 							DA_2457 = 0; 													} 						else { 							DA_2658 = TRUE; 							DA_2457 = 40; 													} 						if (DA_2434 == 0) { 							DA_2660 = FALSE; 							DA_2456 = 0; 													} 						else { 							DA_2660 = TRUE; 							DA_2456 = 85; 													} 						if (DA_2435 == 0) { 							DA_2662 = FALSE; 							DA_2458 = 0; 													} 						else { 							DA_2662 = TRUE; 							DA_2458 = -15; 													} 						if (DA_2436 == 0) { 							DA_2664 = FALSE; 													} 						else { 							DA_2664 = TRUE; 													} 						if(DA_2435 == 6 || DA_2434 == 6 || DA_2433 == 6){ 							if (get_ival(DA_21404[-1 + DA_2435]) || get_ival(DA_21404[-1 + DA_2434]) || get_ival(DA_21404[-1 + DA_2433])){ 								combo_run(DA_20); 															} 													} 						if (DA_2662) { 							if (get_val(DA_21404[-1 + DA_2435])) { 								set_val(DA_21404[-1 + DA_2435], 0); 								combo_run(DA_297); 								DA_21208 = 9000; 															} 													} 						if (DA_2664) { 							if (get_val(DA_21404[-1 + DA_2436])) { 								set_val(DA_21404[-1 + DA_2436], 0); 								combo_run(DA_298); 								DA_21208 = 9000; 							} 							if (combo_running(DA_298)) { 								if (get_ival(DA_2448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA_2452) > 30) { 									combo_stop(DA_298); 																	} 															} 													} 						if (DA_2660) { 							if (get_val(DA_21404[-1 + DA_2434])) { 								set_val(DA_21404[-1 + DA_2434], 0); 								DA_2255(); 								DA_21208 = 9000; 															} 													} 						if (DA_2658) { 							if (get_val(DA_21404[-1 + DA_2433])) { 								set_val(DA_21404[-1 + DA_2433], 0); 								combo_run(DA_295); 								DA_21208 = 9000; 															} 													} 											} 					else{ 						DA_2457 = 0; 						DA_2458 = 0; 						DA_2456 = 0; 											} 					if (DA_2379) { 						DA_2256(); 											} 									} 							} 								if (combo_running(DA_2111) && (  get_ival(DA_2448) ||   get_ival(DA_2454) ||         get_ival(DA_2451) ||        get_ival(DA_2447) ||        get_ival(DA_2450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA_2111); 			} 					} 		else { 			if (!get_ival(DA_2452)) DA_2222(DA_21095); 					} 			} 			DA_2268(); 	} combo DA_21 { 	set_val(DA_2451, 100); 	set_val(DA_2449,100); 	DA_2243(); 	wait(400); 	set_val(DA_2448,100); 	wait(90); 	wait( 400); 	} combo DA_22 { 	set_val(DA_2451, 100); 	set_val(DA_2449,100); 	DA_2243(); 	wait(400); 	set_val(DA_2447,100); 	wait(220); 	wait( 400); 	} combo DA_23 { 	call(DA_228); 	wait( 100); 	call(DA_298); 	DA_2239(DA_21052, DA_2669); 	wait( 800); 	wait( 350); 	set_val(DA_2450,100); 	set_val(DA_2449,100); 	wait( 400); 	} combo DA_24 { 	DA_2245(); 	wait(45); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2236(DA_21153,DA_2671); 	wait(45); 	DA_2243(); 	wait(45); 	vm_tctrl(0); 	wait(350); 	} combo DA_25 { 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_26 { 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	DA_2239(DA_21153, DA_2671); 	wait( DA_21055 + random(1,5)); 	DA_2239(DA_21153, DA_2671); 	wait( 1000); 	wait( 350); 	} combo DA_27 { 	DA_2246(); 	DA_2479 = FALSE; 	wait(DA_21055 + random(1,5)); 	DA_2243(); 	wait(DA_21055 + random(1,5)); 	DA_2246(); 	wait(DA_21055 + random(1,5)); 	DA_2479 = TRUE; 	DA_2243(); 	wait(DA_21055 + random(1,5)); 	wait(350); 	} combo DA_28 { 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2479 = TRUE; 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2479 = FALSE; 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_29 { 	DA_2479 = TRUE; 	DA_2243(); 	wait(DA_21055 + random(1,5)); 	DA_2246(); 	wait(DA_21055 + random(1,5)); 	DA_2479 = FALSE; 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_210 { 	DA_2479 = FALSE; 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2479 = TRUE; 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_211 { 	DA_2239(0,0); 	set_val(DA_2449,100); 	set_val(DA_2450,100); 	DA_2245(); 	wait( 60); 	wait( 60); 	} combo DA_212 { 	set_val(DA_21118, inv(DA_21052)); 	set_val(DA_21119, inv(DA_2669)); 	set_val(DA_2450, 100); 	set_val(DA_2449, 100); 	wait( 60); 	set_val(DA_21118, inv(DA_21052)); 	set_val(DA_21119, inv(DA_2669)); 	set_val(DA_2450, 100); 	set_val(DA_2449, 100); 	wait( 500); 	wait( 350); 	} combo DA_213 { 	DA_2239(0, 0); 	set_val(DA_2451, 100); 	wait( 60); 	DA_2239(0, 0); 	set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	wait( 60); 	DA_2239(0, 0); 	set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 80); 	DA_2239(0, 0); 	set_val(DA_2451, 100); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	wait( 350); 	} combo DA_214 { 	set_val(DA_2447, 100); 	wait( 60); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 80); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	wait( 350); 	} combo DA_215 { 	set_val(DA_2449, 100); 	DA_2243(); 	wait( 500); 	wait( 350); 	} combo DA_216 { 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	if(DA_2479) DA_2687 = DA_2593 + 3; 	else  DA_2687 = DA_2593 - 3; 	DA_2234(DA_2687); 	DA_2236(DA_21153,DA_2671); 	wait(DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	if(DA_2479) DA_2687 = DA_2593 + 1; 	else  DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2236(DA_21153,DA_2671); 	wait(DA_21055 + random(1,5)); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_217 { 	set_val(DA_2449,100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	set_val(DA_2449,100); 	wait( DA_21055 + random(1,5)); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_218 { 	set_val(DA_2451,100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451,100); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451,100); 	DA_2243(); 	set_val(DA_2451,100); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	set_val(DA_2451,100); 	set_val(DA_2452,100); 	wait(50); 	wait(350); 	} combo DA_219 { 	set_val(DA_2451,100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451,100); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451,100); 	DA_2243(); 	set_val(DA_2451,100); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_220 { 	DA_2244(); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	DA_2243()    	wait( DA_21055 + random(1,5)); 	DA_2479 = !DA_2479; 	DA_2242(); 	wait( 1000); 	wait( 350); 	} combo DA_221 { 	set_val(DA_2449,100); 	DA_2246(); 	wait(50); 	DA_2239(0,0); 	set_val(DA_2449,100); 	wait(50); 	set_val(DA_2449,100); 	DA_2246(); 	wait(50); 	wait( 350); 	} combo DA_222 { 	DA_2239(0, 0); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_223 { 	DA_2246(); 	wait(DA_21055 + random(1,5)); 	DA_2247()wait(DA_21055 + random(1,5)); 	DA_2246(); 	wait(DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_224 { 	set_val(DA_2450, 100); 	set_val(DA_2449, 100); 	wait( 20); 	set_val(DA_2450, 100); 	set_val(DA_2449, 100); 	if (DA_2479) DA_2687 = DA_2593 + 4; 	else { 		DA_2687 = DA_2593 - 4; 			} 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	set_val(DA_2452, 100); 	wait( 100); 	wait( 350); 	} combo DA_225 { 	set_val(DA_2451, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451, 100); 	wait( 30); 	set_val(DA_2451, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_226 { 	set_val(DA_2451, 100); 	DA_2245(); 	wait( 70); 	set_val(DA_2451, 100); 	DA_2247(); 	wait( 60); 	set_val(DA_2451, 100); 	DA_2246(); 	wait( 60); 	wait( 350); 	} combo DA_227 { 	set_val(DA_2451, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451, 100); 	wait( 30); 	set_val(DA_2451, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2239(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} combo DA_228 { 	DA_2243(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo DA_229 { 	DA_2243(); 	set_val(DA_2452, 0); 	wait(350); 	wait( 350); 	} combo DA_230 { 	if (DA_21051 == DA_2312) DA_21056 = 200; 	else DA_21056 = 1; 	wait( DA_21056); 	DA_2245(); 	wait( 70); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( 70); 	wait( 350); 	} combo DA_231 { 	set_val(DA_2449, 100)DA_2245(); 	DA_2239(DA_21052,DA_2669); 	wait( 50); 	set_val(DA_2449, 100)DA_2247(); 	DA_2239(DA_21052,DA_2669); 	wait( 50); 	set_val(DA_2449, 100)DA_2243(); 	DA_2239(DA_21052,DA_2669); 	wait( 50); 	DA_2239(DA_21052,DA_2669); 	wait(465); 	DA_2239(DA_21052,DA_2669); 	set_val(DA_2451, 100); 	set_val(DA_2452, 100); 	wait(50); 	if (DA_2479) DA_2687 = DA_2593 - 1; 	else DA_2687 = DA_2593 + 1; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 50); 	if (DA_2479) DA_2687 = DA_2593 + 4; 	else DA_2687 = DA_2593 - 4; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 700); 	wait( 350); 	} combo DA_232 { 	if (DA_21051 == DA_2312) DA_21056 = 200; 	else DA_21056 = 1; 	set_val(DA_2451,100); 	wait( DA_21056); 	DA_2245(); 	set_val(DA_2451,100); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	set_val(DA_2451,100); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	set_val(DA_2451,100); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_233 { 	if (DA_2479) DA_2687 = DA_2593 - 2; 	else DA_2687 = DA_2593 + 2; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	wait( 280); 	DA_2247(); 	wait( 50); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 45); 	set_val(DA_2447, 100); 	DA_2239(DA_21153, DA_2671); 	wait( 45); 	DA_2239(DA_21153, DA_2671); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 45); 	DA_2239(DA_21153, DA_2671); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 45); 	DA_2239(DA_21153, DA_2671); 	wait( 100); 	DA_2239(DA_21153, DA_2671); 	wait( 500); 	wait( 350); 	} combo DA_234 { 	DA_2243(); 	wait( 280); 	DA_2242()  set_val(DA_2447, 100); 	set_val(DA_2451, 100); 	wait( 60); 	DA_2242()  set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 60); 	DA_2242()  set_val(DA_2451, 100); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	wait( 250); 	DA_2242()   	wait( 300); 	wait( 350); 	} combo DA_235 { 	DA_2243(); 	wait( 300); 	DA_2245(); 	wait( 60); 	wait( 350); 	} combo DA_236 { 	set_val(DA_2449, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2449, 100); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2449, 100); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_237 { 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_238 { 	set_val(DA_2449, 100); 	DA_2244(); 	wait( 60); 	set_val(DA_2449, 100); 	DA_2247(); 	wait( 60); 	set_val(DA_2449, 100); 	DA_2243(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo DA_239 { 	DA_2246(); 	set_val(DA_2449,100); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	set_val(DA_2449,100); 	wait( 70); 	DA_2243(); 	set_val(DA_2449,100); 	wait( 70); 	wait( 350); 	} combo DA_240 { 	if (DA_2479) DA_2687 = DA_2593 + 3; 	else DA_2687 = DA_2593 - 3; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	set_val(DA_2447, 100); 	set_val(DA_2451,100); 	wait( 60); 	set_val(DA_2451,100); 	DA_2239(DA_21153, DA_2671); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 80); 	set_val(DA_2451,100); 	DA_2239(DA_21153, DA_2671); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	set_val(DA_2451,100); 	DA_2239(DA_21153, DA_2671); 	wait( 300); 	wait( 350); 	} combo DA_241 { 	set_val(DA_2449, 100); 	DA_2245(); 	DA_2239(0, 0); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2449, 100); 	DA_2247(); 	DA_2239(0, 0); 	wait( 65); 	set_val(DA_2449, 100); 	DA_2239(0, 0); 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	set_val(DA_2452,0); 	DA_2239(DA_21153, DA_2671); 	wait( 200); 	set_val(DA_2452,0); 	wait( 350); 	} combo DA_242 { 	if (DA_21051 == DA_2312) DA_21056 = 200; 	else DA_21056 = 1; 	wait( DA_21056); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	wait( 350); 	} combo DA_243 { 	DA_2246(); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} combo DA_244 { 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	if (DA_21051 == DA_2325) DA_2242(); 	set_val(DA_2451, 100); 	set_val(DA_2452, 100); 	wait( 200); 	if (DA_21051 == DA_2325) DA_2242(); 	wait( 300); 	wait( 350); 	} combo DA_245 { 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2247(); 	wait( DA_21055 + random(1,5)); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	if (DA_21051 == DA_2325) DA_2242(); 	set_val(DA_2451, 100); 	set_val(DA_2452, 100); 	wait( 200); 	if (DA_21051 == DA_2325) DA_2242(); 	wait( 300); 	wait( 350); 	} combo DA_246 { 	call(DA_233)call(DA_235); 	} combo DA_247 {    DA_2718 = FALSE; 	DA_2239(DA_21052, DA_2669); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2239(DA_21052, DA_2669); 	DA_2247(); 	DA_2718 = FALSE; 	wait( DA_21055 + random(1,5)); 	DA_2239(DA_21052, DA_2669); 	DA_2243(); 	DA_2718 = FALSE; 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451, 100); 	set_val(DA_2452, 100); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	DA_2718 = FALSE; 	wait( 400); 	wait( 350); 	DA_2718 = TRUE; 	} combo DA_248 { 	DA_2239(DA_21052, DA_2669); 	set_val(XB1_LS, 100); 	DA_2245(); 	wait( DA_21055 + random(1,5)); 	DA_2239(DA_21052, DA_2669); 	DA_2247(); 	set_val(XB1_LS, 100); 	wait( DA_21055 + random(1,5)); 	DA_2239(DA_21052, DA_2669); 	DA_2243(); 	wait( DA_21055 + random(1,5)); 	set_val(DA_2451, 100); 	set_val(DA_2452, 100); 	if (DA_2479) DA_2687 = DA_2593 + 4; 	else DA_2687 = DA_2593 - 4; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 220); 	if (DA_2479) DA_2687 = DA_2593 + 4; 	else DA_2687 = DA_2593 - 4; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 60); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	wait( 600); 	wait( 350); 	} combo DA_249 { 	set_val(DA_2448, 0); 	set_val(DA_2447, 100); 	wait( 80); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 80); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo DA_250 { 	set_val(DA_2447, 100); 	set_val(DA_2452,100); 	wait( 60); 	set_val(DA_2452,100); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	set_val(DA_2452,100); 	wait( 60); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	set_val(DA_2452,100); 	wait( 60); 	wait( 350); 	} combo DA_251 { 	set_val(DA_2449,100); 	set_val(DA_2450,100); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	wait( 200); 	set_val(DA_2449,100); 	set_val(DA_2450,100); 	DA_2479 = FALSE; 	DA_2242(); 	wait( 50); 	set_val(DA_2449,100); 	set_val(DA_2450,100); 	DA_2479 = !DA_2479; 	DA_2242(); 	set_val(DA_2449,100); 	set_val(DA_2450,100); 	wait( 540); 	wait( 350); 	} combo DA_252 { 	set_val(DA_2447, 100); 	wait( 60); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 60); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} combo DA_253 { 	DA_2239(inv(DA_21052), inv(DA_2669)); 	set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	wait( 60); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 60); 	DA_2239(inv(DA_21052), inv(DA_2669)); 	set_val(DA_2451, 100); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	DA_2239(0, 0); 	wait( 300); 	wait( 350); 	} combo DA_254 { 	set_val(DA_2449, 100); 	set_val(DA_2453, 100); 	wait( 60); 	set_val(DA_2449, 100); 	set_val(DA_2453, 100); 	set_val(DA_2448, 100); 	wait( 60); 	set_val(DA_2449, 100); 	set_val(DA_2453, 0); 	set_val(DA_2448, 100); 	DA_2242(); 	wait( 60); 	set_val(DA_2449, 100); 	DA_2242(); 	wait( 300); 	wait( 350); 	} combo DA_255 { 	set_val(DA_2447, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo DA_256 { 	set_val(DA_2447, 100); 	set_val(DA_2451,100); 	wait( 60); 	set_val(DA_2451,100); 	set_val(DA_2447, 100); 	set_val(DA_2448, 100); 	wait( 60); 	set_val(DA_2451,100); 	set_val(DA_2447, 0); 	set_val(DA_2448, 100); 	wait( 60); 	wait( 350); 	} combo DA_257 { 	set_val(DA_2449, 100); 	DA_2245(); 	wait( 300); 	wait( 350); 	} combo DA_258 { 	DA_2246(); 	wait( 70); 	DA_2247(); 	wait( 70); 	DA_2245(); 	wait( 70); 	wait( 350); 	} combo DA_259 { 	set_val(DA_2449,100); 	DA_2246(); 	wait( 70); 	set_val(DA_2449,100); 	DA_2247(); 	wait( 70); 	DA_2245(); 	set_val(DA_2449,100); 	wait(50); 	wait( 350); 	} combo DA_260 { 	DA_2239(DA_21052, DA_2669); 	DA_2246(); 	wait( 100); 	DA_2247(); 	DA_2239(DA_21052, DA_2669); 	wait( 60); 	DA_2245(); 	DA_2239(DA_21052, DA_2669); 	wait( 320); 	DA_2239(DA_21052, DA_2669); 	DA_2247(); 	wait( 220); 	DA_2239(DA_21052, DA_2669); 	DA_2245(); 	DA_2239(DA_21052, DA_2669); 	wait( 100); 	wait( 350); 	} combo DA_261 { 	call(DA_283); 	DA_2239(0, 0); 	call(DA_284); 	call(DA_284); 	call(DA_284); 	call(DA_284); 	call(DA_284); 	set_val(DA_2451, 100); 	DA_2246(); 	wait( 70); 	set_val(DA_2451, 100); 	DA_2247(); 	wait( 60); 	set_val(DA_2451, 100); 	DA_2245(); 	wait( 60); 	set_val(DA_2451, 100); 	wait( 600); 	wait( 350); 	} combo DA_262 { 	set_val(DA_2451,100); 	set_val(DA_2450,100); 	if (DA_2479) DA_2687 = DA_2593 - 2; 	else DA_2687 = DA_2593 + 2; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	wait(50); 	set_val(DA_2450,100); 	set_val(DA_2451,100); 	if (DA_2479) DA_2687 = DA_2593 - 3; 	else DA_2687 = DA_2593 + 3; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	wait(50); 	set_val(DA_2450,100); 	set_val(DA_2451,100); 	if (DA_2479) DA_2687 = DA_2593 - 4; 	else DA_2687 = DA_2593 + 4; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	wait(50); 	set_val(DA_2450,100); 	set_val(DA_2451,100); 	if (DA_2479) DA_2687 = DA_2593 - 5; 	else DA_2687 = DA_2593 + 5; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	set_val(DA_2451,100); 	set_val(DA_2450,100); 	wait(50); 	set_val(DA_2450,100); 	set_val(DA_2451,100); 	if (DA_2479) DA_2687 = DA_2593 - 6; 	else DA_2687 = DA_2593 + 6; 	DA_2234(DA_2687); 	DA_2236(DA_21153, DA_2671); 	wait(50); 	} combo DA_263 { 	wait( 100); 	DA_2239(0, 0); 	DA_2245(); 	wait( 70); 	DA_2239(0, 0); 	DA_2247()   	wait( 70); 	DA_2239(0, 0); 	DA_2245()   	wait( 70); 	DA_2239(0, 0); 	DA_2247()   	wait( 70); 	DA_2239(0, 0); 	DA_2246(); 	wait( 70); 	DA_2239(0, 0); 	wait( 350); 	} combo DA_264 { 	set_val(PS4_R3,100); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	DA_2239(DA_21153, DA_2671); 	wait( 70); 	DA_2239(DA_21153, DA_2671); 	wait( 400); 	wait( 350); 	} combo DA_265 { 	call(DA_283); 	DA_2239(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (DA_2479) DA_2687 = DA_2593 + 1; 	else DA_2687 = DA_2593 - 1; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	DA_2239(DA_21153, DA_2671); 	wait( 70); 	DA_2239(DA_21153, DA_2671); 	wait( 400); 	wait( 350); 	} combo DA_266 { 	call(DA_283); 	DA_2239(0,0); 	set_val(DA_2451,100); 	set_val(DA_2452,100); 	wait( 750); 	} combo DA_267 { 	set_val(PS4_R3,100); 	if (DA_2479) DA_2687 = DA_2593 + 2; 	else DA_2687 = DA_2593 - 2; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	DA_2239(DA_21153, DA_2671); 	wait( 70); 	DA_2239(DA_21153, DA_2671); 	wait( 400); 	wait( 350); 	} combo DA_268 { 	set_val(DA_2451,100); 	set_val(PS4_R3,100); 	if (DA_2479) DA_2687 = DA_2593 ; 	else DA_2687 = DA_2593; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	DA_2239(DA_21153, DA_2671); 	wait( 70); 	set_val(DA_2451,100); 	DA_2239(DA_21153, DA_2671); 	wait( 400); 	wait( 350); 	} combo DA_269 { 	set_val(DA_2451,100); 	set_val(PS4_R3,100); 	if (DA_2479) DA_2687 = DA_2593 ; 	else DA_2687 = DA_2593; 	DA_2234(DA_2687); 	DA_2239(DA_21153, DA_2671); 	DA_2239(DA_21153, DA_2671); 	wait( 70); 	set_val(DA_2451,100); 	DA_2239(DA_21153, DA_2671); 	wait(800); 	call(DA_298); 	} combo DA_270 { 	DA_2239(0,0); 	set_val(DA_2450,100); 	set_val(DA_2449,100); 	DA_2243(); 	wait( 350); 	wait( 350); 	set_val(DA_2450,100); 	set_val(DA_2449,100); 	wait( 400); 	} int DA_2141 ; int DA_2763 ; int DA_2764 ; int DA_2765; int DA_2766; function DA_2127(DA_2128){ 	DA_2763 = 2; 	DA_2764 = 987654; 	DA_2141 = 54321; 	DA_2765 = (DA_2128 >> DA_2763) | (DA_2128 << (32 - DA_2763)); 	DA_2766 = (((DA_2765 >> ((DA_2765 & 0xF) % 13)) & 0x7FFFF) + DA_2141) % DA_2764 + 123456; 	return DA_2766; 	} define DA_2768 = -1; define DA_2536 = -2; define DA_2770 = -3; define DA_2771 = 0; define DA_2537 = 1; function DA_2129(DA_2128, DA_2131, DA_2132) { 	if(DA_2128 > DA_2132) return DA_2131; 	if(DA_2128 < DA_2131) return DA_2132; 	return DA_2128; 	} int DA_2775,DA_2776; function DA_2133(DA_2134,DA_2135,DA_2136,DA_2137,DA_2138,DA_2139){ 	if(!DA_2139){ 		print(DA_2142(DA_2140(DA_2134),DA_2137,DA_2135),DA_2136,DA_2137,DA_2138,DA_2134)     	} 	else{ 		if(DA_2134 < 0){ 			putc_oled(1,45); 					} 		if(DA_2134){ 			for(DA_2775 = DA_2146(DA_2134) + DA_2776 = (DA_2134 < 0 ),DA_2134 = abs(DA_2134); 			DA_2134 > 0; 			DA_2775-- , DA_2776++){ 				putc_oled(DA_2775,DA_2134%10 + 48); 				DA_2134 = DA_2134/10; 							} 					} 		else{ 			putc_oled(1,48); 			DA_2776 = 1         		} 		puts_oled(DA_2142(DA_2776,DA_2137,DA_2135),DA_2136,DA_2137,DA_2776 ,DA_2138); 			} 	} int DA_2797; function DA_2140(DA_2141) { 	DA_2797 = 0; 	do { 		DA_2141++; 		DA_2797++; 			} 	while (duint8(DA_2141)); 	return DA_2797; 	} function DA_2142(DA_2143,DA_2137,DA_2135) { 	if(DA_2135 == -3){ 		return 128 - ((DA_2143 * (7 + (DA_2137 > 1) + DA_2137 * 4)) + 3 ); 			} 	if(DA_2135 == -2){ 		return 64 - ((DA_2143 * (7 + (DA_2137 > 1) + DA_2137 * 4)) / 2); 			} 	if(DA_2135 == -1){ 		return 3 	} 	return DA_2135; 	} function DA_2146(DA_2147) { 	for(DA_2775 = 1; 	DA_2775 < 11; 	DA_2775++){ 		if(!(abs(DA_2147) / pow(10,DA_2775))){ 			return DA_2775; 			break; 					} 			} 	return 1; 	} function DA_2148() { 	if (get_ival(DA_2447)) { 		set_val(DA_2447, 0); 		if (get_ival(DA_2449)) DA_2804 = 50; 		if (!get_ival(DA_2449)) DA_2804 = 410; 		combo_run(DA_271); 			} 	if (DA_2803 > 0) set_polar(POLAR_LS, DA_2803 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) DA_2803 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) DA_2803 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) DA_2803 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) DA_2803 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA_2803 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA_2803 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA_2803 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA_2803 = 270; } int DA_2804; int DA_2557; int DA_2803; combo DA_271 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( DA_2804); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	DA_2557 = !DA_2557; } define DA_2807 = 19; function DA_2149(DA_2150, DA_2151) { 	if (DA_2354 == DA_2151) { 		if (event_press(PS4_RIGHT)) { 			DA_2150 = clamp(DA_2150 + 1, 0, DA_2810[DA_2354]); 			DA_2359 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			DA_2150 = clamp(DA_2150 - 1, 0, DA_2810[DA_2354]); 			DA_2359 = TRUE; 					} 		if (DA_2354 == 0) { 			print(DA_2214(DA_2167(DA_2814[DA_2360]) ,OLED_FONT_SMALL_WIDTH),DA_2807  ,OLED_FONT_SMALL , OLED_WHITE ,DA_2814[DA_2360]); 					} 		else if (DA_2354 == 1) { 			print(DA_2214(DA_2167(DA_2816[DA_2361]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2816[DA_2361]); 					} 		else if (DA_2354 == 2) { 			print(DA_2214(DA_2167(DA_2816[DA_2362]) ,OLED_FONT_SMALL_WIDTH ),DA_2807  ,OLED_FONT_SMALL , OLED_WHITE ,DA_2816[DA_2362]); 					} 		else if (DA_2354 == 5) { 			print(DA_2214(DA_2167(DA_2820[DA_2365]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2820[DA_2365]); 					} 		else if (DA_2354 == 6) { 			print(DA_2214(DA_2167(DA_2822[DA_2366]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2366]); 					} 		else if (DA_2354 == 7) { 			print(DA_2214(DA_2167(DA_2822[DA_2367]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2367]); 					} 		else if (DA_2354 == 8) { 			print(DA_2214(DA_2167(DA_2822[DA_2368]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2368]); 					} 		else if (DA_2354 == 9) { 			print(DA_2214(DA_2167(DA_2822[DA_2369]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2369]); 					} 		else if (DA_2354 == 20) { 			print(DA_2214(DA_2167(DA_2830[DA_2114]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2830[DA_2114]); 					} 		else { 			if (DA_2150 == 1)        print(DA_2214(DA_2167(DA_2832[1]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2832[1])      else        print(DA_2214(DA_2167(DA_2832[0]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2832[0])     		} 			} 	return DA_2150; 	} function DA_2152(DA_2150, DA_2151) { 	if (DA_2355 == DA_2151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA_2150 += DA_2838[DA_2355][2]  				        DA_2359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA_2150 -= DA_2838[DA_2355][2]  				        DA_2359 = TRUE; 							} 			DA_2150 = clamp(DA_2150, DA_2838[DA_2355][0], DA_2838[DA_2355][1]); 		} 		if (DA_2355 == 8) { 			print(DA_2214(DA_2167(DA_2822[DA_2386]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2386])     		} 		else if (DA_2355 == 9) { 			print(DA_2214(DA_2167(DA_2822[DA_2387]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2387])     		} 		else if (DA_2355 == 10) { 			print(DA_2214(DA_2167(DA_2822[DA_2388]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2388])     		} 		else if (DA_2355 == 11) { 			print(DA_2214(DA_2167(DA_2822[DA_2389]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2389])     		} 		else if (DA_2355 == 12) { 			print(DA_2214(DA_2167(DA_2822[DA_2390]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2390])     		} 		else if (DA_2355 == 13) { 			print(DA_2214(DA_2167(DA_2822[DA_2391]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2391])     		} 		else if (DA_2355 == 14) { 			print(DA_2214(DA_2167(DA_2822[DA_2392]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2392])     		} 		else if (DA_2355 == 15) { 			print(DA_2214(DA_2167(DA_2822[DA_2393]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2393])     		} 		else if (DA_2355 == 16) { 			print(DA_2214(DA_2167(DA_2857[DA_2394]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2394])     		} 		else if (DA_2355 == 17) { 			print(DA_2214(DA_2167(DA_2822[DA_2275]),OLED_FONT_SMALL_WIDTH ),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2822[DA_2275])  		} 		else if(DA_2355 == 18){ 			print(DA_2214(DA_2167(DA_2822[DA_2276]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2822[DA_2276])  		} 		else if(DA_2355 == 19){ 			print(DA_2214(DA_2167(DA_2822[DA_2277]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2822[DA_2277])  		} 		else if(DA_2355 == 20){ 			print(DA_2214(DA_2167(DA_2822[DA_2278]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2822[DA_2278])  		} 		else if(DA_2355 == 21){ 			print(DA_2214(DA_2167(DA_2857[DA_2279]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2857[DA_2279])       		} 		else if(DA_2355 == 22){ 			print(DA_2214(DA_2167(DA_2822[DA_2407]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2407])     		} 		else if (DA_2355 == 23) { 			print(DA_2214(DA_2167(DA_2822[DA_2408]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2408])     		} 		else if (DA_2355 == 24) { 			print(DA_2214(DA_2167(DA_2822[DA_2409]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2409])     		} 		else if (DA_2355 == 25) { 			print(DA_2214(DA_2167(DA_2822[DA_2410]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2410])     		} 		else if (DA_2355 == 26) { 			print(DA_2214(DA_2167(DA_2822[DA_2411]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2822[DA_2411])     		} 		else if (DA_2355 == 27) { 			print(DA_2214(DA_2167(DA_2857[DA_2412]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2412])     		} 		else if (DA_2355 == 28) { 			print(DA_2214(DA_2167(DA_2881[DA_2413]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2881[DA_2413])     		} 		else if (DA_2355 == 29) { 			print(DA_2214(DA_2167(DA_2883[DA_2414]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2883[DA_2414])     		} 		else if (DA_2355 == 30) { 			print(DA_2214(DA_2167(DA_2857[DA_2415]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2415])     		} 		else if (DA_2355 == 31) { 			print(DA_2214(DA_2167(DA_2881[DA_2416]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2881[DA_2416])     		} 		else if (DA_2355 == 32) { 			print(DA_2214(DA_2167(DA_2883[DA_2417]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2883[DA_2417])     		} 		else if (DA_2355 == 33) { 			print(DA_2214(DA_2167(DA_2857[DA_2418]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2418])     		} 		else if (DA_2355 == 34) { 			print(DA_2214(DA_2167(DA_2881[DA_2419]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2881[DA_2419])     		} 		else if (DA_2355 == 35) { 			print(DA_2214(DA_2167(DA_2883[DA_2420]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2883[DA_2420])     		} 		else if (DA_2355 == 36) { 			print(DA_2214(DA_2167(DA_2857[DA_2421]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2421])     		} 		else if (DA_2355 == 37) { 			print(DA_2214(DA_2167(DA_2881[DA_2422]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2881[DA_2422])     		} 		else if (DA_2355 == 38) { 			print(DA_2214(DA_2167(DA_2883[DA_2423]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2883[DA_2423])     		} 		else if (DA_2355 == 41) { 			print(DA_2214(DA_2167(DA_2857[DA_2426]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2426])     		} 		else if (DA_2355 == 48) { 			print(DA_2214(DA_2167(DA_2857[DA_2433]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2433])     		} 		else if (DA_2355 == 49) { 			print(DA_2214(DA_2167(DA_2857[DA_2434]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2434])     		} 		else if (DA_2355 == 50) { 			print(DA_2214(DA_2167(DA_2857[DA_2435]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2435])     		} 		else if (DA_2355 == 51) { 			print(DA_2214(DA_2167(DA_2857[DA_2436]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2857[DA_2436])     		} 		else if(DA_2355 == 0){ 			print(DA_2214(DA_2167(DA_2913[DA_2439]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2439])  		} 		else if(DA_2355 == 1){ 			print(DA_2214(DA_2167(DA_2913[DA_2440]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2440])  		} 		else if(DA_2355 == 2){ 			print(DA_2214(DA_2167(DA_2913[DA_2441]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2441])  		} 		else if(DA_2355 == 3){ 			print(DA_2214(DA_2167(DA_2913[DA_2442]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2442])  		} 		else if(DA_2355 == 4){ 			print(DA_2214(DA_2167(DA_2913[DA_2443]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2443])  		} 		else if(DA_2355 == 5){ 			print(DA_2214(DA_2167(DA_2913[DA_2444]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2444])  		} 		else if(DA_2355 == 6){ 			print(DA_2214(DA_2167(DA_2913[DA_2445]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2445])  		} 		else if(DA_2355 == 7){ 			print(DA_2214(DA_2167(DA_2913[DA_2446]),OLED_FONT_SMALL_WIDTH),DA_2807,OLED_FONT_SMALL,OLED_WHITE,DA_2913[DA_2446])  		} 		else{ 			if (DA_2150 == 1)        print(DA_2214(DA_2167(DA_2832[1]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2832[1])      else        print(DA_2214(DA_2167(DA_2832[0]), OLED_FONT_SMALL_WIDTH), DA_2807, OLED_FONT_SMALL, OLED_WHITE, DA_2832[0])     		} 		DA_2170(0); 			} 	return DA_2150; 	} function DA_2155(DA_2150, DA_2151) { 	if (DA_2355 == DA_2151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA_2150 += DA_2838[DA_2355][2]  				        DA_2359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA_2150 -= DA_2838[DA_2355][2]  				        DA_2359 = TRUE; 							} 			if (event_press(PS4_UP)) { 				DA_2150 += DA_2838[DA_2355][3]  				        DA_2359 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				DA_2150 -= DA_2838[DA_2355][3]  				        DA_2359 = TRUE; 							} 			DA_2150 = clamp(DA_2150, DA_2838[DA_2355][0], DA_2838[DA_2355][1]); 		} 		DA_2217(DA_2150, DA_2220(DA_2150)); 	} 	return DA_2150; 	} int DA_2940, DA_2941, DA_2942; function DA_2158(DA_2128, DA_2160, DA_2161, DA_2162, DA_2137) { 	DA_2941 = 1; 	DA_2942 = 10000; 	if (DA_2128 < 0)  	  { 		putc_oled(DA_2941, 45); 		DA_2941 += 1; 		DA_2128 = abs(DA_2128); 			} 	for (DA_2940 = 5; 	DA_2940 >= 1; 	DA_2940--) { 		if (DA_2160 >= DA_2940) { 			putc_oled(DA_2941, DA_2948[DA_2128 / DA_2942]); 			DA_2128 = DA_2128 % DA_2942; 			DA_2941 += 1; 					} 		DA_2942 /= 10; 			} 	puts_oled(DA_2161, DA_2162, DA_2137, DA_2941 - 1, OLED_WHITE); } const string DA_2574 = " No Edit Variable"; const string DA_2573 = " A/CROSS to Edit "; const string DA_2569 = "MOD;"; const string DA_2571 = "MSL;"; int DA_2952; function DA_2164(DA_2147) { 	DA_2147 = abs(DA_2147); 	if (DA_2147 / 10000 > 0) return 5; 	if (DA_2147 / 1000 > 0) return 4; 	if (DA_2147 / 100 > 0) return 3; 	if (DA_2147 / 10 > 0) return 2; 	return 1; 	} const int8 DA_2948[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int DA_2954, DA_2955; const image DA_2957 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DA_272 { 	call(DA_273); 	DA_2166(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, DA_2957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DA_2957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(DA_274); 	vm_tctrl(0);wait( 1000); 	DA_2356 = TRUE; 	} combo DA_273 { 	cls_oled(OLED_BLACK); 	} int DA_2959; enum { 	DA_2960 = -2, DA_2961, DA_2962 = 5, DA_2963 = -1, DA_2964 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo DA_274 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 DA_21388[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DA_21389[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DA_21390[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 DA_21391[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int DA_2965; int DA_2966; int DA_2967; int DA_2968; int DA_2969; int DA_2970; int DA_2971; function DA_2166() { 	DA_2971 = 3; 	DA_2969 = DA_2971 * DA_21391[DA_2970]; 	DA_2968 = DA_2971 * DA_21388[DA_2970]; 	DA_2966 = ((DA_2969 * DA_21390[DA_2965]) / 328) - ((DA_2968 * DA_21389[DA_2965]) / 328); 	DA_2967 = ((DA_2969 * DA_21389[DA_2965]) / 328) + ((DA_2968 * DA_21390[DA_2965]) / 328); 	DA_2969 = DA_2966; 	DA_2968 = DA_2967; 	DA_2970 += 1; 	DA_2965 += 45; 	if(DA_2970 >= 360) { 		DA_2970 %= 360; 			} 	if(DA_2965 >= 360) { 		DA_2965 %= 360; 			} 	pixel_oled(64 + (((DA_2969 / DA_2971) * 30) / 328), 32 + (((DA_2968 / DA_2971) * 30) / 328), OLED_WHITE); 	} int DA_2975; function DA_2167(DA_2141) { 	DA_2975 = 0; 	do { 		DA_2141++; 		DA_2975++; 			} 	while (duint8(DA_2141)); 	return DA_2975; 	} int DA_2978; const uint8 DA_21392[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function DA_2169() { 	for (DA_2978 = 0; 	DA_2978 < sizeof(DA_21392) / sizeof(DA_21392[0]); 	DA_2978++) { 		if (get_ival(DA_21392[DA_2978]) || event_press(DA_21392[DA_2978])) { 			set_val(DA_21392[DA_2978], 0); 		} 			} 	} define DA_2979 = 131; define DA_2980 = 132; define DA_2981 = 133; define DA_2982 = 134; define DA_2983 = 130; define DA_2984 = 89; define DA_2985 = 127; define DA_2986 = 65; int DA_2987; int DA_2988; int DA_2989 = 1; define DA_2990 = 36; const string DA_2991 = "Hold LT/L2 +"; function DA_2170(DA_2171) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, DA_2991[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, DA_2989); 	putc_oled(1, DA_2981); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DA_2987); 	putc_oled(1, DA_2982); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DA_2988); 	if (DA_2171) { 		putc_oled(1, DA_2979); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DA_2987); 		putc_oled(1, DA_2980); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DA_2988); 			} 	} const uint8 DA_21394 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function DA_2172() { 	DA_21001 = sizeof(data); 	DA_2508 = get_pvar(SPVAR_1,0,1,0); 	DA_2514 = get_pvar(SPVAR_2,0,1,0); 	DA_2507 = get_pvar(SPVAR_3,11111, 99999,11111); 	DA_2174(); 	if (DA_2199(0, 1, 0)) { 		DA_2365 = DA_2199(  0, 6, 0); 		DA_2362 = DA_2199(0, 3, 0); 		DA_2363 = DA_2199(0,1,0); 		DA_2364 = DA_2199(0,1,0); 		DA_2275 = DA_2199(0, 70, 0); 		DA_2276 = DA_2199(0, 70, 0); 		DA_2277 = DA_2199(0, 70, 0); 		DA_2278 = DA_2199(0, 70, 0); 		DA_2279 = DA_2199(0, 22, 8); 		DA_2366 = DA_2199(0, 70, 0); 		DA_2367 = DA_2199(0, 70, 0); 		DA_2368 = DA_2199(0, 70, 0); 		DA_2369 = DA_2199(0, 70, 0); 		DA_2370 = DA_2199(0, 1, 0); 		DA_2371 = DA_2199(0, 1, 0); 		DA_2372 = DA_2199(0, 1, 0); 		DA_2373 = DA_2199(0, 1, 0); 		DA_2381 = DA_2199(0, 1, 0); 		DA_2407 = DA_2199(0, 70, 0); 		DA_2408 = DA_2199(0, 70, 0); 		DA_2409 = DA_2199(0, 70, 0); 		DA_2410 = DA_2199(0, 70, 0); 		DA_2411 = DA_2199(0, 70, 0); 		DA_2412 = DA_2199(1, 25, 1); 		DA_2413 = DA_2199(0, 1, 0); 		DA_2414 = DA_2199(0, 1, 0); 		DA_2415 = DA_2199(1, 25, 5); 		DA_2416 = DA_2199(0, 1, 0); 		DA_2417 = DA_2199(0, 1, 0); 		DA_2418 = DA_2199(0, 25, 2); 		DA_2419 = DA_2199(0, 1, 0); 		DA_2420 = DA_2199(0, 1, 1); 		DA_2421 = DA_2199(1, 25, 8); 		DA_2422 = DA_2199(0, 1, 0); 		DA_2423 = DA_2199(0, 1, 1); 		DA_2424 = DA_2199(350, 600, 350); 		DA_2425 = DA_2199(350, 600, 445); 		DA_2426 = DA_2199(0, 22, 0); 		DA_2427 = DA_2199(0, 1, 0); 		DA_2428 = DA_2199(-100, 300, 0); 		DA_2374 = DA_2199(0, 1, 0); 		DA_2375 = DA_2199(0, 1, 0); 		DA_2376 = DA_2199(0, 1, 0); 		DA_2377 = DA_2199(0, 1, 0); 		DA_2378 = DA_2199(0, 1, 0); 		DA_2429 = DA_2199(-150, 150, 0); 		DA_2430 = DA_2199(-150, 150, 0); 		DA_2431 = DA_2199(0, 1, 0); 		DA_2432 = DA_2199(-150, 150, 0); 		DA_2433 = DA_2199(0, 22, 0); 		DA_2434 = DA_2199(0, 22, 0); 		DA_2435 = DA_2199(0, 22, 0); 		DA_2436 = DA_2199(0, 22, 0); 		DA_2608 = DA_2199(60, 400, 235); 		DA_2438 = DA_2199(0, 1, 0); 		DA_2437 = DA_2199(0, 1, 0); 		DA_2361 = DA_2199(0, 3, 0); 		DA_2386 = DA_2199(0, 70, 0); 		DA_2387 = DA_2199(0, 70, 0); 		DA_2388 = DA_2199(0, 70, 0); 		DA_2391 = DA_2199(0, 70, 0); 		DA_2392 = DA_2199(0, 70, 0); 		DA_2393 = DA_2199(0, 70, 0); 		DA_2394 = DA_2199(0, 22, 8); 		DA_2379 = DA_2199(0, 1, 0); 		DA_2389 = DA_2199(0, 70, 0); 		DA_2390 = DA_2199(0, 70, 0); 		DA_2600 = DA_2199(0, 2500, 1100); 		DA_21252 = DA_2199(0, 1, 0); 		DA_21245 = DA_2199(0, 1, 0); 		DA_2114 = DA_2199(0, 10, 0); 		DA_2406 = DA_2199(0, 1, 0); 		DA_2360 = DA_2199(0, 2, 0); 		DA_2439 = DA_2199(0, 9, 9); 		DA_2440 = DA_2199(0, 9, 8); 		DA_2441 = DA_2199(0, 9, 3); 		DA_2442 = DA_2199(0, 9, 1); 		DA_2443 = DA_2199(0, 9, 4); 		DA_2444 = DA_2199(0, 9, 0); 		DA_2445 = DA_2199(0, 9, 7); 		DA_2446 = DA_2199(0, 9, 6); 		DA_2382    = DA_2199(0, 1, 0); 		DA_2383    = DA_2199(0, 1, 0); 		DA_2384     = DA_2199(0, 1, 0); 		DA_2395     = DA_2199(60, 500, 120); 		DA_2396     = DA_2199(60, 500, 350); 		DA_2397    = DA_2199(0, 1, 0); 		DA_2398 = DA_2199(0, 1, 0); 		DA_2399     = DA_2199(50, 250, 80); 		DA_2400     = DA_2199(100, 850, 180); 		DA_2401 = DA_2199(0, 1, 0); 		DA_2402    = DA_2199(0, 1, 0); 		DA_2403        = DA_2199(80, 500, 120); 		DA_2404        = DA_2199(80, 500, 350); 		DA_2405       = DA_2199(0, 1, 0); 		DA_2455           = DA_2199(3, 60, 3); 		DA_229           = DA_2199(0, 1, 0); 		DA_2476         = DA_2199(0, 1, 0); 		DA_2474       = DA_2199(0, 1, 0); 	} 	else{ 		DA_2365 = 0; 		DA_2362 = 0; 		DA_2363 = 0; 		DA_2364 = 0; 		DA_2275 = 0; 		DA_2276 = 0; 		DA_2277 = 0; 		DA_2278 = 0; 		DA_2279 = 8; 		DA_2366 = 0; 		DA_2367 = 0; 		DA_2368 = 0; 		DA_2369 = 0; 		DA_2370 = 0; 		DA_2371 = 0; 		DA_2372 = 0; 		DA_2373 = 0; 		DA_2381 = 0; 		DA_2407 = 0; 		DA_2408 = 0; 		DA_2409 = 0; 		DA_2410 = 0; 		DA_2411 = 0; 		DA_2412 = 1; 		DA_2413 = 0; 		DA_2414 = 0; 		DA_2415 = 5; 		DA_2416 = 0; 		DA_2417 = 0; 		DA_2418 = 2; 		DA_2419 = 0; 		DA_2420 = 1; 		DA_2421 = 8; 		DA_2422 = 0; 		DA_2423 = 1; 		DA_2424 = 350; 		DA_2425 = 445; 		DA_2426 = 0; 		DA_2427 = 0; 		DA_2428 = 0; 		DA_2374 = 0; 		DA_2375 = 0; 		DA_2376 = 0; 		DA_2377 = 0; 		DA_2378 = 0; 		DA_2429 = 0; 		DA_2430 = 0; 		DA_2431 = 0; 		DA_2432 = 0; 		DA_2433 = 0; 		DA_2434 = 0; 		DA_2435 = 0; 		DA_2436 = 0; 		DA_2608 = 235; 		DA_2438 = 0; 		DA_2437 = 0; 		DA_2361 = 0; 		DA_2386 = 0; 		DA_2387 = 0; 		DA_2388 = 0; 		DA_2391 = 0; 		DA_2392 = 0; 		DA_2393 = 0; 		DA_2394 = 8; 		DA_2379 = 0; 		DA_2389 = 0; 		DA_2390 = 0; 		DA_2600 = 1100; 		DA_21252 = 0; 		DA_21245 = 0; 		DA_2114 = 0; 		DA_2406 = 0; 		DA_2360 = 0; 		DA_2439 = 9; 		DA_2440 = 8; 		DA_2441 = 3; 		DA_2442 = 1; 		DA_2443 = 4; 		DA_2444 = 0; 		DA_2445 = 7; 		DA_2446 = 6; 		DA_2382 = 0; 		DA_2383 = 0; 		DA_2384 = 0; 		DA_2395 = 120; 		DA_2396 = 350; 		DA_2397 = 0; 		DA_2398 = 0; 		DA_2399 = 80; 		DA_2400 = 180; 		DA_2401 = 0; 		DA_2402 = 0; 		DA_2403 = 120; 		DA_2404 = 360; 		DA_2405 = 0; 		DA_2455     = 3; 		DA_229     = 0; 		DA_2476     = 0; 		DA_2474     = 0; 			} 	if (DA_2360 == 0) { 		DA_2447 = PS4_CIRCLE; 		DA_2448 = PS4_CROSS; 		DA_2449 = PS4_L1; 		DA_2450 = PS4_R1; 		DA_2451 = PS4_L2; 		DA_2452 = PS4_R2; 		DA_2453 = PS4_SQUARE; 		DA_2454 = PS4_TRIANGLE; 			} 	else if (DA_2360 == 1) { 		DA_2447      = PS4_SQUARE; 		DA_2448      = PS4_CROSS ; 		DA_2449    = PS4_L1    ; 		DA_2450  = PS4_R1; 		DA_2451    = PS4_L2; 		DA_2452    = PS4_R2; 		DA_2453     = PS4_CIRCLE; 		DA_2454  = PS4_TRIANGLE; 	} 	else if (DA_2360 == 2) { 		DA_2447 = DA_21394[DA_2439]; 		DA_2448 = DA_21394[DA_2440]; 		DA_2449 = DA_21394[DA_2441]; 		DA_2450 = DA_21394[DA_2442]; 		DA_2451 = DA_21394[DA_2443]; 		DA_2452 = DA_21394[DA_2444]; 		DA_2453 = DA_21394[DA_2445]; 		DA_2454 = DA_21394[DA_2446]; 			} 	} function DA_2173() { 	DA_2174(); 	DA_2197(   1,0,     1); 	DA_2197(DA_2365, 0, 6); 	DA_2197(DA_2362, 0, 3); 	DA_2197(DA_2363, 0 , 1); 	DA_2197(DA_2364, 0 , 1); 	DA_2197(DA_2275, 0, 70); 	DA_2197(DA_2276, 0, 70); 	DA_2197(DA_2277, 0, 70); 	DA_2197(DA_2278, 0, 70); 	DA_2197(DA_2279, 0, 22); 	DA_2197(DA_2366, 0, 70); 	DA_2197(DA_2367, 0, 70); 	DA_2197(DA_2368, 0, 70); 	DA_2197(DA_2369, 0, 70); 	DA_2197(DA_2370, 0, 1); 	DA_2197(DA_2371, 0, 1); 	DA_2197(DA_2372, 0, 1); 	DA_2197(DA_2373, 0, 1); 	DA_2197(DA_2381, 0, 1); 	DA_2197(DA_2407, 0, 70); 	DA_2197(DA_2408, 0, 70); 	DA_2197(DA_2409, 0, 70); 	DA_2197(DA_2410, 0, 70); 	DA_2197(DA_2411, 0, 70); 	DA_2197(DA_2412, 1, 25); 	DA_2197(DA_2413, 0, 1); 	DA_2197(DA_2414, 0, 1); 	DA_2197(DA_2415, 1, 25); 	DA_2197(DA_2416, 0, 1); 	DA_2197(DA_2417, 0, 1); 	DA_2197(DA_2418, 0, 25); 	DA_2197(DA_2419, 0, 1); 	DA_2197(DA_2420, 0, 1); 	DA_2197(DA_2421, 1, 25); 	DA_2197(DA_2422, 0, 1); 	DA_2197(DA_2423, 0, 1); 	DA_2197(DA_2424, 350, 600); 	DA_2197(DA_2425, 350, 600); 	DA_2197(DA_2426, 0, 22); 	DA_2197(DA_2427, 0, 1); 	DA_2197(DA_2428, -100, 300); 	DA_2197(DA_2374, 0, 1); 	DA_2197(DA_2375, 0, 1); 	DA_2197(DA_2376, 0, 1); 	DA_2197(DA_2377, 0, 1); 	DA_2197(DA_2378, 0, 1); 	DA_2197(DA_2429, -150, 150); 	DA_2197(DA_2430, -150, 150); 	DA_2197(DA_2431, 0, 1); 	DA_2197(DA_2432, -150, 150); 	DA_2197(DA_2433, 0, 22); 	DA_2197(DA_2434, 0, 22); 	DA_2197(DA_2435, 0, 22); 	DA_2197(DA_2436, 0, 22); 	DA_2197(DA_2608, 60, 400); 	DA_2197(DA_2438, 0, 1); 	DA_2197(DA_2437, 0, 1); 	DA_2197(DA_2361, 0, 3); 	DA_2197(DA_2386, 0, 70); 	DA_2197(DA_2387, 0, 70); 	DA_2197(DA_2388, 0, 70); 	DA_2197(DA_2391, 0, 70); 	DA_2197(DA_2392, 0, 70); 	DA_2197(DA_2393, 0, 70); 	DA_2197(DA_2394, 0, 22); 	DA_2197(DA_2379, 0, 1); 	DA_2197(DA_2389, 0, 70); 	DA_2197(DA_2390, 0, 70); 	DA_2197(DA_2600, 0, 2500); 	DA_2197(DA_21252, 0, 1); 	DA_2197(DA_21245, 0, 1); 	DA_2197(DA_2114, 0, 10); 	DA_2197(DA_2406, 0, 1); 	DA_2197(DA_2360, 0, 2); 	DA_2197(DA_2439, 0, 9); 	DA_2197(DA_2440, 0, 9); 	DA_2197(DA_2441, 0, 9); 	DA_2197(DA_2442, 0, 9); 	DA_2197(DA_2443, 0, 9); 	DA_2197(DA_2444, 0, 9); 	DA_2197(DA_2445, 0, 9); 	DA_2197(DA_2446, 0, 9); 	DA_2197(DA_2382,    0, 1); 	DA_2197(DA_2383,    0, 1); 	DA_2197(DA_2384,     0, 1); 	DA_2197(DA_2395,     60, 500); 	DA_2197(DA_2396,     60, 500); 	DA_2197(DA_2397,    0, 1); 	DA_2197(DA_2398, 0, 1); 	DA_2197(DA_2399,     50, 250); 	DA_2197(DA_2400,     100, 850); 	DA_2197(DA_2401, 0, 1); 	DA_2197(DA_2402,    0, 1); 	DA_2197(DA_2403,        80, 500); 	DA_2197(DA_2404,        80, 500); 	DA_2197(DA_2405,       0, 1); 	DA_2197(DA_2455 ,         3,60); 	DA_2197(DA_229,           0,1); 	DA_2197(DA_2476,           0,1); 	DA_2197(DA_2474,           0,1); 	} function DA_2174() { 	DA_21008 = SPVAR_4; 	DA_21009 = 0; 	DA_21011 = 0; 	} int DA_21009,  DA_21008, DA_21011, DA_21012, DA_21013; function DA_2175(DA_2176) { 	DA_21012 = 0; 	while (DA_2176) { 		DA_21012++; 		DA_2176 = abs(DA_2176 >> 1); 	} 	return DA_21012; 	} function DA_2177(DA_2178, DA_2179) { 	DA_21012 = max(DA_2175(DA_2178), DA_2175(DA_2179)); 	if (DA_2180(DA_2178, DA_2179)) { 		DA_21012++; 	} 	return DA_21012; 	} function DA_2180(DA_2178, DA_2179) { 	return DA_2178 < 0 || DA_2179 < 0; 	} function DA_2183(DA_2184) { 	return 1 << clamp(DA_2184 - 1, 0, 31); 	} function DA_2185(DA_2184) { 	if (DA_2184 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - DA_2184); } function DA_2187(DA_2184) { 	return DA_2185(DA_2184 - 1); 	} function DA_2189(DA_2176, DA_2184) { 	if (DA_2176 < 0) { 		return (abs(DA_2176) & DA_2187(DA_2184)) | DA_2183(DA_2184); 	} 	return DA_2176 & DA_2187(DA_2184); } function DA_2192(DA_2176, DA_2184) { 	if (DA_2176 & DA_2183(DA_2184)) { 		return 0 - (DA_2176 & DA_2187(DA_2184)); 	} 	return DA_2176 & DA_2187(DA_2184); } function DA_2195(DA_2196) { 	return get_pvar(DA_2196, 0x80000000, 0x7FFFFFFF, 0); 	} function DA_2197(DA_2176, min, max) { 	DA_21013 = DA_2177(min, max); 	DA_2176 = clamp(DA_2176, min, max); 	if (DA_2180(min, max)) { 		DA_2176 = DA_2189(DA_2176, DA_21013); 	} 	DA_2176 = DA_2176 & DA_2185(DA_21013); 	if (DA_21013 >= 32 - DA_21009) { 		DA_21011 = DA_21011 | (DA_2176 << DA_21009); 		set_pvar(DA_21008, DA_21011); 		DA_21008++; 		DA_21013 -= (32 - DA_21009); 		DA_2176 = DA_2176 >> (32 - DA_21009); 		DA_21009 = 0; 		DA_21011 = 0; 	} 	DA_21011 = DA_21011 | (DA_2176 << DA_21009); 	DA_21009 += DA_21013; 	if (!DA_21009) { 		DA_21011 = 0; 	} 	set_pvar(DA_21008, DA_21011); } function DA_2199(min, max, DA_2200) { 	DA_21013 = DA_2177(min, max); 	DA_21011 = (DA_2195(DA_21008) >> DA_21009) & DA_2185(DA_21013); 	if (DA_21013 >= 32 - DA_21009) { 		DA_21011 = (DA_21011 & DA_2185(32 - DA_21009)) | ((DA_2195(DA_21008 + 1) & DA_2185(DA_21013 - (32 - DA_21009))) << (32 - DA_21009)); 	} 	DA_21009 += DA_21013; 	DA_21011 = DA_21011 & DA_2185(DA_21013); 	if (DA_21009 >= 32) { 		DA_21008++; 		DA_21009 -= 32; 	} 	if (DA_2180(min, max)) { 		DA_21011 = DA_2192(DA_21011, DA_21013); 	} 	if (DA_21011 < min || DA_21011 > max) { 		return DA_2200; 	} 		if(DA_2202[289] != 8048){    DA_2199(min, max, DA_2200); 	} 	return DA_21011; 	} const string DA_21039 = "SETTINGS"; const string DA_21040 = "WAS SAVED"; combo DA_275 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	DA_2173(); 	print(15, 2, OLED_FONT_MEDIUM, 1, DA_21039[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, DA_21040[0]); 	DA_21041 = 1500; 	combo_run(DA_276); 	} int DA_21041 = 1500; combo DA_276 { 	vm_tctrl(0);wait( DA_21041); 	cls_oled(0); 	DA_2358 = FALSE; 	} define DA_21042 = 0; define DA_21043 = 1; define DA_21044 = 2; define DA_21045 = 3; define DA_21046 = 4; define DA_21047 = 5; define DA_21048 = 6; define DA_21049 = 7; int DA_2687; int DA_21051; int DA_21052, DA_2669; int DA_2479; int DA_21055 = 49; int DA_21056 = 200; int DA_2718 = TRUE; combo DA_277 { 	set_val(DA_2448, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA_2448, 0); 	vm_tctrl(0);wait( 120); 	if (DA_2427) DA_2245(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int DA_2615; int DA_2622; combo DA_278 { 	if (DA_2622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (DA_2622) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (DA_2622) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( DA_2424); 	if (DA_2622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	DA_2615 = FALSE; 	DA_2229(DA_2615); 	} int DA_2624; int DA_2625; combo DA_279 { 	if (DA_2625) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (DA_2625) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(DA_2453, 100); 	set_val(DA_2450, 100); 	set_val(DA_2451, 100); 	DA_21199 = 4000; 	vm_tctrl(0);wait( DA_2425); 	vm_tctrl(0);wait( 50); 	set_val(DA_2453, 100); 	vm_tctrl(0);wait( 50); 	DA_2624 = FALSE; 	DA_2229(DA_2624); 	} int DA_21062 = TRUE; function DA_2201(DA_2202) { 	if (DA_2202) { 		DA_21063 = DA_21095; 			} 	else { 		DA_21063 = DA_21094; 			} 	combo_run(DA_280); 	} int DA_21063; combo DA_280 { 	DA_2222(DA_21063); 	vm_tctrl(0);wait( 300); 	DA_2222(DA_21092); 	vm_tctrl(0);wait( 100); 	DA_2222(DA_21063); 	vm_tctrl(0);wait( 300); 	DA_2222(DA_21092); 	} define DA_21067 = 100; define DA_21068 = 130; const string DA_2552 = "SCRIPT WAS"; function DA_2203(DA_2128, DA_2205, DA_2206) { 	if (!DA_2352 && !DA_2353) { 		cls_oled(0); 		print(DA_2205, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA_2206); 		if (DA_2128) { 			print(DA_2207(sizeof(DA_21072) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA_21072[0]); 		} 		else { 			print(DA_2207(sizeof(DA_21073) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA_21073[0]); 		} 		DA_2201(DA_2128); 			} 	} function DA_2207(DA_2143, DA_2137) { 	return (OLED_WIDTH / 2) - ((DA_2143 * DA_2137) / 2); 	} const string DA_21073 = "OFF"; const string DA_21072 = "ON"; function DA_2210(DA_2134, DA_2212, DA_2128) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(DA_2134, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA_2212); 	DA_2217(DA_2128, DA_2220(DA_2128)); 	DA_2356 = TRUE; 	} const string DA_2595 = "EA PING"; const string DA_2617 = "FK_POWER"; const string DA_2609 = "MaxFnshPwr"const string DA_2601 = "JK_Agg"; int DA_2600; int DA_2608; function DA_2214(DA_2143, DA_2137) { 	return (OLED_WIDTH / 2) - ((DA_2143 * DA_2137) / 2); 	} int DA_21082; int DA_21083, DA_21084; function DA_2217(DA_2128, DA_2160) { 	DA_21082 = 1; 	DA_21084 = 10000; 	if (DA_2128 < 0) { 		putc_oled(DA_21082, 45); 		DA_21082 += 1; 		DA_2128 = abs(DA_2128); 			} 	for (DA_21083 = 5; 	DA_21083 >= 1; 	DA_21083--) { 		if (DA_2160 >= DA_21083) { 			putc_oled(DA_21082, (DA_2128 / DA_21084) + 48); 			DA_2128 %= DA_21084; 			DA_21082++; 			if (DA_21083 == 4) { 				putc_oled(DA_21082, 44); 				DA_21082++; 							} 					} 		DA_21084 /= 10; 			} 	puts_oled(DA_2214(DA_21082 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA_21082 - 1, OLED_WHITE); 	} int DA_21088; function DA_2220(DA_2221) { 	DA_21088 = 0; 	do { 		DA_2221 /= 10; 		DA_21088++; 			} 	while (DA_2221); 	return DA_21088; 	} int DA_2631; define DA_21092 = 0; define DA_21093 = 1; define DA_21094 = 2; define DA_21095 = 3; define DA_21096 = 4; define DA_21097 = 5; define DA_21098 = 6; define DA_21099 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int DA_21100; function DA_2222(DA_2223) { 	for (DA_21100 = 0; 	DA_21100 < 3; 	DA_21100++) { 		set_rgb(data[DA_2223][0], data[DA_2223][1], data[DA_2223][2]); 			} 	} const int8 DA_21404[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int DA_2634 = PS4_L3; define DA_21102 = 1; define DA_21103 = 2; define DA_21104 = 3; define DA_21105 = 2; define DA_21106 = 3; define DA_21107 = 4; define DA_21108 = 5; define DA_21109 = 6; define DA_21110 = 7; define DA_21111 = 8; define DA_21112 = 9; int DA_2628 = FALSE; int DA_21114; int DA_21115; int DA_21116; int DA_21117; define DA_21118 = PS4_LX; define DA_21119 = PS4_LY; define DA_21120 = PS4_RX; define DA_21121 = PS4_RY; function DA_2224 () { if(DA_2362 == 3){ 		if( get_ival(PS4_RY) < -70  && !DA_21114 && !combo_running(DA_20) ) { 			DA_21114 = TRUE; 			DA_2479 = FALSE; 			DA_21051 = DA_2275; 			            DA_2227(DA_2275); 		} 		if( get_ival(PS4_RY) >  70  && !DA_21115 && !combo_running(DA_20)) { 			DA_21115 = TRUE; 			DA_2479 = TRUE; 			DA_21051 = DA_2277; 			           DA_2227(DA_2277); 		} 		if( get_ival(PS4_RX) < -70  && !DA_21116 && !combo_running(DA_20) ) { 			DA_21116 = TRUE; 			DA_2479 = FALSE; 			DA_21051 = DA_2278; 			              DA_2227(DA_2278); 		} 		if( get_ival(PS4_RX) >  70  && !DA_21117 && !combo_running(DA_20) ) { 			DA_21117 = TRUE; 			DA_2479 = TRUE; 			DA_21051 = DA_2276; 			            DA_2227(DA_2276); 		} 			set_val(DA_21120,0);              set_val(DA_21121,0);  			} 	else if(DA_2362 < 3 && !get_ival(XB1_RS) &&  !get_ival(DA_2451) && !get_ival(DA_2452) && !get_ival(DA_2450)) { 		if( get_ival(PS4_RY) < -70  && !DA_21114 && !combo_running(DA_20) ) { 			DA_21114 = TRUE; 			DA_2479 = FALSE; 			DA_21051 = DA_2275; 			            DA_2227(DA_2275); 		} 		if( get_ival(PS4_RY) >  70  && !DA_21115 && !combo_running(DA_20)) { 			DA_21115 = TRUE; 			DA_2479 = TRUE; 			DA_21051 = DA_2277; 			           DA_2227(DA_2277); 		} 		if( get_ival(PS4_RX) < -70  && !DA_21116 && !combo_running(DA_20) ) { 			DA_21116 = TRUE; 			DA_2479 = FALSE; 			DA_21051 = DA_2278; 			              DA_2227(DA_2278); 		} 		if( get_ival(PS4_RX) >  70  && !DA_21117 && !combo_running(DA_20) ) { 			DA_21117 = TRUE; 			DA_2479 = TRUE; 			DA_21051 = DA_2276; 			            DA_2227(DA_2276); 		} 			set_val(DA_21120,0);              set_val(DA_21121,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		DA_21114 = 0; 		DA_21115  = 0; 		DA_21116  = 0; 		DA_21117  = 0; 			} 	} function DA_2225() { 	if (DA_2502 == DA_2593) { 		DA_2479 = FALSE; 		if (DA_2386) DA_2227(DA_2386); 			} 	if (DA_2502 == DA_2232(DA_2593 + 4)) { 		DA_2479 = FALSE; 		if (DA_2393) DA_2227(DA_2393); 			} 	if (DA_2502 == DA_2232(DA_2593 + 1)) { 		DA_2479 = TRUE; 		if (DA_2388) DA_2227(DA_2388); 			} 	if (DA_2502 == DA_2232(DA_2593 - 1)) { 		DA_2479 = FALSE; 		if (DA_2387) DA_2227(DA_2387); 			} 	if (DA_2502 == DA_2232(DA_2593 + 2)) { 		DA_2479 = TRUE; 		if (DA_2390) DA_2227(DA_2390); 			} 	if (DA_2502 == DA_2232(DA_2593 - 2)) { 		DA_2479 = FALSE; 		if (DA_2389) DA_2227(DA_2389); 			} 	if (DA_2502 == DA_2232(DA_2593 + 3)) { 		DA_2479 = TRUE; 		if (DA_2392) DA_2227(DA_2392); 			} 	if (DA_2502 == DA_2232(DA_2593 - 3)) { 		DA_2479 = FALSE; 		if (DA_2391) DA_2227(DA_2391); 			} 	} int DA_21139; int DA_2500 = 0; function DA_2226() { 	if(DA_21139){ 		DA_2500 += get_rtime(); 			} 	if(DA_2500 >= 3000){ 		DA_2500 = 0; 		DA_21139 = FALSE; 			} 	if(DA_2361 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA_2503 && !combo_running(DA_20)) { 			DA_2503 = TRUE; 			DA_21139 = TRUE; 			DA_2500 = 0; 			DA_2502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA_2225(); 					} 		set_val(DA_21120, 0); 		set_val(DA_21121, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(DA_2451) && !get_ival(DA_2452) && !get_ival(DA_2450) && !get_ival(DA_2449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA_2503 && !combo_running(DA_20)) { 			DA_2503 = TRUE; 			DA_21139 = TRUE; 			DA_2500 = 0; 			DA_2502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA_2225(); 					} 		set_val(DA_21120, 0); 		set_val(DA_21121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		DA_2503 = FALSE; 			} 	} function DA_2227(DA_2228) { 	DA_21051 = DA_2228; 	DA_2202[-339 + (DA_2228 * 3)] = TRUE; 	DA_2718 = FALSE; 	block = TRUE; 	if (DA_2114 > 7)vm_tctrl(0); 	} int DA_21147; combo DA_281 { 	set_rumble(DA_21147, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function DA_2229(DA_2128) { 	if (DA_2128) DA_21147 = RUMBLE_A; 	else DA_21147 = RUMBLE_B; 	combo_run(DA_281); 	} int DA_21148 = 300; int DA_21149 ; combo DA_282 { 	DA_21149 = TRUE; 	vm_tctrl(0);wait( DA_21148); 	DA_21149 = FALSE; 	} combo DA_283 { 	DA_2231(); 	DA_2239(0, 0); 	vm_tctrl(0);wait( 20); 	DA_2239(0, 0); 	vm_tctrl(0);wait( 100); 	DA_2239(0, 0); 	set_val(DA_2452, 100); 	DA_2239(0, 0); 	vm_tctrl(0);wait( 60); 	DA_2239(0, 0); 	vm_tctrl(0);wait( 150); 	DA_2718 = TRUE; 	vm_tctrl(0);wait( 350); 	} function DA_2231() { 	DA_2687 = DA_2593  DA_2234(DA_2687); 	DA_21052 = DA_21153; 	DA_2669 = DA_2671; 	} combo DA_284 { 	set_val(DA_2451, 100); 	set_val(DA_2450, 100); 	vm_tctrl(0);wait( 100); 	set_val(DA_2451, 100); 	vm_tctrl(0);wait( 100); 	DA_2718 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 DA_21405[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int DA_21153, DA_2671, DA_2593; int DA_2502; int DA_2503; int DA_21158; function DA_2232(DA_2233) { 	DA_21158 = DA_2233; 	if (DA_21158 < 0) DA_21158 = 8 - abs(DA_2233); 	else if (DA_21158 >= 8) DA_21158 = DA_2233 - 8  return DA_21158; 	} function DA_2234(DA_2235) { 	if (DA_2235 < 0) DA_2235 = 8 - abs(DA_2235); 	else if (DA_2235 >= 8) DA_2235 = DA_2235 - 8; 	DA_21153 = DA_21405[DA_2235][0]; 	DA_2671 = DA_21405[DA_2235][1]; } function DA_2236(DA_2237, DA_2238) { 	set_val(DA_21120, DA_2237); 	set_val(DA_21121, DA_2238); 	} function DA_2239(DA_2240, DA_2241) { 	set_val(DA_21118, DA_2240); 	set_val(DA_21119, DA_2241); 	} function DA_2242() { 	if (DA_2479) { 		set_val(DA_21118, inv(DA_2669)); 		set_val(DA_21119, DA_21052); 			} 	else { 		set_val(DA_21118, DA_2669); 		set_val(DA_21119, inv(DA_21052)); 			} 	} function DA_2243() { 	if (DA_2479) { 		set_val(DA_21120, inv(DA_2669)); 		set_val(DA_21121, DA_21052); 			} 	else { 		set_val(DA_21120, DA_2669); 		set_val(DA_21121, inv(DA_21052)); 			} 	} function DA_2244() { 	if (!DA_2479) { 		set_val(DA_21120, inv(DA_2669)); 		set_val(DA_21121, DA_21052); 			} 	else { 		set_val(DA_21120, DA_2669); 		set_val(DA_21121, inv(DA_21052)); 			} 	} function DA_2245() { 	set_val(DA_21120, DA_21052); 	set_val(DA_21121, DA_2669); 	} function DA_2246() { 	set_val(DA_21120, inv(DA_21052)); 	set_val(DA_21121, inv(DA_2669)); 	} function DA_2247() { 	set_val(DA_21120, 0); 	set_val(DA_21121, 0); 	} int DA_21174; function DA_2248() { 	if ((event_press(DA_2448)  ) && !combo_running(DA_285) && (DA_21199  <= 0 || (DA_21199 < 3000 && DA_21199 > 1  )) && !get_ival(DA_2452) && DA_2555 > 500 &&!get_ival(DA_2451) &&!get_ival(DA_2447) &&!get_ival(DA_2450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA_285) ) { 		combo_run(DA_285); 			} 	if (combo_running(DA_285) && (        get_ival(DA_2452) ||        get_ival(DA_2451) ||        get_ival(DA_2447) ||        get_ival(DA_2450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA_285); 		DA_21330 = TRUE; 			} 	} combo DA_285 { vm_tctrl(0);wait(750); set_val(DA_2449,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(DA_21174 == 1 ){ DA_2236(100,inv(DA_2669));} else{ DA_2236(-100,inv(DA_2669)); } vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo DA_286 { 	vm_tctrl(0);wait( 800); 	DA_21200 = 0; 	} int DA_21177 = 1000; int DA_21178 = 1600; int DA_21179 = 1600; int DA_21180 = TRUE; int DA_21181 = TRUE; int DA_2664 = FALSE; int DA_21183 = TRUE; int DA_2658 = FALSE; int DA_21185 = TRUE; int DA_2660 = FALSE; int DA_21187 = TRUE; int DA_2662 = FALSE; function DA_2249(){ 	if (get_ival(DA_2449)) { 		DA_21189 = 1000; 		DA_21208 = 0; 		DA_2555 = 1; 		combo_stop(DA_295); 			} 	if (event_press(DA_2451) || event_press(DA_2434)) { 		DA_21189 = 4000; 		DA_21208 = 0; 		DA_21177 = 1600; 			} 	if (get_ival(DA_2450) && !get_ival(DA_2449) ) { 		DA_21189 = 0; 		DA_21208 = 0; 		DA_21177 = 1600; 			} 	else if (get_ival(DA_2449)){ 		DA_21189 = 1000; 			} 	if (DA_21189 > 0) { 		DA_21189 -= get_rtime(); 			} 	if (DA_21189 < 0) { 		DA_21189 = 0; 			} 	DA_21269 = DA_2428; 	if (event_release(DA_2448)) { 		DA_21195 = 1; 		DA_21208 = 0; 		DA_2555 = 1; 	} 	if (event_release(DA_2454)) { 		DA_21196 = 1; 		DA_21208 = 0; 		DA_2555 = 1; 	} 	if (event_release(DA_2449)) { 		DA_21178 = 1; 		DA_21208 = 0; 		DA_21177 = 1600; 			} 	if (event_release(DA_2450)) { 		DA_21179 = 1; 		DA_21208 = 0; 		DA_21177 = 1600; 			} 	if (event_release(DA_2453) || (get_ival(DA_2454) && get_ival(DA_2449))) { 		DA_21199 = 4000; 		DA_21208 = 0; 	} 	if (get_ival(DA_2448) && DA_21199 < 4000 && DA_21199 > 3500) { 		DA_21200 = DA_21199; 		DA_21199 = 0; 			} 	if (DA_21177 < 1510) { 		DA_21177 += get_rtime(); 			} 	if (DA_21178 < 1600) { 		DA_21178 += get_rtime(); 			} 	if (DA_21179 < 1600) { 		DA_21179 += get_rtime(); 			} 	if (DA_21199 > 0) { 		DA_21199 -= get_rtime(); 			} 	if (DA_21199 < 0) { 		DA_21199 = 0; 			} 	if (DA_21195 < 5100) { 		DA_21195 += get_rtime(); 			} 	if (DA_21196 < 4100) { 		DA_21196 += get_rtime(); 			} 	if (DA_21208 > 0) { 		DA_21208 -= get_rtime(); 			} 	if (DA_21208 < 0) { 		DA_21208 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		DA_21177 = 1; 		DA_21208 = 0; 			} 	if (combo_running(DA_292)) { 		set_val(DA_2448, 0); 		if(get_ival(DA_2448)){ 			DA_21211 = 0; 			combo_stop(DA_287); 			set_val(DA_2448, 0); 			combo_stop(DA_292); 			combo_run(DA_249); 					} 			} 	if ((combo_running(DA_297) || combo_running(DA_288))) { 		set_val(DA_2448, 0); 		if(get_ival(DA_2448)){ 			DA_2555 = 1; 			DA_21211 = 0; 			combo_stop(DA_287); 			set_val(DA_2448, 0); 			combo_stop(DA_297); 			combo_stop(DA_288); 			combo_run(DA_249); 					} 			} 	if (event_press(DA_2447)) { 		combo_run(DA_286); 			} 	if (DA_2555 > 1500) { 		if (DA_21178 < 1500) { 			DA_21213 = 120; 					} 		if (DA_21179 < 1500) { 			DA_21213 = 228; 					} 		else { 			DA_21213 = 200; 					} 			} 	if (DA_2555 < 1500) { 		DA_21213 = 450; 			} 	if (DA_2555 > 2700) { 		DA_21217 = 920; 			} 	else if (DA_2555 >= 0 && DA_2555 < 2700) { 		DA_21217 = 725; 			} 	} function DA_2250() { 	if (DA_21180) { 		if ((DA_2555 <= 600 || (DA_21177 <= 1500 && DA_21177 > 1) || ( DA_21178 <= 150 || DA_21179 <= 150)) && event_press(DA_2447) ) { 			if (!get_ival(DA_2450) && !get_ival(DA_2449) && !get_ival(DA_2451) && !get_ival(DA_2452)) { 				set_val(DA_2447, 0); 				if (DA_21199 < 4000 && DA_21199 > 1) { 					set_val(DA_2447, 0); 					combo_run(DA_290); 									} 				else { 					set_val(DA_2447, 0); 					combo_run(DA_288); 					DA_21208 = 9000; 				} 							} 					} 			} 	if (DA_21187) { 		if (DA_2555 > 1000 && !DA_21208 && (!get_ival(DA_2450) && !get_ival(PS4_L3) && event_press(DA_2447)) &&  DA_21178 > 150 &&  DA_21179 > 150) { 			if (!get_ival(DA_2449) && !get_ival(DA_2451)) { 				set_val(DA_2447, 0); 				if (((DA_21196 > 1 && DA_21196 <= 2500) || (DA_21195 > 1 && DA_21195 <= 3000)) &&  DA_21177 != 1600) { 					set_val(DA_2447, 0); 					combo_run(DA_289); 					DA_21208 = 9000; 									} 				else if (((DA_21196 > 2500 && DA_21196 <= 4000) || (DA_21195 > 3000 && DA_21195 <= 3500))  &&  DA_21177 != 1600) { 					set_val(DA_2447, 0); 					combo_run(DA_288); 					DA_21208 = 9000; 									} 				else if ((DA_21199 < 4000 && DA_21199 > 1)) { 					set_val(DA_2447, 0); 					combo_run(DA_290); 					DA_21208 = 9000; 									} 				else { 					set_val(DA_2447, 0); 					DA_2254(); 					DA_21208 = 9000; 									} 				DA_21208 = 9000; 							} 					} 			} 	if (DA_21181) { 		if (get_ival(DA_2449) && get_ival(DA_2450)) { 			if (!get_ival(DA_2451) && !get_ival(DA_2452) && (DA_21199 && DA_21195 > 1 && DA_21195 <= 1500) || (!DA_21199 && DA_21195 > 1 && DA_21195 <= 1500) || (DA_21195 > 1500 && !DA_21199) && !DA_21208) { 				if (event_press(DA_2447)) { 					set_val(DA_2447, 0); 					combo_run(DA_298); 					DA_21208 = 9000; 									} 							} 					} 			} 	if (DA_21185) { 		if (!get_ival(DA_2452) && !get_ival(DA_2449) && !get_ival(DA_2450)) { 			if (get_ival(DA_2451) && get_ival(DA_2447)) { 				DA_2255(); 				set_val(DA_2447, 0); 				DA_21208 = 9000; 							} 					} 			} 	if (DA_21183) { 		if (get_ival(DA_2450) && !get_ival(DA_2449) && !DA_21189 && DA_21178 > 1000) { 			if (!get_ival(DA_2451) && !get_ival(DA_2452) && !DA_21208) { 				if (get_ival(DA_2447) && (DA_2555 >= 1000  || DA_21177 > 1000) ){ 					set_val(DA_2447, 0); 					combo_run(DA_295); 					DA_21208 = 9000; 									} 				if (get_ival(DA_2447) && DA_2555 < 1000 && !DA_21189 && DA_21177 < 1000  && DA_21178 > 1000) { 					set_val(DA_2447, 0); 					combo_run(DA_296); 									} 							} 					} 			} 	if(get_ival(DA_2449) && ( combo_running(DA_296) || combo_running(DA_295))){ 	combo_stop(DA_295); 	combo_stop(DA_296); 	} 	if(combo_running(DA_290)){ 		DA_21211 = 0; 		combo_stop(DA_287)   	} 	if (get_ival(DA_2449) || DA_21189 > 0) { 		combo_stop(DA_295); 		combo_stop(DA_297); 		combo_stop(DA_296); 			} 	if (combo_running(DA_288) || combo_running(DA_292) || combo_running(DA_297) || combo_running(DA_298) || combo_running(DA_295)) { 		if (get_ival(DA_2448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA_2452) > 30) { 			combo_stop(DA_292); 			combo_stop(DA_297); 			combo_stop(DA_298); 			combo_stop(DA_295); 			combo_stop(DA_288); 			DA_21211 = 0; 			combo_stop(DA_287)     		} 			} 	if (combo_running(DA_288) || combo_running(DA_289)) { 		if (get_ival(DA_2448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA_2452)) { 			combo_stop(DA_290); 			combo_stop(DA_289); 			combo_stop(DA_288); 			DA_21211 = 0; 			combo_stop(DA_287)     		} 			} 	if (event_press(DA_2447) && DA_21208 > 100 && DA_21208 < 8990) { 		set_val(DA_2447, 0); 		combo_stop(DA_292); 		combo_stop(DA_297); 		combo_stop(DA_298); 		combo_stop(DA_295); 		combo_stop(DA_288); 		DA_21211 = 0; 		combo_stop(DA_287)    combo_run(DA_291); 			} 	if (!DA_2664) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_298); 					} 			} 	if (!DA_2658) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_295); 					} 			} 	if (!DA_2660) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_292); 			DA_21211 = 0; 			combo_stop(DA_287)     		} 			} 	if (!DA_2662) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA_297); 					} 			} 	if ((get_ival(DA_2452) || get_ival(DA_2448)) && !DA_2365) { 		combo_stop(DA_24); 		combo_stop(DA_247); 		combo_stop(DA_233); 			} 	} define DA_21221 = 15; define DA_21222 = 15; int DA_21223 = 0; define DA_21224 = 8000; define DA_21225 = 4; define DA_21226 = 2000; int DA_21211 = 0; const int16 DA_21406[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 DA_21407[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int DA_21228 = FALSE; int DA_21229; int DA_21230; int DA_21231; int DA_21232; function DA_2251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA_21231 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA_21228 = FALSE; 		for ( DA_2978 = 0; 		DA_2978 < sizeof(DA_21407) / sizeof(DA_21407[0]); 		DA_2978++) { 			if (DA_21231 == DA_21407[DA_2978]) { 				DA_21228 = TRUE; 				break; 							} 					} 		if (!DA_21228) { 			DA_21229 = DA_21407[0]; 			DA_21230 = abs(DA_21231 - DA_21407[0]); 			for ( DA_2978 = 1; 			DA_2978 < sizeof(DA_21407) / sizeof(DA_21407[0]); 			DA_2978++) { 				DA_21232 = abs(DA_21231 - DA_21407[DA_2978]); 				if (DA_21232 < DA_21230) { 					DA_21229 = DA_21407[DA_2978]; 					DA_21230 = DA_21232; 									} 							} 			set_polar(POLAR_LS, DA_21229, 32767); 					} 			} } function DA_2252 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA_21231 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA_21228 = FALSE; 		for ( DA_2978 = 0; 		DA_2978 < sizeof(DA_21406) / sizeof(DA_21406[0]); 		DA_2978++) { 			if (DA_21231 == DA_21406[DA_2978]) { 				DA_21228 = TRUE; 				break; 							} 					} 		if (!DA_21228) { 			DA_21229 = DA_21406[0]; 			DA_21230 = abs(DA_21231 - DA_21406[0]); 			for ( DA_2978 = 1; 			DA_2978 < sizeof(DA_21406) / sizeof(DA_21406[0]); 			DA_2978++) { 				DA_21232 = abs(DA_21231 - DA_21406[DA_2978]); 				if (DA_21232 < DA_21230) { 					DA_21229 = DA_21406[DA_2978]; 					DA_21230 = DA_21232; 									} 							} 			set_polar(POLAR_LS, DA_21229, 32767); 					} 			} } int DA_21245; function DA_2253() { 	if (combo_running(DA_287) && ( event_press(DA_2447)    ||   get_ival(DA_2452) ||         get_ival(DA_2448) ||        get_ival(DA_2453) ||        get_ival(DA_2454) ||        get_ival(DA_2449)      )) { 		combo_stop(DA_287); 		DA_21211 = 0; 			} 	if (DA_21223 == 0) { 		if ( ( DA_21199 == 0 && !combo_running(DA_290) && !combo_running(DA_298) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(DA_2447) || (DA_21211 == 1 ||     combo_running(DA_296) || combo_running(DA_294)|| combo_running(DA_292) ||  combo_running(DA_295)     || combo_running(DA_288) || combo_running(DA_297) || combo_running(DA_289) ||  combo_running(DA_291)  ))     ) { 			if(DA_2437)DA_2252 (); 			else if (DA_2438)DA_2251 (); 			combo_stop(DA_2103); 			combo_run(DA_287); 					} 			} 	else{ 		combo_stop(DA_287); 			} 	} combo DA_287 { 	if(DA_2437)DA_2252 (); 	else if (DA_2438)DA_2251 (); 	combo_stop(DA_2103); 	vm_tctrl(0); 	wait(4000); 	DA_21211 = 0; 	} combo DA_288 { 	set_val(DA_2449,0); 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( random(210, 215) + DA_2430); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function DA_2254() { 	if (DA_2555 > 600 && DA_2555 <= 800) { 		DA_21246 = 240; 			} 	if (DA_2555 > 800 && DA_2555 <= 1000) { 		DA_21246 = 230; 			} 	if (DA_2555 > 1000 && DA_2555 <= 1500) { 		DA_21246 = 225; 			} 	if (DA_2555 > 1500 && DA_2555 <= 2000) { 		DA_21246 = 235; 			} 	if (DA_2555 > 2000) { 		DA_21246 = 218; 			} 	combo_run(DA_297); 	} combo DA_289 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 500); 	} combo DA_290 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( 205); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 300); 	} combo DA_291 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( 190); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 400); 	} int DA_21251; int DA_21252; int DA_229; int DA_2476; int DA_2474; int DA_21256; int DA_21257; combo DA_292 { 	if (DA_21252) { 		set_val(DA_2447, 0); 		DA_21256 = 350; 			} 	else { 		DA_21256 = 0; 			} 	if (DA_21252) { 		DA_2243(); 		DA_2239(0, 0); 			} 	vm_tctrl(0); 	wait(DA_21256); 	if (DA_21252) { 		set_val(DA_2450, 0); 		DA_21256 = 60; 			} 	else { 		DA_21256 = 0; 			} 	set_val(DA_2447,0); 	vm_tctrl(0);wait(DA_21256); 	set_val(DA_2450, 0); 	set_val(DA_2447,0); 	vm_tctrl(0);wait(DA_21256); 	if (DA_21252) { 		DA_21256 = 0; 			} 	else { 		DA_21256 = 0; 			} 	set_val(DA_2450, 0); 	set_val(DA_2451, 100); 	set_val(DA_2447,0); 	vm_tctrl(0); 	wait(DA_21256); 	set_val(DA_2447, 100); 	set_val(DA_2451, 100); 	if (!DA_21252)set_val(DA_2452, 100); 	vm_tctrl(0);wait(random(275, 280) +   DA_2429 ); 	set_val(DA_2451, 100); 	set_val(DA_2447, 0); 	if (DA_21252) { 		DA_21256 = 15; 			} 	else { 		DA_21256 = 70; 			} 	vm_tctrl(0);wait(random(0,2) + DA_21270 + DA_21269 + DA_21256 ); 	set_val(DA_2451, 100); 	set_val(DA_2447, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA_2447, 0); 	set_val(DA_2451, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(DA_2451, 100); 	vm_tctrl(0);wait(2500); 	} int DA_21196; int DA_21199; int DA_2555; int DA_21195; int DA_21269; int DA_21270 = 111; int DA_21189; int DA_21200; int DA_21273; function DA_2255() { 	DA_21273 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DA_21273 > 5 && DA_21273 < 40) ) {         DA_2479 = FALSE; 			} 	if ((DA_21273 > 40 && DA_21273 < 85)){ 		DA_2479 = TRUE; 		    } 	if ((DA_21273 > 95 && DA_21273 < 130) ) { 		DA_2479 = FALSE; 		    } 	 if((DA_21273 > 130 && DA_21273 < 175)) { 		DA_2479 = TRUE; 		} 			if ((DA_21273 > 185 && DA_21273 < 220) ) {        DA_2479 = FALSE; 			} 	if ((DA_21273 > 220 && DA_21273 < 265)){        DA_2479 = TRUE; 		    } 	if ((DA_21273 > 275 && DA_21273 < 310) ) {        DA_2479 = FALSE; 		    } 	 if((DA_21273 > 310 && DA_21273 < 355)) { 		DA_2479 = TRUE; 		} 	if (DA_21199 == 0 && (DA_2555 >= 750 || ((DA_21200 > 3000 && DA_21195 > 1 && DA_21195 < 5000)))) { 		if (DA_2555 <= 2000 && DA_21195 > 1500) { 			set_val(DA_2447, 0); 			DA_21270 = 170; 		} 		if (DA_2555 <= 2000 && DA_21195 > 1 && DA_21195 <= 1500) { 			set_val(DA_2447, 0); 			DA_21270 = 202; 					} 		if (DA_2555 > 2000 || (DA_21195 > 1 && DA_21195 <= 1500)) { 			set_val(DA_2447, 0); 			DA_21270 = 151; 					} 		if ((DA_2555 > 2000 && DA_21195 > 1500) || DA_21200 > 1 && DA_21195 > 1) { 			set_val(DA_2447, 0); 			DA_21270 = 152; 					} 		if ((DA_2555 < 2000 && DA_21195 > 1500) || DA_21199 > 1 && DA_21195 > 1) { 			set_val(DA_2447, 0); 			DA_21270 = 149; 					} 		if (DA_21195 > 1500) { 			set_val(DA_2447, 0); 			DA_21270 = 148; 					} 		if (!DA_2555 > 2000 && DA_21200 > 1 && DA_21195 > 1 && DA_21195 <= 1500) { 			set_val(DA_2447, 0); 			DA_21270 = 147; 					} 		set_val(DA_2447, 0); 		combo_stop(DA_297); 		combo_stop(DA_298); 		combo_stop(DA_295); 		combo_stop(DA_288); 		combo_stop(DA_294); 		combo_stop(DA_291); 		combo_stop(DA_290); 		combo_run(DA_292); 			} 	else { 		if (DA_21199) { 			set_val(DA_2447, 0); 			combo_run(DA_293); 					} 		else { 			if (DA_2555 < 750) { 				set_val(DA_2447, 0); 				combo_run(DA_294); 							} 					} 			} } combo DA_293 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo DA_294 { 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( random(200, 205) + DA_2429 )  set_val(DA_2447, 0); 	vm_tctrl(0);wait( 700); 	} int DA_21283 = 246; int DA_21213 = 150; int DA_21285 = 0; combo DA_295 { 	set_val(DA_2451, 100); 	set_val(DA_2450, 0); 	set_val(DA_2447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA_2451, 0); 	set_val(DA_2450, 0); 	set_val(DA_2447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	DA_21285 = DA_2428; 	set_val(DA_2450, 100); 	set_val(DA_2447,0); 	vm_tctrl(0);wait( 60); 	set_val(DA_2450, 100); 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( DA_21283 + 10 + random(-2, 2) +  DA_2432); 	set_val(DA_2450, 100); 	set_val(DA_2447, 0); 	DA_21251 = DA_21213; 	vm_tctrl(0);wait( DA_21213 + DA_21285 - 33 ); 	set_val(DA_2450, 100); 	if(DA_21245)set_val(DA_2447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA_2450, 100); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 3000); 	} combo DA_296 { 	set_val(DA_2450, 100); 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( 160 + DA_2432 ); 	set_val(DA_2450, 100); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 3000); 	} int DA_21208; int DA_21287 = 220; int DA_21246; int DA_21289 = 0; combo DA_297 { 	DA_21289 = DA_2428; 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( DA_21287 + DA_2430); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( DA_21246 + (DA_21289) ); 	if(DA_21245)set_val(DA_2447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA_2447, 0); 	vm_tctrl(0);wait( 2000); 	} int DA_21291 = TRUE; int DA_21217; int DA_21293 = 315; int DA_21294 = 0; combo DA_298 { 	set_val(DA_2449, 100); 	set_val(DA_2450, 100); 	if (DA_21291) { 		DA_21294 = DA_2428; 			} 	else { 		DA_21294 = 0   	} 	set_val(DA_2447, 100); 	vm_tctrl(0);wait( DA_21293); 	set_val(DA_2447,0); 	vm_tctrl(0);wait( DA_21217 + DA_21294 + 40)  	set_val(DA_2447,0); 	vm_tctrl(0);wait(200); } int DA_21297; int DA_21298 = 145; int DA_2651; int DA_2650; int DA_2653; int DA_2652; combo DA_299 { 	set_val(DA_2454, 0); 	vm_tctrl(0);wait( 30); 	set_val(DA_2454, 100); 	vm_tctrl(0);wait( 60); 	} int DA_2654; int DA_2655; combo DA_2100 { 	set_val(DA_2453, 100); 	vm_tctrl(0);wait( DA_2655); 	} define DA_21305 = TRUE; define DA_21306 = 95; define DA_21307 = 10; define DA_21308 = 70; define DA_21309 = FALSE; define DA_21310 = 50; define DA_21311 = 95; define DA_21312 = XB1_LT; define DA_21313 = XB1_RT; define DA_21314 = XB1_LX; define DA_21315 = XB1_LY; define DA_21316 = POLAR_LS; int DA_21317; function DA_2256() { 	if ( get_ival(DA_2452) > 30 &&    (get_ival(DA_2451) || get_ival(DA_2448)) &&    (!get_ival(DA_2453) || !get_ival(DA_2447))  ) { set_val(DA_2452, 0); 		if(!get_ival(DA_2447)){ 			set_val(DA_2451,100); 					} 		else{ 			set_val(DA_2451,0); 			set_val(DA_2452,100); 					} 		  combo_run(DA_2102); 		  if(DA_2431 == TRUE){ 		  if(!get_ival(DA_2450)){combo_run(DA_2101);} 		  } 			} 	else { 		combo_stop(DA_2102); 		combo_stop(DA_2101); 			} 	} combo DA_2101 { set_val(DA_2450, 100); vm_tctrl(0); wait(45); set_val(DA_2450,0); vm_tctrl(0); wait(200); 	} combo DA_2102 { set_val(DA_2452, 0); vm_tctrl(0); wait(35); 	if(!get_ival(DA_2447)){ 		set_val(DA_2451,100); 			} 	else{ 		set_val(DA_2451,0); 			} 	set_val(DA_2452, 100); 	vm_tctrl(0);wait(DA_2600); 	if(!get_ival(DA_2447)){ 		set_val(DA_2451,100); 			} 	else{ 		set_val(DA_2451,0); 			}     set_val(DA_2452, 0); 	wait(1100); 	} int DA_21318; int DA_21319 ; function DA_2257(DA_2240) { return DA_2259(DA_2240 + 8192); } function DA_2259(DA_2240) {   DA_2240 = (DA_2240 % 32767) << 17;   if((DA_2240 ^ (DA_2240 * 2)) < 0) { DA_2240 = (-2147483648) - DA_2240; }   DA_2240 = DA_2240 >> 17;   return DA_2240 * ((98304) - (DA_2240 * DA_2240) >> 11) >> 14; } int DA_21322, DA_21323; function DA_2261(DA_2121, DA_2122, DA_2123, DA_2265){   DA_2265 = (DA_2265 * 32767) / 100;   DA_2123 = (DA_2123 * 32767) / 10000;   DA_2122 = (360 - DA_2122) * 91;   DA_21323 = DA_2259(DA_2122); DA_21322 = DA_2257(DA_2122);   DA_2122 = 32767 - DA_2257(abs(abs(DA_21323) - abs(DA_21322)));   DA_2123 = DA_2123 * (32767 - ((DA_2122 * DA_2265) >> 15)) >> 15;   set_val(42 + DA_2121, clamp((DA_2123 * DA_21322) >> 15, -32767, 32767));   set_val(43 + DA_2121, clamp((DA_2123 * DA_21323) >> 15, -32767, 32767));   return; } int DA_21328, DA_21329; int DA_21330 = TRUE; int DA_21331; int DA_21332; int DA_21333; int DA_21334; int DA_21335; function DA_2266() {    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(DA_2451) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(DA_2104)     && !get_ival(DA_2449) &&  !get_ival(DA_2452) && !get_ival(DA_2448) && !get_ival(DA_2450) && !get_ival(DA_2454) && !get_ival(DA_2447)){        combo_run(DA_2103);    }else{    combo_stop(DA_2103);    } } int DA_21336; int DA_21337; function DA_2267(){   stickize(POLAR_LX, POLAR_LY, 32767);   DA_21328 = get_ipolar(POLAR_LS, POLAR_RADIUS);   DA_21329 = get_ipolar(POLAR_LS, POLAR_ANGLE);   DA_2261(POLAR_LS,  DA_21329,  DA_21328, DA_2455 + random(1,7));   } combo DA_2103 {  vm_tctrl(0); wait(400); DA_2267(); vm_tctrl(0); wait(500); 	} combo DA_2104 { }  combo DA_2105 { } combo DA_2106{ } combo DA_2107 { 	set_val(DA_2448,100); 	vm_tctrl(0);wait( DA_2651); 	set_val(DA_2448,  0); 	vm_tctrl(0);wait( 30); 	if(DA_2401){ 		set_val(DA_2450,100); 			} 	vm_tctrl(0);wait( 60); 	} combo DA_2108 { 	set_val(DA_2450,  100); 	vm_tctrl(0);wait( 60);     wait( 60);     vm_tctrl(0);wait( 600);     set_val(DA_2451,  100);     wait(60);     wait(60); 	} combo DA_2109 { 	set_val(DA_2454,100); 	vm_tctrl(0);wait( DA_2653); 	set_val(DA_2454,  0); 	vm_tctrl(0);wait( 30); 	if(DA_2398){ 		set_val(DA_2454,100); 			} 	vm_tctrl(0);wait( 60); 	} int DA_21001 int DA_21342 combo DA_2110 { 	combo_suspend(DA_2110) 	vm_tctrl(0);wait(361) } function DA_2268 (){ 	if(DA_21001[DA_21342] != 361){ 	    DA_21342-- 	} 	else{ 		if(inv(DA_21342) != 297){ 			DA_2268(); 		} 	} } int DA_21345; combo DA_2111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int DA_2176; function DA_2269(DA_2270, DA_2271, DA_2272) {   DA_2176 = get_ipolar(DA_2270, POLAR_RADIUS);   if(DA_2271) {     if(DA_2176 <= DA_2271) DA_2176 = (DA_2176 * 5000) / DA_2271;     else DA_2176 = ((5000 * (DA_2176 - DA_2271)) / (10000 - DA_2271)) + 5000;   }   if (DA_2272) DA_2176 = (DA_2176 * DA_2272) / 10000;   set_polar2(DA_2270, get_ipolar(DA_2270, POLAR_ANGLE), min(DA_2176, 14142));   if (DA_2270 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; } combo DA_2112{ vm_tctrl(-7); wait(1000); }  