int led_value_base;
define Off = 0;
define Dim_Blue = 1;
define Dim_Red = 2;
define Dim_Green = 3;
define Dim_Pink = 4;
define Dim_SkyBlue = 5;
define Dim_Yellow = 6;
define Dim_White = 7;
define Blue = 8;
define Red = 9;
define Green = 10;
define Pink = 11;
define SkyBlue = 12;
define Yellow = 13;
define White = 14;
define Bright_Blue = 15;
define Bright_Red = 16;
define Bright_Green = 17;
define Bright_Pink = 18;
define Bright_SkyBlue = 19;
define Bright_Yellow = 20;
define Bright_White = 21;

define Stick_Press_LB_Threshold = 95;
int stick_swap_toggle;
int rightStickMagnitude;
int UltimatePower;
int DYN_Acc;

function icos(x) { return isin(x + 8192); }
function isin(x) {
    x = (x % 32767) << 17;
    if ((x ^ (x * 2)) < 0) { x = (-2147483648) - x; }
    x = x >> 17;
    return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggle_active;
int cos_angle, sin_angle;
int multiplier = 18; // Increase the frequency of peaks and dips
function set_polar_dd(stick, angle, radius, dd_factor) {
    dd_factor = (dd_factor * 32767) / 80;
    radius = (radius * 32767) / 10000;
    angle = (360 - angle) * 91;

    sin_angle = isin(angle); 
    cos_angle = icos(angle);

    angle = (angle * multiplier) % 32767;

    // Adjusted radius calculation for more peaks and dips
    radius = radius * (32767 - ((angle * dd_factor) >> 15)) >> 15;

    set_val(42 + stick, clamp((radius * cos_angle) >> 15, -32767, 32767));
    set_val(43 + stick, clamp((radius * sin_angle) >> 15, -32767, 32767));

    return;
}

int r, a, dd;

main {
    vm_tctrl(-9);

    // Toggle functionality using LT and UP
    if (get_val(XB1_LT)) {
        if (event_press(XB1_UP)) {
            toggle_active = !toggle_active;  // Toggle the active state
            combo_run(Feedback);  // Run feedback for interaction
        }
    }   

    // Real-time 'dd' value adjustment
    // Increase 'dd' with RIGHT button press
    if (event_press(XB1_RIGHT)) {
        dd = min(dd + 5, 100);  // Increment 'dd', max limit 100
        set_ds4_led(calculate_color_based_on_dd(dd)); // Update LED based on 'dd'
    }
    // Decrease 'dd' with LEFT button press
    if (event_press(XB1_LEFT)) {
        dd = max(dd - 5, -50);  // Decrement 'dd', min limit -50
        set_ds4_led(calculate_color_based_on_dd(dd)); // Update LED based on 'dd'
    }

    // Apply 'dd' changes only if toggle is active
    if (toggle_active) {
        stickize(POLAR_LX, POLAR_LY, 32767);
        r = get_polar(POLAR_LS, POLAR_RADIUS);
        a = get_polar(POLAR_LS, POLAR_ANGLE);
        set_polar_dd(POLAR_LS, a, r, dd);
    }

    // Additional logic and combos...
}

// Calculate LED color based on 'dd' value
function calculate_color_based_on_dd(int dd) {
    if (dd > 50) return Bright_White;
    else if (dd > 0) return Dim_White;
    return Off; // Ensures all paths have a return value, covering cases where dd <= 0
}


// Optional: A combo to provide some form of feedback that the toggle has occurred
combo Feedback {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(400);
    reset_rumble();
}

combo Feedback2 {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    set_rumble(RUMBLE_A, 0);  // Vibrate controller briefly
    wait(100);
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    reset_rumble();
}

combo banks {
	set_val(XB1_A, 100);
	combo_run(pressing);
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

combo finesse {
	set_val(XB1_B, 100);
	wait(260);
	set_val(XB1_B, 0);
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(440);
	set_val(XB1_LB, 100);
	wait(80);
	set_val(XB1_LB, 0);
	wait(40);
	set_val(XB1_LB, 100);
	wait(250);
	set_val(XB1_LB, 0);
	wait(250);  
}

combo CHIP_SHOT {
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	set_val(PS4_L3,100);
	wait( 80);
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	wait(100);    
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

combo Press_LB {
	set_val(XB1_LB, 100);
	set_val(XB1_RB, 100);
	wait(100);
	wait(20);
	set_val(XB1_A, 100);
	wait(200);
	wait(20);
}

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function set_ds4_led(colour) {
    led_value_base = colour * 4; // Pre-calculate base LED value

    // Check if the base value for LED is out of range
    if (led_value_base < 0 || led_value_base > 251) {
        led_value_base = 0; // Reset to 0 if out of expected byte range
    }

    // Assign values to LEDs ensuring they stay within the 0-255 range
    set_led(LED_1, duint8(led_value_base));
    set_led(LED_2, duint8(led_value_base + 1));
    set_led(LED_3, duint8(led_value_base + 2));
    set_led(LED_4, duint8(led_value_base + 3));
}




 