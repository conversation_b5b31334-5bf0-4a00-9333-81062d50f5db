# Gtuner Computer Vision Functions

gcv_ready
gcv_ready - Check for GCV data updates

Description
bool gcv_ready();
Check if the feedback data from Gtuner's Computer Vision script was updated.

GCV scripts run on Gtuner IV and have as main purpose analyze video frames, such as game streaming, by means of computer vision algorithms. The GCV script can feed data to Titan Two scripts for automatic configurations and/or to trigger automated actions.
Return Value
Returns true if the GCV feedback data was updated, FALSE otherwise.

Examples
Example #1 gcv_ready() example
uint8 weapon_model;
int32 horz_delta;
uint16 shots_fired;
 
main {
    // GCV feedback data was updated
    if(gcv_ready()) {
        // Read the GCV feedback data into variables.
        weapon_model = gcv_read(0);
        gcv_read(1, &horz_delta);
        gcv_read(5, &shots_fired);
    }
}

gcv_read
gcv_read — Read from GCV feedback data

Description
Variation 1
void gcv_read(uint8 offset, <anytype> *variable);
Update the variable pointed by *variable with the value of type and size defined by <anytype>, located in the position offset from the GCV feedback data array.

Variation 2
uint8 gcv_read(uint8 offset);
Read an uint8 value from position offset of the GCV feedback data array.

Parameters
offset: Position of the persistent memory array, starting from 0, from which a value should be read. Note: on Variation 2 the offset is passed as direct parameter of the opcode, therefore stack values can't be used.
<anytype> *variable: Pointer to an variable of type <anytype>.
<anytype> can be: int8, uint8, int16, uint16, int32, uint32, fix32 or any of its aliases.
Byte Size of Variable Types
int8, uint8	1 byte
int16, uint16	2 bytes	big-endian
int32, uint32, fix32	4 bytes	big-endian
Return Value
Variation 1 does not returns any value.
Variation 2 returns an uint8 value from the position offset.
GCV Feedback Data Array
The GCV feedback data is an array of a maximum of 255 bytes utilized by the Gtuner's Computer Vision script to provide information garnered from video frames (e.g. game streaming) by means of computer vision algorithms. The feedback data is utilized by Titan Two scripts for automatic configurations and/or to trigger automated actions.

Examples
Example #1 gcv_read() example
uint8 weapon_model;
int32 horz_delta;
uint16 shots_fired;
 
main {
    // GCV feedback data was updated
    if(gcv_ready()) {
        // Read the GCV feedback data into variables.
        weapon_model = gcv_read(0);
        gcv_read(1, &horz_delta);
        gcv_read(5, &shots_fired);
    }
}