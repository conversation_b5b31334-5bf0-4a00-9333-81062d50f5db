define AngleInterval = 16;
define AngleInterval_2 = 8;

// Variables for oscillation
int AI_VALUES[5];
int AI_VALUES_COUNT;
int current_index = 0;
int ptime;
int debug_radius;
int debug_angle;
int oscillated_angle;

// Initialize oscillation values
init {
    AI_VALUES[0] = 45;
    AI_VALUES[1] = 8;
    AI_VALUES[2] = 90;
    AI_VALUES[3] = 8;
    AI_VALUES[4] = 45;
    AI_VALUES_COUNT = sizeof(AI_VALUES) / sizeof(AI_VALUES[0]);
    ptime = 0;
}

main {
    debug_radius = get_polar(POLAR_LS, POLAR_RADIUS);
    debug_angle = get_polar(POLAR_LS, POLAR_ANGLE);

    // Condition 1: when radius < 2400
    if (debug_radius < 2400) {
        set_polar(POLAR_LS, 0, 0);
    }
    // Condition 2: when radius > 2400 but < 6000
    else if (debug_radius >= 2400 && debug_radius < 6000) {
        set_polar(POLAR_LS, 
            (((inv(debug_angle) * AngleInterval) / 360) * 360) / AngleInterval, 
            min(isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)), 31111)
        );
    }
    // Condition 3: when radius >= 6000
    else if (debug_radius >= 6000) {
        // Update oscillation index every 10ms
        if (get_rtime() - ptime >= 10) {
            current_index = (current_index + 1) % AI_VALUES_COUNT;
            ptime = get_rtime();
        }
        
        set_polar(POLAR_LS, 
            (((inv(debug_angle) * AI_VALUES[current_index]) / 360) * 360) / AI_VALUES[current_index], 
            min(isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)), 31111)
        );
    }
}