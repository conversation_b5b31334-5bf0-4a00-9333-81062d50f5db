// The lookup table defines the maximum allowed radius (in percent of 10000)
// for each degree within a 45° segment. (For example, index 0 corresponds
// to 0° within the segment and index 44 to 44°; note that for an angle
// that is exactly a multiple of 45 the modulus returns 0.)
//
// The numbers were precomputed so that at 0° (and 45°) the allowed radius is 100%,
// and near the middle of the edge (around 22°) the allowed radius drops (to around 92%).
// This creates an octagonal envelope – the joystick (or other polar control) can
// reach the full maximum only along the eight principal directions.
const int8 octagonMax[] = {
  100, 98, 97, 96, 94, 94, 92, 92, 91, 90,
  90, 91, 92, 93, 94, 95, 96, 97, 98, 98,
  99, 100, 100, 98, 97, 96, 94, 94, 92, 92,
  91, 90, 90, 91, 92, 93, 94, 95, 96, 96,
  97, 98, 98, 99, 100
};

// In this example the full maximum radius is 10000.
// (That is, a lookup value of 100 means 10000, 98 means 9800, etc.)
int SCALE = 100;  // Multiplier to get from percent to full scale (100 -> 10000)
int SCALE;
int radius, angle;
int index;
int maxAllowed;
int newRadius;

main {
  // Get the current polar coordinate (angle in degrees and radius)
  angle = get_ipolar(POLAR_LS, POLAR_ANGLE);    // angle is assumed to be in degrees (0–359)
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);    // radius is in the same scale (e.g., 0–10000)

  // Determine which part of the 45° segment we’re in.
  // (An octagon’s envelope is defined in 45° segments, with symmetry between them.)
  index = angle % 45; 

  // Look up the allowed maximum for this angle.
  // Multiply the table value (a percentage) by SCALE to get the allowed maximum.
  maxAllowed = SCALE * octagonMax[index];

  // Limit the current radius so that it does not exceed the octagon edge.
  // (If the current radius is greater than the allowed maximum, it is clipped.)
  newRadius = radius;
  if (radius < maxAllowed)
    newRadius = radius;
  else
    newRadius = maxAllowed;

  // Update the polar coordinate with the (possibly clipped) radius.
  set_polar2(POLAR_LS, angle, newRadius);
}