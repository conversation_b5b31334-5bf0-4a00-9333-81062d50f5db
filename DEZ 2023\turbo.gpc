/* ********************* GPC FILE RECOVERED ********************* */

int turboBtn;

main {

    if (get_val(XB1_A) && get_ptime(XB1_A) >= 30) {
        turboBtn = XB1_A;
        combo_run(TURBO);
        //combo_run(TURBO2);
    }
    /*
    if (get_val(XB1_LT) && get_val(XB1_RT) && get_ptime(XB1_RT) >= 300) {
        turboBtn = XB1_A;
        combo_run(TURBO);
        //combo_run(TURBO2);
    }
    */
}

combo TURBO {
    set_val(turboBtn, 100);
    wait(5);
    set_val(turboBtn, 0);
    wait(200);
}

combo TURBO2 {
    set_val(XB1_LB, 100);
    wait(600);
    set_val(XB1_LB, 0);
    wait(100);
} 