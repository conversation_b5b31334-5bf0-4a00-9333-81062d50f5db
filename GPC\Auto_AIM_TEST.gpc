define GEN_SENS = 90;  // Sensitivity while aiming - Default: 100 - Range [0 ~ 327]
						// set a number below 100 for less sens - above 100 for more sens

define MIDPOINT = 50;   /** MIDPOINT - Default: 50 - Range [0 ~ 100]
    If you set a number below 50 you are defining a zone of high sensitivity range when close to rest position 
    (fast movements) and a low sensitivity range when far from rest position (better accuracy).
    if you set a number above 50 you are defining a zone of low sensitivity when close to the rest position
    (better accuracy), and a zone of high sensitivity when far from rest position (fast movements).

    **/
                               
define PaceCtrol     = XB360_LT; // Pace Control
define FinesseShot   = XB360_LB; // Finesse Shot
define PlayerRun     = XB360_RB; // Player Run  
define ShotBtn       = XB360_B; // Shot Btn  
define SprintBtn     = XB360_RT; // Sprint Btn 
int LX,LY;
int onoff_FK;
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;  

int shootpower = 340;


main {

	if(get_val(XB1_B) && get_ptime(XB1_B)>= shootpower){
		set_val(XB1_B,  0);													  
	}

     if (get_val(XB1_PL1))	{set_val(PS4_LX,-80);set_val(PS4_LY,-50);} 	// Left Up 
	if (get_val(XB1_PR1))	{set_val(PS4_LX,80);set_val(PS4_LY,-50);} 	// Right Up 

    // ADS SENS
	sensitivity(PS4_LX, MIDPOINT, GEN_SENS);        
	sensitivity(PS4_LY, MIDPOINT, GEN_SENS); 


    if(!get_val(PS4_R2)){
        LX = get_val(PS4_LX);
        LY = get_val(PS4_LY);
        
    }else if(get_val(PS4_R2)){
 
        // Fine tune Aim                         
        if(press_hold(PS4_LEFT)) {               
            LX = LX - 1;
        }
        if(press_hold(PS4_RIGHT)){
            LX = LX + 1;
        }
        if(press_hold(PS4_UP)){
            LY = LY - 1;
        }
        if(press_hold(PS4_DOWN)){
            LY = LY + 1;
        }
        set_val(PS4_LX, LX );
        set_val(PS4_LY, LY );
    }
    set_val(PS4_UP,0);
    set_val(PS4_RIGHT,0);
    set_val(PS4_LEFT,0);
    set_val(PS4_DOWN,0);
   

set_val(PS4_R2,0);

}


combo SHOT_POWER {
	set_val(ShotBtn,100);
	wait(shootpower);
	wait(300);  
}

function press_hold(f_btn) {   
    return event_press(f_btn) || get_val(f_btn) && get_ptime(f_btn) > 250
           && get_ptime(f_btn) % (get_rtime() * 8) == 0;  
}
