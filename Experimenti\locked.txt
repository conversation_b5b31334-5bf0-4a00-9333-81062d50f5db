

//rewsr





/*
████████████████████████████████████████████████████████████████████████████████

████████████████████████████████████████████████████████████████████████████████

███████████████▀███▀██████████████▀▀▀▀█████████████████▀"███████████████████████

██████████████▌  ▌   ████████████      ╟███████████▀ ,▄▄ ▄▄,'▀██████████████████

██████████████▌  ▌   ████████████      ▄▄▄▄,,,  -",██████████▄ ▀████████████████

██████████████▌  ▌   ████████████    ▄██████████ ╒████▀▀▀▀█████ ▐███████████████

██████████████▌,▄▌   ████████████   █  ▓███████" ╙███▌]▀▄▀ ████` "██████████████

███████████████▀-    ████████████   █  ╟████████ ▐████,▀ⁿ▀▄████C ███████████████

██████████████▌      ████████████   █  ╟████████▄ ▀███████████▀ ████████████████

██████████████▌      ████████████   █  ╟██████████▄ ▀███▀██▀▀ ▄, ▀██████████████

██████████████▌      ████████████   █  ╟███▀¬ ,, `▀██▄▄  ,▄▄████▄ ▀█████████████

███████████████▄                    "▀████  ██████ ╙█████████████▌ ▀████████████

█████████████████▄,                   `▀█▀ ▐██████C ██████████████▄ ████████████

████████████████████▄                  ▄▄▄▄ ▀▀██▀╙ ▄███████████████  ███████████

████████████████████████████████████████████▄▄▄▄▄▄▄ ▀██████████████▌ ███████████

████████████████████████████████████████████████████, ▀████████████▌ ▐██████████

██████████████████████████████████████████████████████, ▀██████████▌ ▐██████████

████████████████████████████████████████████████████████▄ '▀███████" ███████████

███████████████████████████████████████████████████████████▄ "▀▀██▀ ▄███████████

██████████████████████████████████████████████████████████████▄▄▄▄▄█████████████

████████████████████████████████████████████████████████████████████████████████
*/

/*
  $$\   $$\         $$$$$$\                   $$\          $$\                      
  $$ |  $$ |       $$  __$$\                  \__|         $$ |                     
  $$ |  $$ |$$$$$$\$$ /  \__|$$$$$$$\ $$$$$$\ $$\ $$$$$$\$$$$$$\                    
  $$ |  $$ $$  __$$\$$$$$$\ $$  _____$$  __$$\$$ $$  __$$\_$$  _|                   
  $$ |  $$ $$ |  \__\____$$\$$ /     $$ |  \__$$ $$ /  $$ |$$ |                     
  $$ |  $$ $$ |    $$\   $$ $$ |     $$ |     $$ $$ |  $$ |$$ |$$\                  
  \$$$$$$  $$ |    \$$$$$$  \$$$$$$$\$$ |     $$ $$$$$$$  |\$$$$  |                 
   \______/\__|     \______/ \_______\__|     \__$$  ____/  \____/                  
                                                 $$ |                               
//                                                 $$ |                               
//                                                 \__|                               
//   $$$$$$\ $$$$$$$\  $$$$$$\  $$$$$$\  $$$$$$\  $$$$$$\  $$$$$$\  $$$$$$\ $$$$$$$\  
//  $$$ __$$\$$  ____|$$  __$$\$$  __$$\$$  __$$\$$  __$$\$$  __$$\$$  __$$\$$  ____| 
//  $$$$\ $$ $$ |     $$ /  $$ $$ /  \__$$ /  $$ $$ /  $$ $$ /  $$ \__/  $$ $$ |      
//  $$\$$\$$ $$$$$$$\ \$$$$$$$ $$$$$$$\  $$$$$$  \$$$$$$$ |$$$$$$  |$$$$$$  $$$$$$$\  
//  $$ \$$$$ \_____$$\ \____$$ $$  __$$\$$  __$$< \____$$ $$  __$$<$$  ____/\_____$$\ 
//  $$ |\$$$ $$\   $$ $$\   $$ $$ /  $$ $$ /  $$ $$\   $$ $$ /  $$ $$ |     $$\   $$ |
//  \$$$$$$  \$$$$$$  \$$$$$$  |$$$$$$  \$$$$$$  \$$$$$$  \$$$$$$  $$$$$$$$\\$$$$$$  |
//   \______/ \______/ \______/ \______/ \______/ \______/ \______/\________|\______/ 
//                                                                                    
//                                                                                    
//                                                                                                               
//                                                                                    
/   _   _        _____              _         _                   
//  | | | |      /  ___|            (_)       | |                  
//  | | | | _ __ \ `--.   ___  _ __  _  _ __  | |_                 
//  | | | || '__| `--. \ / __|| '__|| || '_ \ | __|                
//  | |_| || |   /\__/ /| (__ | |   | || |_) || |_                 
//   \___/ |_|   \____/  \___||_|   |_|| .__/  \__|                
//                                     | |                         
//                                     |_|                         
//   _____  _____  _____   ____  _____  _____  _____  _____  _____ 
//  |  _  ||  ___||  _  | / ___||  _  ||  _  ||  _  |/ __  \|  ___|
//  | |/' ||___ \ | |_| |/ /___  \ V / | |_| | \ V / `' / /'|___ \ 
//  |  /| |    \ \\____ || ___ \ / _ \ \____ | / _ \   / /      \ \
//  \ |_/ //\__/ /.___/ /| \_/ || |_| |.___/ /| |_| |./ /___/\__/ /
//   \___/ \____/ \____/ \_____/\_____/\____/ \_____/\_____/\____/ 
//                                                                 

*/
/*
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@@&&5?B&&@&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&^ ^#^  ^&&&&&&&&&&&&&&&~      :&@@@@@@&&&&&&@&BJ~::  .:^!5#&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#   G    #&&&&&&&&&&&&&&        ^!!7?JY5PGB#&5..~YG#57G#P?:.^G&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#   B    #&&&&&&&&&&&&&&       ~??77!~^::...  ?&@@&@@@@@&@&B~ !&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#   G    #&&&&&&&&&&&&&&     !B@@@@@@@@&&&#  B@&&&&5J?JG&&&&@Y ~@&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#  .B    #&&&&&&&&&&&&&&    P7.^&&&&&&&&&B~ ^#&&&#.~!!7:^&&&&G. J#&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&#P5~    #&&&&&&&&&&&&&&    B   B&&&&&&&&J. .P&&&G J~!?7 &&&&?  .G&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&G~      #&&&&&&&&&&&&&&    G.  B&&&&&&&&@B :&&&&&P~~~~!B&&&@# .&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#        #&&&&&&&&&&&&&&    G.  B&&&&&&&&&&Y :#@&&&&&&&&&&@&G. B@&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#        #&&&&&&&&&&&&&&    G.  B&&&&&&@@@@@B: ~P#&&#B&&&#Y: : :G&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&#        #@@@@@@@@@@@@@&    G   B&&&@&P7~^~7P&B7:.:~. :^:.^J#&G. J&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&.       !5YYYYYYYYYYY5!    G~ .#&&&J  ~?YJ~. 7&&#BP~.7GB&&@&&@#. Y@&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&Y.                        .?B&&&@7 ^&@@@@@&! ~&&&&&&&&&&&&&&&&#. 5@&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&@&P^                        .?B#B. P@&&&&&@B  &&&&&&&&&&&&&&&&@G  #&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&@&G~                        .... .P&&@&&G: J&&&&&&&&&&&&&&&&&@7 ~&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&BYJJJJJJJJJJJJJJJJJJJJJP&&&&G^ .^~^.   Y&&&&&&&&&&&&&&&&&&#  B&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&@@@@@@@@@@@@@@@@@@@@@@&&&&&&&#GYJYP##~ .P&&&&&&&&&&&&&&&&@^ ?@&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@@@&&&&G: :G&&&&&&&&&&&&&&@? ~@&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&P: ^G&@&&&&&&&&&&@? ~@&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&P: .J#&@&&&&&&&@^ J@&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&B7. ^Y#&@@&@@P  #&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@&G!. :!YP5! .G&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@&BY!^::~Y&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&@@&&&@@&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
 */
 
 
 /*
 //            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 

//            !!يمنع منعا باتا النزول للاسفل ان تعطل جهازك او خرب السكربت المتجر غير مسؤول ابدا ولن يتم تعويضك 
*/
        




















































































































































int XBOX1_CONTROLLER_HOLD509548_271511 = 0;
 int PS5_R3_UP = 0;
 int ps5_moves;
 int XBOX1_CONTROLLER_HOLD312585_309749;
 int XBOX1_CONTROLLER_HOLD243440_708731;
 int PS5_R3_RIGHT;
 int PS5_R3_DOWN;
 int A_PRESS_XBOX_BUTTON = 117;
int B_PRESS_XBOX_BUTTON = 912;
int X_PRESS_XBOX_BUTTON = 3;
 int XBOX1_CONTROLLER_HOLD949727_880402[6]; int XBOX1_CONTROLLER_HOLD553355_535494 = 0;
 int XBOX1_CONTROLLER_HOLD148058_514103 = 0;
 int XBOX1_CONTROLLER_HOLD615209_524043[0x5]; int XBOX1_CONTROLLER_HOLD687228_876037, displayBufferInsertCopyValue, displayBufferInsertNumberOfDigits;
 int XBOX1_CONTROLLER_HOLD572591_984177 = 0;
 int XBOX1_CONTROLLER_HOLD258112_738204 = 0;
 int XBOX1_CONTROLLER_HOLD363187_641519 = 0;
 define LowDrivenBtn = XB1_RS;
 define MOVE_X = PS4_LX;
 define MOVE_Y = PS4_LY;
 define SKILL_STICK_X = PS4_RX;
 define SKILL_STICK_Y = PS4_RY;
 define FAKE_SHOT_SKILL = 1;
 define HEEL_TO_HEEL_FLICK_SKILL = 2;
 define HEEL_FLICK_TURN_SKILL = 3;
 define RAINBOW_SKILL = 4;
 define DRAG_BACK_SOMBRERO_SKILL = 5;
 define FAKE_PASS_SKILL = 6;
 define DRAG_BACK_UNIVERSAL_SKILL = 7;
 define STEP_OVER_FEINT_SKILL = 8;
 define DRAG_TO_DRAG_SKILL = 9;
 define HOCUS_POCUS_SKILL =10;
 define TRIPLE_ELASTICO_SKILL =11;
 define ELASTICO_SKILL =12;
 define REVERSE_ELASTICO_SKILL =13;
 define CRUYFF_TURN_SKILL =14;
 define LA_CROQUETA_SKILL =15;
 define RONALDO_CHOP_SKILL =16;
 define ROULETTE_SKILL =17;
 define FLAIR_ROULETTE_SKILL =18;
 define BALL_ROLL_SKILL =19;
 define BERBA_MCGEADY_SPIN_SKILL =20;
 define BOLASIE_FLICK_SKILL =21;
 define TORNADO_SKILL =22;
 define THREE_TOUCH_ROULETTE_SKILL =23;
 define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;
 define BALL_ROLL_CHOP_SKILL =25;
 define FEINT_AND_EXIT_SKILL =26;
 define FEINT_L_EXIT_R_SKILL =27;
 define LATERAL_HEEL_TO_HEEL_SKILL =28;
 define WAKA_WAKA_SKILL =29;
 define BODY_FEINT_SKILL =30;
 define DRAG_TO_HEEL =31;
 define BALL_ROLL_FAKE_TURN =32;
 define FEINT_FORWARD_AND_TURN =33;
 define XBOX1_CONTROLLER_HOLD695191_358660 =34;
 define XBOX1_CONTROLLER_HOLD514741_814386 =35;
 define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;
 define REVERSE_STEP_OVER_SKILL =37;
 define FAKE_DRAG_BACK_SKILL =38;
 define RAINBOW_TO_SCORPION_KICK_SKILL =39;
 define STEP_OVER_BOOST_SKILL =40;
 define CANCEL_SHOOT_SKILL =41;
 define DIRECTIONAL_NUTMEG_SKILL =42;
 define CANCELED_BERBA_SPIN_SKILL =43;
 define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
 define BALL_ROLL_TO_SCOOP_TURN_SKILL =45;
 define DRIBBLING_SKILL =46;
 define FOUR_TOUCH_TURN_SKILLS =47;
 define SKILLED_BRIDGE_SKILL =48;
 define SCOOP_TURN_FAKE_SKILL =49;
 define BALL_ROLL_STEP_OVER_SKILL =50;
 define CANCELED_4_TOUCH_TURN_SKILL =51;
 define FAKE_SHOT_CANCEL_SKILL =52;
 define OKKOSHA_FLICK_SKILL =53;
 define ADVANCED_RAINBOW_SKILL =54;
 define STOP_LA_CROQUETA_SKILL =55;
 define JUGGLING_RAINBOW_SKILL =56;
 define STOP_NEYMAR_ROLL_SKILL =57;
 define STOP_V_DRAG_SKILL =58;
 define REV_OR_ELASTICO_SKILL =59;
 define STOP_REV_OR_ELASTICO_SKILL =60;
 define DRAG_REV_OR_ELASTICO_SKILL =61;
 define FAKE_RABONA_SKILL =62;
 define RABONA_TO_REV_ELASTICO_SKILL =63;
 define RABONA_TO_ELASTICO_SKILL =64;
 define SOMBRERO_FLICK_SKILL =65;
 define JUGGLE_BACK_SOMBRERO_SKILL =66;
 define FAKE_BERBA_OPP_EXIT_SKILL =67;
 define DIAGONAL_HEEL_CHOP_SKILL =68;
 define FAKE_BERBA_FAKE_DRAG_SKILL =69;
 define UP = 0;
 define UP_RIGHT = 1;
 define RIGHT = 2;
 define DOWN_RIGHT = 3;
 define DOWN = 4;
 define DOWN_LEFT = 5;
 define LEFT = 6;
 define UP_LEFT = 7;
 int XBOX1_CONTROLLER_HOLD649799_785815;
 int XBOX1_CONTROLLER_HOLD832911_648489;
 int XBOX1_CONTROLLER_HOLD657207_140627;
 int XBOX1_CONTROLLER_HOLD708645_565974;
 int XBOX1_CONTROLLER_HOLD181676_120837;
 int XBOX1_CONTROLLER_HOLD809106_539351 = 1500;
 int XBOX1_CONTROLLER_HOLD667383_110962;
 int XBOX1_CONTROLLER_HOLD808084_843633;
 int XBOX1_CONTROLLER_HOLD357586_187953;
 int XBOX1_CONTROLLER_HOLD597392_189487;
 int XBOX1_CONTROLLER_HOLD928314_240188;
 int XBOX1_CONTROLLER_HOLD161998_138731;
 int XBOX1_CONTROLLER_HOLD428090_112021;
 int XBOX1_CONTROLLER_HOLD571712_465716;
 int XBOX1_CONTROLLER_HOLD179821_743889;
 int XBOX1_CONTROLLER_HOLD343089_183259;
 int XBOX1_CONTROLLER_HOLD749779_346022 = 0;
 int XBOX1_CONTROLLER_HOLD292081_183045 = 0;
 int XBOX1_CONTROLLER_HOLD339031_439011 = 180;
 int XBOX1_CONTROLLER_HOLD197808_727695 = 230;
 int XBOX1_CONTROLLER_HOLD860982_898923 = 80;
 int XBOX1_CONTROLLER_HOLD763382_253927 = 80;
 int L1_TIME = 50;
 int XBOX1_CONTROLLER_HOLD641169_895307 = PS5_PS;
 int Share = PS5_SHARE;
 int XBOX1_CONTROLLER_HOLD210492_580559 = PS5_OPTIONS;
 int R1 = PS5_R1;
 int R2 = PS5_R2;
 int R3 = PS5_R3;
 int L1 = PS5_L1;
 int L2 = PS5_L2;
 int L3 = PS5_L3;
 int RX = PS5_RX;
 int RY = PS5_RY;
 int PS5_SKILL_MOVES;
 int PS5_R3_LEFT;
 int XBOX1_CONTROLLER_HOLD595206_274104 = PS5_UP;
 int XBOX1_CONTROLLER_HOLD157916_778764 = PS5_DOWN;
 int XBOX1_CONTROLLER_HOLD385737_716174 = PS5_LEFT;
 int XBOX1_CONTROLLER_HOLD807235_363103 = PS5_RIGHT;
 int XBOX1_CONTROLLER_HOLD396161_307333 = PS5_TRIANGLE;
 int XBOX1_CONTROLLER_HOLD894285_651586 = PS5_CIRCLE;
 int XBOX1_CONTROLLER_HOLD590143_580018 = PS5_CROSS;
 int XBOX1_CONTROLLER_HOLD944307_799744 = PS5_SQUARE;
 define AMOUNT_OF_VALNAME_IDX = 39;
 const string TITLE = "UR_SCRIPT"; const string SUB_TITLE = "FIFA 25"; const string OFF = "OFF"; const string ON = "ON"; define MAX_MODS_COUNT = 36;
 int XBOX1_CONTROLLER_HOLD397509_948864;
 int XBOX1_CONTROLLER_HOLD731824_426854 = 0;
 int XBOX1_CONTROLLER_HOLD199265_605093 = 0;
 int XBOX1_CONTROLLER_HOLD679191_872402 = 0;
 int XBOX1_CONTROLLER_HOLD116833_956439 = 0;
 int XBOX1_CONTROLLER_HOLD215412_394754 = 0;
 int XBOX1_CONTROLLER_HOLD551026_175331 = 0;
 int XBOX1_CONTROLLER_HOLD670676_890737 = 0;
 int XBOX1_CONTROLLER_HOLD380752_842087 = 0;
 int XBOX1_CONTROLLER_HOLD147809_169073 = 0;
 int XBOX1_CONTROLLER_HOLD379299_599772 = 0;
 int XBOX1_CONTROLLER_HOLD383131_770525 = 0;
 int XBOX1_CONTROLLER_HOLD170227_579625 = 0;
 int XBOX1_CONTROLLER_HOLD963407_707609 = 0;
 int XBOX1_CONTROLLER_HOLD827669_641640 = 0;
 int XBOX1_CONTROLLER_HOLD374691_869672 = 0;
 int XBOX1_CONTROLLER_HOLD337226_272844 = 0;
 int XBOX1_CONTROLLER_HOLD663280_652109 = 0;
 int XBOX1_CONTROLLER_HOLD760217_523408 = 0;
 int XBOX1_CONTROLLER_HOLD554987_453137 = 0;
 int XBOX1_CONTROLLER_HOLD137336_617074 = 0;
 int XBOX1_CONTROLLER_HOLD288357_290232 = 0;
 int XBOX1_CONTROLLER_HOLD883498_810572 = 0;
 int XBOX1_CONTROLLER_HOLD263496_792006 = 0;
 int XBOX1_CONTROLLER_HOLD446722_737573 = 0;
 int Modifier = 0;
 int XBOX1_CONTROLLER_HOLD562128_307172 = 0;
 int XBOX1_CONTROLLER_HOLD344762_620295 = 0;
 int XBOX1_CONTROLLER_HOLD346771_740680 = 0;
 int XBOX1_CONTROLLER_HOLD455115_623354 int FinesseShot int PlayerRun int ShotBtn int SprintBtn int PassBtn int MODIFIER int CrossBtn int ThroughBall const string MOD_STATUS_STR[]={ "SCRIPT WAS", "Ultimate Finish" , "Dynamic Finish" , "Penalty MOD" , "Free Kicks MOD" }; int XBOX1_CONTROLLER_HOLD635031_583299;
 int XBOX1_CONTROLLER_HOLD217287_658633;
 const int16 ValRange [][] ={ { 100,600 }, { 100,600 }, { 100,600 }, { 100,5000}, { 100,400 }, { 100,1000 }, { 0,200 }, { 100,400 }, { 100,1000 }, { 0,200 }, { 100,700 }, { 100,700 }, { 100,700 }, { 100,700 }, { 80,100 }, { 80,327 }, { 100,500 }, { 100,600 }, { 50,800 }, { 50,800 }, {1, 1000} }; const string EditVarStr []={ "la tjeha", "la tjyha", "Low Driven Shot", "After SprintTime", "8wh alshot", "w8t altime", "mtws6 alping", "8wh alshot", "w8t altime", "mtws6 alping", "Shot Power UP", "Shot Power RIGHT", "Shot Power DOWN", "Shot Power LEFT", "Stick Sens", "L S Sprint Sens", "Chip Shot Power", "Low Driven Power", "Ground Pass MIN" , "Trough Pass MIN" , "L1_TIME" }; const string MOD_NAMES_STR [] = { "A6'36 SHM T7T" , "Timed Finish" , "POWER", "Shot Power Restr", "Penalty MOD" , "Free Kicks MOD" , "Left Stick Sens" , "ChipShot Sngle B", "Low Driven SingB", "VM Speed" , "Defence MOD" , "Ground Pass MIN" , "Trough Pass MIN" , "XBOX1_CONTROLLER_HOLD Boost" , "RghtStick Skills", "Right Stick UP" , "Rght Stck DOWN" , "Rght Stck LEFT" , "Rght Stck RIGHT" , "Skill on R3" , "Skill on PR1" , "Skill on PL1" , "Skill on PR2" , "Skill on PL2" , "Modifier InstSkll", "Skll: R1-L1" , "Skll:SQUARE-TRNGL", "Skll:CROSS-CIRCLE", "PaceControl", "FinesseShot", "PlayerRun", "ShotBtn", "SprintBtn", "PassBtn", "MODIFIER", "CrossBtn", "ThroughBall" }; const int8 ASCII_NUM[] = {48,49,50,51,52,53,54,55,56,57}; const string NO_EDIT_VAR1 = "No Edit Variables"; const string NO_EDIT_VAR2 = "for this MOD"; const uint8 Options [] = { 1 , 31 , 31 , 4 , 1 , 1 , 33 , 6 , 6 , 5 , 7 , 8 , 9 , 23 , 26 , 28 , 28 , 28 , 28 , 28 , 28 , 28 , 28 , 28 , 6 , 28 , 28 , 28 , 24 , 24 , 24 , 24 , 24 , 24 , 24 , 24, 24 }; const uint8 EditVal [] = { 4 , 2 , 2 , 4 , 0 , 0 , 1, 1, 1 , 0, 0, 1 , 1 , 1 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 , 0 }; const uint8 OptRange [][] = { { 0 , 3 }, { 4 , 6 }, { 7 , 9 }, {10 , 13 }, { 0 , 0 }, { 0 , 0 }, {14 , 15 }, {16 , 16 }, {17 , 17 }, { 0 , 0 }, { 0 , 0 }, { 18, 18 }, { 19, 19 }, { 20 , 20 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0 , 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 } }; const uint8 Min_Max_Options [][]={ { 0 , 1 }, { 0 , 2 }, { 0 , 2 }, { 0 , 4 }, { 0 , 1 }, { 0 , 1 }, { 0 , 2 }, { 0 , 18}, { 0 , 18}, { 0 , 5 }, { 0 , 4 }, { 0 , 4 }, { 0 , 2 }, { 0 , 2 }, { 0 , 7 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 ,18 }, { 0 ,66 }, { 0 ,66 }, { 0 ,66 }, { 0 , 21 }, { 0 , 21 }, { 0 , 21 }, { 0 , 20 }, { 0 , 20 }, { 0 , 20 }, { 0 , 21 }, { 0 , 21 }, { 0 , 20 } }; int XBOX1_CONTROLLER_HOLD407515_331097;
 int LX,LY;
 int RB_PRESS_XBOX_BUTTON = 1000000;
int BR_PRESS_XBOX_BUTTON = 7;
int RB1_PRESS_XBOX_BUTTON = 54321;
int RX_PRESS_XBOX_BUTTON = 11;
int LX_PRESS_XBOX_BUTTON = 31337;
int LLX_PRESS_XBOX_BUTTON = 98765;
 init { Load_PVARs(); cls_oled(0); XBOX1_CONTROLLER_HOLD687228_876037 = 1;
 PS5_R3_DOWN = 0;
 for(XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 6;
 XBOX1_CONTROLLER_HOLD243440_708731++) { XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD243440_708731] = 0;
 } if(PS5_R3_RIGHT) { PS5_R3_DOWN = 101;
 } } init { XBOX1_CONTROLLER_HOLD731824_426854 = get_pvar(SPVAR_2,1,3,0); XBOX1_CONTROLLER_HOLD199265_605093 = get_pvar(SPVAR_3,0,2,0); XBOX1_CONTROLLER_HOLD679191_872402 = get_pvar(SPVAR_4,0,2,0); XBOX1_CONTROLLER_HOLD116833_956439 = get_pvar(SPVAR_5,0,5,0); XBOX1_CONTROLLER_HOLD215412_394754 = get_pvar(SPVAR_6,0,1,0); XBOX1_CONTROLLER_HOLD551026_175331 = get_pvar(SPVAR_7,0,1,0); XBOX1_CONTROLLER_HOLD670676_890737 = get_pvar(SPVAR_8,0,2,0); XBOX1_CONTROLLER_HOLD380752_842087 = get_pvar(SPVAR_9,0,18,0); XBOX1_CONTROLLER_HOLD379299_599772 = get_pvar(SPVAR_10 ,0,5,0); XBOX1_CONTROLLER_HOLD657207_140627 = get_pvar(SPVAR_13, 100, 600, 190); XBOX1_CONTROLLER_HOLD708645_565974 = get_pvar(SPVAR_14, 100, 600, 240); XBOX1_CONTROLLER_HOLD181676_120837 = get_pvar(SPVAR_15, 100, 600, 230); XBOX1_CONTROLLER_HOLD809106_539351 = get_pvar(SPVAR_16, 100,5000,2000); XBOX1_CONTROLLER_HOLD667383_110962 = get_pvar(SPVAR_17, 1,400, 200); XBOX1_CONTROLLER_HOLD808084_843633 = get_pvar(SPVAR_18, 1,400, 235); XBOX1_CONTROLLER_HOLD357586_187953 = get_pvar(SPVAR_19, 0,200, 0); XBOX1_CONTROLLER_HOLD597392_189487 = get_pvar(SPVAR_20, 1,400, 200); XBOX1_CONTROLLER_HOLD928314_240188 = get_pvar(SPVAR_21, 1,400, 235); XBOX1_CONTROLLER_HOLD161998_138731 = get_pvar(SPVAR_22, 0,200, 0); XBOX1_CONTROLLER_HOLD428090_112021 = get_pvar(SPVAR_23, 100,700,220); XBOX1_CONTROLLER_HOLD571712_465716 = get_pvar(SPVAR_24, 100,700,270); XBOX1_CONTROLLER_HOLD179821_743889 = get_pvar(SPVAR_25, 100,700,300); XBOX1_CONTROLLER_HOLD343089_183259 = get_pvar(SPVAR_26, 100,700,350); XBOX1_CONTROLLER_HOLD749779_346022 = get_pvar(SPVAR_27, 80,100, 88); XBOX1_CONTROLLER_HOLD292081_183045 = get_pvar(SPVAR_28, 80,327,100); XBOX1_CONTROLLER_HOLD339031_439011 = get_pvar(SPVAR_29, 100,500,150); XBOX1_CONTROLLER_HOLD827669_641640 = get_pvar(SPVAR_30, 0, 1, 0); XBOX1_CONTROLLER_HOLD383131_770525 = get_pvar(SPVAR_31, 0, 4, 0); XBOX1_CONTROLLER_HOLD197808_727695 = get_pvar(SPVAR_32, 100,600,390); XBOX1_CONTROLLER_HOLD147809_169073 = get_pvar(SPVAR_33, 0, 18, 0); XBOX1_CONTROLLER_HOLD170227_579625 = get_pvar(SPVAR_34, 0, 2, 0); XBOX1_CONTROLLER_HOLD963407_707609 = get_pvar(SPVAR_35, 0, 2, 0); XBOX1_CONTROLLER_HOLD860982_898923 = get_pvar(SPVAR_36, 30,800, 80); XBOX1_CONTROLLER_HOLD763382_253927 = get_pvar(SPVAR_37, 30,800, 80); XBOX1_CONTROLLER_HOLD374691_869672 = get_pvar(SPVAR_38, 0, 7, 0); XBOX1_CONTROLLER_HOLD337226_272844 = get_pvar(SPVAR_39, 0, 66, 0); XBOX1_CONTROLLER_HOLD663280_652109 = get_pvar(SPVAR_40, 0, 66, 0); XBOX1_CONTROLLER_HOLD760217_523408 = get_pvar(SPVAR_41, 0, 66, 0); XBOX1_CONTROLLER_HOLD554987_453137 = get_pvar(SPVAR_42, 0, 66, 0); XBOX1_CONTROLLER_HOLD137336_617074 = get_pvar(SPVAR_43, 0, 66, 0); XBOX1_CONTROLLER_HOLD288357_290232 = get_pvar(SPVAR_44, 0, 66, 0); XBOX1_CONTROLLER_HOLD883498_810572 = get_pvar(SPVAR_45, 0, 66, 0); XBOX1_CONTROLLER_HOLD263496_792006 = get_pvar(SPVAR_46, 0, 66, 0); XBOX1_CONTROLLER_HOLD446722_737573 = get_pvar(SPVAR_47, 0, 66, 0); Modifier = get_pvar(SPVAR_48, 0, 66, 0); XBOX1_CONTROLLER_HOLD562128_307172 = get_pvar(SPVAR_49, 0, 66, 0); XBOX1_CONTROLLER_HOLD344762_620295 = get_pvar(SPVAR_50, 0, 66, 0); XBOX1_CONTROLLER_HOLD346771_740680 = get_pvar(SPVAR_51, 0, 66, 0); if(XBOX1_CONTROLLER_HOLD116833_956439 == 1) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD428090_112021;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 2) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD571712_465716;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 3) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD179821_743889;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 4) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD343089_183259;
 if(XBOX1_CONTROLLER_HOLD694842_235639 <= 0 ) XBOX1_CONTROLLER_HOLD694842_235639 = 220;
 } main { XBOX1_CONTROLLER_HOLD637682_922924 = SELECT_BTN [XBOX1_CONTROLLER_HOLD380752_842087] ;
 XBOX1_CONTROLLER_HOLD782577_342508 = SELECT_BTN [Modifier] ;
 if(get_ival(PS4_L2)){ if(event_press(XB1_XBOX)){ XBOX1_CONTROLLER_HOLD294058_790711 = !XBOX1_CONTROLLER_HOLD294058_790711;
 blinck(XBOX1_CONTROLLER_HOLD294058_790711); cls_oled(0); draw_rectangle(); display_MOD_status(XBOX1_CONTROLLER_HOLD294058_790711,0); XBOX1_CONTROLLER_HOLD967116_476309 = 2000;
 if(!XBOX1_CONTROLLER_HOLD294058_790711) set_Virtual_Machine_Speed(VM_Default); } } if(XBOX1_CONTROLLER_HOLD967116_476309){ XBOX1_CONTROLLER_HOLD967116_476309 -= get_rtime(); if(XBOX1_CONTROLLER_HOLD967116_476309 <= 0) { cls_oled(0); } } if(XBOX1_CONTROLLER_HOLD294058_790711){ if(XBOX1_CONTROLLER_HOLD210345_469028 || XBOX1_CONTROLLER_HOLD807766_790865) set_Virtual_Machine_Speed(VM_Default); if(XBOX1_CONTROLLER_HOLD939187_293664){ cls_oled(0); if(EditVal[ XBOX1_CONTROLLER_HOLD217287_658633 ] == 0 ){ line_oled(1,45,127,45,1,0); printf( center_x( 17, OLED_FONT_SMALL_WIDTH ), 20, OLED_FONT_SMALL, 1, NO_EDIT_VAR1[0] ); printf( center_x( 12, OLED_FONT_SMALL_WIDTH ), 33, OLED_FONT_SMALL, 1, NO_EDIT_VAR2[0] ); } else { display_edit( XBOX1_CONTROLLER_HOLD832911_648489 [XBOX1_CONTROLLER_HOLD635031_583299 + 1]); XBOX1_CONTROLLER_HOLD832911_648489 [XBOX1_CONTROLLER_HOLD635031_583299 + 1] = edit_val(XBOX1_CONTROLLER_HOLD832911_648489 [XBOX1_CONTROLLER_HOLD635031_583299 + 1] ); } } if(XBOX1_CONTROLLER_HOLD133123_725174) { cls_oled(0); draw_rectangle(); display_mod( XBOX1_CONTROLLER_HOLD217287_658633, XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1] ,Options[ XBOX1_CONTROLLER_HOLD217287_658633 ]); XBOX1_CONTROLLER_HOLD133123_725174 = FALSE;
 } if(XBOX1_CONTROLLER_HOLD285505_350494){ cls_oled(0); ps5_moves = random(1, 6000); printf(center_x(sizeof(TITLE) - 1, OLED_FONT_MEDIUM_WIDTH),13,OLED_FONT_MEDIUM,OLED_WHITE,TITLE[0]); printf(center_x(sizeof(SUB_TITLE) - 1, OLED_FONT_SMALL_WIDTH),43,OLED_FONT_SMALL,OLED_WHITE,SUB_TITLE[0]); XBOX1_CONTROLLER_HOLD807766_790865 = FALSE;
 XBOX1_CONTROLLER_HOLD285505_350494 = FALSE;
 XBOX1_CONTROLLER_HOLD450073_310579 = TRUE;
 } if(XBOX1_CONTROLLER_HOLD450073_310579 ) { XBOX1_CONTROLLER_HOLD320141_515560 += get_rtime(); if(XBOX1_CONTROLLER_HOLD320141_515560 >= 5000) { cls_oled(0); XBOX1_CONTROLLER_HOLD320141_515560 = 0;
 XBOX1_CONTROLLER_HOLD450073_310579 = FALSE;
 } } { if(PS5_R3_DOWN <= 100) {  ps5_moves = random(1, 6000); BlockAllOutputs(); printText(alignCenter, 8, EnterCodeToAccess[0], OLED_FONT_SMALL); if(event_press(PS4_LEFT)) { XBOX1_CONTROLLER_HOLD148058_514103 = Cycle(XBOX1_CONTROLLER_HOLD148058_514103 - 1, 0, 5); cls_oled(0); } if(event_press(PS4_RIGHT)) { XBOX1_CONTROLLER_HOLD148058_514103 = Cycle(XBOX1_CONTROLLER_HOLD148058_514103 + 1, 0, 5); cls_oled(0); } if(event_press(PS4_UP)) { XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD148058_514103] = Cycle(XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD148058_514103] + 1, 0, 9); cls_oled(0); } if(event_press(PS4_DOWN)) { XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD148058_514103] = Cycle(XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD148058_514103] - 1, 0, 9); cls_oled(0); } line_oled(8 + XBOX1_CONTROLLER_HOLD148058_514103 * 20, 30, 8 + XBOX1_CONTROLLER_HOLD148058_514103 * 20 + 11, 30, 2, 1); for(XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 6;
 XBOX1_CONTROLLER_HOLD243440_708731++) { printNumber(8 + XBOX1_CONTROLLER_HOLD243440_708731 * 20, 38, XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD243440_708731], 1); } if(event_press(PS4_CROSS)) { XBOX1_CONTROLLER_HOLD553355_535494 = 0;
 for(XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 6;
 XBOX1_CONTROLLER_HOLD243440_708731++) { XBOX1_CONTROLLER_HOLD553355_535494 = XBOX1_CONTROLLER_HOLD553355_535494 * 10 + XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD243440_708731]; } if(XBOX1_CONTROLLER_HOLD553355_535494 == PS5_R3_HOLD) { PS5_R3_RIGHT = 1;
 SavePVARS(); combo_run(XBOX1_CONTROLLER_HOLD463939_636392); combo_run(XBOX1_CONTROLLER_HOLD121652_797476); } else { combo_run(XBOX1_CONTROLLER_HOLD247816_863711); combo_run(XBOX1_CONTROLLER_HOLD121652_797476); } } } } if(XBOX1_CONTROLLER_HOLD210345_469028){ block_all_inputs(); if(XBOX1_CONTROLLER_HOLD735875_938119){ CheckIfBtnIsPressed(); } if(event_release(XB1_B)){ XBOX1_CONTROLLER_HOLD210345_469028 = FALSE;
 XBOX1_CONTROLLER_HOLD939187_293664 = FALSE;
 XBOX1_CONTROLLER_HOLD807766_790865 = TRUE;
 XBOX1_CONTROLLER_HOLD133123_725174 = TRUE;
 } if(!get_ival(PS4_L2) && event_press(XB1_DOWN)){ XBOX1_CONTROLLER_HOLD635031_583299 ++; if(XBOX1_CONTROLLER_HOLD635031_583299 > OptRange[ XBOX1_CONTROLLER_HOLD217287_658633 ][ 1 ]) XBOX1_CONTROLLER_HOLD635031_583299 = OptRange[ XBOX1_CONTROLLER_HOLD217287_658633 ][ 1 ]; XBOX1_CONTROLLER_HOLD133123_725174 = FALSE;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } if(!get_ival(PS4_L2) && event_press(XB1_UP)){ XBOX1_CONTROLLER_HOLD635031_583299 --; if(XBOX1_CONTROLLER_HOLD635031_583299 < OptRange[ XBOX1_CONTROLLER_HOLD217287_658633 ][ 0 ]) XBOX1_CONTROLLER_HOLD635031_583299 = OptRange[ XBOX1_CONTROLLER_HOLD217287_658633 ][ 0 ]; XBOX1_CONTROLLER_HOLD133123_725174 = FALSE;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } if(XBOX1_CONTROLLER_HOLD735875_938119){ CheckIfBtnIsPressed(); } } if(get_val(PS4_L2) && event_press(XB1_MENU)){ XBOX1_CONTROLLER_HOLD807766_790865 = TRUE;
 XBOX1_CONTROLLER_HOLD217287_658633 = 0;
 XBOX1_CONTROLLER_HOLD133123_725174 = TRUE;
 XBOX1_CONTROLLER_HOLD939187_293664 = FALSE;
 XBOX1_CONTROLLER_HOLD735875_938119 = menu_time_active;
 } if(XBOX1_CONTROLLER_HOLD807766_790865){ block_all_inputs(); if(event_press(XB1_B)){ XBOX1_CONTROLLER_HOLD807766_790865 = FALSE;
 XBOX1_CONTROLLER_HOLD210345_469028 = FALSE;
 combo_run(XBOX1_CONTROLLER_HOLD964396_775778); } if(event_press(XB1_A)){ XBOX1_CONTROLLER_HOLD807766_790865 = FALSE;
 XBOX1_CONTROLLER_HOLD635031_583299 = OptRange[ XBOX1_CONTROLLER_HOLD217287_658633 ][ 0 ]; XBOX1_CONTROLLER_HOLD210345_469028 = TRUE;
 XBOX1_CONTROLLER_HOLD939187_293664= TRUE;
 } if( !get_ival(PS4_L2) && event_press(XB1_DOWN)){ XBOX1_CONTROLLER_HOLD217287_658633 ++; if(XBOX1_CONTROLLER_HOLD217287_658633 > MAX_MODS_COUNT) XBOX1_CONTROLLER_HOLD217287_658633 = 0;
 blinck( XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1]); } if( !get_ival(PS4_L2) && event_press(XB1_UP)){ XBOX1_CONTROLLER_HOLD217287_658633 --; if(XBOX1_CONTROLLER_HOLD217287_658633 < 0) XBOX1_CONTROLLER_HOLD217287_658633 = MAX_MODS_COUNT;
 blinck( XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1]); } XBOX1_CONTROLLER_HOLD450073_310579 = FALSE;
 XBOX1_CONTROLLER_HOLD285505_350494 = FALSE;
 if(!get_ival(PS4_L2) ){ if(event_press(XB1_LEFT)){ XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1] = f_go_back(XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1]); } if(event_press(XB1_RIGHT)){ XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1] = f_go_forward(XBOX1_CONTROLLER_HOLD397509_948864[XBOX1_CONTROLLER_HOLD217287_658633 + 1]); } } XBOX1_CONTROLLER_HOLD133123_725174 = TRUE;
 if(XBOX1_CONTROLLER_HOLD735875_938119){ CheckIfBtnIsPressed(); } } if(!XBOX1_CONTROLLER_HOLD210345_469028 && !XBOX1_CONTROLLER_HOLD807766_790865){ set_Virtual_Machine_Speed( XBOX1_CONTROLLER_HOLD379299_599772); if(XBOX1_CONTROLLER_HOLD215412_394754){ if(get_val(PS4_L1)){ if(event_press(PS4_OPTIONS)){ XBOX1_CONTROLLER_HOLD622030_531966 = !XBOX1_CONTROLLER_HOLD622030_531966;
 f_set_notify(XBOX1_CONTROLLER_HOLD622030_531966); display_MOD_status(XBOX1_CONTROLLER_HOLD622030_531966,3);  ps5_moves = random(1, 6000); } set_val(PS4_OPTIONS,0); } } if(XBOX1_CONTROLLER_HOLD551026_175331){ if(get_val(PS4_L1)){ if(event_press(PS4_SHARE)){ XBOX1_CONTROLLER_HOLD338812_737777 = !XBOX1_CONTROLLER_HOLD338812_737777;
 f_set_notify(XBOX1_CONTROLLER_HOLD338812_737777); display_MOD_status(XBOX1_CONTROLLER_HOLD338812_737777,4);  ps5_moves = random(1, 6000);} set_val(PS4_SHARE,0); } } if((XBOX1_CONTROLLER_HOLD622030_531966 && XBOX1_CONTROLLER_HOLD215412_394754 ) || (XBOX1_CONTROLLER_HOLD551026_175331 && XBOX1_CONTROLLER_HOLD338812_737777)) { vm_tctrl(0); if(XBOX1_CONTROLLER_HOLD338812_737777) colourled(Red); else if(XBOX1_CONTROLLER_HOLD622030_531966) colourled(Pink); if(XBOX1_CONTROLLER_HOLD338812_737777){ f_FREE_KICK (); } if(XBOX1_CONTROLLER_HOLD622030_531966){ fPenalties (); } } else { colourled(Blue); if(XBOX1_CONTROLLER_HOLD670676_890737){ if (!get_val(XBOX1_CONTROLLER_HOLD455115_623354) && !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn) ){ sensitivity(PS4_LX, NOT_USE, XBOX1_CONTROLLER_HOLD749779_346022); sensitivity(PS4_LY, NOT_USE, XBOX1_CONTROLLER_HOLD749779_346022); } if(XBOX1_CONTROLLER_HOLD670676_890737 == 2){ if (get_val(SprintBtn)){ sensitivity(PS4_LX, NOT_USE, XBOX1_CONTROLLER_HOLD292081_183045); sensitivity(PS4_LY, NOT_USE, XBOX1_CONTROLLER_HOLD292081_183045); } } } if(XBOX1_CONTROLLER_HOLD380752_842087){ if(event_press(XBOX1_CONTROLLER_HOLD637682_922924)){ vm_tctrl(0); combo_run(XBOX1_CONTROLLER_HOLD446331_507563); } set_val(XBOX1_CONTROLLER_HOLD637682_922924,0); } if (XBOX1_CONTROLLER_HOLD827669_641640 == 1) { if (get_val(SprintBtn)) { if (XBOX1_CONTROLLER_HOLD572591_984177 == 0 && !XBOX1_CONTROLLER_HOLD258112_738204 && !XBOX1_CONTROLLER_HOLD509548_271511) { set_val(SprintBtn, 0); combo_run(XBOX1_CONTROLLER_HOLD344425_426621); XBOX1_CONTROLLER_HOLD572591_984177 = 1;
 } else if (XBOX1_CONTROLLER_HOLD509548_271511 || XBOX1_CONTROLLER_HOLD258112_738204) { set_val(SprintBtn, 100); set_val(MODIFIER, 0); combo_stop(XBOX1_CONTROLLER_HOLD344425_426621); } } else { XBOX1_CONTROLLER_HOLD572591_984177 = 0;
 set_val(SprintBtn, 0); XBOX1_CONTROLLER_HOLD258112_738204 = 0;
 XBOX1_CONTROLLER_HOLD509548_271511 = 0;
 } if (get_val(XB1_RS) && XBOX1_CONTROLLER_HOLD572591_984177) { if (XBOX1_CONTROLLER_HOLD827669_641640 == 2) { XBOX1_CONTROLLER_HOLD258112_738204 = 1;
 combo_stop(XBOX1_CONTROLLER_HOLD344425_426621); XBOX1_CONTROLLER_HOLD509548_271511 = 1;
 } } } if (XBOX1_CONTROLLER_HOLD827669_641640 == 2) { if (get_val(SprintBtn)) { if (XBOX1_CONTROLLER_HOLD572591_984177 == 0 && !XBOX1_CONTROLLER_HOLD258112_738204 && !XBOX1_CONTROLLER_HOLD509548_271511) { set_val(SprintBtn, 0); combo_run(XBOX1_CONTROLLER_HOLD344425_426621); XBOX1_CONTROLLER_HOLD572591_984177 = 1;
 } else if (XBOX1_CONTROLLER_HOLD509548_271511 || XBOX1_CONTROLLER_HOLD258112_738204) { set_val(SprintBtn, 100); set_val(MODIFIER, 0); combo_stop(XBOX1_CONTROLLER_HOLD344425_426621); } } else { XBOX1_CONTROLLER_HOLD572591_984177 = 0;
 set_val(SprintBtn, 0); XBOX1_CONTROLLER_HOLD258112_738204 = 0;
 XBOX1_CONTROLLER_HOLD509548_271511 = 0;
 } if (get_val(XB1_RS) && XBOX1_CONTROLLER_HOLD572591_984177) { XBOX1_CONTROLLER_HOLD258112_738204 = 1;
 combo_stop(XBOX1_CONTROLLER_HOLD344425_426621); XBOX1_CONTROLLER_HOLD509548_271511 = 1;
 } } if(XBOX1_CONTROLLER_HOLD116833_956439){ if ( get_val(PS4_L1)) { if(event_press(XB1_UP)) {XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD428090_112021;
 colourled(Green); } if(event_press(XB1_RIGHT)) {XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD571712_465716;
 colourled(Blue ); } if(event_press(XB1_DOWN)) {XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD179821_743889;
 colourled(Pink ); } if(event_press(XB1_LEFT)) {XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD343089_183259;
 colourled(Red ); } set_val(13,0); set_val(14,0); set_val(15,0);set_val(16,0); } if(!get_val(FinesseShot) && get_val(ShotBtn) && get_ptime(ShotBtn)>= XBOX1_CONTROLLER_HOLD694842_235639) { set_val(ShotBtn, 0); } if(get_val(ShotBtn)) set_val(SprintBtn,0); } if(XBOX1_CONTROLLER_HOLD199265_605093 ) { if(XBOX1_CONTROLLER_HOLD199265_605093 == 1) { if(get_val(ShotBtn) && get_ptime(ShotBtn) > XBOX1_CONTROLLER_HOLD667383_110962){ set_val(ShotBtn,0); combo_run(XBOX1_CONTROLLER_HOLD567385_312303); } } else if(XBOX1_CONTROLLER_HOLD199265_605093 == 2) { if(event_press(ShotBtn)){ combo_run(XBOX1_CONTROLLER_HOLD655519_768417); } } } if(XBOX1_CONTROLLER_HOLD147809_169073){ if(event_press(LowDrivenBtn)){ combo_run(XBOX1_CONTROLLER_HOLD235866_386279); } } if(XBOX1_CONTROLLER_HOLD679191_872402 ) { if(XBOX1_CONTROLLER_HOLD679191_872402 == 1){ if(get_val(ShotBtn) && get_ptime(ShotBtn) > XBOX1_CONTROLLER_HOLD667383_110962){ set_val(ShotBtn,0); combo_run(XBOX1_CONTROLLER_HOLD567385_312303); } } else if(XBOX1_CONTROLLER_HOLD679191_872402 == 2){ if(event_press(PS4_UP)){ set_val(FinesseShot,100); set_val(MODIFIER,100); combo_run(XBOX1_CONTROLLER_HOLD446062_880359); } } } if(XBOX1_CONTROLLER_HOLD352641_941489){ XBOX1_CONTROLLER_HOLD352641_941489 -= get_rtime(); if(XBOX1_CONTROLLER_HOLD352641_941489 <= 0 ){ QT_MESSAGE_TIMEOUT(); } } if(XBOX1_CONTROLLER_HOLD170227_579625){ if(!get_val(XBOX1_CONTROLLER_HOLD455115_623354)&&!get_val(SprintBtn) ){ if(get_val(PassBtn)){ XBOX1_CONTROLLER_HOLD806055_273899 += get_rtime(); } if(event_release(PassBtn)){ if( XBOX1_CONTROLLER_HOLD806055_273899 < XBOX1_CONTROLLER_HOLD860982_898923 ){ XBOX1_CONTROLLER_HOLD629849_248231 = XBOX1_CONTROLLER_HOLD860982_898923 - XBOX1_CONTROLLER_HOLD806055_273899;
 combo_run(XBOX1_CONTROLLER_HOLD594100_329114); }else{ if(XBOX1_CONTROLLER_HOLD170227_579625 == 2 || XBOX1_CONTROLLER_HOLD170227_579625 == 4) combo_run(XBOX1_CONTROLLER_HOLD820320_395989); } XBOX1_CONTROLLER_HOLD806055_273899 = 0;
 } } } if(XBOX1_CONTROLLER_HOLD963407_707609){ if(!get_val(XBOX1_CONTROLLER_HOLD455115_623354)&&!get_val(SprintBtn)){ if(get_val(ThroughBall)){ XBOX1_CONTROLLER_HOLD926871_397554 += get_rtime(); } if(event_release(ThroughBall)){ if(XBOX1_CONTROLLER_HOLD926871_397554 < XBOX1_CONTROLLER_HOLD763382_253927){ XBOX1_CONTROLLER_HOLD285144_348483 = XBOX1_CONTROLLER_HOLD763382_253927 - XBOX1_CONTROLLER_HOLD926871_397554;
 combo_run(XBOX1_CONTROLLER_HOLD781858_790437); }else{ if(XBOX1_CONTROLLER_HOLD963407_707609 == 2) combo_run(XBOX1_CONTROLLER_HOLD224440_807647); } XBOX1_CONTROLLER_HOLD926871_397554 = 0;
 } } } if( XBOX1_CONTROLLER_HOLD374691_869672 == 0) XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 if( XBOX1_CONTROLLER_HOLD374691_869672 == AlwaysON) XBOX1_CONTROLLER_HOLD382632_533959 = TRUE;
 if( XBOX1_CONTROLLER_HOLD374691_869672 == DoubleTapL3) { if(XBOX1_CONTROLLER_HOLD101452_443785) XBOX1_CONTROLLER_HOLD101452_443785 -=get_rtime(); if(XBOX1_CONTROLLER_HOLD101452_443785){ if(event_press(PS4_L3)){ XBOX1_CONTROLLER_HOLD382632_533959 = !XBOX1_CONTROLLER_HOLD382632_533959;
 f_set_notify(XBOX1_CONTROLLER_HOLD382632_533959); } }else if(event_press(PS4_L3)){ XBOX1_CONTROLLER_HOLD101452_443785 = 300;
 } } if( XBOX1_CONTROLLER_HOLD374691_869672 == ModifierL3) { XBOX1_CONTROLLER_HOLD527003_151861 = PS4_L3;
 XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 } if( XBOX1_CONTROLLER_HOLD374691_869672 == ModifierPR1) { XBOX1_CONTROLLER_HOLD527003_151861 = XB1_PR1;
 XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 } if( XBOX1_CONTROLLER_HOLD374691_869672 == ModifierPR2) { XBOX1_CONTROLLER_HOLD527003_151861 = XB1_PR2;
 XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 } if( XBOX1_CONTROLLER_HOLD374691_869672 == ModifierPL1) { XBOX1_CONTROLLER_HOLD527003_151861 = XB1_PL1;
 XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 } if( XBOX1_CONTROLLER_HOLD374691_869672 == ModifierPL2) { XBOX1_CONTROLLER_HOLD527003_151861 = XB1_PL2;
 XBOX1_CONTROLLER_HOLD382632_533959 = FALSE;
 } if(XBOX1_CONTROLLER_HOLD382632_533959 && XBOX1_CONTROLLER_HOLD940080_712320){ f_RightStickMove(); } if(XBOX1_CONTROLLER_HOLD374691_869672 > 2 && XBOX1_CONTROLLER_HOLD940080_712320){ if(get_val(XBOX1_CONTROLLER_HOLD527003_151861)){ f_RightStickMove(); } } if(XBOX1_CONTROLLER_HOLD288357_290232){ if(event_press(XB1_PR1)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD288357_290232;
 run_skill_combo(XBOX1_CONTROLLER_HOLD288357_290232); } } if(XBOX1_CONTROLLER_HOLD883498_810572){ if(event_press(XB1_PL1)){ XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD883498_810572;
 run_skill_combo(XBOX1_CONTROLLER_HOLD883498_810572); } } if(XBOX1_CONTROLLER_HOLD263496_792006){ if(event_press(XB1_PR2)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD263496_792006;
 run_skill_combo(XBOX1_CONTROLLER_HOLD263496_792006); } } if(XBOX1_CONTROLLER_HOLD446722_737573){ if(event_press(XB1_PL2)){ XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD446722_737573;
 run_skill_combo(XBOX1_CONTROLLER_HOLD446722_737573); } } if(get_val(XBOX1_CONTROLLER_HOLD782577_342508)){ if(XBOX1_CONTROLLER_HOLD562128_307172 ){ if(event_press(PS4_L1)){ XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD562128_307172 ;
 run_skill_combo( XBOX1_CONTROLLER_HOLD562128_307172 ); } if(event_press(PS4_R1)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD562128_307172 ;
 run_skill_combo( XBOX1_CONTROLLER_HOLD562128_307172 ); } set_val(PS4_L1,0); set_val(PS4_R1,0); } if( XBOX1_CONTROLLER_HOLD344762_620295 ){ if(event_press(PS4_SQUARE)){ XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD344762_620295 ;
run_skill_combo( XBOX1_CONTROLLER_HOLD344762_620295 ); } if(event_press(PS4_TRIANGLE)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD344762_620295 ;
run_skill_combo( XBOX1_CONTROLLER_HOLD344762_620295 ); } set_val(PS4_SQUARE,0); set_val(PS4_TRIANGLE,0); } if( XBOX1_CONTROLLER_HOLD346771_740680 ){ if(event_press(PS4_CROSS)){ XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD346771_740680 ;
run_skill_combo( XBOX1_CONTROLLER_HOLD346771_740680 ); } if(event_press(PS4_CIRCLE)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD346771_740680 ;
run_skill_combo( XBOX1_CONTROLLER_HOLD346771_740680 ); } set_val(PS4_CROSS,0); set_val(PS4_CIRCLE,0); } } if(XBOX1_CONTROLLER_HOLD383131_770525) f_defence(); if(XBOX1_CONTROLLER_HOLD731824_426854 && XBOX1_CONTROLLER_HOLD364021_522869){ f_dynamic_finish (); } if(XBOX1_CONTROLLER_HOLD298206_272790 == HEEL_FLICK_TURN_SKILL && combo_running(XBOX1_CONTROLLER_HOLD726245_507825)) set_val(FinesseShot,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == FAKE_PASS_SKILL && combo_running(XBOX1_CONTROLLER_HOLD575803_704291)) set_val(SprintBtn,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == RONALDO_CHOP_SKILL && combo_running(XBOX1_CONTROLLER_HOLD575803_704291)) set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == FLAIR_ROULETTE_SKILL && combo_running(XBOX1_CONTROLLER_HOLD990448_252941)) set_val(PlayerRun,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == BOLASIE_FLICK_SKILL && combo_running(XBOX1_CONTROLLER_HOLD823336_190301)) set_val(FinesseShot,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == TORNADO_SKILL && combo_running(XBOX1_CONTROLLER_HOLD667463_690851)) set_val(PlayerRun,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == THREE_TOUCH_ROULETTE_SKILL && combo_running(XBOX1_CONTROLLER_HOLD667463_690851)) set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == ALTERNATIVE_ELASTICO_CHOP_SKILL && combo_running(XBOX1_CONTROLLER_HOLD667463_690851)) set_val(FinesseShot,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == FEINT_AND_EXIT_SKILL && combo_running(XBOX1_CONTROLLER_HOLD387740_505538)) set_val(PlayerRun,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == DRAG_TO_HEEL && combo_running(XBOX1_CONTROLLER_HOLD667463_690851)) set_val(PlayerRun,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == BALL_ROLL_FAKE_TURN && combo_running(XBOX1_CONTROLLER_HOLD823336_190301)) set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); if(XBOX1_CONTROLLER_HOLD298206_272790 == FAKE_DRAG_BACK_SKILL && combo_running(XBOX1_CONTROLLER_HOLD627070_955826)) { set_val(PlayerRun,100); set_val(FinesseShot,100); } if( XBOX1_CONTROLLER_HOLD298206_272790 == DRIBBLING_SKILL && combo_running(XBOX1_CONTROLLER_HOLD124380_100594)) { sensitivity(PS4_LX,NOT_USE,90); sensitivity(PS4_LY,NOT_USE,90); } } } } } combo XBOX1_CONTROLLER_HOLD235866_386279 { set_val(FinesseShot,100); set_val(PlayerRun,100); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD197808_727695); } int XBOX1_CONTROLLER_HOLD970046_225549;
 combo XBOX1_CONTROLLER_HOLD344425_426621 { set_val(SprintBtn, 0); set_val(MODIFIER, 100); wait(L1_TIME); set_val(MODIFIER, 0); XBOX1_CONTROLLER_HOLD509548_271511 = 1;
 } combo XBOX1_CONTROLLER_HOLD701978_935188 { set_rumble(XBOX1_CONTROLLER_HOLD970046_225549,100); wait(300); reset_rumble(); wait(20); } function f_set_notify (f_val){ if(f_val)XBOX1_CONTROLLER_HOLD970046_225549 = RUMBLE_A;
 else XBOX1_CONTROLLER_HOLD970046_225549 = RUMBLE_B;
 combo_run(XBOX1_CONTROLLER_HOLD701978_935188); } function BlockAllOutputs() { for(XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 30;
 XBOX1_CONTROLLER_HOLD243440_708731++) { if(get_ival(BlockBTNs[XBOX1_CONTROLLER_HOLD243440_708731]) || event_press(BlockBTNs[XBOX1_CONTROLLER_HOLD243440_708731])) { set_val(BlockBTNs[XBOX1_CONTROLLER_HOLD243440_708731], 0); } } } function iif(Expression, TrueVar, FalseVar) { if(Expression) return TrueVar;
 return FalseVar;
 } function Cycle(value, low, high) { ps5_moves = random(1, 6000); return iif(value > high, low, iif(value < low, high, value)); } enum { alignCenter = 0xfffe, alignRight, alignLeft = 0x5, alignBottom = 0xffff, alignTop = 0x5 } const string EnterCodeToAccess [] = { "Enter Access Code" }; const string Welcome [] = { "Welcome" }; const string And [] = { "And" }; const string Enjoy [] = { "Enjoy!" }; const string Incorrect [] = { "Incorrect!" }; const uint8 fontHeight [] = { OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT }; const uint8 fontWidth [] = { OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH }; const uint8 BlockBTNs [] = { PS4_UP, PS4_DOWN, PS4_LEFT, PS4_RIGHT, PS4_CROSS, PS4_CIRCLE, PS4_SQUARE, PS4_TRIANGLE, PS4_L1, PS4_R1, PS4_L3, PS4_R3, PS4_OPTIONS, PS4_SHARE, PS4_TOUCH, PS4_PS, PS4_R2, PS4_RY, PS4_L2, PS4_LY, PS4_RX, PS4_LX, PS4_ACCX, PS4_ACCY, PS4_ACCZ, PS4_GYROX, PS4_GYROY, PS4_GYROZ, XB1_PL1, XB1_PL2, XB1_PR1, XB1_PR2 }; function insertCharacter(Value) { putc_oled(XBOX1_CONTROLLER_HOLD687228_876037, Value); XBOX1_CONTROLLER_HOLD687228_876037++; } function insertNumber(Value) { if(Value < 0) { insertCharacter(ASCII_MINUS); Value = abs(Value); } displayBufferInsertCopyValue = Value;
 displayBufferInsertNumberOfDigits = 0;
 do { displayBufferInsertCopyValue /= 10;
 displayBufferInsertNumberOfDigits++; } while(displayBufferInsertCopyValue) for(XBOX1_CONTROLLER_HOLD312585_309749 = 0;
 XBOX1_CONTROLLER_HOLD312585_309749 < displayBufferInsertNumberOfDigits;
 XBOX1_CONTROLLER_HOLD312585_309749++) { XBOX1_CONTROLLER_HOLD615209_524043[XBOX1_CONTROLLER_HOLD312585_309749] = (Value % 10) + 48;
 Value /= 10;
 } for(XBOX1_CONTROLLER_HOLD312585_309749 = displayBufferInsertNumberOfDigits - 1;
 XBOX1_CONTROLLER_HOLD312585_309749 >= 0;
 XBOX1_CONTROLLER_HOLD312585_309749--) { insertCharacter(XBOX1_CONTROLLER_HOLD615209_524043[XBOX1_CONTROLLER_HOLD312585_309749]); } } function flushBuffer(X, Y, Size, Color) { XBOX1_CONTROLLER_HOLD687228_876037--; switch(X) { case alignRight { X = OLED_WIDTH - (XBOX1_CONTROLLER_HOLD687228_876037 * fontWidth[Size]) - 4;
 break;
 } case alignCenter { X = (OLED_WIDTH >> 1) - ((XBOX1_CONTROLLER_HOLD687228_876037 * fontWidth[Size]) >> 1); break;
 } } switch(Y) { case alignCenter { Y = (OLED_HEIGHT >> 1) - (fontHeight[Size] >> 1); break;
 } case alignBottom { Y = OLED_HEIGHT - fontHeight[Size] - 4;
 break;
 } } puts_oled(X, Y, Size, XBOX1_CONTROLLER_HOLD687228_876037, Color); XBOX1_CONTROLLER_HOLD687228_876037 = 1;
 } function insertString(s) { do { insertCharacter(dint8(s)); s++; } while(dint8(s)) } function printNumber(X, Y, Number, Size) { insertNumber(Number); flushBuffer(X, Y, Size, OLED_WHITE); } function printText(X, Y, Text, Size) { insertString(Text); flushBuffer(X, Y, Size, OLED_WHITE); } function Load_PVARs() { ResetSPVAR(); PS5_R3_RIGHT = ReadSPVAR(0, 1, 0); } function SavePVARS() { ResetSPVAR(); SaveSPVAR(PS5_R3_RIGHT, 0, 1); } int XBOX1_CONTROLLER_HOLD722017_726583, SPVAR_Current_Slot, SPVAR_Current_Value, SPVARTmp, SPVARBits;
 function ResetSPVAR() { SPVAR_Current_Slot = SPVAR_1;
 XBOX1_CONTROLLER_HOLD722017_726583 = 0;
 SPVAR_Current_Value = 0;
 } function Get_Bit_Count(val) { SPVARTmp = 0;
 while (val) { SPVARTmp++; val = abs(val >> 1); } return SPVARTmp;
 } function Get_Bit_Count2(val1, val2) { SPVARTmp = max(Get_Bit_Count(val1), Get_Bit_Count(val2)); return iif(Is_Signed2(val1, val2), SPVARTmp + 1, SPVARTmp); } function Is_Signed2(val1, val2) { return (val1 < 0) || (val2 < 0); } function Make_Sign(bits) { return 1 << clamp(bits - 1, 0, 31); } function Make_Full_Mask(bits) { return iif(bits == 32, inv(1), 0x7FFFFFFF >> (31 - bits)); } function Make_Sign_Mask(bits) { return Make_Full_Mask(bits - 1); } function Pack_I(val, bits) { return iif(val < 0, (abs(val) & Make_Sign_Mask(bits)) | Make_Sign(bits), val & Make_Sign_Mask(bits)); } function Unpack_I(val, bits) { return iif(val & Make_Sign(bits), 0 - (val & Make_Sign_Mask(bits)), val & Make_Sign_Mask(bits)); } function Read_SPVAR_Slot(slot) { return get_pvar(slot, 0x80000000, 0x7FFFFFFF, 0); } function SaveSPVAR(val, min, max) { SPVARBits = Get_Bit_Count2(min, max); val = clamp(val, min, max); if(Is_Signed2(min, max)) { val = Pack_I(val, SPVARBits); } val = iif(SPVARBits == 32, val, val & (0x7FFFFFFF >> (31 - SPVARBits))); if(SPVARBits >= 32 - XBOX1_CONTROLLER_HOLD722017_726583) { SPVAR_Current_Value = SPVAR_Current_Value | (val << XBOX1_CONTROLLER_HOLD722017_726583); set_pvar(SPVAR_Current_Slot, SPVAR_Current_Value); SPVAR_Current_Slot++; SPVARBits -= (32 - XBOX1_CONTROLLER_HOLD722017_726583); val = val >> (32 - XBOX1_CONTROLLER_HOLD722017_726583); XBOX1_CONTROLLER_HOLD722017_726583 = 0;
 SPVAR_Current_Value = 0;
 } SPVAR_Current_Value = SPVAR_Current_Value | (val << XBOX1_CONTROLLER_HOLD722017_726583); XBOX1_CONTROLLER_HOLD722017_726583 += SPVARBits;
 if(!XBOX1_CONTROLLER_HOLD722017_726583) { SPVAR_Current_Value = 0;
 } set_pvar(SPVAR_Current_Slot, SPVAR_Current_Value); } function ReadSPVAR(min, max, def) { SPVARBits = Get_Bit_Count2(min, max); SPVAR_Current_Value = (Read_SPVAR_Slot(SPVAR_Current_Slot) >> XBOX1_CONTROLLER_HOLD722017_726583) & Make_Full_Mask(SPVARBits); if(SPVARBits >= 32 - XBOX1_CONTROLLER_HOLD722017_726583) { SPVAR_Current_Value = (SPVAR_Current_Value & Make_Full_Mask(32 - XBOX1_CONTROLLER_HOLD722017_726583)) | ((Read_SPVAR_Slot(SPVAR_Current_Slot + 1) & Make_Full_Mask(SPVARBits - (32 - XBOX1_CONTROLLER_HOLD722017_726583))) << (32 - XBOX1_CONTROLLER_HOLD722017_726583)); } XBOX1_CONTROLLER_HOLD722017_726583 += SPVARBits;
 if(XBOX1_CONTROLLER_HOLD722017_726583 >= 32) { SPVAR_Current_Slot++; XBOX1_CONTROLLER_HOLD722017_726583 -= 32;
 } if(Is_Signed2(min, max)) { SPVAR_Current_Value = Unpack_I(SPVAR_Current_Value, SPVARBits); } return iif(SPVAR_Current_Value < min || SPVAR_Current_Value > max, def, SPVAR_Current_Value); } #define XBOX1_CONTROLLER_HOLD863446_916052 = 1 #define XBOX1_CONTROLLER_HOLD125345_757431 = 2 #define XBOX1_CONTROLLER_HOLD243980_246274 = 3 #define XBOX1_CONTROLLER_HOLD705031_238424 = 4 #define XBOX1_CONTROLLER_HOLD819365_873270 = 5 #define XBOX1_CONTROLLER_HOLD535109_479048 = 6 #define XBOX1_CONTROLLER_HOLD593226_855586 = 7 #define XBOX1_CONTROLLER_HOLD565232_279744 = 8 #define XBOX1_CONTROLLER_HOLD416172_847904 = 9 #define XBOX1_CONTROLLER_HOLD358809_622292 = 10 #define XBOX1_CONTROLLER_HOLD551780_859326 = 11 #define XBOX1_CONTROLLER_HOLD422232_432284 = 12 #define XBOX1_CONTROLLER_HOLD673928_614444 = 13 #define XBOX1_CONTROLLER_HOLD145582_241424 = 14 #define XBOX1_CONTROLLER_HOLD759792_538446 = 15 #define XBOX1_CONTROLLER_HOLD199008_585150 = 16 #define XBOX1_CONTROLLER_HOLD841821_601086 = 17 #define XBOX1_CONTROLLER_HOLD248426_182439 = 18 #define XBOX1_CONTROLLER_HOLD718125_973247 = 19 #define XBOX1_CONTROLLER_HOLD433423_220721 = 20 #define XBOX1_CONTROLLER_HOLD404647_382456 = 21 #define XBOX1_CONTROLLER_HOLD261135_766292 = 22 #define XBOX1_CONTROLLER_HOLD596667_899425 = 23 #define XBOX1_CONTROLLER_HOLD278463_913677 = 24 #define XBOX1_CONTROLLER_HOLD695342_930846 = 25 #define XBOX1_CONTROLLER_HOLD626943_148146 = 26 #define XBOX1_CONTROLLER_HOLD478376_466202 = 27 #define XBOX1_CONTROLLER_HOLD414796_196040 = 28 #define XBOX1_CONTROLLER_HOLD701179_653493 = 29 #define XBOX1_CONTROLLER_HOLD825753_403021 = 30 #define XBOX1_CONTROLLER_HOLD795152_208579 = 31 #define XBOX1_CONTROLLER_HOLD864558_521884 = 32 #define XBOX1_CONTROLLER_HOLD778954_395852 = 33 #define XBOX1_CONTROLLER_HOLD738882_618038 = 34 #define XBOX1_CONTROLLER_HOLD620704_340819 = 35 #define XBOX1_CONTROLLER_HOLD471233_682043 = 36 #define XBOX1_CONTROLLER_HOLD403870_229700 = 37 #define XBOX1_CONTROLLER_HOLD726471_259941 = 38 #define XBOX1_CONTROLLER_HOLD474697_368312 = 39 #define XBOX1_CONTROLLER_HOLD641587_235088 = 40 #define XBOX1_CONTROLLER_HOLD662661_211417 = 41 #define XBOX1_CONTROLLER_HOLD122837_391676 = 42 #define XBOX1_CONTROLLER_HOLD336273_936828 = 43 #define XBOX1_CONTROLLER_HOLD879429_903774 = 44 #define XBOX1_CONTROLLER_HOLD170009_948860 = 45 #define XBOX1_CONTROLLER_HOLD263546_704648 = 46 #define XBOX1_CONTROLLER_HOLD522018_174301 = 47 #define XBOX1_CONTROLLER_HOLD394973_747614 = 48 #define XBOX1_CONTROLLER_HOLD793761_530714 = 49 #define XBOX1_CONTROLLER_HOLD642384_226653 = 50 #define XBOX1_CONTROLLER_HOLD344687_716105 = 51 #define XBOX1_CONTROLLER_HOLD359637_613756 = 52 #define XBOX1_CONTROLLER_HOLD863548_790601 = 53 #define XBOX1_CONTROLLER_HOLD946365_631591 = 54 #define XBOX1_CONTROLLER_HOLD626078_649192 = 55 #define XBOX1_CONTROLLER_HOLD817233_974965 = 56 #define XBOX1_CONTROLLER_HOLD253907_772381 = 57 #define XBOX1_CONTROLLER_HOLD397704_390889 = 58 #define XBOX1_CONTROLLER_HOLD652928_658146 = 59 #define XBOX1_CONTROLLER_HOLD176353_373892 = 60 #define XBOX1_CONTROLLER_HOLD669027_367360 = 61 #define XBOX1_CONTROLLER_HOLD537454_963363 = 62 #define XBOX1_CONTROLLER_HOLD831362_138666 = 63 #define XBOX1_CONTROLLER_HOLD961992_403212 = 64 #define XBOX1_CONTROLLER_HOLD994082_266713 = 65 #define XBOX1_CONTROLLER_HOLD849425_652521 = 66 #define XBOX1_CONTROLLER_HOLD703851_522910 = 67 #define XBOX1_CONTROLLER_HOLD677952_807841 = 68 #define XBOX1_CONTROLLER_HOLD673843_638962 = 69 #define XBOX1_CONTROLLER_HOLD112358_660226 = 70 #define XBOX1_CONTROLLER_HOLD649342_162046 = 71 #define XBOX1_CONTROLLER_HOLD514068_523186 = 72 #define XBOX1_CONTROLLER_HOLD298340_290646 = 73 #define XBOX1_CONTROLLER_HOLD304441_773391 = 74 #define XBOX1_CONTROLLER_HOLD365584_586662 = 75 #define XBOX1_CONTROLLER_HOLD635559_228769 = 76 #define XBOX1_CONTROLLER_HOLD885340_672400 = 77 #define XBOX1_CONTROLLER_HOLD257656_176798 = 78 #define XBOX1_CONTROLLER_HOLD518998_409130 = 79 #define XBOX1_CONTROLLER_HOLD795921_731057 = 80 #define XBOX1_CONTROLLER_HOLD367352_870089 = 81 #define XBOX1_CONTROLLER_HOLD473197_457932 = 82 #define XBOX1_CONTROLLER_HOLD595561_854883 = 83 #define XBOX1_CONTROLLER_HOLD100152_437263 = 84 #define XBOX1_CONTROLLER_HOLD616077_747843 = 85 #define XBOX1_CONTROLLER_HOLD594224_859607 = 86 #define XBOX1_CONTROLLER_HOLD203521_777867 = 87 #define XBOX1_CONTROLLER_HOLD128740_679150 = 88 #define XBOX1_CONTROLLER_HOLD937562_523954 = 89 #define XBOX1_CONTROLLER_HOLD595155_767886 = 90 #define XBOX1_CONTROLLER_HOLD302640_589888 = 91 #define XBOX1_CONTROLLER_HOLD129598_158991 = 92 #define XBOX1_CONTROLLER_HOLD843379_180644 = 93 #define XBOX1_CONTROLLER_HOLD192443_967675 = 94 #define XBOX1_CONTROLLER_HOLD121343_713661 = 95 #define XBOX1_CONTROLLER_HOLD917128_783530 = 96 #define XBOX1_CONTROLLER_HOLD219558_491880 = 97 #define XBOX1_CONTROLLER_HOLD558218_184486 = 98 #define XBOX1_CONTROLLER_HOLD367873_735286 = 99 #define XBOX1_CONTROLLER_HOLD310492_270222 = 100 #define XBOX1_CONTROLLER_HOLD373750_877863 = 101 #define XBOX1_CONTROLLER_HOLD584253_432142 = 102 #define XBOX1_CONTROLLER_HOLD520215_160408 = 103 #define XBOX1_CONTROLLER_HOLD882023_989576 = 104 #define XBOX1_CONTROLLER_HOLD429885_342539 = 105 #define XBOX1_CONTROLLER_HOLD852841_227981 = 106 #define XBOX1_CONTROLLER_HOLD502954_157939 = 107 #define XBOX1_CONTROLLER_HOLD128886_534221 = 108 #define XBOX1_CONTROLLER_HOLD899939_542945 = 109 #define XBOX1_CONTROLLER_HOLD655707_169749 = 110 #define XBOX1_CONTROLLER_HOLD279155_212159 = 111 #define XBOX1_CONTROLLER_HOLD408937_169361 = 112 #define XBOX1_CONTROLLER_HOLD190212_674441 = 113 #define XBOX1_CONTROLLER_HOLD200412_574371 = 114 #define XBOX1_CONTROLLER_HOLD479888_226923 = 115 #define XBOX1_CONTROLLER_HOLD284141_537734 = 116 #define XBOX1_CONTROLLER_HOLD622726_115831 = 117 #define XBOX1_CONTROLLER_HOLD222383_251232 = 118 #define XBOX1_CONTROLLER_HOLD291797_150515 = 119 #define XBOX1_CONTROLLER_HOLD131659_419302 = 120 #define XBOX1_CONTROLLER_HOLD255721_314977 = 121 #define XBOX1_CONTROLLER_HOLD142920_306089 = 122 #define XBOX1_CONTROLLER_HOLD209172_471829 = 123 #define XBOX1_CONTROLLER_HOLD324227_788462 = 124 #define XBOX1_CONTROLLER_HOLD108415_941714 = 125 #define XBOX1_CONTROLLER_HOLD727612_665729 = 126 #define XBOX1_CONTROLLER_HOLD700361_240147 = 127 #define XBOX1_CONTROLLER_HOLD286753_813146 = 128 #define XBOX1_CONTROLLER_HOLD296204_324478 = 129 #define XBOX1_CONTROLLER_HOLD584467_725777 = 130 #define XBOX1_CONTROLLER_HOLD972584_150873 = 131 #define XBOX1_CONTROLLER_HOLD298709_939090 = 132 #define XBOX1_CONTROLLER_HOLD451054_180493 = 133 #define XBOX1_CONTROLLER_HOLD452934_425283 = 134 #define XBOX1_CONTROLLER_HOLD476239_242492 = 135 #define XBOX1_CONTROLLER_HOLD319219_337470 = 136 #define XBOX1_CONTROLLER_HOLD789203_816067 = 137 #define XBOX1_CONTROLLER_HOLD278053_791210 = 138 #define XBOX1_CONTROLLER_HOLD868247_349776 = 139 #define XBOX1_CONTROLLER_HOLD425281_831857 = 140 #define XBOX1_CONTROLLER_HOLD287620_212665 = 141 #define XBOX1_CONTROLLER_HOLD476265_494752 = 142 #define XBOX1_CONTROLLER_HOLD997819_493336 = 143 #define XBOX1_CONTROLLER_HOLD870585_119925 = 144 #define XBOX1_CONTROLLER_HOLD360251_446621 = 145 #define XBOX1_CONTROLLER_HOLD909050_495605 = 146 #define XBOX1_CONTROLLER_HOLD181776_313754 = 147 #define XBOX1_CONTROLLER_HOLD333954_349230 = 148 #define XBOX1_CONTROLLER_HOLD545550_436967 = 149 #define XBOX1_CONTROLLER_HOLD206457_370552 = 150 #define XBOX1_CONTROLLER_HOLD263264_582971 = 151 #define XBOX1_CONTROLLER_HOLD444557_400278 = 152 #define XBOX1_CONTROLLER_HOLD805343_285443 = 153 #define XBOX1_CONTROLLER_HOLD785841_578274 = 154 #define XBOX1_CONTROLLER_HOLD724895_157746 = 155 #define XBOX1_CONTROLLER_HOLD663423_223970 = 156 #define XBOX1_CONTROLLER_HOLD606653_608538 = 157 #define XBOX1_CONTROLLER_HOLD349424_127690 = 158 #define XBOX1_CONTROLLER_HOLD579377_198781 = 159 #define XBOX1_CONTROLLER_HOLD984950_662035 = 160 #define XBOX1_CONTROLLER_HOLD783596_707847 = 161 #define XBOX1_CONTROLLER_HOLD366559_637132 = 162 #define XBOX1_CONTROLLER_HOLD483699_228301 = 163 #define XBOX1_CONTROLLER_HOLD960470_884696 = 164 #define XBOX1_CONTROLLER_HOLD473983_896681 = 165 #define XBOX1_CONTROLLER_HOLD296812_822214 = 166 #define XBOX1_CONTROLLER_HOLD547989_926780 = 167 #define XBOX1_CONTROLLER_HOLD404670_465749 = 168 #define XBOX1_CONTROLLER_HOLD506188_721375 = 169 #define XBOX1_CONTROLLER_HOLD337900_468574 = 170 #define XBOX1_CONTROLLER_HOLD620452_763936 = 171 #define XBOX1_CONTROLLER_HOLD626524_896490 = 172 #define XBOX1_CONTROLLER_HOLD118142_674660 = 173 #define XBOX1_CONTROLLER_HOLD398741_609419 = 174 #define XBOX1_CONTROLLER_HOLD707204_570100 = 175 #define XBOX1_CONTROLLER_HOLD767519_249523 = 176 #define XBOX1_CONTROLLER_HOLD594969_474729 = 177 #define XBOX1_CONTROLLER_HOLD180841_162990 = 178 #define XBOX1_CONTROLLER_HOLD701556_897273 = 179 #define XBOX1_CONTROLLER_HOLD754948_333961 = 180 #define XBOX1_CONTROLLER_HOLD911221_942980 = 181 #define XBOX1_CONTROLLER_HOLD678200_388017 = 182 #define XBOX1_CONTROLLER_HOLD589885_138225 = 183 #define XBOX1_CONTROLLER_HOLD630687_966928 = 184 #define XBOX1_CONTROLLER_HOLD708337_534479 = 185 #define XBOX1_CONTROLLER_HOLD344903_767806 = 186 #define XBOX1_CONTROLLER_HOLD879797_922900 = 187 #define XBOX1_CONTROLLER_HOLD569072_994499 = 188 #define XBOX1_CONTROLLER_HOLD291799_897243 = 189 #define XBOX1_CONTROLLER_HOLD703918_645728 = 190 #define XBOX1_CONTROLLER_HOLD812966_515411 = 191 #define XBOX1_CONTROLLER_HOLD241912_740983 = 192 #define XBOX1_CONTROLLER_HOLD825430_522658 = 193 #define XBOX1_CONTROLLER_HOLD317547_931717 = 194 #define XBOX1_CONTROLLER_HOLD767559_680979 = 195 #define XBOX1_CONTROLLER_HOLD655414_917880 = 196 #define XBOX1_CONTROLLER_HOLD230494_195393 = 197 #define XBOX1_CONTROLLER_HOLD513941_335335 = 198 #define XBOX1_CONTROLLER_HOLD200131_897438 = 199 #define XBOX1_CONTROLLER_HOLD738152_132095 = 200 #define XBOX1_CONTROLLER_HOLD836587_576320 = 201 #define XBOX1_CONTROLLER_HOLD184475_633405 = 202 #define XBOX1_CONTROLLER_HOLD557911_636712 = 203 #define XBOX1_CONTROLLER_HOLD547820_528645 = 204 #define XBOX1_CONTROLLER_HOLD349293_801311 = 205 #define XBOX1_CONTROLLER_HOLD121417_877038 = 206 #define XBOX1_CONTROLLER_HOLD496287_942752 = 207 #define XBOX1_CONTROLLER_HOLD682369_682097 = 208 #define XBOX1_CONTROLLER_HOLD118875_331188 = 209 #define XBOX1_CONTROLLER_HOLD739775_390621 = 210 #define XBOX1_CONTROLLER_HOLD278498_918435 = 211 #define XBOX1_CONTROLLER_HOLD279159_424282 = 212 #define XBOX1_CONTROLLER_HOLD497156_646867 = 213 #define XBOX1_CONTROLLER_HOLD936906_368255 = 214 #define XBOX1_CONTROLLER_HOLD911411_586467 = 215 #define XBOX1_CONTROLLER_HOLD392526_611680 = 216 #define XBOX1_CONTROLLER_HOLD626003_261564 = 217 #define XBOX1_CONTROLLER_HOLD205386_581543 = 218 #define XBOX1_CONTROLLER_HOLD631522_755704 = 219 #define XBOX1_CONTROLLER_HOLD923490_847365 = 220 #define XBOX1_CONTROLLER_HOLD125358_531694 = 221 #define XBOX1_CONTROLLER_HOLD899126_619331 = 222 #define XBOX1_CONTROLLER_HOLD984429_250025 = 223 #define XBOX1_CONTROLLER_HOLD966520_248315 = 224 #define XBOX1_CONTROLLER_HOLD882094_524676 = 225 #define XBOX1_CONTROLLER_HOLD977574_593330 = 226 #define XBOX1_CONTROLLER_HOLD475087_434183 = 227 #define XBOX1_CONTROLLER_HOLD718417_127277 = 228 #define XBOX1_CONTROLLER_HOLD727563_338176 = 229 #define XBOX1_CONTROLLER_HOLD806989_331748 = 230 #define XBOX1_CONTROLLER_HOLD611612_990856 = 231 #define XBOX1_CONTROLLER_HOLD362189_260475 = 232 #define XBOX1_CONTROLLER_HOLD891101_722571 = 233 #define XBOX1_CONTROLLER_HOLD925442_541290 = 234 #define XBOX1_CONTROLLER_HOLD152077_682521 = 235 #define XBOX1_CONTROLLER_HOLD524358_404639 = 236 #define XBOX1_CONTROLLER_HOLD964657_478701 = 237 #define XBOX1_CONTROLLER_HOLD954952_472712 = 238 #define XBOX1_CONTROLLER_HOLD440933_391600 = 239 #define XBOX1_CONTROLLER_HOLD842592_898416 = 240 #define XBOX1_CONTROLLER_HOLD565153_110599 = 241 #define XBOX1_CONTROLLER_HOLD274203_918533 = 242 #define XBOX1_CONTROLLER_HOLD794390_305656 = 243 #define XBOX1_CONTROLLER_HOLD520389_689991 = 244 #define XBOX1_CONTROLLER_HOLD839267_791140 = 245 #define XBOX1_CONTROLLER_HOLD430331_697702 = 246 #define XBOX1_CONTROLLER_HOLD309923_507900 = 247 #define XBOX1_CONTROLLER_HOLD169052_303865 = 248 #define XBOX1_CONTROLLER_HOLD232099_152906 = 249 #define XBOX1_CONTROLLER_HOLD461825_719005 = 250 #define XBOX1_CONTROLLER_HOLD480106_353072 = 251 #define XBOX1_CONTROLLER_HOLD688581_527849 = 252 #define XBOX1_CONTROLLER_HOLD836742_569116 = 253 #define XBOX1_CONTROLLER_HOLD418949_301220 = 254 #define XBOX1_CONTROLLER_HOLD163114_903919 = 255 #define XBOX1_CONTROLLER_HOLD343771_967737 = 256 #define XBOX1_CONTROLLER_HOLD264626_266916 = 257 #define XBOX1_CONTROLLER_HOLD934295_653155 = 258 #define XBOX1_CONTROLLER_HOLD108985_995739 = 259 #define XBOX1_CONTROLLER_HOLD807872_652808 = 260 #define XBOX1_CONTROLLER_HOLD876295_236399 = 261 #define XBOX1_CONTROLLER_HOLD408260_727967 = 262 #define XBOX1_CONTROLLER_HOLD458533_588215 = 263 #define XBOX1_CONTROLLER_HOLD506052_628348 = 264 #define XBOX1_CONTROLLER_HOLD285869_481164 = 265 #define XBOX1_CONTROLLER_HOLD951896_906621 = 266 #define XBOX1_CONTROLLER_HOLD187855_459999 = 267 #define XBOX1_CONTROLLER_HOLD498667_176796 = 268 #define XBOX1_CONTROLLER_HOLD467403_670225 = 269 #define XBOX1_CONTROLLER_HOLD152919_863467 = 270 #define XBOX1_CONTROLLER_HOLD993083_580854 = 271 #define XBOX1_CONTROLLER_HOLD747643_430766 = 272 #define XBOX1_CONTROLLER_HOLD935094_903383 = 273 #define XBOX1_CONTROLLER_HOLD315952_606162 = 274 #define XBOX1_CONTROLLER_HOLD331201_158645 = 275 #define XBOX1_CONTROLLER_HOLD933423_450977 = 276 #define XBOX1_CONTROLLER_HOLD378662_266475 = 277 #define XBOX1_CONTROLLER_HOLD412992_756383 = 278 #define XBOX1_CONTROLLER_HOLD508861_185226 = 279 #define XBOX1_CONTROLLER_HOLD925026_525502 = 280 #define XBOX1_CONTROLLER_HOLD388211_233949 = 281 #define XBOX1_CONTROLLER_HOLD774523_378919 = 282 #define XBOX1_CONTROLLER_HOLD654554_794782 = 283 #define XBOX1_CONTROLLER_HOLD378863_189984 = 284 #define XBOX1_CONTROLLER_HOLD264548_472673 = 285 #define XBOX1_CONTROLLER_HOLD590963_352867 = 286 #define XBOX1_CONTROLLER_HOLD726509_437630 = 287 #define XBOX1_CONTROLLER_HOLD610713_474376 = 288 #define XBOX1_CONTROLLER_HOLD608433_963696 = 289 #define XBOX1_CONTROLLER_HOLD809876_815992 = 290 #define XBOX1_CONTROLLER_HOLD509538_544925 = 291 #define XBOX1_CONTROLLER_HOLD877110_803622 = 292 #define XBOX1_CONTROLLER_HOLD170098_787545 = 293 #define XBOX1_CONTROLLER_HOLD216903_458364 = 294 #define XBOX1_CONTROLLER_HOLD166455_414530 = 295 #define XBOX1_CONTROLLER_HOLD926427_894801 = 296 #define XBOX1_CONTROLLER_HOLD582423_134093 = 297 #define XBOX1_CONTROLLER_HOLD189565_842415 = 298 #define XBOX1_CONTROLLER_HOLD529488_743643 = 299 #define XBOX1_CONTROLLER_HOLD218301_713665 = 300 #define XBOX1_CONTROLLER_HOLD258697_213948 = 301 #define XBOX1_CONTROLLER_HOLD289415_515854 = 302 #define XBOX1_CONTROLLER_HOLD664754_254318 = 303 #define XBOX1_CONTROLLER_HOLD740624_216578 = 304 #define XBOX1_CONTROLLER_HOLD945837_457870 = 305 #define XBOX1_CONTROLLER_HOLD136523_817921 = 306 #define XBOX1_CONTROLLER_HOLD551302_662090 = 307 #define XBOX1_CONTROLLER_HOLD782972_532732 = 308 #define XBOX1_CONTROLLER_HOLD331879_687023 = 309 #define XBOX1_CONTROLLER_HOLD558972_651878 = 310 #define XBOX1_CONTROLLER_HOLD655883_934600 = 311 #define XBOX1_CONTROLLER_HOLD874990_311388 = 312 #define XBOX1_CONTROLLER_HOLD217200_910559 = 313 #define XBOX1_CONTROLLER_HOLD163600_982973 = 314 #define XBOX1_CONTROLLER_HOLD407954_775192 = 315 #define XBOX1_CONTROLLER_HOLD423630_788053 = 316 #define XBOX1_CONTROLLER_HOLD525191_387515 = 317 #define XBOX1_CONTROLLER_HOLD471448_807620 = 318 #define XBOX1_CONTROLLER_HOLD579225_338703 = 319 #define XBOX1_CONTROLLER_HOLD987670_165570 = 320 #define XBOX1_CONTROLLER_HOLD512495_115253 = 321 #define XBOX1_CONTROLLER_HOLD255227_260342 = 322 #define XBOX1_CONTROLLER_HOLD317260_458691 = 323 #define XBOX1_CONTROLLER_HOLD943421_992524 = 324 #define XBOX1_CONTROLLER_HOLD948158_609238 = 325 #define XBOX1_CONTROLLER_HOLD208445_267635 = 326 #define XBOX1_CONTROLLER_HOLD258414_589368 = 327 #define XBOX1_CONTROLLER_HOLD624472_164671 = 328 #define XBOX1_CONTROLLER_HOLD212028_119213 = 329 #define XBOX1_CONTROLLER_HOLD971031_176800 = 330 #define XBOX1_CONTROLLER_HOLD311848_219919 = 331 #define XBOX1_CONTROLLER_HOLD716398_289402 = 332 #define XBOX1_CONTROLLER_HOLD648605_397633 = 333 #define XBOX1_CONTROLLER_HOLD310369_265055 = 334 #define XBOX1_CONTROLLER_HOLD319867_753433 = 335 #define XBOX1_CONTROLLER_HOLD499971_710679 = 336 #define XBOX1_CONTROLLER_HOLD235050_272998 = 337 #define XBOX1_CONTROLLER_HOLD450449_928195 = 338 #define XBOX1_CONTROLLER_HOLD297507_854193 = 339 #define XBOX1_CONTROLLER_HOLD384450_680565 = 340 #define XBOX1_CONTROLLER_HOLD963931_414678 = 341 #define XBOX1_CONTROLLER_HOLD551223_627540 = 342 #define XBOX1_CONTROLLER_HOLD779756_983945 = 343 #define XBOX1_CONTROLLER_HOLD909650_139688 = 344 #define XBOX1_CONTROLLER_HOLD304532_551644 = 345 #define XBOX1_CONTROLLER_HOLD366626_351243 = 346 #define XBOX1_CONTROLLER_HOLD869674_850770 = 347 #define XBOX1_CONTROLLER_HOLD783434_508727 = 348 #define XBOX1_CONTROLLER_HOLD754869_944165 = 349 #define XBOX1_CONTROLLER_HOLD934562_390718 = 350 #define XBOX1_CONTROLLER_HOLD241849_903130 = 351 #define XBOX1_CONTROLLER_HOLD964358_721682 = 352 #define XBOX1_CONTROLLER_HOLD946989_857995 = 353 #define XBOX1_CONTROLLER_HOLD125865_141868 = 354 #define XBOX1_CONTROLLER_HOLD663966_479590 = 355 #define XBOX1_CONTROLLER_HOLD668224_668242 = 356 #define XBOX1_CONTROLLER_HOLD622913_862971 = 357 #define XBOX1_CONTROLLER_HOLD441425_222303 = 358 #define XBOX1_CONTROLLER_HOLD934367_202280 = 359 #define XBOX1_CONTROLLER_HOLD823520_421086 = 360 #define XBOX1_CONTROLLER_HOLD361068_508065 = 361 #define XBOX1_CONTROLLER_HOLD354782_973463 = 362 #define XBOX1_CONTROLLER_HOLD204217_596634 = 363 #define XBOX1_CONTROLLER_HOLD368322_715179 = 364 #define XBOX1_CONTROLLER_HOLD812127_567809 = 365 #define XBOX1_CONTROLLER_HOLD221597_632673 = 366 #define XBOX1_CONTROLLER_HOLD668122_519566 = 367 #define XBOX1_CONTROLLER_HOLD537158_575525 = 368 #define XBOX1_CONTROLLER_HOLD866217_793369 = 369 #define XBOX1_CONTROLLER_HOLD933688_293955 = 370 #define XBOX1_CONTROLLER_HOLD371226_693364 = 371 #define XBOX1_CONTROLLER_HOLD994719_130634 = 372 #define XBOX1_CONTROLLER_HOLD560698_146905 = 373 #define XBOX1_CONTROLLER_HOLD267016_616473 = 374 #define XBOX1_CONTROLLER_HOLD598019_388763 = 375 #define XBOX1_CONTROLLER_HOLD612834_586766 = 376 #define XBOX1_CONTROLLER_HOLD531616_158898 = 377 #define XBOX1_CONTROLLER_HOLD551189_489238 = 378 #define XBOX1_CONTROLLER_HOLD651252_139203 = 379 #define XBOX1_CONTROLLER_HOLD169622_153307 = 380 #define XBOX1_CONTROLLER_HOLD933461_667784 = 381 #define XBOX1_CONTROLLER_HOLD614552_921192 = 382 #define XBOX1_CONTROLLER_HOLD936396_881079 = 383 #define XBOX1_CONTROLLER_HOLD891149_444332 = 384 #define XBOX1_CONTROLLER_HOLD201499_758394 = 385 #define XBOX1_CONTROLLER_HOLD930908_482242 = 386 #define XBOX1_CONTROLLER_HOLD235510_430426 = 387 #define XBOX1_CONTROLLER_HOLD172022_731840 = 388 #define XBOX1_CONTROLLER_HOLD793141_638496 = 389 #define XBOX1_CONTROLLER_HOLD329122_913472 = 390 #define XBOX1_CONTROLLER_HOLD585463_159714 = 391 #define XBOX1_CONTROLLER_HOLD197447_861595 = 392 #define XBOX1_CONTROLLER_HOLD745082_423518 = 393 #define XBOX1_CONTROLLER_HOLD433576_844379 = 394 #define XBOX1_CONTROLLER_HOLD917433_760522 = 395 #define XBOX1_CONTROLLER_HOLD778833_643869 = 396 #define XBOX1_CONTROLLER_HOLD764482_940917 = 397 #define XBOX1_CONTROLLER_HOLD809733_537900 = 398 #define XBOX1_CONTROLLER_HOLD266740_236774 = 399 #define XBOX1_CONTROLLER_HOLD177857_767850 = 400 #define XBOX1_CONTROLLER_HOLD685521_238513 = 401 #define XBOX1_CONTROLLER_HOLD551131_620942 = 402 #define XBOX1_CONTROLLER_HOLD812870_345628 = 403 #define XBOX1_CONTROLLER_HOLD882648_378636 = 404 #define XBOX1_CONTROLLER_HOLD591501_508554 = 405 #define XBOX1_CONTROLLER_HOLD219045_388881 = 406 #define XBOX1_CONTROLLER_HOLD627479_481642 = 407 #define XBOX1_CONTROLLER_HOLD721456_304815 = 408 #define XBOX1_CONTROLLER_HOLD966983_856733 = 409 #define XBOX1_CONTROLLER_HOLD132958_654875 = 410 #define XBOX1_CONTROLLER_HOLD402335_696954 = 411 #define XBOX1_CONTROLLER_HOLD535098_299865 = 412 #define XBOX1_CONTROLLER_HOLD659681_841802 = 413 #define XBOX1_CONTROLLER_HOLD592089_458551 = 414 #define XBOX1_CONTROLLER_HOLD636049_337807 = 415 #define XBOX1_CONTROLLER_HOLD661421_514516 = 416 #define XBOX1_CONTROLLER_HOLD951273_393130 = 417 #define XBOX1_CONTROLLER_HOLD581033_399887 = 418 #define XBOX1_CONTROLLER_HOLD700293_826907 = 419 #define XBOX1_CONTROLLER_HOLD335949_958119 = 420 #define XBOX1_CONTROLLER_HOLD367871_746150 = 421 #define XBOX1_CONTROLLER_HOLD428146_838787 = 422 #define XBOX1_CONTROLLER_HOLD275642_119347 = 423 #define XBOX1_CONTROLLER_HOLD725941_475815 = 424 #define XBOX1_CONTROLLER_HOLD590092_757454 = 425 #define XBOX1_CONTROLLER_HOLD636377_762260 = 426 #define XBOX1_CONTROLLER_HOLD116437_632203 = 427 #define XBOX1_CONTROLLER_HOLD609254_279193 = 428 #define XBOX1_CONTROLLER_HOLD788469_279992 = 429 #define XBOX1_CONTROLLER_HOLD221936_698070 = 430 #define XBOX1_CONTROLLER_HOLD476426_365774 = 431 #define XBOX1_CONTROLLER_HOLD549274_726312 = 432 #define XBOX1_CONTROLLER_HOLD752496_178341 = 433 #define XBOX1_CONTROLLER_HOLD770496_257073 = 434 #define XBOX1_CONTROLLER_HOLD910298_408502 = 435 #define XBOX1_CONTROLLER_HOLD906017_468972 = 436 #define XBOX1_CONTROLLER_HOLD941920_987079 = 437 #define XBOX1_CONTROLLER_HOLD575382_197494 = 438 #define XBOX1_CONTROLLER_HOLD836535_479280 = 439 #define XBOX1_CONTROLLER_HOLD662737_734976 = 440 #define XBOX1_CONTROLLER_HOLD963750_329409 = 441 #define XBOX1_CONTROLLER_HOLD508203_299566 = 442 #define XBOX1_CONTROLLER_HOLD580073_810560 = 443 #define XBOX1_CONTROLLER_HOLD816633_134809 = 444 #define XBOX1_CONTROLLER_HOLD946616_964438 = 445 #define XBOX1_CONTROLLER_HOLD407547_167342 = 446 #define XBOX1_CONTROLLER_HOLD267770_917192 = 447 #define XBOX1_CONTROLLER_HOLD479716_826372 = 448 #define XBOX1_CONTROLLER_HOLD236572_503248 = 449 #define XBOX1_CONTROLLER_HOLD572120_575627 = 450 #define XBOX1_CONTROLLER_HOLD715768_499291 = 451 #define XBOX1_CONTROLLER_HOLD684527_541924 = 452 #define XBOX1_CONTROLLER_HOLD287187_840238 = 453 #define XBOX1_CONTROLLER_HOLD534833_202356 = 454 #define XBOX1_CONTROLLER_HOLD107763_136597 = 455 #define XBOX1_CONTROLLER_HOLD120333_545994 = 456 #define XBOX1_CONTROLLER_HOLD439041_836416 = 457 #define XBOX1_CONTROLLER_HOLD797201_990351 = 458 #define XBOX1_CONTROLLER_HOLD246214_199244 = 459 #define XBOX1_CONTROLLER_HOLD378811_791705 = 460 #define XBOX1_CONTROLLER_HOLD472467_343990 = 461 #define XBOX1_CONTROLLER_HOLD221453_492713 = 462 #define XBOX1_CONTROLLER_HOLD517575_724578 = 463 #define XBOX1_CONTROLLER_HOLD135430_648800 = 464 #define XBOX1_CONTROLLER_HOLD997390_427434 = 465 #define XBOX1_CONTROLLER_HOLD264408_579034 = 466 #define XBOX1_CONTROLLER_HOLD503521_219176 = 467 #define XBOX1_CONTROLLER_HOLD756320_820800 = 468 #define XBOX1_CONTROLLER_HOLD822793_560090 = 469 #define XBOX1_CONTROLLER_HOLD791158_222013 = 470 #define XBOX1_CONTROLLER_HOLD495212_113926 = 471 #define XBOX1_CONTROLLER_HOLD736877_759549 = 472 #define XBOX1_CONTROLLER_HOLD719772_545962 = 473 #define XBOX1_CONTROLLER_HOLD274407_756268 = 474 #define XBOX1_CONTROLLER_HOLD890044_347269 = 475 #define XBOX1_CONTROLLER_HOLD508625_870178 = 476 #define XBOX1_CONTROLLER_HOLD497857_946943 = 477 #define XBOX1_CONTROLLER_HOLD239108_849798 = 478 #define XBOX1_CONTROLLER_HOLD185878_739809 = 479 #define XBOX1_CONTROLLER_HOLD372455_352132 = 480 #define XBOX1_CONTROLLER_HOLD821271_457596 = 481 #define XBOX1_CONTROLLER_HOLD519108_659621 = 482 #define XBOX1_CONTROLLER_HOLD926432_300048 = 483 #define XBOX1_CONTROLLER_HOLD384188_291215 = 484 #define XBOX1_CONTROLLER_HOLD528396_208968 = 485 #define XBOX1_CONTROLLER_HOLD989222_111252 = 486 #define XBOX1_CONTROLLER_HOLD414289_390904 = 487 #define XBOX1_CONTROLLER_HOLD813223_779108 = 488 #define XBOX1_CONTROLLER_HOLD233804_563823 = 489 #define XBOX1_CONTROLLER_HOLD159956_343370 = 490 #define XBOX1_CONTROLLER_HOLD848441_869791 = 491 #define XBOX1_CONTROLLER_HOLD793770_413692 = 492 #define XBOX1_CONTROLLER_HOLD413367_682365 = 493 #define XBOX1_CONTROLLER_HOLD933898_330979 = 494 #define XBOX1_CONTROLLER_HOLD835539_337926 = 495 #define XBOX1_CONTROLLER_HOLD343360_506681 = 496 #define XBOX1_CONTROLLER_HOLD241269_601268 = 497 #define XBOX1_CONTROLLER_HOLD557112_824373 = 498 #define XBOX1_CONTROLLER_HOLD979998_212959 = 499 #define XBOX1_CONTROLLER_HOLD990840_154138 = 500 #define XBOX1_CONTROLLER_HOLD511275_767344 = 501 #define XBOX1_CONTROLLER_HOLD404758_129912 = 502 #define XBOX1_CONTROLLER_HOLD795993_940256 = 503 #define XBOX1_CONTROLLER_HOLD650406_400835 = 504 #define XBOX1_CONTROLLER_HOLD940835_682990 = 505 #define XBOX1_CONTROLLER_HOLD500008_693230 = 506 #define XBOX1_CONTROLLER_HOLD407947_862323 = 507 #define XBOX1_CONTROLLER_HOLD411863_481998 = 508 #define XBOX1_CONTROLLER_HOLD467129_344583 = 509 #define XBOX1_CONTROLLER_HOLD799363_896459 = 510 #define XBOX1_CONTROLLER_HOLD609093_937890 = 511 #define XBOX1_CONTROLLER_HOLD283529_347891 = 512 #define XBOX1_CONTROLLER_HOLD759344_900924 = 513 #define XBOX1_CONTROLLER_HOLD317624_491982 = 514 #define XBOX1_CONTROLLER_HOLD588059_729941 = 515 #define XBOX1_CONTROLLER_HOLD456122_104780 = 516 #define XBOX1_CONTROLLER_HOLD340734_280226 = 517 #define XBOX1_CONTROLLER_HOLD500546_395875 = 518 #define XBOX1_CONTROLLER_HOLD999017_594319 = 519 #define XBOX1_CONTROLLER_HOLD722615_987747 = 520 #define XBOX1_CONTROLLER_HOLD958060_912969 = 521 #define XBOX1_CONTROLLER_HOLD938021_693278 = 522 #define XBOX1_CONTROLLER_HOLD357849_860176 = 523 #define XBOX1_CONTROLLER_HOLD432147_530842 = 524 #define XBOX1_CONTROLLER_HOLD969745_435457 = 525 #define XBOX1_CONTROLLER_HOLD653385_236125 = 526 #define XBOX1_CONTROLLER_HOLD962630_457847 = 527 #define XBOX1_CONTROLLER_HOLD197300_168399 = 528 #define XBOX1_CONTROLLER_HOLD431738_516585 = 529 #define XBOX1_CONTROLLER_HOLD766298_966601 = 530 #define XBOX1_CONTROLLER_HOLD936948_993047 = 531 #define XBOX1_CONTROLLER_HOLD854199_445484 = 532 #define XBOX1_CONTROLLER_HOLD654661_960089 = 533 #define XBOX1_CONTROLLER_HOLD411589_542001 = 534 #define XBOX1_CONTROLLER_HOLD103414_103389 = 535 #define XBOX1_CONTROLLER_HOLD584042_813642 = 536 #define XBOX1_CONTROLLER_HOLD900862_791544 = 537 #define XBOX1_CONTROLLER_HOLD891541_341478 = 538 #define XBOX1_CONTROLLER_HOLD761982_576799 = 539 #define XBOX1_CONTROLLER_HOLD298951_911282 = 540 #define XBOX1_CONTROLLER_HOLD150863_738357 = 541 #define XBOX1_CONTROLLER_HOLD379441_473367 = 542 #define XBOX1_CONTROLLER_HOLD541808_252182 = 543 #define XBOX1_CONTROLLER_HOLD726282_485440 = 544 #define XBOX1_CONTROLLER_HOLD377282_610264 = 545 #define XBOX1_CONTROLLER_HOLD238393_827906 = 546 #define XBOX1_CONTROLLER_HOLD558946_566745 = 547 #define XBOX1_CONTROLLER_HOLD185140_205333 = 548 #define XBOX1_CONTROLLER_HOLD525564_953140 = 549 #define XBOX1_CONTROLLER_HOLD920942_575803 = 550 #define XBOX1_CONTROLLER_HOLD519597_342387 = 551 #define XBOX1_CONTROLLER_HOLD500050_597004 = 552 #define XBOX1_CONTROLLER_HOLD795018_832682 = 553 #define XBOX1_CONTROLLER_HOLD630834_197970 = 554 #define XBOX1_CONTROLLER_HOLD212834_540541 = 555 #define XBOX1_CONTROLLER_HOLD476376_967999 = 556 #define XBOX1_CONTROLLER_HOLD231473_958775 = 557 #define XBOX1_CONTROLLER_HOLD848100_345878 = 558 #define XBOX1_CONTROLLER_HOLD230241_764805 = 559 #define XBOX1_CONTROLLER_HOLD678753_845424 = 560 #define XBOX1_CONTROLLER_HOLD647051_571811 = 561 #define XBOX1_CONTROLLER_HOLD706621_684567 = 562 #define XBOX1_CONTROLLER_HOLD578369_359304 = 563 #define XBOX1_CONTROLLER_HOLD381776_478540 = 564 #define XBOX1_CONTROLLER_HOLD523574_404928 = 565 #define XBOX1_CONTROLLER_HOLD805572_933088 = 566 #define XBOX1_CONTROLLER_HOLD692213_602782 = 567 #define XBOX1_CONTROLLER_HOLD535113_523329 = 568 #define XBOX1_CONTROLLER_HOLD214128_220464 = 569 #define XBOX1_CONTROLLER_HOLD935754_516799 = 570 #define XBOX1_CONTROLLER_HOLD493322_383859 = 571 #define XBOX1_CONTROLLER_HOLD384516_666055 = 572 #define XBOX1_CONTROLLER_HOLD485640_179445 = 573 #define XBOX1_CONTROLLER_HOLD933351_785099 = 574 #define XBOX1_CONTROLLER_HOLD940456_220712 = 575 #define XBOX1_CONTROLLER_HOLD336159_283710 = 576 #define XBOX1_CONTROLLER_HOLD426894_806771 = 577 #define XBOX1_CONTROLLER_HOLD885041_867927 = 578 #define XBOX1_CONTROLLER_HOLD173497_804154 = 579 #define XBOX1_CONTROLLER_HOLD928622_602846 = 580 #define XBOX1_CONTROLLER_HOLD162882_662963 = 581 #define XBOX1_CONTROLLER_HOLD884395_465873 = 582 #define XBOX1_CONTROLLER_HOLD357391_698485 = 583 #define XBOX1_CONTROLLER_HOLD731969_585647 = 584 #define XBOX1_CONTROLLER_HOLD501298_174100 = 585 #define XBOX1_CONTROLLER_HOLD734749_851991 = 586 #define XBOX1_CONTROLLER_HOLD302629_727517 = 587 #define XBOX1_CONTROLLER_HOLD180660_339196 = 588 #define XBOX1_CONTROLLER_HOLD969945_135054 = 589 #define XBOX1_CONTROLLER_HOLD400924_291868 = 590 #define XBOX1_CONTROLLER_HOLD237654_841163 = 591 #define XBOX1_CONTROLLER_HOLD363740_274092 = 592 #define XBOX1_CONTROLLER_HOLD684039_268426 = 593 #define XBOX1_CONTROLLER_HOLD651539_931725 = 594 #define XBOX1_CONTROLLER_HOLD347913_974926 = 595 #define XBOX1_CONTROLLER_HOLD345567_340465 = 596 #define XBOX1_CONTROLLER_HOLD527666_853955 = 597 #define XBOX1_CONTROLLER_HOLD949465_949210 = 598 #define XBOX1_CONTROLLER_HOLD951610_898874 = 599 #define XBOX1_CONTROLLER_HOLD472943_486975 = 600 #define XBOX1_CONTROLLER_HOLD624517_412131 = 601 #define XBOX1_CONTROLLER_HOLD755860_427496 = 602 #define XBOX1_CONTROLLER_HOLD641550_486589 = 603 #define XBOX1_CONTROLLER_HOLD397681_808331 = 604 #define XBOX1_CONTROLLER_HOLD327850_791030 = 605 #define XBOX1_CONTROLLER_HOLD984261_258988 = 606 #define XBOX1_CONTROLLER_HOLD632661_841818 = 607 #define XBOX1_CONTROLLER_HOLD650897_255491 = 608 #define XBOX1_CONTROLLER_HOLD108855_546166 = 609 #define XBOX1_CONTROLLER_HOLD548040_662636 = 610 #define XBOX1_CONTROLLER_HOLD402798_942149 = 611 #define XBOX1_CONTROLLER_HOLD618664_871488 = 612 #define XBOX1_CONTROLLER_HOLD965598_385655 = 613 #define XBOX1_CONTROLLER_HOLD379556_222303 = 614 #define XBOX1_CONTROLLER_HOLD175429_265879 = 615 #define XBOX1_CONTROLLER_HOLD739218_976689 = 616 #define XBOX1_CONTROLLER_HOLD272181_791215 = 617 #define XBOX1_CONTROLLER_HOLD972849_183254 = 618 #define XBOX1_CONTROLLER_HOLD658324_525121 = 619 #define XBOX1_CONTROLLER_HOLD654600_129179 = 620 #define XBOX1_CONTROLLER_HOLD595561_335867 = 621 #define XBOX1_CONTROLLER_HOLD867409_748958 = 622 #define XBOX1_CONTROLLER_HOLD188631_604264 = 623 #define XBOX1_CONTROLLER_HOLD907370_280646 = 624 #define XBOX1_CONTROLLER_HOLD241735_321458 = 625 #define XBOX1_CONTROLLER_HOLD654859_690403 = 626 #define XBOX1_CONTROLLER_HOLD478783_991341 = 627 #define XBOX1_CONTROLLER_HOLD679299_894598 = 628 #define XBOX1_CONTROLLER_HOLD929776_809284 = 629 #define XBOX1_CONTROLLER_HOLD332094_337257 = 630 #define XBOX1_CONTROLLER_HOLD486038_687742 = 631 #define XBOX1_CONTROLLER_HOLD182717_667412 = 632 #define XBOX1_CONTROLLER_HOLD365145_721602 = 633 #define XBOX1_CONTROLLER_HOLD908434_117652 = 634 #define XBOX1_CONTROLLER_HOLD840883_733189 = 635 #define XBOX1_CONTROLLER_HOLD611680_864972 = 636 #define XBOX1_CONTROLLER_HOLD653163_673496 = 637 #define XBOX1_CONTROLLER_HOLD944594_391489 = 638 #define XBOX1_CONTROLLER_HOLD628469_529084 = 639 #define XBOX1_CONTROLLER_HOLD238452_528974 = 640 #define XBOX1_CONTROLLER_HOLD368310_963422 = 641 #define XBOX1_CONTROLLER_HOLD521015_593252 = 642 #define XBOX1_CONTROLLER_HOLD870322_454065 = 643 #define XBOX1_CONTROLLER_HOLD567663_347614 = 644 #define XBOX1_CONTROLLER_HOLD469400_432963 = 645 #define XBOX1_CONTROLLER_HOLD829939_459379 = 646 #define XBOX1_CONTROLLER_HOLD378623_105190 = 647 #define XBOX1_CONTROLLER_HOLD511772_815434 = 648 #define XBOX1_CONTROLLER_HOLD722778_513225 = 649 #define XBOX1_CONTROLLER_HOLD329606_773666 = 650 #define XBOX1_CONTROLLER_HOLD245003_856252 = 651 #define XBOX1_CONTROLLER_HOLD764537_956466 = 652 #define XBOX1_CONTROLLER_HOLD458056_983956 = 653 #define XBOX1_CONTROLLER_HOLD758821_522258 = 654 #define XBOX1_CONTROLLER_HOLD237596_848695 = 655 #define XBOX1_CONTROLLER_HOLD393974_857026 = 656 #define XBOX1_CONTROLLER_HOLD873758_866962 = 657 #define XBOX1_CONTROLLER_HOLD450537_914305 = 658 #define XBOX1_CONTROLLER_HOLD506085_104005 = 659 #define XBOX1_CONTROLLER_HOLD783430_643814 = 660 #define XBOX1_CONTROLLER_HOLD990261_653984 = 661 #define XBOX1_CONTROLLER_HOLD947287_335631 = 662 #define XBOX1_CONTROLLER_HOLD886422_985998 = 663 #define XBOX1_CONTROLLER_HOLD788678_727738 = 664 #define XBOX1_CONTROLLER_HOLD784293_429605 = 665 #define XBOX1_CONTROLLER_HOLD528756_981879 = 666 #define XBOX1_CONTROLLER_HOLD130945_579193 = 667 #define XBOX1_CONTROLLER_HOLD214953_473855 = 668 #define XBOX1_CONTROLLER_HOLD624483_458469 = 669 #define XBOX1_CONTROLLER_HOLD228811_466245 = 670 #define XBOX1_CONTROLLER_HOLD269498_536602 = 671 #define XBOX1_CONTROLLER_HOLD844912_984250 = 672 #define XBOX1_CONTROLLER_HOLD161141_156011 = 673 #define XBOX1_CONTROLLER_HOLD619610_252198 = 674 #define XBOX1_CONTROLLER_HOLD124606_303221 = 675 #define XBOX1_CONTROLLER_HOLD871408_969289 = 676 #define XBOX1_CONTROLLER_HOLD772919_635683 = 677 #define XBOX1_CONTROLLER_HOLD278755_491786 = 678 #define XBOX1_CONTROLLER_HOLD746068_504426 = 679 #define XBOX1_CONTROLLER_HOLD185250_289590 = 680 #define XBOX1_CONTROLLER_HOLD845584_709689 = 681 #define XBOX1_CONTROLLER_HOLD231316_958290 = 682 #define XBOX1_CONTROLLER_HOLD169129_747946 = 683 #define XBOX1_CONTROLLER_HOLD526705_702043 = 684 #define XBOX1_CONTROLLER_HOLD521090_642673 = 685 #define XBOX1_CONTROLLER_HOLD124396_971400 = 686 #define XBOX1_CONTROLLER_HOLD848297_629110 = 687 #define XBOX1_CONTROLLER_HOLD697564_696613 = 688 #define XBOX1_CONTROLLER_HOLD767749_123298 = 689 #define XBOX1_CONTROLLER_HOLD986303_250391 = 690 #define XBOX1_CONTROLLER_HOLD970836_975770 = 691 #define XBOX1_CONTROLLER_HOLD183468_846539 = 692 #define XBOX1_CONTROLLER_HOLD930510_417102 = 693 #define XBOX1_CONTROLLER_HOLD807035_951119 = 694 #define XBOX1_CONTROLLER_HOLD686911_799998 = 695 #define XBOX1_CONTROLLER_HOLD291468_629010 = 696 #define XBOX1_CONTROLLER_HOLD501070_702593 = 697 #define XBOX1_CONTROLLER_HOLD164726_134667 = 698 #define XBOX1_CONTROLLER_HOLD711555_135985 = 699 #define XBOX1_CONTROLLER_HOLD604186_984142 = 700 #define XBOX1_CONTROLLER_HOLD573568_786634 = 701 #define XBOX1_CONTROLLER_HOLD563335_170208 = 702 #define XBOX1_CONTROLLER_HOLD207816_741898 = 703 #define XBOX1_CONTROLLER_HOLD730660_714711 = 704 #define XBOX1_CONTROLLER_HOLD752287_967562 = 705 #define XBOX1_CONTROLLER_HOLD293316_141600 = 706 #define XBOX1_CONTROLLER_HOLD584846_239888 = 707 #define XBOX1_CONTROLLER_HOLD159256_804943 = 708 #define XBOX1_CONTROLLER_HOLD948801_438582 = 709 #define XBOX1_CONTROLLER_HOLD274649_437305 = 710 #define XBOX1_CONTROLLER_HOLD356987_833327 = 711 #define XBOX1_CONTROLLER_HOLD706317_743892 = 712 #define XBOX1_CONTROLLER_HOLD153177_216824 = 713 #define XBOX1_CONTROLLER_HOLD227903_348599 = 714 #define XBOX1_CONTROLLER_HOLD193286_183950 = 715 #define XBOX1_CONTROLLER_HOLD375950_796862 = 716 #define XBOX1_CONTROLLER_HOLD360829_193355 = 717 #define XBOX1_CONTROLLER_HOLD387896_173160 = 718 #define XBOX1_CONTROLLER_HOLD324484_441672 = 719 #define XBOX1_CONTROLLER_HOLD341717_505914 = 720 #define XBOX1_CONTROLLER_HOLD497936_213971 = 721 #define XBOX1_CONTROLLER_HOLD655952_977313 = 722 #define XBOX1_CONTROLLER_HOLD232807_465609 = 723 #define XBOX1_CONTROLLER_HOLD558836_194921 = 724 #define XBOX1_CONTROLLER_HOLD461726_719666 = 725 #define XBOX1_CONTROLLER_HOLD902272_716553 = 726 #define XBOX1_CONTROLLER_HOLD574947_694029 = 727 #define XBOX1_CONTROLLER_HOLD519390_599796 = 728 #define XBOX1_CONTROLLER_HOLD869213_764284 = 729 #define XBOX1_CONTROLLER_HOLD433613_387898 = 730 #define XBOX1_CONTROLLER_HOLD323789_777011 = 731 #define XBOX1_CONTROLLER_HOLD949027_473431 = 732 #define XBOX1_CONTROLLER_HOLD834985_499183 = 733 #define XBOX1_CONTROLLER_HOLD856628_966348 = 734 #define XBOX1_CONTROLLER_HOLD436286_139663 = 735 #define XBOX1_CONTROLLER_HOLD263175_291226 = 736 #define XBOX1_CONTROLLER_HOLD881911_161941 = 737 #define XBOX1_CONTROLLER_HOLD579296_400000 = 738 #define XBOX1_CONTROLLER_HOLD133690_660437 = 739 #define XBOX1_CONTROLLER_HOLD388837_477324 = 740 #define XBOX1_CONTROLLER_HOLD866921_754679 = 741 #define XBOX1_CONTROLLER_HOLD510472_484249 = 742 #define XBOX1_CONTROLLER_HOLD453481_855860 = 743 #define XBOX1_CONTROLLER_HOLD953313_932959 = 744 #define XBOX1_CONTROLLER_HOLD551151_266038 = 745 #define XBOX1_CONTROLLER_HOLD967088_221836 = 746 #define XBOX1_CONTROLLER_HOLD107366_386654 = 747 #define XBOX1_CONTROLLER_HOLD149084_258114 = 748 #define XBOX1_CONTROLLER_HOLD749786_372730 = 749 #define XBOX1_CONTROLLER_HOLD713278_688353 = 750 #define XBOX1_CONTROLLER_HOLD629989_292777 = 751 #define XBOX1_CONTROLLER_HOLD756531_477262 = 752 #define XBOX1_CONTROLLER_HOLD522215_965197 = 753 #define XBOX1_CONTROLLER_HOLD363447_839281 = 754 #define XBOX1_CONTROLLER_HOLD810178_350400 = 755 #define XBOX1_CONTROLLER_HOLD973951_420546 = 756 #define XBOX1_CONTROLLER_HOLD363619_484189 = 757 #define XBOX1_CONTROLLER_HOLD645669_223736 = 758 #define XBOX1_CONTROLLER_HOLD522964_575166 = 759 #define XBOX1_CONTROLLER_HOLD618690_928318 = 760 #define XBOX1_CONTROLLER_HOLD371810_538898 = 761 #define XBOX1_CONTROLLER_HOLD151610_112631 = 762 #define XBOX1_CONTROLLER_HOLD557857_155383 = 763 #define XBOX1_CONTROLLER_HOLD643503_478095 = 764 #define XBOX1_CONTROLLER_HOLD680086_160408 = 765 #define XBOX1_CONTROLLER_HOLD242995_606218 = 766 #define XBOX1_CONTROLLER_HOLD879242_726888 = 767 #define XBOX1_CONTROLLER_HOLD787785_599717 = 768 #define XBOX1_CONTROLLER_HOLD510402_664488 = 769 #define XBOX1_CONTROLLER_HOLD779679_120632 = 770 #define XBOX1_CONTROLLER_HOLD513652_914078 = 771 #define XBOX1_CONTROLLER_HOLD320431_179939 = 772 #define XBOX1_CONTROLLER_HOLD171559_823140 = 773 #define XBOX1_CONTROLLER_HOLD324728_833747 = 774 #define XBOX1_CONTROLLER_HOLD984838_876655 = 775 #define XBOX1_CONTROLLER_HOLD509201_382574 = 776 #define XBOX1_CONTROLLER_HOLD810724_293844 = 777 #define XBOX1_CONTROLLER_HOLD558829_401378 = 778 #define XBOX1_CONTROLLER_HOLD833193_852238 = 779 #define XBOX1_CONTROLLER_HOLD231576_514675 = 780 #define XBOX1_CONTROLLER_HOLD807639_320457 = 781 #define XBOX1_CONTROLLER_HOLD121100_170247 = 782 #define XBOX1_CONTROLLER_HOLD295180_455059 = 783 #define XBOX1_CONTROLLER_HOLD268532_248779 = 784 #define XBOX1_CONTROLLER_HOLD251330_296239 = 785 #define XBOX1_CONTROLLER_HOLD121960_354929 = 786 #define XBOX1_CONTROLLER_HOLD113923_611313 = 787 #define XBOX1_CONTROLLER_HOLD716305_438283 = 788 #define XBOX1_CONTROLLER_HOLD689976_356166 = 789 #define XBOX1_CONTROLLER_HOLD732260_836724 = 790 #define XBOX1_CONTROLLER_HOLD386219_821016 = 791 #define XBOX1_CONTROLLER_HOLD915696_994455 = 792 #define XBOX1_CONTROLLER_HOLD612430_122369 = 793 #define XBOX1_CONTROLLER_HOLD836913_126766 = 794 #define XBOX1_CONTROLLER_HOLD248128_622321 = 795 #define XBOX1_CONTROLLER_HOLD820431_377265 = 796 #define XBOX1_CONTROLLER_HOLD851686_479877 = 797 #define XBOX1_CONTROLLER_HOLD331086_973541 = 798 #define XBOX1_CONTROLLER_HOLD773604_291118 = 799 #define XBOX1_CONTROLLER_HOLD296499_382965 = 800 #define XBOX1_CONTROLLER_HOLD260242_424254 = 801 #define XBOX1_CONTROLLER_HOLD826921_171217 = 802 #define XBOX1_CONTROLLER_HOLD793443_539547 = 803 #define XBOX1_CONTROLLER_HOLD225073_156811 = 804 #define XBOX1_CONTROLLER_HOLD198713_349738 = 805 #define XBOX1_CONTROLLER_HOLD791949_641874 = 806 #define XBOX1_CONTROLLER_HOLD236447_546013 = 807 #define XBOX1_CONTROLLER_HOLD763594_158546 = 808 #define XBOX1_CONTROLLER_HOLD209300_815940 = 809 #define XBOX1_CONTROLLER_HOLD976476_989298 = 810 #define XBOX1_CONTROLLER_HOLD218569_951092 = 811 #define XBOX1_CONTROLLER_HOLD517675_977869 = 812 #define XBOX1_CONTROLLER_HOLD270505_204315 = 813 #define XBOX1_CONTROLLER_HOLD721632_979166 = 814 #define XBOX1_CONTROLLER_HOLD766989_300407 = 815 #define XBOX1_CONTROLLER_HOLD601420_777969 = 816 #define XBOX1_CONTROLLER_HOLD184616_157070 = 817 #define XBOX1_CONTROLLER_HOLD234316_659053 = 818 #define XBOX1_CONTROLLER_HOLD627494_754250 = 819 #define XBOX1_CONTROLLER_HOLD573674_502680 = 820 #define XBOX1_CONTROLLER_HOLD600702_797660 = 821 #define XBOX1_CONTROLLER_HOLD908448_469265 = 822 #define XBOX1_CONTROLLER_HOLD303972_202591 = 823 #define XBOX1_CONTROLLER_HOLD254639_958185 = 824 #define XBOX1_CONTROLLER_HOLD719338_687989 = 825 #define XBOX1_CONTROLLER_HOLD610236_510494 = 826 #define XBOX1_CONTROLLER_HOLD237546_607241 = 827 #define XBOX1_CONTROLLER_HOLD524019_466740 = 828 #define XBOX1_CONTROLLER_HOLD223147_192938 = 829 #define XBOX1_CONTROLLER_HOLD678632_716330 = 830 #define XBOX1_CONTROLLER_HOLD108337_484051 = 831 #define XBOX1_CONTROLLER_HOLD642608_225275 = 832 #define XBOX1_CONTROLLER_HOLD529271_670716 = 833 #define XBOX1_CONTROLLER_HOLD668389_126925 = 834 #define XBOX1_CONTROLLER_HOLD319985_826168 = 835 #define XBOX1_CONTROLLER_HOLD322565_302671 = 836 #define XBOX1_CONTROLLER_HOLD652580_689341 = 837 #define XBOX1_CONTROLLER_HOLD638434_817854 = 838 #define XBOX1_CONTROLLER_HOLD495755_265877 = 839 #define XBOX1_CONTROLLER_HOLD701368_373033 = 840 #define XBOX1_CONTROLLER_HOLD459488_630390 = 841 #define XBOX1_CONTROLLER_HOLD828879_567465 = 842 #define XBOX1_CONTROLLER_HOLD576078_331447 = 843 #define XBOX1_CONTROLLER_HOLD224694_455675 = 844 #define XBOX1_CONTROLLER_HOLD594159_266063 = 845 #define XBOX1_CONTROLLER_HOLD519978_559471 = 846 #define XBOX1_CONTROLLER_HOLD380549_379027 = 847 #define XBOX1_CONTROLLER_HOLD504250_390892 = 848 #define XBOX1_CONTROLLER_HOLD662021_369686 = 849 #define XBOX1_CONTROLLER_HOLD463600_414565 = 850 #define XBOX1_CONTROLLER_HOLD293791_771373 = 851 #define XBOX1_CONTROLLER_HOLD514425_625200 = 852 #define XBOX1_CONTROLLER_HOLD344841_867329 = 853 #define XBOX1_CONTROLLER_HOLD678997_621846 = 854 #define XBOX1_CONTROLLER_HOLD211242_549985 = 855 #define XBOX1_CONTROLLER_HOLD315199_130568 = 856 #define XBOX1_CONTROLLER_HOLD252794_390095 = 857 #define XBOX1_CONTROLLER_HOLD955436_741546 = 858 #define XBOX1_CONTROLLER_HOLD845106_873277 = 859 #define XBOX1_CONTROLLER_HOLD737223_769487 = 860 #define XBOX1_CONTROLLER_HOLD785124_991315 = 861 #define XBOX1_CONTROLLER_HOLD966172_624752 = 862 #define XBOX1_CONTROLLER_HOLD823162_581976 = 863 #define XBOX1_CONTROLLER_HOLD950745_290944 = 864 #define XBOX1_CONTROLLER_HOLD743858_205623 = 865 #define XBOX1_CONTROLLER_HOLD233577_803206 = 866 #define XBOX1_CONTROLLER_HOLD916344_227066 = 867 #define XBOX1_CONTROLLER_HOLD541210_140314 = 868 #define XBOX1_CONTROLLER_HOLD430348_933663 = 869 #define XBOX1_CONTROLLER_HOLD745239_711064 = 870 #define XBOX1_CONTROLLER_HOLD526203_927174 = 871 #define XBOX1_CONTROLLER_HOLD519629_337084 = 872 #define XBOX1_CONTROLLER_HOLD915569_152681 = 873 #define XBOX1_CONTROLLER_HOLD936143_110242 = 874 #define XBOX1_CONTROLLER_HOLD359783_293796 = 875 #define XBOX1_CONTROLLER_HOLD692131_202447 = 876 #define XBOX1_CONTROLLER_HOLD606989_236151 = 877 #define XBOX1_CONTROLLER_HOLD279517_326425 = 878 #define XBOX1_CONTROLLER_HOLD736506_277092 = 879 #define XBOX1_CONTROLLER_HOLD198678_157789 = 880 #define XBOX1_CONTROLLER_HOLD425210_464710 = 881 #define XBOX1_CONTROLLER_HOLD894984_238145 = 882 #define XBOX1_CONTROLLER_HOLD313022_599867 = 883 #define XBOX1_CONTROLLER_HOLD543377_293177 = 884 #define XBOX1_CONTROLLER_HOLD925463_982362 = 885 #define XBOX1_CONTROLLER_HOLD412212_415690 = 886 #define XBOX1_CONTROLLER_HOLD151788_612950 = 887 #define XBOX1_CONTROLLER_HOLD358925_120259 = 888 #define XBOX1_CONTROLLER_HOLD983440_956545 = 889 #define XBOX1_CONTROLLER_HOLD653154_626035 = 890 #define XBOX1_CONTROLLER_HOLD775092_463789 = 891 #define XBOX1_CONTROLLER_HOLD982245_465797 = 892 #define XBOX1_CONTROLLER_HOLD534995_513787 = 893 #define XBOX1_CONTROLLER_HOLD390604_283721 = 894 #define XBOX1_CONTROLLER_HOLD102229_487766 = 895 #define XBOX1_CONTROLLER_HOLD836074_944787 = 896 #define XBOX1_CONTROLLER_HOLD164945_299366 = 897 #define XBOX1_CONTROLLER_HOLD799262_697697 = 898 #define XBOX1_CONTROLLER_HOLD764515_141361 = 899 #define XBOX1_CONTROLLER_HOLD400415_480109 = 900 #define XBOX1_CONTROLLER_HOLD665375_478598 = 901 #define XBOX1_CONTROLLER_HOLD634641_359751 = 902 #define XBOX1_CONTROLLER_HOLD104735_669156 = 903 #define XBOX1_CONTROLLER_HOLD224594_265024 = 904 #define XBOX1_CONTROLLER_HOLD916824_886959 = 905 #define XBOX1_CONTROLLER_HOLD143084_262111 = 906 #define XBOX1_CONTROLLER_HOLD363942_928133 = 907 #define XBOX1_CONTROLLER_HOLD259878_882145 = 908 #define XBOX1_CONTROLLER_HOLD193291_602875 = 909 #define XBOX1_CONTROLLER_HOLD730096_773901 = 910 #define XBOX1_CONTROLLER_HOLD235439_479614 = 911 #define XBOX1_CONTROLLER_HOLD138742_580395 = 912 #define XBOX1_CONTROLLER_HOLD274002_956133 = 913 #define XBOX1_CONTROLLER_HOLD975464_977349 = 914 #define XBOX1_CONTROLLER_HOLD286365_311988 = 915 #define XBOX1_CONTROLLER_HOLD582104_259717 = 916 #define XBOX1_CONTROLLER_HOLD100110_466198 = 917 #define XBOX1_CONTROLLER_HOLD855168_907047 = 918 #define XBOX1_CONTROLLER_HOLD406049_903954 = 919 #define XBOX1_CONTROLLER_HOLD422259_524887 = 920 #define XBOX1_CONTROLLER_HOLD730291_679619 = 921 #define XBOX1_CONTROLLER_HOLD971884_872446 = 922 #define XBOX1_CONTROLLER_HOLD612769_793793 = 923 #define XBOX1_CONTROLLER_HOLD925981_744061 = 924 #define XBOX1_CONTROLLER_HOLD332405_293598 = 925 #define XBOX1_CONTROLLER_HOLD793359_970021 = 926 #define XBOX1_CONTROLLER_HOLD498206_916438 = 927 #define XBOX1_CONTROLLER_HOLD284473_850509 = 928 #define XBOX1_CONTROLLER_HOLD846256_621744 = 929 #define XBOX1_CONTROLLER_HOLD607737_175388 = 930 #define XBOX1_CONTROLLER_HOLD733532_853749 = 931 #define XBOX1_CONTROLLER_HOLD733141_979770 = 932 #define XBOX1_CONTROLLER_HOLD117232_101906 = 933 #define XBOX1_CONTROLLER_HOLD945712_722100 = 934 #define XBOX1_CONTROLLER_HOLD268097_902337 = 935 #define XBOX1_CONTROLLER_HOLD867564_311462 = 936 #define XBOX1_CONTROLLER_HOLD559966_497051 = 937 #define XBOX1_CONTROLLER_HOLD123535_529809 = 938 #define XBOX1_CONTROLLER_HOLD331702_659650 = 939 #define XBOX1_CONTROLLER_HOLD439372_321868 = 940 #define XBOX1_CONTROLLER_HOLD843031_883320 = 941 #define XBOX1_CONTROLLER_HOLD631656_443561 = 942 #define XBOX1_CONTROLLER_HOLD351025_415711 = 943 #define XBOX1_CONTROLLER_HOLD569315_736971 = 944 #define XBOX1_CONTROLLER_HOLD411601_120743 = 945 #define XBOX1_CONTROLLER_HOLD321813_256513 = 946 #define XBOX1_CONTROLLER_HOLD758658_606096 = 947 #define XBOX1_CONTROLLER_HOLD286717_990894 = 948 #define XBOX1_CONTROLLER_HOLD917584_615402 = 949 #define XBOX1_CONTROLLER_HOLD130066_774557 = 950 #define XBOX1_CONTROLLER_HOLD144140_576293 = 951 #define XBOX1_CONTROLLER_HOLD149599_457813 = 952 #define XBOX1_CONTROLLER_HOLD760952_850082 = 953 #define XBOX1_CONTROLLER_HOLD414494_851624 = 954 #define XBOX1_CONTROLLER_HOLD582543_479986 = 955 #define XBOX1_CONTROLLER_HOLD499737_122257 = 956 #define XBOX1_CONTROLLER_HOLD581214_105090 = 957 #define XBOX1_CONTROLLER_HOLD133764_851733 = 958 #define XBOX1_CONTROLLER_HOLD346657_475393 = 959 #define XBOX1_CONTROLLER_HOLD919134_107639 = 960 #define XBOX1_CONTROLLER_HOLD558513_257592 = 961 #define XBOX1_CONTROLLER_HOLD924164_142709 = 962 #define XBOX1_CONTROLLER_HOLD340532_819767 = 963 #define XBOX1_CONTROLLER_HOLD979209_847535 = 964 #define XBOX1_CONTROLLER_HOLD266696_464885 = 965 #define XBOX1_CONTROLLER_HOLD505807_847508 = 966 #define XBOX1_CONTROLLER_HOLD967675_696923 = 967 #define XBOX1_CONTROLLER_HOLD557224_927964 = 968 #define XBOX1_CONTROLLER_HOLD807746_679111 = 969 #define XBOX1_CONTROLLER_HOLD886673_476645 = 970 #define XBOX1_CONTROLLER_HOLD936821_606022 = 971 #define XBOX1_CONTROLLER_HOLD734596_492161 = 972 #define XBOX1_CONTROLLER_HOLD896129_905794 = 973 #define XBOX1_CONTROLLER_HOLD607768_628750 = 974 #define XBOX1_CONTROLLER_HOLD573263_526448 = 975 #define XBOX1_CONTROLLER_HOLD347191_468738 = 976 #define XBOX1_CONTROLLER_HOLD397988_250347 = 977 #define XBOX1_CONTROLLER_HOLD734045_259361 = 978 #define XBOX1_CONTROLLER_HOLD864840_925198 = 979 #define XBOX1_CONTROLLER_HOLD174988_303553 = 980 #define XBOX1_CONTROLLER_HOLD438427_957343 = 981 #define XBOX1_CONTROLLER_HOLD751415_910485 = 982 #define XBOX1_CONTROLLER_HOLD834158_710382 = 983 #define XBOX1_CONTROLLER_HOLD395206_977801 = 984 #define XBOX1_CONTROLLER_HOLD116766_317282 = 985 #define XBOX1_CONTROLLER_HOLD244670_630851 = 986 #define XBOX1_CONTROLLER_HOLD587399_564808 = 987 #define XBOX1_CONTROLLER_HOLD799043_152708 = 988 #define XBOX1_CONTROLLER_HOLD513101_489357 = 989 #define XBOX1_CONTROLLER_HOLD252055_749801 = 990 #define XBOX1_CONTROLLER_HOLD774115_299963 = 991 #define XBOX1_CONTROLLER_HOLD134682_330228 = 992 #define XBOX1_CONTROLLER_HOLD356611_773353 = 993 #define XBOX1_CONTROLLER_HOLD232290_470408 = 994 #define XBOX1_CONTROLLER_HOLD686113_311356 = 995 #define XBOX1_CONTROLLER_HOLD540421_887135 = 996 #define XBOX1_CONTROLLER_HOLD242630_301328 = 997 #define XBOX1_CONTROLLER_HOLD784207_536972 = 998 #define XBOX1_CONTROLLER_HOLD794980_682166 = 999 #define XBOX1_CONTROLLER_HOLD621442_529992 = 1000 function SKILL_MOVES_MAKER() { if (ps5_angle == XBOX1_CONTROLLER_HOLD863446_916052) { PASSCODE_6DIGIT = 123456;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD863446_916052) { PASSCODE_6DIGIT = 645296;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD125345_757431) { PASSCODE_6DIGIT = 728308;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD243980_246274) { PASSCODE_6DIGIT = 551788;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD705031_238424) { PASSCODE_6DIGIT = 285678;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD819365_873270) { PASSCODE_6DIGIT = 990599;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD535109_479048) { PASSCODE_6DIGIT = 430673;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD593226_855586) { PASSCODE_6DIGIT = 376135;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD565232_279744) { PASSCODE_6DIGIT = 356458;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD416172_847904) { PASSCODE_6DIGIT = 335085;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD358809_622292) { PASSCODE_6DIGIT = 396347;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551780_859326) { PASSCODE_6DIGIT = 275643;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD422232_432284) { PASSCODE_6DIGIT = 616827;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD673928_614444) { PASSCODE_6DIGIT = 384732;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD145582_241424) { PASSCODE_6DIGIT = 272864;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD759792_538446) { PASSCODE_6DIGIT = 454427;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD199008_585150) { PASSCODE_6DIGIT = 712450;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD841821_601086) { PASSCODE_6DIGIT = 511323;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD248426_182439) { PASSCODE_6DIGIT = 145565;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD718125_973247) { PASSCODE_6DIGIT = 157413;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD433423_220721) { PASSCODE_6DIGIT = 259369;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD404647_382456) { PASSCODE_6DIGIT = 170241;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD261135_766292) { PASSCODE_6DIGIT = 723592;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD596667_899425) { PASSCODE_6DIGIT = 217516;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD278463_913677) { PASSCODE_6DIGIT = 884142;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD695342_930846) { PASSCODE_6DIGIT = 945210;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD626943_148146) { PASSCODE_6DIGIT = 134782;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD478376_466202) { PASSCODE_6DIGIT = 467158;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD414796_196040) { PASSCODE_6DIGIT = 989721;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD701179_653493) { PASSCODE_6DIGIT = 548797;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD825753_403021) { PASSCODE_6DIGIT = 539701;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD795152_208579) { PASSCODE_6DIGIT = 544469;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD864558_521884) { PASSCODE_6DIGIT = 707519;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD778954_395852) { PASSCODE_6DIGIT = 788567;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD738882_618038) { PASSCODE_6DIGIT = 532382;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD620704_340819) { PASSCODE_6DIGIT = 912618;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD471233_682043) { PASSCODE_6DIGIT = 708306;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD403870_229700) { PASSCODE_6DIGIT = 748010;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD726471_259941) { PASSCODE_6DIGIT = 867857;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD474697_368312) { PASSCODE_6DIGIT = 326569;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD641587_235088) { PASSCODE_6DIGIT = 217764;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD662661_211417) { PASSCODE_6DIGIT = 757792;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD122837_391676) { PASSCODE_6DIGIT = 811324;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD336273_936828) { PASSCODE_6DIGIT = 641807;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD879429_903774) { PASSCODE_6DIGIT = 704328;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD170009_948860) { PASSCODE_6DIGIT = 618019;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD263546_704648) { PASSCODE_6DIGIT = 521553;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD522018_174301) { PASSCODE_6DIGIT = 850132;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD394973_747614) { PASSCODE_6DIGIT = 971000;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD793761_530714) { PASSCODE_6DIGIT = 824051;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD642384_226653) { PASSCODE_6DIGIT = 268959;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD344687_716105) { PASSCODE_6DIGIT = 481676;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD359637_613756) { PASSCODE_6DIGIT = 400670;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD863548_790601) { PASSCODE_6DIGIT = 328810;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD946365_631591) { PASSCODE_6DIGIT = 553368;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD626078_649192) { PASSCODE_6DIGIT = 177395;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD817233_974965) { PASSCODE_6DIGIT = 163368;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD253907_772381) { PASSCODE_6DIGIT = 365303;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD397704_390889) { PASSCODE_6DIGIT = 837949;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD652928_658146) { PASSCODE_6DIGIT = 204583;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD176353_373892) { PASSCODE_6DIGIT = 795577;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD669027_367360) { PASSCODE_6DIGIT = 225264;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD537454_963363) { PASSCODE_6DIGIT = 338546;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD831362_138666) { PASSCODE_6DIGIT = 640797;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD961992_403212) { PASSCODE_6DIGIT = 809238;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD994082_266713) { PASSCODE_6DIGIT = 386821;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD849425_652521) { PASSCODE_6DIGIT = 531547;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD703851_522910) { PASSCODE_6DIGIT = 573051;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD677952_807841) { PASSCODE_6DIGIT = 976955;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD673843_638962) { PASSCODE_6DIGIT = 483813;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD112358_660226) { PASSCODE_6DIGIT = 372013;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD649342_162046) { PASSCODE_6DIGIT = 502514;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD514068_523186) { PASSCODE_6DIGIT = 128359;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD298340_290646) { PASSCODE_6DIGIT = 974597;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD304441_773391) { PASSCODE_6DIGIT = 379856;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD365584_586662) { PASSCODE_6DIGIT = 632380;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD635559_228769) { PASSCODE_6DIGIT = 409807;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD885340_672400) { PASSCODE_6DIGIT = 296285;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD257656_176798) { PASSCODE_6DIGIT = 716061;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD518998_409130) { PASSCODE_6DIGIT = 732527;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD795921_731057) { PASSCODE_6DIGIT = 883505;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD367352_870089) { PASSCODE_6DIGIT = 216992;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD473197_457932) { PASSCODE_6DIGIT = 522421;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD595561_854883) { PASSCODE_6DIGIT = 429583;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD100152_437263) { PASSCODE_6DIGIT = 354915;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD616077_747843) { PASSCODE_6DIGIT = 974854;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD594224_859607) { PASSCODE_6DIGIT = 519261;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD203521_777867) { PASSCODE_6DIGIT = 857371;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD128740_679150) { PASSCODE_6DIGIT = 638099;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD937562_523954) { PASSCODE_6DIGIT = 222276;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD595155_767886) { PASSCODE_6DIGIT = 151332;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD302640_589888) { PASSCODE_6DIGIT = 575781;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD129598_158991) { PASSCODE_6DIGIT = 567798;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD843379_180644) { PASSCODE_6DIGIT = 130373;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD192443_967675) { PASSCODE_6DIGIT = 595189;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD121343_713661) { PASSCODE_6DIGIT = 340070;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD917128_783530) { PASSCODE_6DIGIT = 292469;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD219558_491880) { PASSCODE_6DIGIT = 515317;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558218_184486) { PASSCODE_6DIGIT = 297440;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD367873_735286) { PASSCODE_6DIGIT = 368277;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD310492_270222) { PASSCODE_6DIGIT = 417952;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD373750_877863) { PASSCODE_6DIGIT = 167086;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD584253_432142) { PASSCODE_6DIGIT = 965434;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD520215_160408) { PASSCODE_6DIGIT = 216735;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD882023_989576) { PASSCODE_6DIGIT = 331112;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD429885_342539) { PASSCODE_6DIGIT = 533051;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD852841_227981) { PASSCODE_6DIGIT = 256371;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD502954_157939) { PASSCODE_6DIGIT = 671956;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD128886_534221) { PASSCODE_6DIGIT = 498630;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD899939_542945) { PASSCODE_6DIGIT = 573797;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD655707_169749) { PASSCODE_6DIGIT = 999746;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD279155_212159) { PASSCODE_6DIGIT = 494559;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD408937_169361) { PASSCODE_6DIGIT = 609545;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD190212_674441) { PASSCODE_6DIGIT = 783833;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD200412_574371) { PASSCODE_6DIGIT = 961832;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD479888_226923) { PASSCODE_6DIGIT = 937889;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD284141_537734) { PASSCODE_6DIGIT = 522183;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD622726_115831) { PASSCODE_6DIGIT = 295432;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD222383_251232) { PASSCODE_6DIGIT = 329473;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD291797_150515) { PASSCODE_6DIGIT = 770090;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD131659_419302) { PASSCODE_6DIGIT = 677287;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD255721_314977) { PASSCODE_6DIGIT = 249827;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD142920_306089) { PASSCODE_6DIGIT = 841431;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD209172_471829) { PASSCODE_6DIGIT = 217702;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD324227_788462) { PASSCODE_6DIGIT = 835798;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD108415_941714) { PASSCODE_6DIGIT = 369449;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD727612_665729) { PASSCODE_6DIGIT = 673762;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD700361_240147) { PASSCODE_6DIGIT = 483440;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD286753_813146) { PASSCODE_6DIGIT = 597554;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD296204_324478) { PASSCODE_6DIGIT = 280318;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD584467_725777) { PASSCODE_6DIGIT = 171220;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD972584_150873) { PASSCODE_6DIGIT = 375337;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD298709_939090) { PASSCODE_6DIGIT = 519374;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD451054_180493) { PASSCODE_6DIGIT = 169620;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD452934_425283) { PASSCODE_6DIGIT = 310836;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD476239_242492) { PASSCODE_6DIGIT = 582068;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD319219_337470) { PASSCODE_6DIGIT = 198103;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD789203_816067) { PASSCODE_6DIGIT = 461277;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD278053_791210) { PASSCODE_6DIGIT = 109073;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD868247_349776) { PASSCODE_6DIGIT = 241338;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD425281_831857) { PASSCODE_6DIGIT = 439927;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD287620_212665) { PASSCODE_6DIGIT = 631377;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD476265_494752) { PASSCODE_6DIGIT = 235702;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD997819_493336) { PASSCODE_6DIGIT = 788599;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD870585_119925) { PASSCODE_6DIGIT = 850511;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD360251_446621) { PASSCODE_6DIGIT = 617521;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD909050_495605) { PASSCODE_6DIGIT = 828551;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD181776_313754) { PASSCODE_6DIGIT = 475265;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD333954_349230) { PASSCODE_6DIGIT = 663444;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD545550_436967) { PASSCODE_6DIGIT = 259299;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD206457_370552) { PASSCODE_6DIGIT = 104395;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD263264_582971) { PASSCODE_6DIGIT = 454364;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD444557_400278) { PASSCODE_6DIGIT = 649254;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD805343_285443) { PASSCODE_6DIGIT = 638290;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD785841_578274) { PASSCODE_6DIGIT = 127711;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD724895_157746) { PASSCODE_6DIGIT = 382984;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD663423_223970) { PASSCODE_6DIGIT = 643703;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD606653_608538) { PASSCODE_6DIGIT = 832856;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD349424_127690) { PASSCODE_6DIGIT = 693034;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD579377_198781) { PASSCODE_6DIGIT = 663662;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD984950_662035) { PASSCODE_6DIGIT = 265654;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD783596_707847) { PASSCODE_6DIGIT = 993001;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD366559_637132) { PASSCODE_6DIGIT = 772806;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD483699_228301) { PASSCODE_6DIGIT = 447458;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD960470_884696) { PASSCODE_6DIGIT = 750365;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD473983_896681) { PASSCODE_6DIGIT = 600814;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD296812_822214) { PASSCODE_6DIGIT = 787464;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD547989_926780) { PASSCODE_6DIGIT = 740461;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD404670_465749) { PASSCODE_6DIGIT = 404723;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD506188_721375) { PASSCODE_6DIGIT = 448004;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD337900_468574) { PASSCODE_6DIGIT = 892126;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD620452_763936) { PASSCODE_6DIGIT = 523073;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD626524_896490) { PASSCODE_6DIGIT = 999812;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD118142_674660) { PASSCODE_6DIGIT = 697064;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD398741_609419) { PASSCODE_6DIGIT = 744774;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD707204_570100) { PASSCODE_6DIGIT = 721836;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD767519_249523) { PASSCODE_6DIGIT = 543187;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD594969_474729) { PASSCODE_6DIGIT = 935634;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD180841_162990) { PASSCODE_6DIGIT = 350306;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD701556_897273) { PASSCODE_6DIGIT = 742767;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD754948_333961) { PASSCODE_6DIGIT = 244715;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD911221_942980) { PASSCODE_6DIGIT = 717531;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD678200_388017) { PASSCODE_6DIGIT = 416839;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD589885_138225) { PASSCODE_6DIGIT = 286318;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD630687_966928) { PASSCODE_6DIGIT = 780446;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD708337_534479) { PASSCODE_6DIGIT = 861832;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD344903_767806) { PASSCODE_6DIGIT = 300388;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD879797_922900) { PASSCODE_6DIGIT = 280386;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD569072_994499) { PASSCODE_6DIGIT = 992486;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD291799_897243) { PASSCODE_6DIGIT = 939736;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD703918_645728) { PASSCODE_6DIGIT = 371058;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD812966_515411) { PASSCODE_6DIGIT = 495960;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD241912_740983) { PASSCODE_6DIGIT = 748639;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD825430_522658) { PASSCODE_6DIGIT = 704174;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD317547_931717) { PASSCODE_6DIGIT = 751444;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD767559_680979) { PASSCODE_6DIGIT = 239192;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD655414_917880) { PASSCODE_6DIGIT = 139526;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD230494_195393) { PASSCODE_6DIGIT = 281190;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD513941_335335) { PASSCODE_6DIGIT = 164945;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD200131_897438) { PASSCODE_6DIGIT = 322432;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD738152_132095) { PASSCODE_6DIGIT = 431547;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD836587_576320) { PASSCODE_6DIGIT = 451070;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD184475_633405) { PASSCODE_6DIGIT = 143777;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD557911_636712) { PASSCODE_6DIGIT = 413863;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD547820_528645) { PASSCODE_6DIGIT = 205105;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD349293_801311) { PASSCODE_6DIGIT = 212555;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD121417_877038) { PASSCODE_6DIGIT = 564231;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD496287_942752) { PASSCODE_6DIGIT = 925842;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD682369_682097) { PASSCODE_6DIGIT = 876903;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD118875_331188) { PASSCODE_6DIGIT = 234752;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD739775_390621) { PASSCODE_6DIGIT = 166355;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD278498_918435) { PASSCODE_6DIGIT = 713638;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD279159_424282) { PASSCODE_6DIGIT = 740408;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD497156_646867) { PASSCODE_6DIGIT = 657641;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD936906_368255) { PASSCODE_6DIGIT = 233746;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD911411_586467) { PASSCODE_6DIGIT = 965770;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD392526_611680) { PASSCODE_6DIGIT = 788230;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD626003_261564) { PASSCODE_6DIGIT = 661276;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD205386_581543) { PASSCODE_6DIGIT = 620232;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD631522_755704) { PASSCODE_6DIGIT = 609393;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD923490_847365) { PASSCODE_6DIGIT = 233669;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD125358_531694) { PASSCODE_6DIGIT = 831126;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD899126_619331) { PASSCODE_6DIGIT = 124427;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD984429_250025) { PASSCODE_6DIGIT = 642052;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD966520_248315) { PASSCODE_6DIGIT = 823860;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD882094_524676) { PASSCODE_6DIGIT = 374032;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD977574_593330) { PASSCODE_6DIGIT = 329112;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD475087_434183) { PASSCODE_6DIGIT = 129398;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD718417_127277) { PASSCODE_6DIGIT = 765903;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD727563_338176) { PASSCODE_6DIGIT = 588306;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD806989_331748) { PASSCODE_6DIGIT = 558717;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD611612_990856) { PASSCODE_6DIGIT = 570705;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD362189_260475) { PASSCODE_6DIGIT = 120838;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD891101_722571) { PASSCODE_6DIGIT = 407242;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD925442_541290) { PASSCODE_6DIGIT = 102131;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD152077_682521) { PASSCODE_6DIGIT = 546977;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD524358_404639) { PASSCODE_6DIGIT = 855766;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD964657_478701) { PASSCODE_6DIGIT = 144293;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD954952_472712) { PASSCODE_6DIGIT = 745958;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD440933_391600) { PASSCODE_6DIGIT = 928010;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD842592_898416) { PASSCODE_6DIGIT = 488044;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD565153_110599) { PASSCODE_6DIGIT = 204554;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD274203_918533) { PASSCODE_6DIGIT = 976941;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD794390_305656) { PASSCODE_6DIGIT = 388865;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD520389_689991) { PASSCODE_6DIGIT = 361545;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD839267_791140) { PASSCODE_6DIGIT = 570909;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD430331_697702) { PASSCODE_6DIGIT = 668459;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD309923_507900) { PASSCODE_6DIGIT = 163749;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD169052_303865) { PASSCODE_6DIGIT = 648683;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD232099_152906) { PASSCODE_6DIGIT = 970708;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD461825_719005) { PASSCODE_6DIGIT = 795615;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD480106_353072) { PASSCODE_6DIGIT = 492699;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD688581_527849) { PASSCODE_6DIGIT = 122144;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD836742_569116) { PASSCODE_6DIGIT = 318624;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD418949_301220) { PASSCODE_6DIGIT = 601277;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD163114_903919) { PASSCODE_6DIGIT = 344999;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD343771_967737) { PASSCODE_6DIGIT = 296535;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD264626_266916) { PASSCODE_6DIGIT = 805798;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD934295_653155) { PASSCODE_6DIGIT = 550960;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD108985_995739) { PASSCODE_6DIGIT = 729539;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD807872_652808) { PASSCODE_6DIGIT = 991708;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD876295_236399) { PASSCODE_6DIGIT = 986330;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD408260_727967) { PASSCODE_6DIGIT = 123376;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD458533_588215) { PASSCODE_6DIGIT = 378880;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD506052_628348) { PASSCODE_6DIGIT = 969779;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD285869_481164) { PASSCODE_6DIGIT = 147980;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD951896_906621) { PASSCODE_6DIGIT = 829961;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD187855_459999) { PASSCODE_6DIGIT = 978275;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD498667_176796) { PASSCODE_6DIGIT = 538648;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD467403_670225) { PASSCODE_6DIGIT = 904646;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD152919_863467) { PASSCODE_6DIGIT = 532218;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD993083_580854) { PASSCODE_6DIGIT = 614244;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD747643_430766) { PASSCODE_6DIGIT = 876225;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD935094_903383) { PASSCODE_6DIGIT = 254646;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD315952_606162) { PASSCODE_6DIGIT = 920218;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD331201_158645) { PASSCODE_6DIGIT = 212126;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD933423_450977) { PASSCODE_6DIGIT = 199527;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD378662_266475) { PASSCODE_6DIGIT = 403333;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD412992_756383) { PASSCODE_6DIGIT = 730322;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD508861_185226) { PASSCODE_6DIGIT = 254978;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD925026_525502) { PASSCODE_6DIGIT = 123752;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD388211_233949) { PASSCODE_6DIGIT = 515693;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD774523_378919) { PASSCODE_6DIGIT = 754484;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD654554_794782) { PASSCODE_6DIGIT = 619431;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD378863_189984) { PASSCODE_6DIGIT = 467413;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD264548_472673) { PASSCODE_6DIGIT = 494175;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD590963_352867) { PASSCODE_6DIGIT = 612572;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD726509_437630) { PASSCODE_6DIGIT = 311156;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD610713_474376) { PASSCODE_6DIGIT = 403086;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD608433_963696) { PASSCODE_6DIGIT = 762583;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD809876_815992) { PASSCODE_6DIGIT = 634864;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD509538_544925) { PASSCODE_6DIGIT = 583158;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD877110_803622) { PASSCODE_6DIGIT = 271943;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD170098_787545) { PASSCODE_6DIGIT = 180894;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD216903_458364) { PASSCODE_6DIGIT = 633990;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD166455_414530) { PASSCODE_6DIGIT = 341491;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD926427_894801) { PASSCODE_6DIGIT = 286026;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD582423_134093) { PASSCODE_6DIGIT = 760465;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD189565_842415) { PASSCODE_6DIGIT = 961158;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD529488_743643) { PASSCODE_6DIGIT = 794017;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD218301_713665) { PASSCODE_6DIGIT = 217438;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD258697_213948) { PASSCODE_6DIGIT = 153363;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD289415_515854) { PASSCODE_6DIGIT = 377511;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD664754_254318) { PASSCODE_6DIGIT = 291963;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD740624_216578) { PASSCODE_6DIGIT = 903789;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD945837_457870) { PASSCODE_6DIGIT = 359262;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD136523_817921) { PASSCODE_6DIGIT = 577290;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551302_662090) { PASSCODE_6DIGIT = 313208;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD782972_532732) { PASSCODE_6DIGIT = 226851;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD331879_687023) { PASSCODE_6DIGIT = 338153;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558972_651878) { PASSCODE_6DIGIT = 262865;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD655883_934600) { PASSCODE_6DIGIT = 347513;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD874990_311388) { PASSCODE_6DIGIT = 197299;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD217200_910559) { PASSCODE_6DIGIT = 737977;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD163600_982973) { PASSCODE_6DIGIT = 289071;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD407954_775192) { PASSCODE_6DIGIT = 134499;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD423630_788053) { PASSCODE_6DIGIT = 795597;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD525191_387515) { PASSCODE_6DIGIT = 857133;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD471448_807620) { PASSCODE_6DIGIT = 463769;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD579225_338703) { PASSCODE_6DIGIT = 203351;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD987670_165570) { PASSCODE_6DIGIT = 765891;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD512495_115253) { PASSCODE_6DIGIT = 261732;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD255227_260342) { PASSCODE_6DIGIT = 353839;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD317260_458691) { PASSCODE_6DIGIT = 346962;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD943421_992524) { PASSCODE_6DIGIT = 403460;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD948158_609238) { PASSCODE_6DIGIT = 146796;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD208445_267635) { PASSCODE_6DIGIT = 431938;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD258414_589368) { PASSCODE_6DIGIT = 439222;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD624472_164671) { PASSCODE_6DIGIT = 898546;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD212028_119213) { PASSCODE_6DIGIT = 267628;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD971031_176800) { PASSCODE_6DIGIT = 349876;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD311848_219919) { PASSCODE_6DIGIT = 761710;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD716398_289402) { PASSCODE_6DIGIT = 156286;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD648605_397633) { PASSCODE_6DIGIT = 288708;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD310369_265055) { PASSCODE_6DIGIT = 217819;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD319867_753433) { PASSCODE_6DIGIT = 367591;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD499971_710679) { PASSCODE_6DIGIT = 620901;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD235050_272998) { PASSCODE_6DIGIT = 895701;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD450449_928195) { PASSCODE_6DIGIT = 258417;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD297507_854193) { PASSCODE_6DIGIT = 120676;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD384450_680565) { PASSCODE_6DIGIT = 553353;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD963931_414678) { PASSCODE_6DIGIT = 781166;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551223_627540) { PASSCODE_6DIGIT = 314329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD779756_983945) { PASSCODE_6DIGIT = 314576;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD909650_139688) { PASSCODE_6DIGIT = 644578;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD304532_551644) { PASSCODE_6DIGIT = 155223;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD366626_351243) { PASSCODE_6DIGIT = 623168;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD869674_850770) { PASSCODE_6DIGIT = 539951;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD783434_508727) { PASSCODE_6DIGIT = 851166;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD754869_944165) { PASSCODE_6DIGIT = 585445;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD934562_390718) { PASSCODE_6DIGIT = 205339;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD241849_903130) { PASSCODE_6DIGIT = 669720;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD964358_721682) { PASSCODE_6DIGIT = 839002;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD946989_857995) { PASSCODE_6DIGIT = 378746;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD125865_141868) { PASSCODE_6DIGIT = 282088;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD663966_479590) { PASSCODE_6DIGIT = 494362;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD668224_668242) { PASSCODE_6DIGIT = 936811;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD622913_862971) { PASSCODE_6DIGIT = 940252;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD441425_222303) { PASSCODE_6DIGIT = 787329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD934367_202280) { PASSCODE_6DIGIT = 929417;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD823520_421086) { PASSCODE_6DIGIT = 819741;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD361068_508065) { PASSCODE_6DIGIT = 605442;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD354782_973463) { PASSCODE_6DIGIT = 937462;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD204217_596634) { PASSCODE_6DIGIT = 516911;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD368322_715179) { PASSCODE_6DIGIT = 289378;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD812127_567809) { PASSCODE_6DIGIT = 228538;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD221597_632673) { PASSCODE_6DIGIT = 981354;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD668122_519566) { PASSCODE_6DIGIT = 958399;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD537158_575525) { PASSCODE_6DIGIT = 497146;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD866217_793369) { PASSCODE_6DIGIT = 373008;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD933688_293955) { PASSCODE_6DIGIT = 473222;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD371226_693364) { PASSCODE_6DIGIT = 564112;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD994719_130634) { PASSCODE_6DIGIT = 854729;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD560698_146905) { PASSCODE_6DIGIT = 564447;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD267016_616473) { PASSCODE_6DIGIT = 617298;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD598019_388763) { PASSCODE_6DIGIT = 507390;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD612834_586766) { PASSCODE_6DIGIT = 828989;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD531616_158898) { PASSCODE_6DIGIT = 114753;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551189_489238) { PASSCODE_6DIGIT = 617049;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD651252_139203) { PASSCODE_6DIGIT = 626621;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD169622_153307) { PASSCODE_6DIGIT = 141487;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD933461_667784) { PASSCODE_6DIGIT = 744289;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD614552_921192) { PASSCODE_6DIGIT = 396851;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD936396_881079) { PASSCODE_6DIGIT = 901837;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD891149_444332) { PASSCODE_6DIGIT = 746839;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD201499_758394) { PASSCODE_6DIGIT = 411333;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD930908_482242) { PASSCODE_6DIGIT = 413686;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD235510_430426) { PASSCODE_6DIGIT = 580055;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD172022_731840) { PASSCODE_6DIGIT = 658255;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD793141_638496) { PASSCODE_6DIGIT = 789631;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD329122_913472) { PASSCODE_6DIGIT = 228268;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD585463_159714) { PASSCODE_6DIGIT = 353959;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD197447_861595) { PASSCODE_6DIGIT = 694379;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD745082_423518) { PASSCODE_6DIGIT = 574594;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD433576_844379) { PASSCODE_6DIGIT = 477995;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD917433_760522) { PASSCODE_6DIGIT = 102058;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD778833_643869) { PASSCODE_6DIGIT = 741276;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD764482_940917) { PASSCODE_6DIGIT = 185125;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD809733_537900) { PASSCODE_6DIGIT = 106064;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD266740_236774) { PASSCODE_6DIGIT = 718044;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD177857_767850) { PASSCODE_6DIGIT = 875644;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD685521_238513) { PASSCODE_6DIGIT = 627856;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551131_620942) { PASSCODE_6DIGIT = 664544;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD812870_345628) { PASSCODE_6DIGIT = 701607;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD882648_378636) { PASSCODE_6DIGIT = 265473;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD591501_508554) { PASSCODE_6DIGIT = 215667;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD219045_388881) { PASSCODE_6DIGIT = 968229;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD627479_481642) { PASSCODE_6DIGIT = 812284;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD721456_304815) { PASSCODE_6DIGIT = 165324;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD966983_856733) { PASSCODE_6DIGIT = 471568;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD132958_654875) { PASSCODE_6DIGIT = 179760;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD402335_696954) { PASSCODE_6DIGIT = 588326;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD535098_299865) { PASSCODE_6DIGIT = 737400;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD659681_841802) { PASSCODE_6DIGIT = 364016;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD592089_458551) { PASSCODE_6DIGIT = 545160;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD636049_337807) { PASSCODE_6DIGIT = 726813;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD661421_514516) { PASSCODE_6DIGIT = 793550;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD951273_393130) { PASSCODE_6DIGIT = 832307;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD581033_399887) { PASSCODE_6DIGIT = 884926;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD700293_826907) { PASSCODE_6DIGIT = 863363;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD335949_958119) { PASSCODE_6DIGIT = 372418;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD367871_746150) { PASSCODE_6DIGIT = 716787;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD428146_838787) { PASSCODE_6DIGIT = 987590;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD275642_119347) { PASSCODE_6DIGIT = 447632;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD725941_475815) { PASSCODE_6DIGIT = 820459;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD590092_757454) { PASSCODE_6DIGIT = 185170;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD636377_762260) { PASSCODE_6DIGIT = 942423;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD116437_632203) { PASSCODE_6DIGIT = 122161;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD609254_279193) { PASSCODE_6DIGIT = 974549;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD788469_279992) { PASSCODE_6DIGIT = 879194;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD221936_698070) { PASSCODE_6DIGIT = 929206;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD476426_365774) { PASSCODE_6DIGIT = 189853;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD549274_726312) { PASSCODE_6DIGIT = 111626;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD752496_178341) { PASSCODE_6DIGIT = 560439;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD770496_257073) { PASSCODE_6DIGIT = 899460;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD910298_408502) { PASSCODE_6DIGIT = 244054;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD906017_468972) { PASSCODE_6DIGIT = 933106;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD941920_987079) { PASSCODE_6DIGIT = 786968;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD575382_197494) { PASSCODE_6DIGIT = 297193;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD836535_479280) { PASSCODE_6DIGIT = 298994;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD662737_734976) { PASSCODE_6DIGIT = 991470;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD963750_329409) { PASSCODE_6DIGIT = 276836;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD508203_299566) { PASSCODE_6DIGIT = 200996;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD580073_810560) { PASSCODE_6DIGIT = 480185;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD816633_134809) { PASSCODE_6DIGIT = 747181;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD946616_964438) { PASSCODE_6DIGIT = 778988;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD407547_167342) { PASSCODE_6DIGIT = 954957;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD267770_917192) { PASSCODE_6DIGIT = 356534;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD479716_826372) { PASSCODE_6DIGIT = 404218;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD236572_503248) { PASSCODE_6DIGIT = 785227;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD572120_575627) { PASSCODE_6DIGIT = 792163;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD715768_499291) { PASSCODE_6DIGIT = 570080;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD684527_541924) { PASSCODE_6DIGIT = 529017;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD287187_840238) { PASSCODE_6DIGIT = 672484;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD534833_202356) { PASSCODE_6DIGIT = 117902;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD107763_136597) { PASSCODE_6DIGIT = 458670;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD120333_545994) { PASSCODE_6DIGIT = 112164;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD439041_836416) { PASSCODE_6DIGIT = 161590;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD797201_990351) { PASSCODE_6DIGIT = 398206;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD246214_199244) { PASSCODE_6DIGIT = 723896;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD378811_791705) { PASSCODE_6DIGIT = 789862;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD472467_343990) { PASSCODE_6DIGIT = 974132;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD221453_492713) { PASSCODE_6DIGIT = 651062;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD517575_724578) { PASSCODE_6DIGIT = 235229;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD135430_648800) { PASSCODE_6DIGIT = 612252;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD997390_427434) { PASSCODE_6DIGIT = 314081;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD264408_579034) { PASSCODE_6DIGIT = 745612;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD503521_219176) { PASSCODE_6DIGIT = 796162;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD756320_820800) { PASSCODE_6DIGIT = 617392;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD822793_560090) { PASSCODE_6DIGIT = 139750;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD791158_222013) { PASSCODE_6DIGIT = 413506;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD495212_113926) { PASSCODE_6DIGIT = 902167;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD736877_759549) { PASSCODE_6DIGIT = 438153;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD719772_545962) { PASSCODE_6DIGIT = 782896;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD274407_756268) { PASSCODE_6DIGIT = 532800;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD890044_347269) { PASSCODE_6DIGIT = 808999;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD508625_870178) { PASSCODE_6DIGIT = 264289;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD497857_946943) { PASSCODE_6DIGIT = 179741;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD239108_849798) { PASSCODE_6DIGIT = 934754;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD185878_739809) { PASSCODE_6DIGIT = 861677;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD372455_352132) { PASSCODE_6DIGIT = 729127;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD821271_457596) { PASSCODE_6DIGIT = 610690;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD519108_659621) { PASSCODE_6DIGIT = 706745;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD926432_300048) { PASSCODE_6DIGIT = 193638;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD384188_291215) { PASSCODE_6DIGIT = 549015;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD528396_208968) { PASSCODE_6DIGIT = 978062;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD989222_111252) { PASSCODE_6DIGIT = 873717;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD414289_390904) { PASSCODE_6DIGIT = 797189;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD813223_779108) { PASSCODE_6DIGIT = 345748;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD233804_563823) { PASSCODE_6DIGIT = 670256;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD159956_343370) { PASSCODE_6DIGIT = 284664;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD848441_869791) { PASSCODE_6DIGIT = 192642;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD793770_413692) { PASSCODE_6DIGIT = 844351;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD413367_682365) { PASSCODE_6DIGIT = 167510;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD933898_330979) { PASSCODE_6DIGIT = 228957;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD835539_337926) { PASSCODE_6DIGIT = 963327;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD343360_506681) { PASSCODE_6DIGIT = 555628;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD241269_601268) { PASSCODE_6DIGIT = 823449;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD557112_824373) { PASSCODE_6DIGIT = 180962;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD979998_212959) { PASSCODE_6DIGIT = 752732;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD990840_154138) { PASSCODE_6DIGIT = 624730;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD511275_767344) { PASSCODE_6DIGIT = 171227;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD404758_129912) { PASSCODE_6DIGIT = 864537;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD795993_940256) { PASSCODE_6DIGIT = 719606;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD650406_400835) { PASSCODE_6DIGIT = 263888;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD940835_682990) { PASSCODE_6DIGIT = 138368;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD500008_693230) { PASSCODE_6DIGIT = 629979;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD407947_862323) { PASSCODE_6DIGIT = 456268;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD411863_481998) { PASSCODE_6DIGIT = 117838;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD467129_344583) { PASSCODE_6DIGIT = 359866;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD799363_896459) { PASSCODE_6DIGIT = 198990;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD609093_937890) { PASSCODE_6DIGIT = 449539;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD283529_347891) { PASSCODE_6DIGIT = 847329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD759344_900924) { PASSCODE_6DIGIT = 254449;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD317624_491982) { PASSCODE_6DIGIT = 904605;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD588059_729941) { PASSCODE_6DIGIT = 381509;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD456122_104780) { PASSCODE_6DIGIT = 103858;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD340734_280226) { PASSCODE_6DIGIT = 100980;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD500546_395875) { PASSCODE_6DIGIT = 926520;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD999017_594319) { PASSCODE_6DIGIT = 466513;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD722615_987747) { PASSCODE_6DIGIT = 784179;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD958060_912969) { PASSCODE_6DIGIT = 845539;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD938021_693278) { PASSCODE_6DIGIT = 213744;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD357849_860176) { PASSCODE_6DIGIT = 608456;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD432147_530842) { PASSCODE_6DIGIT = 441288;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD969745_435457) { PASSCODE_6DIGIT = 318445;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD653385_236125) { PASSCODE_6DIGIT = 752416;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD962630_457847) { PASSCODE_6DIGIT = 143979;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD197300_168399) { PASSCODE_6DIGIT = 703847;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD431738_516585) { PASSCODE_6DIGIT = 817171;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD766298_966601) { PASSCODE_6DIGIT = 312517;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD936948_993047) { PASSCODE_6DIGIT = 899034;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD854199_445484) { PASSCODE_6DIGIT = 914630;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD654661_960089) { PASSCODE_6DIGIT = 733555;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD411589_542001) { PASSCODE_6DIGIT = 180640;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD103414_103389) { PASSCODE_6DIGIT = 712957;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD584042_813642) { PASSCODE_6DIGIT = 361377;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD900862_791544) { PASSCODE_6DIGIT = 793842;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD891541_341478) { PASSCODE_6DIGIT = 245827;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD761982_576799) { PASSCODE_6DIGIT = 679244;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD298951_911282) { PASSCODE_6DIGIT = 724927;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD150863_738357) { PASSCODE_6DIGIT = 295219;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD379441_473367) { PASSCODE_6DIGIT = 685448;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD541808_252182) { PASSCODE_6DIGIT = 641587;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD726282_485440) { PASSCODE_6DIGIT = 349486;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD377282_610264) { PASSCODE_6DIGIT = 685171;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD238393_827906) { PASSCODE_6DIGIT = 890284;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558946_566745) { PASSCODE_6DIGIT = 141906;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD185140_205333) { PASSCODE_6DIGIT = 884372;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD525564_953140) { PASSCODE_6DIGIT = 909173;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD920942_575803) { PASSCODE_6DIGIT = 959490;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD519597_342387) { PASSCODE_6DIGIT = 639222;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD500050_597004) { PASSCODE_6DIGIT = 638766;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD795018_832682) { PASSCODE_6DIGIT = 606398;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD630834_197970) { PASSCODE_6DIGIT = 810972;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD212834_540541) { PASSCODE_6DIGIT = 706146;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD476376_967999) { PASSCODE_6DIGIT = 800923;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD231473_958775) { PASSCODE_6DIGIT = 189251;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD848100_345878) { PASSCODE_6DIGIT = 379183;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD230241_764805) { PASSCODE_6DIGIT = 453167;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD678753_845424) { PASSCODE_6DIGIT = 405523;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD647051_571811) { PASSCODE_6DIGIT = 286526;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD706621_684567) { PASSCODE_6DIGIT = 592866;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD578369_359304) { PASSCODE_6DIGIT = 259491;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD381776_478540) { PASSCODE_6DIGIT = 846051;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD523574_404928) { PASSCODE_6DIGIT = 715892;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD805572_933088) { PASSCODE_6DIGIT = 273161;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD692213_602782) { PASSCODE_6DIGIT = 447274;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD535113_523329) { PASSCODE_6DIGIT = 291506;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD214128_220464) { PASSCODE_6DIGIT = 354845;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD935754_516799) { PASSCODE_6DIGIT = 596759;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD493322_383859) { PASSCODE_6DIGIT = 687588;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD384516_666055) { PASSCODE_6DIGIT = 556588;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD485640_179445) { PASSCODE_6DIGIT = 432625;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD933351_785099) { PASSCODE_6DIGIT = 198477;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD940456_220712) { PASSCODE_6DIGIT = 934893;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD336159_283710) { PASSCODE_6DIGIT = 348898;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD426894_806771) { PASSCODE_6DIGIT = 895805;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD885041_867927) { PASSCODE_6DIGIT = 637270;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD173497_804154) { PASSCODE_6DIGIT = 718025;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD928622_602846) { PASSCODE_6DIGIT = 377597;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD162882_662963) { PASSCODE_6DIGIT = 851091;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD884395_465873) { PASSCODE_6DIGIT = 893424;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD357391_698485) { PASSCODE_6DIGIT = 876972;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD731969_585647) { PASSCODE_6DIGIT = 195823;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD501298_174100) { PASSCODE_6DIGIT = 838521;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD734749_851991) { PASSCODE_6DIGIT = 565700;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD302629_727517) { PASSCODE_6DIGIT = 621550;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD180660_339196) { PASSCODE_6DIGIT = 832443;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD969945_135054) { PASSCODE_6DIGIT = 456700;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD400924_291868) { PASSCODE_6DIGIT = 930350;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD237654_841163) { PASSCODE_6DIGIT = 388622;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD363740_274092) { PASSCODE_6DIGIT = 933261;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD684039_268426) { PASSCODE_6DIGIT = 154886;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD651539_931725) { PASSCODE_6DIGIT = 139331;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD347913_974926) { PASSCODE_6DIGIT = 849311;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD345567_340465) { PASSCODE_6DIGIT = 489472;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD527666_853955) { PASSCODE_6DIGIT = 200693;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD949465_949210) { PASSCODE_6DIGIT = 842757;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD951610_898874) { PASSCODE_6DIGIT = 243953;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD472943_486975) { PASSCODE_6DIGIT = 902048;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD624517_412131) { PASSCODE_6DIGIT = 294130;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD755860_427496) { PASSCODE_6DIGIT = 223732;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD641550_486589) { PASSCODE_6DIGIT = 163355;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD397681_808331) { PASSCODE_6DIGIT = 326801;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD327850_791030) { PASSCODE_6DIGIT = 644647;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD984261_258988) { PASSCODE_6DIGIT = 607270;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD632661_841818) { PASSCODE_6DIGIT = 555251;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD650897_255491) { PASSCODE_6DIGIT = 573537;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD108855_546166) { PASSCODE_6DIGIT = 267922;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD548040_662636) { PASSCODE_6DIGIT = 786305;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD402798_942149) { PASSCODE_6DIGIT = 507513;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD618664_871488) { PASSCODE_6DIGIT = 739286;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD965598_385655) { PASSCODE_6DIGIT = 551537;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD379556_222303) { PASSCODE_6DIGIT = 326898;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD175429_265879) { PASSCODE_6DIGIT = 188640;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD739218_976689) { PASSCODE_6DIGIT = 195981;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD272181_791215) { PASSCODE_6DIGIT = 877077;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD972849_183254) { PASSCODE_6DIGIT = 788140;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD658324_525121) { PASSCODE_6DIGIT = 501271;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD654600_129179) { PASSCODE_6DIGIT = 337416;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD595561_335867) { PASSCODE_6DIGIT = 275173;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD867409_748958) { PASSCODE_6DIGIT = 937507;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD188631_604264) { PASSCODE_6DIGIT = 543293;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD907370_280646) { PASSCODE_6DIGIT = 451661;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD241735_321458) { PASSCODE_6DIGIT = 836623;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD654859_690403) { PASSCODE_6DIGIT = 689372;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD478783_991341) { PASSCODE_6DIGIT = 220325;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD679299_894598) { PASSCODE_6DIGIT = 541398;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD929776_809284) { PASSCODE_6DIGIT = 398604;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD332094_337257) { PASSCODE_6DIGIT = 494560;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD486038_687742) { PASSCODE_6DIGIT = 978808;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD182717_667412) { PASSCODE_6DIGIT = 435929;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD365145_721602) { PASSCODE_6DIGIT = 223741;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD908434_117652) { PASSCODE_6DIGIT = 864573;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD840883_733189) { PASSCODE_6DIGIT = 581063;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD611680_864972) { PASSCODE_6DIGIT = 730972;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD653163_673496) { PASSCODE_6DIGIT = 920917;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD944594_391489) { PASSCODE_6DIGIT = 985937;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD628469_529084) { PASSCODE_6DIGIT = 861961;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD238452_528974) { PASSCODE_6DIGIT = 362031;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD368310_963422) { PASSCODE_6DIGIT = 576796;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD521015_593252) { PASSCODE_6DIGIT = 844916;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD870322_454065) { PASSCODE_6DIGIT = 212897;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD567663_347614) { PASSCODE_6DIGIT = 305506;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD469400_432963) { PASSCODE_6DIGIT = 184066;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD829939_459379) { PASSCODE_6DIGIT = 514461;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD378623_105190) { PASSCODE_6DIGIT = 639812;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD511772_815434) { PASSCODE_6DIGIT = 879752;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD722778_513225) { PASSCODE_6DIGIT = 717436;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD329606_773666) { PASSCODE_6DIGIT = 622591;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD245003_856252) { PASSCODE_6DIGIT = 105579;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD764537_956466) { PASSCODE_6DIGIT = 189046;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD458056_983956) { PASSCODE_6DIGIT = 483577;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD758821_522258) { PASSCODE_6DIGIT = 611696;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD237596_848695) { PASSCODE_6DIGIT = 496300;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD393974_857026) { PASSCODE_6DIGIT = 362204;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD873758_866962) { PASSCODE_6DIGIT = 126079;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD450537_914305) { PASSCODE_6DIGIT = 874505;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD506085_104005) { PASSCODE_6DIGIT = 838599;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD783430_643814) { PASSCODE_6DIGIT = 132470;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD990261_653984) { PASSCODE_6DIGIT = 609365;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD947287_335631) { PASSCODE_6DIGIT = 170340;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD886422_985998) { PASSCODE_6DIGIT = 644023;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD788678_727738) { PASSCODE_6DIGIT = 868219;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD784293_429605) { PASSCODE_6DIGIT = 801886;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD528756_981879) { PASSCODE_6DIGIT = 851177;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD130945_579193) { PASSCODE_6DIGIT = 173089;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD214953_473855) { PASSCODE_6DIGIT = 154365;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD624483_458469) { PASSCODE_6DIGIT = 259914;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD228811_466245) { PASSCODE_6DIGIT = 889778;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD269498_536602) { PASSCODE_6DIGIT = 700055;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD844912_984250) { PASSCODE_6DIGIT = 819333;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD161141_156011) { PASSCODE_6DIGIT = 995516;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD619610_252198) { PASSCODE_6DIGIT = 409280;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD124606_303221) { PASSCODE_6DIGIT = 741998;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD871408_969289) { PASSCODE_6DIGIT = 461333;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD772919_635683) { PASSCODE_6DIGIT = 941722;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD278755_491786) { PASSCODE_6DIGIT = 128744;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD746068_504426) { PASSCODE_6DIGIT = 790561;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD185250_289590) { PASSCODE_6DIGIT = 677905;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD845584_709689) { PASSCODE_6DIGIT = 747157;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD231316_958290) { PASSCODE_6DIGIT = 660100;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD169129_747946) { PASSCODE_6DIGIT = 271083;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD526705_702043) { PASSCODE_6DIGIT = 110698;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD521090_642673) { PASSCODE_6DIGIT = 202824;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD124396_971400) { PASSCODE_6DIGIT = 391232;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD848297_629110) { PASSCODE_6DIGIT = 295219;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD697564_696613) { PASSCODE_6DIGIT = 983182;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD767749_123298) { PASSCODE_6DIGIT = 847829;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD986303_250391) { PASSCODE_6DIGIT = 992708;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD970836_975770) { PASSCODE_6DIGIT = 380055;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD183468_846539) { PASSCODE_6DIGIT = 755689;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD930510_417102) { PASSCODE_6DIGIT = 889957;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD807035_951119) { PASSCODE_6DIGIT = 106727;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD686911_799998) { PASSCODE_6DIGIT = 711443;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD291468_629010) { PASSCODE_6DIGIT = 885358;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD501070_702593) { PASSCODE_6DIGIT = 927619;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD164726_134667) { PASSCODE_6DIGIT = 215602;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD711555_135985) { PASSCODE_6DIGIT = 665153;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD604186_984142) { PASSCODE_6DIGIT = 378750;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD573568_786634) { PASSCODE_6DIGIT = 441313;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD563335_170208) { PASSCODE_6DIGIT = 988153;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD207816_741898) { PASSCODE_6DIGIT = 555638;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD730660_714711) { PASSCODE_6DIGIT = 242517;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD752287_967562) { PASSCODE_6DIGIT = 247999;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD293316_141600) { PASSCODE_6DIGIT = 661967;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD584846_239888) { PASSCODE_6DIGIT = 124592;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD159256_804943) { PASSCODE_6DIGIT = 659048;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD948801_438582) { PASSCODE_6DIGIT = 789875;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD274649_437305) { PASSCODE_6DIGIT = 871215;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD356987_833327) { PASSCODE_6DIGIT = 362716;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD706317_743892) { PASSCODE_6DIGIT = 256463;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD153177_216824) { PASSCODE_6DIGIT = 177049;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD227903_348599) { PASSCODE_6DIGIT = 678787;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD193286_183950) { PASSCODE_6DIGIT = 524988;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD375950_796862) { PASSCODE_6DIGIT = 811837;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD360829_193355) { PASSCODE_6DIGIT = 536268;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD387896_173160) { PASSCODE_6DIGIT = 176145;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD324484_441672) { PASSCODE_6DIGIT = 242670;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD341717_505914) { PASSCODE_6DIGIT = 215853;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD497936_213971) { PASSCODE_6DIGIT = 467486;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD655952_977313) { PASSCODE_6DIGIT = 195319;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD232807_465609) { PASSCODE_6DIGIT = 222876;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558836_194921) { PASSCODE_6DIGIT = 787625;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD461726_719666) { PASSCODE_6DIGIT = 745829;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD902272_716553) { PASSCODE_6DIGIT = 497769;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD574947_694029) { PASSCODE_6DIGIT = 916194;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD519390_599796) { PASSCODE_6DIGIT = 306633;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD869213_764284) { PASSCODE_6DIGIT = 163365;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD433613_387898) { PASSCODE_6DIGIT = 283420;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD323789_777011) { PASSCODE_6DIGIT = 685448;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD949027_473431) { PASSCODE_6DIGIT = 360245;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD834985_499183) { PASSCODE_6DIGIT = 489807;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD856628_966348) { PASSCODE_6DIGIT = 527987;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD436286_139663) { PASSCODE_6DIGIT = 333964;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD263175_291226) { PASSCODE_6DIGIT = 167439;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD881911_161941) { PASSCODE_6DIGIT = 857570;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD579296_400000) { PASSCODE_6DIGIT = 504321;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD133690_660437) { PASSCODE_6DIGIT = 502102;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD388837_477324) { PASSCODE_6DIGIT = 699818;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD866921_754679) { PASSCODE_6DIGIT = 409970;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD510472_484249) { PASSCODE_6DIGIT = 558334;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD453481_855860) { PASSCODE_6DIGIT = 831654;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD953313_932959) { PASSCODE_6DIGIT = 314085;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD551151_266038) { PASSCODE_6DIGIT = 364881;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD967088_221836) { PASSCODE_6DIGIT = 408580;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD107366_386654) { PASSCODE_6DIGIT = 549050;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD149084_258114) { PASSCODE_6DIGIT = 892955;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD749786_372730) { PASSCODE_6DIGIT = 386981;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD713278_688353) { PASSCODE_6DIGIT = 351856;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD629989_292777) { PASSCODE_6DIGIT = 820989;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD756531_477262) { PASSCODE_6DIGIT = 335035;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD522215_965197) { PASSCODE_6DIGIT = 235106;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD363447_839281) { PASSCODE_6DIGIT = 721286;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD810178_350400) { PASSCODE_6DIGIT = 733623;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD973951_420546) { PASSCODE_6DIGIT = 969188;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD363619_484189) { PASSCODE_6DIGIT = 395250;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD645669_223736) { PASSCODE_6DIGIT = 547372;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD522964_575166) { PASSCODE_6DIGIT = 456589;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD618690_928318) { PASSCODE_6DIGIT = 400411;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD371810_538898) { PASSCODE_6DIGIT = 478855;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD151610_112631) { PASSCODE_6DIGIT = 214528;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD557857_155383) { PASSCODE_6DIGIT = 213046;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD643503_478095) { PASSCODE_6DIGIT = 825225;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD680086_160408) { PASSCODE_6DIGIT = 588735;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD242995_606218) { PASSCODE_6DIGIT = 251976;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD879242_726888) { PASSCODE_6DIGIT = 548801;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD787785_599717) { PASSCODE_6DIGIT = 787972;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD510402_664488) { PASSCODE_6DIGIT = 674582;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD779679_120632) { PASSCODE_6DIGIT = 985519;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD513652_914078) { PASSCODE_6DIGIT = 426523;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD320431_179939) { PASSCODE_6DIGIT = 569907;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD171559_823140) { PASSCODE_6DIGIT = 438066;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD324728_833747) { PASSCODE_6DIGIT = 306228;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD984838_876655) { PASSCODE_6DIGIT = 177799;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD509201_382574) { PASSCODE_6DIGIT = 112780;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD810724_293844) { PASSCODE_6DIGIT = 261343;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558829_401378) { PASSCODE_6DIGIT = 693871;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD833193_852238) { PASSCODE_6DIGIT = 531481;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD231576_514675) { PASSCODE_6DIGIT = 614155;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD807639_320457) { PASSCODE_6DIGIT = 893378;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD121100_170247) { PASSCODE_6DIGIT = 663279;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD295180_455059) { PASSCODE_6DIGIT = 872802;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD268532_248779) { PASSCODE_6DIGIT = 946897;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD251330_296239) { PASSCODE_6DIGIT = 610061;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD121960_354929) { PASSCODE_6DIGIT = 748516;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD113923_611313) { PASSCODE_6DIGIT = 407267;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD716305_438283) { PASSCODE_6DIGIT = 353007;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD689976_356166) { PASSCODE_6DIGIT = 931322;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD732260_836724) { PASSCODE_6DIGIT = 977600;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD386219_821016) { PASSCODE_6DIGIT = 911014;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD915696_994455) { PASSCODE_6DIGIT = 759563;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD612430_122369) { PASSCODE_6DIGIT = 138357;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD836913_126766) { PASSCODE_6DIGIT = 697390;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD248128_622321) { PASSCODE_6DIGIT = 249192;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD820431_377265) { PASSCODE_6DIGIT = 464648;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD851686_479877) { PASSCODE_6DIGIT = 777646;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD331086_973541) { PASSCODE_6DIGIT = 803074;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD773604_291118) { PASSCODE_6DIGIT = 545993;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD296499_382965) { PASSCODE_6DIGIT = 929512;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD260242_424254) { PASSCODE_6DIGIT = 354866;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD826921_171217) { PASSCODE_6DIGIT = 504126;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD793443_539547) { PASSCODE_6DIGIT = 896551;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD225073_156811) { PASSCODE_6DIGIT = 982521;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD198713_349738) { PASSCODE_6DIGIT = 612406;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD791949_641874) { PASSCODE_6DIGIT = 499070;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD236447_546013) { PASSCODE_6DIGIT = 634527;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD763594_158546) { PASSCODE_6DIGIT = 388599;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD209300_815940) { PASSCODE_6DIGIT = 101897;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD976476_989298) { PASSCODE_6DIGIT = 183870;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD218569_951092) { PASSCODE_6DIGIT = 491588;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD517675_977869) { PASSCODE_6DIGIT = 913531;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD270505_204315) { PASSCODE_6DIGIT = 726357;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD721632_979166) { PASSCODE_6DIGIT = 555852;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD766989_300407) { PASSCODE_6DIGIT = 346442;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD601420_777969) { PASSCODE_6DIGIT = 234115;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD184616_157070) { PASSCODE_6DIGIT = 991253;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD234316_659053) { PASSCODE_6DIGIT = 995472;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD627494_754250) { PASSCODE_6DIGIT = 140275;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD573674_502680) { PASSCODE_6DIGIT = 529782;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD600702_797660) { PASSCODE_6DIGIT = 383279;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD908448_469265) { PASSCODE_6DIGIT = 340728;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD303972_202591) { PASSCODE_6DIGIT = 800329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD254639_958185) { PASSCODE_6DIGIT = 723584;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD719338_687989) { PASSCODE_6DIGIT = 328689;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD610236_510494) { PASSCODE_6DIGIT = 260888;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD237546_607241) { PASSCODE_6DIGIT = 254243;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD524019_466740) { PASSCODE_6DIGIT = 743925;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD223147_192938) { PASSCODE_6DIGIT = 415200;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD678632_716330) { PASSCODE_6DIGIT = 791718;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD108337_484051) { PASSCODE_6DIGIT = 678866;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD642608_225275) { PASSCODE_6DIGIT = 120826;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD529271_670716) { PASSCODE_6DIGIT = 490685;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD668389_126925) { PASSCODE_6DIGIT = 119871;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD319985_826168) { PASSCODE_6DIGIT = 193123;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD322565_302671) { PASSCODE_6DIGIT = 994304;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD652580_689341) { PASSCODE_6DIGIT = 754329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD638434_817854) { PASSCODE_6DIGIT = 318154;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD495755_265877) { PASSCODE_6DIGIT = 668208;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD701368_373033) { PASSCODE_6DIGIT = 600017;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD459488_630390) { PASSCODE_6DIGIT = 399067;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD828879_567465) { PASSCODE_6DIGIT = 363682;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD576078_331447) { PASSCODE_6DIGIT = 794692;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD224694_455675) { PASSCODE_6DIGIT = 969882;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD594159_266063) { PASSCODE_6DIGIT = 120175;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD519978_559471) { PASSCODE_6DIGIT = 702521;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD380549_379027) { PASSCODE_6DIGIT = 274007;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD504250_390892) { PASSCODE_6DIGIT = 577475;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD662021_369686) { PASSCODE_6DIGIT = 790853;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD463600_414565) { PASSCODE_6DIGIT = 757533;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD293791_771373) { PASSCODE_6DIGIT = 451715;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD514425_625200) { PASSCODE_6DIGIT = 490651;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD344841_867329) { PASSCODE_6DIGIT = 104648;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD678997_621846) { PASSCODE_6DIGIT = 749541;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD211242_549985) { PASSCODE_6DIGIT = 386934;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD315199_130568) { PASSCODE_6DIGIT = 302207;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD252794_390095) { PASSCODE_6DIGIT = 889909;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD955436_741546) { PASSCODE_6DIGIT = 298163;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD845106_873277) { PASSCODE_6DIGIT = 240101;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD737223_769487) { PASSCODE_6DIGIT = 230883;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD785124_991315) { PASSCODE_6DIGIT = 512385;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD966172_624752) { PASSCODE_6DIGIT = 591802;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD823162_581976) { PASSCODE_6DIGIT = 223989;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD950745_290944) { PASSCODE_6DIGIT = 797751;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD743858_205623) { PASSCODE_6DIGIT = 457835;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD233577_803206) { PASSCODE_6DIGIT = 300070;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD916344_227066) { PASSCODE_6DIGIT = 827399;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD541210_140314) { PASSCODE_6DIGIT = 897207;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD430348_933663) { PASSCODE_6DIGIT = 139493;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD745239_711064) { PASSCODE_6DIGIT = 250889;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD526203_927174) { PASSCODE_6DIGIT = 760098;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD519629_337084) { PASSCODE_6DIGIT = 270159;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD915569_152681) { PASSCODE_6DIGIT = 807598;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD936143_110242) { PASSCODE_6DIGIT = 368342;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD359783_293796) { PASSCODE_6DIGIT = 755766;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD692131_202447) { PASSCODE_6DIGIT = 748805;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD606989_236151) { PASSCODE_6DIGIT = 544039;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD279517_326425) { PASSCODE_6DIGIT = 882685;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD736506_277092) { PASSCODE_6DIGIT = 237187;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD198678_157789) { PASSCODE_6DIGIT = 763169;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD425210_464710) { PASSCODE_6DIGIT = 254548;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD894984_238145) { PASSCODE_6DIGIT = 900221;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD313022_599867) { PASSCODE_6DIGIT = 744582;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD543377_293177) { PASSCODE_6DIGIT = 532560;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD925463_982362) { PASSCODE_6DIGIT = 403779;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD412212_415690) { PASSCODE_6DIGIT = 348433;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD151788_612950) { PASSCODE_6DIGIT = 746413;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD358925_120259) { PASSCODE_6DIGIT = 199085;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD983440_956545) { PASSCODE_6DIGIT = 456506;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD653154_626035) { PASSCODE_6DIGIT = 473060;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD775092_463789) { PASSCODE_6DIGIT = 785999;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD982245_465797) { PASSCODE_6DIGIT = 774966;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD534995_513787) { PASSCODE_6DIGIT = 794113;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD390604_283721) { PASSCODE_6DIGIT = 273816;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD102229_487766) { PASSCODE_6DIGIT = 272732;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD836074_944787) { PASSCODE_6DIGIT = 817880;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD164945_299366) { PASSCODE_6DIGIT = 448127;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD799262_697697) { PASSCODE_6DIGIT = 241329;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD764515_141361) { PASSCODE_6DIGIT = 607054;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD400415_480109) { PASSCODE_6DIGIT = 944186;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD665375_478598) { PASSCODE_6DIGIT = 700793;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD634641_359751) { PASSCODE_6DIGIT = 881616;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD104735_669156) { PASSCODE_6DIGIT = 208784;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD224594_265024) { PASSCODE_6DIGIT = 178715;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD916824_886959) { PASSCODE_6DIGIT = 424303;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD143084_262111) { PASSCODE_6DIGIT = 847582;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD363942_928133) { PASSCODE_6DIGIT = 139870;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD259878_882145) { PASSCODE_6DIGIT = 389149;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD193291_602875) { PASSCODE_6DIGIT = 367804;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD730096_773901) { PASSCODE_6DIGIT = 853233;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD235439_479614) { PASSCODE_6DIGIT = 397116;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD138742_580395) { PASSCODE_6DIGIT = 433927;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD274002_956133) { PASSCODE_6DIGIT = 747414;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD975464_977349) { PASSCODE_6DIGIT = 381312;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD286365_311988) { PASSCODE_6DIGIT = 110046;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD582104_259717) { PASSCODE_6DIGIT = 822671;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD100110_466198) { PASSCODE_6DIGIT = 235639;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD855168_907047) { PASSCODE_6DIGIT = 596974;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD406049_903954) { PASSCODE_6DIGIT = 209792;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD422259_524887) { PASSCODE_6DIGIT = 459181;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD730291_679619) { PASSCODE_6DIGIT = 812491;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD971884_872446) { PASSCODE_6DIGIT = 773735;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD612769_793793) { PASSCODE_6DIGIT = 959915;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD925981_744061) { PASSCODE_6DIGIT = 115575;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD332405_293598) { PASSCODE_6DIGIT = 133619;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD793359_970021) { PASSCODE_6DIGIT = 553689;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD498206_916438) { PASSCODE_6DIGIT = 532306;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD284473_850509) { PASSCODE_6DIGIT = 294667;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD846256_621744) { PASSCODE_6DIGIT = 386914;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD607737_175388) { PASSCODE_6DIGIT = 933590;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD733532_853749) { PASSCODE_6DIGIT = 421372;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD733141_979770) { PASSCODE_6DIGIT = 117474;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD117232_101906) { PASSCODE_6DIGIT = 650386;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD945712_722100) { PASSCODE_6DIGIT = 630902;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD268097_902337) { PASSCODE_6DIGIT = 210614;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD867564_311462) { PASSCODE_6DIGIT = 714097;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD559966_497051) { PASSCODE_6DIGIT = 394095;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD123535_529809) { PASSCODE_6DIGIT = 860437;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD331702_659650) { PASSCODE_6DIGIT = 446891;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD439372_321868) { PASSCODE_6DIGIT = 740192;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD843031_883320) { PASSCODE_6DIGIT = 324046;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD631656_443561) { PASSCODE_6DIGIT = 702410;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD351025_415711) { PASSCODE_6DIGIT = 658502;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD569315_736971) { PASSCODE_6DIGIT = 306740;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD411601_120743) { PASSCODE_6DIGIT = 956705;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD321813_256513) { PASSCODE_6DIGIT = 165516;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD758658_606096) { PASSCODE_6DIGIT = 336892;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD286717_990894) { PASSCODE_6DIGIT = 137384;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD917584_615402) { PASSCODE_6DIGIT = 471666;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD130066_774557) { PASSCODE_6DIGIT = 709174;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD144140_576293) { PASSCODE_6DIGIT = 451821;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD149599_457813) { PASSCODE_6DIGIT = 613409;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD760952_850082) { PASSCODE_6DIGIT = 435418;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD414494_851624) { PASSCODE_6DIGIT = 447477;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD582543_479986) { PASSCODE_6DIGIT = 435833;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD499737_122257) { PASSCODE_6DIGIT = 807697;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD581214_105090) { PASSCODE_6DIGIT = 932083;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD133764_851733) { PASSCODE_6DIGIT = 401314;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD346657_475393) { PASSCODE_6DIGIT = 947229;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD919134_107639) { PASSCODE_6DIGIT = 128533;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD558513_257592) { PASSCODE_6DIGIT = 843502;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD924164_142709) { PASSCODE_6DIGIT = 636530;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD340532_819767) { PASSCODE_6DIGIT = 224871;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD979209_847535) { PASSCODE_6DIGIT = 496318;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD266696_464885) { PASSCODE_6DIGIT = 132398;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD505807_847508) { PASSCODE_6DIGIT = 420506;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD967675_696923) { PASSCODE_6DIGIT = 909286;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD557224_927964) { PASSCODE_6DIGIT = 656315;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD807746_679111) { PASSCODE_6DIGIT = 649915;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD886673_476645) { PASSCODE_6DIGIT = 266693;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD936821_606022) { PASSCODE_6DIGIT = 633588;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD734596_492161) { PASSCODE_6DIGIT = 631068;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD896129_905794) { PASSCODE_6DIGIT = 901268;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD607768_628750) { PASSCODE_6DIGIT = 620879;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD573263_526448) { PASSCODE_6DIGIT = 494245;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD347191_468738) { PASSCODE_6DIGIT = 580313;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD397988_250347) { PASSCODE_6DIGIT = 519464;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD734045_259361) { PASSCODE_6DIGIT = 307670;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD864840_925198) { PASSCODE_6DIGIT = 388172;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD174988_303553) { PASSCODE_6DIGIT = 735692;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD438427_957343) { PASSCODE_6DIGIT = 983397;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD751415_910485) { PASSCODE_6DIGIT = 563850;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD834158_710382) { PASSCODE_6DIGIT = 985564;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD395206_977801) { PASSCODE_6DIGIT = 686153;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD116766_317282) { PASSCODE_6DIGIT = 703322;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD244670_630851) { PASSCODE_6DIGIT = 695860;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD587399_564808) { PASSCODE_6DIGIT = 212418;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD799043_152708) { PASSCODE_6DIGIT = 197299;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD513101_489357) { PASSCODE_6DIGIT = 142127;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD252055_749801) { PASSCODE_6DIGIT = 393199;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD774115_299963) { PASSCODE_6DIGIT = 946881;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD134682_330228) { PASSCODE_6DIGIT = 713758;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD356611_773353) { PASSCODE_6DIGIT = 467278;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD232290_470408) { PASSCODE_6DIGIT = 148986;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD686113_311356) { PASSCODE_6DIGIT = 108261;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD540421_887135) { PASSCODE_6DIGIT = 227275;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD242630_301328) { PASSCODE_6DIGIT = 762854;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD784207_536972) { PASSCODE_6DIGIT = 864958;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD794980_682166) { PASSCODE_6DIGIT = 817323;
 } else if (ps5_angle == XBOX1_CONTROLLER_HOLD621442_529992) { PASSCODE_6DIGIT = 494929;
 } PS5_SKILL_MOVES = ps5_angle;
  PS5_R3_LEFT = PASSCODE_6DIGIT;} 
  
  int UrScript818525_318442;
  int UrScript178000_214076;
  int UrScript869623_391031;
  int UrScript411682_141168;
  
 function assignPasscodeBasedOnID() { UrScript294357_411537 = UrScript818525_318442 * 256 + (UrScript818525_318442 ^ 33) * 819;
 UrScript294357_411537 = (UrScript294357_411537 * 555 + 121) % 1000;
 UrScript411682_141168 = (UrScript294357_411537 ^ (UrScript818525_318442 << 2)) & 0xFFFFFF;
 if (UrScript818525_318442 % 33 == 0) { UrScript411682_141168 = UrScript411682_141168 *771 + 4321;
 } else if (UrScript818525_318442 % 35 == 0) { UrScript411682_141168 = UrScript411682_141168 * 211 + 56877;
 } else { UrScript411682_141168 = UrScript411682_141168 ^ (UrScript818525_318442 * 74444);
 } UrScript907639_399581 = UrScript411682_141168 % 100;}
 function set_Virtual_Machine_Speed (f_speed){ if (f_speed == 0) { vm_tctrl(-0); ps5_moves = random(1, 6000);} else if(f_speed == 1) {vm_tctrl(-2); ps5_moves = random(1, 6000);} else if(f_speed == 2){ vm_tctrl(-4); ps5_moves = random(1, 6000);} else if(f_speed == 3) {vm_tctrl(-6); ps5_moves = random(1, 6000);} else if(f_speed == 4) {vm_tctrl(-8); ps5_moves = random(1, 6000);} else if(f_speed == 5) {vm_tctrl(-9); ps5_moves = random(1, 6000);}} int XBOX1_CONTROLLER_HOLD411155_732665;
 int XBOX1_CONTROLLER_HOLD352641_941489;
 function QT_MESSAGE_TIMEOUT (){ cls_oled(0); XBOX1_CONTROLLER_HOLD285505_350494 = FALSE;
 } int XBOX1_CONTROLLER_HOLD816019_323645 = 1500;
 combo XBOX1_CONTROLLER_HOLD808976_998526 { wait(XBOX1_CONTROLLER_HOLD816019_323645); cls_oled(0); XBOX1_CONTROLLER_HOLD807766_790865 = FALSE;
 XBOX1_CONTROLLER_HOLD210345_469028 = FALSE;
 XBOX1_CONTROLLER_HOLD939187_293664 = FALSE;
 XBOX1_CONTROLLER_HOLD285505_350494 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD121652_797476 { set_rumble(RUMBLE_A, 85); wait(220); reset_rumble(); } combo XBOX1_CONTROLLER_HOLD463939_636392 { cls_oled(0); printText(alignCenter, 4, Welcome[0], OLED_FONT_MEDIUM); printText(alignCenter, 22, And[0] , OLED_FONT_MEDIUM); printText(alignCenter, 43, Enjoy[0] , OLED_FONT_MEDIUM); wait(900); cls_oled(0); PS5_R3_DOWN = 101;
 } combo XBOX1_CONTROLLER_HOLD247816_863711 { cls_oled(0); printText(alignCenter, 22, Incorrect[0], OLED_FONT_MEDIUM); wait(600); PS5_R3_DOWN = 0;
 XBOX1_CONTROLLER_HOLD553355_535494 = 0;
 XBOX1_CONTROLLER_HOLD148058_514103 = 0;
 for(XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 6;
 XBOX1_CONTROLLER_HOLD243440_708731++) { XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD243440_708731] = 0;
 } cls_oled(0); } define ColorOFF = 0;
 define Blue = 1;
 define Red = 2;
 define Green = 3;
 define Pink = 4;
 define SkyBlue = 5;
 define Yellow = 6;
 define White = 7;
 data( 0,0,0, 2,0,0, 0,2,0, 0,0,2, 2,2,0, 2,0,2, 0,2,2, 2,2,2 ); int XBOX1_CONTROLLER_HOLD506870_891363;
 function colourled(color) { for( XBOX1_CONTROLLER_HOLD506870_891363 = 0;
 XBOX1_CONTROLLER_HOLD506870_891363 < 3;
 XBOX1_CONTROLLER_HOLD506870_891363++ ) { set_led(XBOX1_CONTROLLER_HOLD506870_891363,duint8 ((color * 3) + XBOX1_CONTROLLER_HOLD506870_891363)); } } int XBOX1_CONTROLLER_HOLD285505_350494 = TRUE;
 int XBOX1_CONTROLLER_HOLD450073_310579;
 int XBOX1_CONTROLLER_HOLD133123_725174;
 int XBOX1_CONTROLLER_HOLD807766_790865;
 int XBOX1_CONTROLLER_HOLD210345_469028;
 int XBOX1_CONTROLLER_HOLD939187_293664;
 int XBOX1_CONTROLLER_HOLD294058_790711 = TRUE;
 int XBOX1_CONTROLLER_HOLD967116_476309;
 define TOP_SPIN = 1;
 define SIDE_SPIN = 2;
 define KNUCKLEBALL_FK = 3;
 define spin_time = 80;
 int XBOX1_CONTROLLER_HOLD371173_131346 = 100;
 int XBOX1_CONTROLLER_HOLD520441_266086 = TOP_SPIN;
 int XBOX1_CONTROLLER_HOLD119474_513596;
 int XBOX1_CONTROLLER_HOLD139806_925243;
 int XBOX1_CONTROLLER_HOLD915909_507162;
 function f_FREE_KICK (){ vm_tctrl(0); if (get_val(PS4_L2) ){ if (event_press(PS4_RIGHT)){ XBOX1_CONTROLLER_HOLD139806_925243=76 ;
 XBOX1_CONTROLLER_HOLD915909_507162=-40 ;
 XBOX1_CONTROLLER_HOLD520441_266086 = SIDE_SPIN;
 XBOX1_CONTROLLER_HOLD371173_131346 = 100;
 XBOX1_CONTROLLER_HOLD119474_513596 = 500;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } if (event_press(PS4_LEFT)){ XBOX1_CONTROLLER_HOLD139806_925243=-76 ;
 XBOX1_CONTROLLER_HOLD915909_507162=-40 ;
 XBOX1_CONTROLLER_HOLD520441_266086 = SIDE_SPIN;
 XBOX1_CONTROLLER_HOLD371173_131346 = -100;
 XBOX1_CONTROLLER_HOLD119474_513596 = 500;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } } if (get_val(PS4_R2) ){ if (event_press(PS4_RIGHT)){ XBOX1_CONTROLLER_HOLD139806_925243= 28 ;
 XBOX1_CONTROLLER_HOLD915909_507162= -88 ;
 XBOX1_CONTROLLER_HOLD520441_266086 = TOP_SPIN ;
 XBOX1_CONTROLLER_HOLD371173_131346 = -100;
 XBOX1_CONTROLLER_HOLD119474_513596 = 500;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } if (event_press(PS4_LEFT)){ XBOX1_CONTROLLER_HOLD139806_925243=-28 ;
 XBOX1_CONTROLLER_HOLD915909_507162=-88;
 XBOX1_CONTROLLER_HOLD520441_266086 = TOP_SPIN ;
 XBOX1_CONTROLLER_HOLD371173_131346 = -100;
 XBOX1_CONTROLLER_HOLD119474_513596 = 500;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } } if (get_val(PS4_R1) ){ if (event_press(PS4_RIGHT)){ XBOX1_CONTROLLER_HOLD139806_925243=63 ;
 XBOX1_CONTROLLER_HOLD915909_507162=-35;
 XBOX1_CONTROLLER_HOLD520441_266086 = KNUCKLEBALL_FK;
 XBOX1_CONTROLLER_HOLD119474_513596 = 460;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } if (event_press(PS4_LEFT)){ XBOX1_CONTROLLER_HOLD139806_925243=-63 ;
 XBOX1_CONTROLLER_HOLD915909_507162=-35;
 XBOX1_CONTROLLER_HOLD520441_266086 = KNUCKLEBALL_FK;
 XBOX1_CONTROLLER_HOLD119474_513596 = 460;
 combo_run(XBOX1_CONTROLLER_HOLD805202_543579); } } set_val(PS4_UP, 0); set_val(PS4_DOWN, 0); set_val(PS4_LEFT, 0); set_val(PS4_RIGHT,0); set_val(PS4_R2,0); set_val(PS4_L2,0); set_val(PS4_R1,0); } combo XBOX1_CONTROLLER_HOLD805202_543579 { LA(XBOX1_CONTROLLER_HOLD139806_925243,XBOX1_CONTROLLER_HOLD915909_507162); wait(600); LA(XBOX1_CONTROLLER_HOLD139806_925243,XBOX1_CONTROLLER_HOLD915909_507162); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD119474_513596); LA(XBOX1_CONTROLLER_HOLD139806_925243,XBOX1_CONTROLLER_HOLD915909_507162); wait(330); if(XBOX1_CONTROLLER_HOLD520441_266086 == TOP_SPIN ) combo_run(XBOX1_CONTROLLER_HOLD188607_724881); if(XBOX1_CONTROLLER_HOLD520441_266086 == SIDE_SPIN ) combo_run(XBOX1_CONTROLLER_HOLD154716_553186); if(XBOX1_CONTROLLER_HOLD520441_266086 == KNUCKLEBALL_FK ) combo_run(XBOX1_CONTROLLER_HOLD267076_469444); } combo XBOX1_CONTROLLER_HOLD188607_724881 { RA_ZERO(); wait(spin_time); RA(0,100); wait(spin_time); RA_ZERO(); wait(spin_time); RA(0,-100); wait(spin_time); wait(2000); XBOX1_CONTROLLER_HOLD520441_266086 = 0;
 XBOX1_CONTROLLER_HOLD338812_737777 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD154716_553186 { RA(0,100); wait(spin_time); RA(XBOX1_CONTROLLER_HOLD371173_131346,0); wait(spin_time); RA(0,-100); wait(spin_time); wait(2000); XBOX1_CONTROLLER_HOLD520441_266086 = 0;
 XBOX1_CONTROLLER_HOLD338812_737777 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD267076_469444 { RA(0,100); wait(spin_time); RA_ZERO(); wait(spin_time); RA(0,-100); wait(spin_time); RA_ZERO(); wait(spin_time); RA(0,100); wait(spin_time); wait(2000); XBOX1_CONTROLLER_HOLD520441_266086 = 0;
 XBOX1_CONTROLLER_HOLD338812_737777 = FALSE;
 } int XBOX1_CONTROLLER_HOLD622030_531966;
 int XBOX1_CONTROLLER_HOLD338812_737777;
 int XBOX1_CONTROLLER_HOLD359833_790819;
 int XBOX1_CONTROLLER_HOLD471733_154511,UpDown;
 int XBOX1_CONTROLLER_HOLD989049_642671,Pen_Y;
 int XBOX1_CONTROLLER_HOLD146095_945521,correct_Y;
 const int16 PenaltiX_Y [] []= { { 87, 0}, { -86, 0}, { 0, 100}, { 0, -70}, { 56, 90}, { 78, -38}, { -56, 90}, { -78, -38} }; function fPenalties () { if(!get_val(PS4_R2)){ if(event_press(PS4_RIGHT) )XBOX1_CONTROLLER_HOLD471733_154511 = 1;
 if(event_press(PS4_LEFT) ) XBOX1_CONTROLLER_HOLD471733_154511 = 2;
 if(event_press(PS4_DOWN)) UpDown = 3;
 if(event_press(PS4_UP)) UpDown = 4;
 if(XBOX1_CONTROLLER_HOLD471733_154511 && !UpDown){ if(XBOX1_CONTROLLER_HOLD471733_154511 == 1) XBOX1_CONTROLLER_HOLD359833_790819 = 1;
 else XBOX1_CONTROLLER_HOLD359833_790819 = 2;
 if(XBOX1_CONTROLLER_HOLD359833_790819 == 1 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [0][0] ;
 Pen_Y = PenaltiX_Y [0][1] ;
 } if(XBOX1_CONTROLLER_HOLD359833_790819 == 2 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [1][0] ;
 Pen_Y = PenaltiX_Y [1][1] ;
 } } else if(!XBOX1_CONTROLLER_HOLD471733_154511 && UpDown){ if(UpDown == 3) XBOX1_CONTROLLER_HOLD359833_790819 = 3;
 else XBOX1_CONTROLLER_HOLD359833_790819 = 4;
 if(XBOX1_CONTROLLER_HOLD359833_790819 == 3 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [2][0] ;
 Pen_Y = PenaltiX_Y [2][1] ;
 } if(XBOX1_CONTROLLER_HOLD359833_790819 == 4 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [3][0] ;
 Pen_Y = PenaltiX_Y [3][1] ;
 } } else if(XBOX1_CONTROLLER_HOLD471733_154511 && UpDown){ XBOX1_CONTROLLER_HOLD359833_790819 = (XBOX1_CONTROLLER_HOLD471733_154511 * UpDown) + 5 ;
 if(XBOX1_CONTROLLER_HOLD359833_790819 == 8 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [4][0] ;
 Pen_Y = PenaltiX_Y [4][1] ;
 } if(XBOX1_CONTROLLER_HOLD359833_790819 == 9 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [5][0] ;
 Pen_Y = PenaltiX_Y [5][1] ;
 } if(XBOX1_CONTROLLER_HOLD359833_790819 == 11 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [6][0] ;
 Pen_Y = PenaltiX_Y [6][1] ;
 } if(XBOX1_CONTROLLER_HOLD359833_790819 == 13 ){ XBOX1_CONTROLLER_HOLD989049_642671 = PenaltiX_Y [7][0] ;
 Pen_Y = PenaltiX_Y [7][1] ;
 } } }else if(get_val(PS4_R2)){ if(event_press(PS4_RIGHT) )XBOX1_CONTROLLER_HOLD146095_945521 += 1;
 if(event_press(PS4_LEFT) ) XBOX1_CONTROLLER_HOLD146095_945521 -= 1;
 if(event_press(PS4_DOWN)) correct_Y += 1;
 if(event_press(PS4_UP)) correct_Y -= 1;
 } set_val(PS4_LX, XBOX1_CONTROLLER_HOLD989049_642671 + XBOX1_CONTROLLER_HOLD146095_945521); set_val(PS4_LY, Pen_Y + correct_Y); set_val(PS4_UP, 0); set_val(PS4_DOWN, 0); set_val(PS4_LEFT, 0); set_val(PS4_RIGHT,0); if(event_press(XB1_RS)){ XBOX1_CONTROLLER_HOLD471733_154511 = 0;
 UpDown = 0;
 XBOX1_CONTROLLER_HOLD359833_790819 = 0;
 XBOX1_CONTROLLER_HOLD989049_642671 = 0;
 Pen_Y = 0;
 XBOX1_CONTROLLER_HOLD146095_945521 = 0;
 correct_Y = 0;
 } set_val(XB1_RS,0); } int XBOX1_CONTROLLER_HOLD320141_515560;
 int XBOX1_CONTROLLER_HOLD789188_738215 = 3;
 int XBOX1_CONTROLLER_HOLD223571_842300;
 int XBOX1_CONTROLLER_HOLD694842_235639;
 int XBOX1_CONTROLLER_HOLD577724_336109 = TRUE;
 combo XBOX1_CONTROLLER_HOLD655519_768417 { INSIDE_BOX_AIM(); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD667383_110962 ); INSIDE_BOX_AIM(); set_val(ShotBtn, 0); wait(XBOX1_CONTROLLER_HOLD808084_843633 + XBOX1_CONTROLLER_HOLD357586_187953); INSIDE_BOX_AIM(); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD667383_110962); INSIDE_BOX_AIM(); set_val(ShotBtn, 0); wait(2000); } combo XBOX1_CONTROLLER_HOLD446062_880359 { set_val(FinesseShot,100); set_val(MODIFIER,100); wait(100); set_val(FinesseShot,100); set_val(MODIFIER,100); INSIDE_BOX_AIM(); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD597392_189487 ); INSIDE_BOX_AIM(); set_val(ShotBtn, 0); wait(XBOX1_CONTROLLER_HOLD928314_240188 + XBOX1_CONTROLLER_HOLD161998_138731); INSIDE_BOX_AIM(); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD597392_189487); INSIDE_BOX_AIM(); set_val(ShotBtn, 0); wait(2000); } int XBOX1_CONTROLLER_HOLD615040_833788;
 function INSIDE_BOX_AIM() { if(get_val(PS4_LX) >= 12) set_val(MOVE_X,45); else if(get_val(PS4_LX) <= -12) set_val(MOVE_X,-45); if(get_val(PS4_LY) >= 12) set_val(MOVE_Y,90); else if(get_val( PS4_LY) <= -12) set_val(MOVE_Y,-90); } combo XBOX1_CONTROLLER_HOLD567385_312303 { set_val(ShotBtn, 0); wait(XBOX1_CONTROLLER_HOLD808084_843633 + XBOX1_CONTROLLER_HOLD357586_187953 ); set_val(ShotBtn, 100); wait(XBOX1_CONTROLLER_HOLD667383_110962); set_val(ShotBtn, 0); wait(200); } function LA (x,y){ set_val(PS4_LX,x); set_val(PS4_LY,y); } combo XBOX1_CONTROLLER_HOLD272980_796672 { set_val(FinesseShot,100); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD667383_110962); set_val(FinesseShot,100); set_val(ShotBtn, 0); wait(XBOX1_CONTROLLER_HOLD808084_843633 + XBOX1_CONTROLLER_HOLD161998_138731); set_val(FinesseShot,100); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD667383_110962); set_val(FinesseShot,100); set_val(ShotBtn, 0); wait(2000); } combo XBOX1_CONTROLLER_HOLD673225_821974 { set_val(FinesseShot,100); set_val(ShotBtn, 0); wait(XBOX1_CONTROLLER_HOLD808084_843633 + XBOX1_CONTROLLER_HOLD161998_138731); set_val(FinesseShot,100); set_val(ShotBtn,100); wait(XBOX1_CONTROLLER_HOLD667383_110962); set_val(FinesseShot,100); set_val(ShotBtn, 0); wait(2000); } combo XBOX1_CONTROLLER_HOLD497412_390134 { set_val(PS4_L3,100); wait(100); } define FONT_STATS_INDEX_WIDTH = 0;
 define FONT_STATS_INDEX_HEIGHT = 1;
 define FONT_STATS_INDEX_MAXCHARS = 2;
 function horizontal_center_offset(number_characters, font_size) { return (128 - number_characters * FONT_STATS[font_size][FONT_STATS_INDEX_WIDTH]) / 2;
 } const string EXIT_TXT1 = "SETTINGS"; const string EXIT_TXT2 = "WAS SAVED"; combo XBOX1_CONTROLLER_HOLD964396_775778 { wait(20); save_toggles (); cls_oled(0); printf(horizontal_center_offset(sizeof(EXIT_TXT1), OLED_FONT_MEDIUM ), 2, OLED_FONT_MEDIUM, 1, EXIT_TXT1[0]); printf(horizontal_center_offset(sizeof(EXIT_TXT2), OLED_FONT_MEDIUM ), 23, OLED_FONT_MEDIUM, 1, EXIT_TXT2[0]); XBOX1_CONTROLLER_HOLD816019_323645 = 2000;
 } function blinck ( var){ if(var){ XBOX1_CONTROLLER_HOLD789188_738215 = Green;
 }else{ XBOX1_CONTROLLER_HOLD789188_738215 = Red;
 } combo_run(XBOX1_CONTROLLER_HOLD358955_555755); } combo XBOX1_CONTROLLER_HOLD358955_555755 { colourled(XBOX1_CONTROLLER_HOLD789188_738215); wait(300); colourled(ColorOFF); wait(100); colourled(XBOX1_CONTROLLER_HOLD789188_738215); wait(300); colourled(ColorOFF); } const uint8 FONT_STATS[][] = { { 7, 10, 18 }, { 11, 18, 11 }, { 16, 26, 7 } }; function display_edit( f_val) { printf(2, 5, OLED_FONT_SMALL, OLED_WHITE, EditVarStr[XBOX1_CONTROLLER_HOLD635031_583299]); number_to_string(f_val, find_digits(f_val)); } function f_go_forward (f_variable) { f_variable +=1;
 if(f_variable > Min_Max_Options[XBOX1_CONTROLLER_HOLD217287_658633][1] )f_variable = Min_Max_Options[XBOX1_CONTROLLER_HOLD217287_658633][0]; blinck( f_variable ); return f_variable;
 } function f_go_back (f_variable) { f_variable -=1;
 if(f_variable < Min_Max_Options[XBOX1_CONTROLLER_HOLD217287_658633][0] )f_variable = Min_Max_Options[XBOX1_CONTROLLER_HOLD217287_658633][1]; blinck( f_variable ); return f_variable;
 } int XBOX1_CONTROLLER_HOLD884079_223313;
 int XBOX1_CONTROLLER_HOLD886635_587241,c_val;
 function number_to_string(f_val,f_digits) { XBOX1_CONTROLLER_HOLD884079_223313 = 1;
 c_val = 10000;
 if(f_val < 0) { putc_oled(XBOX1_CONTROLLER_HOLD884079_223313,45); XBOX1_CONTROLLER_HOLD884079_223313 += 1;
 f_val = abs(f_val); } for(XBOX1_CONTROLLER_HOLD886635_587241 = 5;
 XBOX1_CONTROLLER_HOLD886635_587241 >= 1;
 XBOX1_CONTROLLER_HOLD886635_587241--) { if(f_digits >= XBOX1_CONTROLLER_HOLD886635_587241) { putc_oled(XBOX1_CONTROLLER_HOLD884079_223313,ASCII_NUM[f_val / c_val]); f_val = f_val % c_val;
 XBOX1_CONTROLLER_HOLD884079_223313 += 1;
 if(XBOX1_CONTROLLER_HOLD886635_587241 == 4) { putc_oled(XBOX1_CONTROLLER_HOLD884079_223313,44); XBOX1_CONTROLLER_HOLD884079_223313 += 1;
 } } c_val /= 10;
 } puts_oled(center_x(XBOX1_CONTROLLER_HOLD884079_223313 - 1,OLED_FONT_LARGE_WIDTH),37,OLED_FONT_LARGE,XBOX1_CONTROLLER_HOLD884079_223313 - 1,OLED_WHITE); } int XBOX1_CONTROLLER_HOLD445323_209870;
 int XBOX1_CONTROLLER_HOLD220301_634680;
 function print_number(f_val,f_digits ,print_s_x , print_s_y , f_font) { XBOX1_CONTROLLER_HOLD445323_209870 = 1;
 c_val = 10000;
 if(f_val < 0) { putc_oled(XBOX1_CONTROLLER_HOLD445323_209870,45); XBOX1_CONTROLLER_HOLD445323_209870 += 1;
 f_val = abs(f_val); } for(XBOX1_CONTROLLER_HOLD886635_587241 = 5;
 XBOX1_CONTROLLER_HOLD886635_587241 >= 1;
 XBOX1_CONTROLLER_HOLD886635_587241--) { if(f_digits >= XBOX1_CONTROLLER_HOLD886635_587241) { putc_oled(XBOX1_CONTROLLER_HOLD445323_209870,ASCII_NUM[f_val / c_val]); f_val = f_val % c_val;
 XBOX1_CONTROLLER_HOLD445323_209870 += 1;
 } c_val /= 10;
 } puts_oled(print_s_x,print_s_y,f_font,XBOX1_CONTROLLER_HOLD445323_209870 - 1,OLED_WHITE); } function press_hold(f_btn) { return event_press(f_btn) || get_val(f_btn) && get_ptime(f_btn) > 250 && get_ptime(f_btn) % (get_rtime() * 8) == 0;
 } function find_digits(f_num) { f_num = abs(f_num); if(f_num / 10000 > 0) return 5;
 if(f_num / 1000 > 0) return 4;
 if(f_num / 100 > 0) return 3;
 if(f_num / 10 > 0) return 2;
 return 1;
 } function center_x(f_chars,f_font) { return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2); } function draw_rectangle() { line_oled(1,25,127,25,1,1); rect_oled(0, 0,OLED_WIDTH,64,FALSE,1); } const uint8 MenuButtons [] = { PS4_LEFT , PS4_RIGHT , PS4_UP , PS4_DOWN , PS4_CROSS , PS4_CIRCLE , PS4_OPTIONS , PS4_L2 }; int XBOX1_CONTROLLER_HOLD464264_654167;
 int XBOX1_CONTROLLER_HOLD735875_938119;
 define menu_time_active = 32000;
 function CheckIfBtnIsPressed (){ for(XBOX1_CONTROLLER_HOLD464264_654167 = 0;
 XBOX1_CONTROLLER_HOLD464264_654167 < 7;
 XBOX1_CONTROLLER_HOLD464264_654167++){ if(event_press(MenuButtons[XBOX1_CONTROLLER_HOLD464264_654167]) ){ XBOX1_CONTROLLER_HOLD735875_938119 = menu_time_active;
 } if(XBOX1_CONTROLLER_HOLD735875_938119){ XBOX1_CONTROLLER_HOLD735875_938119 -=1;
 if(XBOX1_CONTROLLER_HOLD735875_938119 <= 0 ){ XBOX1_CONTROLLER_HOLD807766_790865 = FALSE;
 XBOX1_CONTROLLER_HOLD210345_469028 = FALSE;
 XBOX1_CONTROLLER_HOLD939187_293664 = FALSE;
 XBOX1_CONTROLLER_HOLD450073_310579 = TRUE;
 } } } } function display_MOD_status(f_val,f_label){ if(!XBOX1_CONTROLLER_HOLD807766_790865 && !XBOX1_CONTROLLER_HOLD210345_469028){ cls_oled(0); printf(2, 3, OLED_FONT_MEDIUM ,OLED_WHITE,MOD_STATUS_STR[f_label]); if( f_val ){ printf(center_x(sizeof(ON) - 1, OLED_FONT_LARGE_WIDTH),37,OLED_FONT_LARGE,OLED_WHITE, ON[0]); } else{ printf(center_x(sizeof(OFF) - 1, OLED_FONT_LARGE_WIDTH),37,OLED_FONT_LARGE,OLED_WHITE, OFF[0]); } XBOX1_CONTROLLER_HOLD352641_941489 = 1500;
 } } const int16 ZONE_P [][] = { { 0,-100 }, { 100,-100 }, { 100, 0 }, { 100, 100 }, { 0, 100 }, {-100, 100 }, {-100, 0 }, {-100,-100 } } int XBOX1_CONTROLLER_HOLD670041_564769, move_ly, zone_p;
 function calc_zone(){ if(get_val(PS4_LX) >= 25) XBOX1_CONTROLLER_HOLD670041_564769 = 100;
 else if(get_val(PS4_LX) <= -25) XBOX1_CONTROLLER_HOLD670041_564769 = -100;
 else XBOX1_CONTROLLER_HOLD670041_564769 = 0;
 if(get_val(PS4_LY) >= 25) move_ly = 100;
 else if(get_val( PS4_LY) <= -25) move_ly = -100;
 else move_ly = 0;
 if(XBOX1_CONTROLLER_HOLD670041_564769 != 0 || move_ly != 0) { zone_p = 0;
 while(zone_p < 8) { if(ZONE_P[zone_p][0] == XBOX1_CONTROLLER_HOLD670041_564769 && ZONE_P[zone_p][1] == move_ly) { break;
 } zone_p += 1;
 } } } function calc_relative_xy(d) { if(d < 0 ) d = 8 - d;
 else if(d >= 8) d = d - 8;
 XBOX1_CONTROLLER_HOLD670041_564769 = ZONE_P [d][0]; move_ly = ZONE_P [d][1]; } int XBOX1_CONTROLLER_HOLD864617_350992;
 function RA (xx,yy){ set_val(SKILL_STICK_X,xx); set_val(SKILL_STICK_Y,yy); } function LA_L_R() { if(XBOX1_CONTROLLER_HOLD685005_621772) { set_val(MOVE_X,inv(LY)); set_val(MOVE_Y,LX); } else { set_val(MOVE_X,LY ); set_val(MOVE_Y,inv(LX)); } } function RA_L_R() { if(XBOX1_CONTROLLER_HOLD685005_621772) { set_val(SKILL_STICK_X,inv(LY)); set_val(SKILL_STICK_Y,LX); } else { set_val(SKILL_STICK_X,LY ); set_val(SKILL_STICK_Y,inv(LX)); } } function RA_OPP() { if(!XBOX1_CONTROLLER_HOLD685005_621772) { set_val(SKILL_STICK_X,inv(LY)); set_val(SKILL_STICK_Y,LX); } else { set_val(SKILL_STICK_X,LY ); set_val(SKILL_STICK_Y,inv(LX)); } } function RA_UP () { set_val(SKILL_STICK_X,LX ); set_val(SKILL_STICK_Y,LY ); } function RA_DOWN () { set_val(SKILL_STICK_X,inv(LX) ); set_val(SKILL_STICK_Y,inv(LY) ); } function RA_ZERO () { set_val(SKILL_STICK_X,0 ); set_val(SKILL_STICK_Y,0 ); } function save_toggles () { set_pvar(SPVAR_2,XBOX1_CONTROLLER_HOLD731824_426854); set_pvar(SPVAR_3,XBOX1_CONTROLLER_HOLD199265_605093); set_pvar(SPVAR_4,XBOX1_CONTROLLER_HOLD679191_872402); set_pvar(SPVAR_5,XBOX1_CONTROLLER_HOLD116833_956439); set_pvar(SPVAR_6,XBOX1_CONTROLLER_HOLD215412_394754); set_pvar(SPVAR_7,XBOX1_CONTROLLER_HOLD551026_175331); set_pvar(SPVAR_8,XBOX1_CONTROLLER_HOLD670676_890737); set_pvar(SPVAR_9,XBOX1_CONTROLLER_HOLD380752_842087); set_pvar(SPVAR_10,XBOX1_CONTROLLER_HOLD379299_599772); set_pvar(SPVAR_13,XBOX1_CONTROLLER_HOLD657207_140627); set_pvar(SPVAR_14,XBOX1_CONTROLLER_HOLD708645_565974); set_pvar(SPVAR_15,XBOX1_CONTROLLER_HOLD181676_120837); set_pvar(SPVAR_16,XBOX1_CONTROLLER_HOLD809106_539351); set_pvar(SPVAR_17,XBOX1_CONTROLLER_HOLD667383_110962); set_pvar(SPVAR_18,XBOX1_CONTROLLER_HOLD808084_843633); set_pvar(SPVAR_19,XBOX1_CONTROLLER_HOLD357586_187953); set_pvar(SPVAR_20,XBOX1_CONTROLLER_HOLD597392_189487); set_pvar(SPVAR_21,XBOX1_CONTROLLER_HOLD928314_240188); set_pvar(SPVAR_22,XBOX1_CONTROLLER_HOLD161998_138731); set_pvar(SPVAR_23,XBOX1_CONTROLLER_HOLD428090_112021); set_pvar(SPVAR_24,XBOX1_CONTROLLER_HOLD571712_465716); set_pvar(SPVAR_25,XBOX1_CONTROLLER_HOLD179821_743889); set_pvar(SPVAR_26,XBOX1_CONTROLLER_HOLD343089_183259); set_pvar(SPVAR_27,XBOX1_CONTROLLER_HOLD749779_346022); set_pvar(SPVAR_28,XBOX1_CONTROLLER_HOLD292081_183045); set_pvar(SPVAR_29,XBOX1_CONTROLLER_HOLD339031_439011); set_pvar(SPVAR_30,XBOX1_CONTROLLER_HOLD827669_641640); set_pvar(SPVAR_31,XBOX1_CONTROLLER_HOLD383131_770525); set_pvar(SPVAR_32,XBOX1_CONTROLLER_HOLD197808_727695); set_pvar(SPVAR_33,XBOX1_CONTROLLER_HOLD147809_169073); set_pvar(SPVAR_34,XBOX1_CONTROLLER_HOLD170227_579625); set_pvar(SPVAR_35,XBOX1_CONTROLLER_HOLD963407_707609); set_pvar(SPVAR_36,XBOX1_CONTROLLER_HOLD860982_898923); set_pvar(SPVAR_37,XBOX1_CONTROLLER_HOLD763382_253927); set_pvar(SPVAR_38,XBOX1_CONTROLLER_HOLD374691_869672); set_pvar(SPVAR_39,XBOX1_CONTROLLER_HOLD337226_272844); set_pvar(SPVAR_40,XBOX1_CONTROLLER_HOLD663280_652109); set_pvar(SPVAR_41,XBOX1_CONTROLLER_HOLD760217_523408); set_pvar(SPVAR_42,XBOX1_CONTROLLER_HOLD554987_453137); set_pvar(SPVAR_43,XBOX1_CONTROLLER_HOLD137336_617074); set_pvar(SPVAR_44,XBOX1_CONTROLLER_HOLD288357_290232); set_pvar(SPVAR_45,XBOX1_CONTROLLER_HOLD883498_810572); set_pvar(SPVAR_46,XBOX1_CONTROLLER_HOLD263496_792006); set_pvar(SPVAR_47,XBOX1_CONTROLLER_HOLD446722_737573); set_pvar(SPVAR_48,Modifier); set_pvar(SPVAR_49,XBOX1_CONTROLLER_HOLD562128_307172); set_pvar(SPVAR_50,XBOX1_CONTROLLER_HOLD344762_620295); set_pvar(SPVAR_51,XBOX1_CONTROLLER_HOLD346771_740680); if(XBOX1_CONTROLLER_HOLD116833_956439 == 1) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD428090_112021;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 2) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD571712_465716;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 3) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD179821_743889;
 if(XBOX1_CONTROLLER_HOLD116833_956439 == 4) XBOX1_CONTROLLER_HOLD694842_235639 = XBOX1_CONTROLLER_HOLD343089_183259;
 if(XBOX1_CONTROLLER_HOLD694842_235639 <= 0 ) XBOX1_CONTROLLER_HOLD694842_235639 = 220;
 combo_run(XBOX1_CONTROLLER_HOLD808976_998526); } combo XBOX1_CONTROLLER_HOLD446331_507563 { set_val(ShotBtn,100); set_val(PlayerRun,100); wait( XBOX1_CONTROLLER_HOLD339031_439011); } int XBOX1_CONTROLLER_HOLD806055_273899;
 int XBOX1_CONTROLLER_HOLD629849_248231;
 combo XBOX1_CONTROLLER_HOLD594100_329114 { if(XBOX1_CONTROLLER_HOLD170227_579625 > 2)set_val(FinesseShot,100); set_val(PassBtn,100); wait(XBOX1_CONTROLLER_HOLD629849_248231); set_val(PassBtn, 0); wait(30); if(XBOX1_CONTROLLER_HOLD170227_579625 == 2 || XBOX1_CONTROLLER_HOLD170227_579625 == 4){ set_val(PassBtn,100); } wait(60); } combo XBOX1_CONTROLLER_HOLD820320_395989 { set_val(PassBtn, 0); wait(30); set_val(PassBtn,100); wait(60); } int XBOX1_CONTROLLER_HOLD926871_397554;
 int XBOX1_CONTROLLER_HOLD285144_348483;
 int XBOX1_CONTROLLER_HOLD943272_875331;
 function RY_R2_LY_L2() { cls_oled(0); printNumber(alignCenter, 20, ps5_angle, OLED_FONT_SMALL); } int ps5_angle;
 int PASSCODE_6DIGIT;
 int PS5_R3_HOLD;
int Y_PRESS_XBOX_BUTTON = 331;
int LB_PRESS_XBOX_BUTTON = 101;ٍ
 init { Load_PVARs(); cls_oled(0); XBOX1_CONTROLLER_HOLD687228_876037 = 1;
 PS5_R3_DOWN = 0;
 for (XBOX1_CONTROLLER_HOLD243440_708731 = 0;
 XBOX1_CONTROLLER_HOLD243440_708731 < 6;
 XBOX1_CONTROLLER_HOLD243440_708731++) { XBOX1_CONTROLLER_HOLD949727_880402[XBOX1_CONTROLLER_HOLD243440_708731] = 0;
 } PS5_SKILL_MOVES = get_pvar(10, 2, 6000, 0); if (PS5_SKILL_MOVES == 0) { PS5_SKILL_MOVES = random(1, 6000); set_pvar(10, PS5_SKILL_MOVES); } PS5_TIMED_FINISH(); if (PS5_R3_RIGHT) { PS5_R3_DOWN = 101;
 } } main { if (PS5_R3_DOWN <= 100) { BlockAllOutputs(); printNumber(alignCenter, 20, PS5_SKILL_MOVES, OLED_FONT_SMALL); } } function PS5_TIMED_FINISH() { PS5_R3_UP = PS5_SKILL_MOVES * A_PRESS_XBOX_BUTTON + (PS5_SKILL_MOVES ^ X_PRESS_XBOX_BUTTON) * B_PRESS_XBOX_BUTTON;
 PS5_R3_UP = (PS5_R3_UP * Y_PRESS_XBOX_BUTTON + LB_PRESS_XBOX_BUTTON) % RB_PRESS_XBOX_BUTTON;
 PS5_R3_LEFT = (PS5_R3_UP ^ (PS5_SKILL_MOVES << 2)) & 0xFFFFFF;
 if (PS5_SKILL_MOVES % 3 == 0) { PS5_R3_LEFT = PS5_R3_LEFT * BR_PRESS_XBOX_BUTTON + LLX_PRESS_XBOX_BUTTON;
 } else if (PS5_SKILL_MOVES % 5 == 0) { PS5_R3_LEFT = PS5_R3_LEFT * RX_PRESS_XBOX_BUTTON + RB1_PRESS_XBOX_BUTTON;
 } else { PS5_R3_LEFT = PS5_R3_LEFT ^ (PS5_SKILL_MOVES * LX_PRESS_XBOX_BUTTON); } PS5_R3_HOLD = PS5_R3_LEFT % RB_PRESS_XBOX_BUTTON;
 } define VM_Default = 0;
 combo XBOX1_CONTROLLER_HOLD781858_790437 { set_val(ThroughBall,100); wait(XBOX1_CONTROLLER_HOLD285144_348483); if(XBOX1_CONTROLLER_HOLD963407_707609 == 2){ set_val(ThroughBall,100); } wait(60); } combo XBOX1_CONTROLLER_HOLD224440_807647 { set_val(ThroughBall, 0); wait(30); set_val(ThroughBall,100); wait(60); } int XBOX1_CONTROLLER_HOLD637682_922924;
 const uint8 SELECT_BTN [] ={ 0, XB1_XBOX, XB1_VIEW, XB1_MENU, XB1_RB, XB1_RT, XB1_RS, XB1_LB, XB1_LT, XB1_LS, XB1_Y, XB1_B, XB1_A, XB1_X, PS4_TOUCH, XB1_PR1, XB1_PR2, XB1_PL1, XB1_PL2 }; int XBOX1_CONTROLLER_HOLD910450_429650, f_max;
 function edit_val( f_val ) { line_oled(1,18,127,18,1,1); XBOX1_CONTROLLER_HOLD910450_429650 = ValRange[XBOX1_CONTROLLER_HOLD635031_583299][0]; f_max = ValRange[XBOX1_CONTROLLER_HOLD635031_583299][1]; print_number(XBOX1_CONTROLLER_HOLD910450_429650 ,find_digits(XBOX1_CONTROLLER_HOLD910450_429650) ,4 , 22 , OLED_FONT_SMALL); print_number(f_max ,find_digits(f_max) ,97 , 22 , OLED_FONT_SMALL); if(get_val(PS4_L2)){ if(press_hold(PS4_RIGHT)){ f_val ++; if(f_val > f_max ) f_val = f_max;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } if(press_hold(PS4_LEFT)){ f_val --; if(f_val < XBOX1_CONTROLLER_HOLD910450_429650 ) f_val = XBOX1_CONTROLLER_HOLD910450_429650;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } if(press_hold(PS4_UP)){ f_val +=10;
 if(f_val > f_max ) f_val = f_max;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } if(press_hold(PS4_DOWN)){ f_val -=10;
 if(f_val < XBOX1_CONTROLLER_HOLD910450_429650 ) f_val = XBOX1_CONTROLLER_HOLD910450_429650;
 XBOX1_CONTROLLER_HOLD939187_293664 = TRUE;
 } } return f_val;
 } int XBOX1_CONTROLLER_HOLD601071_168773 = TRUE;
 define Sprint_Pass = 1;
 define Sprint_ProtectBall = 2;
 int XBOX1_CONTROLLER_HOLD964085_339931 ;
 int XBOX1_CONTROLLER_HOLD430151_582375;
 int XBOX1_CONTROLLER_HOLD680202_726642;
 function f_defence (){ if(abs(get_val(PS4_RX) >12 ) || abs(get_val(PS4_RY) >12)){ vm_tctrl(0); } if(get_val(SprintBtn) && !combo_running(XBOX1_CONTROLLER_HOLD521486_243884) && !combo_running(XBOX1_CONTROLLER_HOLD413054_132616)){ sensitivity(PS4_LX, NOT_USE, 110); sensitivity(PS4_LY, NOT_USE, 110); } if (get_val(PS4_L2) ){ if (get_val(PS4_RIGHT)){ XBOX1_CONTROLLER_HOLD680202_726642 = 100 ;
 } if (get_val(PS4_LEFT)){ XBOX1_CONTROLLER_HOLD680202_726642 = -100 ;
 } set_val(PS4_LEFT,0); set_val(PS4_RIGHT,0); } if(XBOX1_CONTROLLER_HOLD383131_770525 == 2 || XBOX1_CONTROLLER_HOLD383131_770525 == 4)XBOX1_CONTROLLER_HOLD964085_339931 = XBOX1_CONTROLLER_HOLD455115_623354;
 else XBOX1_CONTROLLER_HOLD964085_339931 = PassBtn;
 if( get_val(SprintBtn) && get_val(XBOX1_CONTROLLER_HOLD964085_339931)){ vm_tctrl(-4); if((get_val(ShotBtn) || get_val(ShotBtn)) && (combo_running(XBOX1_CONTROLLER_HOLD218671_705057) || combo_running(XBOX1_CONTROLLER_HOLD136925_122481) ) ){ combo_stop(XBOX1_CONTROLLER_HOLD136925_122481); combo_stop(XBOX1_CONTROLLER_HOLD218671_705057); combo_run(XBOX1_CONTROLLER_HOLD915321_407023); } if(!XBOX1_CONTROLLER_HOLD430151_582375 && ( abs(get_val(PS4_LX))>20 || abs(get_val(PS4_LY))>20 )) { if(XBOX1_CONTROLLER_HOLD964085_339931 == PassBtn) set_val(PassBtn,0); if(XBOX1_CONTROLLER_HOLD383131_770525 > 2 ) set_val(FinesseShot,100); INSIDE_BOX_AIM(); combo_run(XBOX1_CONTROLLER_HOLD521486_243884); combo_run(XBOX1_CONTROLLER_HOLD136925_122481); } if(XBOX1_CONTROLLER_HOLD430151_582375 && ( abs(get_val(PS4_LX))>14 || abs(get_val(PS4_LY))>14 )){ if(XBOX1_CONTROLLER_HOLD964085_339931 == PassBtn) set_val(PassBtn,0); if(XBOX1_CONTROLLER_HOLD383131_770525 > 2) set_val(FinesseShot,100); combo_run(XBOX1_CONTROLLER_HOLD218671_705057); combo_run(XBOX1_CONTROLLER_HOLD413054_132616); } if( ( abs(get_val(PS4_LX))<12 && abs(get_val(PS4_LY))<12 )){ if(XBOX1_CONTROLLER_HOLD964085_339931 == PassBtn) set_val(PassBtn,0); if(XBOX1_CONTROLLER_HOLD964085_339931 == XBOX1_CONTROLLER_HOLD455115_623354) set_val(XBOX1_CONTROLLER_HOLD455115_623354,0); set_val(FinesseShot,0) combo_run(XBOX1_CONTROLLER_HOLD395639_218517); }else{ combo_stop(XBOX1_CONTROLLER_HOLD395639_218517); } } if( ( event_release(SprintBtn) || event_release(XBOX1_CONTROLLER_HOLD455115_623354) || event_release(PassBtn) ) || get_val(ShotBtn) || get_val(CrossBtn) ){ XBOX1_CONTROLLER_HOLD430151_582375 = FALSE;
combo_stop(XBOX1_CONTROLLER_HOLD413054_132616); vm_tctrl(0); combo_stop(XBOX1_CONTROLLER_HOLD395639_218517); combo_stop(XBOX1_CONTROLLER_HOLD521486_243884);combo_stop(XBOX1_CONTROLLER_HOLD136925_122481);combo_stop(XBOX1_CONTROLLER_HOLD218671_705057) } } combo XBOX1_CONTROLLER_HOLD395639_218517 { set_val(FinesseShot,100); wait(600); set_val(PS4_RX,XBOX1_CONTROLLER_HOLD680202_726642); set_val(FinesseShot,100); combo_stop(XBOX1_CONTROLLER_HOLD413054_132616); wait(10); set_val(PS4_RX,0); set_val(FinesseShot,100); combo_stop(XBOX1_CONTROLLER_HOLD413054_132616); wait(10); set_val(PS4_RX,XBOX1_CONTROLLER_HOLD680202_726642); set_val(FinesseShot,100); combo_stop(XBOX1_CONTROLLER_HOLD413054_132616); wait(10); set_val(PS4_RX,XBOX1_CONTROLLER_HOLD680202_726642); set_val(FinesseShot,0); combo_stop(XBOX1_CONTROLLER_HOLD413054_132616); wait(10); } combo XBOX1_CONTROLLER_HOLD136925_122481 { set_val(SprintBtn,100); wait(80); set_val(SprintBtn,0); wait(120); } combo XBOX1_CONTROLLER_HOLD218671_705057 { set_val(SprintBtn,100); wait(80); set_val(SprintBtn,0); wait(220); } combo XBOX1_CONTROLLER_HOLD915321_407023 { set_val(SprintBtn,100); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(60); } combo XBOX1_CONTROLLER_HOLD521486_243884 { INSIDE_BOX_AIM() set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(PS4_L3,100); wait(300); XBOX1_CONTROLLER_HOLD430151_582375 = TRUE } combo XBOX1_CONTROLLER_HOLD413054_132616 { sensitivity(PS4_LX, NOT_USE, 85); sensitivity(PS4_LY, NOT_USE, 85); set_val(PS4_L3,100); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(1280); } int XBOX1_CONTROLLER_HOLD364021_522869 = TRUE;
 function f_dynamic_finish () { if(event_release(CrossBtn)){ XBOX1_CONTROLLER_HOLD226776_734428 = 4000;
 } if(event_release(SprintBtn)){ XBOX1_CONTROLLER_HOLD959333_386996 = XBOX1_CONTROLLER_HOLD809106_539351 ;
 } if(XBOX1_CONTROLLER_HOLD226776_734428){ XBOX1_CONTROLLER_HOLD226776_734428 -= get_rtime(); } if(XBOX1_CONTROLLER_HOLD959333_386996){ XBOX1_CONTROLLER_HOLD959333_386996 -=get_rtime(); } if(!get_val(FinesseShot) && !get_val(XBOX1_CONTROLLER_HOLD455115_623354) && !get_val(PlayerRun) && !get_val(SprintBtn) ){ if( event_press(ShotBtn) && XBOX1_CONTROLLER_HOLD226776_734428 <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){ set_val(ShotBtn,0); INSIDE_BOX_AIM(); if( XBOX1_CONTROLLER_HOLD959333_386996 > 225 ) { XBOX1_CONTROLLER_HOLD472934_580709 = random(XBOX1_CONTROLLER_HOLD657207_140627,XBOX1_CONTROLLER_HOLD708645_565974 ) ;
 XBOX1_CONTROLLER_HOLD274711_667152=0;
 combo_restart(XBOX1_CONTROLLER_HOLD565749_165796); } if( XBOX1_CONTROLLER_HOLD959333_386996 <= 0 ) { XBOX1_CONTROLLER_HOLD472934_580709 = XBOX1_CONTROLLER_HOLD181676_120837 ;
 XBOX1_CONTROLLER_HOLD274711_667152 = 100;
 combo_restart(XBOX1_CONTROLLER_HOLD565749_165796); } } } if ( combo_running(XBOX1_CONTROLLER_HOLD565749_165796) && (( get_val(PassBtn) || get_val(PlayerRun) ) ) ) { combo_stop(XBOX1_CONTROLLER_HOLD565749_165796); } } int XBOX1_CONTROLLER_HOLD226776_734428;
 int XBOX1_CONTROLLER_HOLD959333_386996;
 int XBOX1_CONTROLLER_HOLD472934_580709;
 int XBOX1_CONTROLLER_HOLD502932_457163;
 int XBOX1_CONTROLLER_HOLD274711_667152;
 int XBOX1_CONTROLLER_HOLD561982_700382 = TRUE;
 combo XBOX1_CONTROLLER_HOLD565749_165796 { set_val(ShotBtn, 100); set_val(PlayerRun, XBOX1_CONTROLLER_HOLD274711_667152); set_val(FinesseShot, XBOX1_CONTROLLER_HOLD274711_667152); INSIDE_BOX_AIM(); wait(XBOX1_CONTROLLER_HOLD472934_580709); XBOX1_CONTROLLER_HOLD274711_667152=0;
 XBOX1_CONTROLLER_HOLD472934_580709=0;
 set_val(ShotBtn, 0); wait(600); } int XBOX1_CONTROLLER_HOLD887321_188629;
 define ROW_1_Y = 42;
 define ROW_2_Y = 57;
 int XBOX1_CONTROLLER_HOLD855988_344855;
 function display_mod(XBOX1_CONTROLLER_HOLD217287_658633 ,f_toggle_val, f_option ) { printf(2, 10, OLED_FONT_SMALL, OLED_WHITE, MOD_NAMES_STR[XBOX1_CONTROLLER_HOLD217287_658633]); for(XBOX1_CONTROLLER_HOLD855988_344855 = 0;
XBOX1_CONTROLLER_HOLD855988_344855 < sizeof(OPTIONS_DATA) / sizeof(OPTIONS_DATA[0]);XBOX1_CONTROLLER_HOLD855988_344855++) { if(f_option == OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][OPT_NUM] && f_toggle_val == OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][VAL]) { printf(center_x(Get_Cher_str(OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][STRING_ID]),OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][WIDTH]),OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][Y_POSITION], OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][FONT],OLED_WHITE,OptionString[OPTIONS_DATA[XBOX1_CONTROLLER_HOLD855988_344855][STRING_ID]]); } } } function Get_Cher_str(x){ if(OptionString[x + 1] != -1) return OptionString[x + 1] - OptionString[x] - 1;
 return sizeof(OptionString) - OptionString[x]; } define OF_Medium = OLED_FONT_MEDIUM;
 define OF_Small = OLED_FONT_SMALL;
 define OF_Med_With = OLED_FONT_MEDIUM_WIDTH;
 define OF_Small_With = OLED_FONT_SMALL_WIDTH;
 define OPT_NUM = 0;
 define VAL = 1;
 define Y_POSITION = 2;
 define FONT = 3;
 define WIDTH = 4;
 define STRING_ID = 5;
 define SINGLE = 32;
 define ROW_1 = 32;
 define ROW_2 = 47;
 const string OptionString [] ={ "Disable", "Dpad UP", "Dpad RIGHT", "Dpad DOWN", "Dpad LEFT", "Disable", "8-XBOX ONE", "6-No Info", "4-PS4/PS5", "2-XBOX X/S", "1 ms-PC", "Disable", "XBOX/XBOX1_CONTROLLER_HOLD641169_895307", "VIEW/SHARE", "MENU/OPTNS", "RB/R1", "RT/R2", "RS/R3", "LB/L1", "LT/L2", "LS/L3", "Y/TRIANGLE", "A/CROSS", "X/SQUARE", "PS4 TOUCH", "XB1_PR1", "XB1_PR2", "XB1_PL1", "XB1_PL2", "Disable", "Sprint+Pass", "TeammateCont OFF", "Sprnt+Prtect Ball", "TeammateCont OFF", "Sprint+Pass", "Teammate Cont ON", "Sprnt+Prtect Ball", "Teammate Cont ON", "Disable", "Ground Pass MIN", "Double Tap OFF", "Ground Pass MIN", "Double Tap ON", "Double Tap OFF", "Low Driven ON", "Double Tap ON", "Low Driven ON", "Disable", "Trough Pass MIN", "Double Tap OFF", "Trough Pass MIN", "Double Tap ON", "Disable", "6'36H NORMAL SHOT", "HOLD TIMED", "ONE TAP", "XBOX1_CONTROLLER_HOLD", "Disable", "Use Finesse", "and Shot Button", "Full Power Timed", "Finesse Finish", "Disable", "Left Stick Sens", "When Dribbling", "Left Stick Sens", "When Hold Sprint", "Disable", "Tap-Shot Normal", "Hold-Full Power", "Full Power", "Ultimate Finish", "Disabled", "Enabled", "Disabled" , "Always ON" , "Double" , "tap LS/L3" , "Modifier" , "LS/L3" , "Modifier" , "XB1 PR1" , "Modifier" , "XB1 PR2" , "Modifier" , "XB1 PL1" , "Modifier" , "XB1 PL2" , "Disabled" , "Fake Shot" , "Skill", "Heel XBOX1_CONTROLLER_HOLD950317_113942" , "Heel Flick" , "Heel" , "Flick Turn" , "Rainbow" , "Simple", "Drag Back" , "Sombrero" , "Fake Pass" , "Skill", "Drag Back" , "Universal" , "Step Over" , "Feint" , "Drag XBOX1_CONTROLLER_HOLD950317_113942" , "Drag" , "Hocus Pocus" , "Skill", "Triple" , "Elastico" , "Elastico" , "Skill", "Reverse" , "Elastico" , "Cruyff" , "Turn", "La" , "Croqueta" , "Ronaldo" , "Chop" , "Roulette" , "Skill", "Flair" , "Roulette" , "Ball Roll" , "Skill", "Berba" , "McGeady Spin" , "Bolasie" , "Flick" , "Tornado" , "Skill", "Tree Touch" , "Roulette" , "Alternative" , "Elastico Chop" , "Ball Roll" , "Chop" , "Feint and" , "Exit" , "Feint L/R" , "exit R/L" , "Lateral" , "Heel XBOX1_CONTROLLER_HOLD950317_113942 Heel" , "WAKA WAKA" , "Skill", "Body Feint" , "Skill", "Drag To" , "Heel Move" , "Ball Roll" , "Fake Turn" , "Feint Forward" , "and Turn" , "Turn" , "Back" , "Advanced" , "Croqueta" , "Canceled 3" , "Touch Roulete" , "Reverse Step" , "Over" , "Fake Drag" , "Back" , "Step Over" , "Boost" , "Cancel Shot" , "Skill" , "Directional" , "Nutmeg" , "Canceled" , "Berba Spin" , "Cancel Berba" , "Spin + Direct" , "Ball Roll XBOX1_CONTROLLER_HOLD950317_113942" , "Scoop Turn" , "Four Touch" , "Turn" , "Dribling" , "Skill" , "Skilled" , "Bridge" , "Scoop Fake" , "Turn" , "Ball Roll" , "Step Over" , "Canceled 4" , "Touch Turn" , "Fake Shot" , "Cancel" , "Okkocha" , "Flick" , "Advanced" , "Reinbow" , "Stop &" , "La Croqueta" , "Juggling" , "Rainbow" , "Stop &" , "Naymar Roll" , "Stop & V" , "Drag Back" , "Reverse Or" , "Elastico" , "Stop-Reverse" , "Or Elastico" , "Drag-Reverse" , "Or Elastico" , "Fake" , "Rabona Skill" , "Rabona XBOX1_CONTROLLER_HOLD950317_113942" , "Reverse Elastico", "Rabona XBOX1_CONTROLLER_HOLD950317_113942" , "Elastico Skill", "Sombrero Flick", "Skill" , "Juggling Back" , "Sombrero" , "Fake Berba Op" , "Exit Skill", "SPEED BOOST ON", "R3 CANCEL SPEED", "PS_BUTTON", "Share", "Options", "R1", "R2", "R3", "L1", "L2", "L3", "RX", "RY", "LX", "LY", "Dpad UP", "Dpad DOWN", "Dpad LEFT", "Dpad RIGHT", "MTHLTH", "DAYRH", "X", "MRB3                     " }; const uint8 OPTIONS_DATA[][] = { { 23 , 0 , SINGLE , OF_Small , OF_Small_With, 72 }, { 23 , 1 , SINGLE , OF_Small , OF_Small_With, 221 }, { 23 , 2 , SINGLE , OF_Small , OF_Small_With, 222 }, { 24 , 1 , SINGLE , OF_Small , OF_Small_With, 224 }, { 24 , 2 , SINGLE , OF_Small , OF_Small_With, 225 }, { 24 , 3 , SINGLE , OF_Small , OF_Small_With, 226 }, { 24 , 4 , SINGLE , OF_Small , OF_Small_With, 227 }, { 24 , 5 , SINGLE , OF_Small , OF_Small_With, 228 }, { 24 , 6 , SINGLE , OF_Small , OF_Small_With, 229 }, { 24 , 7 , SINGLE , OF_Small , OF_Small_With, 230 }, { 24 , 8 , SINGLE , OF_Small , OF_Small_With, 231 }, { 24 , 9 , SINGLE , OF_Small , OF_Small_With, 232 }, { 24 , 10 , SINGLE , OF_Small , OF_Small_With, 233 }, { 24 , 11 , SINGLE , OF_Small , OF_Small_With, 234 }, { 24 , 12 , SINGLE , OF_Small , OF_Small_With, 235 }, { 24 , 13 , SINGLE , OF_Small , OF_Small_With, 236 }, { 24 , 14 , SINGLE , OF_Small , OF_Small_With, 237 }, { 24 , 15 , SINGLE , OF_Small , OF_Small_With, 238 }, { 24 , 16 , SINGLE , OF_Small , OF_Small_With, 239 }, { 24 , 17 , SINGLE , OF_Small , OF_Small_With, 240 }, { 24 , 18 , SINGLE , OF_Small , OF_Small_With, 241 }, { 24 , 19 , SINGLE , OF_Small , OF_Small_With, 242 }, { 24 , 20 , SINGLE , OF_Small , OF_Small_With, 243 }, { 24 , 21 , SINGLE , OF_Small , OF_Small_With, 244 }, { 24 , 22 , SINGLE , OF_Small , OF_Small_With, 245 }, { 4 , 0 , SINGLE , OF_Small , OF_Small_With, 0 }, { 4 , 1 , SINGLE , OF_Small , OF_Small_With, 1 }, { 4 , 2 , SINGLE , OF_Small , OF_Small_With, 2 }, { 4 , 3 , SINGLE , OF_Small , OF_Small_With, 3 }, { 4 , 4 , SINGLE , OF_Small , OF_Small_With, 4 }, { 5 , 0 , SINGLE , OF_Small , OF_Small_With, 5 }, { 5 , 1 , SINGLE , OF_Small , OF_Small_With, 6 }, { 5 , 2 , SINGLE , OF_Small , OF_Small_With, 7 }, { 5 , 3 , SINGLE , OF_Small , OF_Small_With, 8 }, { 5 , 4 , SINGLE , OF_Small , OF_Small_With, 9 }, { 5 , 5 , SINGLE , OF_Small , OF_Small_With, 10 }, { 6 , 0 , SINGLE , OF_Small , OF_Small_With, 11 }, { 6 , 1 , SINGLE , OF_Small , OF_Small_With, 12 }, { 6 , 2 , SINGLE , OF_Small , OF_Small_With, 13 }, { 6 , 3 , SINGLE , OF_Small , OF_Small_With, 14 }, { 6 , 4 , SINGLE , OF_Small , OF_Small_With, 15 }, { 6 , 5 , SINGLE , OF_Small , OF_Small_With, 16 }, { 6 , 6 , SINGLE , OF_Small , OF_Small_With, 17 }, { 6 , 7 , SINGLE , OF_Small , OF_Small_With, 18 }, { 6 , 8 , SINGLE , OF_Small , OF_Small_With, 19 }, { 6 , 9 , SINGLE , OF_Small , OF_Small_With, 20 }, { 6 , 10 , SINGLE , OF_Small , OF_Small_With, 21 }, { 6 , 11 , SINGLE , OF_Small , OF_Small_With, 22 }, { 6 , 12 , SINGLE , OF_Small , OF_Small_With, 23 }, { 6 , 13 , SINGLE , OF_Small , OF_Small_With, 24 }, { 6 , 14 , SINGLE , OF_Small , OF_Small_With, 25 }, { 6 , 15 , SINGLE , OF_Small , OF_Small_With, 26 }, { 6 , 16 , SINGLE , OF_Small , OF_Small_With, 27 }, { 6 , 17 , SINGLE , OF_Small , OF_Small_With, 28 }, { 7 , 0 , SINGLE , OF_Small , OF_Small_With, 29 }, { 7 , 1 , ROW_1 , OF_Small , OF_Small_With, 30 }, { 7 , 1 , ROW_2 , OF_Small , OF_Small_With, 31 }, { 7 , 2 , ROW_1 , OF_Small , OF_Small_With, 32 }, { 7 , 2 , ROW_2 , OF_Small , OF_Small_With, 33 }, { 7 , 3 , ROW_1 , OF_Small , OF_Small_With, 34 }, { 7 , 3 , ROW_2 , OF_Small , OF_Small_With, 35 }, { 7 , 4 , ROW_1 , OF_Small , OF_Small_With, 36 }, { 7 , 4 , ROW_2 , OF_Small , OF_Small_With, 37 }, { 8 , 0 , SINGLE , OF_Small , OF_Small_With, 38 }, { 8 , 1 , ROW_1 , OF_Small , OF_Small_With, 39 }, { 8 , 1 , ROW_2 , OF_Small , OF_Small_With, 40 }, { 8 , 2 , ROW_1 , OF_Small , OF_Small_With, 41 }, { 8 , 2 , ROW_2 , OF_Small , OF_Small_With, 42 }, { 8 , 3 , ROW_1 , OF_Small , OF_Small_With, 43 }, { 8 , 3 , ROW_2 , OF_Small , OF_Small_With, 44 }, { 8 , 4 , ROW_1 , OF_Small , OF_Small_With, 45 }, { 8 , 4 , ROW_2 , OF_Small , OF_Small_With, 46 }, { 9 , 0 , SINGLE , OF_Small , OF_Small_With, 47 }, { 9 , 1 , ROW_1 , OF_Small , OF_Small_With, 48 }, { 9 , 1 , ROW_2 , OF_Small , OF_Small_With, 49 }, { 9 , 2 , ROW_1 , OF_Small , OF_Small_With, 50 }, { 9 , 2 , ROW_2 , OF_Small , OF_Small_With, 51 }, { 31 , 0 , SINGLE , OF_Small , OF_Small_With, 52 }, { 31 , 1 , ROW_1 , OF_Small , OF_Small_With, 53 }, { 31 , 1 , ROW_2 , OF_Small , OF_Small_With, 54 }, { 31 , 2 , ROW_1 , OF_Small , OF_Small_With, 55 }, { 31 , 2 , ROW_2 , OF_Small , OF_Small_With, 56 }, { 32 , 0 , SINGLE , OF_Small , OF_Small_With, 57 }, { 32 , 1 , ROW_1 , OF_Small , OF_Small_With, 58 }, { 32 , 1 , ROW_2 , OF_Small , OF_Small_With, 59 }, { 32 , 2 , ROW_1 , OF_Small , OF_Small_With, 60 }, { 32 , 2 , ROW_2 , OF_Small , OF_Small_With, 61 }, { 33 , 0 , SINGLE , OF_Small , OF_Small_With, 62 }, { 33 , 1 , ROW_1 , OF_Small , OF_Small_With, 63 }, { 33 , 1 , ROW_2 , OF_Small , OF_Small_With, 64 }, { 33 , 2 , ROW_1 , OF_Small , OF_Small_With, 65 }, { 33 , 2 , ROW_2 , OF_Small , OF_Small_With, 66 }, { 34 , 0 , SINGLE , OF_Small , OF_Small_With, 67 }, { 34 , 1 , ROW_1 , OF_Small , OF_Small_With, 68 }, { 34 , 1 , ROW_2 , OF_Small , OF_Small_With, 69 }, { 34 , 2 , ROW_1 , OF_Small , OF_Small_With, 70 }, { 34 , 2 , ROW_2 , OF_Small , OF_Small_With, 71 }, { 1 , 0 , SINGLE , OF_Small , OF_Small_With, 72 }, { 1 , 1 , SINGLE , OF_Small , OF_Small_With, 73 }, { 26 , 0 , SINGLE , OF_Small , OF_Small_With, 74 }, { 26 , 1 , SINGLE , OF_Small , OF_Small_With, 75 }, { 26 , 2 , ROW_1 , OF_Small , OF_Small_With, 76 }, { 26 , 2 , ROW_2 , OF_Small , OF_Small_With, 77 }, { 26 , 3 , ROW_1 , OF_Small , OF_Small_With, 78 }, { 26 , 3 , ROW_2 , OF_Small , OF_Small_With, 79 }, { 26 , 4 , ROW_1 , OF_Small , OF_Small_With, 80 }, { 26 , 4 , ROW_2 , OF_Small , OF_Small_With, 81 }, { 26 , 5 , ROW_1 , OF_Small , OF_Small_With, 82 }, { 26 , 5 , ROW_2 , OF_Small , OF_Small_With, 83 }, { 26 , 6 , ROW_1 , OF_Small , OF_Small_With, 84 }, { 26 , 6 , ROW_2 , OF_Small , OF_Small_With, 85 }, { 26 , 7 , ROW_1 , OF_Small , OF_Small_With, 86 }, { 26 , 7 , ROW_2 , OF_Small , OF_Small_With, 87 }, { 28 , 0 , SINGLE , OF_Small , OF_Small_With, 88 }, { 28 , 1 , ROW_1 , OF_Small , OF_Small_With, 89 }, { 28 , 1 , ROW_2 , OF_Small , OF_Small_With, 90 }, { 28 , 2 , ROW_1 , OF_Small , OF_Small_With, 91 }, { 28 , 2 , ROW_2 , OF_Small , OF_Small_With, 92 }, { 28 , 3 , ROW_1 , OF_Small , OF_Small_With, 93 }, { 28 , 3 , ROW_2 , OF_Small , OF_Small_With, 94 }, { 28 , 4 , ROW_1 , OF_Small , OF_Small_With, 95 }, { 28 , 4 , ROW_2 , OF_Small , OF_Small_With, 96 }, { 28 , 5 , ROW_1 , OF_Small , OF_Small_With, 97 }, { 28 , 5 , ROW_2 , OF_Small , OF_Small_With, 98 }, { 28 , 6 , ROW_1 , OF_Small , OF_Small_With, 99 }, { 28 , 6 , ROW_2 , OF_Small , OF_Small_With, 100 }, { 28 , 7 , ROW_1 , OF_Small , OF_Small_With, 101 }, { 28 , 7 , ROW_2 , OF_Small , OF_Small_With, 102 }, { 28 , 8 , ROW_1 , OF_Small , OF_Small_With, 103 }, { 28 , 8 , ROW_2 , OF_Small , OF_Small_With, 104 }, { 28 , 9 , ROW_1 , OF_Small , OF_Small_With, 105 }, { 28 , 9 , ROW_2 , OF_Small , OF_Small_With, 106 }, { 28 , 10 , ROW_1 , OF_Small , OF_Small_With, 107 }, { 28 , 10 , ROW_2 , OF_Small , OF_Small_With, 108 }, { 28 , 11 , ROW_1 , OF_Small , OF_Small_With, 109 }, { 28 , 11 , ROW_2 , OF_Small , OF_Small_With, 110 }, { 28 , 12 , ROW_1 , OF_Small , OF_Small_With, 111 }, { 28 , 12 , ROW_2 , OF_Small , OF_Small_With, 112 }, { 28 , 13 , ROW_1 , OF_Small , OF_Small_With, 113 }, { 28 , 13 , ROW_2 , OF_Small , OF_Small_With, 114 }, { 28 , 14 , ROW_1 , OF_Small , OF_Small_With, 115 }, { 28 , 14 , ROW_2 , OF_Small , OF_Small_With, 116 }, { 28 , 15 , ROW_1 , OF_Small , OF_Small_With, 117 }, { 28 , 15 , ROW_2 , OF_Small , OF_Small_With, 118 }, { 28 , 16 , ROW_1 , OF_Small , OF_Small_With, 119 }, { 28 , 16 , ROW_2 , OF_Small , OF_Small_With, 120 }, { 28 , 17 , ROW_1 , OF_Small , OF_Small_With, 121 }, { 28 , 17 , ROW_2 , OF_Small , OF_Small_With, 122 }, { 28 , 18 , ROW_1 , OF_Small , OF_Small_With, 123 }, { 28 , 18 , ROW_2 , OF_Small , OF_Small_With, 124 }, { 28 , 19 , ROW_1 , OF_Small , OF_Small_With, 125 }, { 28 , 19 , ROW_2 , OF_Small , OF_Small_With, 126 }, { 28 , 20 , ROW_1 , OF_Small , OF_Small_With, 127 }, { 28 , 20 , ROW_2 , OF_Small , OF_Small_With, 128 }, { 28 , 21 , ROW_1 , OF_Small , OF_Small_With, 129 }, { 28 , 21 , ROW_2 , OF_Small , OF_Small_With, 130 }, { 28 , 22 , ROW_1 , OF_Small , OF_Small_With, 131 }, { 28 , 22 , ROW_2 , OF_Small , OF_Small_With, 132 }, { 28 , 23 , ROW_1 , OF_Small , OF_Small_With, 133 }, { 28 , 23 , ROW_2 , OF_Small , OF_Small_With, 134 }, { 28 , 24 , ROW_1 , OF_Small , OF_Small_With, 135 }, { 28 , 24 , ROW_2 , OF_Small , OF_Small_With, 136 }, { 28 , 25 , ROW_1 , OF_Small , OF_Small_With, 137 }, { 28 , 25 , ROW_2 , OF_Small , OF_Small_With, 138 }, { 28 , 26 , ROW_1 , OF_Small , OF_Small_With, 139 }, { 28 , 26 , ROW_2 , OF_Small , OF_Small_With, 140 }, { 28 , 27 , ROW_1 , OF_Small , OF_Small_With, 141 }, { 28 , 27 , ROW_2 , OF_Small , OF_Small_With, 142 }, { 28 , 28 , ROW_1 , OF_Small , OF_Small_With, 143 }, { 28 , 28 , ROW_2 , OF_Small , OF_Small_With, 144 }, { 28 , 29 , ROW_1 , OF_Small , OF_Small_With, 145 }, { 28 , 29 , ROW_2 , OF_Small , OF_Small_With, 146 }, { 28 , 30 , ROW_1 , OF_Small , OF_Small_With, 147 }, { 28 , 30 , ROW_2 , OF_Small , OF_Small_With, 148 }, { 28 , 31 , ROW_1 , OF_Small , OF_Small_With, 149 }, { 28 , 31 , ROW_2 , OF_Small , OF_Small_With, 150 }, { 28 , 32 , ROW_1 , OF_Small , OF_Small_With, 151 }, { 28 , 32 , ROW_2 , OF_Small , OF_Small_With, 152 }, { 28 , 33 , ROW_1 , OF_Small , OF_Small_With, 153 }, { 28 , 33 , ROW_2 , OF_Small , OF_Small_With, 154 }, { 28 , 34 , ROW_1 , OF_Small , OF_Small_With, 155 }, { 28 , 34 , ROW_2 , OF_Small , OF_Small_With, 156 }, { 28 , 35 , ROW_1 , OF_Small , OF_Small_With, 157 }, { 28 , 35 , ROW_2 , OF_Small , OF_Small_With, 158 }, { 28 , 36 , ROW_1 , OF_Small , OF_Small_With, 159 }, { 28 , 36 , ROW_2 , OF_Small , OF_Small_With, 160 }, { 28 , 37 , ROW_1 , OF_Small , OF_Small_With, 161 }, { 28 , 37 , ROW_2 , OF_Small , OF_Small_With, 162 }, { 28 , 38 , ROW_1 , OF_Small , OF_Small_With, 163 }, { 28 , 38 , ROW_2 , OF_Small , OF_Small_With, 164 }, { 28 , 39 , ROW_1 , OF_Small , OF_Small_With, 165 }, { 28 , 39 , ROW_2 , OF_Small , OF_Small_With, 166 }, { 28 , 40 , ROW_1 , OF_Small , OF_Small_With, 167 }, { 28 , 40 , ROW_2 , OF_Small , OF_Small_With, 168 }, { 28 , 41 , ROW_1 , OF_Small , OF_Small_With, 169 }, { 28 , 41 , ROW_2 , OF_Small , OF_Small_With, 170 }, { 28 , 42 , ROW_1 , OF_Small , OF_Small_With, 171 }, { 28 , 42 , ROW_2 , OF_Small , OF_Small_With, 172 }, { 28 , 43 , ROW_1 , OF_Small , OF_Small_With, 173 }, { 28 , 43 , ROW_2 , OF_Small , OF_Small_With, 174 }, { 28 , 44 , ROW_1 , OF_Small , OF_Small_With, 175 }, { 28 , 44 , ROW_2 , OF_Small , OF_Small_With, 176 }, { 28 , 45 , ROW_1 , OF_Small , OF_Small_With, 177 }, { 28 , 45 , ROW_2 , OF_Small , OF_Small_With, 178 }, { 28 , 46 , ROW_1 , OF_Small , OF_Small_With, 179 }, { 28 , 46 , ROW_2 , OF_Small , OF_Small_With, 180 }, { 28 , 47 , ROW_1 , OF_Small , OF_Small_With, 181 }, { 28 , 47 , ROW_2 , OF_Small , OF_Small_With, 182 }, { 28 , 48 , ROW_1 , OF_Small , OF_Small_With, 183 }, { 28 , 48 , ROW_2 , OF_Small , OF_Small_With, 184 }, { 28 , 49 , ROW_1 , OF_Small , OF_Small_With, 185 }, { 28 , 49 , ROW_2 , OF_Small , OF_Small_With, 186 }, { 28 , 50 , ROW_1 , OF_Small , OF_Small_With, 187 }, { 28 , 50 , ROW_2 , OF_Small , OF_Small_With, 188 }, { 28 , 51 , ROW_1 , OF_Small , OF_Small_With, 189 }, { 28 , 51 , ROW_2 , OF_Small , OF_Small_With, 190 }, { 28 , 52 , ROW_1 , OF_Small , OF_Small_With, 191 }, { 28 , 52 , ROW_2 , OF_Small , OF_Small_With, 192 }, { 28 , 53 , ROW_1 , OF_Small , OF_Small_With, 193 }, { 28 , 53 , ROW_2 , OF_Small , OF_Small_With, 194 }, { 28 , 54 , ROW_1 , OF_Small , OF_Small_With, 195 }, { 28 , 54 , ROW_2 , OF_Small , OF_Small_With, 196 }, { 28 , 55 , ROW_1 , OF_Small , OF_Small_With, 197 }, { 28 , 55 , ROW_2 , OF_Small , OF_Small_With, 198 }, { 28 , 56 , ROW_1 , OF_Small , OF_Small_With, 199 }, { 28 , 56 , ROW_2 , OF_Small , OF_Small_With, 200 }, { 28 , 57 , ROW_1 , OF_Small , OF_Small_With, 201 }, { 28 , 57 , ROW_2 , OF_Small , OF_Small_With, 202 }, { 28 , 58 , ROW_1 , OF_Small , OF_Small_With, 203 }, { 28 , 58 , ROW_2 , OF_Small , OF_Small_With, 204 }, { 28 , 59 , ROW_1 , OF_Small , OF_Small_With, 205 }, { 28 , 59 , ROW_2 , OF_Small , OF_Small_With, 206 }, { 28 , 60 , ROW_1 , OF_Small , OF_Small_With, 207 }, { 28 , 60 , ROW_2 , OF_Small , OF_Small_With, 208 }, { 28 , 61 , ROW_1 , OF_Small , OF_Small_With, 209 }, { 28 , 61 , ROW_2 , OF_Small , OF_Small_With, 210 }, { 28 , 62 , ROW_1 , OF_Small , OF_Small_With, 211 }, { 28 , 62 , ROW_2 , OF_Small , OF_Small_With, 212 }, { 28 , 63 , ROW_1 , OF_Small , OF_Small_With, 213 }, { 28 , 63 , ROW_2 , OF_Small , OF_Small_With, 214 }, { 28 , 64 , ROW_1 , OF_Small , OF_Small_With, 215 }, { 28 , 64 , ROW_2 , OF_Small , OF_Small_With, 216 }, { 28 , 65 , ROW_1 , OF_Small , OF_Small_With, 217 }, { 28 , 65 , ROW_2 , OF_Small , OF_Small_With, 218 }, { 28 , 66 , ROW_1 , OF_Small , OF_Small_With, 219 }, { 28 , 66 , ROW_2 , OF_Small , OF_Small_With, 220 } }; define AlwaysON = 1;
 define DoubleTapL3 = 2;
 define ModifierL3 = 3;
 define ModifierPR1 = 4;
 define ModifierPR2 = 5;
 define ModifierPL1 = 6;
 define ModifierPL2 = 7;
 int XBOX1_CONTROLLER_HOLD382632_533959;
 int XBOX1_CONTROLLER_HOLD527003_151861;
 int XBOX1_CONTROLLER_HOLD101452_443785;
 int XBOX1_CONTROLLER_HOLD940080_712320 = TRUE;
 int XBOX1_CONTROLLER_HOLD782577_342508;
 int XBOX1_CONTROLLER_HOLD495718_510468;
 int Sombrero;
 int XBOX1_CONTROLLER_HOLD788066_286674 = 40;
 int XBOX1_CONTROLLER_HOLD685005_621772;
 int XBOX1_CONTROLLER_HOLD582595_441923;
 int XBOX1_CONTROLLER_HOLD216454_752951;
 int XBOX1_CONTROLLER_HOLD774482_150812;
 int XBOX1_CONTROLLER_HOLD749584_223145;
 int XBOX1_CONTROLLER_HOLD298206_272790;
 function print_parent_mod_options_label(f_cheract_size , f_label ){ printf( center_x( f_cheract_size , OLED_FONT_SMALL_WIDTH) ,ROW_1_Y, OLED_FONT_SMALL , OLED_WHITE, f_label); } function f_RightStickMove () { if( get_ival(PS4_RY) < -70 && !XBOX1_CONTROLLER_HOLD582595_441923 ) { XBOX1_CONTROLLER_HOLD582595_441923 = TRUE;
 XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD337226_272844;
 run_skill_combo(XBOX1_CONTROLLER_HOLD337226_272844); } if( get_ival(PS4_RY) > 70 && !XBOX1_CONTROLLER_HOLD216454_752951 ) { XBOX1_CONTROLLER_HOLD216454_752951 = TRUE;
 XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD663280_652109;
 run_skill_combo(XBOX1_CONTROLLER_HOLD663280_652109); } if( get_ival(PS4_RX) < -70 && !XBOX1_CONTROLLER_HOLD774482_150812 ) { XBOX1_CONTROLLER_HOLD774482_150812 = TRUE;
 XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD760217_523408;
 run_skill_combo(XBOX1_CONTROLLER_HOLD760217_523408); } if( get_ival(PS4_RX) > 70 && !XBOX1_CONTROLLER_HOLD749584_223145 ) { XBOX1_CONTROLLER_HOLD749584_223145 = TRUE;
 XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD554987_453137;
 run_skill_combo(XBOX1_CONTROLLER_HOLD554987_453137); } if(XBOX1_CONTROLLER_HOLD137336_617074){ if(event_press(PS4_R3)){ XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 XBOX1_CONTROLLER_HOLD298206_272790 = XBOX1_CONTROLLER_HOLD137336_617074;
 run_skill_combo(XBOX1_CONTROLLER_HOLD137336_617074); } set_val(PS4_R3,0); } if(abs(get_ival(PS4_RY))<20 && abs(get_ival(PS4_RX))<20){ XBOX1_CONTROLLER_HOLD582595_441923 = 0;
 XBOX1_CONTROLLER_HOLD216454_752951 = 0;
 XBOX1_CONTROLLER_HOLD774482_150812 = 0;
 XBOX1_CONTROLLER_HOLD749584_223145 = 0;
 } } function run_skill_combo( f_skill){ if(f_skill == FAKE_SHOT_SKILL) combo_run(XBOX1_CONTROLLER_HOLD575803_704291); if(f_skill == HEEL_TO_HEEL_FLICK_SKILL) combo_run(XBOX1_CONTROLLER_HOLD726245_507825); if(f_skill == HEEL_FLICK_TURN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD726245_507825); if(f_skill == RAINBOW_SKILL) combo_run(XBOX1_CONTROLLER_HOLD305427_528162); if(f_skill == 5){Sombrero = TRUE;
 combo_run(XBOX1_CONTROLLER_HOLD944029_567972);} if(f_skill == FAKE_PASS_SKILL) combo_run(XBOX1_CONTROLLER_HOLD575803_704291); if(f_skill == 7){Sombrero = FALSE;
 combo_run(XBOX1_CONTROLLER_HOLD944029_567972);} if(f_skill == STEP_OVER_FEINT_SKILL) combo_run(XBOX1_CONTROLLER_HOLD390036_659337); if(f_skill == DRAG_TO_DRAG_SKILL)combo_run(XBOX1_CONTROLLER_HOLD571821_492829); if(f_skill == HOCUS_POCUS_SKILL) combo_run(XBOX1_CONTROLLER_HOLD280799_267880); if(f_skill == TRIPLE_ELASTICO_SKILL) combo_run(XBOX1_CONTROLLER_HOLD426140_767298); if(f_skill == ELASTICO_SKILL){XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 combo_run(XBOX1_CONTROLLER_HOLD161309_700625);} if(f_skill == REVERSE_ELASTICO_SKILL){XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
combo_run(XBOX1_CONTROLLER_HOLD161309_700625);} if(f_skill == CRUYFF_TURN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD114292_164249); if(f_skill == LA_CROQUETA_SKILL) combo_run(XBOX1_CONTROLLER_HOLD999858_215245); if(f_skill == RONALDO_CHOP_SKILL) combo_run(XBOX1_CONTROLLER_HOLD575803_704291); if(f_skill == ROULETTE_SKILL) combo_run(XBOX1_CONTROLLER_HOLD990448_252941); if(f_skill == FLAIR_ROULETTE_SKILL) combo_run(XBOX1_CONTROLLER_HOLD990448_252941); if(f_skill == BALL_ROLL_SKILL) combo_run(XBOX1_CONTROLLER_HOLD559953_291238); if(f_skill == BERBA_MCGEADY_SPIN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD823336_190301); if(f_skill == BOLASIE_FLICK_SKILL) combo_run(XBOX1_CONTROLLER_HOLD823336_190301); if(f_skill == TORNADO_SKILL) combo_run(XBOX1_CONTROLLER_HOLD667463_690851); if(f_skill == THREE_TOUCH_ROULETTE_SKILL)combo_run(XBOX1_CONTROLLER_HOLD667463_690851); if(f_skill == ALTERNATIVE_ELASTICO_CHOP_SKILL)combo_run(XBOX1_CONTROLLER_HOLD667463_690851); if(f_skill == BALL_ROLL_CHOP_SKILL) combo_run(XBOX1_CONTROLLER_HOLD845068_248021); if(f_skill == FEINT_AND_EXIT_SKILL) combo_run(XBOX1_CONTROLLER_HOLD387740_505538); if(f_skill == FEINT_L_EXIT_R_SKILL) combo_run(XBOX1_CONTROLLER_HOLD387740_505538); if(f_skill == LATERAL_HEEL_TO_HEEL_SKILL) combo_run(XBOX1_CONTROLLER_HOLD102535_534462); if(f_skill == WAKA_WAKA_SKILL) combo_run(XBOX1_CONTROLLER_HOLD558488_922004); if(f_skill == BODY_FEINT_SKILL) combo_run(XBOX1_CONTROLLER_HOLD661877_159051); if(f_skill == DRAG_TO_HEEL ) combo_run(XBOX1_CONTROLLER_HOLD667463_690851); if(f_skill == BALL_ROLL_FAKE_TURN ) combo_run(XBOX1_CONTROLLER_HOLD823336_190301); if(f_skill == FEINT_FORWARD_AND_TURN ) combo_run(XBOX1_CONTROLLER_HOLD405750_554290); if(f_skill == XBOX1_CONTROLLER_HOLD695191_358660 ) combo_run(XBOX1_CONTROLLER_HOLD695191_358660); if(f_skill == XBOX1_CONTROLLER_HOLD514741_814386 ) combo_run(XBOX1_CONTROLLER_HOLD514741_814386); if(f_skill == CANCELED_THREE_TOUCH_ROULETTE_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD228473_931773); if(f_skill == REVERSE_STEP_OVER_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD325792_583654); if(f_skill == FAKE_DRAG_BACK_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD627070_955826); if(f_skill == STEP_OVER_BOOST_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD652123_413275); if(f_skill == CANCEL_SHOOT_SKILL) combo_run(XBOX1_CONTROLLER_HOLD754649_164085); if(f_skill == DIRECTIONAL_NUTMEG_SKILL) combo_run(XBOX1_CONTROLLER_HOLD258159_457072); if(f_skill == CANCELED_BERBA_SPIN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD222763_212149); if(f_skill == CANCELED_BERBA_SPIN_WITH_DIRECTION) combo_run(XBOX1_CONTROLLER_HOLD222763_212149); if(f_skill == BALL_ROLL_TO_SCOOP_TURN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD574341_901192); if(f_skill == DRIBBLING_SKILL ){XBOX1_CONTROLLER_HOLD661207_131240 = TRUE;
 combo_run(XBOX1_CONTROLLER_HOLD124380_100594);} if(f_skill == FOUR_TOUCH_TURN_SKILLS ) combo_run(XBOX1_CONTROLLER_HOLD416429_884883); if(f_skill == SKILLED_BRIDGE_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD764280_123067); if(f_skill == SCOOP_TURN_FAKE_SKILL) combo_run(XBOX1_CONTROLLER_HOLD577183_178039); if(f_skill == BALL_ROLL_STEP_OVER_SKILL) combo_run(XBOX1_CONTROLLER_HOLD464968_106419); if(f_skill == CANCELED_4_TOUCH_TURN_SKILL) combo_run(XBOX1_CONTROLLER_HOLD224948_717092); if(f_skill == FAKE_SHOT_CANCEL_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD637258_829341); if(f_skill == OKKOSHA_FLICK_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD675838_617028); if(f_skill == ADVANCED_RAINBOW_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD708192_401293); if(f_skill == STOP_LA_CROQUETA_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD495931_282591); if(f_skill == JUGGLING_RAINBOW_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD404370_455996); if(f_skill == STOP_NEYMAR_ROLL_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD404953_267280); if(f_skill == STOP_V_DRAG_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD511131_302230); if(f_skill == REV_OR_ELASTICO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD161309_700625); if(f_skill == STOP_REV_OR_ELASTICO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD798374_667502); if(f_skill == DRAG_REV_OR_ELASTICO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD340019_722213); if(f_skill == FAKE_RABONA_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD861344_922734); if(f_skill == RABONA_TO_REV_ELASTICO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD402940_727521); if(f_skill == RABONA_TO_ELASTICO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD640363_863546); if(f_skill == SOMBRERO_FLICK_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD355057_815225); if(f_skill == JUGGLE_BACK_SOMBRERO_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD678223_364697); if(f_skill == FAKE_BERBA_OPP_EXIT_SKILL ) combo_run(XBOX1_CONTROLLER_HOLD524199_223589); } combo XBOX1_CONTROLLER_HOLD416429_884883 { set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(30); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD764280_123067 { set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(FinesseShot,100); wait(60); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(30); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(FinesseShot,100); wait(80); } combo XBOX1_CONTROLLER_HOLD577183_178039 { RA_L_R () ;
 wait(280); LA_L_R() set_val(ShotBtn,100); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(40); LA_L_R() set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); set_val(PassBtn,100); wait(60); LA_L_R() set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); wait(250); LA_L_R() wait(300); } combo XBOX1_CONTROLLER_HOLD575803_704291 { set_val(ShotBtn,100); wait(40); set_val(ShotBtn,100); set_val(PassBtn,100); wait(60); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); } combo XBOX1_CONTROLLER_HOLD726245_507825 { RA_UP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD305427_528162 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_UP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD944029_567972 { set_val(MOVE_X,inv(LX)); set_val(MOVE_Y,inv(LY)); set_val(FinesseShot,100); set_val(PlayerRun,100); wait(60); set_val(MOVE_X,inv(LX)); set_val(MOVE_Y,inv(LY)); set_val(FinesseShot,100); set_val(PlayerRun,100); if(Sombrero) set_val(PS4_R3,100); wait(40); } combo XBOX1_CONTROLLER_HOLD390036_659337 { RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD571821_492829 { LA(0,0); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(40); LA(0,0); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); wait(40); LA(0,0); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); set_val(PassBtn,100); wait(80); LA(0,0); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); } combo XBOX1_CONTROLLER_HOLD280799_267880 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD426140_767298 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = TRUE;
 RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD161309_700625 { RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = !XBOX1_CONTROLLER_HOLD685005_621772;
 RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD114292_164249 { LA (inv(LX),inv(LY)); set_val(ShotBtn,100); wait(40); LA (inv(LX),inv(LY)); set_val(ShotBtn,100); set_val(PassBtn,100); wait(80); LA (inv(LX),inv(LY)); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); } combo XBOX1_CONTROLLER_HOLD999858_215245 { set_val(PlayerRun,100); RA_L_R (); wait(500); } combo XBOX1_CONTROLLER_HOLD990448_252941 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD559953_291238 { RA_L_R () ;
 wait(250); } combo XBOX1_CONTROLLER_HOLD823336_190301 { if(XBOX1_CONTROLLER_HOLD298206_272790 == BALL_ROLL_FAKE_TURN ) XBOX1_CONTROLLER_HOLD495718_510468 = 200;
 else XBOX1_CONTROLLER_HOLD495718_510468 = 1;
 wait(XBOX1_CONTROLLER_HOLD495718_510468); RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD667463_690851 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD845068_248021 { RA_L_R () ;
 wait(300); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_OPP () ;
 wait(300); } combo XBOX1_CONTROLLER_HOLD387740_505538 { RA_OPP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD102535_534462 { set_val(PlayerRun,100); RA_OPP () ;
 wait(60); set_val(PlayerRun,100); RA_ZERO (); wait(60); set_val(PlayerRun,100); RA_L_R () ;
 wait(60); wait(300); } combo XBOX1_CONTROLLER_HOLD558488_922004 { RA_OPP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (0,0); RA_UP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (0,0); RA_L_R() wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = !XBOX1_CONTROLLER_HOLD685005_621772;
 LA_L_R(); wait(1000); } combo XBOX1_CONTROLLER_HOLD661877_159051 { RA_L_R () ;
 wait(100); RA_ZERO (); wait(80); LA_L_R(); wait(600); wait(600); } combo XBOX1_CONTROLLER_HOLD405750_554290 { LA (0,0); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (0,0); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (0,0); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (0,0); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD695191_358660 { set_val(FinesseShot,100); set_val(PlayerRun,100); RA_DOWN (); wait(80); } combo XBOX1_CONTROLLER_HOLD514741_814386 { set_val(PlayerRun,100); RA_L_R (); wait(300); set_val(PS4_L2,100); set_val(PS4_R2,100); LA_L_R(); wait(800); } function Cancel_Skill (){ set_val(PS4_L2,100); set_val(PS4_R2,100); } combo XBOX1_CONTROLLER_HOLD228473_931773 { RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); Cancel_Skill(); wait(300); } combo XBOX1_CONTROLLER_HOLD325792_583654 { RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); } combo XBOX1_CONTROLLER_HOLD627070_955826 { LA (inv(LX),inv(LY)); wait(200); XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 LA_L_R (); wait(50); XBOX1_CONTROLLER_HOLD685005_621772 = !XBOX1_CONTROLLER_HOLD685005_621772;
 LA_L_R (); wait(540); } combo XBOX1_CONTROLLER_HOLD652123_413275 { if (XBOX1_CONTROLLER_HOLD685005_621772) XBOX1_CONTROLLER_HOLD649799_785815 = zone_p + 1;
 else XBOX1_CONTROLLER_HOLD649799_785815 = zone_p - 1;
 calc_relative_xy(XBOX1_CONTROLLER_HOLD649799_785815); RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R(); LA (XBOX1_CONTROLLER_HOLD670041_564769,move_ly); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA (XBOX1_CONTROLLER_HOLD670041_564769,move_ly); wait(1000); } combo XBOX1_CONTROLLER_HOLD754649_164085 { set_val(ShotBtn,100); wait(290); Cancel_Skill(); wait(300); } combo XBOX1_CONTROLLER_HOLD258159_457072 { set_val(FinesseShot,100); set_val(PlayerRun,100); wait(20); set_val(FinesseShot,100); set_val(PlayerRun,100); if (XBOX1_CONTROLLER_HOLD685005_621772) XBOX1_CONTROLLER_HOLD649799_785815 = zone_p + 1;
 else { XBOX1_CONTROLLER_HOLD649799_785815 = zone_p - 1;
 } calc_relative_xy(XBOX1_CONTROLLER_HOLD649799_785815); RA (XBOX1_CONTROLLER_HOLD670041_564769,move_ly); set_val(SprintBtn,100); wait(100); } combo XBOX1_CONTROLLER_HOLD222763_212149 { RA_UP (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_ZERO (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_L_R () ;
 wait(XBOX1_CONTROLLER_HOLD788066_286674); if( XBOX1_CONTROLLER_HOLD298206_272790 == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R(); Cancel_Skill(); wait(200); if( XBOX1_CONTROLLER_HOLD298206_272790 == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R(); wait(300); } combo XBOX1_CONTROLLER_HOLD574341_901192 { RA_L_R () ;
 wait(280); LA_L_R() set_val(ShotBtn,100); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(40); LA_L_R() set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); set_val(PassBtn,100); wait(60); LA_L_R() set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); } int XBOX1_CONTROLLER_HOLD661207_131240;
 combo XBOX1_CONTROLLER_HOLD124380_100594 { set_val(FinesseShot,100); wait(20); set_val(FinesseShot,100); LA_L_R(); wait(375); wait(20); set_val(FinesseShot, 0); set_val(SprintBtn,100); wait(800); XBOX1_CONTROLLER_HOLD661207_131240 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD464968_106419 { RA_L_R (); wait(300); RA_UP(); wait(60); } combo XBOX1_CONTROLLER_HOLD224948_717092 { set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(30); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_DOWN (); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA(0,0); wait(400); Cancel_Skill(); wait(70); } combo XBOX1_CONTROLLER_HOLD637258_829341 { set_val(ShotBtn,100); wait(40); set_val(ShotBtn,100); set_val(PassBtn,100); wait(60); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); wait(140); Cancel_Skill(); wait(100); } combo XBOX1_CONTROLLER_HOLD675838_617028 { set_val(PlayerRun,100); RA_UP (); wait(300); } combo XBOX1_CONTROLLER_HOLD708192_401293 { RA_DOWN (); wait(100); RA_ZERO(); wait(40); RA_UP(); wait(320); RA_ZERO(); wait(220); RA_UP(); wait(100); } combo XBOX1_CONTROLLER_HOLD495931_282591 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); call(XBOX1_CONTROLLER_HOLD999858_215245); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD404370_455996 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); wait(60); call(XBOX1_CONTROLLER_HOLD824937_174974); call(XBOX1_CONTROLLER_HOLD824937_174974); call(XBOX1_CONTROLLER_HOLD824937_174974); call(XBOX1_CONTROLLER_HOLD824937_174974); call(XBOX1_CONTROLLER_HOLD824937_174974); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_DOWN (); wait(70); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_ZERO(); wait(40); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); RA_UP(); wait(70); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(800); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD404953_267280 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); RA_L_R(); wait(200); RA_L_R(); LA(LX,LY); wait(125); wait(300); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD511131_302230 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); set_val(SprintBtn,100); wait(125); set_val(ShotBtn,100); set_val(SprintBtn,100); wait(30); LA_L_R(); set_val(SprintBtn,100); set_val(ShotBtn,100); set_val(PassBtn,100); wait(30); LA_L_R(); set_val(SprintBtn,100); set_val(ShotBtn,0); set_val(PassBtn,100); wait(30); LA_L_R(); wait(400); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD798374_667502 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); call(XBOX1_CONTROLLER_HOLD387740_505538); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD340019_722213 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 zone_saver(); call(XBOX1_CONTROLLER_HOLD944029_567972); wait(280); call(XBOX1_CONTROLLER_HOLD387740_505538); wait(300); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD861344_922734{ LA(inv(LX),inv(LY)); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); wait(40); LA(inv(LX),inv(LY)); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,100); set_val(PassBtn,100); wait(60); LA(inv(LX),inv(LY)); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(ShotBtn,0); set_val(PassBtn,100); wait(60); LA(0,0); wait(300); } combo XBOX1_CONTROLLER_HOLD402940_727521 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 zone_saver(); call(XBOX1_CONTROLLER_HOLD861344_922734); wait(100); RA_UP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_DOWN () wait(XBOX1_CONTROLLER_HOLD788066_286674); wait(400); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD640363_863546 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 zone_saver(); call(XBOX1_CONTROLLER_HOLD861344_922734); wait(100); RA_DOWN(); wait(XBOX1_CONTROLLER_HOLD788066_286674); XBOX1_CONTROLLER_HOLD685005_621772 = FALSE;
 RA_L_R (); wait(XBOX1_CONTROLLER_HOLD788066_286674); RA_UP () wait(XBOX1_CONTROLLER_HOLD788066_286674); wait(400); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD355057_815225 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); wait(100); RA_UP(); wait(50); RA_ZERO () wait(50); RA_UP() wait(50); RA_ZERO () wait(50); RA_DOWN(); wait(50); wait(700); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD678223_364697 { XBOX1_CONTROLLER_HOLD267607_885628 = TRUE;
 call(XBOX1_CONTROLLER_HOLD837281_922481); wait(100); call(XBOX1_CONTROLLER_HOLD824937_174974); call(XBOX1_CONTROLLER_HOLD824937_174974); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(FinesseShot,100); LA(inv(LX),inv(LY)); wait(400); XBOX1_CONTROLLER_HOLD267607_885628 = FALSE;
 } combo XBOX1_CONTROLLER_HOLD524199_223589{ LA(LX,LY); RA_UP(); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA(LX,LY); RA_ZERO(); wait(XBOX1_CONTROLLER_HOLD788066_286674); LA(LX,LY); RA_L_R(); wait(XBOX1_CONTROLLER_HOLD788066_286674); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(SprintBtn,100); LA(inv(LX),inv(LY)); wait(600); } combo XBOX1_CONTROLLER_HOLD837281_922481 { zone_saver(); wait(20); wait(100); set_val(SprintBtn,100); wait(40); wait(160); } function zone_saver() { XBOX1_CONTROLLER_HOLD649799_785815 = zone_p calc_relative_xy(XBOX1_CONTROLLER_HOLD649799_785815); LX = XBOX1_CONTROLLER_HOLD670041_564769;
 LY = move_ly;
 } combo XBOX1_CONTROLLER_HOLD824937_174974{ set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); set_val(FinesseShot,100); wait(100); set_val(XBOX1_CONTROLLER_HOLD455115_623354,100); wait(100); } int XBOX1_CONTROLLER_HOLD267607_885628;
