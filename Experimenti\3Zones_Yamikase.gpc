define MIN_RADIUS_1 = 2400;
define MIN_RADIUS_2 = 6000;
define MAX_RADIUS = 10000;
define MAX_VALUE = 31111;
define ANGLE_INTERVAL_1 = 16;
define ANGLE_INTERVAL_2 = 8;

function adjust_polar(interval, angle, x, y) {
    // Normalize angle to specified interval and set polar coordinates
    set_polar(POLAR_LS, 
        (((inv(angle) * interval) / 360) * 360) / interval,
        min(isqrt(pow(x, 2) + pow(y, 2)), MAX_VALUE)
    );
}

main {
    // Get current input values once
    define current_radius = get_polar(POLAR_LS, POLAR_RADIUS);
    define current_angle = get_polar(POLAR_LS, POLAR_ANGLE);
    define lx = get_val(POLAR_LX);
    define ly = get_val(POLAR_LY);

    if (current_radius < MIN_RADIUS_1) {
        // Deadzone - reset to center
        set_polar(POLAR_LS, 0, 0);
    }
    else if (current_radius >= MIN_RADIUS_1 && current_radius < MIN_RADIUS_2) {
        // Medium range - coarse angle adjustment
        adjust_polar(ANGLE_INTERVAL_1, current_angle, lx, ly);
    }
    else if (current_radius >= MIN_RADIUS_2 && current_radius < MAX_RADIUS) {
        // High range - fine angle adjustment
        adjust_polar(ANGLE_INTERVAL_2, current_angle, lx, ly);
    }
}