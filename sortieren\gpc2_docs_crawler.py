import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin
import re
import asyncio
import aiohttp
import async_timeout

class GPCDocsCrawler:
    def __init__(self, base_url="https://www.consoletuner.com/wiki/"):
        self.base_url = base_url
        self.visited_urls = set()
        self.gpc_docs_urls = set()
        self.output_dir = "gpc2_docs"
        self.session = None
        
        self.gpc_patterns = [
            "t2:gpc_",
            "t2:.*_functions",
            "t2:scripting",
            "t2:tutorial",
            "t2:examples",
            "t2:reference"
        ]

    async def init_session(self):
        self.session = aiohttp.ClientSession()

    async def close_session(self):
        if self.session:
            await self.session.close()
        
    def is_gpc_related(self, url):
        if "id=" not in url:
            return False
        page_id = url.split("id=")[-1].lower()
        return any(re.search(pattern, page_id) for pattern in self.gpc_patterns)
    
    async def get_links_from_page(self, url):
        try:
            async with async_timeout.timeout(10):
                async with self.session.get(url) as response:
                    text = await response.text()
                    soup = BeautifulSoup(text, 'html.parser')
                    
                    links = set()
                    for a in soup.find_all('a', href=True):
                        href = a['href']
                        full_url = urljoin(self.base_url, href)
                        if "consoletuner.com/wiki" in full_url and "action=" not in full_url:
                            links.add(full_url)
                    return links
        except Exception as e:
            print(f"Error fetching links from {url}: {e}")
            return set()

    async def crawl(self, url):
        if url in self.visited_urls:
            return
        
        self.visited_urls.add(url)
        
        if self.is_gpc_related(url):
            print(f"Found GPC documentation: {url}")
            self.gpc_docs_urls.add(url)
        
        links = await self.get_links_from_page(url)
        tasks = []
        for link in links:
            if link not in self.visited_urls:
                tasks.append(self.crawl(link))
        
        if tasks:
            await asyncio.gather(*tasks)

    async def save_page(self, url):
        try:
            page_name = url.split('=')[-1].replace(':', '_')
            output_file = os.path.join(self.output_dir, f"{page_name}.md")
            
            print(f"Saving: {url} to {output_file}")
            
            async with self.session.get(url) as response:
                text = await response.text()
            
            soup = BeautifulSoup(text, 'html.parser')
            content = soup.find('div', {'class': 'page'})
            
            if content:
                title = soup.find('h1').text.strip() if soup.find('h1') else "GPC2 Documentation"
                markdown_content = f"# {title}\n\n"
                markdown_content += f"Source: {url}\n\n"
                
                for element in content.find_all(['h1', 'h2', 'h3', 'p', 'pre', 'code', 'ul', 'li', 'table']):
                    if element.name in ['h1', 'h2', 'h3']:
                        markdown_content += f"{'#' * int(element.name[1])} {element.text.strip()}\n\n"
                    elif element.name == 'p':
                        markdown_content += f"{element.text.strip()}\n\n"
                    elif element.name == 'pre' or element.name == 'code':
                        markdown_content += f"```\n{element.text.strip()}\n```\n\n"
                    elif element.name == 'ul':
                        for li in element.find_all('li'):
                            markdown_content += f"- {li.text.strip()}\n"
                        markdown_content += "\n"
                    elif element.name == 'table':
                        for row in element.find_all('tr'):
                            cols = row.find_all(['td', 'th'])
                            markdown_content += "| " + " | ".join(col.text.strip() for col in cols) + " |\n"
                            if row == element.find('tr'):
                                markdown_content += "|" + "|".join("---" for _ in cols) + "|\n"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                    
        except Exception as e:
            print(f"Error saving {url}: {e}")

    async def save_docs(self):
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        tasks = [self.save_page(url) for url in self.gpc_docs_urls]
        await asyncio.gather(*tasks)

    def create_index(self):
        index_file = os.path.join(self.output_dir, "index.md")
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write("# GPC Documentation Index\n\n")
            
            categories = {
                "Core GPC": [],
                "Functions": [],
                "Tutorials": [],
                "Examples": [],
                "Other": []
            }
            
            for url in sorted(self.gpc_docs_urls):
                page_id = url.split('=')[-1]
                if "gpc_" in page_id:
                    categories["Core GPC"].append(url)
                elif "_functions" in page_id:
                    categories["Functions"].append(url)
                elif "tutorial" in page_id:
                    categories["Tutorials"].append(url)
                elif "example" in page_id:
                    categories["Examples"].append(url)
                else:
                    categories["Other"].append(url)
            
            for category, urls in categories.items():
                if urls:
                    f.write(f"## {category}\n\n")
                    for url in urls:
                        page_name = url.split('=')[-1].replace(':', '_')
                        f.write(f"- [{page_name}]({page_name}.md)\n")
                    f.write("\n")

async def main():
    crawler = GPCDocsCrawler()
    print("Starting crawler...")
    
    await crawler.init_session()
    await crawler.crawl("https://www.consoletuner.com/wiki/index.php?id=t2:start")
    print(f"\nFound {len(crawler.gpc_docs_urls)} GPC documentation pages")
    
    print("\nSaving documentation...")
    await crawler.save_docs()
    
    print("\nCreating index...")
    crawler.create_index()
    
    await crawler.close_session()
    print("\nDone!")

if __name__ == "__main__":
    asyncio.run(main())
