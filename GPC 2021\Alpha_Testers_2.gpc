// ALPHA - TESTERS // 
/////////////////////
/////////////////////

// your script must have (Dynamic or Ultimate finishing ).

// your script must have (Timed Finesse Shots ) .

// 1st  step - . 
// search for inside_box_aim() function ... and replace it with the one below 


function INSIDE_BOX_AIM() { 

    if(get_val(PS4_LX) >= 12) set_val(MOVE_X,45);
    else if(get_val(PS4_LX) <= -12) set_val(MOVE_X,-45);

    if(get_val(PS4_LY) >= 12) set_val(MOVE_Y,90);
    else if(get_val( PS4_LY) <= -12) set_val(MOVE_Y,-90);

   
}

// 2nd step - . 

// search for (( combo Timed_Finesse_Finish )) without (()) .

// inside that combo do the following : 

// - Delete CORNER_FIX_MOVE() .
// - Replace any CORNER (); to >>>>>  INSIDE_BOX_AIM();

// now test the script and let me know how was your experiment. thanks for your time .