int Where_am_I; // Variable to store the polar angle value
int previous_direction = 0; // Variable to store the previous direction (0: none, 1: left, 2: right)
define LEFT = 1; // Constant to represent the left direction
define RIGHT = 2; // Constant to represent the right direction

main {

if(get_val(XB1_PR2)){
    // Get the polar angle value of the left joystick and ensure it's within 0-360 degrees
    Where_am_I = (get_ipolar(POLAR_LS, POLAR_ANGLE) + 360) % 360; 

    // Check if the joystick is moved significantly in any direction
    if (abs(get_val(PS4_LX)) > 50 || abs(get_val(PS4_LY)) > 50) {
        // Determine direction based on polar angle
        if (Where_am_I > 90 && Where_am_I < 270) {
            // Current direction is left
            if (previous_direction != LEFT) {
                combo_run(R2_at_direction_change);
                previous_direction = LEFT;
            }
        } else {
            // Current direction is right
            if (previous_direction != RIGHT) {
                combo_run(R2_at_direction_change2);
                previous_direction = RIGHT;
            }
        }
    } else {
        // Joystick is not being significantly used
        previous_direction = 0;
    }

    // Optional: For debugging purposes, you can trace the current direction
    set_val(TRACE_1, previous_direction);
    }
}

combo R2_at_direction_change {
    set_val(PS4_L1, 100);set_val(PS4_R1, 100); // Activate R2 button
    wait(60); // Wait for 60 ms
    set_val(PS4_L1, 0);set_val(PS4_R1, 0); // Release R2 button
    wait(100); // Wait for 100 ms before allowing the next activation
}

combo R2_at_direction_change2 {
    set_polar(POLAR_LS, 180 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(300); // Wait for 60 ms
    //set_polar(POLAR_LS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(60); // Wait for 100 ms before allowing the next activation
}q