import os
import re

def clean_gpc_files(directory):
    # Counter for processed files
    cleaned_count = 0
    error_count = 0
    
    print(f"Starting to clean GPC files in: {directory}")
    
    # Walk through all files in directory and subdirectories
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.gpc'):
                file_path = os.path.join(root, file)
                try:
                    # Read the file content
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # Store original length for comparison
                    original_length = len(content)
                    
                    # More thorough cleaning patterns
                    cleaned_content = content
                    # Remove invalid characters after closing braces
                    cleaned_content = re.sub(r'}[\s]*[�]+', '}', cleaned_content)
                    # Remove invalid characters at the end of the file
                    cleaned_content = re.sub(r'[\s]*[�]+[\s]*$', '', cleaned_content)
                    # Remove any remaining invalid characters
                    cleaned_content = re.sub(r'[�]+', '', cleaned_content)
                    # Remove multiple newlines at the end of file
                    cleaned_content = re.sub(r'\n+$', '\n', cleaned_content)
                    
                    # Only write if content was actually changed
                    if len(cleaned_content) != original_length:
                        with open(file_path, 'w', encoding='utf-8', newline='\n') as f:
                            f.write(cleaned_content)
                        print(f"✓ Cleaned: {file_path}")
                        cleaned_count += 1
                    
                except Exception as e:
                    print(f"✗ Error processing {file_path}: {str(e)}")
                    error_count += 1

    # Print summary
    print("\nCleaning completed!")
    print(f"Files cleaned: {cleaned_count}")
    print(f"Errors encountered: {error_count}")

if __name__ == "__main__":
    # Replace this path with your GPC scripts folder path
    directory = r"C:\Users\<USER>\OneDrive\Dokumente\ZEN GPS Scripts"
    clean_gpc_files(directory)
