define Angle_Time = 19;
define Angle_Speed_Multipler = 2;
int final_angle;
int L_direction;
int radius;
int cur_angle;
int angle_timer;
int angle_out;
int check_manual = 0;
define HOLD_TIME = 8000;  // define the hold time in milliseconds
int hold_timer; 
int i_x;
define SIZE_OF_ARRAY = 4;
function aim_box() {
  vm_tctrl(0);
  
  if (check_manual == 0) {
    if (get_ival(ShotBtn) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500 ) {
            cur_angle = atan2_angle(get_ival(POLAR_LX), get_ival(POLAR_LY));
      final_angle = Final_Angles[cur_angle / 90];
      L_direction = (((final_angle - cur_angle + 540) % 360 >= 180) * 2 - 1) * Angle_Speed_Multipler;
      radius = min(isqrt(pow(get_ival(POLAR_LX), 2) + pow(get_ival(POLAR_LY), 2)), 32767);
      hold_timer = get_rtime() + HOLD_TIME;  // reset the hold_timer each time you aim
    }

    if (abs(cur_angle - final_angle) >= Angle_Speed_Multipler) {
      angle_timer += get_rtime();
      if (angle_timer >= Angle_Time) {
                cur_angle += L_direction;
        if (cur_angle < 0)
          cur_angle += 360;
        if (cur_angle >= 360)
          cur_angle -= 360;
        angle_timer = 0;
      }
      if (get_rtime() < hold_timer) {  // only set polar if hold_timer is not expired
        set_polar(POLAR_LS, cur_angle, radius);
        
      }

    }
    
  }
        for (i_x = 0 ;i_x < SIZE_OF_ARRAY ; i_x++) {
    if (final_angle == Final_Angles[i_x]) {
      combo_run(hold_my_aim);
      break;  // exit loop once combo started
    }
    }
}
combo hold_my_aim {
set_polar(POLAR_LS, cur_angle, radius);
wait(2000);
}


function atan2_angle(f_x, f_y) {
  if (f_x >= 0 && f_y > 0)
    angle_out = -90000;
  else if (f_x < 0 && f_y >= 0)
    angle_out = 90000;
  else if (f_x <= 0 && f_y < 0)
    angle_out = -270000;
  else angle_out = 270000;
  f_x = abs(f_x);
  f_y = abs(f_y);
  if (f_x < f_y)
    angle_out += (f_x * 45000 / f_y);
  else if (f_x > f_y)
    angle_out += 90000 - (f_y * 45000 / f_x);
  else angle_out += 45000;
  return abs(angle_out) / 1000;
} 