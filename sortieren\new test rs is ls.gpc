define Stick_Press_LB_Threshold = 95;

int stick_swap_toggle;
int rightStickMagnitude; // Declare rightStickMagnitude at the top

main {

    if(get_ival(XB1_RS)) {
    set_val(XB1_RS, 0);
technicaldribbler()

    }



}

function technicaldribbler() {
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
            // Copy right stick values to left stick
            set_val(POLAR_LX, get_val(POLAR_RX));
            set_val(POLAR_LY, get_val(POLAR_RY));
            // Zero out the right stick input
            set_val(XB1_RX, 0);
            set_val(XB1_RY, 0);
        }
        if(rightStickMagnitude >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
            combo_run(Press_LB);
}

combo Press_LB {
    set_val(XB1_LB, 100);
    wait(100);
    wait(20);
}
