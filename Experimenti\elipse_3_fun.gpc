// Constants for stick identification
define stickX = XB1_LX;
define stickY = XB1_LY;

// Ellipse ratios
define X_RATIO = 100;
define Y_RATIO = 40;

// Auto-rotation settings
define ROTATION_SPEED = 80;
define ROTATION_RADIUS = 100;

int x, y;
int last_x = 0;    // Remember last stick position X
int last_y = 0;    // Remember last stick position Y
int last_step = 0; // Remember last direction step
int rotation_step = 0;
int rotation_counter = 0;
int cos_a;
int sin_a;
int rx;
int ry;

// Add zone-based rotation matrix components
const int8 cos_table[] = {100, 71, 0, -71, -100, -71, 0, 71};
const int8 sin_table[] = {0, 71, 100, 71, 0, -71, -100, -71};

// Function to determine which zone (0-7) the stick position falls into
function get_zone(int x, int y) {
    if (abs(x) > abs(y) * 2) {
        if (x > 0) return 0;  // Right
        return 4;             // Left
    } 
    else if (abs(y) > abs(x) * 2) {
        if (y > 0) return 2;  // Down
        return 6;             // Up
    }
    else {
        if (x > 0 && y > 0) return 1;      // Down-Right
        if (x < 0 && y > 0) return 3;      // Down-Left
        if (x < 0 && y < 0) return 5;      // Up-Left
        if (x > 0 && y < 0) return 7;      // Up-Right
        return 0;  // Default to right for any other case (including center)
    }
    return 0;  // Fallback return for compiler
}
int zone;
// Add function prototype at the top, after the variable declarations

function process_stick_movement() {
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Always use elliptical scaling when stick is moved
    if (abs(x) > 20 || abs(y) > 20) {
        zone = get_zone(x, y);
        cos_a = cos_table[zone];
        sin_a = sin_table[zone];
        
        // Original scaling calculations
        rx = (x * cos_a + y * sin_a) / 100;
        ry = (-x * sin_a + y * cos_a) / 100;
        rx = (rx * X_RATIO) / 100;
        ry = (ry * Y_RATIO) / 100;
        x = (rx * cos_a - ry * sin_a) / 100;
        y = (rx * sin_a + ry * cos_a) / 100;
        
        // Store last valid position
        last_step = zone;
        last_x = x;
        last_y = y;
    }
    // Auto-rotation when stick is centered
    else {
        rotation_step = last_step;
        // Auto-rotation position calculations
        if (rotation_step == 0) {
            x = ROTATION_RADIUS;
            y = 0;
        } else if (rotation_step == 1) {
            x = ROTATION_RADIUS * 71 / 100;
            y = ROTATION_RADIUS * 71 / 100;
        } else if (rotation_step == 2) {
            x = 0;
            y = ROTATION_RADIUS;
        } else if (rotation_step == 3) {
            x = -ROTATION_RADIUS * 71 / 100;
            y = ROTATION_RADIUS * 71 / 100;
        } else if (rotation_step == 4) {
            x = -ROTATION_RADIUS;
            y = 0;
        } else if (rotation_step == 5) {
            x = -ROTATION_RADIUS * 71 / 100;
            y = -ROTATION_RADIUS * 71 / 100;
        } else if (rotation_step == 6) {
            x = 0;
            y = -ROTATION_RADIUS;
        } else if (rotation_step == 7) {
            x = ROTATION_RADIUS * 71 / 100;
            y = -ROTATION_RADIUS * 71 / 100;
        }
        
        // Rotation counter update
        rotation_counter += ROTATION_SPEED;
        if (rotation_counter >= 100) {
            rotation_counter -= 100;
            rotation_step = (rotation_step + 1) % 8;
        }
    }
    
    set_val(stickX, x);
    set_val(stickY, y);
}

main {
    process_stick_movement();  // Semicolon added
} 