																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																
																💻 most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																💻 My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																
																Your contributions have been invaluable, and I am truly grateful for your support."
																
																
																
																
															⚽💻 "IF YOU'RE INTERESTED IN DIVING INTO FIFA/FC GPC CODING IN DEPTH,  CHECK "FIFA CODE ACADEMY" there." ⚽💻 : https://discord.gg/CpcFqAvP 
																*/







































































 int DA217[0];
 init {
 	DA116();
 	combo_run(DA1);
 	combo_run(DA2);
 	combo_run(DA3);
 	combo_run(DA4);
 	combo_run(DA5);
 	combo_run(DA6);
 	combo_run(DA7);
 	combo_run(DA8);
 	combo_run(DA9);
 	combo_run(DA10);
 	combo_run(DA11);
 	combo_run(DA12);
 	combo_run(DA13);
 	combo_run(DA14);
 	combo_run(DA15);
 	combo_run(DA16);
 	combo_run(DA17);
 	combo_run(DA18);
 	combo_run(DA19);
 	combo_run(DA20);
 	combo_run(DA21);
 	combo_run(DA22);
 	combo_run(DA23);
 	combo_run(DA24);
 	combo_run(DA25);
 	combo_run(DA26);
 	combo_run(DA27);
 	combo_run(DA28);
 	combo_run(DA29);
 	combo_run(DA30);
 	combo_run(DA31);
 	combo_run(DA32);
 	combo_run(DA33);
 	combo_run(DA34);
 	combo_run(DA35);
 	combo_run(DA36);
 	combo_run(DA37);
 	combo_run(DA38);
 	combo_run(DA39);
 	combo_run(DA40);
 	combo_run(DA41);
 	combo_run(DA42);
 	combo_run(DA43);
 	combo_run(DA44);
 	combo_run(DA45);
 	combo_run(DA46);
 	combo_run(DA47);
 	combo_run(DA48);
 	combo_run(DA49);
 	combo_run(DA50);
 	combo_run(DA51);
 	combo_run(DA52);
 	combo_run(DA53);
 	combo_run(DA54);
 	combo_run(DA55);
 	combo_run(DA56);
 	combo_run(DA57);
 	combo_run(DA58);
 	combo_run(DA59);
 	combo_run(DA60);
 	combo_run(DA61);
 	combo_run(DA62);
 	combo_run(DA63);
 	combo_run(DA64);
 	combo_run(DA65);
 	combo_run(DA66);
 	combo_run(DA67);
 	combo_run(DA68);
 	combo_run(DA69);
 	combo_run(DA70);
 	combo_stop(DA1);
 	combo_stop(DA2);
 	combo_stop(DA3);
 	combo_stop(DA4);
 	combo_stop(DA5);
 	combo_stop(DA6);
 	combo_stop(DA7);
 	combo_stop(DA8);
 	combo_stop(DA9);
 	combo_stop(DA10);
 	combo_stop(DA11);
 	combo_stop(DA12);
 	combo_stop(DA13);
 	combo_stop(DA14);
 	combo_stop(DA15);
 	combo_stop(DA16);
 	combo_stop(DA17);
 	combo_stop(DA18);
 	combo_stop(DA19);
 	combo_stop(DA20);
 	combo_stop(DA21);
 	combo_stop(DA22);
 	combo_stop(DA23);
 	combo_stop(DA24);
 	combo_stop(DA25);
 	combo_stop(DA26);
 	combo_stop(DA27);
 	combo_stop(DA28);
 	combo_stop(DA29);
 	combo_stop(DA30);
 	combo_stop(DA31);
 	combo_stop(DA32);
 	combo_stop(DA33);
 	combo_stop(DA34);
 	combo_stop(DA35);
 	combo_stop(DA36);
 	combo_stop(DA37);
 	combo_stop(DA38);
 	combo_stop(DA39);
 	combo_stop(DA40);
 	combo_stop(DA41);
 	combo_stop(DA42);
 	combo_stop(DA43);
 	combo_stop(DA44);
 	combo_stop(DA45);
 	combo_stop(DA46);
 	combo_stop(DA47);
 	combo_stop(DA48);
 	combo_stop(DA49);
 	combo_stop(DA50);
 	combo_stop(DA51);
 	combo_stop(DA52);
 	combo_stop(DA53);
 	combo_stop(DA54);
 	combo_stop(DA55);
 	combo_stop(DA56);
 	combo_stop(DA57);
 	combo_stop(DA58);
 	combo_stop(DA59);
 	combo_stop(DA60);
 	combo_stop(DA61);
 	combo_stop(DA62);
 	combo_stop(DA63);
 	combo_stop(DA64);
 	combo_stop(DA65);
 	combo_stop(DA66);
 	combo_stop(DA67);
 	combo_stop(DA68);
 	combo_stop(DA69);
 	combo_stop(DA70);
 	combo_run(DA109);
 }
 int DA277 ;
 int DA278;
 int DA279;
 int DA280;
 int DA281;
 define DA282 = 0;
 define DA283 = 1;
 define DA284 = 2;
 define DA285 = 3;
 define DA286 = 4;
 define DA287 = 5;
 define DA288 = 6;
 define DA289 = 7;
 define DA290 = 8;
 define DA291 = 9;
 define DA292 = 10;
 define DA293 = 11;
 define DA294 = 12;
 define DA295 = 13;
 define DA296 = 14;
 define DA297 = 15;
 define DA298 = 16;
 define DA299 = 17;
 define DA300 = 18;
 define DA301 = 19;
 define DA302 = 20;
 define DA303 = 21;
 define DA304 = 22;
 define DA23 = 23;
 define DA306 = 24;
 define DA307 = 25;
 define DA308 = 26;
 define DA309 = 27;
 define DA310 = 28;
 define DA311 = 29;
 define DA312 = 30;
 define DA313 = 31;
 define DA314 = 32;
 define DA315 = 33;
 define DA316 = 34;
 define DA317 = 35;
 define DA318 = 36;
 define DA319 = 37;
 define DA320 = 38;
 define DA321 = 39;
 define DA322 = 40;
 define DA323 = 41;
 define DA324 = 42;
 define DA325 = 43;
 define DA326 = 44;
 define DA327 = 45;
 define DA328 = 46;
 define DA329 = 47;
 define DA330 = 48;
 define DA331 = 49;
 define DA332 = 50;
 define DA333 = 51;
 define DA334 = 52;
 define DA335 = 53;
 define DA336 = 54;
 define DA337 = 55;
 define DA338 = 56;
 define DA339 = 57;
 define DA340 = 58;
 define DA341 = 59;
 define DA342 = 60;
 define DA343 = 61;
 define DA344 = 62;
 define DA345 = 63;
 define DA346 = 64;
 define DA347 = 65;
 define DA348 = 66;
 define DA349 = 67;
 define DA350 = 68;
 define DA351 = 69;
 define DA352 = 70;
 define DA353 = 0;
 function DA110(DA111) {
 	if (DA111 == 0) vm_tctrl(-0);
 	else if (DA111 == 1) vm_tctrl(2);
 	else if (DA111 == 2) vm_tctrl(-2);
 	else if (DA111 == 3) vm_tctrl(-4);
 	else if (DA111 == 4) vm_tctrl(-6);
 	else if (DA111 == 5) vm_tctrl(-8);
 	else if (DA111 == 6) vm_tctrl(-9);
 }
 int DA354, DA355;
 int DA356, DA357;
 int DA358 = FALSE, DA359;
 int DA360 = TRUE;
 int DA361;
 const string DA827[] = {
 	"Off",  "On" }
 ;
 int DA362;
 int DA363;
 int DA364;
 int DA365;
 int DA366;
 int DA367;
 int DA368;
 int DA369;
 int DA370;
 int DA371;
 int DA372;
 int DA373;
 int DA374;
 int DA375;
 int DA376;
 int DA377;
 int DA378;
 int DA379;
 int DA380;
 int DA381;
 int DA111;
 int DA383;
 int DA384 ;
 int DA385 ;
 int DA386 ;
 define DA387 = 24;
 int DA388;
 int DA389;
 int DA390;
 int DA391;
 int DA392;
 int DA393;
 int DA394;
 int DA395;
 int DA396;
 int DA397 ;
 int DA398 ;
 int DA399 ;
 int DA400 ;
 int DA401 ;
 int DA402 ;
 int DA403 ;
 int DA404 ;
 int DA405 ;
 int DA406 ;
 int DA407 ;
 int DA408;
 int DA409;
 int DA410;
 int DA411;
 int DA412;
 int DA413;
 int DA414;
 int DA415;
 int DA416;
 int DA417;
 int DA418;
 int DA419;
 int DA420;
 int DA421;
 int DA422;
 int DA423;
 int DA424;
 int DA425;
 int DA426;
 int DA427;
 int DA428;
 int DA429;
 int DA430;
 int DA431;
 int DA432;
 int DA433;
 int DA434;
 int DA435;
 int DA436;
 int DA437;
 int DA438;
 int DA439;
 int DA440;
 int DA441 ;
 int DA442 ;
 int DA443 ;
 int DA444;
 int DA445 ;
 int DA446 ;
 int DA447 ;
 int DA448;
 int DA449 ;
 int DA450 ;
 int DA451 ;
 int DA452;
 int DA453 ;
 int DA454 ;
 int DA455 ;
 int DA456;
 int DA457;
 int DA458;
 int DA459;
 int DA460;
 int DA461;
 const int16 DA833[][] = {
 {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 22, 1, 10, 13 	}
 	,   {
 		0, 70, 1, 10, 2 	}
 	,    {
 		0, 70, 1, 10, 3 	}
 	,    {
 		0, 70, 1, 10, 4 	}
 	,    {
 		0, 70, 1, 10, 5 	}
 	,    {
 		0, 22, 1, 10, 13 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,    {
 		1, 25, 1, 10, 6 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		0, 1, 1, 10, 20 	}
 	,     {
 		1, 25, 1, 10, 8 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		0, 1, 1, 10, 20 	}
 	,     {
 		0, 25, 1, 10, 7 	}
 	,     {
 		0, 1, 1, 10, 21 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		1, 25, 1, 10, 9 	}
 	,     {
 		0, 1, 1, 10, 28 	}
 	,     {
 		0, 1, 1, 10, 29 	}
 	,     {
 		1, 800, 1, 10, 0 	}
 	,    {
 		1, 800, 1, 10, 0 	}
 	,    {
 		0, 22, 1, 10, 13 	}
 	,    {
 		0, 1, 1, 10, 33 	}
 	,     {
 		-100, 300, 1, 10, 1 	}
 	,  {
 		-150, 150, 10, 10, 0 	}
 	, {
 		-150, 150, 10, 10, 0 	}
 	, {
 		0, 1, 1, 10, 37 	}
 	,      {
 		-150, 150, 10, 10, 0 	}
 	, {
 		0, 22, 1, 10, 49 	}
 	,     {
 		0, 22, 1, 10, 50 	}
 	,     {
 		0, 22, 1, 10, 51 	}
 	,     {
 		0, 22, 1, 10, 52 	}
 	,     {
 		0, 1, 1, 10, 53 	}
 	,      {
 		0, 1, 1, 10, 54 	}
 	,      {
 		0, 1, 1, 10, 1 	}
 	,       {
 		0, 1, 1, 10, 1 	}
 	,       {
 		60, 500, 5, 10, 0 	}
 	,    {
 		60, 500, 5, 10, 0 	}
 	,    {
 		0, 1, 1, 10, 1 	}
 	,       {
 		0, 1, 1, 10, 1 	}
 	,       {
 		50, 250, 5, 10, 0 	}
 	,    {
 		100, 850, 5, 10, 0 	}
 	,   {
 		0, 1, 1, 10, 1 	}
 	,       {
 		0,      1,      1,     10,     1   	}
 	,  {
 		80,    500,      5,     10,     1   	}
 	,  {
 		80,    500,      5,     10,     1   	}
 	,  {
 		0,      1,      1,     10,     1   	}
 	,  {
 		0,2500,25,10,0 	}
 	,   {
 		0, 1, 1, 10, 1 	}
 	,      {
 		0, 1, 1, 10, 1 	}
 	,      {
 		0, 1, 1, 10, 1 	}
 }
 ;
 const int16 DA580[][] = {
 {
 		0, 7, 1 	}
 	,   	    {
 		8,   16, 1 	}
 	,   	    {
 		17,  21, 1 	}
 	,   	    {
 		68,68,1 	}
 	,       	    {
 		69,70,1 	}
 	,       	    {
 		22, 26, 1 	}
 	,   	    {
 		27, 29, 1 	}
 	,   	    {
 		30, 32, 1 	}
 	,   	    {
 		33, 35, 1 	}
 	,   	    {
 		36, 38, 1 	}
 	,   	    {
 		39, 39, 1 	}
 	,   	    {
 		40, 40, 1 	}
 	,   	    {
 		41, 42, 1 	}
 	,   	    {
 		43, 43, 1 	}
 	,   	    {
 		0,  0, 0 	}
 	,   	    {
 		54, 55, 1 	}
 	,   	    {
 		44, 47, 1 	}
 	,   {
 		48, 51, 1 	}
 	,   {
 		52, 53, 1 	}
 	,   {
 		0, 0, 0 	}
 	,    {
 		0, 0, 0 	}
 	,    {
 		67, 67, 1 	}
 	,    {
 		56, 59, 1 	}
 	,   {
 		60, 63, 1 	}
 	,   {
 		64, 66, 1 	}
 }
 ;
 const uint8 DA805[] = {
 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1,   	    1,  	1,  	1   }
 ;
 const string DA590[] = {
 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" }
 ;
 const string DA589[] = {
 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","Double Tap GrounP","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" }
 ;
 const string DA809 [] = {
 	"Classic","Alternative","Custom", ""  }
 ;
 const string DA908 [] = {
 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  }
 ;
 const string DA825[] = {
 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" }
 ;
 const string DA811[] = {
 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" }
 ;
 const string DA878[] = {
 	"Right",  "Left",  "" }
 ;
 const string DA876[] = {
 	"One Tap",  "Double Tap",  "" }
 ;
 const string DA815[] = {
 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" }
 ;
 const string DA817[] = {
 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"Feint && Exit",  	"Feint & Exit",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Ball Roll Chop",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel to Ball",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"Spin Move L/R",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Drag Back Som.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" }
 ;
 const string DA852[] = {
 	"OFF",  "PS5_PS",  "PS5_SHARE",  "PS5_OPTIONS",  "PS5_R1",  "PS5_R2",  "PS5_R3",  "PS5_L1",  "PS5_L2",  "PS5_L3",  "PS5_UP",  "PS5_DOWN",  "PS5_LEFT",  "PS5_RIGHT",  "PS5_TRIANGLE",  "PS5_CIRCLE",  "PS5_CROSS",  "PS5_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS5_TOUCH",  "" }
 ;
 int DA462 = -1;
 int DA463 = -1;
 int DA464 = -1;
 int DA465 = -1;
 int DA466 = -1;
 int DA467;
 int DA468;
 int DA469;
 int DA470;
 int DA471;
 const uint8 DA1355[] = {
 	4,4,4, 4,4,4, 4,4,4,4 }
 ;
 const uint8 DA1356[] = {
 	41, 15, 4, 41, 15, 41, 41, 41, 4, 15,29 }
 ;
 const uint8 DA1357[] = {
 	15, 4, 41, 41, 15, 41, 41, 4, 41, 15,29  }
 ;
 const uint8 DA1358[] = {
 	15,29,70,41,70,15,29,70,41,15,29  }
 ;
 const uint8 DA1359[] = {
 	29,41,15,29,70,15,70,41,70,15 ,29  }
 ;
 const uint8 DA1360[] = {
 	29, 44, 44, 44, 29, 44, 44, 44,44,44,29 }
 ;
 const uint8 DA1361[] = {
 	29, 44, 44, 44, 29, 44, 44, 44,44,44 ,29  }
 ;
 const uint8 DA1362[] = {
 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21 }
 ;
 const uint8 DA1363[] = {
 	4,4,4, 4,4,4, 4,4,4,4 }
 ;
 const uint8 DA1364[] = {
 	9, 33, 15, 15, 9, 4, 9, 34, 78, 41, 9,29 }
 ;
 const uint8 DA1365[] = {
 	33, 10, 7, 15, 10, 78, 4, 41, 15, 7, 34,29  }
 ;
 const uint8 DA1366[] = {
 	41, 9, 9, 15, 9, 41, 15, 41, 33, 41, 9,29  }
 ;
 const uint8 DA1367[] = {
 	7, 15, 10, 7, 10, 33, 41, 7, 15, 41, 10 ,29 }
 ;
 const uint8 DA1368[] = {
 	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29 }
 ;
 const uint8 DA1369[] = {
 	41, 9, 24, 48, 45, 9, 24, 44,41,33 ,33 ,29 }
 ;
 const uint8 DA1370[] = {
 	51, 63, 47, 63, 24, 51, 44, 63, 48, 12  }
 ;
 function DA112(DA113) {
 	if (DA113 >= 9) {
 		DA472 = -1;
 			}
 	else if (DA113 <= 0) {
 		DA472 = 1;
 			}
 	DA113 += DA472;
 	return DA113;
 	}
 function DA114() {
 	vm_tctrl(0);
 	if(DA29 && DA365){
 		if(DA556 < 1000){
 			DA474 = 10;
 			DA500   = 10;
 			DA498  = 10;
 					}
 			}
 	if(DA477 && DA366){
 		DA475 = FALSE;
 		if(DA556 < 1000){
 			DA474 = 11;
 			DA500   = 11;
 			DA498  = 11;
 					}
 			}
 	if(DA475 && DA366){
 		DA477 = FALSE;
 		if(DA556 < 1000){
 			DA474 = 10;
 			DA500   = 10;
 			DA498  = 10;
 					}
 			}
 	if(DA556 >= 1000){
 		DA479 = DA112(DA479);
 		DA497 = DA112(DA497);
 		DA498 = DA112(DA498);
 		DA474 = DA112(DA474);
 		DA500 = DA112(DA500);
 			}
 	if(DA365){
 		if(DA503 == DA596){
 			DA480 = !DA480;
 			if(DA1355[DA479]) DA242(DA1355[DA479]);
 					}
 		if(DA503 == DA247 (DA596 + 4)){
 			DA480 = FALSE;
 			if(DA1362[DA497]) DA242(DA1362[DA497]);
 					}
 		if(DA503 == DA247 (DA596 + 1) ){
 			DA480 = TRUE;
 			if(DA1357[DA474]) DA242(DA1357[DA474]);
 					}
 		if(DA503 == DA247 (DA596 - 1) ){
 			DA480 = FALSE;
 			if(DA1356[DA474]) DA242(DA1356[DA474]);
 					}
 		if(DA503 == DA247 (DA596 + 2) ){
 			DA480 = TRUE;
 			if(DA1359[DA500]) DA242(DA1359[DA500]);
 					}
 		if(DA503 == DA247 (DA596 - 2) ){
 			DA480 = FALSE;
 			if(DA1358[DA500]) DA242(DA1358[DA500]);
 					}
 		if(DA503 == DA247 (DA596 + 3) ){
 			DA480 = TRUE;
 			if(DA1360[DA498]) DA242(DA1360[DA498]);
 					}
 		if(DA503 == DA247 (DA596 - 3) ){
 			DA480 = FALSE;
 			if(DA1361[DA498]) DA242(DA1361[DA498]);
 					}
 			}
 	if(DA366){
 		if(DA503 == DA596){
 			DA480 = !DA480;
 			if(DA1363[DA479]) DA242(DA1363[DA479]);
 					}
 		if(DA503 == DA247 (DA596 + 4)){
 			DA480 = FALSE;
 			if(DA1370[DA497]) DA242(DA1370[DA497]);
 					}
 		if(DA503 == DA247 (DA596 + 1) ){
 			DA480 = TRUE;
 			if(DA1365[DA474]) DA242(DA1365[DA474]);
 					}
 		if(DA503 == DA247 (DA596 - 1) ){
 			DA480 = FALSE;
 			if(DA1364[DA474]) DA242(DA1364[DA474]);
 					}
 		if(DA503 == DA247 (DA596 + 2) ){
 			DA480 = TRUE;
 			if(DA1367[DA500]) DA242(DA1367[DA500]);
 					}
 		if(DA503 == DA247 (DA596 - 2) ){
 			DA480 = FALSE;
 			if(DA1366[DA500]) DA242(DA1366[DA500]);
 					}
 		if(DA503 == DA247 (DA596 + 3) ){
 			DA480 = TRUE;
 			if(DA1368[DA498]) DA242(DA1368[DA498]);
 					}
 		if(DA503 == DA247 (DA596 - 3) ){
 			DA480 = FALSE;
 			if(DA1369[DA498]) DA242(DA1369[DA498]);
 					}
 			}
 	DA467 = DA479;
 	DA468 = DA497;
 	DA469 = DA498;
 	DA470 = DA474;
 	DA471 = DA500;
 }
 int DA479;
 int DA497;
 int DA498;
 int DA474;
 int DA500;
 function DA115() {
 	if(DA1153){
 		DA501 += get_rtime();
 			}
 	if(DA501 >= 3000){
 		DA501 = 0;
 		DA1153 = FALSE;
 			}
 	if (!get_ival(XB1_RS) && !get_ival(DA453) && !get_ival(DA454) && !get_ival(DA452) && !get_ival(DA451)) {
 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA504 && !combo_running(DA0)) {
 			DA503 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
 			DA504 = TRUE;
 			DA1153 = TRUE;
 			DA501 = 0;
 			vm_tctrl(0);
 			DA114();
 					}
 		set_val(DA1138, 0);
 		set_val(DA1139, 0);
 			}
 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
 		DA504 = FALSE;
 			}
 	}
 function DA116() {
 	DA187();
 	if (DA388 == 0 && DA389 == 0 && DA390 == 0 && DA391 == 0 && DA392 == 0 && DA393 == 0 && DA394 == 0 && DA395 == 0) {
 		DA388 = 4;
 		DA389 = 15;
 		DA390 = 66;
 		DA391 = 48;
 		DA392 = 15;
 		DA393 = 29;
 		DA394 = 29;
 		DA395 = 44;
 			}
 	DA947 = get_slot();
 	}
 int DA472 = 1;
 int DA508;
 int DA509;
 int DA510 = TRUE;
 int DA511[6];
 int DA512;
 int DA513;
 int DA514;
 int DA515;
 function DA117(DA118, DA119, DA120) {
 	DA120 = (DA120 * 14142) / 46340;
 	if (DA119 <= 0) {
 		set_polar2(DA118, (DA119 = (abs(DA119) + 360) % 360), min(DA120, DA519[DA119 % 90]));
 		return;
 			}
 	set_polar2(DA118, inv(DA119 % 360), min(DA120, DA519[DA119 % 90]));
 	}
 function DA121(DA118, DA123) {
 	if (DA123) return (360 - get_polar(DA118, POLAR_ANGLE)) % 360;
 	return isqrt(~(pow(get_val(42 + DA118), 2) + pow(get_val(43 + DA118), 2))) + 1;
 	}
 function DA124(DA118,DA123) {
 	if (DA123) return (360 - get_ipolar(DA118, POLAR_ANGLE)) % 360;
 	return isqrt(~(pow(get_ival(42 + DA118), 2) + pow(get_ival(43 + DA118), 2))) + 1;
 	}
 const int16 DA519[] = {
 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  }
 ;
 int block = FALSE;
 int DA523 = 1;
 combo DA0{
 	set_polar(POLAR_RS,0,0);
 	vm_tctrl(0);
 	wait(100);
 	vm_tctrl(0);
 	wait(300);
 	}
 main{
 			if(DA383){
 				DA273();
 							}
 	if(!DA509){
 		DA509 = TRUE;
 		DA508 = random(0x2B67, 0x1869F);
 		set_pvar(SPVAR_1,DA509);
 		set_pvar(SPVAR_3,DA508);
 		DA510 = TRUE;
 			}
 	if(!DA515){
 		vm_tctrl(0x0);
 		if(event_press(PS5_LEFT)){
 			DA514 = DA129(DA514 + 0x1 ,0x0,0x5)DA510 = TRUE 		}
 		if(event_press(PS5_RIGHT)){
 			DA514 = DA129(DA514 - 0x1 ,0x0,0x5)DA510 = TRUE 		}
 		if(event_press(PS5_UP)){
 			DA511[DA514]  = DA129(DA511[DA514] + 0x1 ,0x0,0x9)DA510 = TRUE 		}
 		if(event_press(PS5_DOWN)){
 			DA511[DA514]  = DA129(DA511[DA514] - 0x1 ,0x0,0x9)DA510 = TRUE 		}
 if(event_press(PS5_CROSS)){
    DA515 = TRUE; // Bypassing the validation and directly setting DA515 to TRUE
    set_pvar(SPVAR_2,DA515);
    DA510 = TRUE;
}			
 /*		if(event_press(PS5_CROSS)){
 			DA512 = 0x0;
 			for(DA513 = 0x5;
 			DA513 >= 0x0;
 			DA513--){
 				DA512 += DA511[DA513] * pow(0xA,DA513) 			}
 			if(DA512 == DA127(DA508)){
 				DA515 = TRUE;
 				set_pvar(SPVAR_2,DA515)  			}
 			DA510 = TRUE;
 					}
 */
 			}
 	if(DA510){
 		cls_oled(0x0)if(!DA515){
 			DA133(DA508,DA538,0xA,OLED_FONT_MEDIUM,OLED_WHITE,DA539)for( DA513 = 0x0;
 			DA513 < 0x6;
 			DA513++){
 				DA133(DA511[DA513],0x55 - (DA513 * 0xA),0x28,OLED_FONT_MEDIUM,!(DA513 == DA514),DA539) 			}
 					}
 		DA510 = FALSE;
 			}
 	if(DA515){
 		if (get_ival(DA449) || get_ival(DA453) || get_ival(DA451) || get_ival(DA452) || DA358 || combo_running(DA72) || get_info(CPU_USAGE) > 0x5F ) {
 			vm_tctrl(0x0);
 					}
 		else{
 			DA110(DA111);
 					}
 		if(get_ival(DA454) > 0x28 || (!get_ival(DA451) && !get_ival(DA452))){
 			if(get_ival(DA449)){
 				vm_tctrl(0x0);
 				if(get_ptime(DA449) > DA611){
 					set_val(DA449,0x0);
 									}
 							}
 					}
 		if(!get_ival(DA451)){
 			if(get_ival(DA449)){
 				vm_tctrl(0x0);
 				if(get_ptime(DA449) > DA611){
 					set_val(DA449,0x0);
 									}
 							}
 					}
 		if (DA358) {
 			vm_tctrl(0x0);
 			if(DA359 < 0x1F72){
 				DA359 += get_rtime();
 							}
 			if (DA359 >= 0x1F40) {
 				cls_oled(OLED_BLACK);
 				DA359 = 0x0;
 				DA358 = FALSE;
 							}
 					}
 		if (block) {
 			if (DA523 < 0x136) {
 				DA523 += get_rtime();
 							}
 			if (DA523 <= 0x12C ) {
 				DA184();
 							}
 			if (DA523 > 0x12C ) {
 				block = FALSE;
 				DA523 = 0x1;
 				DA1075 = TRUE;
 							}
 			if (DA523 < 0x0) {
 				DA523 = 0x1;
 							}
 			if (DA523 <= 0x64) {
 				combo_stop(DA89);
 				combo_stop(DA98);
 				combo_stop(DA90);
 				combo_stop(DA99);
 				combo_stop(DA96);
 				combo_stop(DA97);
 				combo_stop(DA93);
 				combo_stop(DA95);
 				combo_stop(DA92);
 				combo_stop(DA88);
 				combo_stop(DA86);
 				combo_stop(DA91);
 				combo_stop(DA106);
 				combo_stop(DA108);
 				combo_stop(DA102);
 				combo_stop(DA107);
 				combo_stop(DA101);
 							}
 					}
 		if((get_ival(PS5_L2) && event_press(PS5_R2) || event_press(PS5_L2) && get_ival(PS5_R2) )){
 			block = TRUE;
 					}
 		if(DA439){
 			DA440 = FALSE;
 					}
 		if(DA440){
 			DA439 = FALSE;
 					}
 		if(DA363){
 			DA364 = FALSE;
 			DA365 = FALSE;
 			DA366 = FALSE;
 					}
 		if(DA364){
 			DA363 = FALSE;
 			DA365 = FALSE;
 			DA366 = FALSE;
 					}
 		if(DA365){
 			DA363 = FALSE;
 			DA364 = FALSE;
 			DA366 = FALSE;
 					}
 		if(DA366){
 			DA363 = FALSE;
 			DA364 = FALSE;
 			DA365 = FALSE;
 					}
 		if (get_ival(PS5_L2)) {
 			if (get_ival(PS5_LEFT)) {
 				set_val(PS5_LEFT, 0x0);
 				DA1187 = -1 			}
 			else if (get_ival(PS5_RIGHT)) {
 				set_val(PS5_RIGHT, 0x0);
 				DA1187 = 0x1 			}
 					}
 		if (get_ival(DA454)) {
 			if ((abs(get_ival(DA1138)) > 0x55 || abs(get_ival(DA1139)) > 0x55) && !get_ival(PS5_R3)) {
 				vm_tctrl(0x0);
 				combo_run(DA83);
 							}
 					}
 		if (get_ival(PS5_L2)) {
 			set_val(PS5_SHARE, 0x0);
 			if (event_press(PS5_SHARE)) {
 				vm_tctrl(0x0);
 				DA1080 = !DA1080;
 				DA244(DA1302);
 				DA218(DA1080, sizeof(DA554) - 0x1, DA554[0x0]);
 				DA358 = TRUE;
 							}
 					}
 		if (DA1080) {
 			if (DA381) {
 				DA272();
 							}
 			if (event_release(DA454)) {
 				DA556 = 0x1;
 							}
 			if (DA556 < 0x1F40) {
 				DA556 += get_rtime();
 							}
 			if (get_ival(PS5_R2)) {
 				if (event_press(PS5_OPTIONS)) {
 					DA558 = !DA558;
 					DA244(DA558);
 									}
 				set_val(PS5_OPTIONS, 0x0);
 							}
 			if (DA558) {
 				if (DA558) DA237(DA1112);
 				if (DA558) {
 					DA148();
 									}
 							}
 			else if (!get_ival(DA454)) {
 				DA237(DA1115);
 				if (get_ival(PS5_L2)) {
 					if (event_press(PS5_OPTIONS)) {
 						DA354 = TRUE;
 						DA361 = TRUE;
 						DA360 = FALSE;
 						if (!DA354) {
 							DA360 = TRUE;
 													}
 											}
 					set_val(PS5_OPTIONS, 0x0);
 									}
 				if (!DA360) {
 					if (DA354 || DA355) {
 						vm_tctrl(0x0);
 					}
 					if (DA354) {
 						combo_stop(DA72);
 						vm_tctrl(0x0);
 						DA362= DA149(DA362,0x0  );
 						DA363 = DA149(DA363, 0x1);
 						DA364  = DA149(DA364   ,0x2  );
 						DA365  = DA149(DA365 , 0x3);
 						DA366  = DA149(DA366 , 0x4);
 						DA367 = DA149(DA367, 0x5);
 						DA368 = DA149(DA368, 0x6);
 						DA369 = DA149(DA369, 0x7);
 						DA370 = DA149(DA370, 0x8);
 						DA371 = DA149(DA371, 0x9);
 						DA372 = DA149(DA372, 0xA);
 						DA373 = DA149(DA373, 0xB);
 						DA374 = DA149(DA374, 0xC);
 						DA375 = DA149(DA375,0xD);
 						DA376 = DA149(DA376, 0xE);
 						DA377 = DA149(DA377, 0xF);
 						DA378 = DA149(DA378, 0x10);
 						DA379 = DA149(DA379, 0x11);
 						DA380 = DA149(DA380, 0x12);
 						DA381 = DA149(DA381, 0x13);
 						DA111 = DA149(DA111, 0x14);
 						DA383 = DA149(DA383, 0x15);
 						DA384              = DA149(DA384              ,0x16  );
 						DA385              = DA149(DA385              ,0x17  );
 						DA386               = DA149(DA386               ,0x18  );
 						if (event_press(PS5_DOWN)) {
 							DA356 = clamp(DA356 + 0x1, 0x0, DA387);
 							DA361 = TRUE;
 													}
 						if (event_press(PS5_UP)) {
 							DA356 = clamp(DA356 - 0x1, 0x0, DA387);
 							DA361 = TRUE;
 													}
 						if (event_press(PS5_CIRCLE)) {
 							DA354 = FALSE;
 							DA360 = FALSE;
 							DA361 = FALSE;
 							vm_tctrl(0x0);
 							combo_run(DA75);
 													}
 						if (DA580[DA356][0x2] == 0x1) {
 							if(DA356 == 0x0 ){
 								if(DA362 == 0x2 ){
 									if (event_press(PS5_CROSS)) {
 										DA357 = DA580[DA356][0x0];
 										DA354 = FALSE;
 										DA355 = TRUE;
 										DA361 = TRUE;
 																			}
 																	}
 															}
 							else{
 								if (event_press(PS5_CROSS)) {
 									DA357 = DA580[DA356][0x0];
 									DA354 = FALSE;
 									DA355 = TRUE;
 									DA361 = TRUE;
 																	}
 															}
 													}
 						DA184();
 						print(0x2, 0x26, OLED_FONT_SMALL, OLED_WHITE, DA570[0x0]);
 						DA158(DA356 + 0x1, DA164(DA356 + 0x1), 0x1C, 0x26, OLED_FONT_SMALL);
 						print(0x54, 0x26, OLED_FONT_SMALL, OLED_WHITE, DA572[0x0]);
 						DA158(DA947, DA164(DA947), 0x70, 0x26, OLED_FONT_SMALL);
 						line_oled(0x1, 0x30, 0x7F, 0x30, 0x1, 0x1);
 						if(DA356 == 0x0 ){
 							if(DA362 == 0x2 ){
 								print(0x2, 0x34, OLED_FONT_SMALL, 0x1, DA574[0x0]);
 															}
 							else{
 								print(0x2, 0x34, OLED_FONT_SMALL, 0x1, DA575[0x0]);
 															}
 													}
 						else{
 							if (DA580[DA356][0x2] == 0x0) {
 								print(0x2, 0x34, OLED_FONT_SMALL, 0x1, DA575[0x0]);
 															}
 							else {
 								print(0x2, 0x34, OLED_FONT_SMALL, 0x1, DA574[0x0]);
 															}
 													}
 											}
 					if (DA355) {
 						DA441               = DA152(DA441, 0x0);
 						DA442               = DA152(DA442, 0x1);
 						DA443             = DA152(DA443, 0x2);
 						DA444           = DA152(DA444, 0x3);
 						DA445             = DA152(DA445, 0x4);
 						DA446             = DA152(DA446, 0x5);
 						DA447              = DA152(DA447, 0x6);
 						DA448           = DA152(DA448, 0x7);
 						DA388          = DA152(DA388, 0x8);
 						DA389   = DA152(DA389, 0x9);
 						DA390 = DA152(DA390, 0xA);
 						DA391      = DA152(DA391, 0xB);
 						DA392    = DA152(DA392, 0xC);
 						DA393    = DA152(DA393, 0xD);
 						DA394    = DA152(DA394, 0xE);
 						DA395      = DA152(DA395, 0xF);
 						DA396      = DA152(DA396, 0x10);
 						DA277              = DA152(DA277, 0x11);
 						DA278           = DA152(DA278, 0x12);
 						DA279            = DA152(DA279, 0x13);
 						DA280            = DA152(DA280, 0x14);
 						DA281= DA152(DA281, 0x15);
 						DA409               = DA152(DA409, 0x16);
 						DA410               = DA152(DA410, 0x17);
 						DA411                   = DA152(DA411, 0x18);
 						DA412                   = DA152(DA412, 0x19);
 						DA413                   = DA152(DA413, 0x1A);
 						DA414   = DA152(DA414, 0x1B);
 						DA415   = DA152(DA415, 0x1C);
 						DA416 = DA152(DA416, 0x1D);
 						DA417   = DA152(DA417, 0x1E);
 						DA418   = DA152(DA418, 0x1F);
 						DA419 = DA152(DA419, 0x20);
 						DA420   = DA152(DA420, 0x21);
 						DA421   = DA152(DA421, 0x22);
 						DA422 = DA152(DA422, 0x23);
 						DA423   = DA152(DA423, 0x24);
 						DA424   = DA152(DA424, 0x25);
 						DA425 = DA152(DA425, 0x26);
 						DA426   = DA155(DA426, 0x27);
 						DA427         = DA155(DA427, 0x28);
 						DA428   = DA152(DA428, 0x29);
 						DA429     = DA152(DA429, 0x2A);
 						DA430                   = DA155(DA430, 0x2B);
 						DA1263 = DA152(DA1263, 0x36);
 						DA1256 = DA152(DA1256, 0x37);
 						DA431               = DA155(DA431, 0x2C);
 						DA432 = DA155(DA432, 0x2D);
 						DA433     = DA152(DA433, 0x2E);
 						DA434               = DA155(DA434, 0x2F);
 						DA435 = DA152(DA435, 0x30);
 						DA436 = DA152(DA436, 0x31);
 						DA437 = DA152(DA437, 0x32);
 						DA438 = DA152(DA438, 0x33);
 						DA439               = DA152(DA439, 0x34);
 						DA440                 = DA152(DA440, 0x35);
 						DA397       = DA155(DA397     ,0x38 );
 						DA398       = DA155(DA398     ,0x39 );
 						DA399      = DA152(DA399    ,0x3A );
 						DA400   = DA152(DA400 ,0x3B );
 						DA401       = DA155(DA401     ,0x3C );
 						DA402       = DA155(DA402     ,0x3D );
 						DA403   = DA152(DA403 ,0x3E );
 						DA404      = DA152(DA404    ,0x3F );
 						DA405          = DA155(DA405        ,0x40 );
 						DA406          = DA155(DA406        ,0x41 );
 						DA407         = DA152(DA407       ,0x42 );
 						DA457             = DA155(DA457           ,0x43 );
 						DA29             = DA152(DA29           ,0x44);
 						DA477           = DA152(DA477         ,0x45);
 						DA475         = DA152(DA475       ,0x46);
 						if (!get_ival(PS5_L2)) {
 							if (event_press(PS5_RIGHT)) {
 								DA357 = clamp(DA357 + 0x1, DA580[DA356][0x0], DA580[DA356][0x1]);
 								DA361 = TRUE;
 															}
 							if (event_press(PS5_LEFT)) {
 								DA357 = clamp(DA357 - 0x1, DA580[DA356][0x0], DA580[DA356][0x1]);
 								DA361 = TRUE;
 															}
 													}
 						if (event_press(PS5_CIRCLE)) {
 							DA354 = TRUE;
 							DA355 = FALSE;
 							DA361 = TRUE;
 													}
 						DA184();
 						DA949 = DA833[DA357][0x0];
 						DA950 = DA833[DA357][0x1];
 						if (DA833[DA357][0x4] == 0x0) {
 							DA158(DA949, DA164(DA949), 0x4, 0x14, OLED_FONT_SMALL);
 							DA158(DA950, DA164(DA950), 0x61, 0x14, OLED_FONT_SMALL);
 													}
 											}
 					if (DA361) {
 						cls_oled(OLED_BLACK);
 						rect_oled(0x0, 0x0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE);
 						line_oled(0x0, 0xE, 0x7F, 0xE, 0x1, 0x1);
 						if (DA355) {
 							print(DA229(DA182(DA589[DA357]), OLED_FONT_SMALL_WIDTH), 0x3, OLED_FONT_SMALL, OLED_WHITE, DA589[DA357]);
 													}
 						else {
 							print(DA229(DA182(DA590[DA356]), OLED_FONT_SMALL_WIDTH), 0x3, OLED_FONT_SMALL, OLED_WHITE, DA590[DA356]);
 													}
 						DA361 = FALSE;
 					}
 									}
 				if (!DA354 && !DA355) {
 					if (DA360) {
 						cls_oled(0x0);
 						combo_run(DA72);
 						DA360 = FALSE;
 						DA358 = TRUE;
 						vm_tctrl(0x0);
 					}
 					if(DA362 == 0x0){
 						DA449      = PS5_CIRCLE;
 						DA450      = PS5_CROSS ;
 						DA451    = PS5_L1    ;
 						DA452  = PS5_R1;
 						DA453    = PS5_L2;
 						DA454    = PS5_R2;
 						DA455     = PS5_SQUARE;
 						DA456  = PS5_TRIANGLE;
 					}
 					else if(DA362 == 0x1){
 						DA449      = PS5_SQUARE;
 						DA450      = PS5_CROSS ;
 						DA451    = PS5_L1    ;
 						DA452  = PS5_R1;
 						DA453    = PS5_L2;
 						DA454    = PS5_R2;
 						DA455     = PS5_CIRCLE;
 						DA456  = PS5_TRIANGLE;
 					}
 					else if(DA362 == 0x2){
 						DA449      = DA1386[DA441];
 						DA450      = DA1386[DA442] ;
 						DA451    = DA1386[DA443]  ;
 						DA452  = DA1386[DA444];
 						DA453    = DA1386[DA445];
 						DA454    = DA1386[DA446];
 						DA455     = DA1386[DA447];
 						DA456  = DA1386[DA448];
 					}
 					if (DA594 >= 0x7D0) {
 						DA594 = 0x7D0;
 											}
 					else if (DA594 <= 0x32) {
 						DA594 = 0x32;
 											}
 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA1210) {
 						set_val(DA450, 0x0);
 						vm_tctrl(0x0);
 						combo_run(DA77);
 											}
 					if (DA1075) {
 						if ((get_ipolar(POLAR_LS,POLAR_RADIUS) > 0x13EC) ){
 							DA596 = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + 0x17) % 0x168) / -45) + 0xA) % 0x8;
 							DA1070 = DA1397[DA596][0x0];
 							DA674 = DA1397[DA596][0x1];
 													}
 					}
 					if (get_ival(XB1_RS)) {
 						if (event_press(PS5_RIGHT)) {
 							DA430 += 0x5;
 							DA225(DA229(sizeof(DA598) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA598[0x0], DA430);
 													}
 						if (event_press(PS5_LEFT)) {
 							DA430 -= 0x5;
 							DA225(DA229(sizeof(DA598) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA598[0x0], DA430);
 													}
 						set_val(PS5_RIGHT, 0x0);
 						set_val(PS5_LEFT, 0x0);
 											}
 					if (get_ival(XB1_RS) && !DA618 ) {
 						if (event_press(PS5_UP)) {
 							DA594 += 0x32;
 							DA225(DA229(sizeof(DA604) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA604[0x0], DA594);
 													}
 						if (event_press(PS5_DOWN)) {
 							DA594 -= 0x32;
 							DA225(DA229(sizeof(DA604) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA604[0x0], DA594);
 													}
 						set_val(PS5_UP, 0x0);
 						set_val(PS5_DOWN, 0x0);
 											}
 					if (DA376) {
 						DA263();
 											}
 					if (DA377) {
 						DA264();
 						DA265();
 											}
 					if (!DA377) {
 						if (get_ival(DA449)) {
 							if (event_press(PS5_RIGHT)) {
 								DA611 += 0x2;
 								DA225(DA229(sizeof(DA612) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA612[0x0], DA611);
 															}
 							if (event_press(PS5_LEFT)) {
 								DA611 -= 0x2;
 								DA225(DA229(sizeof(DA612) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA612[0x0], DA611);
 															}
 							set_val(PS5_RIGHT, 0x0);
 							set_val(PS5_LEFT, 0x0);
 													}
 						if(!get_ival(DA451) ){
 							if(get_ival(DA449) && get_ptime(DA449) > DA611){
 								set_val(DA449,0x0);
 															}
 													}
 											}
 					if(DA380){
 						DA268();
 											}
 					if (DA372) {
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_SHARE)) {
 								DA618 = !DA618;
 								DA244(DA618);
 															}
 							set_val(PS5_SHARE, 0x0);
 													}
 											}
 					if (DA618 && DA372) {
 						vm_tctrl(0x0);
 						combo_stop(DA86);
 						if (get_ival(XB1_RS)) {
 							if (event_press(PS5_UP)) {
 								DA426 += 0xA;
 								DA225(DA229(sizeof(DA620) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA620[0x0], DA426);
 															}
 							if (event_press(PS5_DOWN)) {
 								DA426 -= 0xA;
 								DA225(DA229(sizeof(DA620) - 0x1, OLED_FONT_MEDIUM_WIDTH), DA620[0x0], DA426);
 															}
 							set_val(PS5_UP, 0x0);
 							set_val(PS5_DOWN, 0x0);
 													}
 						DA237(DA1114);
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_RIGHT)) {
 								DA625 = FALSE;
 								vm_tctrl(0x0);
 								combo_run(DA78);
 															}
 							if (event_press(PS5_LEFT)) {
 								DA625 = TRUE;
 								vm_tctrl(0x0);
 								combo_run(DA78);
 															}
 							set_val(PS5_L1,0x0);
 													}
 											}
 					if (DA373) {
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_OPTIONS)) {
 								DA627 = !DA627;
 								DA244(DA627);
 															}
 							set_val(PS5_OPTIONS, 0x0);
 													}
 											}
 					if (DA627 && DA373) {
 						vm_tctrl(0x0);
 						DA237(DA1116);
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_LEFT)) {
 								DA628 = FALSE;
 								vm_tctrl(0x0);
 								combo_run(DA79);
 															}
 							if (event_press(PS5_RIGHT)) {
 								DA628 = TRUE;
 								vm_tctrl(0x0);
 								combo_run(DA79);
 															}
 													}
 											}
 					if(DA365 || DA366 ){
 						DA115();
 											}
 					if (DA363) {
 						if (DA363 == DA1120) DA631 = TRUE;
 						if (DA363 == DA1121) {
 							if (event_press(DA1396[-1 +DA396]) && get_brtime(DA1396[-1 +DA396]) <= 0xC8) {
 								DA631 = !DA631;
 								DA244(DA631);
 															}
 							set_val(DA1396[-1 +DA396], 0x0);
 													}
 						if (DA363 > 0x0 && DA363 < 0x3 && DA631 == 0x1) {
 							DA241();
 													}
 						else if (DA363 == 0x3) {
 							if (get_ival(DA1396[-1 +DA396])) {
 								DA241();
 															}
 							set_val(DA1396[-1 +DA396], 0x0);
 													}
 											}
 					if( DA364 == 0x0)        DA634 = FALSE;
 					if( DA364 == DA1120) DA634 = TRUE;
 					if( DA364 == DA1121) {
 						if (event_press( DA1396[ -1 +DA281]) && get_brtime(DA1396[DA281])<=0xC8){
 							DA634 = !DA634;
 							DA244(DA634);
 													}
 						set_val(DA1396[ -1 +DA281],0x0);
 											}
 					if(DA634 ){
 						DA239();
 											}
 					if(DA364 > 0x2 ){
 						if(get_ival(DA1396[ -1 +DA281])){
 							DA239();
 													}
 						set_val(DA1396[ -1 +DA281],0x0);
 											}
 					if (DA367) {
 						if (DA367 == 0x1) {
 							DA637 = PS5_R3;
 							DA634 = FALSE;
 													}
 						if (DA367 == 0x2) {
 							DA637 = PS5_L3;
 							DA634 = FALSE;
 													}
 						if (DA367 == 0x3) {
 							DA637 = XB1_PR1;
 							DA634 = FALSE;
 													}
 						if (DA367 == 0x4) {
 							DA637 = XB1_PR2;
 							DA634 = FALSE;
 													}
 						if (DA367 == 0x5) {
 							DA637 = XB1_PL1;
 							DA634 = FALSE;
 													}
 						if (DA367 == 0x6) {
 							DA637 = XB1_PL2;
 							DA634 = FALSE;
 													}
 						if(get_ival(DA637)){
 							if(DA409 || DA410){
 								if( DA409 && event_press(PS5_L1)){
 									DA480 = FALSE;
 									DA1069 = DA409  ;
 									DA242( DA409   );
 								}
 								if( DA410 && event_press(PS5_R1)){
 									DA480 = TRUE;
 									DA1069 =  DA410 ;
 									DA242( DA410   );
 																	}
 								set_val(PS5_L1,0x0);
 								set_val(PS5_R1,0x0);
 								block = TRUE;
 															}
 							if( DA411 ){
 								if(event_press(PS5_SQUARE)){
 									DA480 = FALSE;
 									DA1069 =  DA411  ;
 									DA242( DA411   );
 								}
 								if(event_press(PS5_TRIANGLE)){
 									DA480 = TRUE;
 									DA1069 =  DA411  ;
 									DA242( DA411   );
 								}
 								set_val(PS5_SQUARE,0x0);
 								set_val(PS5_TRIANGLE,0x0);
 								block = TRUE;
 															}
 							if( DA412 ){
 								if(event_press(PS5_CROSS)){
 									DA480 = FALSE;
 									DA1069 =  DA412  ;
 									DA242( DA412   );
 								}
 								if(event_press(PS5_CIRCLE)){
 									DA480 = TRUE;
 									DA1069 =  DA412  ;
 									DA242( DA412   );
 								}
 								set_val(PS5_CROSS,0x0);
 								set_val(PS5_CIRCLE,0x0);
 								block = TRUE;
 															}
 							if( DA413 ){
 								if(event_press(PS5_R3)){
 									DA480 = FALSE;
 									DA1069 =  DA413  ;
 									DA242( DA413   );
 								}
 								set_val(PS5_R3,0x0);
 								block = TRUE;
 															}
 													}
 						set_val(DA637,0x0);
 											}
 					if (DA368) {
 						if (DA415 == 0x1) {
 							if (event_press(DA1396[-1 + DA414]) && !DA1162) {
 								vm_tctrl(0x0);
 								combo_run(DA82);
 															}
 							else if (event_press(DA1396[-1 + DA414]) && DA1162) {
 								set_val(DA1396[-1 + DA414], 0x0);
 								DA480 = !DA416;
 								DA1069 = DA368;
 								DA242(DA368);
 															}
 													}
 						else {
 							if (event_press(DA1396[-1 + DA414])) {
 								DA480 = !DA416;
 								set_val(DA1396[-1 + DA414], 0x0);
 								DA1069 = DA368;
 								DA242(DA368);
 															}
 													}
 					}
 					if (DA370) {
 						if (DA421 == 0x1) {
 							if (event_press(DA1396[-1 +DA420]) && !DA1162) {
 								vm_tctrl(0x0);
 								combo_run(DA82);
 															}
 							else if (event_press(DA1396[-1 +DA420]) && DA1162) {
 								set_val(DA1396[-1 +DA420], 0x0);
 								DA480 = !DA422;
 								DA1069 = DA370;
 								DA242(DA370);
 															}
 													}
 						else {
 							if (event_press(DA1396[-1 +DA420])) {
 								DA480 = !DA422;
 								set_val(DA1396[-1 +DA420], 0x0);
 								DA1069 = DA370;
 								DA242(DA370);
 															}
 													}
 					}
 					if (DA369) {
 						if (DA418 == 0x1) {
 							if (event_press(DA1396[-1 +DA417]) && !DA1162) {
 								vm_tctrl(0x0);
 								combo_run(DA82);
 															}
 							else if (event_press(DA1396[-1 +DA417]) && DA1162) {
 								set_val(DA1396[-1 +DA417], 0x0);
 								DA480 = !DA419;
 								DA1069 = DA369;
 								DA242(DA369);
 															}
 													}
 						else {
 							if (event_press(DA1396[-1 +DA417])) {
 								DA480 = !DA419;
 								set_val(DA1396[-1 +DA417], 0x0);
 								DA1069 = DA369;
 								DA242(DA369);
 															}
 													}
 					}
 					if (DA371) {
 						if (DA424 == 0x1) {
 							if (event_press(DA1396[-1 +DA423]) && !DA1162) {
 								vm_tctrl(0x0);
 								combo_run(DA82);
 															}
 							else if (event_press(DA1396[-1 +DA423]) && DA1162) {
 								set_val(DA1396[-1 +DA423], 0x0);
 								DA480 = !DA425;
 								DA1069 = DA371;
 								DA242(DA371);
 															}
 													}
 						else {
 							if (event_press(DA1396[-1 +DA423])) {
 								DA480 = !DA425;
 								set_val(DA1396[-1 +DA423], 0x0);
 								DA1069 = DA371;
 								DA242(DA371);
 															}
 													}
 					}
 					if (DA1069 == DA312 && combo_running(DA30)) set_val(DA451, 0x64);
 					if(DA385){
 						if(!block){
 							if(!get_val(DA453)){
 								if( !get_val(DA454)){
 									if(get_val(DA450)){
 										DA654 += get_rtime();
 																			}
 									if(DA404){
 										if(get_ival(DA450) && get_ptime(DA450) > DA402){
 											set_val(DA450,0x0);
 																					}
 																			}
 									if(event_release(DA450)){
 										if( DA654 < DA401 ){
 											DA655 = DA401 - DA654;
 											combo_run(DA106);
 																					}
 										else{
 											if(DA403) combo_run(DA107);
 																					}
 										DA654 = 0x0;
 																			}
 																	}
 							}
 						}
 											}
 					if(DA384){
 						if(!block){
 							if(!get_ival(DA453)){
 								if( !get_ival(DA454)){
 									if(get_ival(DA456)){
 										DA656 += get_rtime();
 																			}
 									if(event_release(DA456)){
 										if(DA656 < DA397){
 											DA657 = DA397 - DA656;
 											combo_run(DA108);
 																					}
 										else{
 											if(DA400) combo_run(DA101);
 																					}
 										DA656 = 0x0;
 																			}
 																	}
 							}
 						}
 											}
 					if(DA386){
 						if(!block){
 							if(get_ival(DA455)){
 								DA658 += get_rtime();
 															}
 							if(DA407){
 								if(get_ival(DA455) && get_ptime(DA455) > DA406){
 									set_val(DA455,0x0);
 																	}
 															}
 							if(event_release(DA455)){
 								if(DA658 && (DA658 < DA405)){
 									DA659 = DA405 - DA658;
 									combo_run(DA102);
 																	}
 								DA658 = 0x0;
 															}
 													}
 											}
 					if (DA374) {
 						if (event_press(DA1396[-1 +DA428])) {
 							vm_tctrl(0x0);
 							combo_run(DA77);
 													}
 						set_val(DA1396[-1 +DA428], 0x0);
 											}
 					if(!DA378){
 						DA431 = 0x0 ;
 						DA432 = 0x0;
 						DA433 = FALSE;
 						DA434 = 0x0;
 											}
 					if (DA379) {
 						DA264();
 						if (DA435 == 0x0) {
 							DA662 = FALSE;
 							DA459 = 0x0;
 													}
 						else {
 							DA662 = TRUE;
 							DA459 = 0x28;
 													}
 						if (DA436 == 0x0) {
 							DA664 = FALSE;
 							DA458 = 0x0;
 													}
 						else {
 							DA664 = TRUE;
 							DA458 = 0x55;
 													}
 						if (DA437 == 0x0) {
 							DA666 = FALSE;
 							DA460 = 0x0;
 													}
 						else {
 							DA666 = TRUE;
 							DA460 = -15;
 													}
 						if (DA438 == 0x0) {
 							DA668 = FALSE;
 													}
 						else {
 							DA668 = TRUE;
 													}
 						if(DA437 == 0x6 || DA436 == 0x6 || DA435 == 0x6){
 							if (get_ival(DA1396[-1 + DA437]) || get_ival(DA1396[-1 + DA436]) || get_ival(DA1396[-1 + DA435])){
 								combo_run(DA0);
 															}
 													}
 						if (DA666) {
 							if (get_val(DA1396[-1 + DA437])) {
 								set_val(DA1396[-1 + DA437], 0x0);
 								combo_run(DA98);
 								DA1219 = 0x2328;
 															}
 													}
 						if (DA668) {
 							if (get_val(DA1396[-1 + DA438])) {
 								set_val(DA1396[-1 + DA438], 0x0);
 								combo_run(DA99);
 								DA1219 = 0x2328;
 							}
 							if (combo_running(DA99)) {
 								if (get_ival(DA450) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA454) > 0x1E) {
 									combo_stop(DA99);
 																	}
 															}
 													}
 						if (DA664) {
 							if (get_val(DA1396[-1 + DA436])) {
 								set_val(DA1396[-1 + DA436], 0x0);
 								DA271();
 								DA1219 = 0x2328;
 															}
 													}
 						if (DA662) {
 							if (get_val(DA1396[-1 + DA435])) {
 								set_val(DA1396[-1 + DA435], 0x0);
 								combo_run(DA96);
 								DA1219 = 0x2328;
 															}
 													}
 											}
 					else{
 						DA459 = 0x0;
 						DA460 = 0x0;
 						DA458 = 0x0;
 											}
 					if (DA381) {
 						DA272();
 											}
 									}
 							}
 			if(get_ival(DA454)){
 				DA672 = 0x0;
 				combo_stop(DA88);
 							}
 					}
 		else {
 			if (!get_ival(DA454)) DA237(DA1113);
 					}
 			}
 			DA274();
 	}
 combo DA1 {
 	set_val(DA453, 0x64);
 	set_val(DA451,0x64);
 	DA258();
 	wait(0x190);
 	set_val(DA450,0x64);
 	wait(0x5A);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	}
 combo DA2 {
 	set_val(DA453, 0x64);
 	set_val(DA451,0x64);
 	DA258();
 	wait(0x190);
 	set_val(DA449,0x64);
 	wait(0xDC);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	}
 combo DA3 {
 	call(DA28);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	call(DA99);
 	DA254(DA1070, DA674);
 	vm_tctrl(0x0);
 	wait( 0x320);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	set_val(DA452,0x64);
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	}
 combo DA4 {
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA5 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA6 {
 	if (DA480) DA691 = DA596 + 0x1;
 	else DA691 = DA596 - 0x1;
 	DA249(DA691);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x3E8);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA7 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = FALSE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = TRUE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA8 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = TRUE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = FALSE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA9 {
 	DA480 = TRUE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = FALSE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA10 {
 	DA480 = FALSE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = TRUE;
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA11 {
 	DA254(0x0,0x0);
 	set_val(DA451,0x64);
 	set_val(DA452,0x64);
 	DA260();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	}
 combo DA12 {
 	set_val(DA1136, inv(DA1070));
 	set_val(DA1137, inv(DA674));
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA1136, inv(DA1070));
 	set_val(DA1137, inv(DA674));
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x1F4);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA13 {
 	DA254(0x0, 0x0);
 	set_val(DA453, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(0x0, 0x0);
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(0x0, 0x0);
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	DA254(0x0, 0x0);
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA14 {
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(inv(DA1070), inv(DA674));
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	DA254(inv(DA1070), inv(DA674));
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA15 {
 	set_val(DA451, 0x64);
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x1F4);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA16 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA17 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA18 {
 	DA259();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA19 {
 	DA259();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA20 {
 	DA259();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	DA258()  	  vm_tctrl(0x0);
 	wait( DA1073);
 	DA480 = !DA480;
 	DA257();
 	vm_tctrl(0x0);
 	wait( 0x3E8);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA21 {
 	set_val(DA451,0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait(0x3C);
 	DA254(0x0,0x0);
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait(0x3C);
 	set_val(DA451,0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait(0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA22 {
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA23 {
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( 0x50);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA24 {
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x14);
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	if (DA480) DA691 = DA596 + 0x4;
 	else {
 		DA691 = DA596 - 0x4;
 			}
 	DA249(DA691);
 	DA251(DA1166, DA677);
 	set_val(DA454, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA25 {
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x1E);
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA26 {
 	set_val(DA453, 0x64);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA27 {
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x1E);
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	set_val(PS5_L2, 0x64);
 	set_val(PS5_L1, 0x64);
 	set_val(PS5_R1, 0x64);
 	set_val(PS5_R2, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA28 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	set_val(PS5_R3,0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 }
 combo DA29 {
 	DA258();
 	set_val(DA454, 0x0);
 	vm_tctrl(0x0);
 	wait( 0x136);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA30 {
 	if (DA1069 == DA314) DA1074 = 0xC8;
 	else DA1074 = 0x1;
 	vm_tctrl(0x0);
 	wait( DA1074);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA31 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA259();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA32 {
 	if (DA1069 == DA314) DA1074 = 0xC8;
 	else DA1074 = 0x1;
 	set_val(DA453,0x64);
 	vm_tctrl(0x0);
 	wait( DA1074);
 	DA260();
 	set_val(DA453,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	set_val(DA453,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	set_val(DA453,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA33 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0xFA);
 	DA262();
 	vm_tctrl(0x0);
 	wait( 0x32);
 	DA480 = !DA480;
 	if (DA480) DA691 = DA596 - 0x2;
 	else DA691 = DA596 + 0x2;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x2D);
 	set_val(DA449, 0x64);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x2D);
 	DA254(DA1166, DA677);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x2D);
 	DA254(DA1166, DA677);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x2D);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x1F4);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA34 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x118);
 	DA257()  set_val(DA449, 0x64);
 	set_val(DA453, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA257()  set_val(DA453, 0x64);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA257()  set_val(DA453, 0x64);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0xFA);
 	DA257()  vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA35 {
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	DA260();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA36 {
 	set_val(DA451, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA451, 0x64);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA451, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA37 {
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA38 {
 	set_val(DA451, 0x64);
 	DA259();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA451, 0x64);
 	DA262();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA451, 0x64);
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA39 {
 	DA261();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA40 {
 	if (DA480) DA691 = DA596 + 0x3;
 	else DA691 = DA596 - 0x3;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(DA1166, DA677);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	DA254(DA1166, DA677);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA41 {
 	set_val(DA451, 0x64);
 	DA260();
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA451, 0x64);
 	DA262();
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA451, 0x64);
 	DA254(0x0, 0x0);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	if (DA480) DA691 = DA596 + 0x1;
 	else DA691 = DA596 - 0x1;
 	DA249(DA691);
 	set_val(DA454,0x0);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0xC8);
 	set_val(DA454,0x0);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA42 {
 	if (DA1069 == DA314) DA1074 = 0xC8;
 	else DA1074 = 0x1;
 	vm_tctrl(0x0);
 	wait( DA1074);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA43 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(PS5_L2, 0x64);
 	set_val(PS5_R2, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA44 {
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	if (DA1069 == DA327) DA257();
 	set_val(DA453, 0x64);
 	set_val(DA454, 0x64);
 	vm_tctrl(0x0);
 	wait( 0xC8);
 	if (DA1069 == DA327) DA257();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA45 {
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	if (DA1069 == DA327) DA257();
 	set_val(DA453, 0x64);
 	set_val(DA454, 0x64);
 	vm_tctrl(0x0);
 	wait( 0xC8);
 	if (DA1069 == DA327) DA257();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA46 {
 	DA261();
 	set_val(DA452,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA258();
 	set_val(DA452,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	set_val(DA452,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA47 {
 	DA254(DA1070, DA674);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(DA1070, DA674);
 	DA262();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(DA1070, DA674);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	set_val(DA454, 0x64);
 	DA254(inv(DA1070), inv(DA674));
 	vm_tctrl(0x0);
 	wait( 0x258);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA48 {
 	DA254(DA1070, DA674);
 	set_val(XB1_LS, 0x64);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(DA1070, DA674);
 	DA262();
 	set_val(XB1_LS, 0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA254(DA1070, DA674);
 	DA258();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	set_val(DA453, 0x64);
 	set_val(DA454, 0x64);
 	if (DA480) DA691 = DA596 + 0x4;
 	else DA691 = DA596 - 0x4;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0xDC);
 	if (DA480) DA691 = DA596 + 0x4;
 	else DA691 = DA596 - 0x4;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	if (DA480) DA691 = DA596 + 0x1;
 	else DA691 = DA596 - 0x1;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x258);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA49 {
 	set_val(DA450, 0x0);
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x50);
 	vm_tctrl(0x0);
 	wait( 0x1F4);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA50 {
 	set_val(DA449, 0x64);
 	set_val(DA454,0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA454,0x64);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	set_val(DA454,0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	set_val(DA454,0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA51 {
 	set_val(DA451,0x64);
 	set_val(DA452,0x64);
 	DA254(inv(DA1070), inv(DA674));
 	vm_tctrl(0x0);
 	wait( 0xC8);
 	set_val(DA451,0x64);
 	set_val(DA452,0x64);
 	DA480 = FALSE;
 	DA257();
 	vm_tctrl(0x0);
 	wait( 0x32);
 	set_val(DA451,0x64);
 	set_val(DA452,0x64);
 	DA480 = !DA480;
 	DA257();
 	set_val(DA451,0x64);
 	set_val(DA452,0x64);
 	vm_tctrl(0x0);
 	wait( 0x21C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA52 {
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x8C);
 	set_val(PS5_L2, 0x64);
 	set_val(PS5_R2, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA53 {
 	DA254(inv(DA1070), inv(DA674));
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(inv(DA1070), inv(DA674));
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(inv(DA1070), inv(DA674));
 	set_val(DA453, 0x64);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA54 {
 	set_val(DA451, 0x64);
 	set_val(DA455, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA451, 0x64);
 	set_val(DA455, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA451, 0x64);
 	set_val(DA455, 0x0);
 	set_val(DA450, 0x64);
 	DA257();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA451, 0x64);
 	DA257();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA55 {
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x122);
 	set_val(PS5_L2, 0x64);
 	set_val(PS5_R2, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA56 {
 	set_val(DA449, 0x64);
 	set_val(DA453,0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA453,0x64);
 	set_val(DA449, 0x64);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA453,0x64);
 	set_val(DA449, 0x0);
 	set_val(DA450, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA57 {
 	set_val(DA451, 0x64);
 	DA260();
 	vm_tctrl(0x0);
 	wait( 0x12C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA58 {
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA59 {
 	set_val(DA451,0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( DA1073);
 	DA260();
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( DA1073);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA60 {
 	DA254(DA1070, DA674);
 	DA261();
 	vm_tctrl(0x0);
 	wait( 0x64);
 	DA262();
 	DA254(DA1070, DA674);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	DA260();
 	DA254(DA1070, DA674);
 	vm_tctrl(0x0);
 	wait( 0x140);
 	DA254(DA1070, DA674);
 	DA262();
 	vm_tctrl(0x0);
 	wait( 0xDC);
 	DA254(DA1070, DA674);
 	DA260();
 	DA254(DA1070, DA674);
 	vm_tctrl(0x0);
 	wait( 0x64);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA61 {
 	call(DA84);
 	DA254(0x0, 0x0);
 	call(DA85);
 	call(DA85);
 	call(DA85);
 	call(DA85);
 	call(DA85);
 	set_val(DA453, 0x64);
 	DA261();
 	vm_tctrl(0x0);
 	wait( 0x46);
 	set_val(DA453, 0x64);
 	DA262();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA453, 0x64);
 	DA260();
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA453, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x258);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA62 {
 	set_val(DA1136, inv(DA1070));
 	set_val(DA1137, inv(DA674));
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(DA1136, inv(DA1070));
 	set_val(DA1137, inv(DA674));
 	set_val(DA452, 0x64);
 	set_val(DA451, 0x64);
 	set_val(PS5_R3, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA63 {
 	vm_tctrl(0x0);
 	wait( 0x64);
 	DA254(0x0, 0x0);
 	DA260();
 	vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(0x0, 0x0);
 	DA262()  vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(0x0, 0x0);
 	DA260()  vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(0x0, 0x0);
 	DA262()  vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(0x0, 0x0);
 	DA261();
 	vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(0x0, 0x0);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA64 {
 	set_val(PS5_R3,0x64);
 	if (DA480) DA691 = DA596 + 0x1;
 	else DA691 = DA596 - 0x1;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA65 {
 	call(DA84);
 	DA254(0x0,0x0);
 	vm_tctrl(0x0);
 	wait( 0x3C);
 	set_val(PS5_R3,0x64);
 	if (DA480) DA691 = DA596 + 0x1;
 	else DA691 = DA596 - 0x1;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA66 {
 	call(DA84);
 	DA254(0x0,0x0);
 	set_val(DA453,0x64);
 	set_val(DA454,0x64);
 	vm_tctrl(0x0);
 	wait( 0x2EE);
 	}
 combo DA67 {
 	set_val(PS5_R3,0x64);
 	if (DA480) DA691 = DA596 + 0x2;
 	else DA691 = DA596 - 0x2;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA68 {
 	set_val(DA453,0x64);
 	set_val(PS5_R3,0x64);
 	if (DA480) DA691 = DA596 ;
 	else DA691 = DA596;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	set_val(DA453,0x64);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA69 {
 	call(DA84);
 	set_val(DA453,0x64);
 	set_val(PS5_R3,0x64);
 	if (DA480) DA691 = DA596 ;
 	else DA691 = DA596;
 	DA249(DA691);
 	DA254(DA1166, DA677);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x46);
 	set_val(DA453,0x64);
 	DA254(DA1166, DA677);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	}
 combo DA70 {
 	DA254(0x0,0x0);
 	set_val(DA452,0x64);
 	set_val(DA451,0x64);
 	DA258();
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	vm_tctrl(0x0);
 	wait( 0x15E);
 	set_val(DA452,0x64);
 	set_val(DA451,0x64);
 	vm_tctrl(0x0);
 	wait( 0x190);
 	}
 int DA141 ;
 int DA758 ;
 int DA759 ;
 int DA760;
 int DA761;
 function DA127(DA128){
 	DA758 = 0x2;
 	DA759 = 0xF1206;
 	DA141 = 0xD431;
 	DA760 = (DA128 >> DA758) | (DA128 << (0x20 - DA758));
 	DA761 = (((DA760 >> ((DA760 & 0xF) % 0xD)) & 0x7FFFF) + DA141) % DA759 + 0x1E240;
 	return DA761;
 	}
 define DA763 = -1;
 define DA538 = -2;
 define DA765 = -3;
 define DA766 = 0x0;
 define DA539 = 0x1;
 function DA129(DA128, DA131, DA132) {
 	if(DA128 > DA132) return DA131;
 	if(DA128 < DA131) return DA132;
 	return DA128;
 	}
 int DA770,DA771;
 function DA133(DA134,DA135,DA136,DA137,DA138,DA139){
 	if(!DA139){
 		print(DA142(DA140(DA134),DA137,DA135),DA136,DA137,DA138,DA134)     	}
 	else{
 		if(DA134 < 0x0){
 			putc_oled(0x1,0x2D);
 					}
 		if(DA134){
 			for(DA770 = DA146(DA134) + DA771 = (DA134 < 0x0 ),DA134 = abs(DA134);
 			DA134 > 0x0;
 			DA770-- , DA771++){
 				putc_oled(DA770,DA134%0xA + 0x30);
 				DA134 = DA134/0xA;
 							}
 					}
 		else{
 			putc_oled(0x1,0x30);
 			DA771 = 0x1         		}
 		puts_oled(DA142(DA771,DA137,DA135),DA136,DA137,DA771 ,DA138);
 			}
 	}
 int DA792;
 function DA140(DA141) {
 	DA792 = 0x0;
 	do {
 		DA141++;
 		DA792++;
 			}
 	while (duint8(DA141));
 	return DA792;
 	}
 function DA142(DA143,DA137,DA135) {
 	if(DA135 == inv(0x3)){
 		return 0x80 - ((DA143 * (0x7 + (DA137 > 0x1) + DA137 * 0x4)) + 0x3 );
 			}
 	if(DA135 == inv(0x2)){
 		return 0x40 - ((DA143 * (0x7 + (DA137 > 0x1) + DA137 * 0x4)) / 0x2);
 			}
 	if(DA135 == inv(0x1)){
 		return 0x3 	}
 	return DA135;
 	}
 function DA146(DA147) {
 	for(DA770 = 0x1;
 	DA770 < 0xB;
 	DA770++){
 		if(!(abs(DA147) / pow(0xA,DA770))){
 			return DA770;
 			break;
 					}
 			}
 	return 0x1;
 	}
 function DA148() {
 	if (get_ival(DA449)) {
 		set_val(DA449, 0x0);
 		if (get_ival(DA451)) DA799 = 0x32;
 		if (!get_ival(DA451)) DA799 = 0x1B8;
 		combo_run(DA71);
 			}
 	if (DA798 > 0x0) set_polar(POLAR_LS, DA798 * inv(0x1), 0x7FFF);
 	if (get_ival(PS5_RIGHT) && get_ival(PS5_DOWN)) DA798 = 0x159;
 	if (get_ival(PS5_RIGHT) && get_ival(PS5_UP)) DA798 = 0x2D;
 	if (get_ival(PS5_LEFT) && get_ival(PS5_UP)) DA798 = 0x87;
 	if (get_ival(PS5_LEFT) && get_ival(PS5_DOWN)) DA798 = 0xE1;
 	if (event_press(PS5_LEFT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA798 = 0xB4;
 	if (event_press(PS5_RIGHT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA798 = 0x1;
 	if (event_press(PS5_UP) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA798 = 0x5A;
 	if (event_press(PS5_DOWN) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA798 = 0x10E;
 }
 int DA799;
 int DA558;
 int DA798;
 combo DA71 {
 	set_val(DA449, 0x64);
 	vm_tctrl(0x0);
 	wait( DA799);
 	vm_tctrl(0x0);
 	wait( 0x32);
 	vm_tctrl(0x0);
 	wait( 0xED8);
 	DA558 = !DA558;
 }
 define DA802 = 0x13;
 function DA149(DA150, DA151) {
 	if (DA356 == DA151) {
 		if (event_press(PS5_RIGHT)) {
 			DA150 = clamp(DA150 + 0x1, 0x0, DA805[DA356]);
 			DA361 = TRUE;
 					}
 		if (event_press(PS5_LEFT)) {
 			DA150 = clamp(DA150 - 0x1, 0x0, DA805[DA356]);
 			DA361 = TRUE;
 					}
 		if (DA356 == 0x0) {
 			print(DA229(DA182(DA809[DA362]) ,OLED_FONT_SMALL_WIDTH),DA802  ,OLED_FONT_SMALL , OLED_WHITE ,DA809[DA362]);
 					}
 		else if (DA356 == 0x1) {
 			print(DA229(DA182(DA811[DA363]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA811[DA363]);
 					}
 		else if (DA356 == 0x2) {
 			print(DA229(DA182(DA811[DA364]) ,OLED_FONT_SMALL_WIDTH ),DA802  ,OLED_FONT_SMALL , OLED_WHITE ,DA811[DA364]);
 					}
 		else if (DA356 == 0x5) {
 			print(DA229(DA182(DA815[DA367]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA815[DA367]);
 					}
 		else if (DA356 == 0x6) {
 			print(DA229(DA182(DA817[DA368]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA368]);
 					}
 		else if (DA356 == 0x7) {
 			print(DA229(DA182(DA817[DA369]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA369]);
 					}
 		else if (DA356 == 0x8) {
 			print(DA229(DA182(DA817[DA370]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA370]);
 					}
 		else if (DA356 == 0x9) {
 			print(DA229(DA182(DA817[DA371]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA371]);
 					}
 		else if (DA356 == 0x14) {
 			print(DA229(DA182(DA825[DA111]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA825[DA111]);
 					}
 		else {
 			if (DA150 == 0x1)        print(DA229(DA182(DA827[0x1]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA827[0x1])      else        print(DA229(DA182(DA827[0x0]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA827[0x0])     		}
 			}
 	return DA150;
 	}
 function DA152(DA150, DA151) {
 	if (DA357 == DA151) {
 		if (get_ival(PS5_L2)) {
 			if (event_press(PS5_RIGHT)) {
 				DA150 += DA833[DA357][0x2]  				        DA361 = TRUE;
 							}
 			if (event_press(PS5_LEFT)) {
 				DA150 -= DA833[DA357][0x2]  				        DA361 = TRUE;
 							}
 			DA150 = clamp(DA150, DA833[DA357][0x0], DA833[DA357][0x1]);
 		}
 		if (DA357 == 0x8) {
 			print(DA229(DA182(DA817[DA388]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA388])     		}
 		else if (DA357 == 0x9) {
 			print(DA229(DA182(DA817[DA389]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA389])     		}
 		else if (DA357 == 0xA) {
 			print(DA229(DA182(DA817[DA390]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA390])     		}
 		else if (DA357 == 0xB) {
 			print(DA229(DA182(DA817[DA391]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA391])     		}
 		else if (DA357 == 0xC) {
 			print(DA229(DA182(DA817[DA392]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA392])     		}
 		else if (DA357 == 0xD) {
 			print(DA229(DA182(DA817[DA393]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA393])     		}
 		else if (DA357 == 0xE) {
 			print(DA229(DA182(DA817[DA394]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA394])     		}
 		else if (DA357 == 0xF) {
 			print(DA229(DA182(DA817[DA395]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA395])     		}
 		else if (DA357 == 0x10) {
 			print(DA229(DA182(DA852[DA396]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA396])     		}
 		else if (DA357 == 0x11) {
 			print(DA229(DA182(DA817[DA277]),OLED_FONT_SMALL_WIDTH ),DA802,OLED_FONT_SMALL,OLED_WHITE,DA817[DA277])  		}
 		else if(DA357 == 0x12){
 			print(DA229(DA182(DA817[DA278]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA817[DA278])  		}
 		else if(DA357 == 0x13){
 			print(DA229(DA182(DA817[DA279]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA817[DA279])  		}
 		else if(DA357 == 0x14){
 			print(DA229(DA182(DA817[DA280]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA817[DA280])  		}
 		else if(DA357 == 0x15){
 			print(DA229(DA182(DA852[DA281]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA852[DA281])       		}
 		else if(DA357 == 0x16){
 			print(DA229(DA182(DA817[DA409]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA409])     		}
 		else if (DA357 == 0x17) {
 			print(DA229(DA182(DA817[DA410]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA410])     		}
 		else if (DA357 == 0x18) {
 			print(DA229(DA182(DA817[DA411]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA411])     		}
 		else if (DA357 == 0x19) {
 			print(DA229(DA182(DA817[DA412]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA412])     		}
 		else if (DA357 == 0x1A) {
 			print(DA229(DA182(DA817[DA413]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA817[DA413])     		}
 		else if (DA357 == 0x1B) {
 			print(DA229(DA182(DA852[DA414]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA414])     		}
 		else if (DA357 == 0x1C) {
 			print(DA229(DA182(DA876[DA415]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA876[DA415])     		}
 		else if (DA357 == 0x1D) {
 			print(DA229(DA182(DA878[DA416]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA878[DA416])     		}
 		else if (DA357 == 0x1E) {
 			print(DA229(DA182(DA852[DA417]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA417])     		}
 		else if (DA357 == 0x1F) {
 			print(DA229(DA182(DA876[DA418]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA876[DA418])     		}
 		else if (DA357 == 0x20) {
 			print(DA229(DA182(DA878[DA419]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA878[DA419])     		}
 		else if (DA357 == 0x21) {
 			print(DA229(DA182(DA852[DA420]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA420])     		}
 		else if (DA357 == 0x22) {
 			print(DA229(DA182(DA876[DA421]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA876[DA421])     		}
 		else if (DA357 == 0x23) {
 			print(DA229(DA182(DA878[DA422]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA878[DA422])     		}
 		else if (DA357 == 0x24) {
 			print(DA229(DA182(DA852[DA423]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA423])     		}
 		else if (DA357 == 0x25) {
 			print(DA229(DA182(DA876[DA424]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA876[DA424])     		}
 		else if (DA357 == 0x26) {
 			print(DA229(DA182(DA878[DA425]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA878[DA425])     		}
 		else if (DA357 == 0x29) {
 			print(DA229(DA182(DA852[DA428]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA428])     		}
 		else if (DA357 == 0x30) {
 			print(DA229(DA182(DA852[DA435]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA435])     		}
 		else if (DA357 == 0x31) {
 			print(DA229(DA182(DA852[DA436]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA436])     		}
 		else if (DA357 == 0x32) {
 			print(DA229(DA182(DA852[DA437]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA437])     		}
 		else if (DA357 == 0x33) {
 			print(DA229(DA182(DA852[DA438]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA852[DA438])     		}
 		else if(DA357 == 0x0){
 			print(DA229(DA182(DA908[DA441]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA441])  		}
 		else if(DA357 == 0x1){
 			print(DA229(DA182(DA908[DA442]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA442])  		}
 		else if(DA357 == 0x2){
 			print(DA229(DA182(DA908[DA443]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA443])  		}
 		else if(DA357 == 0x3){
 			print(DA229(DA182(DA908[DA444]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA444])  		}
 		else if(DA357 == 0x4){
 			print(DA229(DA182(DA908[DA445]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA445])  		}
 		else if(DA357 == 0x5){
 			print(DA229(DA182(DA908[DA446]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA446])  		}
 		else if(DA357 == 0x6){
 			print(DA229(DA182(DA908[DA447]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA447])  		}
 		else if(DA357 == 0x7){
 			print(DA229(DA182(DA908[DA448]),OLED_FONT_SMALL_WIDTH),DA802,OLED_FONT_SMALL,OLED_WHITE,DA908[DA448])  		}
 		else{
 			if (DA150 == 0x1)        print(DA229(DA182(DA827[0x1]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA827[0x1])      else        print(DA229(DA182(DA827[0x0]), OLED_FONT_SMALL_WIDTH), DA802, OLED_FONT_SMALL, OLED_WHITE, DA827[0x0])     		}
 		DA185(0x0);
 			}
 	return DA150;
 	}
 function DA155(DA150, DA151) {
 	if (DA357 == DA151) {
 		if (get_ival(PS5_L2)) {
 			if (event_press(PS5_RIGHT)) {
 				DA150 += DA833[DA357][0x2]  				        DA361 = TRUE;
 							}
 			if (event_press(PS5_LEFT)) {
 				DA150 -= DA833[DA357][0x2]  				        DA361 = TRUE;
 							}
 			if (event_press(PS5_UP)) {
 				DA150 += DA833[DA357][0x3]  				        DA361 = TRUE;
 							}
 			if (event_press(PS5_DOWN)) {
 				DA150 -= DA833[DA357][0x3]  				        DA361 = TRUE;
 							}
 			DA150 = clamp(DA150, DA833[DA357][0x0], DA833[DA357][0x1]);
 		}
 		DA232(DA150, DA235(DA150));
 	}
 	return DA150;
 	}
 int DA935, DA936, DA937;
 function DA158(DA128, DA160, DA161, DA162, DA137) {
 	DA936 = 0x1;
 	DA937 = 0x2710;
 	if (DA128 < 0x0)  	  {
 		putc_oled(DA936, 0x2D);
 		DA936 += 0x1;
 		DA128 = abs(DA128);
 			}
 	for (DA935 = 0x5;
 	DA935 >= 0x1;
 	DA935--) {
 		if (DA160 >= DA935) {
 			putc_oled(DA936, DA943[DA128 / DA937]);
 			DA128 = DA128 % DA937;
 			DA936 += 0x1;
 					}
 		DA937 /= 0xA;
 			}
 	puts_oled(DA161, DA162, DA137, DA936 - 0x1, OLED_WHITE);
 }
 const string DA575 = " No Edit Variable";
 const string DA574 = " A/CROSS to Edit ";
 const string DA570 = "MOD;";
 const string DA572 = "MSL;";
 int DA947;
 function DA164(DA147) {
 	DA147 = abs(DA147);
 	if (DA147 / 0x2710 > 0x0) return 0x5;
 	if (DA147 / 0x3E8 > 0x0) return 0x4;
 	if (DA147 / 0x64 > 0x0) return 0x3;
 	if (DA147 / 0xA > 0x0) return 0x2;
 	return 0x1;
 	}
 const int8 DA943[] =     {
 	0x30,    0x31,    0x32,    0x33,    0x34,    0x35,    0x36,    0x37,    0x38,    0x39   }
 ;
 int DA949, DA950;
 const image DA952 = {
 	0x62, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF }
 ;
 combo DA72 {
 	call(DA73);
 	DA181();
 	vm_tctrl(0x0);
 	wait( 0x960);
 	cls_oled(0x0);
 	image_oled(0x0, 0x0, TRUE, TRUE, DA952[0x0]);
 	vm_tctrl(0x0);
 	wait( get_rtime());
 	vm_tctrl(0x0);
 	wait( 0x1F4)cls_oled(0x1)image_oled(0x0, 0x0, 0x0, 0x1, DA952[0x0]);
 	vm_tctrl(0x0);
 	wait( get_rtime());
 	vm_tctrl(0x0);
 	wait( 0x3E8)call(DA74);
 	vm_tctrl(0x0);
 	wait( 0x3E8);
 	DA358 = TRUE;
 	}
 combo DA73 {
 	cls_oled(OLED_BLACK);
 	}
 const int8 DA1378[] = {
 	OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT  }
 const int8 DA1379[] = {
 	OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH  }
 int DA954;
 function DA166(DA167, DA168, DA169, DA170) {
 	DA954--;
 	switch(DA167) {
 		case DA968 {
 			DA167 = OLED_WIDTH - (DA954 * DA1379[DA169]) - 0x4;
 			break;
 					}
 		case DA967 {
 			DA167 = (OLED_WIDTH >> 0x1) - ((DA954 * DA1379[DA169]) >> 0x1);
 			break;
 					}
 	}
 	switch(DA168) {
 		case DA967 {
 			DA168 = (OLED_HEIGHT >> 0x1) - (DA1378[DA169] >> 0x1);
 			break;
 					}
 		case DA970 {
 			DA168 = OLED_HEIGHT - DA1378[DA169] - 0x4;
 			break;
 					}
 	}
 	puts_oled(DA167, DA168, DA169, DA954, DA170);
 	DA954 = 0x1;
 }
 enum {
 	DA967 = -2, DA968, DA969 = 0x5, DA970 = -1, DA971 = 0x5  }
 data(0x20,0x44, 0x61, 0x72 ,0x6B ,0x20, 0x41, 0x6E, 0x67, 0x65, 0x6C,0x20,0x0);
 combo DA74 {
 	vm_tctrl(0x0);
 	wait(0x168);
 	print(0x1,0x12,OLED_FONT_MEDIUM,OLED_WHITE,0x0) 	set_rumble(RUMBLE_A, 0x32);
 	vm_tctrl(0x0);
 	wait( 0x2D0);
 	set_rumble(RUMBLE_A, 0x32);
 	set_rumble(RUMBLE_B, 0x64);
 	vm_tctrl(0x0);
 	wait( 0x2D0);
 	reset_rumble();
 	vm_tctrl(0x0);
 	wait( 0x618);
 	}
 function DA171(DA167, DA168, DA174, DA169, DA170) {
 	DA179(DA174);
 	DA166(DA167, DA168, DA169, DA170);
 	}
 function DA177(DA178) {
 	putc_oled(DA954, DA178);
 	DA954++;
 	}
 function DA179(DA180) {
 	do {
 		DA177(dint8(DA180));
 		DA180++;
 	}
 	while(dint8(DA180))  }
 const int16 DA1380[] = {
 	-0, 0x6, 0xB, 0x11, 0x15, 0x1A, 0x1E, 0x21, 0x23, 0x24, 0x25, 0x24, 0x22, 0x1F, 0x1B, 0x16, 0x10, 0x8,0x0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 0x17, 0x2F, 0x47, 0x5F, 0x77, 0x8E, 0xA4, 0xBA, 0xCE, 0xE2, 0xF3, 0x103, 0x111, 0x11D, 0x127, 0x12F, 0x135,0x138, 0x138, 0x136, 0x132, 0x12B, 0x121, 0x116, 0x107, 0xF7, 0xE5, 0xD1, 0xBB, 0xA3, 0x8A, 0x70, 0x55, 0x39, 0x1D,0x0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 0x17, 0x2D, 0x42, 0x56, 0x69, 0x7A, 0x89, 0x98, 0xA4, 0xAE, 0xB7, 0xBE, 0xC3, 0xC6, 0xC7, 0xC7, 0xC4,0xC1, 0xBB, 0xB4, 0xAC, 0xA3, 0x99, 0x8E, 0x82, 0x76, 0x69, 0x5C, 0x4F, 0x43, 0x36, 0x2A, 0x1E, 0x13, 0x9,0x0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 }
 const int16 DA1381[] = {
 	0x0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148,0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 }
 const int16 DA1382[] = {
 	0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148 }
 const int16 DA1383[] = {
 	0x148, 0x148, 0x147, 0x147, 0x147, 0x146, 0x146, 0x145, 0x144, 0x144, 0x143, 0x142, 0x141, 0x13F, 0x13E, 0x13D, 0x13B, 0x139,0x138, 0x136, 0x134, 0x132, 0x130, 0x12E, 0x12B, 0x129, 0x127, 0x124, 0x121, 0x11F, 0x11C, 0x119, 0x116, 0x113, 0x110, 0x10C,0x109, 0x106, 0x102, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC, 0xE8, 0xE4, 0xDF, 0xDB, 0xD7, 0xD3, 0xCE, 0xCA, 0xC5,0xC1, 0xBC, 0xB7, 0xB2, 0xAE, 0xA9, 0xA4, 0x9F, 0x9A, 0x95, 0x90, 0x8A, 0x85, 0x80, 0x7B, 0x75, 0x70, 0x6B,0x65, 0x60, 0x5A, 0x55, 0x4F, 0x4A, 0x44, 0x3F, 0x39, 0x33, 0x2E, 0x28, 0x22, 0x1D, 0x17, 0x11, 0xB, 0x6,0x0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0x0, 0x6, 0xB, 0x11, 0x17, 0x1D, 0x22, 0x28, 0x2E, 0x33, 0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x55, 0x5A, 0x60,0x65, 0x6B, 0x70, 0x75, 0x7B, 0x80, 0x85, 0x8A, 0x90, 0x95, 0x9A, 0x9F, 0xA4, 0xA9, 0xAE, 0xB2, 0xB7, 0xBC,0xC1, 0xC5, 0xCA, 0xCE, 0xD3, 0xD7, 0xDB, 0xDF, 0xE4, 0xE8, 0xEC, 0xF0, 0xF4, 0xF7, 0xFB, 0xFF, 0x102, 0x106,0x109, 0x10C, 0x110, 0x113, 0x116, 0x119, 0x11C, 0x11F, 0x121, 0x124, 0x127, 0x129, 0x12B, 0x12E, 0x130, 0x132, 0x134, 0x136,0x138, 0x139, 0x13B, 0x13D, 0x13E, 0x13F, 0x141, 0x142, 0x143, 0x144, 0x144, 0x145, 0x146, 0x146, 0x147, 0x147, 0x147, 0x148 }
 int DA983;
 int DA984;
 int DA985;
 int DA986;
 int DA987;
 int DA988;
 int DA989;
 function DA181() {
 	DA989 = 0x3;
 	DA987 = DA989 * DA1383[DA988];
 	DA986 = DA989 * DA1380[DA988];
 	DA984 = ((DA987 * DA1382[DA983]) / 0x148) - ((DA986 * DA1381[DA983]) / 0x148);
 	DA985 = ((DA987 * DA1381[DA983]) / 0x148) + ((DA986 * DA1382[DA983]) / 0x148);
 	DA987 = DA984;
 	DA986 = DA985;
 	DA988 += 0x1;
 	DA983 += 0x2D;
 	if(DA988 >= 0x168) {
 		DA988 %= 0x168;
 			}
 	if(DA983 >= 0x168) {
 		DA983 %= 0x168;
 			}
 	pixel_oled(0x40 + (((DA987 / DA989) * 0x1E) / 0x148), 0x20 + (((DA986 / DA989) * 0x1E) / 0x148), OLED_WHITE);
 	}
 int DA993;
 function DA182(DA141) {
 	DA993 = 0x0;
 	do {
 		DA141++;
 		DA993++;
 			}
 	while (duint8(DA141));
 	return DA993;
 	}
 int DA996;
 const uint8 DA1384[] = {
 	PS5_OPTIONS,  PS5_LEFT,  PS5_RIGHT,  PS5_UP,  PS5_DOWN,  PS5_CROSS,  PS5_CIRCLE,  PS5_SQUARE,  PS5_TRIANGLE,  PS5_R3,  PS5_L3,  PS5_RX,  PS5_RY,  PS5_PS,  PS5_TOUCH,  PS5_SHARE }
 ;
 function DA184() {
 	for (DA996 = 0x0;
 	DA996 < sizeof(DA1384) / sizeof(DA1384[0x0]);
 	DA996++) {
 		if (get_ival(DA1384[DA996]) || event_press(DA1384[DA996])) {
 			set_val(DA1384[DA996], 0x0);
 		}
 			}
 	}
 define DA997 = 0x83;
 define DA998 = 0x84;
 define DA999 = 0x85;
 define DA1000 = 0x86;
 define DA1001 = 0x82;
 define DA1002 = 0x59;
 define DA1003 = 0x7F;
 define DA1004 = 0x41;
 int DA1005;
 int DA1006;
 int DA1007 = 0x1;
 define DA1008 = 0x24;
 const string DA1009 = "Hold LT/L2 +";
 function DA185(DA186) {
 	line_oled(0x1, 0x30, 0x7F, 0x30, 0x1, 0x1);
 	print(0x2, 0x34, OLED_FONT_SMALL, 0x1, DA1009[0x0]);
 	rect_oled(0x5A, 0x32, 0x7F, 0x3C, OLED_WHITE, DA1007);
 	putc_oled(0x1, DA999);
 	puts_oled(0x5B, 0x33, OLED_FONT_SMALL, 0x1, DA1005);
 	putc_oled(0x1, DA1000);
 	puts_oled(0x65, 0x33, OLED_FONT_SMALL, 0x1, DA1006);
 	if (DA186) {
 		putc_oled(0x1, DA997);
 		puts_oled(0x6F, 0x33, OLED_FONT_SMALL, 0x1, DA1005);
 		putc_oled(0x1, DA998);
 		puts_oled(0x79, 0x33, OLED_FONT_SMALL, 0x1, DA1006);
 			}
 	}
 const uint8 DA1386 [] = {
 	  PS5_R1,        	  PS5_R2,        	  PS5_R3,        	  PS5_L1,        	  PS5_L2,        	  PS5_L3,        	  PS5_TRIANGLE,  	  PS5_CIRCLE,    	  PS5_CROSS,     	  PS5_SQUARE     }
 ;
 function DA187() {
 	DA1019 = sizeof(data);
 	DA509 = get_pvar(SPVAR_1,0x0,0x1,0x0);
 	DA515 = get_pvar(SPVAR_2,0x0,0x1,0x0);
 	DA508 = get_pvar(SPVAR_3,0x2B67, 0x1869F,0x2B67);
 	DA189();
 	if (DA214(0x0, 0x1, 0x0)) {
 		DA367 = DA214(  0x0, 0x6, 0x0);
 		DA364 = DA214(0x0, 0x3, 0x0);
 		DA365 = DA214(0x0,0x1,0x0);
 		DA366 = DA214(0x0,0x1,0x0);
 		DA277 = DA214(0x0, 0x46, 0x0);
 		DA278 = DA214(0x0, 0x46, 0x0);
 		DA279 = DA214(0x0, 0x46, 0x0);
 		DA280 = DA214(0x0, 0x46, 0x0);
 		DA281 = DA214(0x0, 0x16, 0x8);
 		DA368 = DA214(0x0, 0x46, 0x0);
 		DA369 = DA214(0x0, 0x46, 0x0);
 		DA370 = DA214(0x0, 0x46, 0x0);
 		DA371 = DA214(0x0, 0x46, 0x0);
 		DA372 = DA214(0x0, 0x1, 0x0);
 		DA373 = DA214(0x0, 0x1, 0x0);
 		DA374 = DA214(0x0, 0x1, 0x0);
 		DA375 = DA214(0x0, 0x1, 0x0);
 		DA383 = DA214(0x0, 0x1, 0x0);
 		DA409 = DA214(0x0, 0x46, 0x0);
 		DA410 = DA214(0x0, 0x46, 0x0);
 		DA411 = DA214(0x0, 0x46, 0x0);
 		DA412 = DA214(0x0, 0x46, 0x0);
 		DA413 = DA214(0x0, 0x46, 0x0);
 		DA414 = DA214(0x1, 0x19, 0x1);
 		DA415 = DA214(0x0, 0x1, 0x0);
 		DA416 = DA214(0x0, 0x1, 0x0);
 		DA417 = DA214(0x1, 0x19, 0x5);
 		DA418 = DA214(0x0, 0x1, 0x0);
 		DA419 = DA214(0x0, 0x1, 0x0);
 		DA420 = DA214(0x0, 0x19, 0x2);
 		DA421 = DA214(0x0, 0x1, 0x0);
 		DA422 = DA214(0x0, 0x1, 0x1);
 		DA423 = DA214(0x1, 0x19, 0x8);
 		DA424 = DA214(0x0, 0x1, 0x0);
 		DA425 = DA214(0x0, 0x1, 0x1);
 		DA426 = DA214(0x15E, 0x258, 0x15E);
 		DA427 = DA214(0x15E, 0x258, 0x1BD);
 		DA428 = DA214(0x0, 0x16, 0x0);
 		DA429 = DA214(0x0, 0x1, 0x0);
 		DA430 = DA214(inv(0x64), 0x12C, 0x0);
 		DA376 = DA214(0x0, 0x1, 0x0);
 		DA377 = DA214(0x0, 0x1, 0x0);
 		DA378 = DA214(0x0, 0x1, 0x0);
 		DA379 = DA214(0x0, 0x1, 0x0);
 		DA380 = DA214(0x0, 0x1, 0x0);
 		DA431 = DA214(inv(0x96), 0x96, 0x0);
 		DA432 = DA214(inv(0x96), 0x96, 0x0);
 		DA433 = DA214(0x0, 0x1, 0x0);
 		DA434 = DA214(inv(0x96), 0x96, 0x0);
 		DA435 = DA214(0x0, 0x16, 0x0);
 		DA436 = DA214(0x0, 0x16, 0x0);
 		DA437 = DA214(0x0, 0x16, 0x0);
 		DA438 = DA214(0x0, 0x16, 0x0);
 		DA611 = DA214(0x3C, 0x190, 0xEB);
 		DA440 = DA214(0x0, 0x1, 0x0);
 		DA439 = DA214(0x0, 0x1, 0x0);
 		DA363 = DA214(0x0, 0x3, 0x0);
 		DA388 = DA214(0x0, 0x46, 0x0);
 		DA389 = DA214(0x0, 0x46, 0x0);
 		DA390 = DA214(0x0, 0x46, 0x0);
 		DA393 = DA214(0x0, 0x46, 0x0);
 		DA394 = DA214(0x0, 0x46, 0x0);
 		DA395 = DA214(0x0, 0x46, 0x0);
 		DA396 = DA214(0x0, 0x16, 0x8);
 		DA381 = DA214(0x0, 0x1, 0x0);
 		DA391 = DA214(0x0, 0x46, 0x0);
 		DA392 = DA214(0x0, 0x46, 0x0);
 		DA594 = DA214(0x32, 0x3E8, 0x1F4);
 		DA1263 = DA214(0x0, 0x1, 0x0);
 		DA1256 = DA214(0x0, 0x1, 0x0);
 		DA111 = DA214(0x0, 0x6, 0x0);
 		DA408 = DA214(0x0, 0x1, 0x0);
 		DA362 = DA214(0x0, 0x2, 0x0);
 		DA441 = DA214(0x0, 0x9, 0x9);
 		DA442 = DA214(0x0, 0x9, 0x8);
 		DA443 = DA214(0x0, 0x9, 0x3);
 		DA444 = DA214(0x0, 0x9, 0x1);
 		DA445 = DA214(0x0, 0x9, 0x4);
 		DA446 = DA214(0x0, 0x9, 0x0);
 		DA447 = DA214(0x0, 0x9, 0x7);
 		DA448 = DA214(0x0, 0x9, 0x6);
 		DA384    = DA214(0x0, 0x1, 0x0);
 		DA385    = DA214(0x0, 0x1, 0x0);
 		DA386     = DA214(0x0, 0x1, 0x0);
 		DA397     = DA214(0x3C, 0x1F4, 0x78);
 		DA398     = DA214(0x3C, 0x1F4, 0x15E);
 		DA399    = DA214(0x0, 0x1, 0x0);
 		DA400 = DA214(0x0, 0x1, 0x0);
 		DA401     = DA214(0x32, 0xFA, 0x50);
 		DA402     = DA214(0x64, 0x352, 0xB4);
 		DA403 = DA214(0x0, 0x1, 0x0);
 		DA404    = DA214(0x0, 0x1, 0x0);
 		DA405        = DA214(0x50, 0x1F4, 0x78);
 		DA406        = DA214(0x50, 0x1F4, 0x15E);
 		DA407       = DA214(0x0, 0x1, 0x0);
 		DA457           = DA214(0x0, 0x9C4, 0x2EE);
 		DA29           = DA214(0x0, 0x1, 0x0);
 		DA477         = DA214(0x0, 0x1, 0x0);
 		DA475       = DA214(0x0, 0x1, 0x0);
 	}
 	else{
 		DA367 = 0x0;
 		DA364 = 0x0;
 		DA365 = 0x0;
 		DA366 = 0x0;
 		DA277 = 0x0;
 		DA278 = 0x0;
 		DA279 = 0x0;
 		DA280 = 0x0;
 		DA281 = 0x8;
 		DA368 = 0x0;
 		DA369 = 0x0;
 		DA370 = 0x0;
 		DA371 = 0x0;
 		DA372 = 0x0;
 		DA373 = 0x0;
 		DA374 = 0x0;
 		DA375 = 0x0;
 		DA383 = 0x0;
 		DA409 = 0x0;
 		DA410 = 0x0;
 		DA411 = 0x0;
 		DA412 = 0x0;
 		DA413 = 0x0;
 		DA414 = 0x1;
 		DA415 = 0x0;
 		DA416 = 0x0;
 		DA417 = 0x5;
 		DA418 = 0x0;
 		DA419 = 0x0;
 		DA420 = 0x2;
 		DA421 = 0x0;
 		DA422 = 0x1;
 		DA423 = 0x8;
 		DA424 = 0x0;
 		DA425 = 0x1;
 		DA426 = 0x15E;
 		DA427 = 0x1BD;
 		DA428 = 0x0;
 		DA429 = 0x0;
 		DA430 = 0x0;
 		DA376 = 0x0;
 		DA377 = 0x0;
 		DA378 = 0x0;
 		DA379 = 0x0;
 		DA380 = 0x0;
 		DA431 = 0x0;
 		DA432 = 0x0;
 		DA433 = 0x0;
 		DA434 = 0x0;
 		DA435 = 0x0;
 		DA436 = 0x0;
 		DA437 = 0x0;
 		DA438 = 0x0;
 		DA611 = 0xEB;
 		DA440 = 0x0;
 		DA439 = 0x0;
 		DA363 = 0x0;
 		DA388 = 0x0;
 		DA389 = 0x0;
 		DA390 = 0x0;
 		DA393 = 0x0;
 		DA394 = 0x0;
 		DA395 = 0x0;
 		DA396 = 0x8;
 		DA381 = 0x0;
 		DA391 = 0x0;
 		DA392 = 0x0;
 		DA594 = 0x1F4;
 		DA1263 = 0x0;
 		DA1256 = 0x0;
 		DA111 = 0x0;
 		DA408 = 0x0;
 		DA362 = 0x0;
 		DA441 = 0x9;
 		DA442 = 0x8;
 		DA443 = 0x3;
 		DA444 = 0x1;
 		DA445 = 0x4;
 		DA446 = 0x0;
 		DA447 = 0x7;
 		DA448 = 0x6;
 		DA384 = 0x0;
 		DA385 = 0x0;
 		DA386 = 0x0;
 		DA397 = 0x78;
 		DA398 = 0x15E;
 		DA399 = 0x0;
 		DA400 = 0x0;
 		DA401 = 0x50;
 		DA402 = 0xB4;
 		DA403 = 0x0;
 		DA404 = 0x0;
 		DA405 = 0x78;
 		DA406 = 0x168;
 		DA407 = 0x0;
 		DA457     = 0x2EE;
 		DA29     = 0x0;
 		DA477     = 0x0;
 		DA475     = 0x0;
 			}
 	if (DA362 == 0x0) {
 		DA449 = PS5_CIRCLE;
 		DA450 = PS5_CROSS;
 		DA451 = PS5_L1;
 		DA452 = PS5_R1;
 		DA453 = PS5_L2;
 		DA454 = PS5_R2;
 		DA455 = PS5_SQUARE;
 		DA456 = PS5_TRIANGLE;
 			}
 	else if (DA362 == 0x1) {
 		DA449      = PS5_SQUARE;
 		DA450      = PS5_CROSS ;
 		DA451    = PS5_L1    ;
 		DA452  = PS5_R1;
 		DA453    = PS5_L2;
 		DA454    = PS5_R2;
 		DA455     = PS5_CIRCLE;
 		DA456  = PS5_TRIANGLE;
 	}
 	else if (DA362 == 0x2) {
 		DA449 = DA1386[DA441];
 		DA450 = DA1386[DA442];
 		DA451 = DA1386[DA443];
 		DA452 = DA1386[DA444];
 		DA453 = DA1386[DA445];
 		DA454 = DA1386[DA446];
 		DA455 = DA1386[DA447];
 		DA456 = DA1386[DA448];
 			}
 	}
 function DA188() {
 	DA189();
 	DA212(   1,0,     1);
 	DA212(DA367, 0, 6);
 	DA212(DA364, 0, 3);
 	DA212(DA365, 0 , 1);
 	DA212(DA366, 0 , 1);
 	DA212(DA277, 0, 70);
 	DA212(DA278, 0, 70);
 	DA212(DA279, 0, 70);
 	DA212(DA280, 0, 70);
 	DA212(DA281, 0, 22);
 	DA212(DA368, 0, 70);
 	DA212(DA369, 0, 70);
 	DA212(DA370, 0, 70);
 	DA212(DA371, 0, 70);
 	DA212(DA372, 0, 1);
 	DA212(DA373, 0, 1);
 	DA212(DA374, 0, 1);
 	DA212(DA375, 0, 1);
 	DA212(DA383, 0, 1);
 	DA212(DA409, 0, 70);
 	DA212(DA410, 0, 70);
 	DA212(DA411, 0, 70);
 	DA212(DA412, 0, 70);
 	DA212(DA413, 0, 70);
 	DA212(DA414, 1, 25);
 	DA212(DA415, 0, 1);
 	DA212(DA416, 0, 1);
 	DA212(DA417, 1, 25);
 	DA212(DA418, 0, 1);
 	DA212(DA419, 0, 1);
 	DA212(DA420, 0, 25);
 	DA212(DA421, 0, 1);
 	DA212(DA422, 0, 1);
 	DA212(DA423, 1, 25);
 	DA212(DA424, 0, 1);
 	DA212(DA425, 0, 1);
 	DA212(DA426, 350, 600);
 	DA212(DA427, 350, 600);
 	DA212(DA428, 0, 22);
 	DA212(DA429, 0, 1);
 	DA212(DA430, -100, 300);
 	DA212(DA376, 0, 1);
 	DA212(DA377, 0, 1);
 	DA212(DA378, 0, 1);
 	DA212(DA379, 0, 1);
 	DA212(DA380, 0, 1);
 	DA212(DA431, -150, 150);
 	DA212(DA432, -150, 150);
 	DA212(DA433, 0, 1);
 	DA212(DA434, -150, 150);
 	DA212(DA435, 0, 22);
 	DA212(DA436, 0, 22);
 	DA212(DA437, 0, 22);
 	DA212(DA438, 0, 22);
 	DA212(DA611, 60, 400);
 	DA212(DA440, 0, 1);
 	DA212(DA439, 0, 1);
 	DA212(DA363, 0, 3);
 	DA212(DA388, 0, 70);
 	DA212(DA389, 0, 70);
 	DA212(DA390, 0, 70);
 	DA212(DA393, 0, 70);
 	DA212(DA394, 0, 70);
 	DA212(DA395, 0, 70);
 	DA212(DA396, 0, 22);
 	DA212(DA381, 0, 1);
 	DA212(DA391, 0, 70);
 	DA212(DA392, 0, 70);
 	DA212(DA594, 50, 1000);
 	DA212(DA1263, 0, 1);
 	DA212(DA1256, 0, 1);
 	DA212(DA111, 0, 6);
 	DA212(DA408, 0, 1);
 	DA212(DA362, 0, 2);
 	DA212(DA441, 0, 9);
 	DA212(DA442, 0, 9);
 	DA212(DA443, 0, 9);
 	DA212(DA444, 0, 9);
 	DA212(DA445, 0, 9);
 	DA212(DA446, 0, 9);
 	DA212(DA447, 0, 9);
 	DA212(DA448, 0, 9);
 	DA212(DA384,    0, 1);
 	DA212(DA385,    0, 1);
 	DA212(DA386,     0, 1);
 	DA212(DA397,     60, 500);
 	DA212(DA398,     60, 500);
 	DA212(DA399,    0, 1);
 	DA212(DA400, 0, 1);
 	DA212(DA401,     50, 250);
 	DA212(DA402,     100, 850);
 	DA212(DA403, 0, 1);
 	DA212(DA404,    0, 1);
 	DA212(DA405,        80, 500);
 	DA212(DA406,        80, 500);
 	DA212(DA407,       0, 1);
 	DA212(DA457 ,         0,2500);
 	DA212(DA29,           0,1);
 	DA212(DA477,           0,1);
 	DA212(DA475,           0,1);
 	}
 function DA189() {
 	DA1026 = SPVAR_4;
 	DA1027 = 0;
 	DA1029 = 0;
 	}
 int DA1027,  DA1026, DA1029, DA1030, DA1031;
 function DA190(DA191) {
 	DA1030 = 0;
 	while (DA191) {
 		DA1030++;
 		DA191 = abs(DA191 >> 1);
 	}
 	return DA1030;
 	}
 function DA192(DA193, DA194) {
 	DA1030 = max(DA190(DA193), DA190(DA194));
 	if (DA195(DA193, DA194)) {
 		DA1030++;
 	}
 	return DA1030;
 	}
 function DA195(DA193, DA194) {
 	return DA193 < 0 || DA194 < 0;
 	}
 function DA198(DA199) {
 	return 1 << clamp(DA199 - 1, 0, 31);
 	}
 function DA200(DA199) {
 	if (DA199 == 32) {
 		return -1;
 			}
 	return 0x7FFFFFFF >> (31 - DA199);
 }
 function DA202(DA199) {
 	return DA200(DA199 - 1);
 	}
 function DA204(DA191, DA199) {
 	if (DA191 < 0) {
 		return (abs(DA191) & DA202(DA199)) | DA198(DA199);
 	}
 	return DA191 & DA202(DA199);
 }
 function DA207(DA191, DA199) {
 	if (DA191 & DA198(DA199)) {
 		return 0 - (DA191 & DA202(DA199));
 	}
 	return DA191 & DA202(DA199);
 }
 function DA210(DA211) {
 	return get_pvar(DA211, 0x80000000, 0x7FFFFFFF, 0);
 	}
 function DA212(DA191, min, max) {
 	DA1031 = DA192(min, max);
 	DA191 = clamp(DA191, min, max);
 	if (DA195(min, max)) {
 		DA191 = DA204(DA191, DA1031);
 	}
 	DA191 = DA191 & DA200(DA1031);
 	if (DA1031 >= 32 - DA1027) {
 		DA1029 = DA1029 | (DA191 << DA1027);
 		set_pvar(DA1026, DA1029);
 		DA1026++;
 		DA1031 -= (32 - DA1027);
 		DA191 = DA191 >> (32 - DA1027);
 		DA1027 = 0;
 		DA1029 = 0;
 	}
 	DA1029 = DA1029 | (DA191 << DA1027);
 	DA1027 += DA1031;
 	if (!DA1027) {
 		DA1029 = 0;
 	}
 	set_pvar(DA1026, DA1029);
 }
 function DA214(min, max, DA215) {
 	DA1031 = DA192(min, max);
 	DA1029 = (DA210(DA1026) >> DA1027) & DA200(DA1031);
 	if (DA1031 >= 32 - DA1027) {
 		DA1029 = (DA1029 & DA200(32 - DA1027)) | ((DA210(DA1026 + 1) & DA200(DA1031 - (32 - DA1027))) << (32 - DA1027));
 	}
 	DA1027 += DA1031;
 	DA1029 = DA1029 & DA200(DA1031);
 	if (DA1027 >= 32) {
 		DA1026++;
 		DA1027 -= 32;
 	}
 	if (DA195(min, max)) {
 		DA1029 = DA207(DA1029, DA1031);
 	}
 	if (DA1029 < min || DA1029 > max) {
 		return DA215;
 	}
 		if(DA217[283] != 7590){
 		DA214(min, max, DA215);
 	}
 	return DA1029;
 	}
 const string DA1057 = "SETTINGS";
 const string DA1058 = "WAS SAVED";
 combo DA75 {
 	vm_tctrl(0);
 	wait( 20);
 	cls_oled(0);
 	DA188();
 	print(15, 2, OLED_FONT_MEDIUM, 1, DA1057[0]);
 	print(10, 23, OLED_FONT_MEDIUM, 1, DA1058[0]);
 	DA1059 = 1500;
 	combo_run(DA76);
 	}
 int DA1059 = 1500;
 combo DA76 {
 	vm_tctrl(0);
 	wait( DA1059);
 	cls_oled(0);
 	DA360 = FALSE;
 	}
 define DA1060 = 0;
 define DA1061 = 1;
 define DA1062 = 2;
 define DA1063 = 3;
 define DA1064 = 4;
 define DA1065 = 5;
 define DA1066 = 6;
 define DA1067 = 7;
 int DA691;
 int DA1069;
 int DA1070, DA674;
 int DA480;
 int DA1073 = 50;
 int DA1074 = 200;
 int DA1075 = TRUE;
 combo DA77 {
 	set_val(DA450, 0);
 	set_val(PS5_L3, 100);
 	set_val(PS5_R3, 100);
 	vm_tctrl(0);
 	wait( 60);
 	set_val(DA450, 0);
 	vm_tctrl(0);
 	wait( 120);
 	if (DA429) DA260();
 	vm_tctrl(0);
 	wait( 50);
 	vm_tctrl(0);
 	wait( 50);
 	}
 int DA618;
 int DA625;
 combo DA78 {
 	if (DA625) set_val(XB1_LX, 100);
 	else set_val(XB1_LX, -100);
 	vm_tctrl(0);
 	wait( 70);
 	if (DA625) set_val(XB1_RX, 100);
 	else set_val(XB1_RX, -100);
 	set_val(XB1_RY, 100);
 	vm_tctrl(0);
 	wait( 2000);
 	if (DA625) set_val(XB1_RX, -100);
 	else set_val(XB1_RX, 100);
 	vm_tctrl(0);
 	wait( 50);
 	vm_tctrl(0);
 	wait( 200);
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( DA426);
 	if (DA625) set_val(XB1_LX, 100);
 	else set_val(XB1_LX, 100);
 	set_val(XB1_LY,100);
 	vm_tctrl(0);
 	wait( 50);
 	vm_tctrl(0);
 	wait( 1200);
 	DA618 = FALSE;
 	DA244(DA618);
 	}
 int DA627;
 int DA628;
 combo DA79 {
 	if (DA628) set_val(XB1_RX, -100);
 	else set_val(XB1_RX, 100);
 	set_val(XB1_RY, 100);
 	vm_tctrl(0);
 	wait( 320);
 	vm_tctrl(0);
 	wait( 50);
 	set_val(XB1_RY, -60);
 	vm_tctrl(0);
 	wait( 1100);
 	vm_tctrl(0);
 	wait( 50);
 	if (DA628) set_val(XB1_LX, 60);
 	else set_val(XB1_LX, -60);
 	vm_tctrl(0);
 	wait( 120);
 	vm_tctrl(0);
 	wait( 50);
 	set_val(XB1_LY, -100);
 	set_val(DA455, 100);
 	set_val(DA452, 100);
 	set_val(DA453, 100);
 	DA1210 = 4000;
 	vm_tctrl(0);
 	wait( DA427);
 	vm_tctrl(0);
 	wait( 50);
 	set_val(DA455, 100);
 	vm_tctrl(0);
 	wait( 50);
 	DA627 = FALSE;
 	DA244(DA627);
 	}
 int DA1080 = TRUE;
 function DA216(DA217) {
 	if (DA217) {
 		DA1081 = DA1113;
 			}
 	else {
 		DA1081 = DA1112;
 			}
 	combo_run(DA80);
 	}
 int DA1081;
 combo DA80 {
 	DA237(DA1081);
 	vm_tctrl(0);
 	wait( 300);
 	DA237(DA1110);
 	vm_tctrl(0);
 	wait( 100);
 	DA237(DA1081);
 	vm_tctrl(0);
 	wait( 300);
 	DA237(DA1110);
 	}
 define DA1085 = 100;
 define DA1086 = 130;
 const string DA554 = "SCRIPT WAS";
 function DA218(DA128, DA220, DA221) {
 	if (!DA354 && !DA355) {
 		cls_oled(0);
 		print(DA220, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA221);
 		if (DA128) {
 			print(DA222(sizeof(DA1090) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA1090[0]);
 		}
 		else {
 			print(DA222(sizeof(DA1091) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA1091[0]);
 		}
 		DA216(DA128);
 			}
 	}
 function DA222(DA143, DA137) {
 	return (OLED_WIDTH / 2) - ((DA143 * DA137) / 2);
 	}
 const string DA1091 = "OFF";
 const string DA1090 = "ON";
 function DA225(DA134, DA227, DA128) {
 	cls_oled(0);
 	line_oled(1, 18, 127, 18, 1, 1);
 	print(DA134, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA227);
 	DA232(DA128, DA235(DA128));
 	DA358 = TRUE;
 	}
 const string DA598 = "EA PING";
 const string DA620 = "FK_POWER";
 const string DA612 = "MaxFnshPwr"const string DA604 = "JK_Agg";
 int DA594;
 int DA611;
 function DA229(DA143, DA137) {
 	return (OLED_WIDTH / 2) - ((DA143 * DA137) / 2);
 	}
 int DA1100;
 int DA1101, DA1102;
 function DA232(DA128, DA160) {
 	DA1100 = 1;
 	DA1102 = 10000;
 	if (DA128 < 0) {
 		putc_oled(DA1100, 45);
 		DA1100 += 1;
 		DA128 = abs(DA128);
 			}
 	for (DA1101 = 5;
 	DA1101 >= 1;
 	DA1101--) {
 		if (DA160 >= DA1101) {
 			putc_oled(DA1100, (DA128 / DA1102) + 48);
 			DA128 %= DA1102;
 			DA1100++;
 			if (DA1101 == 4) {
 				putc_oled(DA1100, 44);
 				DA1100++;
 							}
 					}
 		DA1102 /= 10;
 			}
 	puts_oled(DA229(DA1100 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA1100 - 1, OLED_WHITE);
 	}
 int DA1106;
 function DA235(DA236) {
 	DA1106 = 0;
 	do {
 		DA236 /= 10;
 		DA1106++;
 			}
 	while (DA236);
 	return DA1106;
 	}
 int DA634;
 define DA1110 = 0;
 define DA1111 = 1;
 define DA1112 = 2;
 define DA1113 = 3;
 define DA1114 = 4;
 define DA1115 = 5;
 define DA1116 = 6;
 define DA1117 = 7;
 const int16 data[][] = {
 	{
 		0,    0,    0   	}
 	,  	  {
 		0,    0,    255   	}
 	,  	  {
 		255,    0,    0   	}
 	,  	  {
 		0,    255,    0   	}
 	,  	  {
 		255,    0,    255   	}
 	,  	  {
 		0,    255,    255   	}
 	,  	  {
 		255,    255,    0   	}
 	,  	  {
 		255,    255,    255   	}
 }
 ;
 int DA1118;
 function DA237(DA170) {
 	for (DA1118 = 0;
 	DA1118 < 3;
 	DA1118++) {
 		set_rgb(data[DA170][0], data[DA170][1], data[DA170][2]);
 			}
 	}
 const int8 DA1396[] = {
 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS5_R1,  	  PS5_R2,  	  XB1_RS,  	  PS5_L1,  	  PS5_L2,  	  XB1_LS,  	  PS5_UP,  	  PS5_DOWN,  	  PS5_LEFT,  	  PS5_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS5_TOUCH  }
 int DA637 = PS5_L3;
 define DA1120 = 1;
 define DA1121 = 2;
 define DA1122 = 3;
 define DA1123 = 2;
 define DA1124 = 3;
 define DA1125 = 4;
 define DA1126 = 5;
 define DA1127 = 6;
 define DA1128 = 7;
 define DA1129 = 8;
 define DA1130 = 9;
 int DA631 = FALSE;
 int DA1132;
 int DA1133;
 int DA1134;
 int DA1135;
 define DA1136 = PS5_LX;
 define DA1137 = PS5_LY;
 define DA1138 = PS5_RX;
 define DA1139 = PS5_RY;
 function DA239 () {
 	if(!get_ival(XB1_RS) &&  !get_ival(DA453) && !get_ival(DA454) && !get_ival(DA452)) {
 		if( get_ival(PS5_RY) < -70  && !DA1132 && !combo_running(DA0) ) {
 			DA1132 = TRUE;
 			DA480 = FALSE;
 			DA1069 = DA277;
 			            DA242(DA277);
 		}
 		if( get_ival(PS5_RY) >  70  && !DA1133 && !combo_running(DA0)) {
 			DA1133 = TRUE;
 			DA480 = TRUE;
 			DA1069 = DA279;
 			           DA242(DA279);
 		}
 		if( get_ival(PS5_RX) < -70  && !DA1134 && !combo_running(DA0) ) {
 			DA1134 = TRUE;
 			DA480 = FALSE;
 			DA1069 = DA280;
 			              DA242(DA280);
 		}
 		if( get_ival(PS5_RX) >  70  && !DA1135 && !combo_running(DA0) ) {
 			DA1135 = TRUE;
 			DA480 = TRUE;
 			DA1069 = DA278;
 			            DA242(DA278);
 		}
 			}
 	if(abs(get_ival(PS5_RY))<20  && abs(get_ival(PS5_RX))<20){
 		DA1132 = 0;
 		DA1133  = 0;
 		DA1134  = 0;
 		DA1135  = 0;
 			}
 	}
 function DA240() {
 	if (DA503 == DA596) {
 		DA480 = FALSE;
 		if (DA388) DA242(DA388);
 			}
 	if (DA503 == DA247(DA596 + 4)) {
 		DA480 = FALSE;
 		if (DA395) DA242(DA395);
 			}
 	if (DA503 == DA247(DA596 + 1)) {
 		DA480 = TRUE;
 		if (DA390) DA242(DA390);
 			}
 	if (DA503 == DA247(DA596 - 1)) {
 		DA480 = FALSE;
 		if (DA389) DA242(DA389);
 			}
 	if (DA503 == DA247(DA596 + 2)) {
 		DA480 = TRUE;
 		if (DA392) DA242(DA392);
 			}
 	if (DA503 == DA247(DA596 - 2)) {
 		DA480 = FALSE;
 		if (DA391) DA242(DA391);
 			}
 	if (DA503 == DA247(DA596 + 3)) {
 		DA480 = TRUE;
 		if (DA394) DA242(DA394);
 			}
 	if (DA503 == DA247(DA596 - 3)) {
 		DA480 = FALSE;
 		if (DA393) DA242(DA393);
 			}
 	}
 int DA1153;
 int DA501 = 0;
 function DA241() {
 	if(DA1153){
 		DA501 += get_rtime();
 			}
 	if(DA501 >= 3000){
 		DA501 = 0;
 		DA1153 = FALSE;
 			}
 	if (!get_ival(XB1_RS) && !get_ival(DA453) && !get_ival(DA454) && !get_ival(DA452) && !get_ival(DA451)) {
 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA504 && !combo_running(DA0)) {
 			DA504 = TRUE;
 			DA1153 = TRUE;
 			DA501 = 0;
 			DA503 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
 			DA240();
 					}
 		set_val(DA1138, 0);
 		set_val(DA1139, 0);
 			}
 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
 		DA504 = FALSE;
 			}
 	}
 function DA242(DA243) {
 	DA1069 = DA243;
 	DA217[-330 + (DA243 * 3)] = TRUE;
 	DA1075 = FALSE;
 	block = TRUE;
 	}
 int DA1160;
 combo DA81 {
 	set_rumble(DA1160, 100);
 	vm_tctrl(0);
 	wait( 300);
 	reset_rumble();
 	vm_tctrl(0);
 	wait( 20);
 	}
 function DA244(DA128) {
 	if (DA128) DA1160 = RUMBLE_A;
 	else DA1160 = RUMBLE_B;
 	combo_run(DA81);
 	}
 int DA1161 = 300;
 int DA1162 ;
 combo DA82 {
 	DA1162 = TRUE;
 	vm_tctrl(0);
 	wait( DA1161);
 	DA1162 = FALSE;
 	}
 combo DA83 {
 	vm_tctrl(0);
 	wait( 45);
 	set_val(PS5_RX, 0);
 	set_val(PS5_RY, 0);
 	vm_tctrl(0);
 	wait( 160);
 	}
 combo DA84 {
 	DA246();
 	DA254(0, 0);
 	vm_tctrl(0);
 	wait( 20);
 	DA254(0, 0);
 	vm_tctrl(0);
 	wait( 100);
 	DA254(0, 0);
 	set_val(DA454, 100);
 	DA254(0, 0);
 	vm_tctrl(0);
 	wait( 60);
 	DA254(0, 0);
 	vm_tctrl(0);
 	wait( 150);
 	DA1075 = TRUE;
 	vm_tctrl(0);
 	wait( 350);
 	}
 function DA246() {
 	DA691 = DA596  DA249(DA691);
 	DA1070 = DA1166;
 	DA674 = DA677;
 	}
 combo DA85 {
 	set_val(DA453, 100);
 	set_val(DA452, 100);
 	vm_tctrl(0);
 	wait( 100);
 	set_val(DA453, 100);
 	vm_tctrl(0);
 	wait( 100);
 	DA1075 = TRUE;
 	vm_tctrl(0);
 	wait( 350);
 	}
 const int8 DA1397[][] = {
 {
 		0,    -99   	}
 	,  	  {
 		98,    -100   	}
 	,  	  {
 		97,    0   	}
 	,  	  {
 		96,    99   	}
 	,  	  {
 		0,    99   	}
 	,  	  {
 		-96,    98   	}
 	,  	  {
 		-95,    0   	}
 	,  	  {
 		-94,    -96   	}
 }
 ;
 int DA1166, DA677, DA596;
 int DA503;
 int DA504;
 int DA1171;
 function DA247(DA248) {
 	DA1171 = DA248;
 	if (DA1171 < 0) DA1171 = 8 - abs(DA248);
 	else if (DA1171 >= 8) DA1171 = DA248 - 8  return DA1171;
 	}
 function DA249(DA250) {
 	if (DA250 < 0) DA250 = 8 - abs(DA250);
 	else if (DA250 >= 8) DA250 = DA250 - 8;
 	DA1166 = DA1397[DA250][0];
 	DA677 = DA1397[DA250][1];
 }
 function DA251(DA252, DA253) {
 	set_val(DA1138, DA252);
 	set_val(DA1139, DA253);
 	}
 function DA254(DA167, DA168) {
 	set_val(DA1136, DA167);
 	set_val(DA1137, DA168);
 	}
 function DA257() {
 	if (DA480) {
 		set_val(DA1136, inv(DA674));
 		set_val(DA1137, DA1070);
 			}
 	else {
 		set_val(DA1136, DA674);
 		set_val(DA1137, inv(DA1070));
 			}
 	}
 function DA258() {
 	if (DA480) {
 		set_val(DA1138, inv(DA674));
 		set_val(DA1139, DA1070);
 			}
 	else {
 		set_val(DA1138, DA674);
 		set_val(DA1139, inv(DA1070));
 			}
 	}
 function DA259() {
 	if (!DA480) {
 		set_val(DA1138, inv(DA674));
 		set_val(DA1139, DA1070);
 			}
 	else {
 		set_val(DA1138, DA674);
 		set_val(DA1139, inv(DA1070));
 			}
 	}
 function DA260() {
 	set_val(DA1138, DA1070);
 	set_val(DA1139, DA674);
 	}
 function DA261() {
 	set_val(DA1138, inv(DA1070));
 	set_val(DA1139, inv(DA674));
 	}
 function DA262() {
 	set_val(DA1138, 0);
 	set_val(DA1139, 0);
 	}
 int DA1187;
 function DA263() {
 	if ((event_press(DA450)  ) && !combo_running(DA86) && (DA1210  <= 0 || (DA1210 < 3000 && DA1210 > 1  )) && !get_ival(DA454) && DA556 > 500 &&!get_ival(DA453) &&!get_ival(DA449) &&!get_ival(DA452) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA86) ) {
 		combo_run(DA86);
 			}
 	if (combo_running(DA86) && (        get_ival(DA454) ||        get_ival(DA453) ||        get_ival(DA449) ||        get_ival(DA452) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_ipolar(POLAR_RS,POLAR_RADIUS) >= 1500      )) {
 		combo_stop(DA86);
 		DA1327 = TRUE;
 			}
 	}
 combo DA86 {
 vm_tctrl(0);
 wait(750);
 set_val(DA451,100);
 vm_tctrl(0);
 wait(60);
 vm_tctrl(0);
 wait(60);
 if(DA1187 == 1 ){
 set_val(XB1_RX,100)}
else{
set_val(XB1_RX,-100)}
 vm_tctrl(0);
 wait(60);
 vm_tctrl(0);
 wait(60);
 	}
 combo DA87 {
 	vm_tctrl(0);
 	wait( 800);
 	DA1211 = 0;
 	}
 int DA1188 = 1600;
 int DA1189 = 1600;
 int DA1190 = 1600;
 int DA1191 = TRUE;
 int DA1192 = TRUE;
 int DA668 = FALSE;
 int DA1194 = TRUE;
 int DA662 = FALSE;
 int DA1196 = TRUE;
 int DA664 = FALSE;
 int DA1198 = TRUE;
 int DA666 = FALSE;
 function DA264(){
 	if (get_ival(DA451)) {
 		DA1200 = 1000;
 		DA1219 = 0;
 		DA556 = 1;
 		combo_stop(DA96);
 			}
 	if (event_press(DA453) || event_press(DA436)) {
 		DA1200 = 4000;
 		DA1219 = 0;
 		DA1188 = 1600;
 			}
 	if (get_ival(DA452) && !get_ival(DA451) ) {
 		DA1200 = 0;
 		DA1219 = 0;
 		DA1188 = 1600;
 			}
 	else if (get_ival(DA451)){
 		DA1200 = 1000;
 			}
 	if (DA1200 > 0) {
 		DA1200 -= get_rtime();
 			}
 	if (DA1200 < 0) {
 		DA1200 = 0;
 			}
 	DA1280 = DA430;
 	if (event_release(DA450)) {
 		DA1206 = 1;
 		DA1219 = 0;
 		DA556 = 1;
 	}
 	if (event_release(DA456)) {
 		DA1207 = 1;
 		DA1219 = 0;
 		DA556 = 1;
 	}
 	if (event_release(DA451)) {
 		DA1189 = 1;
 		DA1219 = 0;
 		DA1188 = 1600;
 			}
 	if (event_release(DA452)) {
 		DA1190 = 1;
 		DA1219 = 0;
 		DA1188 = 1600;
 			}
 	if (event_release(DA455) || (get_ival(DA456) && get_ival(DA451))) {
 		DA1210 = 4000;
 		DA1219 = 0;
 	}
 	if (get_ival(DA450) && DA1210 < 4000 && DA1210 > 3500) {
 		DA1211 = DA1210;
 		DA1210 = 0;
 			}
 	if (DA1188 < 1510) {
 		DA1188 += get_rtime();
 			}
 	if (DA1189 < 1600) {
 		DA1189 += get_rtime();
 			}
 	if (DA1190 < 1600) {
 		DA1190 += get_rtime();
 			}
 	if (DA1210 > 0) {
 		DA1210 -= get_rtime();
 			}
 	if (DA1210 < 0) {
 		DA1210 = 0;
 			}
 	if (DA1206 < 5100) {
 		DA1206 += get_rtime();
 			}
 	if (DA1207 < 4100) {
 		DA1207 += get_rtime();
 			}
 	if (DA1219 > 0) {
 		DA1219 -= get_rtime();
 			}
 	if (DA1219 < 0) {
 		DA1219 = 0;
 			}
 	if (abs(get_ival(PS5_RX)) > 30 || abs(get_ival(PS5_RY)) > 30) {
 		DA1188 = 1;
 		DA1219 = 0;
 			}
 	if (combo_running(DA93)) {
 		set_val(DA450, 0);
 		if(get_ival(DA450)){
 			DA672 = 0;
 			combo_stop(DA88);
 			set_val(DA450, 0);
 			combo_stop(DA93);
 			combo_run(DA49);
 					}
 			}
 	if ((combo_running(DA98) || combo_running(DA89))) {
 		set_val(DA450, 0);
 		if(get_ival(DA450)){
 			DA556 = 1;
 			DA672 = 0;
 			combo_stop(DA88);
 			set_val(DA450, 0);
 			combo_stop(DA98);
 			combo_stop(DA89);
 			combo_run(DA49);
 					}
 			}
 	if (event_press(DA449)) {
 		combo_run(DA87);
 			}
 	if (DA556 > 1500) {
 		if (DA1189 < 1500) {
 			DA1224 = 120;
 					}
 		if (DA1190 < 1500) {
 			DA1224 = 228;
 					}
 		else {
 			DA1224 = 200;
 					}
 			}
 	if (DA556 < 1500) {
 		DA1224 = 450;
 			}
 	if (DA556 > 2700) {
 		DA1228 = 920;
 			}
 	else if (DA556 >= 0 && DA556 < 2700) {
 		DA1228 = 725;
 			}
 	}
 function DA265() {
 	if (DA1191) {
 		if ((DA556 <= 600 || (DA1188 <= 1500 && DA1188 > 1) || ( DA1189 <= 150 || DA1190 <= 150)) && event_press(DA449) ) {
 			if (!get_ival(DA452) && !get_ival(DA451) && !get_ival(DA453) && !get_ival(DA454)) {
 				set_val(DA449, 0);
 				if (DA1210 < 4000 && DA1210 > 1) {
 					set_val(DA449, 0);
 					combo_run(DA91);
 									}
 				else {
 					set_val(DA449, 0);
 					combo_run(DA89);
 					DA1219 = 9000;
 				}
 							}
 					}
 			}
 	if (DA1198) {
 		if (DA556 > 1000 && !DA1219 && (!get_ival(DA452) && !get_ival(PS5_L3) && event_press(DA449)) &&  DA1189 > 150 &&  DA1190 > 150) {
 			if (!get_ival(DA451) && !get_ival(DA453)) {
 				set_val(DA449, 0);
 				if (((DA1207 > 1 && DA1207 <= 2500) || (DA1206 > 1 && DA1206 <= 3000)) &&  DA1188 != 1600) {
 					set_val(DA449, 0);
 					combo_run(DA90);
 					DA1219 = 9000;
 									}
 				else if (((DA1207 > 2500 && DA1207 <= 4000) || (DA1206 > 3000 && DA1206 <= 3500))  &&  DA1188 != 1600) {
 					set_val(DA449, 0);
 					combo_run(DA89);
 					DA1219 = 9000;
 									}
 				else if ((DA1210 < 4000 && DA1210 > 1)) {
 					set_val(DA449, 0);
 					combo_run(DA91);
 					DA1219 = 9000;
 									}
 				else {
 					set_val(DA449, 0);
 					DA269();
 					DA1219 = 9000;
 									}
 				DA1219 = 9000;
 							}
 					}
 			}
 	if (DA1192) {
 		if (get_ival(DA451) && get_ival(DA452)) {
 			if (!get_ival(DA453) && !get_ival(DA454) && (DA1210 && DA1206 > 1 && DA1206 <= 1500) || (!DA1210 && DA1206 > 1 && DA1206 <= 1500) || (DA1206 > 1500 && !DA1210) && !DA1219) {
 				if (event_press(DA449)) {
 					set_val(DA449, 0);
 					combo_run(DA99);
 					DA1219 = 9000;
 									}
 							}
 					}
 			}
 	if (DA1196) {
 		if (!get_ival(DA454) && !get_ival(DA451) && !get_ival(DA452)) {
 			if (get_ival(DA453) && get_ival(DA449)) {
 				DA271();
 				set_val(DA449, 0);
 				DA1219 = 9000;
 							}
 					}
 			}
 	if (DA1194) {
 		if (get_ival(DA452) && !get_ival(DA451) && !DA1200) {
 			if (!get_ival(DA453) && !get_ival(DA454) && !DA1219) {
 				if (get_ival(DA449) && DA556 >= 1000) {
 					set_val(DA449, 0);
 					combo_run(DA96);
 					DA1219 = 9000;
 									}
 				if (get_ival(DA449) && DA556 < 1000 && !DA1200) {
 					set_val(DA449, 0);
 					combo_run(DA97);
 									}
 							}
 					}
 			}
 	if(combo_running(DA91)){
 		DA672 = 0;
 		combo_stop(DA88)   	}
 	if (get_ival(DA451) || DA1200 > 0) {
 		combo_stop(DA96);
 		combo_stop(DA98);
 		combo_stop(DA97);
 			}
 	if (combo_running(DA89) || combo_running(DA93) || combo_running(DA98) || combo_running(DA99) || combo_running(DA96)) {
 		if (get_ival(DA450) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA454) > 30) {
 			combo_stop(DA93);
 			combo_stop(DA98);
 			combo_stop(DA99);
 			combo_stop(DA96);
 			combo_stop(DA89);
 			DA672 = 0;
 			combo_stop(DA88)     		}
 			}
 	if (combo_running(DA89) || combo_running(DA90)) {
 		if (get_ival(DA450) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA454)) {
 			combo_stop(DA91);
 			combo_stop(DA90);
 			combo_stop(DA89);
 			DA672 = 0;
 			combo_stop(DA88)     		}
 			}
 	if (event_press(DA449) && DA1219 > 100 && DA1219 < 8990) {
 		set_val(DA449, 0);
 		combo_stop(DA93);
 		combo_stop(DA98);
 		combo_stop(DA99);
 		combo_stop(DA96);
 		combo_stop(DA89);
 		DA672 = 0;
 		combo_stop(DA88)    combo_run(DA92);
 			}
 	if (!DA668) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DA99);
 					}
 			}
 	if (!DA662) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DA96);
 					}
 			}
 	if (!DA664) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DA93);
 			DA672 = 0;
 			combo_stop(DA88)     		}
 			}
 	if (!DA666) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DA98);
 					}
 			}
 	if ((get_ival(DA454) || get_ival(DA450)) && !DA367) {
 		combo_stop(DA4);
 		combo_stop(DA47);
 		combo_stop(DA33);
 			}
 	}
 define DA1232 = 15;
 define DA1233 = 15;
 int DA1234 = 0;
 define DA1235 = 8000;
 define DA1236 = 4;
 define DA1237 = 2000;
 int DA672 = 0;
 const int16 DA1398[] = {
 	15, 20, 25 ,30,35    ,145,150 , 155, 160,165 ,    195, 200,205, 210,215,325  ,330, 335, 340,345 }
 ;
 const int16 DA1399[] = {
 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 }
 ;
 int DA1239 = FALSE;
 int DA1240;
 int DA1241;
 int DA1242;
 int DA1243;
 function DA266 () {
 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) {
 		DA1242 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
 		DA1239 = FALSE;
 		for ( DA996 = 0;
 		DA996 < sizeof(DA1399) / sizeof(DA1399[0]);
 		DA996++) {
 			if (DA1242 == DA1399[DA996]) {
 				DA1239 = TRUE;
 				break;
 							}
 					}
 		if (!DA1239) {
 			DA1240 = DA1399[0];
 			DA1241 = abs(DA1242 - DA1399[0]);
 			for ( DA996 = 1;
 			DA996 < sizeof(DA1399) / sizeof(DA1399[0]);
 			DA996++) {
 				DA1243 = abs(DA1242 - DA1399[DA996]);
 				if (DA1243 < DA1241) {
 					DA1240 = DA1399[DA996];
 					DA1241 = DA1243;
 									}
 							}
 			set_polar(POLAR_LS, DA1240, 32767);
 					}
 			}
 }
 function DA267 () {
 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) {
 		DA1242 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
 		DA1239 = FALSE;
 		for ( DA996 = 0;
 		DA996 < sizeof(DA1398) / sizeof(DA1398[0]);
 		DA996++) {
 			if (DA1242 == DA1398[DA996]) {
 				DA1239 = TRUE;
 				break;
 							}
 					}
 		if (!DA1239) {
 			DA1240 = DA1398[0];
 			DA1241 = abs(DA1242 - DA1398[0]);
 			for ( DA996 = 1;
 			DA996 < sizeof(DA1398) / sizeof(DA1398[0]);
 			DA996++) {
 				DA1243 = abs(DA1242 - DA1398[DA996]);
 				if (DA1243 < DA1241) {
 					DA1240 = DA1398[DA996];
 					DA1241 = DA1243;
 									}
 							}
 			set_polar(POLAR_LS, DA1240, 32767);
 					}
 			}
 }
 int DA1256;
 function DA268() {
 	if (combo_running(DA88) && (        get_ival(DA454) ||        get_ival(DA449) ||        get_ival(DA450) ||        get_ival(DA455) ||        get_ival(DA456) ||        get_ival(DA451)      )) {
 		combo_stop(DA88);
 		DA672 = 0;
 			}
 	if (DA1234 == 0) {
 		if ( ( DA1210 == 0 && !combo_running(DA91) && !combo_running(DA99) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( event_press(DA449) || (DA672 == 1 ||     combo_running(DA97) || combo_running(DA95)|| combo_running(DA93) ||  combo_running(DA96)     || combo_running(DA89) || combo_running(DA98) || combo_running(DA90) ||  combo_running(DA92)  ))     ) {
 			if(DA439)DA267 ();
 			else if (DA440)DA266 ();
 			combo_restart(DA88);
 					}
 			}
 	else{
 		combo_stop(DA88);
 			}
 	}
 combo DA88 {
 	if(DA439)DA267 ();
 	else if (DA440)DA266 ();
 	vm_tctrl(0);
 	wait( 4000);
 	DA672 = 0;
 	}
 combo DA89 {
 	set_val(DA451,0);
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( random(210, 215) + DA432);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait(600);
 	wait( 2000);
 	}
 function DA269() {
 	if (DA556 > 600 && DA556 <= 800) {
 		DA1257 = 240;
 			}
 	if (DA556 > 800 && DA556 <= 1000) {
 		DA1257 = 230;
 			}
 	if (DA556 > 1000 && DA556 <= 1500) {
 		DA1257 = 225;
 			}
 	if (DA556 > 1500 && DA556 <= 2000) {
 		DA1257 = 235;
 			}
 	if (DA556 > 2000) {
 		DA1257 = 218;
 			}
 	combo_run(DA98);
 	}
 combo DA90 {
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( random(170, 190));
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 500);
 	}
 combo DA91 {
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( 205);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 300);
 	}
 combo DA92 {
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( 190);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 400);
 	}
 int DA1262;
 int DA1263;
 int DA29;
 int DA477;
 int DA475;
 int DA1267;
 int DA1268;
 function DA270() {
 	}
 combo DA93 {
 	if (DA1263) {
 		set_val(DA449, 0);
 		DA1267 = 400;
 			}
 	else {
 		DA1267 = 0;
 			}
 	if (DA1263) {
 		DA251(0, DA1268);
 		DA254(0, 0);
 			}
 	vm_tctrl(0);
 	wait(DA1267);
 	if (DA1263) {
 		set_val(DA453, 100);
 		set_val(DA452, 0);
 		DA1267 = 60;
 			}
 	else {
 		set_val(DA452, 100);
 		DA1267 = 60;
 			}
 	DA270();
 	set_val(DA449,0);
 	vm_tctrl(0);
 	wait(DA1267);
 	set_val(DA453, 0);
 	set_val(DA452, 0);
 	set_val(DA449,0);
 	vm_tctrl(0);
 	DA270();
 	wait(DA1267);
 	if (DA1263) {
 		DA1267 = 0;
 			}
 	else {
 		DA1267 = 60;
 			}
 	set_val(DA452, 0);
 	set_val(DA453, 0);
 	set_val(DA449,0);
 	wait(DA1267);
 	vm_tctrl(0);
 	set_val(DA449, 100);
 	set_val(DA453, 100);
 	DA270();
 	wait(random(265, 268) +   DA431 );
 	set_val(DA453, 100);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	if (DA1263) {
 		DA1267 = 25;
 			}
 	else {
 		DA1267 = 30;
 			}
 	DA270();
 	wait(random(0,2) + DA1281 + DA1280 + DA1267 );
 	set_val(DA453, 100);
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	DA270();
 	wait(random(0,2) + 60);
 	vm_tctrl(0);
 	set_val(DA449, 0);
 	set_val(DA453, 100);
 	vm_tctrl(0);
 	DA270();
 	wait(random(0,2) + 80);
 	vm_tctrl(0);
 	set_val(DA453, 100);
 	vm_tctrl(0);
 	DA270();
 	wait(2500);
 	}
 int DA1207;
 int DA1210;
 int DA556;
 int DA1206;
 int DA1280;
 int DA1281 = 111;
 int DA1200;
 int DA1211;
 int DA1284;
 function DA271() {
 	DA1284 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
 	if ((DA1284 > 10 && DA1284 < 80) || (DA1284 > 110 && DA1284 < 170)) {
 		DA1268 = 100;
 			}
 	if ((DA1284 < 350 && DA1284 > 280) || (DA1284 < 260 && DA1284 > 190)) {
 		DA1268 = -100;
 			}
 	if (DA1210 == 0 && (DA556 >= 750 || ((DA1211 > 3000 && DA1206 > 1 && DA1206 < 5000)))) {
 		if (DA556 <= 2000 && DA1206 > 1500) {
 			set_val(DA449, 0);
 			DA1281 = 170;
 		}
 		if (DA556 <= 2000 && DA1206 > 1 && DA1206 <= 1500) {
 			set_val(DA449, 0);
 			DA1281 = 202;
 					}
 		if (DA556 > 2000 || (DA1206 > 1 && DA1206 <= 1500)) {
 			set_val(DA449, 0);
 			DA1281 = 151;
 					}
 		if ((DA556 > 2000 && DA1206 > 1500) || DA1211 > 1 && DA1206 > 1) {
 			set_val(DA449, 0);
 			DA1281 = 152;
 					}
 		if ((DA556 < 2000 && DA1206 > 1500) || DA1210 > 1 && DA1206 > 1) {
 			set_val(DA449, 0);
 			DA1281 = 149;
 					}
 		if (DA1206 > 1500) {
 			set_val(DA449, 0);
 			DA1281 = 148;
 					}
 		if (!DA556 > 2000 && DA1211 > 1 && DA1206 > 1 && DA1206 <= 1500) {
 			set_val(DA449, 0);
 			DA1281 = 147;
 					}
 		set_val(DA449, 0);
 		combo_stop(DA98);
 		combo_stop(DA99);
 		combo_stop(DA96);
 		combo_stop(DA89);
 		combo_stop(DA95);
 		combo_stop(DA92);
 		combo_stop(DA91);
 		combo_run(DA93);
 			}
 	else {
 		if (DA1210) {
 			set_val(DA449, 0);
 			combo_run(DA94);
 					}
 		else {
 			if (DA556 < 750) {
 				set_val(DA449, 0);
 				combo_run(DA95);
 							}
 					}
 			}
 }
 combo DA94 {
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait(random(0, 6) + random(200, 205));
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait(random(0, 6) + 700);
 	}
 combo DA95 {
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( random(200, 205) + DA431 )  set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 700);
 	}
 int DA1288 = 257;
 int DA1224 = 150;
 int DA1290 = 0;
 combo DA96 {
 	set_val(DA453, 100);
 	set_val(DA452, 0);
 	set_val(DA449,0);
 	vm_tctrl(0);
 	DA270();
 	wait(random(0,2) + 60);
 	set_val(DA453, 0);
 	set_val(DA452, 0);
 	set_val(DA449,0);
 	vm_tctrl(0);
 	DA270();
 	wait(random(0,2) + 60);
 	DA1290 = DA430;
 	DA270();
 	vm_tctrl(0);
 	set_val(DA452, 100);
 	set_val(DA449,0);
 	wait( 60);
 	set_val(DA452, 65);
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( DA1288 + random(-2, 2) +  DA434);
 	set_val(DA452, 75);
 	set_val(DA449, 0);
 	DA1262 = DA1224;
 	vm_tctrl(0);
 	wait( DA1224 + DA1290 - 58 + DA459 );
 	set_val(DA452, 85);
 	if(DA1256)set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( 60);
 	set_val(DA452, 100);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 3000);
 	}
 combo DA97 {
 	set_val(DA452, 100);
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( 160 + DA434 );
 	set_val(DA452, 100);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 3000);
 	}
 int DA1219;
 int DA1292 = 220;
 int DA1257;
 int DA1294 = 0;
 combo DA98 {
 	DA1294 = DA430;
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( DA1292);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( DA1257 + (DA1294) + 22 + DA460);
 	if(DA1256)set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( DA1292);
 	set_val(DA449, 0);
 	vm_tctrl(0);
 	wait( 2000);
 	}
 int DA1296 = TRUE;
 int DA1228;
 int DA1298 = 260;
 int DA1299 = 0;
 combo DA99 {
 	set_val(DA451, 100);
 	set_val(DA452, 100);
 	if (DA1296) {
 		DA1299 = DA430;
 			}
 	else {
 		DA1299 = 0   	}
 	set_val(DA449, 100);
 	vm_tctrl(0);
 	wait( DA1298);
 	vm_tctrl(0);
 	wait( DA1228 + DA1299 + 40)  }
 int DA1302;
 int DA1303 = 145;
 combo DA100 {
 	set_val(DA449, 100);
 	set_val(DA451, 100);
 	vm_tctrl(0);
 	wait( DA1303);
 	vm_tctrl(0);
 	wait( 500);
 	}
 int DA655;
 int DA654;
 int DA657;
 int DA656;
 combo DA101 {
 	set_val(DA456, 0);
 	vm_tctrl(0);
 	wait( 30);
 	set_val(DA456, 100);
 	vm_tctrl(0);
 	wait( 60);
 	}
 int DA658;
 int DA659;
 combo DA102 {
 	set_val(DA455, 100);
 	vm_tctrl(0);
 	wait( DA659);
 	}
 define DA1310 = TRUE;
 define DA1311 = 95;
 define DA1312 = 10;
 define DA1313 = 70;
 define DA1314 = FALSE;
 define DA1315 = 50;
 define DA1316 = 95;
 define DA1317 = XB1_LT;
 define DA1318 = XB1_RT;
 define DA1319 = XB1_LX;
 define DA1320 = XB1_LY;
 define DA1321 = POLAR_LS;
 int DA1322;
 combo DA103 {
 	set_polar(POLAR_LS,0,0);
 	vm_tctrl(0);
 	wait(60);
 	vm_tctrl(0);
 	wait(60);
 	}
 function DA272() {
 	if (    get_ival(DA454) > 30 &&    (get_ival(DA453) || get_ival(DA450)) &&    (!get_ival(DA455) || !get_ival(DA449))  ) {
 set_val(DA454, 0);
 		if(!get_ival(DA449)){
 			set_val(DA453,100);
 					}
 		else{
 			set_val(DA453,0);
 					}
 		  combo_run(DA105);
 		if(DA433 == TRUE){
 			combo_run(DA104);
 					}
 			}
 	else {
 		combo_stop(DA105);
 		combo_stop(DA104);
 			}
 	}
 combo DA104 {
 	if (DA433 == TRUE) {
 		set_val(DA452, 100);
 		DA1323 = 60;
 			}
 	else {
 		DA1323 = 0;
 			}
 	set_val(DA453, 0);
 	vm_tctrl(0);
 	wait( DA1323);
 	if (DA433 == TRUE) {
 		set_val(DA452, 0);
 		DA1323 = 60;
 			}
 	else {
 		DA1323 = 0;
 			}
 	set_val(DA453, 0);
 	vm_tctrl(0);
 	wait( DA1323);
 	if (DA433 == TRUE) {
 		set_val(DA452, 100);
 			}
 	vm_tctrl(0);
 	wait( 750);
 	vm_tctrl(0);
 	wait( 750);
 	}
 combo DA105 {
 	if(!get_ival(DA449)){
 		set_val(DA453,100);
 			}
 	else{
 		set_val(DA453,0);
 			}
 	set_val(DA454, 100);
 	vm_tctrl(0);
 	wait(DA594);
 	if(!get_ival(DA449)){
 		set_val(DA453,100);
 			}
 	else{
 		set_val(DA453,0);
 			}
     set_val(DA454, 0);
 	vm_tctrl(0);
 	wait(500);
 	}
 int DA1325;
 int DA1323 ;
 int DA1327 = TRUE;
 int DA1328;
 int DA1329;
 int DA1330;
 int DA1331;
 int DA1332;
 function DA273() {
 			if((DA1330 >= DA457) || get_ival(XB1_LS) || get_ival(XB1_RS) || get_ival(DA452) || get_ival(DA451)  || get_ival(DA453) ||       get_ival(DA450) || get_ival(DA456) || get_ival(DA449) || get_ival(DA455)   || get_ival(XB1_PR1) ||      get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(DA1138))> 45 || abs(get_ival(DA1139))> 45))){
 				if(!get_ival(DA454))DA117(POLAR_LS, DA124(POLAR_LS,POLAR_ANGLE), DA124(POLAR_LS, POLAR_RADIUS));
 							}
 	if( !get_ival(DA453) && !get_ival(DA454) && !get_ival(DA451) && !combo_running(DA105) ){
 		if (DA124(POLAR_LS, POLAR_RADIUS) > 1800) {
 			DA1330 += get_rtime();
 			if( (DA1330 > 2500) ) DA1330 = 0;
 			if((DA1330 <  DA457) && DA556 > 2000){
 			       sensitivity(PS4_LX, 60, 80);
 			       sensitivity(PS4_LY, 60, 80);
 				}
 					}
 			}
 }
 combo DA106 {
 	set_val(DA450,100);
 	vm_tctrl(0);
 	wait( DA655);
 	set_val(DA450,  0);
 	vm_tctrl(0);
 	wait( 30);
 	if(DA403){
 		set_val(DA450,100);
 			}
 	vm_tctrl(0);
 	wait( 60);
 	}
 combo DA107 {
 	set_val(DA450,  0);
 	vm_tctrl(0);
 	wait( 30);
 	set_val(DA450,100);
 	vm_tctrl(0);
 	wait( 60);
 	}
 combo DA108 {
 	set_val(DA456,100);
 	vm_tctrl(0);
 	wait( DA657);
 	set_val(DA456,  0);
 	vm_tctrl(0);
 	wait( 30);
 	if(DA400){
 		set_val(DA456,100);
 			}
 	vm_tctrl(0);
 	wait( 60);
 	}
 int DA1019 int DA1337 combo DA109 {
 	combo_suspend(DA109) 	wait(361) }
 function DA274 (){
 	if(DA1019[DA1337] != 361){
 	    DA1337-- 	}
 	else{
 		if(inv(DA1337) != 285){
 			DA274();
 		}
 	}
 }

   