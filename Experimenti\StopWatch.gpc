// Constants for text alignment
define Left = -1  
define Center = -2 
define Right = -3  
define String = 0  
define Value = 1 

// Variables for timers and display control
int ScreenSaverTimer, Display = TRUE, h, m, s, AfkTime;

// Width of fonts used for OLED display
const uint8 width [] = {7,11,16};

// Separator for clock display
const string clock = ":";

// Main loop
main {
    // Increment screen saver timer
    ScreenSaverTimer += get_rtime();

    // Activate display after 28 seconds
    if(ScreenSaverTimer >= 28000){
        Display = TRUE;
    }

    // Reset screen saver timer after 30 seconds
    if(ScreenSaverTimer >= 30000){
        ScreenSaverTimer = 0;
        Display = TRUE;
    }   
    
    // Increment AFK timer
    AfkTime += get_rtime();

    // Calculate seconds, minutes, and hours from AFK timer
    s = (AfkTime / 1000) % 60;       
    m = (AfkTime / 60000) % 60;      
    h = AfkTime / 3600000;

    // Update display every second
    if(!(AfkTime % 1000)){
        Display = TRUE;
    }

    // Display clock if needed
    if(Display){
        cls_oled(OLED_BLACK);
        if(ScreenSaverTimer <= 28000){
            print_clock(h,11,33,OLED_FONT_LARGE,OLED_WHITE);
            Print(clock[0],43,33,OLED_FONT_LARGE,OLED_WHITE,String);
            print_clock(m,59,33,OLED_FONT_LARGE,OLED_WHITE);
            print_clock(s,95,39,OLED_FONT_MEDIUM,OLED_WHITE);
            rect_oled(0, 0, 128, 64, 0, 1);
        }
    }
    Display = FALSE;    
}

// Function to print clock digits with leading zero if needed
function print_clock (f_string, f_x, f_y, f_font, f_color){
    if(find_digits(f_string) == 1){
        Print (0, f_x, f_y, f_font, f_color, Value);
        Print (f_string, f_x + width[f_font], f_y, f_font, f_color, Value);
    }
    else{
        Print (f_string, f_x, f_y, f_font, f_color, Value);
    }
}

// General print function for OLED display
int n, i;
function Print (f_string, f_x, f_y, f_font, f_color, f_type){
    if(!f_type){
        print(x_location(get_string_length(f_string), f_font, f_x), f_y, f_font, f_color, f_string);
    }
    else{
        if(f_string < 0){putc_oled(1,45);} // Print negative sign if needed
        if(f_string){
            for(n = find_digits(f_string) + i = (f_string < 0), f_string = abs(f_string); f_string > 0; n--, i++){  
                putc_oled(n, f_string % 10 + 48);
                f_string = f_string / 10;
            }
        }
        else{
            putc_oled(1,48); i = 1; // Print zero if value is zero
        }
        puts_oled(x_location(i, f_font, f_x), f_y, f_font, i, f_color);
    }
}

// Function to calculate string length (by Swizzy)
int stringLength;
function get_string_length(offset) {
    stringLength = 0;
    while (duint8(offset++)) { stringLength++; }
    return stringLength + 1;
}

// Function to calculate x location based on alignment (by Jbaze122)
function x_location(f_chars, f_font, f_x) {
    if(f_x == -3){return 128 - ((f_chars * (7 + (f_font > 1) + f_font * 4)) + 3 );} // Right alignment
    if(f_x == -2){return 64 - ((f_chars * (7 + (f_font > 1) + f_font * 4)) / 2);}  // Center alignment  
    if(f_x == -1){return 3;} // Left alignment
    return f_x;     
}

// Function to find number of digits in a number
function find_digits(f_num) {
    for(n = 1; n < 11; n++){
        if(!(abs(f_num) / pow(10,n))){
            return n; 
            break;
        }
    }
    return 1;
}