int current_angle;
int last_position = -1;
int deadzone = 50;
int position_1_triggered = 0;
int position_2_triggered = 0;
int position_3_triggered = 0;
int position_4_triggered = 0;

main {
    current_angle = (360 - get_ipolar(POLAR_LS, POLAR_ANGLE)) % 360;
    
    if (abs(get_ival(XB1_LX)) > deadzone || abs(get_ival(XB1_LY)) > deadzone) {
        if (current_angle >= 315 || current_angle < 45) {
            if (last_position != 0 && !position_1_triggered) {
                combo_run(R2_at_positions);
                position_1_triggered = 1;
            }
            last_position = 0;
        } else if (current_angle >= 45 && current_angle < 135) {
            if (last_position != 1 && !position_2_triggered) {
                combo_run(R2_at_positions);
                position_2_triggered = 1;
            }
            last_position = 1;
        } else if (current_angle >= 135 && current_angle < 225) {
            if (last_position != 2 && !position_3_triggered) {
                combo_run(R2_at_positions);
                position_3_triggered = 1;
            }
            last_position = 2;
        } else if (current_angle >= 225 && current_angle < 315) {
            if (last_position != 3 && !position_4_triggered) {
                combo_run(R2_at_positions);
                position_4_triggered = 1;
            }
            last_position = 3;
        }
    } else {
        last_position = -1;
        position_1_triggered = 0;
        position_2_triggered = 0;
        position_3_triggered = 0;
        position_4_triggered = 0;
    }
    
    set_val(TRACE_1, last_position);
    set_val(TRACE_2, current_angle);
}

combo R2_at_positions {
    set_val(XB1_LB, 100);
    wait(60);
    set_val(XB1_LB, 0);
    wait(100);
} 