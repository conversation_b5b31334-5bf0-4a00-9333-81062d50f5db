define RB_DOUBLE_CLICK_TIMEOUT = 300;  // Maximum time between clicks in ms
int rb_last_press;
int rb_first_click;
int current_time;

main {

        if(get_val(XB1_RB)) {
                set_val(XB1_RB, 0);
        combo_run(switch_direction);
        }else{
                combo_stop(switch_direction);
        }

	if(get_val(XB1_LS) ) {
        combo_run(switch_direction2);
        }

}

combo switch_direction {
        move_right_ls();
        wait(350);
        move_left_ls();
        wait(350);

}

combo switch_direction2 {
	set_val(XB1_RB, 100);
	move_down_ls();
	wait(350);
	set_val(XB1_RB, 0);set_val(XB1_LB, 100);
	//move_up_ls();
	wait(850);

}

function move_down_ls() {
        set_polar(POLAR_LS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);

}

function move_up_ls() {
        set_polar(POLAR_LS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_left_ls() {
        set_polar(POLAR_LS, 330 - get_ipolar(POLAR_LS, POLAR_ANGLE), 31000);
}

function move_right_ls() {
        set_polar(POLAR_LS, 30 - get_polar(POLAR_LS, POLAR_ANGLE), 31000);
}