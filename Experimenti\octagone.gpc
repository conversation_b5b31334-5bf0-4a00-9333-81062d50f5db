int LX, LY;            
int scaled_x, scaled_y;////////////////////////////////////////////////////////////////////////////////
// GLOBAL
////////////////////////////////////////////////////////////////////////////////
const int8 anglesMax[] = {
   // Index: 0..9
   100, 99, 98, 97, 96, 95, 94, 94, 93, 92,
   // 10..19
    91, 90, 89, 88, 87, 86, 85, 85, 84, 83,
   // 20..29
    82, 78, 70, 78, 82, 83, 84, 85, 85, 86,
   // 30..39
    87, 88, 89, 90, 91, 92, 93, 94, 94, 95,
   // 40..44
    96, 97, 98, 99, 100
};

int angle, radius;

////////////////////////////////////////////////////////////////////////////////
// MAIN
////////////////////////////////////////////////////////////////////////////////
main {

    // Beispiel: Wenn LB ODER RB gedrückt wird,
    // "stutzen" wir den linken Stick (POLAR_LS) auf dieses Oktagon.
    if((get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0) ||
       (get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0)) {
        angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
        radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

        // Möglicherweise reicht hier: min(radius, anglesMax[angle % 45])
        // -> Hängt davon ab, ob radius bis 100 oder bis 10000 reicht:
        set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
    } else {
        scaled_x = LX;
        scaled_y = LY;
    }
}
