int run_PL1;

main {
if(event_press(XB1_PL1)) {
    if(run_PL1 == 1) {
        run_PL1 = 0;
    } else {
        run_PL1 = 1;
    }
}
set_val(XB1_PL1, 0)

if(run_PL1 == 1) {
    set_val(XB1_PL1, 100);
    } else if(run_PL1 == 0) {
    set_val(XB1_PL1, 0);
}

if(event_press(XB1_PL1)) combo_run (STOJ_cmb);
if(event_release(XB1_PL1)) combo_stop (STOJ_cmb);

}
combo STOJ_cmb {
 set_val(XB1_LX, 0);
 set_val(XB1_LY, 0);
  wait(80);  
 set_val(XB1_RT, 100);
 wait(4000);  
}