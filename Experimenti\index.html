<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIFA Settings Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .profile-section {
            margin-bottom: 20px;
        }
        .profile-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .profile-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #e0e0e0;
        }
        .profile-btn.active {
            background-color: #2196F3;
            color: white;
        }
        .slider-section {
            margin-bottom: 30px;
        }
        .slider-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        .slider-group {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .slider-group h3 {
            margin-top: 0;
            color: #333;
        }
        .slider-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .slider-label {
            width: 200px;
            margin-right: 10px;
        }
        .slider {
            flex-grow: 1;
        }
        .value-display {
            width: 50px;
            text-align: right;
            margin-left: 10px;
        }
        .json-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .switch-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .switch-row label {
            margin-right: 10px;
        }
        .number-input {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .number-input label {
            margin-right: 10px;
        }
        .validation-notice {
            margin-bottom: 10px;
        }
        .validation-notice p {
            font-size: 14px;
            color: #666;
        }
        .tooltip {
            display: none;
            position: absolute;
            background-color: #333;
            color: white;
            padding: 5px;
            border-radius: 4px;
        }
        .preset-section {
            margin-bottom: 20px;
        }
        .preset-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FIFA Settings Manager</h1>
        
        <div class="profile-section">
            <h2>Select Profile</h2>
            <div class="profile-selector">
                <button class="profile-btn active" onclick="selectProfile('start')">Start</button>
                <button class="profile-btn" onclick="selectProfile('hard')">Hard</button>
                <button class="profile-btn" onclick="selectProfile('legit')">Legit</button>
                <button class="profile-btn" onclick="selectProfile('end')">End</button>
            </div>
        </div>

        <div class="slider-section">
            <h2>Local Settings</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <h3>Movement</h3>
                    <div id="local-movement-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Ball Control</h3>
                    <div id="local-ball-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Positioning</h3>
                    <div id="local-position-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Goalkeeper</h3>
                    <div id="local-gk-sliders"></div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Opponent Settings</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <h3>Movement</h3>
                    <div id="opponent-movement-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Ball Control</h3>
                    <div id="opponent-ball-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Positioning</h3>
                    <div id="opponent-position-sliders"></div>
                </div>
                <div class="slider-group">
                    <h3>Goalkeeper</h3>
                    <div id="opponent-gk-sliders"></div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Goalkeeper Settings</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div id="local-gk-sliders"></div>
                </div>
                <div class="slider-group">
                    <div id="opponent-gk-sliders"></div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Match Timer</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div class="switch-row">
                        <label>Freeze Match Timer:</label>
                        <input type="checkbox" id="freeze_match_timer" onchange="updateJSON()">
                        <div class="tooltip" id="freeze_timer_tooltip">
                            Pauses the in-game match timer
                        </div>
                    </div>
                    <div class="switch-row">
                        <label>Reset Match Timer:</label>
                        <input type="checkbox" id="reset_match_timer" onchange="updateJSON()">
                    </div>
                    <div class="number-input">
                        <label>Set Match Timer Value:</label>
                        <input type="number" id="new_match_timer_value" min="0" max="999" onchange="updateJSON()">
                    </div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Ball State</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div class="switch-row">
                        <label>Freeze Ball:</label>
                        <input type="checkbox" id="freeze_ball" onchange="updateJSON()">
                    </div>
                    <div class="switch-row">
                        <label>Left Goal:</label>
                        <input type="checkbox" id="left_goal" onchange="updateJSON()">
                    </div>
                    <div class="switch-row">
                        <label>Right Goal:</label>
                        <input type="checkbox" id="right_goal" onchange="updateJSON()">
                    </div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Player Info</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div class="switch-row">
                        <label>Show Opponent Name:</label>
                        <input type="checkbox" id="show_opponent_name" onchange="updateJSON()">
                    </div>
                    <div class="switch-row">
                        <label>Show Opponent Platform:</label>
                        <input type="checkbox" id="show_opponent_platform" onchange="updateJSON()">
                    </div>
                    <div class="number-input">
                        <label>DR Rating:</label>
                        <input type="number" id="dr_rating" min="0" max="3000" onchange="updateJSON()">
                    </div>
                    <div class="number-input">
                        <label>Chemistry:</label>
                        <input type="number" id="chemistry" min="0" max="100" onchange="updateJSON()">
                    </div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Hotkeys</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div class="validation-notice">
                        <p>ℹ️ Hotkeys use key codes (0-255). Use <a href="https://keycode.info/" target="_blank">keycode.info</a> to find codes</p>
                    </div>
                    <div class="number-input">
                        <label>Disconnect Hotkey:</label>
                        <input type="number" id="disconnect_hotkey" min="0" max="255" onchange="updateJSON()">
                    </div>
                    <div class="number-input">
                        <label>Slider Hotkey:</label>
                        <input type="number" id="slider_hotkey" min="0" max="255" onchange="updateJSON()">
                    </div>
                    <div class="number-input">
                        <label>Crash Opponent Hotkey:</label>
                        <input type="number" id="crash_hotkey" min="0" max="255" onchange="updateJSON()">
                    </div>
                </div>
            </div>
        </div>

        <div class="slider-section">
            <h2>Miscellaneous</h2>
            <div class="slider-container">
                <div class="slider-group">
                    <div class="switch-row">
                        <label>Skip Pack Animations:</label>
                        <input type="checkbox" id="skip_pack_anim" onchange="updateJSON()">
                    </div>
                    <div class="switch-row">
                        <label>Green Time Finishing:</label>
                        <input type="checkbox" id="green_time_finishing" onchange="updateJSON()">
                    </div>
                </div>
            </div>
        </div>

        <div class="preset-section">
            <h2>Presets</h2>
            <div class="preset-controls">
                <button onclick="savePreset()">Save Preset</button>
                <input type="file" id="presetFile" onchange="loadPreset()">
            </div>
        </div>

        <button onclick="saveConfig()">Save Configuration</button>
        
        <div class="json-output">
            <h3>JSON Output:</h3>
            <pre id="jsonOutput"></pre>
        </div>
    </div>

    <script>
        const sliderDefinitions = {
            local: {
                movement: [
                    { id: 'acceleration', label: 'Acceleration', min: 0, max: 100 },
                    { id: 'sprint', label: 'Sprint Speed', min: 0, max: 100 }
                ],
                ball: [
                    { id: 'shoot_error', label: 'Shoot Error', min: 0, max: 100 },
                    { id: 'shoot_speed', label: 'Shoot Speed', min: 0, max: 100 },
                    { id: 'pass_error', label: 'Pass Error', min: 0, max: 100 },
                    { id: 'pass_speed', label: 'Pass Speed', min: 0, max: 100 },
                    { id: 'first_touch_error', label: 'First Touch Error', min: 0, max: 100 }
                ],
                position: [
                    { id: 'position_marking', label: 'Position Marking', min: 0, max: 100 },
                    { id: 'position_line_length', label: 'Line Length', min: 0, max: 100 },
                    { id: 'position_line_width', label: 'Line Width', min: 0, max: 100 },
                    { id: 'position_defensive_line_height', label: 'Defensive Line Height', min: 0, max: 100 },
                    { id: 'position_run_frequency', label: 'Run Frequency', min: 0, max: 100 },
                    { id: 'position_fullback', label: 'Fullback Position', min: 0, max: 100 }
                ],
                goalkeeper: [
                    { id: 'gk_ability', label: 'GK Ability', min: 0, max: 100 }
                ]
            },
            opponent: {
                movement: [
                    { id: 'acceleration', label: 'Acceleration', min: 0, max: 100 },
                    { id: 'sprint', label: 'Sprint Speed', min: 0, max: 100 }
                ],
                ball: [
                    { id: 'shoot_error', label: 'Shoot Error', min: 0, max: 100 },
                    { id: 'shoot_speed', label: 'Shoot Speed', min: 0, max: 100 },
                    { id: 'pass_error', label: 'Pass Error', min: 0, max: 100 },
                    { id: 'pass_speed', label: 'Pass Speed', min: 0, max: 100 },
                    { id: 'first_touch_error', label: 'First Touch Error', min: 0, max: 100 }
                ],
                position: [
                    { id: 'position_marking', label: 'Position Marking', min: 0, max: 100 },
                    { id: 'position_line_length', label: 'Line Length', min: 0, max: 100 },
                    { id: 'position_line_width', label: 'Line Width', min: 0, max: 100 },
                    { id: 'position_defensive_line_height', label: 'Defensive Line Height', min: 0, max: 100 },
                    { id: 'position_run_frequency', label: 'Run Frequency', min: 0, max: 100 },
                    { id: 'position_fullback', label: 'Fullback Position', min: 0, max: 100 }
                ],
                goalkeeper: [
                    { id: 'gk_ability', label: 'GK Ability', min: 0, max: 100 }
                ]
            }
        };

        let currentProfile = 'start';
        let config = {};

        function createSlider(prefix, def) {
            const sliderId = `${prefix}_${def.id}`;
            const container = document.createElement('div');
            container.className = 'slider-row';
            container.innerHTML = `
                <span class="slider-label">${def.label}</span>
                <input type="range" class="slider" id="${sliderId}"
                       min="${def.min}" max="${def.max}" value="50"
                       oninput="updateSliderValue(this)" onchange="updateJSON()">
                <span class="value-display">50</span>
            `;
            return container;
        }

        function initializeSliders() {
            for (const [type, categories] of Object.entries(sliderDefinitions)) {
                for (const [category, sliders] of Object.entries(categories)) {
                    const containerId = `${type}-${category}-sliders`;
                    const container = document.getElementById(containerId);
                    container.innerHTML = '';
                    sliders.forEach(def => {
                        container.appendChild(createSlider(type, def));
                    });
                }
            }
        }

        function updateSliderValue(slider) {
            slider.nextElementSibling.textContent = slider.value;
            updateJSON();
        }

        function selectProfile(profile) {
            document.querySelectorAll('.profile-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.toLowerCase() === profile) {
                    btn.classList.add('active');
                }
            });
            currentProfile = profile;
            updateJSON();
        }

        function updateJSON() {
            const newConfig = { current_profile: currentProfile };
            
            for (const [type, categories] of Object.entries(sliderDefinitions)) {
                for (const [category, sliders] of Object.entries(categories)) {
                    sliders.forEach(def => {
                        const sliderId = `${type}_${def.id}`;
                        const value = document.getElementById(sliderId).value;
                        const key = `${currentProfile}.Sliders.${type === 'opponent' ? 'opponent_' : 'local_'}${def.id}`;
                        newConfig[key] = value;
                    });
                }
            }

            newConfig.freeze_match_timer = document.getElementById('freeze_match_timer').checked;
            newConfig.reset_match_timer = document.getElementById('reset_match_timer').checked;
            newConfig.new_match_timer_value = document.getElementById('new_match_timer_value').value;
            newConfig.freeze_ball = document.getElementById('freeze_ball').checked;
            newConfig.left_goal = document.getElementById('left_goal').checked;
            newConfig.right_goal = document.getElementById('right_goal').checked;
            newConfig.show_opponent_name = document.getElementById('show_opponent_name').checked;
            newConfig.show_opponent_platform = document.getElementById('show_opponent_platform').checked;
            newConfig.dr_rating = document.getElementById('dr_rating').value;
            newConfig.chemistry = document.getElementById('chemistry').value;
            newConfig.disconnect_hotkey = document.getElementById('disconnect_hotkey').value;
            newConfig.slider_hotkey = document.getElementById('slider_hotkey').value;
            newConfig.crash_hotkey = document.getElementById('crash_hotkey').value;
            newConfig.skip_pack_anim = document.getElementById('skip_pack_anim').checked;
            newConfig.green_time_finishing = document.getElementById('green_time_finishing').checked;

            config = newConfig;
            document.getElementById('jsonOutput').textContent = JSON.stringify(config, null, 2);
        }

        function saveConfig() {
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'config.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function savePreset() {
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'preset.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function loadPreset() {
            const fileInput = document.getElementById('presetFile');
            const file = fileInput.files[0];
            const reader = new FileReader();
            reader.onload = () => {
                config = JSON.parse(reader.result);
                updateJSON();
            };
            reader.readAsText(file);
        }

        // Initialize the UI
        initializeSliders();
        updateJSON();
    </script>
</body>
</html>
