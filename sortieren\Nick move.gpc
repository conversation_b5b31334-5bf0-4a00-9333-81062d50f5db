int LS_Direction;
int RS_Angle;
int RS_Direction;

main {

    RS_Angle = (360 - get_polar(POLAR_RS, POLAR_ANGLE));
    RS_Direction = get_polar(POLAR_RS, POLAR_ANGLE) * -1;

        if (RS_Angle >= 1500) {
            LS_Direction = RS_Direction + 180;
        } else {
            LS_Direction = RS_Direction - 180;
        }

if (get_ipolar(POLAR_RS,POLAR_RADIUS) >= 1500)  {
	combo_run(cb_nick);
  }  
}

combo cb_nick {
	set_val(POLAR_LX, 0);
	set_val(POLAR_LY, 0);
	wait(400);
	set_polar(POLAR_LS, LS_Direction, 32767);
	combo_run(x_plus_a);
    wait(500);
    wait(50);
}

combo x_plus_a {  
	set_val(XB1_X,100);  
	wait(40); 
	set_val(XB1_X,100);  
	set_val(XB1_A,100); 
	wait(40); 
	set_val(XB1_X,0);  
	set_val(XB1_A,100);
	wait(40);
	wait(300);
} 