// Script was generated with < FIFA Series Skills Generator > ver. 16.3 Date :10/15/21 Time: 14:04:14
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
$$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
$$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
$$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
$$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
$$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
$$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
$$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
\__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
$$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
$$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
$$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
$$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
$$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
$$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
$$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
\__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT :CLASIC
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_L1; // Finesse Shot
define PlayerRun     = PS4_R1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MODIFIER      = PS4_L1;     
define CrossBtn      = PS4_SQUARE; // Cross Btn 
define ThroughBall   = PS4_TRIANGLE; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
define SCOOP_TURN_FAKE_SKILL          =49; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
                                               
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
    	if (event_press(XB1_PR2)){  right_on = 0;ACTIVE = FOUR_TOUCH_TURN_SKILLS ;  combo_run(FOUR_TOUCH_TURN_cmb); }
    	
    	
    	// MICRO Dribbling              
//--------------------------------------------------------------
		if(!get_val(XB1_A)) {
		if (get_val(XB1_PR1)) {
			set_val(XB1_LT,100);
			set_val(XB1_RT,100);
		}
	}
    	
   //--- DEFENSE                               
   if(defence_on) f_defence();                 
                                                
                                                                
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot)){ 
	      if(event_press(ShotBtn) ){ 
		        combo_run(Timed_Finesse_Finish ); 
	      } 
         set_val(ShotBtn,0);
    } 
    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
	       LX = get_val(MOVE_X);                                      
	       LY = get_val(MOVE_Y);                                      
     calc_zone ();                                              
    }                                                           
    //----------------------------------------------------------- 
                                      
   if(!get_val(PS4_R3) &&  !get_val(PaceCtrol) && !get_val(SprintBtn)){ // all Skills mode                
                                                     
	      //  Right Stick -->  UP                          
	      if( get_val(PS4_RY) < -70  && !flick_up ) {   
	      		flick_up = TRUE;                          
	      		right_on = FALSE;          
	      		ACTIVE = HEEL_TO_HEEL_FLICK_SKILL; combo_run(HEELtoHEEL);   //2. Heel to Heel
	      }                                              
	      //  Right Stick -->  DOWN                               
	      if( get_val(PS4_RY) >  70  && !flick_d ) {     
	      		flick_d = TRUE;                            
	      		right_on = TRUE;              
	      		 ACTIVE = SCOOP_TURN_FAKE_SKILL; combo_run(SCOOP_TURN_FAKE);
	      }                                               
                                                        
	      //  Right Stick --> LEFT                                
	      if( get_val(PS4_RX) < -70  && !flick_l ) {     
	      		flick_l = TRUE;                             
	      		right_on = TRUE;               
	      		ACTIVE = REVERSE_ELASTICO_SKILL; combo_run(REVERSE_ELASTICO);  // 13. REVERSE_ELASTICO_SKILL
	      }                                               
                                                        
	      // Right Stick --> RIGHT                                   
	      if( get_val(PS4_RX) >  70  && !flick_r ) {      
	      		flick_r = TRUE;                             
	      		right_on = FALSE;            
	      		ACTIVE = ELASTICO_SKILL; combo_run(ELASTICO);  // 12. ELASTICO_SKILL
	      }                                                
                                                         
                                                          
        if(abs(get_val(PS4_RY))<20  && abs(get_val(PS4_RX))<20){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }                                              
        set_val(SKILL_STICK_X,0); 
        set_val(SKILL_STICK_Y,0); 
    }// end of ALWAYS ON  
    //===============================
    //  ADDITIONAL SKILL
    //===============================
      if(event_press(PS4_R3)){    
	      		right_on = TRUE;      
	      		ACTIVE = ROULETTE_SKILL; combo_run(ROULETTE); // 17. ROULETTE_SKILL
      }                     
    //===============================
    //  ADDITIONAL SKILL
    //===============================
    if(event_press(PS4_L2) && !tap){  
        combo_run(ONE_TAP);              
	                    
    }else if( event_press(PS4_L2) &&  tap){ 
    	                                 
        right_on = TRUE;ACTIVE = FOUR_TOUCH_TURN_SKILLS ;  combo_run(FOUR_TOUCH_TURN_cmb);
    }                           
                                                
    //========================================
    // *** DYNAMIC FINISHING ***
    //========================================
    if(Dynamic_Finish_onoff) f_dynamic_finish (); 
    //===============================================
    //    GRROUND PASSES  MIN / MAX
    //===============================================
    if(!get_val(PaceCtrol) && !get_val(SprintBtn)){
	    if(get_val(PassBtn)){
	        ground_pass_timer += get_rtime();
	    }
	    if(get_val(PassBtn) && get_ptime(PassBtn) > Ground_Pass_MAX){
	        set_val(PassBtn,0);
	    }
	    if(event_release(PassBtn)){
	        if(ground_pass_timer && (ground_pass_timer < Ground_Pass_MIN)){
	            GP_difference = Ground_Pass_MIN - ground_pass_timer;
	            
	            combo_run(Ground_Pass_MIN_cmb);
	        }
	        ground_pass_timer = 0;
	    }
    }
    //===============================================
    //    TROUGH PASSES  MIN / MAX
    //===============================================
	    if(get_val(ThroughBall)){
	    	trough_pass_timer += get_rtime();
	    }
	    if(get_val(ThroughBall) && get_ptime(ThroughBall) > Trough_Pass_MAX){
	    	set_val(ThroughBall,0);
	    	if(DoubleTapTroughPass && !trough_start) combo_run(DOUBLE_TAP_TROUGH_cmb);
	    	trough_start = TRUE;
	    }
	    if(event_release(ThroughBall)){
	    	if(!trough_start){
		    	if(trough_pass_timer && (trough_pass_timer < Trough_Pass_MIN)){
		    		TroughP_difference = Trough_Pass_MIN - trough_pass_timer;
		    		
		    		combo_run(Trough_Pass_MIN_cmb);
		    	}else{
		    		if(DoubleTapTroughPass) combo_run(DOUBLE_TAP_TROUGH_cmb);
		    	}
	    	}
	    	trough_pass_timer = 0;
	    	trough_start      = FALSE;
	    }

	//===============================================
    //    LOB PASSES/CROSES  MIN / MAX
    //===============================================
	    if(get_val(CrossBtn)){
	    	Lob_pass_timer += get_rtime();
	    }
	    if(get_val(CrossBtn) && get_ptime(CrossBtn) > Lob_Pass_MAX){
	    	set_val(CrossBtn,0);
	    	if(DoubleTapTroughPass)combo_run(DOUBLE_TAP_TROUGH_cmb);
	    }
	    if(event_release(CrossBtn)){
	    	if(Lob_pass_timer && (Lob_pass_timer < Lob_Pass_MIN)){
	    		LobP_difference = Lob_Pass_MIN - Lob_pass_timer;
	    		
	    		combo_run(Lob_Pass_MIN_cmb);
	    	}
	    	Lob_pass_timer = 0;
	    }

    // all Skills mode                
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
function f_dynamic_finish () { 
    if(event_release(CrossBtn)){
        cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1000;
    }
    
    if(cross_timer){
        cross_timer -= get_rtime();
    }
    
    if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && cross_timer <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){
            set_val(ShotBtn,0);
            INSIDE_BOX_AIM();     
            if( after_sprint_timer > 225  ) {
                UltimatePower = f_clamp(182.7 + ( 0.0323*(after_sprint_timer)),190,220 ) ; // since the ms the sprint is released >> 190 up to 215 ms shooting power generated. 
                drval=0;
                combo_restart(Dynamic_Shoot); 
            }
            if( after_sprint_timer <= 0  ) {
                UltimatePower = 230 ;
                drval=100;
                combo_restart(Dynamic_Shoot); 
            }
        }
    } 
    /// FakeShot Support avoid Conflictions
    if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  
        combo_stop(Dynamic_Shoot);
    }
  
}

function f_clamp (f_val, Low, High ){
    if(f_val < Low) return Low;
    else if(f_val > High)return High;
    return f_val; 
}

////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;
int Dynamic_Finish_onoff = TRUE;

combo Dynamic_Shoot {
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(UltimatePower + 20);
    
    set_val(ShotBtn, 0);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(DrivenShot + 15);
    
    set_val(ShotBtn, 0);
    wait(50);
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM();
    wait(50);
    
    set_val(ShotBtn, 0);
    set_val(PS4_R3,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}
function INSIDE_BOX_AIM() { 
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 }
int ground_pass_timer; 
int Ground_Pass_MIN = 80;
int Ground_Pass_MAX = 250;
int GP_difference;

combo Ground_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(GP_difference);
}
//=================================
int trough_pass_timer; 
int Trough_Pass_MIN = 80;
int Trough_Pass_MAX = 300;
int TroughP_difference;
int DoubleTapTroughPass = TRUE;
int trough_start;

combo Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	wait(TroughP_difference);
	if(DoubleTapTroughPass){
		set_val(ThroughBall,100);
	}
	wait(60);
}
combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}

//=================================
int Lob_pass_timer; 
int Lob_Pass_MIN = 80;
int Lob_Pass_MAX = 350;
int LobP_difference;

combo Lob_Pass_MIN_cmb {
	set_val(CrossBtn,100);
	wait(LobP_difference);
}
//=================================
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
//--- Defense V 3.0
// Credits to Dark.Angle 
//=======================================
combo JoCKEY { 
  set_val(PaceCtrol,100);
  wait(150); 
  set_val(PaceCtrol,100);
  set_val(PS4_L3,100);
  wait(150);
  set_val(PS4_L3,0);
  set_val(PaceCtrol,0);
  wait(30);
}

int Teammate_Contain_on = FALSE;
int defence_on = TRUE;

function f_defence (){
    
   //============ COPY THis TO MAIN SECTION ============//
    
	if( get_val(SprintBtn) && (get_val(PassBtn) || get_val(PaceCtrol) ) ){	
	   vm_tctrl(-6);
		
		if( abs(get_val(PS4_LX))>20  || abs(get_val(PS4_LY))>20  ) {
			set_val(PassBtn,0);
			set_val(PaceCtrol,100);
			if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
			combo_run(JoCKEY);
		}else{  
			set_val(PassBtn,100);
			
		}
	
	}
	
	if(event_release(PassBtn) || event_release(PaceCtrol)){vm_tctrl(-0); combo_stop(JoCKEY) } 
  
  //==============NEW SENSitivity MODE ==============//
  //This will gives you good left stick dribbling and better control on ball ,, also faster jockey reactions while defending
  //with new defence mode
    if (!get_val(SprintBtn) && !get_val(PassBtn) && !get_val(PS4_TRIANGLE) && !get_val(ShotBtn) && !combo_running(JoCKEY) ){
		sensitivity(PS4_LX, NOT_USE, 88);
		sensitivity(PS4_LY, NOT_USE, 88);
	}else if ( get_val(SprintBtn) && !combo_running(JoCKEY) ) {
		sensitivity(PS4_LX, NOT_USE, 110);
		sensitivity(PS4_LY, NOT_USE, 110);
	} else if ( get_val(SprintBtn) && combo_running(JoCKEY) ) { 
		sensitivity(PS4_LX, NOT_USE, 108);
		sensitivity(PS4_LY, NOT_USE, 108);
	}
}
combo InstantTimedFinish {
    CORNER()
    set_val(ShotBtn, 100);             
    wait(220);
    set_val(ShotBtn, 0);
    CORNER()
    wait(150)
    set_val(ShotBtn, 100);             
    wait(220);  
} 

int long_finesse;
function CORNER() { 

     if (combo_running(InstantTimedFinish)){
         FINESSE_OR_NORMAL = 100;
     }else{
         FINESSE_OR_NORMAL =  25;
     }   
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {  
        right_on = FALSE;
        LA (FINESSE_OR_NORMAL, -90);
    }
          
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    { 
        right_on = TRUE;
        LA (FINESSE_OR_NORMAL, 90);
    }
    
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    { 
        right_on = TRUE;
        LA (inv(FINESSE_OR_NORMAL), -90);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {
        right_on = FALSE;
        LA (inv(FINESSE_OR_NORMAL),  90);
    }
          
}

function CORNER_FIX_MOVE() {
        
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {   right_on = FALSE;
        LA (100,-16);
    }
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    {   right_on = TRUE;
        LA (100, 16);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {   right_on = TRUE; 
        LA (-100,-16); 
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {   right_on = FALSE;
        LA (-100, 16);
    }
}

int ping = 70;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 220;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    CORNER ();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause );
    
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn)            
           
} 
combo FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);     
}

combo SCOOP_TURN_FAKE {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PaceCtrol,100);
    wait(40); 
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
    wait(250);
    LA_L_R()
    wait(300); 
}        
                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}                                        
                                         
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   
combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
}                  
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}                        
///////////////////////////////////////////////////
// ZONE FUNCTION
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(PS4_LX) >= 50) move_lx = 100;
    else if(get_val(PS4_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(PS4_LY) >= 50) move_ly = 100;
    else if(get_val( PS4_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(ZONE_P[zone_p][0] == move_lx && ZONE_P[zone_p][1] == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
}
function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - d;
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             