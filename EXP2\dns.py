# Import the subprocess module to run external commands
import subprocess
import sys # Import sys to check the operating system
import os # Import os for platform-specific operations

def display_dns_cache_windows():
    """
    Executes 'ipconfig /displaydns' on Windows and returns its output.
    Handles potential errors if the command is not found or fails.
    """
    try:
        # Run the command and capture the output
        # 'text=True' decodes the output as text using the default encoding
        # 'check=True' raises a CalledProcessError if the command returns a non-zero exit code
        # 'capture_output=True' captures stdout and stderr
        result = subprocess.run(['ipconfig', '/displaydns'], capture_output=True, text=True, check=True, encoding='cp850', errors='ignore') # Common Windows console encoding

        # Print the standard output from the command
        print("--- DNS Resolver Cache ---")
        print(result.stdout)
        print("--- End of Cache ---")

    except FileNotFoundError:
        # Handle the case where 'ipconfig' command is not found (unlikely on Windows)
        print("Error: 'ipconfig' command not found. Make sure you are running this on a Windows system.")
    except subprocess.CalledProcessError as e:
        # Handle errors during command execution
        print(f"Error executing command: {e}")
        # Sometimes error messages are in stderr
        if e.stderr:
            print(f"Error details: {e.stderr}")
    except Exception as e:
        # Catch any other unexpected errors
        print(f"An unexpected error occurred: {e}")

def display_dns_cache_macos():
    """
    Executes 'dscacheutil -q host' on macOS and returns its output.
    """
    try:
        # Command to query the host cache on macOS
        result = subprocess.run(['dscacheutil', '-q', 'host'], capture_output=True, text=True, check=True)
        print("--- DNS Resolver Cache (macOS) ---")
        print(result.stdout)
        print("--- End of Cache ---")
    except FileNotFoundError:
        print("Error: 'dscacheutil' command not found. Make sure you are running this on macOS.")
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def display_dns_cache_linux():
    """
    Attempts to display DNS cache info on Linux using systemd-resolve or nscd.
    Note: Linux DNS caching is less standardized than Windows/macOS.
    """
    print("--- DNS Resolver Cache (Linux) ---")
    # Try systemd-resolved (common on modern systems)
    try:
        print("\nAttempting with systemd-resolve:")
        # Check statistics which often includes cache info or size
        result_stats = subprocess.run(['systemd-resolve', '--statistics'], capture_output=True, text=True, check=True)
        print(result_stats.stdout)
        # Dump the cache (might require specific permissions or configuration)
        # result_dump = subprocess.run(['systemd-resolve', '--dump-cache'], capture_output=True, text=True, check=True)
        # print(result_dump.stdout) # This can be very verbose
        print("Note: Full cache dump might require specific permissions or is very verbose.")
        print("Displayed statistics instead.")

    except FileNotFoundError:
        print("systemd-resolve not found.")
    except subprocess.CalledProcessError as e:
        print(f"Error with systemd-resolve: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
    except Exception as e:
        print(f"An unexpected error occurred with systemd-resolve: {e}")

    # Try nscd (older systems or specific configurations)
    try:
        print("\nAttempting with nscd:")
        # Query the hosts database statistics from nscd
        result_nscd = subprocess.run(['nscd', '-g'], capture_output=True, text=True, check=True)
        print(result_nscd.stdout)
    except FileNotFoundError:
        print("nscd command not found or nscd service not running.")
    except subprocess.CalledProcessError as e:
        print(f"Error with nscd: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
    except Exception as e:
        print(f"An unexpected error occurred with nscd: {e}")

    print("\nNote: Linux DNS caching varies. Check your specific distribution and configuration (e.g., systemd-resolved, dnsmasq, nscd).")
    print("--- End of Cache (Linux) ---")


# --- Main execution ---
if __name__ == "__main__":
    # Check the operating system
    if sys.platform == "win32":
        display_dns_cache_windows()
    elif sys.platform == "darwin": # darwin is the platform name for macOS
        display_dns_cache_macos()
    elif sys.platform.startswith("linux"):
        display_dns_cache_linux()
    else:
        print(f"Unsupported operating system: {sys.platform}")
        print("This script currently supports Windows, macOS, and basic Linux checks.")

