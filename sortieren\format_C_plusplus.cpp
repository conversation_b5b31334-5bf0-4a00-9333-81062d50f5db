// Variables for axis mapping function
int axisVal, sign, step1, step2, step3, step4;

// Configuration variables
int console_deadzone = 25;    // Game's default deadzone
int custom_deadzone = 25;     // Your desired deadzone
int custom_sensitivity = 200; // Sensitivity (100 is standard)

// Axis mapping function
function axis(int cdz, int axis, int dz, int sens)
{
  if (dz >= 100 * 100 / sens)
    dz = (100 * 100 / sens) - 1;

  if (abs(get_ival(axis)) > dz)
  {
    sign = get_ival(axis) / abs(get_ival(axis));
    step1 = 100 - cdz;
    step2 = 100 * 100 / sens - dz;
    if (step2 < 0)
      step2 = 1;
    step3 = get_ival(axis) - sign * dz;
    step4 = step3 * step1 / step2;
    axisVal = sign * cdz + step4;
    set_val(axis, axisVal);
  }
  else
    set_val(axis, 0);
}

main
{
  // Apply mapping to left stick X axis
  axis(console_deadzone, PS4_LX, custom_deadzone, custom_sensitivity);

  // Apply mapping to left stick Y axis
  axis(console_deadzone, PS4_LY, custom_deadzone, custom_sensitivity);

  // Display values in trace for debugging
  set_val(TRACE_1, get_val(PS4_LX));
  set_val(TRACE_2, get_val(PS4_LY));
}