int a = 12345;
int b = 631;
int c = 634;

main {
    set_val(TRACE_1, round_div(a, b));    // Outputs 20
    set_val(TRACE_2, round_div(a, c));    // Outputs 19
}


int q;
int r;

// Returns a/b rounded to the nearest integer.
// If b is zero, it returns 0 (you may wish to handle that case differently).
function round_div(a, b)
{
    if(b == 0)
        return 0; // or handle error as appropriate

    q = a / b;   // integer division truncates toward 0
    r = a % b;   // remainder (has the same sign as 'a' in C-like languages)

    // Multiply remainder by 2 and compare with the absolute value of b.
    // If the absolute remainder is at least half of |b|, then adjust q.
    // We must adjust upward if a/b is positive and downward if a/b is negative.
    if (2 * abs(r) >= abs(b))    // for a tie (==), this rounds away from zero
    {
        if ((a >= 0 && b > 0) || (a < 0 && b < 0))
            q++;
        else
            q--;
    }
    
    return q;
}
