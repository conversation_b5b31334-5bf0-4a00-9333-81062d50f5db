// Define direction constants
define NE = 1;  // Northeast
define SE = 2;  // Southeast
define SW = 3;  // Southwest
define NW = 4;  // Northwest

int current_direction
int Where_am_I;          // Variable to store the polar angle value
int previous_direction = 0; // Variable to store the previous direction (0: none, 1: NE, 2: SE, 3: SW, 4: NW)

main {
    // Get the polar angle value of the left joystick and normalize it to 0-360 degrees
    Where_am_I = (get_ipolar(POLAR_LS, POLAR_ANGLE) + 360) % 360;

    // Check if the joystick is moved significantly in any direction
    if (abs(get_val(PS4_LX)) > 50 || abs(get_val(PS4_LY)) > 50) {
        current_direction = 0;

        // Determine the current direction based on polar angle
        if (Where_am_I > 45 && Where_am_I <= 135) {
            current_direction = NE;
        }
        else if (Where_am_I > 135 && Where_am_I <= 225) {
            current_direction = SE;
        }
        else if (Where_am_I > 225 && Where_am_I <= 315) {
            current_direction = SW;
        }
        else {
            // Covers 315° < Angle ≤ 360° and 0° ≤ Angle ≤ 45°
            current_direction = NW;
        }

        // If the direction has changed
        if (current_direction != previous_direction) {
            // Check if none of the specified buttons are pressed
            if (!get_val(XB1_LB) && !get_val(XB1_RB) && !get_val(XB1_RT) && !get_val(XB1_LT)) {
                combo_run(R2_at_direction_change);
                previous_direction = current_direction;
            }
        }
    }
    else {
        // Joystick is not being significantly used
        previous_direction = 0;
    }

    // Optional: For debugging purposes, you can trace the current direction
    set_val(TRACE_1, previous_direction);
}

combo R2_at_direction_change {
    set_val(PS4_R2, 100); // Activate R2 button
    wait(60);              // Wait for 60 ms
    set_val(PS4_R2, 0);    // Release R2 button
    wait(100);             // Wait for 100 ms before allowing the next activation
}
 