// Dynamic Array Implementation for Cronus ZEN
// This implementation provides a dynamic list/array structure with memory management

// Global pointer to store digits as a dynamic list
int String_digits;


init
{	
    // Initialize the dynamic array with size 1 and char type
    // sizeof(char) is used as the type size for storing ASCII characters
    String_digits = List(1, sizeof(char));
    
    // Initialize first element with ASCII code for digit '0'
    List_setIndex(ASCII_DIGIT0, 0, String_digits);
    
    // Clear OLED display and show initial list content
    cls_oled(0);
    String_print(0, 0, 0, 1, String_digits);
}


int count = 0;  // Counter to track number of digits
main
{
    // Set virtual machine to run at maximum speed
    // 1-10 = -9, which is the fastest speed setting
    vm_tctrl(1 - 10);

    // Handle UP button press - Add digit
    if(event_press(PS4_UP) && count < 9) {
        count++;
        
        // Append new digit to list (ASCII_DIGIT0 + count gives next digit's ASCII code)
        String_digits = List_append(ASCII_DIGIT0 + count, String_digits)
        
        // Update display with new list content
        cls_oled(0);
        String_print(0, 0, 0, 1, String_digits);
    }
    
    // Handle DOWN button press - Remove digit
    if(event_press(PS4_DOWN) && count > 0) {
        // Remove last digit from list
        String_digits = List_remove(count, String_digits)
    
        // Update display with new list content
        cls_oled(0);
        String_print(0, 0, 0, 1, String_digits);
        count--;
    }
}


// String printing utility function from string.gpc
// Prints contents of a dynamic list to OLED display
int print_i,
    print_len;
function String_print(int x, int y, int font, int color, int address) {
    print_i = 0;
    print_len = List_getLen(address);  // Get number of elements in list
    // Print each character from the list
    for(print_i=0; print_i<print_len; print_i++) {
        putc_oled(print_i + 1, List_getIndex(print_i, address));
    }
    // Display the complete string on OLED
    puts_oled(x, y, font, print_len, color);
}


// Memory Management Implementation
// This section handles low-level memory operations for the dynamic array

// Memory layout constants
define DATA = 0x00;  // Bit used for storing actual data
define INIT = 0x01;  // Bit used for tracking initialized memory
define HEAP_SIZE_BYTES = 400;  // Total heap size in bytes
define HEAP_SIZE_INT16 = HEAP_SIZE_BYTES/2;  // Heap size in 16-bit words
define TOTAL_BITS = HEAP_SIZE_BYTES * 8;  // Total number of bits available
define TOTAL_ADDRESSABLE_BITS = HEAP_SIZE_BYTES * 4;  // Number of addressable memory locations
int HEAP[HEAP_SIZE_INT16];  // Heap array for memory storage


// List management variables
int List_size,    // Size of the list in bits
    List_pt,      // Pointer to list memory location
    List_i;       // Iterator for list operations

// Function to create a new list with specified length and type size
function List(int length, int typeSize) {
    List_size = typeSize * 8 * length + 3;  // Calculate list size in bits
    List_pt = Memory_malloc(List_size);  // Allocate memory for the list
    Memory_setInt(typeSize, List_pt, 3);  // Set type size in the list header
    if(List_pt > 0) {
        // Initialize list elements to zero
        for(List_i=0; List_i<length; List_i++) {
            Memory_setInt(0x00, List_pt + 3 + List_i * typeSize, typeSize * 8);
        }
    }
    return List_pt;
}

// Function to set an index in the list to a specified value
function List_setIndex(int value, int index, int address) {
    setIndex_bits = List_getTypeSize(address) * 8;  // Get type size in bits
    Memory_setInt(value, address + 3 + index * setIndex_bits, setIndex_bits);
}

// Function to get the value at a specified index in the list
function List_getIndex(int index, int address) {
    getIndex_bits = List_getTypeSize(address) * 8;  // Get type size in bits
    return Memory_getInt(address + 3 + index * getIndex_bits, getIndex_bits);
}

// Function to append a new element to the list
function List_append(int value, int address) {
    append_len = List_getLen(address) + 1;  // Calculate new list length
    append_typeSize = List_getTypeSize(address);  // Get type size
    append_pt = Memory_realloc(address, append_typeSize * 8 * append_len + 3);
    List_setIndex(value, append_len - 1, append_pt);  // Set new element's value
    return append_pt;
}

// Function to remove an element from the list at a specified index
function List_remove(int index, int address) {
    remove_len = List_getLen(address);  // Get current list length
    remove_typeSize = List_getTypeSize(address) * 8;  // Get type size in bits
    address += 3;  // Adjust address to point to list data
    remove_j = 0;  // Iterator for shifting elements
    for(remove_i=0; remove_i<remove_len; remove_i++) {
        if(remove_i != index) {
            remove_val = Memory_getInt(address + remove_i * remove_typeSize, remove_typeSize);
            Memory_setInt(remove_val, address + remove_j * remove_typeSize, remove_typeSize);
            remove_j++;
        }
    }
    Memory_free(address + remove_typeSize * (remove_j));  // Free unused memory
    address -= 3;  // Adjust address back to list header
    return address;
}

// Function to get the type size of a list
function List_getTypeSize(int address) {
    return Memory_getInt(address, 3);
}

// Function to get the length of a list
function List_getLen(int address) {
    getListLen_typeSize = List_getTypeSize(address);  // Get type size
    getListLen_i = 0;  // Iterator for counting elements
    getListLen_len = 0;  // List length
    while(Memory_getBit(address + 3 + getListLen_i, INIT) == 1) {
        getListLen_i++;
        if((getListLen_i % (getListLen_typeSize * 8)) == 0) {
            getListLen_len++;
        }
    }
    return getListLen_len;
}

// Memory management functions

// Function to defragment the heap (not implemented)
function Memory_defragHeap() {
}

// Function to allocate memory of a specified size
function Memory_malloc(int bits) {
    malloc_i = 0;  // Iterator for searching free memory
    while(malloc_i < TOTAL_ADDRESSABLE_BITS) {
        if(!Memory_getBit(malloc_i, INIT) && !Memory_getBit(malloc_i + 1, INIT)) {
            malloc_i++;
            malloc_j = 0;  // Iterator for checking contiguous free memory
            while(!Memory_getBit(malloc_i + malloc_j, INIT) && malloc_j <= bits) {
                if(malloc_j == bits) {
                    malloc_j = 0;
                    while(!Memory_getBit(malloc_i + malloc_j, INIT) && malloc_j <= bits) {
                        Memory_setBit(0x01, malloc_i + malloc_j, INIT);
                        if(malloc_j == bits - 1) {
                            return malloc_i;  // Return allocated memory address
                        }
                        malloc_j++;
                    }  
                }
                malloc_j++;
            }
            malloc_i += malloc_j;
        }
        malloc_i++;
    }
    return -1;  // Return -1 if no free memory is found
}

// Function to reallocate memory to a new size
function Memory_realloc(int address, int bits) {
    malloc_bits = bits;  // Store new size in bits
    while(Memory_getBit(address + realloc_i, INIT) == 1) {
        realloc_i++;
    }
    bits -= realloc_i;
    while(realloc_i < TOTAL_ADDRESSABLE_BITS) {
        if(!Memory_getBit(address + realloc_i, INIT) && !Memory_getBit(address + realloc_i + 1, INIT)) {
            realloc_j = 0;  // Iterator for checking contiguous free memory
            while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits) {
                if(realloc_j == bits) {
                    realloc_j = 0;
                    while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits) {
                        Memory_setBit(0x01, address + realloc_i + realloc_j, INIT);
                        if(realloc_j == bits - 1) {
                            return address;  // Return reallocated memory address
                        }
                        realloc_j++;
                    }  
                }
                realloc_j++;
            }
            realloc_i += realloc_j;
        }
        realloc_i++;
    }
    realloc_new = Memory_malloc(malloc_bits);  // Allocate new memory
    if(realloc_new >= 0) {
        realloc_i = 0;  // Iterator for copying data
        while(Memory_getBit(address + realloc_i, INIT) == 1) {
            Memory_setBit(Memory_getBit(address + realloc_i, DATA), realloc_new + realloc_i, DATA);
            realloc_i++;
        }
        Memory_free(address);  // Free old memory
        return realloc_new;  // Return new memory address
    }
    return -1;  // Return -1 if reallocation fails
}

// Function to free memory at a specified address
function Memory_free(int address) {
    free_i = address;  // Iterator for freeing memory
    while(Memory_getBit(free_i, INIT) == 1) {
        Memory_setBit(0, free_i, DATA);  // Clear data bit
        Memory_setBit(0, free_i, INIT);  // Clear initialized bit
        free_i++;
    }
}

// Function to set an integer value in memory
function Memory_setInt(int value, int address, int bits) {
    value = Memory_pack(value, bits);  // Pack value into binary representation
    for(setInt_i=0; setInt_i<bits; setInt_i++) {
        if(Memory_getBit(address + setInt_i, INIT)) {
            setInt_bitVal = (value & Memory_bitMask(setInt_i)) >> (setInt_i);
            Memory_setBit(setInt_bitVal, address + setInt_i, DATA);
        }
    }
}

// Function to get an integer value from memory
function Memory_getInt(int address, int bits) {
    for(getInt_i=0; getInt_i<bits; getInt_i++) {
        if(Memory_getBit(address + getInt_i, INIT)) {
            getInt_bitVal = Memory_getBit(address + getInt_i, DATA);
            getInt_value = (getInt_value & ~Memory_bitMask(getInt_i)) | (getInt_bitVal << getInt_i);
        }
    }
    getInt_value = Memory_unpack(getInt_value, bits);  // Unpack binary representation
    return getInt_value;
}

// Function to get a bit value from memory
function Memory_getBit(int address, int mode) {
    address *= 2;  // Adjust address for bit-level access
    address += mode;  // Add mode (DATA or INIT) to address
    getBit_int = address / 16;  // Get 16-bit word index
    getBit_bit = address % 16;  // Get bit index within 16-bit word
    return (HEAP[getBit_int] & Memory_bitMask(getBit_bit)) >> (getBit_bit);
}

// Function to set a bit value in memory
function Memory_setBit(int value, int address, int mode) {
    if(abs(value) > 1) return;  // Check for valid bit value
    address *= 2;  // Adjust address for bit-level access
    address += mode;  // Add mode (DATA or INIT) to address
    setBit_int = address / 16;  // Get 16-bit word index
    setBit_bit = address % 16;  // Get bit index within 16-bit word
    HEAP[setBit_int] = (HEAP[setBit_int] & ~Memory_bitMask(setBit_bit)) | (value << setBit_bit);
}

// Function to create a bit mask for a specified bit position
function Memory_bitMask(int bit) {
    return 1 << bit;
}

// Function to create a sign bit mask for a specified bit size
function Memory_makeSign(bits) {
    return 1 << clamp(bits - 1, 0, 31);
}

// Function to create a full mask for a specified bit size
function Memory_makeFullMask(bits) {  
    if (bits == 32) {
        return -1;
    }
    return 0x7FFFFFFF >> (31 - bits); 
}

// Function to create a sign mask for a specified bit size
function Memory_makeSignMask(bits) {
    return Memory_makeFullMask(bits - 1);
}

// Function to pack an integer value into binary representation
function Memory_pack(val, bits) {
    if (val < 0) { 
        return (abs(val) & Memory_makeSignMask(bits)) | Memory_makeSign(bits); 
    }
    return val & Memory_makeSignMask(bits);
}

// Function to unpack binary representation into an integer value
function Memory_unpack(val, bits) {
    if (val & Memory_makeSign(bits)) { 
        return 0 - (val & Memory_makeSignMask(bits)); 
    }
    return val & Memory_makeSignMask(bits);
}