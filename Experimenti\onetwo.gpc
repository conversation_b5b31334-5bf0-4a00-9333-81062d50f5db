int lastPressTime; // Time of the last press
int mode; // 0 = no press, 1 = single press, 2 = double press
int currentTime; // For timing calculations
int timer; // Accumulated timer

init { 
  lastPressTime = 0; 
  mode = 0; 
  currentTime = 0; 
  timer = 0;
}

main { 
  timer += get_rtime();
  // On button press: Check the time gap to decide mode.
  if(event_press(XB1_LS)) { 
    currentTime = timer;
    if(lastPressTime != 0 && (currentTime - lastPressTime < 400)) { 
      mode = 2; // Double press detected 
    } else { 
      mode = 1; // Single press 
    } 
    lastPressTime = currentTime; 
  }

  // When XB1_LS is held down, activate triggers based on mode.
  if(get_val(XB1_LS)) { 
    if(mode == 2) { 
      // Double press: activate both left and right triggers.
      set_val(XB1_LT, 100); 
      set_val(XB1_RT, 100); 
    } else if(mode == 1) { 
      // Single press: activate only left trigger.
      set_val(XB1_LT, 100); 
      set_val(XB1_RT, 0); 
    } 
  } 
  // Reset mode and lastPressTime only if XB1_LS is not pressed and 400ms have passed since the last press
  if(!get_val(XB1_LS) && (timer - lastPressTime >= 400)) { 
    set_val(XB1_LT, 0); 
    set_val(XB1_RT, 0); 
    mode = 0; 
    lastPressTime = 0; 
  } 
}