// <PERSON><PERSON><PERSON>funk<PERSON>, die kontinuierlich läuft
main {
    // Überprüft, ob die XB1_LT-Taste gehalten wird und die XB1_DOWN-Taste gedrückt wird
    // oder ob die XB1_DOWN-Taste gehalten wird und die XB1_LT-Taste gedrückt wird
    if((get_ival(XB1_LT) && event_press(XB1_DOWN)) || (get_ival(XB1_DOWN) && event_press(XB1_LT))) {
        // Startet die Combo mit dem Namen 'maxcmb'
        combo_run(maxcmb);
    }
}

// Definition der Combo namens 'maxcmb'
combo maxcmb {
    // Setzt den rechten Stick auf X-Achse auf den Wert 30 (leicht nach rechts)
    set_val(XB1_RX, 30);
    
    // Setzt den rechten Stick auf Y-Achse auf den Wert 100 (vollständig nach oben)
    set_val(XB1_RY, 100);
    
    // Wartet für 80 Millisekunden
    wait(80);
    
    // Wartet zusätzlich für 1600 Millisekunden
    wait(1600);
    
    // Drückt die Square-Taste mit voller Intensität (Wert 100)
    set_val(XB1_X, 100);
    
    // Wartet für 1260 Millisekunden
    wait(1260);
}