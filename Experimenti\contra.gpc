// Script to invert left stick angle by 180° when RB is pressed
int invertActive; // Track if inversion is active

main {
    // When RB is pressed
    if(get_val(XB1_LS)) {
        // Get current left stick angle and invert it
        set_polar(POLAR_RS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
        set_val(XB1_RB, 100);
        invertActive = TRUE;
    } else {
        // When RB is released, stop modifying the stick
        if(invertActive) {
          set_polar(POLAR_LS, 0, 0);
            set_val(XB1_RB, 0);
            invertActive = FALSE;
        }
    }
}