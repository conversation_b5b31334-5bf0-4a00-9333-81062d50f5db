
        // Script was generated with < FIFA 22 S.G.I > ver. 17.7 Date :11/09/21 Time: 5:46:54 AM
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
  \__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
   $$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
   $$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
   $$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
   $$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
   $$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
   $$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
   $$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
   \__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS  


define time_to_dblclick     = 300; // Time to Double click     
//====================================================
// DEVICE :  Cronus ZEN device 
//====================================================
//====================================================
define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_LB;     
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;                 
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_PR1_ROULETTE_SKILL        =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_RBND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_RBND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_PR1_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_PR1_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
define SCOOP_TURN_SKILL          =49; // FIFA 22
define BALL_ROLL_STEP_OVER_SKILL      =50; // FIFA 22
define CANCELED_4_PR1_TURN_SKILL    =51; // FIFA 22
define FAKE_SHOT_CANCEL_SKILL         =52; // FIFA 22
//--------------------------------------------------------------   

define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 80;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int Temp; 
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
int RS_Sens  = 50
                                            
 int RS_X , RS_Y ;  
 define SKILLR_X      = XB1_RX ;
define SKILLR_Y      = XB1_RY ;
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
int run_combo_juggle = FALSE;
int pre_juggle ;

main {

// PADDLES
	if (get_val(XB1_PL2)) {
			set_val(XB1_LB,100);
			set_val(XB1_RB,100);
			set_val(XB1_Y,100);
		}
		
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_A,100);
		}

if (get_val(XB1_PL1)){set_val(XB1_LB,100); }
if (event_press(XB1_PR1)){combo_run(FAKE_SHOT2) }   

if(abs(get_val(XB1_RX) <12 ) && abs(!get_val(XB1_RX) <12)){
vm_tctrl(-6);
}

     if(get_val(SprintBtn)){
     set_val(SprintBtn,100);} // will need this in main to force full speed while analog sprint on in fifa settings
                                                  
    //--------------------------------------------------------------
    //  turn ON Penalty  hold  L1 and press OPTIONS 
    if(get_val(XB1_LB)){                      
        if(event_press(XB1_MENU)){             
            onoff_penalty = !onoff_penalty;   
            f_set_notify(onoff_penalty);           
        }                                         
       set_val(XB1_MENU,0);                    
    }                                              
                                                   
	  //-----------------------------------------  
	  // ON / OFF FREE KICK MODE                   
	  // hold L1/LB and press SHARE/VIEW           
	  if(get_val(XB1_LB)){                          
        if(event_press(XB1_VIEW)){             
            onoff_FK = !onoff_FK;               
            f_set_notify(onoff_FK);           
        }                                       
       set_val(XB1_VIEW,0);                    
    }                                           
    //----------------------------------------- 
  if(onoff_penalty || onoff_FK) {// Penalties_FKeecks
       ////////////////////////////////  
       // LED color indication  
       vm_tctrl(0);         
       if(onoff_FK)        colorled(0,0,0,2); 
       else if(onoff_penalty)   colorled(0,2,2,0);
    if(onoff_FK){ f_FREE_KICK ();  }             
    if(onoff_penalty){ fPenalties ();  }  
  } else { // all other code
  
  


    //========================================== 
    // SUPER KNOCK ON                            
   /* if(event_press(SprintBtn) && !tap ) combo_run(ONE_TAP); 
                                                 
    if(event_press(SprintBtn) &&  tap ){     
    	   vm_tctrl(0);combo_run(SPRINT_cmb);       
    } 
 */
  



/*
	    if(get_val(XB1_RS) && (abs(get_val(MOVE_X))> 30 || abs(get_val(MOVE_Y))> 30)) {// running to Right from miedfield
	     set_val(XB1_RS,0);

         combo_run(BALL_ROLL_CHOP);
	
	    }*/


	    
	   
	    

	    


/*

if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RS)) {
             load_slot (2);
      }
      set_val(XB1_RS,0);
}
                 	if(  event_press(XB1_PL2)){ 
	vm_tctrl(0);
		combo_run(CHIP_SHOT);   
	}                       
	set_val(XB1_PL2,0); */     
       ////////////////////////////////  
       // LED color indication           
       colorled(2,0,0,0);

    
        if(!get_val(XB1_RS) && (abs(get_val(SKILLR_X))> 45 || abs(get_val(SKILLR_Y))> 45)){ // getting RS zones
	     calc_RS() ;
	    RS_X = ZONE_P_RS[zone_RS][0];
        RS_Y = ZONE_P_RS[zone_RS][1];
        

        }else{
        zone_RS=8; //center position 
        }
        
    

    
     /*  if (  get_val(FinesseShot) && ( get_val(XB1_RX) > 60 || get_val(XB1_RX) < -60 || get_val(XB1_RY) < -60 || get_val(XB1_RY) > 60 )
       && !get_val(PassBtn) && !get_val(PlayerRun) && !get_val(SprintBtn)&& !get_val(XB1_RS)  ) { 
      set_val(FinesseShot,0) 
    vm_tctrl(0);
    combo_run(SOMBRERO_FLICK)  ; 
    }
    
*/
 
    
    
        if (get_val(SprintBtn)){
            sensitivity(XB1_LX, NOT_USE, LS_Sprint_Sens);
            sensitivity(XB1_LY, NOT_USE, LS_Sprint_Sens);
        }
        if (!get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)  ){
            sensitivity(XB1_LX, NOT_USE, LS_Sens);
            sensitivity(XB1_LY, NOT_USE, LS_Sens);
        }
         
   //--- DEFENSE                               
   if(defence_on) f_defence();                 
                                                
    //--- Use Open Up MOD                                                         
    if(event_press(ShotBtn) || event_press(PassBtn) || event_press(FinesseShot) || (abs(get_val(XB1_RX)>20) || abs(get_val(XB1_RY)>20))){ 
    vm_tctrl(0);
        combo_run(OPEN_UP_cmb);                                                   
    }                                                                            
                                                                                 
    	if (get_val(PassBtn) || get_val(ShotBtn) || get_val(SprintBtn) || get_val(FinesseShot) || get_val(ThroughBall)  || get_val(CrossBtn) ) {
	combo_stop(SEQ2);
	combo_stop(SEQ1);
	combo_stop(SKILL_SEQ3);
	combo_stop(SKILL_SEQ4);
	combo_stop(BALL_ROLL_CHOP);
	combo_stop(ROULETTE);
	pre_juggle = 0 ;
	run_combo_juggle = FALSE;
	}
	
	
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot)){ 
	      if(event_press(ShotBtn) ){ 
	      vm_tctrl(0);
		        combo_run(Timed_Finesse_Finish ); 
	      } 
         set_val(ShotBtn,0);
    } 
    
    
      if(abs(get_val(MOVE_X))> 45 || abs(get_val(MOVE_Y))> 45){
        
        calc_zone (); 
        LX = ZONE_P[zone_p][0];
        LY = ZONE_P[zone_p][1];
    }
    

    //----------------------------------------------------------- 
                                      
   if( !combo_running(LOCK_RS)  && !get_val(XB1_LT)&& !get_val(XB1_RS) &&  !get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(FinesseShot)) { // all Skills mode 
                                            

              //LS DIRECTIONS OF PLAYER//
     
              if(zone_p == 2  ) // RIGHT LS
    {                                             
	       ALL_SKILLS_RIGHT();
	      
	      }
	      
	          if(zone_p == 6 ) // LEFT LS
    { 
	       ALL_SKILLS_LEFT();
	      }
	      
	          if(zone_p == 0  ) // UP LS
    { 
	        ALL_SKILLS_UP();
	        
	      }
	      
	          if(zone_p == 4   ) // Down LS
    {  
	      
	        ALL_SKILLS_DOWN();
	      
	      }
	      
              if(zone_p == 1   ) // UP-Right
    {  
	      
	      ALL_SKILLS_UPR();
	      
	      }
	      
	          if(zone_p == 3  ) // Down-Right
    {   
          ALL_SKILLS_DOR();
	     
          }
       
         if(zone_p == 7  )  // UP-Left
    {   
       
	      ALL_SKILLS_UPL();
	      
          }
        
         if(zone_p == 5  ) // Down-Left
    {   
          ALL_SKILLS_DOL();
	      
          } 
	      
	      
         if(abs(get_val(XB1_RY))<15  && abs(get_val(XB1_RX))<15){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }  
        

	      
	      set_val(SKILL_STICK_X,0); 
          set_val(SKILL_STICK_Y,0);
	      	      
    }
    // end of ALWAYS ON  
    //===============================
    //  ADDITIONAL SKILL
    //===============================
      /* sensitivity(XB1_LT, NOT_USE, 15);
      if(get_val(XB1_LT) && !get_val(SprintBtn)){ 
      set_val(SprintBtn,get_val(XB1_LT));
      
      set_val(XB1_LT,0);
	      		
	      		
      }   */                  
                                                
    //========================================
    // *** DYNAMIC FINISHING ***
    //========================================
    if(Dynamic_Finish_onoff) f_dynamic_finish (); 
    //===============================================
    //    GRROUND PASSES  MIN / MAX
    //===============================================
    if(!get_val(PaceCtrol)){    
        if( !get_val(SprintBtn) && !get_val(PlayerRun)){
	        if(get_val(PassBtn)){
	       
	                combo_run(Ground_Pass_MIN_cmb);                     
         
	        }
        }
    }
    
        if(!get_val(PaceCtrol)){    
        if( !get_val(SprintBtn) ){
	        if(!get_val(PlayerRun) && get_val(ThroughBall)){
	       
	                combo_run(THROGH_Pass_MIN_cmb);                     
         
	        }
        }
    }
    
    
            if(!get_val(PaceCtrol)){    
        if( !get_val(SprintBtn) ){
	        if(get_val(PlayerRun) && get_val(ThroughBall)){
	       
	                combo_run(Long_THROGH_Pass_MIN_cmb); 
	                
	                if ( event_release(ThroughBall)){
	                combo_stop(Long_THROGH_Pass_MIN_cmb);
	                combo_run(DOUBLE_TAP_TROUGH_cmb);
	                }
         
	        }
        }
    }
    
    // PaceCtrol
    //===============================================
    //    TROUGH PASSES  MIN / MAX
    //===============================================
     if( get_val(PlayerRun)){
    if(event_release(ThroughBall)){
    vm_tctrl(0);
     combo_run(DOUBLE_TAP_TROUGH_cmb) ;
     }
     }// PaceCtrol
    // all Skills mode                
  }// all other code
   //--------------------------------------------------------------
} // end of main block  

combo CANCEL_FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    LA(0,0);
    wait(400);
    set_val(PS4_L2,100);
    set_val(PS4_L1,100);
    set_val(PS4_R1,100);
    set_val(PS4_R2,100);
    wait(70);      
}

combo Four_Touch {
    LA(LX,LY);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    LA(0,0);
    wait(400);
    }



combo Sprint_DR{
set_val(PaceCtrol,100);
set_val(SprintBtn,20);
set_val(PlayerRun,100);
wait(160);
set_val(PaceCtrol,0);
set_val(SprintBtn,0);
set_val(PlayerRun,100);
wait(120);
}

combo Sprint_DR_Stop{
combo_stop(Sprint_DR);
wait(1000);

}

                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
int cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;
int Dynamic_Finish_onoff = TRUE;

function f_dynamic_finish () { 


    if(event_release(CrossBtn)){
        cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 2500;
    }
    
    if(cross_timer){
        cross_timer -= get_rtime();
    }
    
    if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && cross_timer <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){
            set_val(ShotBtn,0);
            INSIDE_BOX_AIM();     
            if( after_sprint_timer > 225  ) {
                UltimatePower = random(205,209 ) ; 
                drval=0;
                combo_restart(Dynamic_Shoot); 
            }
            if( after_sprint_timer <= 0  ) {
                UltimatePower = 235 ;
                drval = 100;
                combo_restart(Dynamic_Shoot); 
            }
        }
    } 
    /// FakeShot Support avoid Conflictions
    if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  
        combo_stop(Dynamic_Shoot);
    }
  
}


combo BALL_ROLL_Step_Over { 
    
	RA_L_R (); //  <-/->  
	wait(300);
	
	RA_UP();      
	wait(60);   
} 

combo DRAG_NOT_BACK {            
	set_val(MOVE_X,inv(get_val(XB1_LX)));    
	set_val(MOVE_Y,inv(get_val(XB1_LY)));   
    set_val(FinesseShot,100);  
    set_val(PlayerRun,100);  
	wait(100);
	LA(0,0);
	wait(120);
	
	}
	
	combo DRAG_BACK {            
	set_val(MOVE_X,inv(LX));    
	set_val(MOVE_Y,inv(LY));   
 set_val(FinesseShot,100);  
 set_val(PlayerRun,100);  
	wait(60);                  
	set_val(MOVE_X,inv(LX));    
	set_val(MOVE_Y,inv(LY));   
 set_val(FinesseShot,100);  
 set_val(PlayerRun,100);  
	if(Sombrero) set_val(XB1_RS,100);
	wait(40);                  
} 

combo FAKE_SHOT {
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    wait(40);
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60); 
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    }
    
    combo PROTECT_v1_Stop {
    combo_stop(PROTECT_v1);
    wait(1000);
    }
	
combo inv_Fake_SHOT {
	set_val(MOVE_X,inv(get_val(XB1_LX)));    
	set_val(MOVE_Y,inv(get_val(XB1_LY))); 
    set_val(ShotBtn,100);  
    wait(40); 
    set_val(MOVE_X,inv(get_val(XB1_LX)));    
	set_val(MOVE_Y,inv(get_val(XB1_LY))); 
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);  
    set_val(MOVE_X,inv(get_val(XB1_LX)));    
	set_val(MOVE_Y,inv(get_val(XB1_LY))); 
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    }
    
combo SPRint_STOP {
LA(0,0);
wait(60);
LA(0,0);
set_val(SprintBtn,100);
set_val(PlayerRun,100);
wait(160);
LA(0,0);
wait(80);
}

combo ROLL_SCOOP_TURN {
    LA(0,0);
    RA_L_R();
    wait(260);
    set_val(XB1_LY,inv(LY));
    set_val(ShotBtn,100); 
    wait(40); 
    set_val(XB1_LY,inv(LY));
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    set_val(XB1_LY,inv(LY));
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
} 

combo HEELtoHEE1L {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}   

combo PROTECT {
set_val(PaceCtrol,100);
wait(800);
}
combo SEQ1 {
call (FAKE_SHOT_STOP);
LA(0,0);
wait(280);
call(ELASTICO);
}

combo SEQ2 {
call (FAKE_SHOT_STOP);
LA(0,0);
wait(280);
LX=0;
call (LA_CROQUETA);
wait(500);

}

combo FAKE_PASS{
    LA(0,0);
    set_val(SprintBtn,100);
    set_val(ShotBtn,100);  
    wait(40);
    LA(0,0);
    set_val(SprintBtn,100);
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(40);
    LA(0,0);
    set_val(SprintBtn,100);
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    }

combo FAKE_RABONA{
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    wait(40);
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    }
    
    
    combo FAKE_RABONA_True{
    LA(inv(move_lx),inv(move_ly));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    wait(40);
    LA(inv(move_lx),inv(move_ly));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);
    LA(inv(move_lx),inv(move_ly));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    }
    
    


combo SPRINT_cmb { 
    set_val(SprintBtn,85);
	wait(40);
	wait(40);
    set_val(SprintBtn,85);
	wait(40);
	wait(40);
} 

combo SKILL_SEQ3 {
call(FAKE_RABONA);
LA(0,0);
wait(400);
wait(200);
call(ELASTICO);
wait(500);
set_val(PaceCtrol,85);
wait(1000);
}

combo SKILL_SEQ4 {
call(HALF_DRAG);
wait(600);
call (Four_Touch);
wait(600);
set_val(PaceCtrol,85);
wait(1000);
}

combo HOCUS_POCUS {
    LA(0,0);
	RA_DOWN (); // Down    
	wait(w_rstick);        
	right_on = FALSE;      
	RA_L_R () ;
	LA(0,0);// L    
	wait(w_rstick);        
	RA_DOWN ();
	LA(0,0);// down 
	wait(w_rstick);        
	right_on = TRUE;      
	RA_L_R () ; 
	LA(0,0);// R    
	wait(w_rstick);        
} 


combo FAKe_DRAGBACK {
calc_zone (); 
wait(20);
dEnd = zone_p
calc_relative_xy(dEnd);
LX = move_lx;
LY = move_ly;
LA(move_lx,move_ly);
wait(30);
LA(inv(LX),inv(LY));
set_val(FinesseShot,100);
set_val(PlayerRun,100);
wait(380);
dEnd = zone_p + 2
calc_relative_xy(dEnd);
LA(move_lx,move_ly);
wait(60);
LA(0,0);
wait(60);
dEnd = zone_p - 2
calc_relative_xy(dEnd);
LA(move_lx,move_ly);
wait(60);
LA(0,0);
wait(60);
LA(inv(move_lx),inv(move_ly));
wait(400);
}







combo initiate_juggle {
set_val(PaceCtrol,100);
set_val(FinesseShot,100);
wait(50);
set_val(PaceCtrol,100);
set_val(FinesseShot,0);
wait(50);
}

combo dribble_juggle {
set_val(XB1_RS,100);
wait(700);
RA_UP();
wait(80);
}















combo HALF_DRAG { 
set_val(MOVE_X,inv(LX)  );    
set_val(MOVE_Y,inv(LY)  );   
set_val(FinesseShot,100);  
set_val(PlayerRun,100);  
wait(70);
set_val(FinesseShot,100);  
set_val(PlayerRun,100); 
LA(0,0);
wait(160);
}


combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
}  

combo BALL_ROLL_CHOP {         
	RA_L_R () ;    // Left or Right 
	wait(300);                     
	RA_ZERO ();    // ZERO         
	wait(w_rstick);                
	RA_OPP () ;    // Left or Right
	wait(300);                    
}  


function LA_LOCATIONS(){

// Moving to UP
    if(zone_p == 0  ) // UP
    {  
        LA (0, -96);
    }
    // Moving to DOwn     
    if(zone_p == 4 ) // Down
    {  
        LA (0, 96);
    }
    // Moving to Right     
    if(zone_p == 2 ) // Right
    { 
        LA (96, 0);
    }
    // Moving to LEFT     
    if(zone_p == 6 ) // Left
    {  
       LA (-96, 0);
    }
    // Moving to UP-Right
    if(zone_p == 1 ) // UP-Right
    {  
        LA (96, -96);
    }
    // Moving to Down-Right     
    if(zone_p == 3 ) // Down-Right
    {   
        LA (96, 96);
    }
    // Moving to UP-Left    
    if(zone_p == 7)  // UP-Left
    {   
        LA (-96, -96);
    }
    // Moving to Down-Left     
    if(zone_p == 5) // Down-Left
    {   
        LA (-96, 96);
    } 
    
    }
    
    
    combo LOCK_RS {
    set_val(XB1_RX,0);
    set_val(XB1_RX,0);
    wait(100);
    }
    
    combo  TURN_BACK  {       
    LA(LX,LY);
	RA_DOWN ();             
	wait(80);
	LA(LX,LY);
	RA_ZERO ();             
	wait(80);
	RA_DOWN ();             
	wait(80); 
	RA_ZERO ();             
	wait(80);
}      
    
    function ALL_SKILLS_RIGHT() {
    ///////////////////            RIGHT           /////////////////////////
///////////////////////////////////////////////////////////////////////
                              
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) //UP SP DONE
    {  
       vm_tctrl(0);
	   flick_up = TRUE;
	  right_on = FALSE;
	   combo_run(TURN_AND_SPIN);
       
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down SP done
    {  
       vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = TRUE;
	   combo_run(TURN_AND_SPIN);
    }
    
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right SP DONE
    { 
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(okosha_flick);
    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(TURN_BACK);
	   
    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP_DONE
    {  

       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(drag_to_heel);
    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {   

       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(drag_to_heel);
    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {   
     
       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = FALSE;    
	   combo_run(TREE_TOUCH);   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {   
  
      vm_tctrl(0);
	  flick_l = TRUE; 
	  right_on = TRUE;   
	  combo_run(TREE_TOUCH);   
    }                                             


   
       
       }
       
       combo drag_to_heel{
       set_val(PlayerRun,100);
       LA(LX,LY);
       RA_DOWN();
       wait(w_rstick);
       set_val(PlayerRun,100);
       RA_ZERO();
       wait(w_rstick);
       set_val(PlayerRun,100);
       RA_L_R();
       wait(w_rstick);
       }
       
       
       
       
       


function ALL_SKILLS_LEFT() {
///////////////////            LEFT           /////////////////////////
///////////////////////////////////////////////////////////////////////

 
                                  
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = TRUE;    
	   combo_run(TURN_AND_SPIN);   
       
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down SP DONE   
    {  
       vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = FALSE;
	   combo_run(TURN_AND_SPIN);
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right AUTO DONE
    { 
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(TURN_BACK);
    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) //  Down SP done
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(okosha_flick);
    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(TREE_TOUCH);
    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {   
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(TREE_TOUCH);
    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {   

       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = TRUE;    
	   combo_run(drag_to_heel);   // 38. Fake Drag Back
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left AUTO DONE
    {   
    
      vm_tctrl(0);
	  flick_l = TRUE; 
	  right_on = FALSE;   
	  combo_run(drag_to_heel);  
    }                                             


        }
        
    combo SKILLED_BRIDGE_cmb {
    set_val(PaceCtrol,100);
    wait(25);
    set_val(PaceCtrol,100);
    set_val(FinesseShot,100);
    wait(70);
    set_val(PaceCtrol,100);
    wait(70);
    set_val(PaceCtrol,100);
    set_val(FinesseShot,100);
    wait(80);
    wait(80);
} 
        
        
        function ALL_SKILLS_UP() {
///////////////////            UP           /////////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(okosha_flick);
       
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down
    {  
	   vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(TURN_BACK);
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right   SP DONE
    { 
       vm_tctrl(0);
	   flick_up = TRUE;
	   right_on = TRUE;
	   combo_run(TURN_AND_SPIN);
    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left SP DONE
    {  
       vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = FALSE;
	   combo_run(TURN_AND_SPIN);
    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // Up RIGHT SP DONE
    { 

       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = TRUE;    
	   combo_run(drag_to_heel);

    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(TREE_TOUCH);

    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left AUTO DONE
    {  

      vm_tctrl(0);
	  flick_l = TRUE; 
	  right_on = FALSE;
	  combo_run(drag_to_heel);
   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP Done
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(TREE_TOUCH);
   
    }   

    
    }
    
    
            
        
        function ALL_SKILLS_DOWN() {
///////////////////            DOWN           /////////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP AUTO DONE
    {  
        vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(TURN_BACK);
       
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down  DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(okosha_flick);
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right SP DONE
    { 
       vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = FALSE;
	   combo_run(TURN_AND_SPIN);
    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left SP DONE
    {  
       vm_tctrl(0);
	   flick_up = TRUE;
	   right_on = TRUE;
	   combo_run(TURN_AND_SPIN);
    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(TREE_TOUCH);
    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right AUTO DONE
    {   

       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = FALSE;    
	   combo_run(drag_to_heel);
    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {   
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(TREE_TOUCH);   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {   
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(drag_to_heel);
    }   
    

    
    }
   
   combo LATERAL_HEELtoHEEL {  
    set_val(PlayerRun,100);
    RA_OPP () ;            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_ZERO ();            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_L_R () ;            
    wait(60);//           
    wait(300);            
} 
    
            function ALL_SKILLS_UPR() {
///////////////////            UP_RIGHT           /////////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP AUTO DONE
    {  
    
       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = FALSE;    
	   combo_run(LATERAL_HEELtoHEEL);
	   
       
       
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(BALL_ROLL_CHOP);
    
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right SP DONE
    { 
    

       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(BALL_ROLL_CHOP);

    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left AUTO DONE SP
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);

    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(SKILLED_BRIDGE_cmb);

    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {   
       vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = TRUE;
	   combo_run(TURN_AND_SPIN);

    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {          
       vm_tctrl(0);
	   flick_up = TRUE;
	   right_on = FALSE;
	   combo_run(DRAG_BACK_2_zones);
   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(Heel_Chop_Turn);
      
    }   
    

    
    }
    
    combo Heel_Chop_Turn {
    dEnd = zone_p - 1
    calc_relative_xy(dEnd);
    LX = move_lx;
    LY = move_ly;
    LA(move_lx,move_ly);
    set_val(ShotBtn,100);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    wait(30);
    dEnd = zone_p 
    calc_relative_xy(dEnd);
    LX = move_lx;
    LY = move_ly;
    LA(inv(move_lx),inv(move_ly));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100);
    wait(60);
    LA(inv(move_lx),inv(move_ly));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(40);
    dEnd = zone_p 
    calc_relative_xy(dEnd);
    LX = move_lx;
    LY = move_ly;
    LA(inv(move_lx),inv(move_ly));
    wait(300);
    }
    
    
    
    
    
    
    
    
    
     function ALL_SKILLS_DOR() {
///////////////////            SOWN_RIGHT           /////////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP SP Done
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);
   
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down (AUTO DONE)
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(BALL_ROLL_CHOP);
    
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right
    { 
    
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(LATERAL_HEELtoHEEL);
	  

    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left DONE SP
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);

    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(DRAG_BACK_2_zones);
 

    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {   
      vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(SKILLED_BRIDGE_cmb);

    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left DONE SP
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(Heel_Chop_Turn);

   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {  
            vm_tctrl(0);
	   flick_up = TRUE;
	   right_on = TRUE;
	   combo_run(DRAG_BACK_2_zones);
      
    }   
    

    
    }
    
    
    
         function ALL_SKILLS_UPL() {
///////////////////            UP_LEFT           ///////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(LATERAL_HEELtoHEEL);

   
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(Four_Touch);
    
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right SP DONE
    { 
    
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);

    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left AUTO DONE
    {  

	   flick_l = TRUE; 
	   right_on = FALSE;    
	   combo_run(BALL_ROLL_CHOP);

    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right AUTO DONE
    {  
           vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = TRUE;
	   combo_run(DRAG_BACK_2_zones);
 

    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SP DONE
    {   
           vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(Heel_Chop_Turn);


    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(SKILLED_BRIDGE_cmb);

   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {  
            vm_tctrl(0);
	   flick_up = TRUE;
	   right_on = FALSE;
	   combo_run(TURN_AND_SPIN);
      
    }   
    

    
    }
    
   combo CANCELED_barba {  
    LA(0,0);
    RA_UP ();      // up   
    wait(w_rstick); 
    LA(0,0);
    RA_ZERO ();    // ZERO  
    wait(w_rstick);
    LA(0,0);
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    LA(inv(LX),inv(LY));
    set_val(XB1_LT,100);
    set_val(XB1_RT,100);
    wait(500);
}     


        
         function ALL_SKILLS_DOL() {
///////////////////            UP_LEFT           ///////////////////////
///////////////////////////////////////////////////////////////////////
                                 
    // Moving to UP
    if(zone_RS == 0  && !flick_up ) // UP
    {  
    
           vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(Four_Touch);


   
    }
    // Moving to DOwn     
    if(zone_RS == 4   && !flick_d) // Down AUTO DONE
    {  

       vm_tctrl(0);
	   flick_l = TRUE; 
	   right_on = FALSE;    
	   combo_run(LATERAL_HEELtoHEEL);
	   
                  
    
    }
    // Moving to Right     
    if(zone_RS == 2  && !flick_r ) // Right DONE SP
    { 
    
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE;
	   combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);

    }
    // Moving to LEFT     
    if(zone_RS == 6  && !flick_l ) // Left SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = FALSE;
	   combo_run(BALL_ROLL_CHOP);

    }
    // Moving to UP-Right
    if(zone_RS == 1  && !flick_r ) // UP-Right SP DONE
    {  
       vm_tctrl(0);
	   flick_r = TRUE; 
	   right_on = TRUE
	   combo_run(Heel_Chop_Turn); 

    }
    // Moving to Down-Right     
    if(zone_RS == 3  && !flick_r) // Down-Right SPO DONE
    {   
  
           vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = FALSE;
	   combo_run(DRAG_BACK_2_zones);

    }
    // Moving to UP-Left    
    if(zone_RS == 7  && !flick_l)  // UP-Left SP DONE
    {  
    
      vm_tctrl(0);
	   flick_d = TRUE;
	   right_on = TRUE;
	   combo_run(DRAG_BACK_2_zones);


   
    }
    // Moving to Down-Left     
    if(zone_RS == 5  && !flick_l) // Down-Left SP DONE
    {  
       vm_tctrl(0);
	   flick_l = TRUE;
	   right_on = TRUE;
	   combo_run(SKILLED_BRIDGE_cmb);
      
    }   
    

    
    }
    
    
    combo ALT_ELASTICO { 
    set_val(FinesseShot,100);
	RA_DOWN ();    // down  
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	set_val(FinesseShot,100);
	wait(w_rstick);         
	RA_L_R ();     //  <-/-> 
	set_val(FinesseShot,100);
	wait(w_rstick);       
} 
    
    
    
    combo DRAG_TO_UP {
   set_val(FinesseShot,100);
   set_val(PlayerRun,100);
   LA (LX,inv(LY)); 
   wait(212); 
   }
    



        
 
        
combo DRAG_BACK_2_zones {            
	set_val(MOVE_X,inv(LX));    
	set_val(MOVE_Y,inv(LY));   
    set_val(FinesseShot,100);  
    set_val(PlayerRun,100);  
	wait(100); 
	LA(0,0);
	wait(300);
	dEnd = zone_p + 2
    calc_relative_xy(dEnd);
    LX = move_lx;
    LY = move_ly;
    LA(move_lx,move_ly);
    wait(400);
	
	}


combo BALL_ROLL {
    LA(0,0);
	RA_L_R () ;    // Left or Right 
	wait(330); 
	wait(30);
} 



combo TRIPLE_ELASTICO { 
	RA_DOWN ();      // Down 
	wait(w_rstick);         
	right_on = TRUE;      
	RA_L_R () ;    // R  
	wait(w_rstick);       
	RA_DOWN ();    // down 
	wait(w_rstick);       
	right_on = FALSE;     
	RA_L_R () ;    // L   
	wait(w_rstick);      
} 


combo WAKA_WAKA {
	RA_OPP();  // L       
	wait(w_rstick);       
	LA (0,0);             
	RA_UP();       // up  
	wait(w_rstick);                         
	LA (0,0);             
	RA_L_R()     // L     
	wait(w_rstick);       
	right_on = !right_on; 
	LA_L_R();             
	wait(600);                 
}  










////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int general_timer;  
int ping_RBll;
int Timed_wait;
int wait_RBctive;
combo Timed_Roll {
set_val(SprintBtn,0);
set_val(ShotBtn,0);
	RA_UP();  
	set_val(SprintBtn,0);
set_val(ShotBtn,0);// up                     
	wait(w_rstick);                          
	RA_ZERO ();  
	set_val(SprintBtn,0);
set_val(ShotBtn,0);// ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();  
	set_val(SprintBtn,0);
set_val(ShotBtn,0);// down                  
	wait(w_rstick);  
}

combo okosha_flick{
LA(0,0);
call(SPRint_STOP);
set_val(PlayerRun,100);
LA(0,0);
RA_L_R();
wait(250);
}




combo Dynamic_Shoot {
   
    set_val(ShotBtn, 100);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(UltimatePower);
    
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(XB1_LS,100);
    INSIDE_BOX_AIM();
    wait(50);
    
    set_val(ShotBtn, 0);
    set_val(XB1_LS,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(800); 
}


combo Dynamic_timed_Shoot {

INSIDE_BOX_AIM();
set_val(ShotBtn,0);
wait(10);
    set_val(ShotBtn, 100);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(UltimatePower);
    
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(XB1_LS,100);
    INSIDE_BOX_AIM();

    wait(Timed_wait+ping);
    
    INSIDE_BOX_AIM();
    set_val(ShotBtn,wait_RBctive);
    wait(Timed_wait+ping);
    set_val(ShotBtn, 0);
    set_val(XB1_LS,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait_RBctive=0;
    Timed_wait=0;
    wait(600); 
}
function INSIDE_BOX_AIM() { 
  // Moving to UP
    if((get_val(XB1_LX) > -30 && get_val(XB1_LX) <30) && get_val(XB1_LY) < -35 ) // UP
    {  
        LA (0, -100);
    }
    // Moving to DOwn     
    if((get_val(XB1_LX) > -30 && get_val(XB1_LX) <30) && get_val(XB1_LY) > 35 ) // Down
    {  
        LA (0, 100);
    }
    // Moving to Right     
    if((get_val(XB1_LY) > -30 && get_val(XB1_LY) <30) && get_val(XB1_LX) > 35 ) // Right
    { 
        LA (100, 0);
    }
    // Moving to LEFT     
    if((get_val(XB1_LY) > -30 && get_val(XB1_LY) <30) && get_val(XB1_LX) < -35 ) // Left
    {  
        LA (-100, 0);
    }
    // Moving to UP-Right
    if(get_val(XB1_LX) > 30  && get_val(XB1_LY) < -50 ) // UP-Right
    {  
        LA (100, -100)
    }
    // Moving to Down-Right     
    if(get_val(XB1_LX) > 30 && get_val(XB1_LY) > 50 ) // Down-Right
    {   
        LA (100, 100)
    }
    // Moving to UP-Left    
    if(get_val(XB1_LX) < -30 && get_val(XB1_LY) < -50)  // UP-Left
    {   
        LA (-100, -100)
    }
    // Moving to Down-Left     
    if(get_val(XB1_LX) < -30 && get_val(XB1_LY) > 50) // Down-Left
    {   
        LA (-100, 100);
    }    
 }
int ground_pass_timer; 
int Ground_Pass_MIN = 95;
int Ground_Pass_MAX = 250;
int GP_difference;
int DoubletapGroundPass = FALSE;
int low_driver_pass = TRUE;

combo THROGH_Pass_MIN_cmb {
    
	set_val(ThroughBall,100);
	wait(95);
	set_val(SprintBtn,15);
    wait(30);
    wait(370);
    }
    
    combo Long_THROGH_Pass_MIN_cmb {
    set_val(PlayerRun,100);
    set_val(FinesseShot,100);
	set_val(ThroughBall,100);
	wait(95);
	set_val(PlayerRun,100);
    set_val(FinesseShot,100);
    wait(400);
    }
    
    


combo Ground_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(87);
	set_val(SprintBtn,15);
    wait(30);
    wait(370);
}

combo CANCEL_Four_Touch {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    LA(0,0);
    wait(400);
    set_val(XB1_LT,100);
    set_val(XB1_LB,100);
    set_val(XB1_RB,100);
    set_val(XB1_RT,100);
    wait(70);      
}



combo FEINT{
RA_L_R();
wait(40);
RA_UP();
wait(40);
}

combo DoubleTapGroundPass_cmb {
    
    set_val(PassBtn,  0);
    wait(30);
    set_val(PassBtn,100);
    wait(60);    
}

combo FAKE_SPIN {
LA(0,0);
RA_DOWN();
wait(w_rstick);
LA(0,0);
RA_ZERO();
set_val(PaceCtrol,100);
LA(0,0);
if( right_on = FALSE )right_on=!right_on;
RA_OPP();
wait(w_rstick);
LA(0,0);
RA_ZERO();
set_val(PaceCtrol,100);
set_val(FinesseShot,100);
wait(w_rstick);
set_val(PaceCtrol,100);
set_val(SprintBtn,100);
LA(100,0);
wait(60);
set_val(PaceCtrol,100);
set_val(SprintBtn,100);
LA(0,100);
wait(60);
set_val(PaceCtrol,100);
set_val(SprintBtn,100);
LA(-100,0);
wait(60);
set_val(PaceCtrol,100);
set_val(SprintBtn,100);
LA(100,0);
wait(300);
}






//=================================
int trough_pass_timer; 
int Trough_Pass_MIN = 50;
int Trough_Pass_MAX = 300;
int TroughP_difference;
int DoubleTapTroughPass = TRUE;
int trough_start;

combo Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	wait(TroughP_difference);
	if(DoubleTapTroughPass){
		set_val(ThroughBall,100);
	}
	wait(60);
}





combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(40);
	set_val(ThroughBall,100);
	wait(60);
}






int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo OPEN_UP_cmb {      
    set_val(XB1_LS,100); 
    wait(800);           
}                       
////////////////////////////////////////////////////////////////
// VARIABLES  for FREE KICK MOD 
//-------------------------------------------- 
define TOP_SPIN       = 1;                     
define SIDE_SPIN      = 2;                     
define KNUCKLEBALL_FK = 3;                     
define spin_time      = 80;                    
//-------------------------------------------- 
                                  
int side_dir   = 100;                          
int FK_mode    = TOP_SPIN;

int FK_Shot_Power;                          
int FK_LX;
int FK_LY;

function f_FREE_KICK (){
vm_tctrl(0);
	//===============================
    // SIDE SPIN
    //===============================
    if (get_val(XB1_LT) ){
        if (event_press(XB1_RIGHT)){
            FK_LX=76 ; FK_LY=-40 ;
            FK_mode = SIDE_SPIN;                          
            side_dir =  100;   
            FK_Shot_Power = 650;
            combo_run(FreeKick_Menu);
        } 
        if (event_press(XB1_LEFT)){
            FK_LX=-76 ; FK_LY=-40 ;
            FK_mode = SIDE_SPIN;                          
            side_dir =  -100;
            FK_Shot_Power = 650;
            combo_run(FreeKick_Menu);                        
        } 
    }
    //===============================
    // TOP SPIN
    //===============================
    if (get_val(XB1_RT) ){
        if (event_press(XB1_RIGHT)){
            FK_LX= 28 ; FK_LY= -88 ;
            FK_mode = TOP_SPIN ;                         
            side_dir =  -100; 
            FK_Shot_Power = 650;
            combo_run(FreeKick_Menu);                        
        } 
        if (event_press(XB1_LEFT)){
            FK_LX=-28 ; FK_LY=-88;
            FK_mode = TOP_SPIN ;                         
            side_dir =  -100;  
            FK_Shot_Power = 650;        
            combo_run(FreeKick_Menu);                        
        } 
    }
    //===============================
    // KNUCKLEBALL
    //===============================
    if (get_val(XB1_RB) ){
        //--- RIGHT
        if (event_press(XB1_RIGHT)){
            FK_LX=63 ; FK_LY=-35;
            FK_mode = KNUCKLEBALL_FK;                     
            FK_Shot_Power = 650;               
            combo_run(FreeKick_Menu); 
        } 
        //--- LEFT
        if (event_press(XB1_LEFT)){
            FK_LX=-63 ; FK_LY=-35;
            FK_mode = KNUCKLEBALL_FK;                     
            FK_Shot_Power = 650;               
            combo_run(FreeKick_Menu); 
        } 
    }
    set_val(XB1_UP,   0);                       
    set_val(XB1_DOWN, 0);                       
    set_val(XB1_LEFT, 0);                      
    set_val(XB1_RIGHT,0);                           
    set_val(XB1_RT,0);
    set_val(XB1_LT,0);
    set_val(XB1_RB,0);
}


combo FreeKick_Menu {  
    LA(FK_LX,FK_LY);
    wait(600);
    LA(FK_LX,FK_LY);
    set_val(XB1_RY,100);
    //set_val(ShotBtn,100);
    wait(FK_Shot_Power);
    LA(FK_LX,FK_LY);
    wait(330);                                           
    /////////////////////////////////////////////////   
    //  FREE KICK MODE                                  
    if(FK_mode == TOP_SPIN )  combo_run(TOP_SPIN_FK);    
    if(FK_mode == SIDE_SPIN ) combo_run(SIDE_SPIN_FK);    
    if(FK_mode == KNUCKLEBALL_FK ) combo_run(KNUCKLEBALL);
}                                                       
//--------------------------------------------------- 
combo TOP_SPIN_FK  {                                  
    RA_ZERO();                                  
    wait(spin_time);                                           
    RA(0,100);  // DOWN                                
    wait(spin_time);                                           
    RA_ZERO();                                
    wait(spin_time);                                           
    RA(0,-100); // UP                                
    wait(spin_time); 
    wait(2000);
    //--------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                                                     
//--------------------------------------------------- 
combo SIDE_SPIN_FK  {                                 
    RA(0,100);   // DOWN                                
    wait(spin_time);                                           
    RA(side_dir,0);// LEFT or RIGHT           
    wait(spin_time);                                           
    RA(0,-100); // UP                                  
    wait(spin_time);
    RA(0,100); // Down                                  
    wait(spin_time);
    wait(2000);
    //--------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                                                     
//--------------------------------------------------- 
combo KNUCKLEBALL {                                   
    RA(0,100); // DOWN                                 
    wait(spin_time);                                           
    RA_ZERO();                                 
    wait(spin_time);                                           
    RA(0,-100);    // UP                              
    wait(spin_time);                                           
    RA_ZERO();                                
    wait(spin_time);                                           
    RA(0,100);// DOWN                        
    wait(spin_time); 
    wait(2000);
    //-----------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                         
//--- Defense V 4.3
// Credits to Dark.Angle 
//=======================================
int Teammate_Contain_on = FALSE;
int defence_on = TRUE;
define Sprint_Pass        = 0;
define Sprint_ProtectBall = 1;
int want_to_use = Sprint_ProtectBall;
int Second_Button ;
int Pre_jock_mod;
int Opp_Goal_dir;
function f_defence (){

if(abs(get_val(XB1_RX) >12 ) || abs(get_val(XB1_RY) >12)){ // Fix RS Switching , sometimes high - vm causing RS switching to wider distance than usual
vm_tctrl(0);
}

	if(get_val(SprintBtn) && !combo_running(Pre_JoCKEY) && !combo_running(JoCKEY)){ // provides higher sprinting speed .
		sensitivity(XB1_LX, NOT_USE, 110);
        sensitivity(XB1_LY, NOT_USE, 110);
    }
    // User in the start of the match will need to define opponent goal direction
    // in the start of 2nd half should do the same .
    if (get_val(XB1_LT) ){
        if (get_val(XB1_RIGHT)){
            Opp_Goal_dir =  100 ;
        }
        if (get_val(XB1_LEFT)){
            Opp_Goal_dir =  -100 ;
        }
        set_val(XB1_LEFT,0);
        set_val(XB1_RIGHT,0);  
    }
    
    if(want_to_use == Sprint_ProtectBall)Second_Button = PaceCtrol; 
    else  Second_Button = PassBtn;                
    
    if( get_val(SprintBtn) && get_val(Second_Button)){
    vm_tctrl(-6);
    set_val(SprintBtn,90);
            if((get_val(ShotBtn) || get_val(ShotBtn)) && (combo_running(Sprint_Spam2) || combo_running(Sprint_Spam1) ) ){
            combo_stop(Sprint_Spam1);
            combo_stop(Sprint_Spam2);
            combo_run(Tackle);
            }
            
        if(!Pre_jock_mod && ( abs(get_val(XB1_LX))>20  || abs(get_val(XB1_LY))>20  )) { // first 500 ms pre Jockey , no sprint only jockey +  90 sens = (Player Solid Shape) .
            if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
            directional_jockey();
            combo_run(Pre_JoCKEY);        
            combo_run(Sprint_Spam1);
        }
    
         if(Pre_jock_mod && ( abs(get_val(XB1_LX))>14  || abs(get_val(XB1_LY))>14  )){ // after 500 ms  Jockey + Sprint in loop .
            if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
            combo_run(Sprint_Spam2);
            combo_run(JoCKEY);
        }
            
        if( ( abs(get_val(XB1_LX))<12  && abs(get_val(XB1_LY))<12  )){ // when NO LS directions AI Switching and pressing AUTO.
            if(Second_Button == PassBtn) set_val(PassBtn,0);
            if(Second_Button == PaceCtrol) set_val(PaceCtrol,0);
            set_val(FinesseShot,0)
            combo_run(Auto_Switch);            
        }else{
            combo_stop(Auto_Switch);
        }
     }
    
    if( ( event_release(SprintBtn) || event_release(PaceCtrol) || event_release(PassBtn) ) || get_val(ShotBtn) || get_val(CrossBtn) ){
        Pre_jock_mod = FALSE;combo_stop(JoCKEY); vm_tctrl(0); combo_stop(Auto_Switch); combo_stop(Pre_JoCKEY);combo_stop(Sprint_Spam1);combo_stop(Sprint_Spam2)
    }
  
 
}


function directional_jockey() { 
  // Moving to UP
    if((get_val(XB1_LX) > -30 && get_val(XB1_LX) <30) && get_val(XB1_LY) < -35 ) // UP
    {  
        LA (0, -96);
    }
    // Moving to DOwn     
    if((get_val(XB1_LX) > -30 && get_val(XB1_LX) <30) && get_val(XB1_LY) > 35 ) // Down
    {  
        LA (0, 96);
    }
    // Moving to Right     
    if((get_val(XB1_LY) > -30 && get_val(XB1_LY) <30) && get_val(XB1_LX) > 35 ) // Right
    { 
        LA (96, 0);
    }
    // Moving to LEFT     
    if((get_val(XB1_LY) > -30 && get_val(XB1_LY) <30) && get_val(XB1_LX) < -35 ) // Left
    {  
        LA (-96, 0);
    }
    // Moving to UP-Right
    if(get_val(XB1_LX) > 30  && get_val(XB1_LY) < -50 ) // UP-Right
    {  
        LA (96, -96)
    }
    // Moving to Down-Right     
    if(get_val(XB1_LX) > 30 && get_val(XB1_LY) > 50 ) // Down-Right
    {   
        LA (96, 96)
    }
    // Moving to UP-Left    
    if(get_val(XB1_LX) < -30 && get_val(XB1_LY) < -50)  // UP-Left
    {   
        LA (-96, -96)
    }
    // Moving to Down-Left     
    if(get_val(XB1_LX) < -30 && get_val(XB1_LY) > 50) // Down-Left
    {   
        LA (-96, 96);
    }    
}

combo Auto_Switch {
    set_val(FinesseShot,100);
    wait(600);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(FinesseShot,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,0);
    set_val(FinesseShot,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(FinesseShot,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(FinesseShot,0);
    combo_stop(JoCKEY);
    wait(10);
}



combo Sprint_Spam1 {
    set_val(SprintBtn,100);
    wait(80);
    set_val(SprintBtn,0);
    wait(120);   
}

combo Sprint_Spam2 {
    set_val(SprintBtn,100);
    wait(80);
    set_val(SprintBtn,0);
    wait(220);   
}

combo Tackle {
    set_val(SprintBtn,100);
    set_val(PaceCtrol,100);
    wait(60);  
}

combo Pre_JoCKEY {
    directional_jockey()
    set_val(PaceCtrol,100);
    set_val(XB1_LS,100);
    wait(300); 
    Pre_jock_mod = TRUE
}

combo JoCKEY {
  set_val(XB1_LS,100);
  set_val(PaceCtrol,100);
  wait(1280);
}


	
	function AUTO_EXIT_SK() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(XB1_LX) > 5 && get_val(XB1_LY) < -5 ) // TOP Right Corner
	{   right_on = FALSE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(XB1_LX) > 5 && get_val(XB1_LY) > 5) // Bottom Right Corner
	{   right_on = TRUE
	
		 
	}
	
	
	if(get_val(XB1_LX) < -5 && get_val(XB1_LY) < -5) // TOP Left Corner
	{   right_on = TRUE
		 

	}
	
	if(get_val(XB1_LX) < -5 && get_val(XB1_LY) > 5) // Bottom Left Corner
	{   right_on = FALSE

		  
		 
	}
	}

combo TURN_RBND_SPIN { 
    LA(LX,LY);
    RA_UP (); 
    // up   
    wait(w_rstick); 
    LA(LX,LY);
    RA_ZERO (); 
    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    LA_L_R();
    set_val(XB1_LT,100);
    set_val(XB1_RT,100);
    wait(200);
    LA_L_R();
    wait(300);
}  




combo L1_Spam_Sprint_boost {
sensitivity(XB1_LX, NOT_USE, 110);
sensitivity(XB1_LY, NOT_USE, 110);
set_val(FinesseShot,0);
set_val(PlayerRun,100):
wait(300);
sensitivity(XB1_LX, NOT_USE, 110);
sensitivity(XB1_LY, NOT_USE, 110);
set_val(FinesseShot,0);
set_val(PlayerRun,0):
wait(120);
}

function Directional_Dribbling() { 
    // Moving to UP
    if((get_val(XB1_LX) > -15 && get_val(XB1_LX) <15) && get_val(XB1_LY) < -50 ) // UP
    {   combo_run(L1_Spam_Sprint_boost);
        LA (0, -100);
    }
    // Moving to DOwn     
    if((get_val(XB1_LX) > -15 && get_val(XB1_LX) <15) && get_val(XB1_LY) > 50 ) // Down
    {   combo_run(L1_Spam_Sprint_boost);
        LA (0, 100);
    }
    // Moving to Right     
    if((get_val(XB1_LY) > -15 && get_val(XB1_LY) <15) && get_val(XB1_LX) > 50 ) // Right
    {   combo_run(L1_Spam_Sprint_boost);
        LA (100, 0);
    }
    // Moving to LEFT     
    if((get_val(XB1_LY) > -15 && get_val(XB1_LY) <15) && get_val(XB1_LX) < -50 ) // Left
    {   combo_run(L1_Spam_Sprint_boost);
        LA (-100, 0);
    }
    // Moving to UP-Right
    if(get_val(XB1_LX) > 85  && get_val(XB1_LY) < -85 ) // UP-Right
    {   combo_run(L1_Spam_Sprint_boost);
        LA (100, -100)
    }
    // Moving to Down-Right     
    if(get_val(XB1_LX) > 85 && get_val(XB1_LY) > 85 ) // Down-Right
    {   combo_run(L1_Spam_Sprint_boost);
        LA (100, 100)
    }
    // Moving to UP-Left    
    if(get_val(XB1_LX) < -85 && get_val(XB1_LY) < -85)  // UP-Left
    {   combo_run(L1_Spam_Sprint_boost);
        LA (-100, -100)
    }
    // Moving to Down-Left     
    if(get_val(XB1_LX) < -85 && get_val(XB1_LY) > 85) // Down-Left
    {   combo_run(L1_Spam_Sprint_boost);
        LA (-100, 100);
    }    
}

combo SOMBRERO_FLICK {
    RA_ZERO()
	wait(20);
	set_val(XB1_RX,RS_X )
	set_val(XB1_RY,RS_Y  )
	wait(50);
	RA_ZERO ()
	wait(50);
	set_val(XB1_RX,RS_X )
	set_val(XB1_RY,RS_Y  )
	wait(50);
	RA_ZERO ()
	wait(50);
	set_val(XB1_RX,inv(RS_X))
	set_val(XB1_RY,inv(RS_Y))   
	wait(50)
	RA_ZERO ()
	wait(700);
  
}





int long_finesse;
function CORNER() { 

     // Moving to UP
    if(zone_p == 0  ) // UP
    {  
         
        LA (0, -100);
    }
          
    // Moving to DOwn     
    if(zone_p == 4 ) // Down
    {  
         
        LA (0, 100);
    }
    
    
    // Moving to Right     
    if(zone_p == 2 ) // Right
    
    { 
     
        LA (100, 0);
    }
    
    // Moving to LEFT     
    if(zone_p == 6 ) // Left
    {  
     
        LA (-100, 0);
    }
    
    
            // Moving to UP-Right
    if(zone_p == 1 ) // UP-Right
    
    {  
     
        LA (100, -100)
    }
          
    // Moving to Down-Right     
    if(zone_p == 3 ) // Down-Right
    {   
        LA (100, 100)
    }
    
    
    // Moving to UP-Left    
    if(zone_p == 7)  // UP-Left
    {   
        LA (-100, -100)
    }
    
    // Moving to Down-Left     
    if(zone_p == 5) // Down-Left
    {   
        LA (-100, 100);
    }

          
}

combo CHIP_SHOT {
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    set_val(XB1_LS,100);
    wait(90);
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    wait(96);
}

function CORNER_FIX_MOVE() {
        
      // Moving to UP
    if(zone_p == 0  ) // UP
    {  
         
        LA (0, -100);
    }
          
    // Moving to DOwn     
    if(zone_p == 4 ) // Down
    {  
         
        LA (0, 100);
    }
    
    
    // Moving to Right     
    if(zone_p == 2 ) // Right
    
    { 
     
        LA (100, 0);
    }
    
    // Moving to LEFT     
    if(zone_p == 6 ) // Left
    {  
     
        LA (-100, 0);
    }
    
    
            // Moving to UP-Right
    if(zone_p == 1 ) // UP-Right
    
    {  
     
        LA (100, -100)
    }
          
    // Moving to Down-Right     
    if(zone_p == 3 ) // Down-Right
    {   
        LA (100, 100)
    }
    
    
    // Moving to UP-Left    
    if(zone_p == 7)  // UP-Left
    {   
        LA (-100, -100)
    }
    
    // Moving to Down-Left     
    if(zone_p == 5) // Down-Left
    {   
        LA (-100, 100);
    }

}



int ping = 30;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 240;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
    //CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
    CORNER ();
    set_val(ShotBtn, 0);
    wait(10):               
    CORNER ();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause + ping); // Ping is variable set to 0 for squad battle , still need more testing in online to entre a suitable value to it .
    
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn)            
           
} 

combo FAKE_SHOT2 {        
	set_val(CrossBtn,100);  
	wait(40);              
	set_val(CrossBtn,100);  
	set_val(PassBtn,100); 
	wait(60);             
	set_val(CrossBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
} 

combo TURN_AND_SPIN {  
  if(ACTIVE == BALL_ROLL_FAKE_TURN ) hold_btn = 200;//  Ball Roll Fake Turn L2 
	else hold_btn = 1;      
 wait(hold_btn);
	RA_UP ();      // up   
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);          
	RA_L_R () ;    // Left or Right 
	wait(w_rstick);    
}   

combo TREE_TOUCH { 
	RA_DOWN (); 
	set_val(PaceCtrol,100);// down  
	wait(w_rstick);         
	RA_ZERO (); 
	set_val(PaceCtrol,100);// ZERO  
	wait(w_rstick);         
	RA_L_R ();  
	set_val(PaceCtrol,100);//  <-/-> 
	wait(w_rstick);       
}     

combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   
combo LA_CROQUETA {        
  set_val(PlayerRun,100);
  RA_L_R ();      // <-/-> 
  wait(280);//            
}                         
combo ROULETTE { 
    //set_val(PlayerRun,100);
    LA(LX,LY);
	RA_DOWN ();     // down 
	wait(w_rstick);
	LA(LX,LY);
	//set_val(PlayerRun,100);
	RA_L_R ();      // <-/->
	wait(w_rstick);
	//set_val(PlayerRun,100);
	RA_UP ();       // up   
	wait(w_rstick);  
	wait(400);
	set_val(PaceCtrol,100);
	wait(1000);
}                        
combo CANCELED_Four_Touch {  
    RA_UP ();      // up   
    wait(w_rstick);         
    RA_ZERO ();    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    wait(250);
    //if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    set_val(XB1_LB,100);
    set_val(XB1_RT,100);
    set_val(XB1_LT,100);
    set_val(XB1_RB,100);
    wait(200);
   // if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    wait(300);
}    


combo FAKE_SHOT_STOP {
    LA(0,0)
    set_val(ShotBtn,100);  
    wait(60);  
    LA(0,0)
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60); 
    LA(0,0)
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    LA(0,0)
}                  

combo Block_change {
deadzone(PS4_RX, PS4_RY, 100, 100);
wait(1000);
}
///////////////////////////////////////////////////
// ZONE FUNCTION
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}


const int ZONE_P_RS [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 }, //7 Left Up
{ 0,0} // 8 centre
}

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 30) move_lx = 100;
    else if(get_val(XB1_LX) <= -30) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 30) move_ly = 100;
    else if(get_val( XB1_LY) <= -30) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(ZONE_P[zone_p][0] == move_lx && ZONE_P[zone_p][1] == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
}

int RS_x, RS_y ;
int zone_RS;
function calc_RS(){
 if(get_val(XB1_RX) >= 25) RS_x = 100;
    else if(get_val(XB1_RX) <= -25) RS_x = -100;
    else RS_x = 0;
    if(get_val(XB1_RY) >= 25) RS_y = 100;
    else if(get_val( XB1_RY) <= -25) RS_y = -100;
    else RS_y = 0;
    
    if(RS_x != 0 || RS_y != 0) {
        zone_RS = 0; while(zone_RS < 8) {
            if(ZONE_P_RS[zone_RS][0] == RS_x && ZONE_P_RS[zone_RS][1] == RS_y) {
                break;
            } zone_RS += 1;
        }
    }   
    }



function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - d;
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}
int onoff_penalty;  
int onoff_FK;  
//=========================================================== 
// --- PENALTIES 
//*************************************************                                    
int dir;                                      
int LeftRight,UpDown; 
int Pen_X,Pen_Y;
int correct_X,correct_Y;
const int PenaltiX_Y [] []= {
{  87,   0},//0. 1 Right
{ -86,   0},//1. 2 Left
{   0, 100},//2. 3 Down
{   0, -70},//3. 4 Up
{  56,  90},//4. 8 Right Down
{  78, -38},//5. 9 Right Up
{ -56,  90},//6.11 Left Down  
{ -78, -38} //7.13 Left Up 
};
//---------------------------   
function fPenalties () {                              
    
    if(!get_val(XB1_RT)){
		if(event_press(XB1_RIGHT) )LeftRight = 1;// Right
		                                              
		if(event_press(XB1_LEFT) ) LeftRight = 2;// Left
		                                              
		if(event_press(XB1_DOWN))  UpDown    = 3;// Down
		                                              
		if(event_press(XB1_UP))    UpDown    = 4;// Up  
		                                              
		if(LeftRight && !UpDown){                       
		if(LeftRight == 1) dir = 1; // Right          
		else   dir = 2;             // Left           
		                                              
		if(dir == 1 ){                                
		   Pen_X = PenaltiX_Y [0][0] ;  //0.          
		   Pen_Y = PenaltiX_Y [0][1] ;                        
		}                                             
		                                              
		if(dir == 2 ){                                
		   Pen_X = PenaltiX_Y [1][0] ;  //1.           
		   Pen_Y = PenaltiX_Y [1][1] ;                     
		}                                             
		}                                               
		else if(!LeftRight && UpDown){                  
		if(UpDown == 3) dir = 3; // Down              
		else   dir = 4;          // Up                
		if(dir == 3 ){                                
		   Pen_X = PenaltiX_Y [2][0] ;  //2.           
		   Pen_Y = PenaltiX_Y [2][1] ;                   
		}                                             
		                                              
		if(dir == 4 ){                                
		   Pen_X = PenaltiX_Y [3][0] ;  //3.           
		   Pen_Y = PenaltiX_Y [3][1] ;                    
		}                                             
		                                              
		}                                               
		else if(LeftRight && UpDown){                   
		//---------------------------------------       
		  dir = (LeftRight * UpDown) + 5 ;            
		  // Right Down                               
		  if(dir == 8 ){                              
		      Pen_X = PenaltiX_Y [4][0] ;  //4.           
		      Pen_Y = PenaltiX_Y [4][1] ;                      
		  }                                           
		  //Right Up                                  
		  if(dir == 9 ){                              
		      Pen_X = PenaltiX_Y [5][0] ;  //5.           
		      Pen_Y = PenaltiX_Y [5][1] ;                     
		  }
		  // Left Down                                
		  if(dir == 11 ){                             
		      Pen_X = PenaltiX_Y [6][0] ;  //6.           
		      Pen_Y = PenaltiX_Y [6][1] ;                      
		  }           
		  // Left Up                                  
		  if(dir == 13 ){                             
		      Pen_X = PenaltiX_Y [7][0] ;  //7.           
		      Pen_Y = PenaltiX_Y [7][1] ;                        
		  }                                           
		                                  
		}                                               
     }else if(get_val(XB1_RT)){
		if(event_press(XB1_RIGHT) )correct_X += 1;// Right
		                                          
		if(event_press(XB1_LEFT) ) correct_X -= 1;// Left
		                                          
		if(event_press(XB1_DOWN))  correct_Y += 1;// Down
		                                          
		if(event_press(XB1_UP))    correct_Y -= 1;// Up  
     }
     
     	set_val(XB1_LX, Pen_X + correct_X);
     	set_val(XB1_LY, Pen_Y + correct_Y);
     	
      set_val(XB1_UP,   0);                       
      set_val(XB1_DOWN, 0);                       
      set_val(XB1_LEFT, 0);                      
      set_val(XB1_RIGHT,0);                      
      //----  reset the aiming direction  
      if(event_press(XB1_RS)){           
      	LeftRight = 0;                   
      	UpDown    = 0;                  
      	dir       = 0;
      	Pen_X     = 0;
      	Pen_Y     = 0;
      	correct_X = 0;
      	correct_Y = 0;
      }                              
      set_val(XB1_RS,0); 
      
      
      if (event_release(ShotBtn)){
      combo_run(SwitchBack);
      }
}     

combo SwitchBack {
	wait(3000);
	onoff_penalty = !onoff_penalty
	}                        
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
int LS_Sens        = 96;
int LS_Sprint_Sens = 110;
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             