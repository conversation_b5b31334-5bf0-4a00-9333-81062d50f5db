/*
    Button Sequence Script for Cronus ZEN
    Created: 2024-12-19
    
    Description:
    When XB1_A is pressed and released:
    - Presses XB1_LB for 100ms after 200ms
    - Presses XB1_LT for 100ms after 210ms more
    - Presses XB1_LB for 150ms after 300ms more
*/



main {
    if(event_press(XB1_A)) {
        wait(release(XB1_A));
        combo_run(ButtonSequence);
    }
}


combo ButtonSequence {
    wait(200);
    combo_run(XB1_LB);
    wait(100);
    combo_stop(XB1_LB);
    
    wait(210);
    combo_run(XB1_LT);
    wait(100);
    combo_stop(XB1_LT);
    
    wait(300);
    combo_run(XB1_LB);
    wait(150);
    combo_stop(XB1_LB);
}