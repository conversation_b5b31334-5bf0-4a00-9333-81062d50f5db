define AA_Size = 100;
define AA_Interval = 70;

main {

  if(get_ival(PS5_L1))
    combo_run(AIM_ASSIST_ABUSE);
  else combo_stop(AIM_ASSIST_ABUSE);
  
  /*
  if(get_ival(PS5_R1)) 
    combo_run(NERVOUS);
  else combo_stop(NERVOUS);
*/
}

combo AIM_ASSIST_ABUSE {
   set_val(POLAR_LX, min(get_val(POLAR_LX) + (AA_Size * 32767 / 100), 32767));
   wait(AA_Interval);
   set_val(POLAR_LY, min(get_val(POLAR_LY) + (AA_Size * 32767 / 100), 32767));
   wait(AA_Interval);
  // set_val(POLAR_LX, max(-32768, get_val(POLAR_LX) - (AA_Size * 32768 / 100)));
   //wait(AA_Interval);
  // set_val(POLAR_LY, max(-32768, get_val(POLAR_LY) - (AA_Size * 32768 / 100)));
  // wait(AA_Interval - get_rtime());
  // set_val(POLAR_LY, max(-32768, get_val(POLAR_LY) - (AA_Size * 32768 / 100)));
}


combo NERVOUS {
   set_polar(POLAR_LS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
   wait(AA_Interval);
   set_polar(POLAR_LS, -180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
   wait(AA_Interval);
   set_polar(POLAR_LS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
   wait(AA_Interval);
   set_polar(POLAR_LS, -180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
   wait(AA_Interval - get_rtime());
   //set_polar(POLAR_LS, 10 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
}