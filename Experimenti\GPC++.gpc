
const string string1 = "Object 1:";
const string string2 = "Object 2:";

int current;

int #objects[3];




init { 
	INIT();
}

function INIT() {
	// Clear Screen
	cls_oled(0);

	// Instantiate objects
	objects[0] = Class(
				 	Var(-10000, sizeof(int) , SIGNED),
				 	Var(70    , sizeof(int8), UNSIGNED),
				 	String(addr(string1))
		         );
				 
	objects[1] = Class(
			    	Var(-5000, sizeof(int) , SIGNED),
					Var(25   , sizeof(int8), UNSIGNED),
					String(addr(string2))
		   		 );
	
	// Add the objects to eachother
	objects[2] = Class_add(objects[0], objects[1]);
	
	// Print the first object upon startup
	Class_print(0, 0, objects[current]);
	
}

main {
	
	// Press UP/DOWN to look at different objects
	if(event_press(PS4_UP  )) {
		cls_oled(0);
		current -= (current > 0);
		Class_print(0, 0, objects[current]);
	}
	if(event_press(PS4_DOWN)) {
		cls_oled(0);
		current += (current < 2);
		Class_print(0, 0, objects[current]);
	}
	
	// Press CROSS to reconstruct objects
	if(event_press(PS4_CROSS)) {
		_Class(objects[0]);
		_Class(objects[1]);
		_Class(objects[2]);
		INIT();
	}
	
	// Press CIRCLE to deconstruct objects
	if(event_press(PS4_CIRCLE)) {
		_Class(objects[0]);
		_Class(objects[1]);
		_Class(objects[2]);
		objects[0] = 0;
		objects[1] = 0;
		objects[2] = 0;
		cls_oled(0);
		current = 0;
		Class_print(0, 0, objects[current]);
	}
	
	
	// Inspect memory
	set_val(TRACE_1, HEAP[0]);
	set_val(TRACE_2, HEAP[1]);
	set_val(TRACE_3, HEAP[2]);
	set_val(TRACE_4, HEAP[3]);
	set_val(TRACE_5, HEAP[4]);
	set_val(TRACE_6, HEAP[5]);
	
}




//===========//
// GPC Class //
//===========//

// Attribute id's
enum {
	_a,
	_b,
	_c
}

// Constructor
int #Class_pt;
function Class(int #a, int #b, int #c) {
	// Inheritance of root object
	Class_pt = Object();
	
	// Create attributes
	Class_pt = Attribute(a, Class_pt);
	Class_pt = Attribute(b, Class_pt);
	Class_pt = Attribute(c, Class_pt);
	
	// return the object pointer
	return Class_pt;
}

// Destructor
function _Class(int #address) {
	Object_free(address);
}

// Setters and Getters for a Var
function Class_setA(int value, int #address) {
	Var_set(value, Attribute_get(_a, address));
}
function Class_getA(int #address) {
	return Var_get(Attribute_get(_a, address));
}

function Class_setB(int value, int #address) {
	Var_set(value, Attribute_get(_b, address));
}
function Class_getB(int #address) {
	return Var_get(Attribute_get(_b, address));
}

// Setters and Getters for a String
function Class_setC(int constAddress, int #address) {
	Attribute_set(String(constAddress), _c, address);
}
function Class_getC(int #address) {
	return Attribute_get(_c, address);
}

// Other methods
function Class_add(a, b) {
	if(a && b) {
		return Class(
				    Var(Class_getA(a) + Class_getA(b), sizeof(int), SIGNED),
					Var(Class_getB(a) + Class_getB(b), sizeof(int), SIGNED),
					String_cat(Class_getC(a), Class_getC(b))
			   );
	}
	return 0;
}

function Class_print(x, y, #obj) {
	if(obj > 0) {
		String_print(x,      y, 0, 1, Class_getC(obj));
		String_print(x, y + 10, 0, 1, String_fromInt(Class_getA(obj)));
		String_print(x, y + 20, 0, 1, String_fromInt(Class_getB(obj)));
	}
}












////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


// #include <object.gph>

function Object() {
	return List(0, sizeof(#int16), UNSIGNED);
}

function Object_free(int #address) {
	Memory_free2D(address);
}

function Attribute(int attribute, int #address) {
	return List_append(attribute, address);
}

function Attribute_set(int value, int attribute, int #address) {
	List_setIndex(value, attribute, address);
}

function Attribute_get(int attribute, int #address) {
	return List_getIndex(attribute, address);
}




// #include <var.gph>

int #Var_pt;
function Var(int value, int size, int sign) {
	Var_pt = List(1, size, sign);
	List_setIndex(value, 0, Var_pt);
	return Var_pt;
}


function Var_set(int value, int #address) {
	List_setIndex(value, 0, address);
}


function Var_get(int #address) {
	return List_getIndex(0, address);
}




// #include <string.gph>

int #String_pt;
int String_i,
	String_len;
function String(int address) {
	String_len = String_lenFromAddress(address);
	String_pt = List(String_len, sizeof(char), UNSIGNED);
	if(String_pt >= 0) {
		String_i = 0;
		while(duint8(address + String_i)) {
			List_setIndex(duint8(address + String_i), String_i, String_pt);
			String_i++;
		}	
		return String_pt;	
	}
	return -1;
}


int print_i,
	print_len;
function String_print(int x, int y, int font, int color, int #address) {
	print_len = List_len(address);
	for(print_i=0; print_i<print_len; print_i++) {
		putc_oled(print_i + 1, List_getIndex(print_i, address));
	}
	puts_oled(x, y, font, print_len, color);
}


int printN_i,
	printN_len;
function String_printN(int start, int end, int x, int y, int font, int color, int #address) {
	printN_len = List_len(address);
	if(start >= printN_len) return;
	for(printN_i=start; (printN_i<end) && (printN_i<printN_len); printN_i++) {
		putc_oled(printN_i - start + 1, List_getIndex(printN_i, address));
	}
	puts_oled(x, y, font, min(printN_len, end) - start, color);
}


// Comment this out when not in use
int lenFromAddress_i;
function String_lenFromAddress(int #address) {
	lenFromAddress_i = 0;
	while(duint8(address + lenFromAddress_i)) {
		lenFromAddress_i++;
	}
	return lenFromAddress_i;
}


function String_len(int #address) {
	return List_len(address);
}


function String_setIndex(int value, int index, int #address) {
	List_setIndex(value, index, address);
}


function String_getIndex(int index, int #address) {
	return List_getIndex(index, address);
}


function String_append(int value, int #address) {
	return List_append(value, address);
}


function String_insert(int value, int index, int #address) {
	return List_insert(value, index, address);
}


function String_remove(int index, int #address) {
	return List_remove(index, address)
}

int #cat_pt;
int cat_size1,
	cat_size2,
	cat_size,
	cat_i;
function String_cat(int #address1, int #address2) {
	cat_size1 = String_len(address1);
	cat_size2 = String_len(address2);
	cat_size = cat_size1 + cat_size2;
	cat_pt = List(cat_size, sizeof(char), UNSIGNED);
	if(cat_pt >= 0) {
		cat_i = 0;
		while(cat_i < cat_size) {
			if(cat_i < cat_size1) {
				List_setIndex(List_getIndex(cat_i, address1), cat_i, cat_pt);
			}
			else {
				List_setIndex(List_getIndex(cat_i - cat_size1, address2), cat_i, cat_pt);
			}
			cat_i++;
		}
		return cat_pt;
	}
	return -1;
}

int #rev_pt;
int rev_i,
	rev_len;
function String_rev(int #address) {
	rev_len = String_len(address);
	rev_pt = List(rev_len, sizeof(char), UNSIGNED);
	if(rev_pt >= 0) {
		for(rev_i=0; rev_i < rev_len; rev_i++) {
			List_setIndex(List_getIndex(rev_i, address), rev_len - rev_i - 1, rev_pt);
		}
		return rev_pt;
	}
	return -1;
}

int #cpy_pt;
int cpy_i,
	cpy_len;
function String_cpy(int #address) {
	cpy_len = String_len(address);
	cpy_pt = List(cpy_len, sizeof(char), UNSIGNED);
	if(cpy_pt >= 0) {
		for(cpy_i=0; cpy_i < cpy_len; cpy_i++) {
			List_setIndex(List_getIndex(cpy_i, address), cpy_i, cpy_pt);
		}
		return cpy_pt;
	}
	return -1;
}


int cmp_i,
	cmp_len1,
	cmp_len2;
function String_cmp(int #address1, int #address2) {
	cmp_len1 = String_len(address1);
	cmp_len2 = String_len(address2);
	if(cmp_len1 != cmp_len2) return 1;
	for(cmp_i=0; cmp_i<cmp_len1; cmp_i++) {
		if(List_getIndex(cmp_i, address1) != List_getIndex(cmp_i, address2)) return 1;
	}
	return 0;
}


int #fromInt_pt;
int fromInt_int1,
	fromInt_i,
	fromInt_digit,
	fromInt_digits,
	fromInt_len;
function String_fromInt(int integer) {
	fromInt_int1 = integer;
	fromInt_digits = 0;
	do {
    	fromInt_digits ++;
    	fromInt_int1 /= 10;
	}
	while(fromInt_int1);
	fromInt_len = fromInt_digits;
	if(integer < 0) {
		fromInt_len++;
	}
	fromInt_pt = List(fromInt_len, sizeof(char), UNSIGNED);
	if(fromInt_pt >= 0) {
		if(integer < 0) {
			integer *= -1;
			List_setIndex(ASCII_MINUS, 0, fromInt_pt);
		}
		for(fromInt_i=0; fromInt_i<fromInt_digits; fromInt_i++) {
			fromInt_digit = integer / pow(10,fromInt_i) % 10;
			List_setIndex(ASCII_DIGIT0 + fromInt_digit, fromInt_len - fromInt_i - 1, fromInt_pt);
		}
		return fromInt_pt;
	}
	return -1;
}


int toInt_len,
	toInt_i,
	toInt_digit,
	toInt_val,
	toInt_sign,
	toInt_start;
function String_toInt(int #address) {
	toInt_start = 0;
	toInt_sign = 1;
	if(List_getIndex(0, address) == ASCII_MINUS) {
		toInt_sign = -1;
		toInt_start = 1;
	}
	toInt_len = List_len(address);
	toInt_val = 0;
	for(toInt_i=toInt_start; toInt_i<toInt_len; toInt_i++) {
		toInt_digit = List_getIndex(toInt_i, address) - ASCII_DIGIT0;
		if(toInt_digit >= 0 && toInt_digit <= 9) {
			toInt_val += toInt_digit * pow(10, toInt_len - toInt_i - 1);
		}
		else return -1
	}
	return toInt_val * toInt_sign;
}


int #fromHex_pt;
int fromHex_i,
	fromHex_temp,
	fromHex_zeros;
const string hex = "0123456789abcdef";
function String_fromHex(int integer) {
	fromHex_zeros = TRUE;
	fromHex_pt = List(0, sizeof(char), UNSIGNED);
	for(fromHex_i=0; fromHex_i<INT_BITS/4; fromHex_i++) {
		fromHex_temp = ((integer & (0x0000000F << (4 * (7 - fromHex_i)))) >> (4 * (7 - fromHex_i))) & 0x0000000F;
		if(fromHex_temp && fromHex_zeros) fromHex_zeros = FALSE;
		if(!fromHex_zeros) fromHex_pt = List_append(duint8(hex[fromHex_temp]), fromHex_pt);
	}
	return fromHex_pt;
}


int toHex_i,
	toHex_len,
	toHex_temp,
	toHex_value;
function String_toHex(int #address) {
	toHex_len = List_len(address);
	if(toHex_len > 8 || toHex_len < 1) return 0;
	toHex_value = 0;
	for(toHex_i=0; toHex_i<toHex_len; toHex_i++) {
		toHex_temp = List_getIndex(toHex_i, address);
		if(toHex_temp >= ASCII_LOWER_A && toHex_temp <= ASCII_LOWER_F) {
			toHex_temp = toHex_temp - ASCII_LOWER_A + 10;
		}
		else if(toHex_temp >= ASCII_UPPER_A && toHex_temp <= ASCII_UPPER_F) {
			toHex_temp = toHex_temp - ASCII_UPPER_A + 10;
		}
		else if(toHex_temp >= ASCII_DIGIT0 && toHex_temp <= ASCII_DIGIT9) {
			toHex_temp = toHex_temp - ASCII_DIGIT0;
		}
		else return 0;
		toHex_value = (toHex_value << 4) | toHex_temp;
	}
	return toHex_value
}




// #include <list.gph>

define TYPE_BITS = 2;
define SIGN_BITS = 1;
define META_BITS = TYPE_BITS + SIGN_BITS;
define UNSIGNED = 0;
define SIGNED = 1;

int #List_pt;
int List_bits,
	List_i;
function List(int length, int size, int sign) {
	List_bits = size * 8 * length + META_BITS;
	List_pt = Memory_alloc(List_bits);
	if(List_pt > 0) {
		List_setType(size, List_pt);
		List_setSign(sign, List_pt);
		for(List_i=0; List_i<length; List_i++) {
			Memory_setBits(0, List_pt + META_BITS + List_i * size, size * 8);
		}
		return List_pt;
	}
	return -1;
}


int setIndex_bits,
	setIndex_size;
function List_setIndex(int value, int index, int #address) {
	setIndex_size = List_getType(address);
	setIndex_bits = setIndex_size * 8;
	Memory_setBits(value, (address + META_BITS) + (index * setIndex_bits), setIndex_bits);
}


int getIndex_size,
	getIndex_sign,
	getIndex_bits;
function List_getIndex(int index, int #address) {
	getIndex_size = List_getType(address);
	getIndex_sign = List_getSign(address);
	getIndex_bits = getIndex_size * 8;
	return List_unpack(Memory_getBits((address + META_BITS) + (index * getIndex_bits), getIndex_bits), getIndex_bits, getIndex_sign);
}


function List_unpack(int value, int bits, int sign) {
	if(sign) return (value << (32 - bits)) >> (32 - bits);
	return value;
}


int #append_pt;
int append_len,
	append_size;
function List_append(int value, int #address) {
	append_len = List_len(address) + 1;
	append_size = List_getType(address);
	append_pt = Memory_realloc(address, META_BITS + append_len * append_size * 8);
	Memory_setBits(value, append_pt + META_BITS + (append_len - 1) * append_size * 8, append_size * 8);
	return append_pt;
}


int #insert_pt;
int insert_len,
	insert_size,
	insert_i,
	insert_val,
	insert_tmp;
function List_insert(int value, int index, int #address) {
	insert_len = List_len(address) + 1;
	insert_size = List_getType(address);
	insert_pt = Memory_realloc(address, META_BITS + insert_len * insert_size * 8);
	insert_val = value;
	for(insert_i=index; insert_i<insert_len; insert_i++) {
		insert_tmp = Memory_getBits(insert_pt + META_BITS + insert_i * insert_size * 8, insert_size * 8);
		Memory_setBits(insert_val, insert_pt + META_BITS + insert_i * insert_size * 8, insert_size * 8);
		insert_val = insert_tmp;
	}
	return insert_pt;
}


int remove_len,
	remove_size,
	remove_i,
	remove_j, 
	remove_val;
function List_remove(int index, int #address) {
	remove_len = List_len(address);
	remove_size = List_getType(address);
	remove_j = 0;
	for(remove_i=0; remove_i<remove_len; remove_i++) {
		if(remove_i != index) {
			remove_val = Memory_getBits(address + META_BITS + remove_i * remove_size * 8, remove_size * 8)
			Memory_setBits(remove_val, address + META_BITS + remove_j * remove_size * 8, remove_size * 8);
			remove_j++;
		}
	}
	Memory_free(address + META_BITS + remove_size * remove_j * 8);
	return address;
}


int getListLen_i,
	getListLen_size,
	getListLen_len;
function List_len(int #address) {
	getListLen_size = List_getType(address);
	getListLen_i = address + META_BITS;
	getListLen_len = 0;
	while(Memory_getBit(getListLen_i, INIT) == 1) {
		getListLen_i += getListLen_size * 8;
		getListLen_len++;
	}
	return getListLen_len;
}


function List_setType(int size, int #address) {
	Memory_setBits(size - 1, address, TYPE_BITS);
}


function List_getType(int #address) {
	return Memory_getBits(address, TYPE_BITS) + 1;
}


function List_setSign(int sign, int #address) {
	Memory_setBits(sign, address + TYPE_BITS, SIGN_BITS);
}


function List_getSign(int #address) {
	return Memory_getBits(address + TYPE_BITS, SIGN_BITS);
}




// #include <memory.gph>

define INT_BITS = 32;
define HEAP_VARS = 500;
define INT_BYTES = INT_BITS / 8;
define TOTAL_ADDRESSABLE_BITS =  HEAP_VARS * INT_BITS / 2;
define DATA = 0;
define INIT = 1;
int last_pt;


int HEAP[HEAP_VARS];


int alloc_i,
	alloc_j;
function Memory_alloc(int bits) {
	alloc_i = 0;
	while(alloc_i < TOTAL_ADDRESSABLE_BITS) {
		if(!Memory_getBit(alloc_i, INIT) && !Memory_getBit(alloc_i + 1, INIT)) {
			alloc_i++;
			alloc_j = 0;
			while(!Memory_getBit(alloc_i + alloc_j, INIT) && alloc_j <= bits) {
				if(alloc_j == bits) {
				alloc_j = 0;
					while(!Memory_getBit(alloc_i + alloc_j, INIT) && alloc_j <= bits) {
						Memory_setBit(1, alloc_i + alloc_j, INIT);
						if(alloc_j == bits - 1) {
							last_pt = alloc_i;
							return alloc_i;	
						}
						alloc_j++;
					}	
				}
				alloc_j++;
			}
			alloc_i += alloc_j;
		}
		alloc_i++;
	}

	return -1;	
}


int realloc_i,
	realloc_j,
	realloc_new,
	alloc_bits;
function Memory_realloc(int #address, int bits) {
	realloc_i = 0;
	alloc_bits = bits;
	while(Memory_getBit(address + realloc_i, INIT) == 1) {
		realloc_i++;
	}	
	bits -= realloc_i;
	while(realloc_i < TOTAL_ADDRESSABLE_BITS) {
		if(!Memory_getBit(address + realloc_i, INIT) && !Memory_getBit(address + realloc_i + 1, INIT)) {
			realloc_j = 0;
			while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits) {
				if(realloc_j == bits) {
				realloc_j = 0;
					while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits) {
						Memory_setBit(1, address + realloc_i + realloc_j, INIT);
						if(realloc_j == bits - 1) {
							last_pt = address;
							return address;	
						}
						realloc_j++;
					}	
				}
				realloc_j++;
			}
			realloc_i += realloc_j;
		}
		else break;
		realloc_i++;
	}
	
	realloc_new = Memory_alloc(alloc_bits);
	if(realloc_new >= 0) {
		realloc_i = 0;
		while(Memory_getBit(address + realloc_i, INIT) == 1) {
			Memory_setBit(Memory_getBit(address + realloc_i, DATA), realloc_new + realloc_i, DATA);
			realloc_i++;
		}
		Memory_free(address);
		return realloc_new;
	}
	
	return -1;	
}


int free_i;
function Memory_free(int #address) {
	free_i = address;
	while(Memory_getBit(free_i, INIT) == 1) {
		Memory_setBit(0, free_i, DATA);
		Memory_setBit(0, free_i, INIT);
		free_i++;
	}
}


function Memory_freeLast() {
	Memory_free(last_pt);
}


int freeAll_i;
function Memory_freeAll() {
	for(freeAll_i=0; freeAll_i<HEAP_VARS; freeAll_i++) {
		HEAP[freeAll_i] = 0;
	}
}


int free2D_i,
	free2D_len;
function Memory_free2D(int #address) {
	free2D_len = List_len(address);
	for(free2D_i=0; free2D_i<free2D_len; free2D_i++) {
		Memory_free(List_getIndex(free2D_i, address));
	}
	Memory_free(address);
}


int setBits_i,
	setBits_bitVal;
function Memory_setBits(int value, int #address, int bits) {
	setBits_bitVal = 0;
	for(setBits_i=0; setBits_i<bits; setBits_i++) {
		if(Memory_getBit(address + setBits_i, INIT)) {
			setBits_bitVal = (value & Memory_bitMask(setBits_i)) >> (setBits_i);
			Memory_setBit(setBits_bitVal, address + setBits_i, DATA)
		}
	}
}


int getBits_i,
	getBits_bitVal,
	getBits_value;
function Memory_getBits(int #address, int bits) {
	getBits_bitVal = 0;
	getBits_value = 0;
	for(getBits_i=0; getBits_i<bits; getBits_i++) {
		if(Memory_getBit(address + getBits_i, INIT)) {
			getBits_bitVal = Memory_getBit(address + getBits_i, DATA);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << getBits_i);
		}
	}
	return getBits_value;
}


int getBits_c;
function Memory__getBits(int #address, int bits) {
	getBits_bitVal = 0;
	getBits_value = 0;
	getBits_c = 0;
	for(getBits_i=0; getBits_i<bits; getBits_i++) {
		if(!((address + getBits_i) % 2)) {
			getBits_bitVal = Memory_getBit((address / 2) + getBits_c, DATA);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << getBits_i);
		}
		else {
			getBits_bitVal = Memory_getBit((address / 2) + getBits_c, INIT);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << (getBits_i));
			getBits_c++;
		}
	}
	return getBits_value;
}


int bitsAllocated_i,
	bitsAllocated_count,
	bitsAllocated_finalBit;
function Memory_bitsAllocated() {
	bitsAllocated_count = 0;
	for(bitsAllocated_i=0; bitsAllocated_i<TOTAL_ADDRESSABLE_BITS; bitsAllocated_i++) {
		if(Memory_getBit(bitsAllocated_i, INIT)) {
			bitsAllocated_count++; 
			bitsAllocated_finalBit = bitsAllocated_i;
		}
	}
	return bitsAllocated_count;
}


int setBit_int,
	setBit_bit;
function Memory_setBit(int value, int #address, int mode) {
	if(abs(value) > 1) return;
	address *= 2;
	address += mode;
	setBit_int = address / INT_BITS;
	setBit_bit = address % INT_BITS;
	HEAP[setBit_int] = (HEAP[setBit_int] & ~Memory_bitMask(setBit_bit)) | (value << setBit_bit);
}


int getBit_int,
	getBit_bit;
function Memory_getBit(int #address, int mode) {
	address *= 2;
	address += mode;
	getBit_int = address / INT_BITS;
	getBit_bit = address % INT_BITS;
	return (HEAP[getBit_int] & Memory_bitMask(getBit_bit) >> (getBit_bit)) & Memory_bitMask(0);
}


function Memory_bitMask(int bit) {
	return 1 << bit;
}
