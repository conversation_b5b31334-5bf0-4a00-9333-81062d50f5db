// This script regulates the sensitivity of the left and right analog sticks
// by reducing the raw input values to a more manageable range.

main
{
  // left stick
  _sensitivity(POLAR_LX, 16384, 32768 * 2);

  // right stick
  _sensitivity(POLAR_RX, 16384, 32768 * 2);
}

int _val_s, _val;
function _sensitivity(id, mid, sen)
{
  // get the value of the input
  _val = clamp(get_val(id), -32767, 32766);

  // if mid is NOT_USE, then it's a linear conversion
  if (mid == NOT_USE)
  {
    // if the value is negative, then the result should be negative
    _val_s = -1;
    if (_val >= 0)
      _val_s = 1;
    // make the value positive
    _val *= _val_s;
    // if the value is less than mid, then it's a linear conversion
    if (_val <= mid)
      _val = (_val * 16384) / mid;
    // otherwise, it's a non-linear conversion
    else
      _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
    // make the value negative again if it was negative
    _val *= _val_s;
  }

  // if sen is NOT_USE, then don't adjust the sensitivity
  if (sen != NOT_USE)
  {
    // adjust the sensitivity
    _val = (_val * sen) >> 15;
  }

  // set the value of the input
  set_val(id, clamp(_val, -32768, 32767));
}