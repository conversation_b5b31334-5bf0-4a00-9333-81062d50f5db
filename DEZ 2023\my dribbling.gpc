/* ********************* GPC FILE RECOVERED ********************* */

const int8 anglesMax[] = {100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 97, 98, 98, 99, 100, 100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 96, 97, 98, 98, 99, 100};

int radius, angle;
main {

	    if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(MyCombo);
    } else if(combo_running(MyCombo)) {
        combo_stop(MyCombo);
    }

  angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
  set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle%45]));
}

combo MyCombo {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100);
	wait(380);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 0);
    set_val(XB1_LB, 100);
    wait(370);            
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 0);
    set_val(XB1_LB, 0);
    wait(140);             // Wait for 40ms pause
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
    set_val(XB1_LB, 0);
    wait(500);            // Wait for 150ms
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 0);
    set_val(XB1_LB, 0);
    wait(120);  
} 