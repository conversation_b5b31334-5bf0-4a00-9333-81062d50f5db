// Snap Tap GPC Script
// This script implements keyboard-based movement control for Xbox controller emulation
// Currently using A and D keys for left/right movement

const uint8 key_list[] = {
  KEY_A    // Left movement key
  //, KEY_B
  //, KEY_C
  , KEY_D  // Right movement key
  //, KEY_E
  //, KEY_F
  //, KEY_G
  //, KEY_H
  //, KEY_I
  //, KEY_J
  //, KEY_K
  //, KEY_L
  //, KEY_M
  //, KEY_N
  //, KEY_O
  //, KEY_P
  //, KEY_Q
  //, KEY_R
  //, KEY_S
  //, KEY_T
  //, KEY_U
  //, KEY_V
  //, KEY_W
  //, KEY_X
  //, KEY_Y
  //, KEY_Z
  //, KEY_1
  //, KEY_2
  //, KEY_3
  //, KEY_4
  //, KEY_5
  //, KEY_6
  //, KEY_7
  //, KEY_8
  //, KEY_9
  //, KEY_0
  //, KEY_ENTER
  //, KEY_ESC
  //, KEY_BACKSPACE
  //, KEY_TAB
  //, KEY_SPACE
  //, KEY_MINUS
  //, KEY_EQUAL
  //, KEY_LEFTBRACE
  //, KEY_RIGHTBRACE
  //, KEY_BACKSLASH
  //, KEY_HASHTILDE
  //, KEY_SEMICOLON
  //, KEY_APOSTROPHE
  //, KEY_GRAVE
  //, KEY_COMMA
  //, KEY_DOT
  //, KEY_SLASH
  //, KEY_CAPSLOCK
  //, KEY_F1
  //, KEY_F2
  //, KEY_F3
  //, KEY_F4
  //, KEY_F5
  //, KEY_F6
  //, KEY_F7
  //, KEY_F8
  //, KEY_F9
  //, KEY_F10
  //, KEY_F11
  //, KEY_F12
  //, KEY_SYSRQ
  //, KEY_SCROLLLOCK
  //, KEY_PAUSE
  //, KEY_INSERT
  //, KEY_HOME
  //, KEY_PAGEUP
  //, KEY_DELETE
  //, KEY_END
  //, KEY_PAGEDOWN
  //, KEY_RIGHT
  //, KEY_LEFT
  //, KEY_DOWN
  //, KEY_UP
  //, KEY_NUMLOCK
  //, KEY_KPSLASH
  //, KEY_KPASTERISK
  //, KEY_KPMINUS
  //, KEY_KPPLUS
  //, KEY_KPENTER
  //, KEY_KP1
  //, KEY_KP2
  //, KEY_KP3
  //, KEY_KP4
  //, KEY_KP5
  //, KEY_KP6
  //, KEY_KP7
  //, KEY_KP8
  //, KEY_KP9
  //, KEY_KP0
  //, KEY_KPDOT
  //, KEY_102ND
  //, KEY_COMPOSE
  //, KEY_POWER
  //, KEY_KPEQUAL
  //, KEY_F13
  //, KEY_F14
  //, KEY_F15
  //, KEY_F16
  //, KEY_F17
  //, KEY_F18
  //, KEY_F19
  //, KEY_F20
  //, KEY_F21
  //, KEY_F22
  //, KEY_F23
  //, KEY_F24
  //, KEY_OPEN
  //, KEY_HELP
  //, KEY_PROPS
  //, KEY_FRONT
  //, KEY_STOP
  //, KEY_AGAIN
  //, KEY_UNDO
  //, KEY_CUT
  //, KEY_COPY
  //, KEY_PASTE
  //, KEY_FIND
  //, KEY_MUTE
  //, KEY_VOLUMEUP
  //, KEY_VOLUMEDOWN
  //, KEY_KPCOMMA
  //, KEY_RO
  //, KEY_KATAKANAHIRAGANA
  //, KEY_YEN
  //, KEY_HENKAN
  //, KEY_MUHENKAN
  //, KEY_KPJPCOMMA
  //, KEY_HANGEUL
  //, KEY_HANJA
  //, KEY_KATAKANA
  //, KEY_HIRAGANA
  //, KEY_ZENKAKUHANKAKU
  //, KEY_KPLEFTPAREN
  //, KEY_KPRIGHTPAREN
  //, KEY_LEFTCTRL
  //, KEY_LEFTSHIFT
  //, KEY_LEFTALT
  //, KEY_LEFTMETA
  //, KEY_RIGHTCTRL
  //, KEY_RIGHTSHIFT
  //, KEY_RIGHTALT
  //, KEY_RIGHTMETA
  //, KEY_MEDIA_PLAYPAUSE
  //, KEY_MEDIA_STOPCD
  //, KEY_MEDIA_PREVIOUSSONG
  //, KEY_MEDIA_NEXTSONG
  //, KEY_MEDIA_EJECTCD
  //, KEY_MEDIA_VOLUMEUP
  //, KEY_MEDIA_VOLUMEDOWN
  //, KEY_MEDIA_MUTE
  //, KEY_MEDIA_WWW
  //, KEY_MEDIA_BACK
  //, KEY_MEDIA_FORWARD
  //, KEY_MEDIA_STOP
  //, KEY_MEDIA_FIND
  //, KEY_MEDIA_SCROLLUP
  //, KEY_MEDIA_SCROLLDOWN
  //, KEY_MEDIA_EDIT
  //, KEY_MEDIA_SLEEP
  //, KEY_MEDIA_COFFEE
  //, KEY_MEDIA_REFRESH
  //, KEY_MEDIA_CALC
};

int x_direction;
int active_key, release_key, key_buffer, key_list_idx, i, _continue, timer;
int key_array[6];
int key_array_ptime[6];

main {

  // `key_events()` function must be called at the start of main
  key_events(0);

  if(get_keyboard(KEY_A) || get_keyboard(KEY_D)) {
      // Change direction on A press
      if(event_press(KEY_A))
          x_direction = -100;
      // Change direction on D press
      if(event_press(KEY_D))
          x_direction = 100;
      set_val(XB1_LX, x_direction);
  }

}

// Helper function to check if a key was just pressed
function key_press(a) { return (a == active_key); }

// Helper function to check if a key was just released
function key_release(a) { return (a == release_key); }

// Function to get the duration (in ms) that a key has been pressed
// Returns 0 if key is not pressed, otherwise returns time since key press
function key_ptime(a) {
  if (get_key(a))
  for (i = 0; i <= 6; i++)
  {
    if (a == key_array[i]) return timer - key_array_ptime[i];
  }
  return 0;
}

// Main key event handling function
// Manages the state of all pressed keys and updates the key buffer
// Parameters:
//   temp: Temporary variable used for loop control and array indexing
function key_events(temp) {
  timer += get_rtime();
  active_key = release_key = 0;
  // Handle key releases
  if (key_buffer) {
    for (i = 0; i < 6; i++){
      if (key_array[i] && !get_key(key_array[i])) { release_key = key_array[i]; temp = i; break; }
    }
    if (release_key) {
      key_array[temp] = 0;
      key_array_ptime[temp] = 0;
      key_buffer--;
    }
  }
  // Handle new key presses (up to 6 simultaneous keys)
  if (key_buffer < 6) {
    for (temp = 0; temp < sizeof(key_list); temp++) {
      _continue = 1;
      if (get_key(key_list[temp])) {
        // Check if key is already in buffer
        for (i = 0; i < 6; i++) {
          if (key_array[i] == key_list[temp]) { _continue = 0; break; }
        }
        if (_continue) {
          // Add new key to first empty slot
          for (i = 0; i < 6; i++) {
            if (!key_array[i]) {
              key_array[i] = key_list[temp];
              break;
            }
          }
          key_array_ptime[i] = timer;
          active_key = key_list[temp];
          key_buffer++;
        }
      }
    }
  }
}

// Helper function to get the state of a key
// Handles both regular keyboard keys and modifier keys (keys >= 0xE0)
function get_key(key) {
  if (key >= 0xE0) {
    return get_modifiers(1 << (key ^ 0xE0));
  }
  return get_keyboard(key);
}
