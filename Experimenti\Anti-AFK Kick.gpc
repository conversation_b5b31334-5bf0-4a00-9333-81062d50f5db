/*
    Anti-AFK Kick v1.1
    Created By Fadexz
*/

define AFK_Timeout = 10000;

int
    timer,
    input_idx;

main
{

  // -- If the sticks are moved with a radius of 20% or more reset the timer
  if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 2000 && get_ipolar(POLAR_RS, POLAR_RADIUS) >= 2000)
    timer = 0;

  // -- Go through all the main inputs (except sticks) and reset the timer if there is a change in the input since the last loop
  for (input_idx = 1; input_idx <= 20; input_idx++)
  {
    if (input_idx == 9)
      input_idx = 13;
    if (get_ival(input_idx) != get_lval(input_idx))
      timer = 0;
  }

  // -- Running state
  if (timer < AFK_Timeout)
  {
    // -- Increment timer by last loop time passed
    timer += get_rtime();
    // -- If combo is still running after the timer is reset
    if (combo_running(Anti_AFK_Kick))
    {
      reset_rumble();
      combo_stop(Anti_AFK_Kick);
    }
  }
  // -- Timer has past set time
  else
  {
    block_all_inputs();
    block_rumble();
    combo_run(Anti_AFK_Kick);
  }
}

combo Anti_AFK_Kick
{
  set_val(XB1_LT, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LX, -100);
  wait(40);
  wait(3400);
  set_val(XB1_LS, 100);
  wait(40);
  wait(3400);
  set_val(XB1_RY, -100);
  wait(40);
  wait(3400);
  set_val(XB1_LT, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LY, -100);
  wait(40);
  wait(3400);
  set_val(XB1_LS, 100);
  wait(40);
  wait(3400);
  set_val(XB1_RX, -100);
  wait(40);
  wait(3400);
  set_val(XB1_LT, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LX, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LS, 100);
  wait(40);
  wait(3400);
  set_val(XB1_RY, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LT, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LY, 100);
  wait(40);
  wait(3400);
  set_val(XB1_LS, 100);
  wait(40);
  wait(3400);
  set_val(XB1_RX, 100);
  wait(40);
  wait(3400 - get_rtime());
}