const int8 anglesMax[] = {100, 93, 86, 79, 72, 72, 72, 79, 86, 93, 100, 93, 86, 79, 72, 72, 72, 79, 86, 93, 100, 93, 86, 79, 72, 72, 72, 79, 86, 93, 100, 93, 86, 79, 72, 72, 72, 79, 86, 93, 100, 93, 86, 79, 72};
int radius, angle;
main {


if(get_ival(XB1_LB)){
	//set_val(XB1_RB, 100);
	angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
  set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle%45])); }


if(get_ival(XB1_RB)){
	//set_val(XB1_RB, 100);
	angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
  set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle%45])); }
}
