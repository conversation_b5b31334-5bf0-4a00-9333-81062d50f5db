/*
           ██████╗  ██████╗  ██████╗ ███████╗    ██████╗  ██████╗ ██╗      █████╗ ██████╗     ███████╗███████╗███████╗███████╗ ██████╗████████╗███████╗
           ██╔══██╗██╔═══██╗██╔════╝ ╚══███╔╝    ██╔══██╗██╔═══██╗██║     ██╔══██╗██╔══██╗    ██╔════╝██╔════╝██╔════╝██╔════╝██╔════╝╚══██╔══╝██╔════╝
           ██║  ██║██║   ██║██║  ███╗  ███╔╝     ██████╔╝██║   ██║██║     ███████║██████╔╝    █████╗  █████╗  █████╗  █████╗  ██║        ██║   ███████╗
           ██║  ██║██║   ██║██║   ██║ ███╔╝      ██╔═══╝ ██║   ██║██║     ██╔══██║██╔══██╗    ██╔══╝  ██╔══╝  ██╔══╝  ██╔══╝  ██║        ██║   ╚════██║
           ██████╔╝╚██████╔╝╚██████╔╝███████╗    ██║     ╚██████╔╝███████╗██║  ██║██║  ██║    ███████╗██║     ██║     ███████╗╚██████╗   ██║   ███████║
           ╚═════╝  ╚═════╝  ╚═════╝ ╚══════╝    ╚═╝      ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝    ╚══════╝╚═╝     ╚═╝     ╚══════╝ ╚═════╝   ╚═╝   ╚══════╝
                                       ██╗   ██╗███████╗██████╗ ███████╗██╗ ██████╗ ███╗   ██╗   ██████╗     ██╗ ██████╗ 
                                       ██║   ██║██╔════╝██╔══██╗██╔════╝██║██╔═══██╗████╗  ██║   ╚════██╗   ███║██╔═████╗
                                       ██║   ██║█████╗  ██████╔╝███████╗██║██║   ██║██╔██╗ ██║    █████╔╝   ╚██║██║██╔██║
                                       ╚██╗ ██╔╝██╔══╝  ██╔══██╗╚════██║██║██║   ██║██║╚██╗██║   ██╔═══╝     ██║████╔╝██║
                                        ╚████╔╝ ███████╗██║  ██║███████║██║╚██████╔╝██║ ╚████║   ███████╗██╗ ██║╚██████╔╝
                                         ╚═══╝  ╚══════╝╚═╝  ╚═╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝   ╚══════╝╚═╝ ╚═╝ ╚═════╝ 																													 
                                                 ███████╗ ██████╗ ██████╗ ████████╗███╗   ██╗██╗████████╗███████╗
                                                 ██╔════╝██╔═══██╗██╔══██╗╚══██╔══╝████╗  ██║██║╚══██╔══╝██╔════╝
                                                 █████╗  ██║   ██║██████╔╝   ██║   ██╔██╗ ██║██║   ██║   █████╗  
                                                 ██╔══╝  ██║   ██║██╔══██╗   ██║   ██║╚██╗██║██║   ██║   ██╔══╝  
                                                 ██║     ╚██████╔╝██║  ██║   ██║   ██║ ╚████║██║   ██║   ███████╗
                                                 ╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═══╝╚═╝   ╚═╝   ╚══════╝

                    ▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
                    ██ ██ █ ▄▄█ ▄▄█ ▄▄▀████ ▄▄▀█ ▄▄█ ▄▄█ ▄▄█ ▄▄▀█ ▄▄█ ▄▄▀█▀▄▀█ ▄▄██
                    ██ ██ █▄▄▀█ ▄▄█ ▀▀▄████ ▀▀▄█ ▄▄█ ▄██ ▄▄█ ▀▀▄█ ▄▄█ ██ █ █▀█ ▄▄██
                    ██▄▀▀▄█▄▄▄█▄▄▄█▄█▄▄████ ██ █▄▄▄█▄███▄▄▄█▄█▄▄█▄▄▄█▄██▄██▄██▄▄▄██
                    ▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀
                    
                    ╔═════════════════════════════════════════════╗
                            SCRIPT MODS INCLUDED VERSION 2.10
                    ╚═════════════════════════════════════════════╝                    
                    1.  New Speed Menu Designed to Find What You Want Faster
                    2.  DoGz Polar Effects Aim Assist Version 2.00 ^^^ 43 Effects See Below ^^^
                    3️.  DoGz Sixth Sense Anti-Recoil               ^^^ Adjusted To Work With DoGz Polar Effects ^^^ 	***UPDATED QUICK ADD/SUBTRACT VALUE [ON THE FLY]***
                    4.  Rapid Fire
                    5.  Drop Shot {Crouch Shot}
                    6.  Jump Shot
                    7.  Strafe Shot
                    8.  Perfect Accuracy
                    9.  Instant Pickaxe
                    10. Build Tracker 
                    11. Quick Edit Assist  [^^^ REQUIRES CONFIRM ON RELEASE ON IN GAME ^^^]
                    12. Fast Reset         [^^^ REQUIRES CONFIRM ON RESET ON IN GAME   ^^^]      [^^^ CONFIRM ON BOTH IN GAME == IF USING QUICK EDIT ASSIST & FAST RESET ^^^]
                    13. Wall Take 
                    14. Wall Take Build Piece     
                    15. Pump Wall 		   
                    16. Pump Ramp           
                    17. Block Shot          
                    18. Ramp Floor Wall      
                    19. Floor Pyramid       
                    20. PlayStation On Xbox Fix [TOUCHPAD] 
                    21. Pump Smg            
                    22. Adaptive Aim Abuse      
                    23. Hair Triggers
                    24. Block Rumble               
                    ╔═════════════════════════════════════════════╗
                               PATCH NOTES VERSION 2.10
                    ╚═════════════════════════════════════════════╝                   
                    1.  Added Mod Jump Shot 
                    2.  Added Mod Strafe Shot
                    3️.  Added Mod Wall Take 
                    4.  Added Mod Wall Take Build Piece     
                    5.  Added Mod Pump Wall 		   
                    6.  Added Mod Pump Ramp           
                    7.  Added Mod Block Shot          
                    8.  Added Mod Ramp Floor Wall      
                    9.  Added Mod Floor Pyramid       
                    10. Added Mod PlayStation On Xbox Fix [TOUCHPAD] 
                    11. Added Mod Pump Smg            
                    12. Added Mod Adaptive Aim Abuse      
                    13. Added Mod Hair Triggers
                    14. Added Mod Block Rumble
                    15. Added New Toggles & Activators For Certain Mods
                    16. Added Anti Recoil Speed Adjustment
                    17. Added New Functionality To Shot Mods And Fire Mod [Now Release Of Fire Can Disable Mod]         
                    18. Rewrote Script To Allow For All New Changes                                                     
                    ╔═══════════════════════════════════════════════╗ 
                                  🅼🅾🅳 🅼🅴🅽🆄 🆂🆈🆂🆃🅴🅼             
                    ╚═══════════════════════════════════════════════╝ 
                    ╔═══════════════════════════════════════════════╗ 
                    ║  NEW SPEED MENU LAYOUT UPGRADED READ BELOW!   ║ 
                    ║ * Enter Menu = L2/LT + OPTIONS/MENU           ║ 
                    ║ * Next Menu  = CROSS/A (Mod Values/Settings)  ║ ╔═════════════════════════════════════════════╗	    
                    ║ * Back to Main Mod = CIRCLE/B                 ║        🆂🅿🅴🅴🅳 🆃🅾🅶🅶🅻🅴🆂 [🅳🅴🅵🅰🆄🅻🆃]       	
                    ║ * Exit Menu = TRIANGLE/Y                      ║ ╚═════════════════════════════════════════════╝ 
                    ║ * Next Mod/Scroll Right    = RIGHT DPAD       ║ ╔═════════════════════════════════════════════╗     
                    ║ * Previous Mod/Scroll Left = LEFT DPAD        ║ ║      [RAPID FIRE] = (R2/RT + RIGHT DPAD)    ║     
                    ║                                               ║ ║       [CROUCH SHOT] = (R2/RT + CROUCH)      ║ 	
                    ║ * Adjust Mod/Value  [+ Increase] = UP         ║ ║         [JUMP SHOT] = (L2/LT + JUMP)        ║
                    ║ * Adjust Mod/Value  [- Decrease] = DOWN       ║ ║       [STRAFE SHOT] = (L2/LT + DOWN DPAD)   ║    
                    ║         (HOLD TO ADJUST FASTER)               ║ ║  [PERFECT ACCURACY] = (L2/LT + SHARE/VIEW)  ║       
                    ║  											    ║ ║       [ANTI_RECOIL] = (L2/LT + LEFT DPAD)   ║     
                    ║  * AUTOSAVES SETTINGS WHEN YOU EXIT MENU *    ║ ║     [AIM ABUSE] = (SQUARE/X + RIGHT DPAD)   ║ 
                    ║        * AUTO TIMEOUT WHEN IDOL *             ║ ║    [GLOBAL MODS] = (SQUARE/X + LEFT DPAD)   ║     
                    ╚═══════════════════════════════════════════════╝ ╚═════════════════════════════════════════════╝     
																	  *NOTE* --> GLOBAL MODS ENABLE/DISABLES ALL MODS AT ONCE... HERE = [PumpWall + PumpRamp + BlockShot + PumpSmg + WallTake + WallTakeBuild]
					╔═══════════════════════════════════════════════╗ ╔═════════════════════════════════════════════╗ 																										
					  🅰🅽🆃🅸🆁🅴🅲🅾🅸🅻 🅾🅽 🆃🅷🅴 🅵🅻🆈 🅰🅳🅹🆄🆂🆃🅴🆁   🅼🅾🅳 🅰🅲🆃🅸🆅🅰🆃🅴 🆃🅾🅶🅶🅻🅴🆂[🅳🅴🅵🅰🆄🅻🆃] 																		
					╚═══════════════════════════════════════════════╝ ╚═════════════════════════════════════════════╝																	
					╔═══════════════════════════════════════════════╗ ╔═════════════════════════════════════════════╗ 																	
					║ NEW ANTI-RECOIL SPEED ADJUSTMENT READ BELOW!  ║ ║     [PERFECT ACCURACY] (L2/LT + R3/RS)      ║																	
					║ 		                                        ║ ║     	  [WALL TAKE] (R2/RT + L1/LB)       ║ 
					║               * HOLD SQUARE/X +               ║ ║      [WALL TAKE PIECE] (R2/RT + R1/RB)      ║ 
					║      INCREASE VALUE OF RECOIL = TAP UP        ║ ║         [PUMP WALL] (L2/LT + R1/RB)         ║                    
					║      DECREASE VALUE OF RECOIL = TAP DOWN      ║ ║         [PUMP RAMP] (L2/LT + L1/LB)         ║                    									
					║                                               ║ ║    [BLOCK SHOT] (SQUARE/X + TOUCHPAD/VIEW)  ║                    									
					║ *THIS WILL ADD/SUBTRACT FROM ALL MENU VALUES* ║ ║        [RAMP FLOOR WALL] (SQUARE/X)         ║                   
 					╚═══════════════════════════════════════════════╝ ║ [FLOOR N PYRAMID] (DOUBLE TAP FLOOR + HOLD) ║                   									
					*NOTE* --> ADD IS 3 BY DEFAULT                    ╚═════════════════════════════════════════════╝                   
					*NOTE* --> SUBTRACT IS 2 BY DEFAULT 			  *NOTE* --> COMBAT MODS (NOT IN BUILD MODE) INCLUDE... [PERFECT ACCURACY + WALL TAKE + WALL TAKE PIECE + PUMP WALL + PUMP RAMP + BLOCK SHOT] 
																	  *NOTE* --> BUILD MODS (IN BUILD MODE INCLUDE... [RAMP FLOOR WALL + FLOOR N PYRAMID]                                       
					╔════════════════════════════════════════════════════════════════════════════════╗                  
                    ║    ╔═══╗ ╔═══╗ ╔═══╗╔═══╗╔═══╗╔════╗    ╔═══╗╔═══╗╔════╗╔══╗╔═══╗╔═╗ ╔╗╔═══╗   ║                  
                    ║    ║╔══╝ ║╔══╝ ║╔══╝║╔══╝║╔═╗║║╔╗╔╗║    ║╔═╗║║╔═╗║║╔╗╔╗║╚╣╠╝║╔═╗║║║╚╗║║║╔═╗║   ║
                    ║    ║╚══╗ ║╚══╗ ║╚══╗║╚══╗║║ ╚╝╚╝║║╚╝    ║║ ║║║╚═╝║╚╝║║╚╝ ║║ ║║ ║║║╔╗╚╝║║╚══╗   ║
                    ║    ║╔══╝ ║╔══╝ ║╔══╝║╔══╝║║ ╔╗  ║║      ║║ ║║║╔══╝  ║║   ║║ ║║ ║║║║╚╗║║╚══╗║   ║
                    ║    ║╚══╗╔╝╚╗  ╔╝╚╗  ║╚══╗║╚═╝║ ╔╝╚╗     ║╚═╝║║║    ╔╝╚╗ ╔╣╠╗║╚═╝║║║ ║║║║╚═╝║   ║
                    ║    ╚═══╝╚══╝  ╚══╝  ╚═══╝╚═══╝ ╚══╝     ╚═══╝╚╝    ╚══╝ ╚══╝╚═══╝╚╝ ╚═╝╚═══╝   ║
                    ║                                                                                ║
                    ║         AIM ASSIST EFFECTS TO CHOOSE & TEST!!  0 to 42 (Enter ID or Name)      ║
                    ╚════════════════════════════════════════════════════════════════════════════════╝                                                                                                                                                            
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   0. ║       Circle       ║  8. ║     TrappedEye     ║ 16. ║ CurvilinearDiamond ║  24. ║       Barrels      ║  32. ║      FullStack     ║  40. ║       Spring       ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   1. ║     Concentric     ║  9. ║      Baseball      ║ 17. ║       Diamond      ║  25. ║      FatCross      ║  33. ║       Teasure      ║  41. ║      NinjaStar     ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   2. ║     Concentric2    ║ 10. ║      RugbyBall     ║ 18. ║        Star        ║  26. ║      ThinCross     ║  34. ║       Galaxy       ║  42. ║      Labyrinth     ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   3. ║     Concentric3    ║ 11. ║   ConcentricOval   ║ 19. ║       Rainbow      ║  27. ║       Orbiter      ║  35. ║       Coiled       ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   4. ║       Portal       ║ 12. ║     Beachball      ║ 20. ║       Smiley       ║  28. ║   RoundedFatCross  ║  36. ║    RockyMountain   ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   5. ║       Bullet       ║ 13. ║     NestedOvals    ║ 21. ║        Wings       ║  29. ║    RoundedCross    ║  37. ║       Killbox      ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   6. ║        Eye         ║ 14. ║       React        ║ 22. ║       Pyramid      ║  30. ║     Loop2Loop      ║  38. ║       Scanner      ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   7. ║      TightEye      ║ 15. ║      TearDrop      ║ 23. ║     TwinDiamonds   ║  31. ║    CrossSection    ║  39. ║    Rollercoaster   ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
/*
██╗   ██╗███████╗███████╗██████╗     ███████╗███████╗████████╗██╗   ██╗██████╗     ██████╗ ███████╗██╗      ██████╗ ██╗    ██╗    ██╗
██║   ██║██╔════╝██╔════╝██╔══██╗    ██╔════╝██╔════╝╚══██╔══╝██║   ██║██╔══██╗    ██╔══██╗██╔════╝██║     ██╔═══██╗██║    ██║    ██║
██║   ██║███████╗█████╗  ██████╔╝    ███████╗█████╗     ██║   ██║   ██║██████╔╝    ██████╔╝█████╗  ██║     ██║   ██║██║ █╗ ██║    ██║
██║   ██║╚════██║██╔══╝  ██╔══██╗    ╚════██║██╔══╝     ██║   ██║   ██║██╔═══╝     ██╔══██╗██╔══╝  ██║     ██║   ██║██║███╗██║    ╚═╝
╚██████╔╝███████║███████╗██║  ██║    ███████║███████╗   ██║   ╚██████╔╝██║         ██████╔╝███████╗███████╗╚██████╔╝╚███╔███╔╝    ██╗
 ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝   ╚═╝    ╚═════╝ ╚═╝         ╚═════╝ ╚══════╝╚══════╝ ╚═════╝  ╚══╝╚══╝     ╚═╝
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                    In Game Buttons                 )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
// define Fire     = PS4_R2;
// define Ads      = PS4_L2;
define Fire     = XB1_A;
define Ads      = XB1_B;
define N_Weapon = PS4_R1;
define P_Weapon = PS4_L1;
define Pickaxe  = PS4_TRIANGLE;
define Crouch   = PS4_R3;
define Jump     = PS4_CROSS;
define Edit     = PS4_CIRCLE;
define Select   = PS4_R2;
define Reset    = PS4_R3;
define Build    = PS4_CIRCLE;
define Wall     = PS4_R2;
define Floor    = PS4_R1;
define Ramp     = PS4_L2;
define Roof     = PS4_L1;
define Trap     = PS4_SQUARE;
define Up       = PS4_UP;
define Down     = PS4_DOWN;
define Left     = PS4_LEFT;
define Right    = PS4_RIGHT;
define AimX     = PS4_RX;
define AimY     = PS4_RY;
define Walk     = PS4_LY;
define Strafe   = PS4_LX;
define AimPX    = POLAR_LX;
define AimPY    = POLAR_LY;
define On       = TRUE;
define Off      = FALSE;
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                    Mod Buttons                     )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define EditAssist    = PS4_L3;
define FastReset     = PS4_TOUCH;
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                    Mod Toggles                     ) SPEED TOGGLES ENABLES/DISABLES MODS QUICKLY WHEN IN GAME (RATHER THAN GOING INTO THE MENU) When Enable In Menu System
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define AntiRecoilModHold  = PS4_L2;   // ANTI RECOIL SPEED TOGGLE BUTTONS
define AntiRecoilModPress = PS4_LEFT;

define RapidFireModHold   = PS4_R2;   // RAPID FIRE SPEED TOGGLE BUTTONS
define RapidFireModPress  = PS4_RIGHT;

define DropShotModHold    = PS4_R2;   // DROP SHOT SPEED TOGGLE BUTTONS (Crouch Shot)
define DropShotModPress   = Crouch;
define BlockPressDS       = Off;      // BLOCK EVENT PRESS (STOP CONTROLLER BEING PRESSED IN GAME) 

define JumpShotModHold    = PS4_L2;   // JUMP SHOT SPEED TOGGLE BUTTONS (Jump Shot)
define JumpShotModPress   = Jump;
define BlockPressJS       = Off;      // BLOCK EVENT PRESS (STOP CONTROLLER BEING PRESSED IN GAME) 

define StrafeShotModHold  = PS4_L2;   // STRAFE SHOT SPEED TOGGLE BUTTONS (Strafe Shot)
define StrafeShotModPress = PS4_DOWN;

define AccuracyModHold    = PS4_L2;   // PERFECT ACCURACY SPEED TOGGLE BUTTONS
define AccuracyModPress   = PS4_SHARE;

define AimAbuseModHold    = PS4_SQUARE;// AIM ABUSE SPEED TOGGLE BUTTONS
define AimAbuseModPress   = PS4_RIGHT;

define GlobalModHold      = PS4_SQUARE;// GLOBAL MODS SPEED TOGGLE BUTTONS
define GlobalModPress     = PS4_LEFT;
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                   Mod Activators                   ) ACTIVATOR WILL RUN THE MOD WHEN ENABLED
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define PerfectAccuracyHold  = PS4_L2;  	 // PERFECT ACCURACY ACTIVATOR BUTTONS
define PerfectAccuracyPress = PS4_R3;

define WallTakeHold         = PS4_R2;  	 // WALL TAKE
define WallTakePress        = PS4_L1;  

define WallTakeBuildHold    = PS4_R2;  	 // WALL TAKE BUILD ACTIVATOR BUTTONS  	
define WallTakeBuildPress   = PS4_R1;    	
define WallTakeBuildPiece   = Roof;    	 // BUILD PIECE TO PLACE

define PumpWallHold  		= PS4_L2;  	 // PUMP WALL ACTIVATOR BUTTONS  
define PumpWallPress 		= PS4_R1;  
									                  
define PumpRampHold  		= PS4_L2;  	 // PUMP RAMP ACTIVATOR BUTTONS   
define PumpRampPress 		= PS4_L1; 

define BlockShotHold  		= PS4_SQUARE;// BLOCK SHOT ACTIVATOR BUTTONS 
define BlockShotPress 		= PS4_TOUCH; 

define RampFloorWallPress   = PS4_SQUARE;// RAMP FLOOR WALL ACTIVATOR BUTTON
define BlockPressRFW        = Off;       // BLOCK EVENT PRESS (STOP CONTROLLER BEING PRESSED IN GAME)
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(         Recoil On The Fly Adjustment Buttons       )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define RecoilHoldOnFly  = PS4_SQUARE;  // RECOIL ADJUSTMENT BUTTONS [HOLD +]
define RecoilUpPress    = PS4_UP;	   // TAP + RecoilValueAdd [DEFAULT 3] * LINE 396*
define RecoilDownPress  = PS4_DOWN;    // TAP - RecoilValueSub [DEFUALT 2] * LINE 397*
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                    Menu Buttons                    )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/																					
define MenuEnterHold   = PS4_L2;		// HOLD + TAP MenuEnterPress To ENTER MENU 			{DEFAULT L2/LT + OPTIONS/MENU}
define MenuEnterPress  = PS4_OPTIONS;   // WHILE HOLDING MenuEnterHold, TAP TO ENTER MENU 	{DEFAULT L2/LT + OPTIONS/MENU}
define MenuExitPress   = PS4_TRIANGLE;	// EXIT MENU COMPLETELY								{DEFAULT TRIANGLE/Y}
define ChangeMenuPress = PS4_CROSS; 	// ENTER MOD VALUES									{DEFAULT CROSS/A}
define MenuBackPress   = PS4_CIRCLE;	/* BACK TO MOD LIST									{DEFAULT CIRCLE/B} 

					╔════════════════════════════════════════════════════════════════════════════════╗
                    ║    ╔═══╗ ╔═══╗ ╔═══╗╔═══╗╔═══╗╔════╗    ╔═══╗╔═══╗╔════╗╔══╗╔═══╗╔═╗ ╔╗╔═══╗   ║
                    ║    ║╔══╝ ║╔══╝ ║╔══╝║╔══╝║╔═╗║║╔╗╔╗║    ║╔═╗║║╔═╗║║╔╗╔╗║╚╣╠╝║╔═╗║║║╚╗║║║╔═╗║   ║
                    ║    ║╚══╗ ║╚══╗ ║╚══╗║╚══╗║║ ╚╝╚╝║║╚╝    ║║ ║║║╚═╝║╚╝║║╚╝ ║║ ║║ ║║║╔╗╚╝║║╚══╗   ║
                    ║    ║╔══╝ ║╔══╝ ║╔══╝║╔══╝║║ ╔╗  ║║      ║║ ║║║╔══╝  ║║   ║║ ║║ ║║║║╚╗║║╚══╗║   ║
                    ║    ║╚══╗╔╝╚╗  ╔╝╚╗  ║╚══╗║╚═╝║ ╔╝╚╗     ║╚═╝║║║    ╔╝╚╗ ╔╣╠╗║╚═╝║║║ ║║║║╚═╝║   ║
                    ║    ╚═══╝╚══╝  ╚══╝  ╚═══╝╚═══╝ ╚══╝     ╚═══╝╚╝    ╚══╝ ╚══╝╚═══╝╚╝ ╚═╝╚═══╝   ║
                    ║                                                                                ║
                    ║         AIM ASSIST EFFECTS TO CHOOSE & TEST!!  0 to 42 (Enter ID or Name)      ║
                    ╚════════════════════════════════════════════════════════════════════════════════╝                                                                                                                                                            
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   0. ║       Circle       ║  8. ║     TrappedEye     ║ 16. ║ CurvilinearDiamond ║  24. ║       Barrels      ║  32. ║      FullStack     ║  40. ║       Spring       ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   1. ║     Concentric     ║  9. ║      Baseball      ║ 17. ║       Diamond      ║  25. ║      FatCross      ║  33. ║       Teasure      ║  41. ║      NinjaStar     ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   2. ║     Concentric2    ║ 10. ║      RugbyBall     ║ 18. ║        Star        ║  26. ║      ThinCross     ║  34. ║       Galaxy       ║  42. ║      Labyrinth     ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   3. ║     Concentric3    ║ 11. ║   ConcentricOval   ║ 19. ║       Rainbow      ║  27. ║       Orbiter      ║  35. ║       Coiled       ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   4. ║       Portal       ║ 12. ║     Beachball      ║ 20. ║       Smiley       ║  28. ║   RoundedFatCross  ║  36. ║    RockyMountain   ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   5. ║       Bullet       ║ 13. ║     NestedOvals    ║ 21. ║        Wings       ║  29. ║    RoundedCross    ║  37. ║       Killbox      ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   6. ║        Eye         ║ 14. ║       React        ║ 22. ║       Pyramid      ║  30. ║     Loop2Loop      ║  38. ║       Scanner      ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
      ╔════════════════════╗     ╔════════════════════╗     ╔════════════════════╗      ╔════════════════════╗      ╔════════════════════╗
   7. ║      TightEye      ║ 15. ║      TearDrop      ║ 23. ║     TwinDiamonds   ║  31. ║    CrossSection    ║  39. ║    Rollercoaster   ║
      ╚════════════════════╝     ╚════════════════════╝     ╚════════════════════╝      ╚════════════════════╝      ╚════════════════════╝
*/
int ModDigit = 44;
int Effect            = NinjaStar;   // Effect Selection
int AssistPower       = 20;          // Power On Ads	 [While Pressing Only Ads] [NinjaStar + Labyrinth Power / 10]
int AssistSpeed       = 15;		     // Speed On Ads	 [While Pressing Only Ads] [NinjaStar + Labyrinth N/A]
int AssistPowerBoost  = 20;          // Power On Fire [While Pressing Only Fire or Ads + Fire][NinjaStar + Labyrinth N/A]
int AssistSpeedBoost  = 38;          // Speed On Fire [While Pressing Only Fire or Ads + Fire] [NinjaStar + Labyrinth N/A]	   
int BoostPower        = On;          // Power Boost On Fire On or Off
int BoostSpeed        = On;          // Speed Boost On Fire On or Off
int PolarHipFire      = On;          // Hip Fire Assist On or Off
int AntiRecoilMode    = Progression; // Method = ( RecoilSet  or  Progression ) {Progression Will Remain On Last Value or Recoil Set Will Repeat The Loop} 
int verticalOne       = 39; 		 // Vertical Value 1
int verticalTwo       = 41; 		 // Vertical Value 2
int verticalThree     = 43; 		 // Vertical Value 3
int verticalFour      = 45; 		 // Vertical Value 4
int verticalFive      = 43; 	     // Vertical Value 5 
int verticalSix       = 41; 	     // Vertical Value 6 
int recoilYTimeOne    = 100;	     // Vertical Time 1 [Milliseconds] 1000 ms = 1 Second
int recoilYTimeTwo    = 200; 	     // Vertical Time 2 [Milliseconds]
int recoilYTimeThree  = 300; 	     // Vertical Time 3 [Milliseconds]
int recoilYTimeFour   = 250; 	     // Vertical Time 4 [Milliseconds]
int recoilYTimeFive   = 500; 	     // Vertical Time 5 [Milliseconds]
int recoilYTimeSix    = 350; 	     // Vertical Time 6 [Milliseconds]
int RapidFireHold     = 30;   	     // Time press is held
int RapidFireWait     = 30;   	     // Time before next press
int rfActivate        = AnyFire;     // [^ FireOnly - AdsFire - AnyFire ^] - Rapid Fire Activator Method
int rfDeActivate      = Release;     // [^ Release - Toggled ^] - Rapid Fire Deactvate Method [Release = Releasing Fire Will Auto Switch Off -- Toggled = Requires Toggling]
int DropShotWait      = 150;  	     // Time before next crouch
int dsActivate        = AdsFire;     // [^ FireOnly - AdsFire - AnyFire ^] - Drop Shot Activator Method
int dsDeActivate      = Release;     // [^ Release - Toggled ^] - Drop Shot Deactvate Method [Release = Releasing Fire Will Auto Switch Off -- Toggled = Requires Toggling]
int JumpShotTime      = 250;  	     // Time = [ {Press = Not Used} - {Hold = Time Between Jumps} - {Delayed = Time After Shot} ]  
int jsActivate        = Delayed;     // [^ Press - Hold - Delayed ^] - Jump Shot Activator Method
int jsDeActivate      = Release;     // [^ Release - Toggled ^] - Jump Shot Deactvate Method [Release = Releasing Fire Will Auto Switch Off -- Toggled = Requires Toggling]
int StrafeShotWait    = 1500;  	     // Time before direction inverted 
int ssActivate        = AnyFire;     // [^ FireOnly - AdsFire - AnyFire ^] - Strafe Shot Activator Method
int ssDeActivate      = Release;     // [^ Release - Toggled ^] - Strafe Shot Deactvate Method [Release = Releasing Fire Will Auto Switch Off -- Toggled = Requires Toggling]
int AccuracyTime      = 350;  	     // Time Between Accuracy Shot
int BuildSyncTime     = 2000; 	     // Auto De-Sync After 2 Seconds Of being Idol [Pickaxe == Manual Re-Sync]
int WallTakeTime      = 40;          // Wall Take Press and Delay Time
int WallTakeBuildTime = 40;          // Wall Take Build Press and Delay Time
int PumpWallTime 	  = 50;          // Pump Wall Press and Delay Time
int PumpRampTime 	  = 50;          // Pump Ramp Press and Delay Time
int BlockShotTime 	  = 50;          // Block Shot Press And Delay Time
int PumpSmgTime       = 30;          // Pump Smg Delay Time
int AdaptiveHold      = 120;         // Adaptive Aim Abuse - Press Time LIMITS [50 - 150] [Variable output based on the Aim stick input]
int AdaptiveRelease   = 20;          // Adaptive Aim Abuse - Release Time LIMITS [10 - 60] [Variable output based on the Aim stick input]
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                   Mod Toggles                      ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/ 
int ModSwitch = 21;
int DoGzPolarEffectsV2 = On;     // [Off - On] - Aim Assist Mod
int SixthSenseRecoil   = On;     // [Off - On] - AntiRecoil Mod
int RapidFire          = Off;    // [Off - On] - Rapid Fire Mod
int DropShot           = Off;    // [Off - On] - Drop Shot Mod
int JumpShot           = Off;    // [Off - On] - Jump Shot Mod
int StrafeShot         = Off;    // [Off - On] - Strafe Shot Mod
int PerfectAccuracy    = Off;    // [Off - On] - 100% Accuracy Mod
int InstantPickaxe     = Off;	 // [Off - On] - Instant Pickaxe
int BuildTracker       = Off;    // [Off - On] - Isolate Aim Assist & Other Mods In Build Mode
int QuickEditAssist    = Off;	 // [Off - On] - Quick Edit Assist
int InstaFastReset     = Off;	 // [Off - On] - Fast Reset
int WallTake           = Off;	 // [Off - On] - Wall Take
int WallTakeBuild      = Off;	 // [Off - On] - Wall Take Build Piece
int PumpWall 		   = Off;	 // [Off - On] - Pump Shot Build Wall
int PumpRamp           = Off;	 // [Off - On] - Pump Shot Build Ramp
int BlockShot          = Off;	 // [Off - On] - Block Shot
int RampFloorWall      = Off;    // [Off - On] - Ramp Floor Roof
int FloorPyramid       = Off;    // [Off - On] - Floor n Pyramid
int PlayStationOnXbox  = Off;    // [Off - On] - Touchpad Fix On PC or Xbox
int PumpSmg            = Off;    // [Off - On] - Pump Shot Swap to Smg
int AdaptiveAbuse      = Off;    // [Off - On] - Adaptive Aim Abuse [Variable output based on the Aim stick input]
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(           Quick Toggles Default State              )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
int AntiRecoilMod = On  // [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int RapidFireMod  = Off;// [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int DropShotMod   = Off;// [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int JumpShotMod   = Off;// [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int StrafeShotMod = Off;// [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int AccuracyMod   = On; // [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int AimAbuseMod   = On; // [Off - On] Toggle Default State {Leave On If Unsure What It Is}
int GlobalMod     = On; // [Off - On] Toggle Default State {Leave On If Unsure What It Is}
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                 Additional Settings                )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define InvertedYAxis  = Off; // Anti Recoil Inverted Aim Option
define BlockRumble    = On;  // Block In Game Rumble
define HairTriggers   = On;  // Instant Triggers
define AbuseOnFire    = On;  // Aim Abuse Requires Ads + Fire  [Note Ads Must Be More Than The Below Limit On Both Settings]
define AimAbuseLimit  = 90;  // Aim Abuse Ads Value To Run Mod
define RecoilValueAdd = 3;   // On The Fly Recoil Adjuster Increment Value {Add}
define RecoilValueSub = 2;   // On The Fly Recoil Adjuster Decrement Value {Subtract}
int AntiRecoilAdjust  = 0;   // On The Fly Recoil Adjuster
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                       Init                         ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/ 
init {
	
	Load();
    combo_run(Boot);
}
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                       Main                         ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/ 
main {

    if (BlockRumble) {
    
    	block_rumble();
    }

	if (PlayStationOnXbox) {
		
		if (get_console() == PIO_XB360 || get_console() == PIO_XB1) {
			
			if (get_controller() == PIO_PS4 || get_controller() == PIO_PS5) { 
	
				swap(PS4_TOUCH,PS4_SHARE);
			}
		}
    }

   	if (HairTriggers) { 
   		
   		deadzone(PS4_L2,PS4_R2,100,100);
   	}
   	
	if (QuickEditAssist) {
	 	
	 	if (get_ival(EditAssist)) {
			
			EditAssist();
			Edit1 = On;
		}

		if (event_release(EditAssist)) {
			
			EditT = Off;
			Edit1 = Off;
		}
	}

    if (InstaFastReset) {
    	
    	if (!get_ival(Ads) && !get_ival(BlockShotHold) && event_press(FastReset)) { 
        
        	Shunt = FastReset;
        	combo_run(FastReset);
        }
    }

	if (!MainMenu && BuildTracker) {
            
    	if (event_press(Build)) {
                
        	BuildTrack = !BuildTrack;
        }
        
        if (BuildTrack) {
            
            BuildCount += get_rtime();
            
            if (event_press(Pickaxe) || BuildCount == BuildSyncTime) {
                
                BuildTrack = Off;
                BuildCount = Off;
 			}
            
            if (get_ival(Roof) || get_ival(Floor) || get_ival(Ramp) || get_ival(Wall) || get_ival(Trap) || combo_running(RampFloorWall) || combo_running(FloorPyramid)) BuildCount = Off;
	    
	        if (!Edit1) {
        	
        	    if (RampFloorWall) {
                	
                	if (event_press(RampFloorWallPress)) {
			    	
			    		if (BlockPressRFW) {
			    			
			    			Shunt = RampFloorWallPress;
			    		}
			    		combo_run(RampFloorWall);
			    	}
			    }
			    
			    if (FloorPyramid) {
			    
			    	if (event_press(Floor) && get_brtime(Floor) <= FloorRoofTapTime) {
			    	
			    		FloorRoof = On;
			    		combo_run(FloorPyramid);
			    	}
			    	
			    	if (FloorRoof && get_ival(Floor)) {
			    	
			    		combo_run(FloorPyramid);
			   		
			   		} else {
			   			
			   			FloorRoof = Off;
			   		}
			   	}
			}
	    } 
	}
	
	if (!BuildTrack && !Edit1) {
    
	    if (!MainMenu && InstantPickaxe) {
	        
	        if (event_press(Pickaxe)) 
	        	
	        	Axe = On;
	        
	        if (Axe) {
	        	
	        	if (event_press(Build) || event_press(Ads) || event_press(N_Weapon) || event_press(P_Weapon)) 
	            	
	            	Axe = Off;
	                    
	            if (event_release(Fire))
	            	
	            	combo_run(Pickaxe);
	        }
	    }
        
        if (DoGzPolarEffectsV2) {
        
        	if (Effect <= 40) {
        
        		if (!get_ival(Ads) && event_press(Fire) ||  !get_ival(Fire) && event_press(Ads)) {
			
					if (Effect == 19 || Effect == 20 || Effect == 21 || Effect == 22) Angle = Center;
				}
        
        		if (get_ival(Ads) || PolarHipFire && get_ival(Fire)) {
        		
        			if (get_ival(Fire)) {
        		
        				if (BoostPower) ActualRadius = AssistPowerBoost;  
        				if (BoostSpeed) ActualSpeed = AssistSpeedBoost; 
        		
        			} else {
        		
        				ActualRadius = AssistPower;
        				ActualSpeed = AssistSpeed;
        			}
    
    				Radius = clamp(((ActualRadius * (PolarMax + ActualRadius - isqrt(pow(get_ival(AimPX),Raise) + pow(get_ival(AimPY),Raise)))) / PolarMax),Off,ActualRadius);
        		
        			Effects(Effect);
        			DamSteadyPolar(Angle,((Radius) * PolarValue));
        		}
        	
        	} else if (Effect == 41 || Effect == 42) {
        		
        		if (get_ival(Ads) || PolarHipFire && get_ival(Fire)) {
            		
            		if (Effect == 41) {
        		
        				if (AssistPower >= 10) TimezPower = AssistPower / 10;
        			
        				else TimezPower = 10 / 10;
        			
        				TimezSpeed = 50;
        			}
            	
            		if (Effect == 42) {
        		
        				if (AssistPower >= 10) TimezPower = AssistPower / 10;
        			
        				else TimezPower = 10 / 10;
        			
        				TimezSpeed = 2;
        			}
            		
            		AimAssist();
            	}
            
            	if (!get_ival(Ads) && !get_ival(Fire)) {
            
            		AAT = 0;
            		Axis = 0;
            	}
        	}       	
        }

		if (AimAbuseMod) { 
		
            if (AdaptiveAbuse) {        
            	
            	AdaptAbuse = AdaptiveHold * 5 - isqrt(abs(get_ival(AimX)) * abs(get_ival(AimX)) + abs(get_ival(AimY)) * abs(get_ival(AimY)) * 5);
            	
                if (AbuseOnFire) {
                	
                	if (get_ival(Ads) >= AimAbuseLimit && get_ival(Fire)) {
                	
                		combo_run(AdaptiveAbuse);
                	
                	} else {
                            
                    	combo_stop(AdaptiveAbuse);
                	} 
                } else if (get_ival(Ads) >= AimAbuseLimit) {
                        
                    combo_run(AdaptiveAbuse);
                        
                } else {
                            
                    combo_stop(AdaptiveAbuse);
                }
            }
        }

		if (RapidFireMod) {
	
			if (RapidFire) {
	
				if (rfActivate == AnyFire) {
	                
	                if (get_ival(Fire)) {
	            
	                    combo_run(RapidFire);
	            
	                } 
	            } else if (rfActivate == AdsFire) {
	            	
	            	if (get_ival(Ads) && get_ival(Fire)) {
	            
	                	combo_run(RapidFire);    
	                }
	           	} else if (rfActivate == FireOnly) {
	            	
	            	if (!get_ival(Ads) && get_ival(Fire)) {
	            
	                	combo_run(RapidFire);    
	                }            
	            } else {
	                
	                combo_stop(RapidFire);
	            }
	            if (rfDeActivate) {
	            	
	            	if (event_release(Fire)) {
	            	
	            		RapidFireMod = Off;
	            	}
	            }
	        }
	    }
	
		if (DropShotMod) {
		
			if (DropShot) {
				
				if (dsActivate == AnyFire) {
	                
	                if (get_ival(Fire)) {
	            
	                    combo_run(DropShot);
	            
	                } 
	            } else if (dsActivate == AdsFire) {
	            	
	            	if (get_ival(Ads) && get_ival(Fire)) {
	            
	                	combo_run(DropShot);    
	                }
	           	} else if (dsActivate == FireOnly) {
	            	
	            	if (!get_ival(Ads) && get_ival(Fire)) {
	            
	                	combo_run(DropShot);    
	                }            
	            } else {
	                
	                combo_stop(DropShot);
	            }
	            if (dsDeActivate) {
	            	
	            	if (event_release(Fire)) {
	            	
	            		DropShotMod = Off;
	            	}
	        	}
	        }
	    }

		if (JumpShotMod) {
		
			if (JumpShot) {
				
				if (jsActivate == Press) {
	                
	                if (event_press(Fire)) {
	            
	                    set_val(Jump,100);
	            	} 
	            } else if (jsActivate == Hold) {
	            	
	            	if (get_ival(Fire)) {
	            
	                	combo_run(JumpShot);    
	                
	                } else {
	                
	                	combo_stop(DropShot);
	            
	            	}
	            } else if (jsActivate == Delayed) {
	            	
	            	if (event_press(Fire)) {
	            
	                	combo_run(JumpShotDelay);    
	                }            
	            
	            } 
	            if (jsDeActivate) {
	            	
	            	if (event_release(Fire)) {
	            	
	            		JumpShotMod = Off;
	            	}
	        	}
	        }
	    }

		if (StrafeShotMod) {

        	if (StrafeShot) {
        
                if (abs(get_ival(Strafe)) <= 35 && abs(get_ival(Walk)) <= 35) {
                
                	if (ssActivate == AnyFire) {
	                    
	                    if (get_ival(Fire)) {
	                
	                        combo_run(StrafeShot);
	                
	                    } 
	                } else if (ssActivate == AdsFire) {
	                	
	                	if (get_ival(Ads) && get_ival(Fire)) {
	                
	                    	combo_run(StrafeShot);    
	                    }
	               	} else if (ssActivate == FireOnly) {
	                	
	                	if (!get_ival(Ads) && get_ival(Fire)) {
	                
	                    	combo_run(StrafeShot);    
	                    }            
	                } else {
	                    
	                    combo_stop(StrafeShot);
	                }
        	    }
        	    
        	    if (jsDeActivate) {
	                	
	                if (event_release(Fire)) {
	                	
	                	JumpShotMod = Off;
	                }
	            }
	        }
    	}

	    if (AccuracyMod) {
		    
		    if (PerfectAccuracy) {
			
			    if (get_ival(PerfectAccuracyHold) && get_ival(PerfectAccuracyPress)) {
			    
			    	Shunt = PerfectAccuracyPress;
			    	combo_run(PerfectAccuracy);
			    
			    } else {
			    
			    	combo_stop(PerfectAccuracy);
			    }
			}
		}
		
	    if (GlobalMod) {
	    	
	    	if (PumpWall) { 
	    
	    		if (get_ival(PumpWallHold) && event_press(PumpWallPress)) {
            
            		Shunt = PumpWallPress;
            		combo_run(PumpWall);
            	}
            }
        	
	    	if (PumpRamp) {
	    
	    		if (get_ival(PumpRampHold) && event_press(PumpRampPress)) {
            
            		Shunt = PumpRampPress;
            		combo_run(PumpRamp);
            	}
            }

			if (BlockShot) {
	    
	    		if (get_ival(BlockShotHold) && event_press(BlockShotPress)) {
            
            		Shunt = BlockShotPress;
            		combo_run(BlockShot);
            	}
            }
	        
	        if (PumpSmg) {
            
                if (R2Block) {
                    
                    set_val(Fire,0);
                }
                
                if (event_press(Fire) && !Axe) {
                    
                    if (!Tap) {
                        
                        Tap = On;
                        combo_run(PumpSmg);
                        set_val(Fire,0);
                	}
                }
                
                if (event_press(N_Weapon)) {
                    
                    Tap = Off;
                }
                
                if (event_press(P_Weapon)) {
                    
                    Tap = On;
                }
            }
        	
        	if (WallTake) {
				
				if (get_ival(WallTakeBuildHold) && event_press(WallTakeBuildPress)) {
	                
	                Shunt = WallTakeBuildPress;
	                combo_run(WallTake);
	            }
			}
			
			if (WallTakeBuild) {
				
				if (get_ival(WallTakeHold) && event_press(WallTakePress)) {
	                
	                Shunt = WallTakeBuildPress;
	                combo_run(WallTakeBuild);
	            }
			}	    	
		}
		
		if (AntiRecoilMod) {
	    
	        if (SixthSenseRecoil) {
	        
	            if (event_press(Ads) || event_press(Fire)) {
	            	
	            	Start = On;
	            }
	            
	            if (get_ival(Ads) && get_ival(Fire)) {
	            	
	            	SixthSenseRecoil();
	            }
	            
	            if (event_release(Ads) || event_release(Fire)) {
	            	
	            	ActualVal = Off;
	            }
	        }
	        
	        if (!MainMenu && get_ival(RecoilHoldOnFly)) {
                
                if (event_press(RecoilDownPress)) {
                
                    AntiRecoilAdjust = clamp(AntiRecoilAdjust - RecoilValueSub,-50,50);
                    combo_restart(PrintValue);
                }
                
                if (event_press(RecoilUpPress)) {
                
                    AntiRecoilAdjust = clamp(AntiRecoilAdjust + RecoilValueAdd,-50,50);
                    combo_restart(PrintValue);
                }
                
                set_val(RecoilUpPress,0); set_val(RecoilDownPress,0);
            }
	    }
	} 

	if (!MainMenu && get_ival(MenuEnterHold) && event_press(MenuEnterPress)) {

        Shunt = MenuEnterPress;
        MenuToggle(On,Off,On,On,On);
        combo_run(Rumble);
        combo_stop(Saved);
        combo_stop(PrintValue);
    }

    if (MainMenu) {

        combo_stop(Boot);
        MenuTimeout += get_rtime();

        if (get_ival(Up) || get_ival(Down) || get_ival(Left) || get_ival(Right) || event_press(ChangeMenuPress) || event_press(MenuBackPress)) {

            MenuTimeout = Off;
        }

            if (MenuTimeout >= TimeoutLimitM || event_press(MenuExitPress)) {
            
                if (event_press(MenuExitPress)) Shunt = MenuExitPress;
                
                MenuToggle(Off,Off,Off,Off,Off);
                combo_run(Rumble);
                combo_run(Saved);
                Save();
                Angle = Off;
                DPTAngle = Off;
                Axis = Off;
            }

            if (MenuSwitch && event_press(ChangeMenuPress)) {

				Pointer = ModIndex;
				
				if (PointerValues[Pointer - 1][0]) {
                
                	MenuValues = On;
                	MenuSwitch = Off;
                	ModIndex = PointerValues[Pointer - 1][1];
                	Update = On;
                
                } else {
                
                	combo_run(Error);
                }    
            }
            
            if (MenuValues && event_press(MenuBackPress)) {
            
            	MenuSwitch = On;
            	MenuValues = Off;
                ModIndex = Pointer;
                Update = On;
            }	

        if (MenuValues) {
        	
        	
        	if (ModIndex == 9) {

        		ModDigit[ModIndex] = PrintMod(ModDigit[ModIndex],ModDigitVal[ModIndex - 1][0],ModDigitVal[ModIndex - 1][1],ModDigitVal[ModIndex - 1][2],PointerValues[Pointer - 1][1],PointerValues[Pointer - 1][2],ModValue[ModIndex - 1],ModRecoilHelp[ModDigit[ModIndex]]);    
        	
        	} else if (ModIndex == 24 || ModIndex == 27 || ModIndex == 30 || ModIndex == 33) {

        		ModDigit[ModIndex] = PrintMod(ModDigit[ModIndex],ModDigitVal[ModIndex - 1][0],ModDigitVal[ModIndex - 1][1],ModDigitVal[ModIndex - 1][2],PointerValues[Pointer - 1][1],PointerValues[Pointer - 1][2],ModValue[ModIndex - 1],ModActivateHelp[ModDigit[ModIndex]]);    
        
        	} else if (ModIndex == 25 || ModIndex == 28 || ModIndex == 31 || ModIndex == 34) {

        		ModDigit[ModIndex] = PrintMod(ModDigit[ModIndex],ModDigitVal[ModIndex - 1][0],ModDigitVal[ModIndex - 1][1],ModDigitVal[ModIndex - 1][2],PointerValues[Pointer - 1][1],PointerValues[Pointer - 1][2],ModValue[ModIndex - 1],ModDeActivateHelp[ModDigit[ModIndex]]);    
        
        	} else if (ModIndex == 30) {

        		ModDigit[ModIndex] = PrintMod(ModDigit[ModIndex],ModDigitVal[ModIndex - 1][0],ModDigitVal[ModIndex - 1][1],ModDigitVal[ModIndex - 1][2],PointerValues[Pointer - 1][1],PointerValues[Pointer - 1][2],ModValue[ModIndex - 1],ModJSActivateHelp[ModDigit[ModIndex]]);    
        
        	} else {
        
            	ModDigit[ModIndex] = PrintMod(ModDigit[ModIndex],ModDigitVal[ModIndex - 1][0],ModDigitVal[ModIndex - 1][1],ModDigitVal[ModIndex - 1][2],PointerValues[Pointer - 1][1],PointerValues[Pointer - 1][2],ModValue[ModIndex - 1],ModValueHelp[ModIndex - 1]);
			}
		}

        if (MenuSwitch) {
            
            ModSwitch[ModIndex] = PrintMod(ModSwitch[ModIndex],Off,On,1,IndexMinimum,ModSwitch,ModToggle[ModIndex - 1],ModToggleHelp[ModIndex - 1]);
        }    
		
		BlockButton(Down); BlockButton(Up); BlockButton(Left); BlockButton(Right); BlockButton(ChangeMenuPress); BlockButton(MenuBackPress);
	}
	
	if (!MainMenu) {
	
		if (SixthSenseRecoil && get_ival(AntiRecoilModHold) && event_press(AntiRecoilModPress)) {

    		Shunt = AntiRecoilModPress;
    		AntiRecoilMod = !AntiRecoilMod;
    		ToggleName = On;
    		Toggle(AntiRecoilMod,AntiRecoilL[0]);
		}
	
		if (RapidFire && get_ival(RapidFireModHold) && event_press(RapidFireModPress)) {

    		Shunt = RapidFireModPress;
    		RapidFireMod = !RapidFireMod;
    		ToggleName = On;
    		Toggle(RapidFireMod,RapidFireL[0]);
		}
		
		if (DropShot && get_ival(DropShotModHold) && event_press(DropShotModPress)) {
			
			if (BlockPressDS) Shunt = DropShotModPress;
    		DropShotMod = !DropShotMod;
    		ToggleName = On;
    		Toggle(DropShotMod,DropShotL[0]);
		}

		if (JumpShot && get_ival(JumpShotModHold) && event_press(JumpShotModPress)) {

    		if (BlockPressJS) Shunt = JumpShotModPress;
    		JumpShotMod = !JumpShotMod;
    		ToggleName = On;
    		Toggle(JumpShotMod,JumpShotL[0]);
		}
		
		if (StrafeShot && get_ival(StrafeShotModHold) && event_press(StrafeShotModPress)) {

    		Shunt = StrafeShotModPress;
    		StrafeShotMod = !StrafeShotMod;
    		ToggleName = On;
    		Toggle(StrafeShotMod,StrafeShotL[0]);
		}
		
		if (PerfectAccuracy && get_ival(AccuracyModHold) && event_press(AccuracyModPress)) {

    		Shunt = AccuracyModPress;
    		AccuracyMod = !AccuracyMod;
    		ToggleName = On;
    		Toggle(AccuracyMod,AccuracyL[0]);
		}
		
		if (AdaptiveAbuse && get_ival(AimAbuseModHold) && event_press(AimAbuseModPress)) {

    		Shunt = AimAbuseModPress;
    		AimAbuseMod = !AimAbuseMod;
    		ToggleName = On;
    		Toggle(AimAbuseMod,AimAbuseModL[0]);
		}
		
		if (PumpWall || PumpRamp || BlockShot || PumpSmg || WallTake || WallTakeBuild) {
			
			if (get_ival(GlobalModHold) && event_press(GlobalModPress)) {

    			Shunt = GlobalModPress;
    			GlobalMod = !GlobalMod;
    			ToggleName = On;
    			Toggle(GlobalMod,GlobalModL[0]);
    		}
		}
	}
	
	if (!SixthSenseRecoil || SixthSenseRecoil && !AntiRecoilMod) {
		
		YAxis = Off;
	}
    
    if (ToggleTime) {
        
        TimeToggle();
    }

    if (Shunt) {
        
        if (event_release(Shunt)) {
            
            Shunt = Off;
        
        } else {
            
            set_val(Shunt,Off);
        }
    }
}
/* 
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                   Combos                           ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/
combo AdaptiveAbuse {

    set_val(Ads,100);
    wait(AdaptAbuse);
    set_val(Ads,0);
    wait(AdaptiveRelease);
}

combo RapidFire {

    set_val(Fire,100);
    wait(RapidFireHold);
    set_val(Fire,0);
    wait(RapidFireWait);
}

combo DropShot {

    set_val(Crouch,100);
    wait(50);
    set_val(Crouch,0);
    wait(20);
    wait(DropShotWait);
}

combo JumpShotDelay {
          
    wait(JumpShotTime);
    set_val(Jump,100);       
    wait(50);
}

combo StrafeShot {

    set_val(Strafe,-100);
    wait(StrafeShotWait);
    set_val(Strafe,100);
    wait(StrafeShotWait);
}

combo JumpShot {
    set_val(Jump,100);      
    wait(50);               
    set_val(Jump,0);       
    wait(20);
    wait(JumpShotTime);
}

combo PerfectAccuracy {
	
	set_val(Fire,100);
	wait(50);
	wait(AccuracyTime);
}

combo Pickaxe {
    
    set_val(Pickaxe,100);
    wait(30);
    Axe = Off;
}

combo PumpSmg {
    
    R2Block = On;
    set_val(Fire,100);
    wait(PumpSmgTime);
    wait(5);
    set_val(Ads,100);
    set_val(P_Weapon,100);
    wait(PumpSmgTime);
    R2Block = Off;
}

combo PumpWall {

    set_val(Fire,100);
    wait(PumpWallTime);
 	set_val(Build,100);
    wait(PumpWallTime);
    set_val(Wall,100);
    wait(PumpWallTime);
    set_val(Build,100);
    wait(50);
    wait(30);
}

combo PumpRamp {

    set_val(Fire,100);
    wait(PumpRampTime);
 	set_val(Build,100);
 	set_val(Ramp,0);
    wait(PumpRampTime);
    set_val(Ramp,100);
    wait(PumpRampTime);
    set_val(Build,100);
    wait(50);
    wait(30);
}

combo WallTake {
	wait(10);
	set_val(Fire,100);
	wait(WallTakeTime);
	set_val(Build,100);
	wait(WallTakeTime);
	set_val(Wall,100);
	wait(WallTakeTime);
	set_val(Build,100);
	wait(20);
}

combo WallTakeBuild {
	set_val(Build,100);
	set_val(WallTakeBuildPiece,0);
	wait(WallTakeTime);	
	set_val(WallTakeBuildPiece,100);
	set_val(Wall,0);
	wait(WallTakeTime);	
	set_val(WallTakeBuildPiece,0);
	set_val(Wall,100);
	wait(WallTakeTime);	
	set_val(Build,100);
	wait(WallTakeTime);
}

combo RampFloorWall {																													
	
	set_val(Ramp,100);
	wait(60);
	wait(30);
	set_val(Floor,100);
	wait(60);
	wait(30);
	set_val(Wall,100);
	wait(60);
	wait(40);
}

combo FloorPyramid {
	
	wait(20);
	set_val(Roof,100);
	wait(50);
	set_val(Floor,0);
	
	if (FloorRoof) {
		
		combo_run(FloorPyramid);
	}
}

combo BlockShot {

    set_val(Fire,100);
    wait(BlockShotTime);
 	set_val(Build,100);
    wait(BlockShotTime);
    set_val(Wall,100);
    wait(30);
    set_val(Floor,100);
    wait(30);
    set_val(Wall,100);
    wait(30);
    set_val(Floor,100);
    wait(30);    
    set_val(Build,100);
    wait(BlockShotTime);
    wait(30);
}

combo FastReset {

    set_val(EditAssist,100);
    wait(30)
    set_val(Reset,100);        
    wait(30);
}

combo Rumble {
    
    set_rumble(RUMBLE_B,15);
    wait(250);
    reset_rumble();
}

combo PrintValue {
    
    if (combo_running(Boot))    
        
        combo_stop(Boot);
    
    if (combo_running(Saved))    
        
        combo_stop(Saved);
        
	combo_restart(Rumble);
	cls_oled(0);
	image_oled(0,0,1,1,MenuAABorderPage[0]);
	String(MiscString[13],wSmall,tLine1 - 5,fSmall,nCenter);
	String(MiscString[14],wSmall,tLine4 + 3,fSmall,nCenter);
	FindDigit(AntiRecoilAdjust,DigitValue(AntiRecoilAdjust),mvLine2 - 3);
	wait(10);
    wait(10000);
    cls_oled(0);	
}

combo Boot {
        
	cls_oled(0);
	String(MiscString[2],wSmall,tLine1,fSmall,nCenter);
	String(MiscString[3],wSmall,tLine2,fSmall,nCenter); 
	String(MiscString[11],wSmall,tLine3,fSmall,nCenter);
	String(MiscString[12],wSmall,tLine4,fSmall,nCenter);
	wait(10);
    wait(20000);
    cls_oled(0);
}

combo Error {
	
	combo_run(Rumble);
	wait(10);
    image_oled(0,0,1,1,MenuAABorderPage[0]);
    print(CenterPosition(StringLength(MiscString[8]),11),5,1,1,MiscString[8]);
    print(CenterPosition(StringLength(MiscString[9]),11),24,1,1,MiscString[9]);
    print(CenterPosition(StringLength(MiscString[10]),11),44,1,1,MiscString[10]);
    wait(20);
    wait(1000);
    cls_oled(0);
    Update = On;
}

combo VibrateBlink {
    
    if (Modled) {
        
        set_rgb(0,255,0);
        set_rumble(RUMBLE_A,15);
    
    } else {
        
        set_rgb(255,0,0);
        set_rumble(RUMBLE_B,15);
    }
    
    wait(150);
    set_rgb(0,0,0);
    wait(250);
    
    if (Modled)
        
        set_rgb(0,255,0);
    
        else
            
        set_rgb(255,0,0);

    reset_rumble();
    reset_leds();
}

combo Saved {

	wait(10);
    image_oled(0,0,1,1,MenuAABorderPage[0]);
    print(CenterPosition(StringLength(MiscString[5]),11),5,1,1,MiscString[5]);
    print(CenterPosition(StringLength(MiscString[6]),11),24,1,1,MiscString[6]);
    print(CenterPosition(StringLength(MiscString[7]),11),44,1,1,MiscString[7]);
    wait(20);
    set_rgb(125,0,125);
    wait(3000);
    cls_oled(0);
    reset_leds();
}
/* 
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                   Functions                        ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/ 
function EditAssist () {

    EditT += get_rtime();
    
    if (get_ival(EditAssist)) {
    	
    	if (EditT >= 30) Press(Select);
    }
}

function Press (Button) {

    if (get_ival(Button)) return;
    
    set_val(Button, 100);
}

function SixthSenseRecoil() {
   
	if (Start) {
		
    	vTime = Off;
    	MoveY = Off;
    	MovementY = Off;
    	vP = 10;
    	vT = 16;
	    Start = Off;
	}
	
	if (AntiRecoilMode == RecoilSet) {
	
	    YAxis = ModDigit[vP];
    
	    if (vTime >= ModDigit[vT]) {
	    
	        if (get_ival(Ads) && get_ival(Fire)) {
	        	
	        	if (vT == 21 && vTime >= ModDigit[vT]) {
	        		vP = 9;
	        		vT = 15;
	        	}
	        }
	        
	        vTime = Off;
	        vP = clamp(vP++,10,15);
	        vT = clamp(vT++,16,21);
	    }		
	}
	
	if (AntiRecoilMode == Progression) {
	
		if (!MovementY) {
	
			if (!MoveY)	
				
				YAxis = ModDigit[vP];
	
			if (vTime >= ModDigit[vT]) {
				
				MoveY = On;
		
			if (ModDigit[vP] > ModDigit[vP + 1]) 
				
				YAxis --;
			
			if (ModDigit[vP] < ModDigit[vP + 1]) 
				
				YAxis ++;
				vTime = Off;
		}
		
		if (YAxis == ModDigit[vP + 1]) {
			
			MoveY = Off;
			vP = clamp(vP++,10,15);
	        vT = clamp(vT++,16,21);
		}
			else if (vP == 15) {
				
				MovementY = On;
			}
		}
	}
	
	ActualVal = clamp(YAxis + AntiRecoilAdjust,0,100);
	
	if (!DoGzPolarEffectsV2) {
	    	
		if (InvertedYAxis) {

			set_Val(AimY,inv(ActualVal));
	    	
	    } else {
	
			set_Val(AimY,ActualVal);
	   	}
	} else {
        
        ActualVal = ActualVal;
    }
    
    vTime += get_rtime();
}

function set_Val(Input,Output) {

      set_val(Input,clamp(Output * (100 - abs(get_ival(Input))) / 100 + get_ival(Input),-100,100));
        
      return; 
}

function AimAssist() {

	AAT += get_rtime();
			
	DPTAngle = (DPTAngle + TimezSpeed);
			
	if (!Axis) 
		
		DamSteadyPolar(DPTAngle,(AAT * TimezPower / 10 * 2 * 327));
		
	if (Axis == 1)  
		
		DamSteadyPolar(DPTAngle,(AAT * TimezPower / 10 * 2 * 327));
	
	if (Axis == 2)  
		
		DamSteadyPolar(DPTAngle,inv(AAT * TimezPower / 10 * 2 * 327));

	if (Axis == 3) {
		
		DamSteadyPolar(DPTAngle,inv(AAT * TimezPower / 10 * 2 * 327));
	
		if (AAT > 50) {
		
			AAT = Off;
			Axis = Off;
		}
	
	} else if (AAT > 50) {
		
		AAT = Off;
		Axis ++;
	}
}

function Effects(Num) {
	
	if (!Num || !Increment) Angle = ((Angle + ActualSpeed));
	
	if (!Num) {
		
		Increment = Off; Shape = Off; AxisX = Off; AxisY = Off; IndexRY = Off;
	}
	
	if (Num > 0 && Num <= 3) {
		
		Increment = Off; Shape = Off; AxisX = Off; AxisY = Off;	
		IndexRY++;
		
		if (Num == 1 && IndexRY >= 2 || Num == 2 && IndexRY >= 3 || Num == 3 && IndexRY >= 4) {
		
			IndexRY = Off;
		}
	}
	
	if (Num == 4) {
		
		Increment = Off; Shape = Off; IndexRY = Off; AxisY = Off; AxisX = AssistValues[33];
	}
	
	if (Num == 5) {
	
		Increment = Off; Shape = Off; AxisY = Off; IndexRY = Off; AxisX = AssistValues[34];
	}
	
	if (Num == 6) {
	
		Increment = Off; Shape = Off; IndexRY = Off; AxisX = Off; AxisY = AssistValues[33];
	}	
	
	if (Num == 7) {
	
		Increment = Off; Shape = Off; IndexRY = Off; AxisX = Off; AxisY = AssistValues[34];
	}
	
	if (Num > 7 && Num <= 10) {
		
		Increment = Off; Shape = Off; IndexRY = Off; AxisX = Off;
		AxisY++;
		
		if (Num == 8 && AxisY >= 2 || Num == 9 && AxisY >= 3) {
			
			AxisY = Off;
		
		} else if (Num == 10 && AxisY >= 3) {
			
			AxisY = AssistValues[33];
		} 
	}
	
	if (Num > 10 && Num <= 13) {
		
		Increment = Off; Shape = Off; IndexRY = Off; AxisY = Off;
		AxisX++;
		
		if (Num == 11 && AxisX >= 2 || Num == 12 && AxisX >= 3) {
			
			AxisX = Off;
		
		} else if (Num == 13 && AxisX >= 3) {
			
			AxisX = AssistValues[33];
		} 
	}
	
	if (Num == 14) {
	    
	    Increment = Off; Shape = Off;
	    if (Angle >= 360) Switcher = !Switcher;
	    
	    if (Switcher) { 
	    	 		
	    	 AxisX = AssistValues[34]; AxisY = AssistValues[33];
	    	 	
	    } else { 
	    	  		
	    	 AxisX = AssistValues[33]; AxisY = AssistValues[34];
	    }
	}
	
	if (Num == 15) {

		Increment = Off; Shape = AssistValues[36]; AxisX = Off; AxisY = Off;
		IndexRY = Off;
	}
	
	if (Num == 16) {

		Increment = Off; Shape = AssistValues[37]; AxisX = Off; AxisY = Off;
		IndexRY = Off;
	}	
	
	if (Num == 17) {

		Increment = Off; Shape = AssistValues[33]; AxisX = Off; AxisY = Off;
		IndexRY = Off;
	}
	
	if (Num == 18) {
	    
	    Increment = Off; Shape = AssistValues[33];
	    if (Angle >= 360) Switcher = !Switcher;
	    
	    if (Switcher) { 
	    	 		
	    	 AxisX = AssistValues[34]; AxisY = AssistValues[33];
	    	 	
	    } else { 
	    	  		
	    	 AxisX = AssistValues[33]; AxisY = AssistValues[34];
	    }
	}
	
	if (Num == 19) {
	
		Increment = AssistValues[33]; Shape = Off; IndexRY = Off; AxisY = Off; AxisX = Off;
		
		if (Angle >= High) Switcher = Off;
		if (Angle <= Center) Switcher = AssistValues[33];
		
		if (Switcher) {
		
			Angle += ActualSpeed % High;
		
		} else {
			
			Angle -= ActualSpeed % Center;
		}
	}

	if (Num == 20) {
	
		Increment = AssistValues[33]; Shape = Off; IndexRY = Off; AxisY = Off; AxisX = Off;
		
		if (Angle >= Center) Switcher = Off;
		if (Angle <= Low) Switcher = AssistValues[33];
		
		if (Switcher) {
		
			Angle += ActualSpeed % Low;
		
		} else {
			
			Angle -= ActualSpeed % Center;
		}
	}
	
	if (Num == 21) {
	
		Increment = AssistValues[33]; Shape = AssistValues[33]; IndexRY = Off; AxisY = Off; AxisX = Off;
		
		if (Angle >= High) Switcher = Off;
		if (Angle <= Center) Switcher = AssistValues[33];
		
		if (Switcher) {
		
			Angle += ActualSpeed % High;
		
		} else {
			
			Angle -= ActualSpeed % Center;
		}
	}
	
	if (Num == 22) {
	
		Increment = AssistValues[33]; Shape = AssistValues[33]; IndexRY = Off; AxisY = Off; AxisX = Off;
		
		if (Angle >= Center) Switcher = Off;
		if (Angle <= Low) Switcher = AssistValues[33];
		
		if (Switcher) {
		
			Angle += ActualSpeed % Low;
		
		} else {
			
			Angle -= ActualSpeed % Center;
		}
	}
	
	if (Num > 22 && Num <= 29) {
	
		Increment = AssistValues[33]; IndexRY = Off;  AxisY = Off; AxisX = Off;
		Angle += ActualSpeed % High; 
		IndexX++;
		
		if (Num > 22 && Num <= 26) Shape = AssistValues[33];
		else Shape = Off;
		
		if (IndexX == 1) {
			
			if (Num == 23) { AdjustY = AssistValues[0]; AdjustX = Off; }
			if (Num == 24) { AdjustY = AssistValues[1]; AdjustX = Off; }
			if (Num == 25) { AdjustY = AssistValues[2]; AdjustX = Off; }
			if (Num == 26) { AdjustY = AssistValues[3]; AdjustX = Off; }
			if (Num == 27) { AdjustY = AssistValues[4]; AdjustX = Off; }
			if (Num == 28) { AdjustY = AssistValues[5]; AdjustX = Off; }
			if (Num == 29) { AdjustY = AssistValues[3]; AdjustX = Off; }
		}
		
		if (IndexX == 2) {
			
			if (Num == 23) { AdjustY = Off; AdjustX = AssistValues[0]; }
			if (Num == 24) { AdjustY = Off; AdjustX = AssistValues[1]; }
			if (Num == 25) { AdjustY = Off; AdjustX = AssistValues[2]; }
			if (Num == 26) { AdjustY = Off; AdjustX = AssistValues[3]; }
			if (Num == 27) { AdjustY = Off; AdjustX = AssistValues[4]; }
			if (Num == 28) { AdjustY = Off; AdjustX = AssistValues[5]; }
			if (Num == 29) { AdjustY = Off; AdjustX = AssistValues[3]; }			
		}
		
		if (IndexX >= 3) IndexX = Off;		
	}
	
	if (Num >= 30 && Num <= 33) {
	
		Increment = AssistValues[33]; IndexRY = Off;  AxisY = Off; AxisX = Off;
		Angle += ActualSpeed % High; 
		IndexX++;
		
		if (Num == 30) Shape = Off;
		else Shape = AssistValues[33];
		
		if (IndexX == 1) {
		
			if (Num == 30) { AdjustY = AssistValues[6]; AdjustX = AssistValues[7]; }
			if (Num == 31) { AdjustY = AssistValues[10]; AdjustX = AssistValues[11]; }
			if (Num == 32) { AdjustY = AssistValues[14]; AdjustX = AssistValues[15]; }
			if (Num == 33) { AdjustY = AssistValues[18]; AdjustX = AssistValues[19]; }
			
		}
		
		if (IndexX == 2) {

			if (Num == 30) { AdjustY = AssistValues[7]; AdjustX = AssistValues[6]; }
			if (Num == 31) { AdjustY = AssistValues[12]; AdjustX = AssistValues[13]; }
			if (Num == 32) { AdjustY = AssistValues[16]; AdjustX = AssistValues[17]; }
			if (Num == 33) { AdjustY = AssistValues[20]; AdjustX = AssistValues[21]; }
		}
		
		if (IndexX == 3) {
		
			if (Num == 30) { AdjustY = AssistValues[8]; AdjustX = AssistValues[9]; }
			if (Num == 31) { AdjustY = AssistValues[13]; AdjustX = AssistValues[12]; }
			if (Num == 32) { AdjustY = AssistValues[17]; AdjustX = AssistValues[16]; }
			if (Num == 33) { AdjustY = AssistValues[21]; AdjustX = AssistValues[20]; }
		}
		
		if (IndexX == 4) {

			if (Num == 30) { AdjustY = AssistValues[9]; AdjustX = AssistValues[8]; }
			if (Num == 31) { AdjustY = AssistValues[11]; AdjustX = AssistValues[10]; }
			if (Num == 32) { AdjustY = AssistValues[15]; AdjustX = AssistValues[14]; }
			if (Num == 33) { AdjustY = AssistValues[19]; AdjustX = AssistValues[18]; }
		}			
		
		if (IndexX >= 5) IndexX = Off;
	}
	
	if (Num >= 34 && Num <= 40) {
	
		Increment = AssistValues[33]; IndexRY = Off;  AxisY = Off; AxisX = Off;
		Angle += ActualSpeed % High; 
			
		if (IndexX < 5) {
			
			if (Num == 34) { AdjustY += AssistValues[22]; AdjustX += AssistValues[23]; }
			if (Num == 35) { AdjustY += AssistValues[24]; AdjustX += Off; }
			if (Num == 36) { AdjustY += AssistValues[25]; AdjustX += Off; }
			if (Num == 37) { AdjustY += AssistValues[26]; AdjustX += AssistValues[27]; }
			if (Num == 38) { AdjustY += AssistValues[28]; AdjustX += AssistValues[29]; }
			if (Num == 39) { AdjustY += AssistValues[30]; AdjustX += Off; }
			if (Num == 40) { AdjustY += Off; AdjustX += AssistValues[24]; }
		}
		
		IndexX++;
		
		if (IndexX >= 5) IndexX = Off;
	}
}
					
function DamSteadyPolar(Angle,Radius) {
	
	Radius = Radius >> On;
	Radius = Radius * 4;
	set_val(AimPX,get_ival(AimPX) + inv(clamp(((Radius * Cos(Angle + AdjustX) >> (AxisX + IndexRY)) / (PolarRM) << On),PolarMin + get_ival(AimPX),PolarMax + get_ival(AimPX))));
	
	if (InvertedYAxis) {
		
		set_val(AimPY,get_ival(AimPY) + inv(ActualVal * PolarValue) + inv(clamp(((Radius * Sin(Angle + AdjustY) >> (AxisY + IndexRY)) / (PolarRM) << On),PolarMin + get_ival(AimPY),PolarMax + get_ival(AimPY) + inv(ActualVal * PolarValue))));
	
	} else {
		
		set_val(AimPY,get_ival(AimPY) + (ActualVal * PolarValue) + inv(clamp(((Radius * Sin(Angle + AdjustY) >> (AxisY + IndexRY)) / (PolarRM) << On),PolarMin + get_ival(AimPY) + (ActualVal * PolarValue),PolarMax + get_ival(AimPY))));
	}
}

function AngleCorrection(Angle) {
  
	if (Angle < Off) {
  		
  		Angle = PolarAngle + (Angle % PolarAngle);
  	}
  	
  	Angle = (Angle + PolarDirection) % PolarAngle;
  	
  	return Angle;
}

function Sin(Angle) {
  
  	if (!Shape || Shape == AssistValues[37]) {
  	
  		return Interpolation(CircleArray[(AngleCorrection(Angle - On))],CircleArray[(AngleCorrection(Angle + On))],inv(PolarMin));
  	
  	} else if (Shape == AssistValues[33] || Shape == AssistValues[36]) {
  	
  		return Interpolation(DiamondArray[(AngleCorrection(Angle - On))],DiamondArray[(AngleCorrection(Angle + On))],inv(PolarMin));
  	}
  	
  	return Off;
}

function Cos(Angle) {
  
	if (!Shape || Shape == 4) {
	
		return Interpolation(CircleArray[((AngleCorrection(Angle - On) + PolarDirection) % PolarAngle)],CircleArray[((AngleCorrection(Angle + On) + PolarDirection) % PolarAngle)],inv(PolarMin));
  
  	} else if (Shape == AssistValues[33] || Shape == 5) {
  
  		return Interpolation(DiamondArray[((AngleCorrection(Angle - On) + PolarDirection) % PolarAngle)],DiamondArray[((AngleCorrection(Angle + On) + PolarDirection) % PolarAngle)],inv(PolarMin));
  	}
  	
  	return Off;
}

function Interpolation(Negative,Positive,Value) {  

	return Shift(Lerp(Multiply(Negative),Multiply(Positive),Value)); 
}

function Multiply(Value) { 

	return Value * PolarRH; 
}

function Shift(Value) { 

	if (Value >= Off) {
	
		return (Value + PolarRH >> On) / PolarRH; 
	}
	
	return (Value - PolarRH >> On) / PolarRH; 
}

function FixMultiply(Value1,Value2) { 

	return ((Value1 >> PolarShift) * (Value2 >> PolarShift)); 
}

function Lerp(Negative,Positive,Value) { 
	
	return FixMultiply((PolarRH - Value),Negative) + FixMultiply(Value,Positive); 
}

function PrintMod(ModVal,ModValMin,ModValMax,ModValInc,IndexMin,IndexMax,ModValLbl,ModHlp) { 

    if (Update) { 
 		
 		rect_oled(2,2,127,61,1,0); 
        Grid(); 
		String(ModValLbl,wSmall,mvLine1,fSmall,nCenter);
		String(ModHlp,wSmall,mvLine3,fSmall,nCenter);
		
		if (MenuValues) {
		
			if (ModIndex == 1) {
				
				if (ModVal == 2 || ModVal == 3 || ModVal == 11 || ModVal == 13 || ModVal == 16 || ModVal == 17 || ModVal == 23 || ModVal == 28 || ModVal == 29 || ModVal == 31 || ModVal == 36 || ModVal == 39) {
				
					String(ModAssist[ModVal],wSmall,mvLine2 + 3,fSmall,nCenter);
				
				} else {
					
					String(ModAssist[ModVal],wMedium,mvLine2,fMedium,nCenter);
				}                                                             
			
			} else if (ModIndex >= 6 && ModIndex <= 8) { 
                                                                              
				String(MiscString[ModVal],wMedium,mvLine2,fMedium,nCenter);  
                                                                              
			} else if (ModIndex == 9) { 
                                                                              
				String(ModRecoil[ModVal],wMedium,mvLine2,fMedium,nCenter);  
                                                                              
        	} else if (ModIndex == 24 || ModIndex == 27 || ModIndex == 30 || ModIndex == 33) {                    
                                                                              
				String(ModActivate[ModVal],wMedium,mvLine2,fMedium,nCenter);  
                                                                              
        	} else if (ModIndex == 25 || ModIndex == 28 || ModIndex == 31 || ModIndex == 34) {                    
                                                                              
				String(ModDeActivate[ModVal],wMedium,mvLine2,fMedium,nCenter);  
                                                                              
        	} else if (ModIndex == 30 ) {                    
                                                                              
				String(ModJSActivate[ModVal],wMedium,mvLine2,fMedium,nCenter);  
                                                                              
        	} else {                                                          
                                                                              
            	FindDigit(ModVal,DigitValue(ModVal),mvLine2);
        	}
        } if (MenuSwitch) String(MiscString[ModVal],wMedium,mvLine2,fMedium,nCenter);
		             
        Update = Off; 
    } 

    if (event_press(Up) || get_ival (Up) && get_ptime (Up) >= 450) {
 
        Update = On;
        ModVal += ModValInc;

        if (ModVal > ModValMax) ModVal = ModValMin;
        
        if (MenuSwitch) {
		
			Modled = ModVal;
			combo_run(VibrateBlink);
		}
    } 
 
    if (event_press(Down) || get_ival (Down) && get_ptime (Down) >= 450) { 

        Update = On;
        ModVal -= ModValInc;

        if (ModVal < ModValMin) ModVal = ModValMax;
        
        if (MenuSwitch) {
		
			Modled = ModVal;
			combo_run(VibrateBlink);
		}
    }
	
	if (event_press(Right)) {

		Update = On;
		ModIndex += 1;
 
		if (ModIndex > IndexMax) ModIndex = IndexMin;
	}
  
	if (event_press(Left)) {
		
		Update = On;
		ModIndex -= 1; 
 
		if (ModIndex < IndexMin) ModIndex = IndexMax;
	} 
	
	return ModVal;
} 

function Toggle(ModT,ModP) {
    
    if (combo_running(Boot))    
        
        combo_stop(Boot);
    
    if (combo_running(Saved))    
        
        combo_stop(Saved);

    if (combo_running(PrintValue))

        combo_stop(PrintValue);
        
        Modled = ModT;
        ToggleT = 0;
        ToggleTime = On;
        combo_run(VibrateBlink);
    
    if (ToggleName) {
        
        cls_oled(0);
        String(MiscString[4],wSmall,qtLine1,fSmall,nCenter);
        rect_oled(0,19,128,23,0,1);    
        rect_oled(2,21,124,19,0,1);
        rect_oled(0,40,128,23,0,1);
        rect_oled(2,42,124,19,0,1);
        String(ModP,wSmall,qtLine2,fSmall,nCenter);
  		String(MiscString[ModT],wSmall,qtLine3,fSmall,nCenter);    
               
        ToggleName = Off;
    }
}

function TimeToggle() {
        
    ToggleT += get_rtime();
    
    if (!MainMenu && ToggleT >= 7000) {
        
        cls_oled(0);
        ToggleTime = Off;
        ToggleT = 0;
    }
}

function BlockButton(Button) { 

	if (!get_ival(Button))
		
		return; 
	
	set_val(Button,Off);
} 
  
function MenuToggle(Main,Vals,Indx,Print,Toggle) { 

	cls_oled(0); 
	MainMenu    = Main; 
	MenuValues  = Vals;
	ModIndex    = Indx;
	Update      = Print;
	MenuSwitch  = Toggle;
	MenuTimeout = Off;
}
     
function Grid() { 
  
	line_oled(126,20,2,20,2,1);
	rect_oled(1,1,127,63,0,1); 
	line_oled(126,46,2,46,2,1);
}

function String(label,width,line,font,offset) {
	
	if (offset) line = (64 - line) / 2;
	print(CenterPosition(StringLength(label),width),line,font,pWhite,label);
}

function StringLength(CharCount) { 
    
    StringLength = 0;
    
    do { 
        
        CharCount++;
        StringLength++;
    } 
    
    while (duint8(CharCount));
    
    return StringLength;
 } 

function CenterPosition(Char,Font) {

    return (128 / 2) - ((Char * Font) / 2);
}

function FindDigit(Val,Digit,Line) {
    
    BufferIndex = 1;  
    DigitIndex = 10000;
    
    if (Val < 0) {
         
         putc_oled(BufferIndex,45);
         BufferIndex += 1;
         Val = abs(Val);
    }
    
    for (CharIndex = 5;CharIndex >= 1;CharIndex--) {
        
        if (Digit >= CharIndex) {
            
            putc_oled(BufferIndex,(Val / DigitIndex) + 48);
            Val %= DigitIndex;
            BufferIndex ++; 
       }
        
        DigitIndex /= 10;
    } 
    
    puts_oled(CenterPosition(BufferIndex - 1,11),Line,1,BufferIndex - 1,1);
} 

function DigitValue(Num) {
   
   LogVal = 0;
   
   do {
      
      Num /= 10;
      LogVal++;
   } 
   
   while (Num);
   
   return LogVal;
}

// 🆂🆆🅸🆉🆉🆈❜🆂 🆂🅿🆅🅰🆁 🆂🅰🆅🅴🆂 🅱🅴🅻🅾🆆 🅳🅾 🅽🅾🆃 🆃🅾🆄🅲🅷 
function Load(){ 
    
    ResetSpvar();
    
    if (ReadSpvar(0,1,0)) {    
        
        for (SpvarIndex = IndexMinimum; SpvarIndex <= ModDigit; SpvarIndex++) {
            
            ModDigit[SpvarIndex] = ReadSpvar(ModDigitVal[SpvarIndex - 1][0],ModDigitVal[SpvarIndex - 1][1],ModDigit[SpvarIndex]);
        }
        
        for (SpvarIndex = IndexMinimum; SpvarIndex <= ModSwitch; SpvarIndex++) {
            
            ModSwitch[SpvarIndex] = ReadSpvar(ModSwitchVal[SpvarIndex - 1][0],ModSwitchVal[SpvarIndex - 1][1],ModSwitch[SpvarIndex]);
        }
	}
} 	
	
function Save(){ 

	ResetSpvar();
    SaveSpvar(1,0,1,FALSE);
    
    for (SpvarIndex = IndexMinimum; SpvarIndex <= ModDigit; SpvarIndex++) {
        
        SaveSpvar(ModDigit[SpvarIndex],ModDigitVal[SpvarIndex - 1][0],ModDigitVal[SpvarIndex - 1][1],FALSE);
    }
    
    for (SpvarIndex = IndexMinimum; SpvarIndex <= ModSwitch; SpvarIndex++) {
        
        SaveSpvar(ModSwitch[SpvarIndex],ModSwitchVal[SpvarIndex - 1][0],ModSwitchVal[SpvarIndex - 1][1],FALSE);
    }
    	
	SaveSpvar(ModSwitch[SpvarIndex],ModSwitchVal[SpvarIndex - 1][0],ModSwitchVal[SpvarIndex - 1][1],TRUE);
} 

// 🆂🆆🅸🆉🆉🆈❜🆂 🆂🅿🆅🅰🆁 🆂🅰🆅🅴🆂 🅱🅴🅻🅾🆆 🅳🅾 🅽🅾🆃 🆃🅾🆄🅲🅷 
function ResetSpvar() {
	
	CurrentSlot = SPVAR_1;
	CurrentBit = 0;
	CurrentValue = 0;
}

function GetBitCount(Value) {

	if (Value < 0) {
		
		return GetBitCount(abs(Value + 1));
	}
	
	SpvarTemp = 0;
	
	do {
		
		SpvarTemp++;
		Value = Value >> 1;
		
	} while (Value);
	
	return SpvarTemp;
}

function GetBitCount2(Min,Max) {	
	
	SpvarTemp = max(GetBitCount(Min),GetBitCount(Max));
	
	if (IsSigned(Min,Max)) {
		
		SpvarTemp++;
	}
	
	return SpvarTemp;
}

function IsSigned(Min,Max) { 

	return Min < 0 || Max < 0; 
}

function MakeFullMask(Bits) {	
	
	if (Bits == MaxFWBits) {
		
		return FullBitmask;
	}
	
	return (FullBitmask & (~(1 << (MaxFWBits - 1)))) >> (MaxFWBits - Bits - 1);
}

function MakeSign(Bits) { 
	
	return 1 << clamp(Bits - 1,0,MaxFWBits - 1); 
}

function MakeSignMask(Bits) { 

	return ~MakeSign(Bits); 
}

function PackU(Value,Bits) { 

	return Value & MakeFullMask(Bits); 
}

function Pack(Value,Bits) {
	
	if (Value < 0) {
		
		return (abs(Value) & MakeSignMask(Bits)) | MakeSign(Bits);	
	}
	
	return Value & MakeSignMask(Bits);
}

function Unpack(Value,Bits) {
	
	if (Value & MakeSign(Bits)) {
		
		return 0 - (Value & MakeSignMask(Bits));
	}
	
	return Value & MakeSignMask(Bits);
}

function ReadSpvarSlot(Slot) { 

	return get_pvar(Slot,0-(FullBitmask & (1 << (MaxBits - 1))),0-((0-(FullBitmask & (1 << (MaxBits - 1)))) + 1),0); 
}

function SaveSpvar(Value,Min,Max,Save) {

	SpvarReset = ResetBits;
	SpvarBits = GetBitCount2(Min,Max);
	
	if (IsSigned(Min,Max) && SpvarReset == IndexMaximum) {
	
		Value = Pack(Value,SpvarBits);
	}
	
	Value = PackU(Value,SpvarBits);
	
	if (SpvarBits > MaxBits - CurrentBit) {
		
		CurrentValue = CurrentValue | (Value << CurrentBit);
		set_pvar(CurrentSlot,CurrentValue);	
		CurrentSlot++;
		SpvarBits -= (MaxBits - CurrentBit);
		Value = Value >> (MaxBits - CurrentBit);
		CurrentBit = 0;
		CurrentValue = 0;
	}
	
	CurrentValue = CurrentValue | (Value << CurrentBit);
	CurrentBit += SpvarBits;
	
	if (CurrentBit >= MaxBits) {
	
		CurrentBit -= MaxBits;
		set_pvar(CurrentSlot,CurrentValue);
		CurrentSlot++;
		
		if (!CurrentBit) {
			
			CurrentValue = 0;
		}
	}
	
	if (Save) {
	
		set_pvar(CurrentSlot,CurrentValue);
	}	
}

function ReadSpvar(Min,Max,default) {

	CurrentValue = ReadSpvarSlot(CurrentSlot);
	SpvarBits = GetBitCount2(Min,Max);
	CurrentValue = PackU(CurrentValue >> CurrentBit, SpvarBits);
	
	if (SpvarBits > MaxBits - CurrentBit) {
	
		CurrentSlot++;
		SpvarTemp = ReadSpvarSlot(CurrentSlot);
		SpvarTemp = PackU(SpvarTemp, SpvarBits - (MaxBits - CurrentBit));
		SpvarTemp = SpvarTemp << (MaxBits - CurrentBit);
		CurrentValue = PackU(CurrentValue, MaxBits - CurrentBit);
		CurrentValue = CurrentValue | SpvarTemp;
		CurrentBit = CurrentBit - MaxBits;
	}	
	
	CurrentBit += SpvarBits;
	
	if (CurrentBit >= MaxBits) {
	
		CurrentBit -= MaxBits;
		CurrentSlot++;
	}	
	
	CurrentValue = PackU(CurrentValue,SpvarBits);
	
	if (IsSigned(Min,Max)) {
		
		CurrentValue = Unpack(CurrentValue,SpvarBits);
	}
	
	if (CurrentValue < Min || CurrentValue > Max) {
	
		return default;
	}
	
	return CurrentValue;
}
/* 🆂🆆🅸🆉🆉🆈❜🆂 🆂🅿🆅🅰🆁 🆂🅰🆅🅴🆂 🅰🅱🅾🆅🅴 🅳🅾 🅽🅾🆃 🆃🅾🆄🅲🅷
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                   Menu Arrays                      )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
const string ModActivate [] = { 
	"FIRE ONLY",
 	"ADS + FIRE", 
 	"ANY FIRE"
};

const string ModActivateHelp [] = { 
	"Ads Blocks Mod", 
	"Reqs Ads + Fire",
	"Runs On Fire"
};

const string ModDeActivate[] = {
	"Toggle", 
	"Release" 
};

const string ModDeActivateHelp [] = { 
	"Manual Toggle Off", 
	"Release Fire Off"
};

const string ModRecoil[] = {
	"Recoil Set", 
	"Progression" 
};

const string ModRecoilHelp [] = { 
	"Repeats The Loop",
	"Stays On Last Val"
};

const string ModJSActivate [] = { 
	"TAP FIRE",
 	"HOLD FIRE", 
 	"DELAYED"
};

const string ModJSActivateHelp [] = { 
	"On Tap of Fire", 
	"Repeat On Fire",
	"Delay B4 Jump"
};

const string ModAssist [] = { 
    "Circle",
    "Concentric",
    "Concentric 2",
    "Concentric 3",
    "Portal",
    "Bullet",
    "Eye",
    "Tight Eye",
    "Trapped Eye",
    "Baseball",
    "RugbyBall",
    "Concentric Oval",
    "Beachball",
    "Nested Ovals",
    "React",
    "TearDrop",
    "Curved Diamond",
    "Diamond",
    "Star",
    "Rainbow",
    "Smiley",
    "Wings",
    "Pyramid",
    "Twin Diamonds",
    "Barrels",
    "Fat Cross",
    "Thin Cross",
    "Orbiter",
    "Rounded Fat Cross",
    "Rounded Cross",
    "Loop 2 Loop",
    "Cross Section",
    "Full Stack",
    "Teasure",
    "Galaxy",
    "Coiled",
    "Rocky Mountain",
    "Killbox",
    "Scanner",
    "Rollercoaster",
    "Spring",
    "Ninja Star",
    "Labyrinth"
};

const string ModToggle [] = { 
	"DoGzPolar Effects",
	"SixthSense Recoil",
	"Rapid Fire",  
	"Drop Shot", 
	"Jump Shot",
	"Strafe Shot",
	"Perfect Accuaracy", 
	"Instant Pickaxe", 						
	"Build Tracker", 
	"Edit Assist", 
	"Fast Reset", 
	"Wall Take",
	"Wall Take Build",
	"Pump Wall",
	"Pump Ramp",
	"Block Shot",	
	"Ramp Floor Wall",
	"Floor n Pyramid",
	"PS On Xbox",
	"Pump > Smg",
	"Aim Abuse"		
};

const string ModToggleHelp [] = {
	"Aim Assist Mod",            
	"Anti Recoil Mod",           
	"Turbo Fire",                
	"Turbo Crouch",              
	"Jump On Fire",              
	"Move Side 2 Side",          
	"Controlled Fire",           
	"Swap To Weapon",            
	"Isolate Mods",              
	"Holds Select",              
	"One Button Reset",          
	"Smash n Take",
	"Smash n Take +",
	"Pump Build Wall",
	"Pump Build Ramp",
	"Insta Defense CM",	
	"One Tap RFW In BM",
	"Spam Flr n Pyr BM ",
	"Touchpad Fix",
	"Pump Shot Swap",
	"Spam Ads Lockon"
};

const string ModValue [] = {                
	"Aim Assist Effect", 
	"Power", 
	"Speed", 
	"Boost Power", 
	"Boost Speed",
	"Power Boost",
	"Speed Boost",
	"Hip Fire AA",
	"Recoil Mode", 
	"Recoil Power One", 
	"Recoil Power Two", 
	"Recoil Power Three",
	"Recoil Power Four",
	"Recoil Power Five",
	"Recoil Power Six",
	"Recoil Time One",
	"Recoil Time Two",
	"Recoil Time Three",
	"Recoil Time Four",
	"Recoil Time Five",
	"Recoil Time Six",
	"Rapid Fire Hold", 
	"Rapid Fire Wait",
	"RF Activator",	
	"RF DeActivator",
	"Drop Shot Wait",
	"DS Activator",
	"DS DeActivator",
	"Jump Shot Time",
	"JS Activator",
	"JS DeActivator",
	"Strafe Shot Wait",
	"SS Activator",
	"SS DeActivator",
	"Accuaracy Delay", 
	"Build Sync Time", 
	"Wall Take Time",
	"Wall Take P Time",
	"Pump Wall Time",
	"Pump Ramp Time",
	"Block Shot Time",
	"Pump Smg Time",
	"Adaptive Hold",
	"Adaptive Release"	
};

const string ModValueHelp [] = { 
	"Aim Assist Shape",    
	"Radius On Ads",       
	"Angle On Ads",        
	"Radius On Fire",      
	"Angle On Fire",       
	"Boost On Fire",       
	"Boost On Fire",       
	"AA On Fire Btn",      
	"Recoil Set Method",   
	"Power Set Value 1",   
	"Power Set Value 2",   
	"Power Set Value 3",   
	"Power Set Value 4",   
	"Power Set Value 5",   
	"Power Set Value 6",   
	"Set Time Value 1",    
	"Set Time Value 2",    
	"Set Time Value 3",    
	"Set Time Value 4",    
	"Set Time Value 5",    
	"Set Time Value 6",    
	"RF Press Time",       
	"RF Release Time",     
	"Activates On..",      
	"DeActivates On..",    
	"Drop Shot Speed",     
	"Activates On..",      
	"DeActivates On..",    
	"Jump Shot Time",      
	"Activates On..",      
	"DeActivates On..",    
	"Strafe Shot Time",    
	"Activates On..",      
	"DeActivates On..",    
	"Control Fire Tap",    
	"Reset Build Mode", 
	"Tap & Delay Time",
	"Tap & Delay Time",
	"Tap & Delay Time",
	"Tap & Delay Time",
	"Tap & Delay Time",
	"Tap & Delay Time",
	"Aim Abuse Hold",
	"Aim Abuse Release"	
};

const string MiscString [] = { 
	"DISABLED",
	"ENABLED",
	"DoGzTheFiGhTeR's", 
	"Polar Effects", 
	"MOD TOGGLE", 
	"SETTINGS", 
	"SAVED", 
	"TO THE ZEN",
	"NO FURTHER",
	"VALUES FOR",
	"THIS MOD!",
	"Version 2.10",
	"FORTNITE",
	"RECOIL ADUSTER",
	"Overall Power +/-",
	"Yamicase"
};
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                   Toggle Strings                   )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
const string RapidFireL   = "RAPID FIRE";
const string DropShotL    = "DROP SHOT";
const string JumpShotL    = "JUMP SHOT";
const string StrafeShotL  = "STRAFE SHOT";
const string AccuracyL    = "PERFECT ACCURACY";
const string AntiRecoilL  = "ANTI-RECOIL";
const string AimAbuseModL = "AIM ABUSE";
const string GlobalModL   = "GLOBAL MODS";
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~.
(                 Script Variable                    )
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-*
*/
define TimeoutLimit  = 1;
define TimeoutLimitM = 30000;
define IndexMinActivator = 0;
define IndexMaxActivator= 2;
define IndexMinAssist = 0;
define IndexMaxAssist = 42;
define IndexMinimum = 1;
define IndexMaximum = 17;
define FloorRoofTapTime = 200; // Floor + Pyramid Double Tap Time(ms)

define pBlack = 0, pWhite = 1, wSmall = 7, wMedium = 11, wLarge = 17, fSmall = 0, fMedium = 1, fLarge = 2, hSmall = 10, hMedium = 18, hLarge = 26;
define mLine1 = -1, tLine1 = 11, tLine2 = 23, tLine3 = 35, tLine4 = 47,nCenter = 0, yCenter = 1, qtLine1 = 6,qtLine2 = 26,qtLine3 = 47, mvLine1 = 7, mvLine2 = 26, mvLine3 = 52;
define PolarAngle = 360,PolarDirection = 90,PolarValue = 327,AxisValue = -100,PolarShift = 8,PolarMax = 32767,PolarMin = -32768,Raise = 2,PolarLH = -65736,PolarRH = 65536,PolarLM = -23175,PolarRM = 23170;
define Low = 0, Center = 180, High = 360,RecoilSet = 0,Progression = 1,MaxBits = 32,MaxFWBits = 32,ResetBits = 17,FullBitmask = -1;

int SpvarIndex,CurrentBit,CurrentSlot,CurrentValue,SpvarTemp,SpvarReset,SpvarBits,BufferIndex,DigitIndex,CharIndex,LogVal,StringLength,IndexX,IndexRY,Switcher,AdjustX,AdjustY;
int Pointer,Update,MenuTimeout,MainMenu,ModIndex,MenuValues,MenuSwitch,Shunt,ToggleT,ToggleTime,ToggleName,Modled,Edit1,EditT,Axe,BuildTrack,BuildCount,FloorRoof,AdaptAbuse;
int Start,YAxis,MoveY,MovementY,vT,vP,vTime,TimezPower,TimezSpeed,Angle,DPTAngle,AAT,Axis,ActualRadius,ActualSpeed,Radius,AxisX,AxisY,Increment,Shape,R2Block,Tap,ActualVal;

const int16 CircleArray[] = { 
	23170,  23167,  23156,  23139,  23114,  23082,  23044,  22998,  22945,  22885,  22818,  22745,  22664,  22577,  22482,  22381,  22273,  22158,  22036,  21908,  21773,  21632,  21483,  21329,  21167,  21000,  20825,  20645,  20458,  20265,  20066,  19861,  19650,  19432,  19209,  18980,  18745,  18505,  18259,  18007,  17750,  17487,  17219,  16946,  16667,  16384,  16096,  15802,  15504,  15201,  14894,  14582,  14265,  13944,  13619,  13290,  12957,  12620,  12278,  11934,  11585,  11233,  10878,  10519,  10157,  9792,  9424,  9053,  8680,  8304,  7925,  7544,  7160,  6774,  6387,  5997,  5605,  5212,  4817,  4421,  4024,  3625,  3225,  2824,  2422,  2019,  1616,  1213,  809,  404,  00,  -404,  -809,  -1213,  -1616,  -2019,  -2422,  -2824,  -3225,  -3625,  -4024,  -4421,  -4817,  -5212,  -5605,  -5997,  -6387,  -6774,  -7160,  -7544,  -7925,  -8304,  -8680,  -9053,  -9424,  -9792,  -10157,  -10519,  -10878,  -11233,  -11585,  -11934,  -12278,  -12620,  -12957,  -13290,  -13619,  -13944,  -14265,  -14582,  -14894,  -15201,  -15504,  -15802,  -16096,  -16384,  -16667,  -16946,  -17219,  -17487,  -17750,  -18007,  -18259,  -18505,  -18745,  -18980,  -19209,  -19432,  -19650,  -19861,  -20066,  -20265,  -20458,  -20645,  -20825,  -21000,  -21167,  -21329,  -21483,  -21632,  -21773,  -21908,  -22036,  -22158,  -22273,  -22381,  -22482,  -22577,  -22664,  -22745,  -22818,  -22885,  -22945,  -22998,  -23044,  -23082,  -23114,  -23139,  -23156,  -23167,  -23170,  -23167,  -23156,  -23139,  -23114,  -23082,  -23044,  -22998,  -22945,  -22885,  -22818,  -22745,  -22664,  -22577,  -22482,  -22381,  -22273,  -22158,  -22036,  -21908,  -21773,  -21632,  -21483,  -21329,  -21167,  -21000,  -20825,  -20645,  -20458,  -20265,  -20066,  -19861,  -19650,  -19432,  -19209,  -18980,  -18745,  -18505,  -18259,  -18007,  -17750,  -17487,  -17219,  -16946,  -16667,  -16384,  -16096,  -15802,  -15504,  -15201,  -14894,  -14582,  -14265,  -13944,  -13619,  -13290,  -12957,  -12620,  -12278,  -11934,  -11585,  -11233,  -10878,  -10519,  -10157,  -9792,  -9424,  -9053,  -8680,  -8304,  -7925,  -7544,  -7160,  -6774,  -6387,  -5997,  -5605,  -5212,  -4817,  -4421,  -4024,  -3625,  -3225,  -2824,  -2422,  -2019,  -1616,  -1213,  -809,  -404,  00,  404,  809,  1213,  1616,  2019,  2422,  2824,  3225,  3625,  4024,  4421,  4817,  5212,  5605,  5997,  6387,  6774,  7160,  7544,  7925,  8304,  8680,  9053,  9424,  9792,  10157,  10519,  10878,  11233,  11585,  11934,  12278,  12620,  12957,  13290,  13619,  13944,  14265,  14582,  14894,  15201,  15504,  15802,  16096,  16384,  16667,  16946,  17219,  17487,  17750,  18007,  18259,  18505,  18745,  18980,  19209,  19432,  19650,  19861,  20066,  20265,  20458,  20645,  20825,  21000,  21167,  21329,  21483,  21632,  21773,  21908,  22036,  22158,  22273,  22381,  22482,  22577,  22664,  22745,  22818,  22885,  22945,  22998,  23044,  23082,  23114,  23139,  23156,  23167,  23170 
};

const int16 DiamondArray[] = { 
	-23170, -22913, -22655, -22398, -22140, -21883, -21625, -21368, -21110, -20853, -20596, -20338, -20081, -19823, -19566, -19308, -19051, -18793, -18536, -18279, -18021, -17764, -17506, -17249, -16991, -16734, -16476, -16219, -15962, -15704, -15447, -15189, -14932, -14674, -14417, -14159, -13902, -13645, -13387, -13130, -12872, -12615, -12357, -12100, -11842, -11585, -11328, -11070, -10813, -10555, -10298, -10040, -9783, -9525, -9268, -9011, -8753, -8496, -8238, -7981, -7723, -7466, -7208, -6951, -6694, -6436, -6179, -5921, -5664, -5406, -5149, -4891, -4634, -4377, -4119, -3862, -3604, -3347, -3089, -2832, -2574, -2317, -2060, -1802, -1545, -1287, -1030, -772, -515, 257, 257, 515, 772, 1030, 1287, 1545, 1802, 2060, 2317, 2574, 2832, 3089, 3347, 3604, 3862, 4119, 4377, 4634, 4891, 5149, 5406, 5664, 5921, 6179, 6436, 6694, 6951, 7208, 7466, 7723, 7981, 8238, 8496, 8753, 9011, 9268, 9525, 9783, 10040, 10298, 10555, 10813, 11070, 11328, 11585, 11842, 12100, 12357, 12615, 12872, 13130, 13387, 13645, 13902, 14159, 14417, 14674, 14932, 15189, 15447, 15704, 15962, 16219, 16476, 16734, 16991, 17249, 17506, 17764, 18021, 18279, 18536, 18793, 19051, 19308, 19566, 19823, 20081, 20338, 20596, 20853, 21110, 21368, 21625, 21883, 22140, 22398, 22655, 22913, 23170, 23170, 22913, 22655, 22398, 22140, 21883, 21625, 21368, 21110, 20853, 20596, 20338, 20081, 19823, 19566, 19308, 19051, 18793, 18536, 18279, 18021, 17764, 17506, 17249, 16991, 16734, 16476, 16219, 15962, 15704, 15447, 15189, 14932, 14674, 14417, 14159, 13902, 13645, 13387, 13130, 12872, 12615, 12357, 12100, 11842, 11585, 11328, 11070, 10813, 10555, 10298, 10040, 9783, 9525, 9268, 9011, 8753, 8496, 8238, 7981, 7723, 7466, 7208, 6951, 6694, 6436, 6179, 5921, 5664, 5406, 5149, 4891, 4634, 4377, 4119, 3862, 3604, 3347, 3089, 2832, 2574, 2317, 2060, 1802, 1545, 1287, 1030, 772, 515, 257, -257, -515, -772, -1030, -1287, -1545, -1802, -2060, -2317, -2574, -2832, -3089, -3347, -3604, -3862, -4119, -4377, -4634, -4891, -5149, -5406, -5664, -5921, -6179, -6436, -6694, -6951, -7208, -7466, -7723, -7981, -8238, -8496, -8753, -9011, -9268, -9525, -9783, -10040, -10298, -10555, -10813, -11070, -11328, -11585, -11842, -12100, -12357, -12615, -12872, -13130, -13387, -13645, -13902, -14159, -14417, -14674, -14932, -15189, -15447, -15704, -15962, -16219, -16476, -16734, -16991, -17249, -17506, -17764, -18021, -18279, -18536, -18793, -19051, -19308, -19566, -19823, -20081, -20338, -20596, -20853, -21110, -21368, -21625, -21883, -22140, -22398, -22655, -22913, -23170
};

const uint16 AssistValues[] = { 10, 20, 55, 80, 30, 65, 265, 90, 285, 290, 105, 15, 155, 85, 20, 35, 40, 70, 350, 160, 190, 280, 45, 30, 5, 35, 80, 165, 360, 165, 175, 275, 175, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 };

enum {
	Circle,Concentric,Concentric2,Concentric3,Portal,Bullet,Eye,TightEye,TrappedEye,Baseball,RugbyBall,ConcentricOval,Beachball,NestedOvals,React,TearDrop,CurvilinearDiamond,Diamond,Star,Rainbow,Smiley,Wings,Pyramid,TwinDiamonds,Barrels,FatCross,ThinCross,Orbiter,RoundedFatCross,RoundedCross,Loop2Loop,CrossSection,FullStack,Teasure,Galaxy,Coiled,RockyMountain,Killbox,Scanner,Rollercoaster,Spring,NinjaStar,Labyrinth
}

enum {
	FireOnly,AdsFire,AnyFire
}

enum {
	Toggled,Release
}

enum {
	Press,Hold,Delayed
}

const image MenuAABorderPage = {
128, 64, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0xFF, 0xF0, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0x00, 0x0F, 0xFF
};
/*
 .~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~. 
(                Mod Values/Times                    ) 
 `-~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-* 
*/ 
const int16 ModDigitVal [][] = { 
	{  0   , 42   , 1   },// DoGz Polar Effects											
	{  1   , 50   , 1   },// DoGz Polar Power (Radius)
	{  1   , 180  , 1   },// DoGz Polar Speed (Angle)
	{  1   , 50   , 1   },// DoGz Polar Power (Radius On Fire Boost)
	{  1   , 180  , 1   },// DoGz Polar Speed (Angle On Fire Boost)
	{  0   , 1    , 1   },// DoGz Polar Boost Power
	{  0   , 1    , 1   },// DoGz Polar Boost Speed
	{  0   , 1    , 1   },// DoGz Polar Boost HipFire
	{  0   , 1    , 1   },// Anti Recoil Method                                    
	{  0   , 100  , 1   },// Anti Recoil Power 1                                   
	{  0   , 100  , 1   },// Anti Recoil Power 2               
	{  0   , 100  , 1   },// Anti Recoil Power 3               
	{  0   , 100  , 1   },// Anti Recoil Power 4               
	{  0   , 100  , 1   },// Anti Recoil Power 5               
	{  0   , 100  , 1   },// Anti Recoil Power 6               
    {  20  , 2000 , 10  },// Anti Recoi Time 1                 
    {  20  , 2000 , 10  },// Anti Recoi Time 2                 
    {  20  , 2000 , 10  },// Anti Recoi Time 3                 
    {  20  , 2000 , 10  },// Anti Recoi Time 4                 
    {  20  , 2000 , 10  },// Anti Recoi Time 5
    {  20  , 2000 , 10  },// Anti Recoi Time 6
	{  30  , 200  , 10  },// Rapid Fire Hold
	{  30  , 200  , 10  },// Rapid Fire Wait
	{  0   , 2    , 1   },// Rapid Fire Activator
	{  0   , 1    , 1   },// Rapid Fire DeActivator				
	{  30  , 500  , 10  },// Drop Shot Wait
	{  0   , 2    , 1   },// Drop Shot Activator
	{  0   , 1    , 1   },// Drop Shot DeActivator
	{  30  , 700  , 10  },// Jump Shot Time
	{  0   , 2    , 1   },// Jump Shot Activator
	{  0   , 1    , 1   },// Jump Shot DeActivator
	{  500 , 4000 , 100 },// Strafe Shot Time
	{  0   , 2    , 1   },// Strafe Shot Activator
	{  0   , 1    , 1   },// Strafe Shot DeActivator
	{  150 , 600  , 10  },// Perfect Accuarcy Delay
	{  200 , 5000 , 10  },// Build Mode Sync Time
	{  10  , 150  , 10  },// Wall Take Time
	{  10  , 150  , 10  },// Wall Take P Time
	{  10  , 150  , 10  },// Pump Wall Time
	{  10  , 150  , 10  },// Pump Ramp Time
	{  10  , 150  , 10  },// Block Shot Time
	{  10  , 150  , 10  },// Pump Smg Time                                       
	{  50  , 150  , 1   },// Aim Abuse Hold                                       														                                                              
	{  10  , 60   , 10  } // Aim Abuse Release 
};
const int16 ModSwitchVal [][] = { 
	{ 0 , 1 },// Aim Assist
	{ 0 , 1 },// Anti Recoil
	{ 0 , 1 },// Rapid Fire
	{ 0 , 1 },// Drop Shot									
	{ 0 , 1 },// Jump Shot									
	{ 0 , 1 },// Strafe Shot                            	
	{ 0 , 1 },// Accuracy Mod                           	
	{ 0 , 1 },// Instant Pickaxe                        	
	{ 0 , 1 },// Build Tracker                          	
	{ 0 , 1 },// Edit Assist                            	
	{ 0 , 1 },// Fast Reset                             	
	{ 0 , 1 },// Wall Take                              	
	{ 0 , 1 },// Wall Take Build                        	
	{ 0 , 1 },// Pump Wall                              	
	{ 0 , 1 },// Pump Ramp                              	
	{ 0 , 1 },// Block Shot	                            	
	{ 0 , 1 },// Instant RFW                            	
	{ 0 , 1 },// Floor n Pyramid                        	
	{ 0 , 1 },// PS On Xbox                             	
	{ 0 , 1 },// Pump -> Smg                            	
	{ 0 , 1 } // Aim Abuse	                            	
};                                                      	
const int16 PointerValues [][] = {                      	
	{ 1 ,  1  , 8  },// Aim Assist                      	
	{ 1 ,  9  , 21 },// Anti Recoil                     	
	{ 1 ,  22 , 25 },// Rapid Fire                      	
	{ 1 ,  26 , 28 },// Drop Shot                       	
	{ 1 ,  29 , 31 },// Jump Shot                       	
	{ 1 ,  32 , 34 },// Strafe Shot                     	
	{ 1 ,  35 , 35 },// Accuracy Mod                    	
	{ 0 ,  0  , 0  },// Instant Pickaxe                 	
	{ 1 ,  36 , 36 },// Build Tracker            
	{ 0 ,  0  , 0  },// Edit Assist              
	{ 0 ,  0  , 0  },// Fast Reset               
	{ 1 ,  37 , 37 },// Wall Take                
	{ 1 ,  38 , 38 },// Wall Take Build          
	{ 1 ,  39 , 39 },// Pump Wall                
	{ 1 ,  40 , 40 },// Pump Ramp                
	{ 1 ,  41 , 41 },// Block Shot	             
	{ 0 ,  0  , 0  },// Instant RFW              
	{ 0 ,  0  , 0  },// Floor n Pyramid                 	
	{ 0 ,  0  , 0  },// PS On Xbox                      	
	{ 1 ,  42 , 42 },// Pump -> Smg                     																
	{ 1 ,  43 , 44 } // Aim Abuse
};