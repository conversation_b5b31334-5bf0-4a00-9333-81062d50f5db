# Controller Mappings

This table represents the mapping of GPC button codes to various controller buttons for different platforms.

| Idx | GPC         | PlayStation 4 | Xbox One      | Switch   | PlayStation 3 | Xbox 360 |
| --- | ----------- | ------------- | ------------- | -------- | ------------- | -------- |
| 0   | `BUTTON_1`  | PS            | Xbox          | Home     | PS            | Guide    |
| 1   | `BUTTON_2`  | Touch Click   | View          | Minus    | Select        | Back     |
| 2   | `BUTTON_3`  | Options       | Menu          | Plus     | Start         | Start    |
| 3   | `BUTTON_4`  | R1            | RB            | R        | R1            | RB       |
| 4   | `BUTTON_5`  | R2            | RT            | ZR       | R2            | RT       |
| 5   | `BUTTON_6`  | R3            | RS            | SR       | R3            | RS       |
| 6   | `BUTTON_7`  | L1            | LB            | L        | L1            | LB       |
| 7   | `BUTTON_8`  | L2            | LT            | ZL       | L2            | LT       |
| 8   | `BUTTON_9`  | L3            | LS            | SL       | L3            | LS       |
| 9   | `BUTTON_10` | DPad Up       | DPad Up       | DPad Up  | DPad Up       | DPad Up  |
| 10  | `BUTTON_11` | DPad Down     | DPad Down     | DPad Down| DPad Down     | DPad Down|
| 11  | `BUTTON_12` | DPad Left     | DPad Left     | DPad Left| DPad Left     | DPad Left|
| 12  | `BUTTON_13` | DPad Right    | DPad Right    | DPad Right| DPad Right   | DPad Right|
| 13  | `BUTTON_14` | Triangle      | Y             | X        | Triangle      | Y        |
| 14  | `BUTTON_15` | Circle        | B             | A        | Circle        | B        |
| 15  | `BUTTON_16` | Cross         | A             | B        | Cross         | A        |
| 16  | `BUTTON_17` | Square        | X             | Y        | Square        | X        |
| 17  | `BUTTON_18` | Share         |               | Capture  |               |          |
| 18  | `BUTTON_19` | Touch P1      |               |          |               |          |
| 19  | `BUTTON_20` | Touch P2      |               |          |               |          |
| 20  | `BUTTON_21` |               | Sync          |          |               |          |
| 21  | `STICK_1_X` | Right Stick X | Right Stick X | Right Stick X | Right Stick X | Right Stick X |
| 22  | `STICK_1_Y` | Right Stick Y | Right Stick Y | Right Stick Y | Right Stick Y | Right Stick Y |
| 23  | `STICK_2_X` | Left Stick X  | Left Stick X  | Left Stick X  | Left Stick X  | Left Stick X  |
| 24  | `STICK_2_Y` | Left Stick Y  | Left Stick Y  | Left Stick Y  | Left Stick Y  | Left Stick Y  |
| 25  | `POINT_1_X` | Touch P1 X    |               |          |               |          |
| 26  | `POINT_1_Y` | Touch P1 Y    |               |          |               |          |
| 27  | `POINT_2_X` | Touch P2 X    |               |          |               |          |
| 28  | `POINT_2_Y` | Touch P2 Y    |               |          |               |          |
| 29  | `ACCEL_1_X` | Accel X       |               | Accel X  | Accel X       |          |
| 30  | `ACCEL_1_Y` | Accel Y       |               | Accel Y  | Accel Y       |          |
| 31  | `ACCEL_1_Z` | Accel Z       |               | Accel Z  | Accel Z       |          |
| 32  | `ACCEL_2_X` |               |               |          |               |          |
| 33  | `ACCEL_2_Y` |               |               |          |               |          |
| 34  | `ACCEL_2_Z` |               |               |          |               |          |
| 35  | `GYRO_1_X`  | Gyro X        |               | Gyro X   |               |          |
| 36  | `GYRO_1_Y`  | Gyro Y        |               | Gyro Y   |               |          |
| 37  | `GYRO_1_Z`  | Gyro Z        |               | Gyro Z   | Gyro Z        |          |
| 38  | `PADDLE_1`  |               | P1¹           |          |               |          |
| 39  | `PADDLE_2`  |               | P2²           |          |               |          |
| 40  | `PADDLE_3`  |               | P3³           |          |               |          |
| 41  | `PADDLE_4`  |               | P4⁴           |          |               |          |

# GPC Hearder xb1.gph

#include <header_filename.gph>

#ifndef XB1_GPH_
#define XB1_GPH_
 
#define XB1_XBOX        BUTTON_1
#define XB1_VIEW        BUTTON_2
#define XB1_MENU        BUTTON_3
#define XB1_RB          BUTTON_4
#define XB1_RT          BUTTON_5
#define XB1_RS          BUTTON_6
#define XB1_LB          BUTTON_7
#define XB1_LT          BUTTON_8
#define XB1_LS          BUTTON_9
#define XB1_UP          BUTTON_10
#define XB1_DOWN        BUTTON_11
#define XB1_LEFT        BUTTON_12
#define XB1_RIGHT       BUTTON_13
#define XB1_Y           BUTTON_14
#define XB1_B           BUTTON_15
#define XB1_A           BUTTON_16
#define XB1_X           BUTTON_17
#define XB1_SYNC        BUTTON_21
#define XB1_P1          PADDLE_1
#define XB1_P2          PADDLE_2
#define XB1_P3          PADDLE_3
#define XB1_P4          PADDLE_4
 
#define XB1_RX          STICK_1_X
#define XB1_RY          STICK_1_Y
#define XB1_LX          STICK_2_X
#define XB1_LY          STICK_2_Y
 
#endif /* XB1_GPH_ */
