int rt_output, rt_timer;
int rt_delay = 5;
int rt_delay2 = 40;// Delay in ms between steps (lower = faster ramp)

main {
    rt_timer += get_rtime();

    if (get_val(XB1_RT)) {
        if (rt_timer >= rt_delay) {
            rt_timer = 0;
            if (rt_output < 100) rt_output += 4;
        }
    } else {
        if (rt_timer >= rt_delay2) {
            rt_timer = 0;
            if (rt_output > 0) rt_output -= 4;
        }
    }

    set_val(XB1_RT, rt_output);
}