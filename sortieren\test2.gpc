define MAX_STICK_VALUE = 32767
define STICK_DEADZONE = 5000

int leftStickAngle;
int leftStickMagnitude;

main {
  // Get left stick polar coordinates
  leftStickAngle = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
  leftStickMagnitude = get_polar(POLAR_LS, POLAR_RADIUS);
  
  // Activate when stick is out of deadzone
  if (leftStickMagnitude > STICK_DEADZONE) {
    // Activate XB1_LB
    set_val(XB1_LB, 100);

    
    // Rotate left stick input
    set_polar(POLAR_LS, leftStickAngle, MAX_STICK_VALUE);
    
    // Output angle for debugging
    set_val(TRACE_1, leftStickAngle);
  } else {
    // Deactivate XB1_LB when stick is within deadzone
    set_val(XB1_LB, 0);
  }
} 