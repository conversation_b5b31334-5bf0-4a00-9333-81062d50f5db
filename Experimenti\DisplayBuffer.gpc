// OLED constants:
const uint8 FontHeight[] = { OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT }
const uint8 FontWidth[] = { OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH }

// String constants:
const string _string1 = "SWEDE";
const string _string2 = "MAFIA";

enum {
	// Display elements (items in Display[]):
	Buffer = 0, BufferInsertCopyValue, BufferInsertDigitCount, BufferInsertDigits, Update = BufferInsertDigits + 5, // BufferInsertDigits requires 11 elements
	
	// OLED identifiers:
	AlignCenter = -3, AlignBottom = -2, AlignRight = -1, AlignLeft = 2, AlignTop = 5
}

// Required variables:
int i;
int display[15];

init {
	display[Buffer] = 1;	// Initialize the display buffer
	cls_oled(OLED_BLACK);	// Clear the screen
}

main
{
	// Display "SWEDEMAFIA#17" to top-center screen
	InsertString(addr(_string1));
	InsertString(addr(_string2));
	InsertCharacter(ASCII_POUND);
	InsertNumber(17);
	FlushBuffer(AlignCenter, AlignTop, OLED_FONT_SMALL, OLED_WHITE);
	
	// Display "SWEDE" with provided coordinates
	PrintString(5, 15, addr(_string1), OLED_FONT_SMALL, OLED_WHITE);
	
	// Display the value of 2147483647 to center screen
	PrintNumber(AlignCenter, AlignCenter, 2147483647, OLED_FONT_SMALL, OLED_WHITE);
	
	// Display the value of -1234657890 to center screen
	PrintNumber(AlignCenter, 38, -1234567890, OLED_FONT_SMALL, OLED_WHITE);
	
	// Display "100" to bottom right
	InsertNumber(100);
	FlushBuffer(AlignRight, AlignBottom, OLED_FONT_SMALL, OLED_WHITE);
	
	// Display "Hey!" to bottom left
	InsertCharacter(ASCII_UPPER_H);
	InsertCharacter(ASCII_LOWER_E);
	InsertCharacter(ASCII_LOWER_Y);
	InsertCharacter(ASCII_EXCLAMATION);
	FlushBuffer(AlignLeft, AlignBottom, OLED_FONT_SMALL, OLED_WHITE);
	
	// Build a display to the bottom center
	InsertNumber(12);
	InsertCharacter(ASCII_COLON);
	InsertString(addr(_string2));
	FlushBuffer(AlignCenter, AlignBottom, OLED_FONT_SMALL, OLED_WHITE);
}

// This function is an alternative for the ternary operator
function iif(expression, truepart, falsepart)
{
	// evaluate if expression is true
	if(expression) {
		return truepart; // Return truepart
	}
	
	// Else, return falsepart
	return falsepart;
}

// Flushes the display buffer
function FlushBuffer(x, y, size, color)
{
	// Decrement display buffer value for proper output
	display[Buffer]--; 
	
	// Check horizontal alignment
	if(x == AlignRight) {
		x = OLED_WIDTH - (display[Buffer] * FontWidth[size]) - 2; // Additional 2 for padding from border
	} else if(x == AlignCenter) {
		x = (OLED_WIDTH >> 1) - ((display[Buffer] * FontWidth[size]) >> 1);
	}
	
	// Check vertical alignment
	if(y == AlignBottom) {
		y = OLED_HEIGHT - FontHeight[size] - 2; // Additional 2 for padding from border
	} else if(y == AlignCenter) {
		y = (OLED_HEIGHT >> 1) - (FontHeight[size] >> 1);
	}
	
	// Output display buffer to OLED
	puts_oled(clamp(x, 2, 127), clamp(y, 2, 63), size, display[Buffer], color);
	
	// Reset display buffer
	display[Buffer] = 1;
}

// Inserts a character into the display buffer
function InsertCharacter(value)
{
	// Puts a character in the display buffer
	putc_oled(display[Buffer], value);
	display[Buffer]++;
}

// Inserts a number into the display buffer
function InsertNumber(value)
{
	// Check if value is negative
	if(value < 0) {
		InsertCharacter(ASCII_MINUS); // Insert '-' into the display buffer
		value = abs(value); // Convert value to positive
	}
	
	display[BufferInsertCopyValue] = value; // Create a copy of value to perform work on
	display[BufferInsertDigitCount] = 0; // Reset number of digits to 0
	
    // Determine the number of digits in the number by
    // dividing it by 10 repeatedly until it becomes 0
    // while not ignoring a value of 0
    do {
        display[BufferInsertCopyValue] /= 10;
        display[BufferInsertDigitCount]++;
    } while(display[BufferInsertCopyValue])
    
    // Extract each digit of the number and store it in an array
    for(i = 0; i < display[BufferInsertDigitCount]; i++) {
        display[BufferInsertDigits + i] = (value % 10) + 48;
        value /= 10;
    }
    
    // Insert the digits in the order that they appear in the number
    for(i = display[BufferInsertDigitCount] - 1; i >= 0; i--) {
        InsertCharacter(display[BufferInsertDigits + i]);
    }
}

// Inserts a string into the display buffer
function InsertString(s)
{
	// Loop through each character of the string
    do {
    	InsertCharacter(dint8(s)); // Insert character into display buffer
    	s++; // Move to next character of the string
    } while(dint8(s)) // Check if a next character exists
}


// This function prints a character to the screen
function PrintCharacter(x, y, character, size, color)
{
	InsertCharacter(character);
	FlushBuffer(x, y, size, color);
}

// Prints a number to the screen
function PrintNumber(x, y, number, size, color)
{
	InsertNumber(number); // Insert number to the display buffer
	FlushBuffer(x, y, size, color); // Flush display buffer
}

 //	Prints text to the screen
function PrintString(x, y, text, size, color)
{
	InsertString(text); // Insert string to the display buffer
	FlushBuffer(x, y, size, color); // Flush display buffer
} 