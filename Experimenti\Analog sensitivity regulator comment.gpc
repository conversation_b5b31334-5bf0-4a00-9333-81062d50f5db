// Analog-Sensitivitätsregler
// Die<PERSON>t passt die Empfindlichkeit der linken Analogsticks (LX und LY) an.
// Der Benutzer kann den Empfindlichkeitswert durch Ändern der Variable 'sensitivityValue' anpassen.

int sensitivityValue = 200; // Empfindlichkeitswert (Standard: 200%)

main
{
  // Anpassung der Empfindlichkeit für den linken Stick X-Achse (POLAR_LX)
  _sensitivity(POLAR_LX, 16384, sensitivityValue * 32767 / 100);

  // Anpassung der Empfindlichkeit für den linken Stick Y-Achse (POLAR_LY)
  _sensitivity(POLAR_LY, 16384, sensitivityValue * 32767 / 100);
}

int _val_s, _val;

// Funktion zur Anpassung der Sensitivität eines gegebenen Achsen-IDs
function _sensitivity(id, mid, sen)
{
  // Aktuellen Wert der Achse abrufen und auf den gültigen Bereich beschränken
  _val = clamp(get_val(id), -32767, 32766);

  // Wenn 'mid' nicht verwendet wird (Optionaler Bereich, hier nicht genutzt)
  if (mid == NOT_USE)
  {
    _val_s = -1;
    if (_val >= 0)
      _val_s = 1;
    _val *= _val_s;

    // Anpassung basierend auf dem mittleren Wert
    if (_val <= mid)
    {
      _val = (_val * 16384) / mid;
    }
    else
    {
      _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
    }
    _val *= _val_s;
  }

  // Wenn ein Sensitivitätswert angegeben ist, diesen anwenden
  if (sen != NOT_USE)
  {
    _val = (_val * sen) >> 15; // Effektive Division durch 32768
  }

  // Angepassten Wert wieder auf die Achse setzen, innerhalb des gültigen Bereichs
  set_val(id, clamp(_val, -32768, 32767));
}