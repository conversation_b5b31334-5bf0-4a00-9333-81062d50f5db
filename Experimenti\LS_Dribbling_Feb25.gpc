////////////////////////////////////////////////////////////////////////////////
// LS_Dribbling_Feb25.gpc
// Left stick dribbling script with octagon mapping and polar adjustment
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// GLOBAL VARIABLES
////////////////////////////////////////////////////////////////////////////////
int LX, LY;              // Raw stick inputs
int scaled_x, scaled_y;  // Final outputs after shaping
int angle, radius;       // For polar coordinates

// Constants
int DEADZONE   = 30;     // Square deadzone threshold
int MAX_INPUT  = 100;    // Maximum stick range

// Helper globals for deadzone scaling
int sign, abs_val, output;

// Helper globals for circular clamp
int mag_sq, limit_sq, mag;

// Helper globals for the integer square-root function
int iSqrtValue, iSqrtRes, iSqrtBit, iSqrtTemp;

// Angle max values for polar adjustment
const int8 anglesMax[] = {
   // Index: 0..9
   100, 99, 98, 97, 96, 95, 94, 94, 93, 92,
   // 10..19
    91, 90, 89, 88, 87, 86, 85, 85, 84, 83,
   // 20..29
    82, 78, 70, 78, 82, 83, 84, 85, 85, 86,
   // 30..39
    87, 88, 89, 90, 91, 92, 93, 94, 94, 95,
   // 40..44
    96, 97, 98, 99, 100
};

////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
//   - Must not declare local int variables here, so we use the global ones
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes = 0;
    iSqrtBit = 1 << 14; // The second-to-top bit is set: 1L<<30 for long

    // "bit" starts at the highest power of four <= the argument.
    while(iSqrtBit > iSqrtValue) {
        iSqrtBit >>= 2;
    }

    while(iSqrtBit != 0) {
        if(iSqrtValue >= iSqrtRes + iSqrtBit) {
            iSqrtValue -= iSqrtRes + iSqrtBit;
            iSqrtRes = (iSqrtRes >> 1) + iSqrtBit;
        } else {
            iSqrtRes >>= 1;
        }
        iSqrtBit >>= 2;
    }
    return iSqrtRes;
}

////////////////////////////////////////////////////////////////////////////////
// Function: apply_one_axis_deadzone(val)
//   - Applies a square deadzone on a single axis: [DEADZONE..100] -> [0..100]
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(val) {
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);

    // If within ±DEADZONE, zero it out
    if(abs_val <= DEADZONE) return 0;

    // Clamp raw input above 100
    if(abs_val > MAX_INPUT) abs_val = MAX_INPUT;

    // Scale from (DEADZONE..MAX_INPUT) -> (0..MAX_INPUT)
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    return sign * output;
}

////////////////////////////////////////////////////////////////////////////////
// Function: map_circular_octagon(x, y)
//   - Maps input coordinates to an octagonal boundary
//   - 1) Applies a square deadzone to x,y
//   - 2) Clamps final radius so sqrt(x^2 + y^2) <= MAX_INPUT
////////////////////////////////////////////////////////////////////////////////
function map_circular_octagon(x, y) {
    // Step 1: Apply square deadzone
    scaled_x = apply_one_axis_deadzone(x);
    scaled_y = apply_one_axis_deadzone(y);

    // Step 2: Clamp radius (circular)
    mag_sq   = scaled_x*scaled_x + scaled_y*scaled_y;
    limit_sq = MAX_INPUT*MAX_INPUT; // 100^2 = 10,000

    if(mag_sq > limit_sq) {
        mag      = intSqrt(mag_sq);    // approximate sqrt
        scaled_x = (scaled_x*MAX_INPUT) / mag;
        scaled_y = (scaled_y*MAX_INPUT) / mag;
    }
}

////////////////////////////////////////////////////////////////////////////////
// MAIN LOOP
////////////////////////////////////////////////////////////////////////////////
main {
    LX = get_val(XB1_LX);  // Get stick values first
    LY = get_val(XB1_LY);

    // Get polar coordinates
    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

    // Switch between octagon and polar modes with XB1_LS
    if (get_val(XB1_LS)) {
        map_circular_octagon(LX, LY);  // Apply octagon mapping when XB1_LS is active
        set_val(XB1_LX, scaled_x);  // Apply the scaled values
        set_val(XB1_LY, scaled_y);
    } else {
        // Apply set_polar2 with anglesMax when XB1_LS is not active
        set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
    }
}
