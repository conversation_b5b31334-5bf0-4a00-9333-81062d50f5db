/*
==================================================================================================
   Script was generated with | FIFA 23 S.G.I && PES 23| ver. ********* | Date :07.02.23 | Time: 01:36:54 |
   Special thanks to <PERSON><PERSON><PERSON><PERSON> - the author of one of the best new MODS                       
   Thanks to all Alpha Supporters!                         
==================================================================================================
==================================================================================================
| This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                       | 
| UNLESS permission is given by the creator and/or copywritee,                                    | 
| All rights reserved. This material may not be reproduced, displayed,                            | 
| modified or distributed without the express prior written permission of the                     | 
| copyright holder. For permission, contact CronusMax.                                            | 
|     __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                  | 
|    /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                                 | 
|   /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                                 | 
|  /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                                 | 
| /   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                                 | 
| \     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                                 | 
|  \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                                 | 
|                                                                                                 | 
================================================================================================== 
==================================================================================================
*/
                                                                       
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define None  = 0;
define FAKE_SHOT_SKILL                   = 1;
define HEEL_TO_HEEL_FLICK_SKILL          = 2;
define HEEL_FLICK_TURN_SKILL             = 3;
define RAINBOW_SKILL                     = 4;
define DRAG_BACK_SOMBRERO_SKILL          = 5;
define FAKE_PASS_SKILL                   = 6;
define DRAG_BACK_UNIVERSAL_SKILL         = 7;
define STEP_OVER_FEINT_SKILL             = 8;
define DRAG_TO_DRAG_SKILL                = 9;
define HOCUS_POCUS_SKILL                 = 10;
define TRIPLE_ELASTICO_SKILL             = 11;
define ELASTICO_SKILL                    = 12;
define REVERSE_ELASTICO_SKILL            = 13;
define CRUYFF_TURN_SKILL                 = 14;
define LA_CROQUETA_SKILL                 = 15;
define RONALDO_CHOP_SKILL                = 16;
define ROULETTE_SKILL                    = 17;
define FLAIR_ROULETTE_SKILL              = 18;
define BALL_ROLL_SKILL                   = 19;
define BERBA_MCGEADY_SPIN_SKILL          = 20;
define BOLASIE_FLICK_SKILL               = 21;
define TORNADO_SKILL                     = 22;
define THREE_TOUCH_ROULETTE_SKILL        = 23;
define ALTERNATIVE_ELASTICO_CHOP_SKILL   = 24;
define BALL_ROLL_CHOP_SKILL              = 25;
define FEINT_AND_EXIT_SKILL              = 26;
define FEINT_L_EXIT_R_SKILL              = 27;
define LATERAL_HEEL_TO_HEEL_SKILL        = 28;
define WAKA_WAKA_SKILL                   = 29;
define BODY_FEINT_SKILL                  = 30;
define DRAG_TO_HEEL                      = 31;
define BALL_ROLL_FAKE_TURN               = 32;
define FEINT_FORWARD_AND_TURN            = 33;
define TURN_BACK                         = 34;
define ADVANCED_CROQUETA                 = 35;
define CANCELED_THREE_TOUCH_ROULETTE_SKILL = 36;
define REVERSE_STEP_OVER_SKILL           = 37;
define FAKE_DRAG_BACK_SKILL              = 38;
define RAINBOW_TO_SCORPION_KICK_SKILL    = 39;
define STEP_OVER_BOOST_SKILL             = 40;
define CANCEL_SHOOT_SKILL                = 41;
define DIRECTIONAL_NUTMEG_SKILL          = 42;
define CANCELED_BERBA_SPIN_SKILL         = 43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION = 44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL     = 45;
define DRIBBLING_SKILL                   = 46;
define FOUR_TOUCH_TURN_SKILLS            = 47;
define SKILLED_BRIDGE_SKILL              = 48;
define SCOOP_TURN_FAKE_SKILL             = 49;
define BALL_ROLL_STEP_OVER_SKILL         = 50;
define CANCELED_4_TOUCH_TURN_SKILL       = 51;
define FAKE_SHOT_CANCEL_SKILL            = 52;
define OKKOSHA_FLICK_SKILL               = 53;
define ADVANCED_RAINBOW_SKILL            = 54;
define STOP_LA_CROQUETA_SKILL            = 55;
define JUGGLING_RAINBOW_SKILL            = 56;
define STOP_NEYMAR_ROLL_SKILL            = 57;
define STOP_V_DRAG_SKILL                 = 58;
define REV_OR_ELASTICO_SKILL             = 59;
define STOP_REV_OR_ELASTICO_SKILL        = 60;
define DRAG_REV_OR_ELASTICO_SKILL        = 61;
define FAKE_RABONA_SKILL                 = 62;
define RABONA_TO_REV_ELASTICO_SKILL      = 63;
define RABONA_TO_ELASTICO_SKILL          = 64;
define SOMBRERO_FLICK_SKILL              = 65;
define JUGGLE_BACK_SOMBRERO_SKILL        = 66;
define FAKE_BERBA_OPP_EXIT_SKILL         = 67;
define DIAGONAL_HEEL_CHOP_SKILL          = 68;
define FAKE_BERBA_FAKE_DRAG_SKILL        = 69;
define ELASTICO_CHOP_SKILL               = 70;
define BALL_ROLL_CUT_180_SKILL           = 71;
define HEEL_TO_BALL_ROLL_SKILL = 72;
define STUTTER_FEINT_SKILL = 73;
define JOG_OPENUP_FAKE_SHOT = 74;
define SPIN_MOVE_LEFT_RIGHT_SKILL = 75;
//====================================================
// DEVICE :  Cronus ZEN device 
//====================================================
//====================================================
// YOUR BUTTON LAYOUT : CUSTOM
//====================================================
define ShotBtn       = XB1_B          ; // Shot Btn         (default B/CIRCLE 
define PassBtn       = XB1_A          ; // Short Pass Btn   (default A/CROSS)
define PlayerRun     = XB1_LB         ; // Player Run       (default L1/LB) 
define FinesseShot   = XB1_RB         ; // Finesse Shot     (default R1/RB)
define PaceCtrol     = XB1_LT         ; // Protect Ball     (default L2/LT)
define SprintBtn     = XB1_RT         ; // Sprint Btn       (default R2/RT)
define CrossBtn      = XB1_X          ; // Cross Btn        (default X/SQUARE)
define ThroughBall   = XB1_Y          ; // Through Ball Btn (default Y/TRIANGLE) 
//====================================================
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//====================================================
main { // main block start here
 if(time_to_clear_screen){
    time_to_clear_screen -= get_rtime();
    if(time_to_clear_screen <= 0)combo_run(CLEAR_SCREEN);
 }


					if ( event_press(XB1_LEFT)){
					    corn_right_on = FALSE;
						combo_run(CORNER_CURVE_cmb);
					}
					if ( event_press(XB1_RIGHT)){
					    corn_right_on = TRUE;
						combo_run(CORNER_CURVE_cmb);
					}			

			   
} // main block end here
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int Get_LS_Output = TRUE;
int corner_power = 480;
///// Corner combos /////////
int Corner_on_off;
int corn_right_on ;
combo CORNER_CURVE_cmb {
   set_val(PlayerRun,100);
   wait(50);
   if(corn_right_on)set_val(XB1_RX,-100); 
   else set_val(XB1_RX,100);
   wait(700);
   wait(50);
   set_val(XB1_RY,-65);
   wait(500);  
   wait(50);
   if(corn_right_on)set_val(XB1_LX,-100);
   else set_val(XB1_LX,100);
   wait(480);
   wait(50);
   set_val(CrossBtn,100);
   wait(corner_power);
   wait(50);
   if(corn_right_on)set_val(XB1_LX,100);
   else set_val(XB1_LX,-100);
   wait(500);
   if(corn_right_on)set_val(XB1_LX,100);
   else set_val(XB1_LX,-100);
   wait(900);
   if(corn_right_on)set_val(XB1_LX,-100);
   else set_val(XB1_LX,100);
   set_val(SprintBtn,100);
   set_val(PaceCtrol,0);
   wait(1500);
   set_val(CrossBtn,100);
   wait(50);
   wait(500);
   set_val(PaceCtrol,100);
   sensitivity(PS4_LX, NOT_USE, 100);
   sensitivity(PS4_LY, NOT_USE, 100);
   wait(1500);
   Corner_on_off = FALSE;
   f_set_notify(Corner_on_off);
   load_slot (1);
} 

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2); 
} 
const string OFF   = "OFF";       
const string ON    = "ON";

   //=======================================
   //  DISPLAY EDIT VALUE ON THE FLY        
   //=======================================
function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 
const string EA_PING = "EA PING";   
   
   
/*   
=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}
/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
} 
int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}
combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}            

int OnOFF_Skills;
int ModifierBtn;
int dubltime;
define ColorOFF  = 0;
define Blue      = 1;
define Red       = 2;
define Green     = 3;
define Pink      = 4;
define SkyBlue   = 5;
define Yellow    = 6;
define White     = 7;
                      
data(                 
  0,0,0, //0. ColorOFF
  2,0,0, //1. Blue     
  0,2,0, //2. Red      
  0,0,2, //3. Green    
  2,2,0, //4. Pink     
  2,0,2, //5. SkyBlue 
  0,2,2, //6. Yellow   
  2,2,2  //7. White    
); // end of data segment-------------- 
// COLOR LED function        
//-------------------------------------------------------------- 
                                       
int data_indx;
function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

const int8 SelectBtn [] ={
XB1_XBOX, // 0 
XB1_VIEW, // 1 
XB1_MENU, // 2 
XB1_RB,   // 3 
XB1_RT,   // 4 
XB1_RS,   // 5 
XB1_LB,   // 6 
XB1_LT,   // 7 
XB1_LS,   // 8 
XB1_UP,   // 9 
XB1_DOWN, // 10  
XB1_LEFT, // 11  
XB1_RIGHT,// 12  
XB1_Y,    // 13 
XB1_B,    // 14 
XB1_A,    // 15 
XB1_X,    // 16 
XB1_PR1,  // 17 
XB1_PR2,  // 18 
XB1_PL1,  // 19 
XB1_PL2,  // 20 
PS4_TOUCH // 21  
}

int ShootPower;
int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {     
 if(get_ival(PS4_LX) >= 12) AIM_X = f_LX ; 
 else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX) ;  
               
 if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY ;  
 else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}  
int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function set_right_or_left () {
    right_on = FALSE; 
    if (zone_p == 4 || zone_p == 3 || zone_p == 7 ) { 
        right_on = TRUE; 
    } /// 
}
int time_to_dblclick = 300
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo STOP_PLAYER_cmb {
  zone_saver(); 
  wait(20);
  wait(100);
  set_val(SprintBtn,100);
  wait(40);
  wait(160);
    Get_LS_Output = TRUE;
}

function zone_saver() {
dEnd = zone_p
calc_relative_xy(dEnd);
LX = move_lx;
LY = move_ly;
}

combo JUGGLING_cmb{
set_val(PaceCtrol,100);
set_val(FinesseShot,100);
wait(100);
set_val(PaceCtrol,100);
wait(100);
    Get_LS_Output = TRUE;
}
int LS_BlockOutput;
///////////////////////////////////////////////////
// ZONE FUNCTION
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_ival(PS4_LX) >= 35) move_lx = 100;
    else if(get_ival(PS4_LX) <= -35) move_lx = -100;
    else move_lx = 0;
    if(get_ival(PS4_LY) >= 35) move_ly = 100;
    else if(get_ival( PS4_LY) <= -35) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(ZONE_P[zone_p][0] == move_lx && ZONE_P[zone_p][1] == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
}
function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - abs(d);
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                              