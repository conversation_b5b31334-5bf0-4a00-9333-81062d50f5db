/*

                    ▓█████▄  ▄▄▄       ██▀███   ██ ▄█▀    ▄▄▄       ███▄    █   ▄████ ▓█████  ██▓
                    ▒██▀ ██▌▒████▄    ▓██ ▒ ██▒ ██▄█▒    ▒████▄     ██ ▀█   █  ██▒ ▀█▒▓█   ▀ ▓██▒
                    ░██   █▌▒██  ▀█▄  ▓██ ░▄█ ▒▓███▄░    ▒██  ▀█▄  ▓██  ▀█ ██▒▒██░▄▄▄░▒███   ▒██░
                    ░▓█▄   ▌░██▄▄▄▄██ ▒██▀▀█▄  ▓██ █▄    ░██▄▄▄▄██ ▓██▒  ▐▌██▒░▓█  ██▓▒▓█  ▄ ▒██░
                    ░▒████▓  ▓█   ▓██▒░██▓ ▒██▒▒██▒ █▄    ▓█   ▓██▒▒██░   ▓██░░▒▓███▀▒░▒████▒░██████▒
                     ▒▒▓  ▒  ▒▒   ▓▒█░░ ▒▓ ░▒▓░▒ ▒▒ ▓▒    ▒▒   ▓▒█░░ ▒░   ▒ ▒  ░▒   ▒ ░░ ▒░ ░░ ▒░▓  ░
                     ░ ▒  ▒   ▒   ▒▒ ░  ░▒ ░ ▒░░ ░▒ ▒░     ▒   ▒▒ ░░ ░░   ░ ▒░  ░   ░  ░ ░  ░░ ░ ▒  ░
                     ░ ░  ░   ░   ▒     ░░   ░ ░ ░░ ░      ░   ▒      ░   ░ ░ ░ ░   ░    ░     ░ ░
                       ░          ░  ░   ░     ░  ░            ░  ░         ░       ░    ░  ░    ░  ░
                     ░

                                               ,▄▄▄,
                                          ,▄███▀▀▀▀▀██                            ▄██████████▄,
                                       ,▄██▀▀▄▄██ , ,▐█                          █▀ ,  &█▄▄▄▀▀███▄
                                    ,▄███▀,▄████▓█ █▄██L                        ▓█▄▄L▀█▌▀█████▄ ▀███▄
                              .wææ████▀ ▄███▄█▀▄█ ▄▀`▀`                          ▀▀▀█ ▐██.▀█████▄  ███Nw,
                             ══P▀███' ▄██████▄██▌ █               ,                 █U ████▄██▄███▄ ▀█▀▀▄,
                               ,█▀  ▄███████████▌ █          ,▄▄▄█▌▄██              █ ▐█████▀███▀███  █▄
                              █▀  ▄██▀▀▄██▀▄█████ █▌      ,m▀███▓██████▄P          █▀ █████▀▀▄▀▐█▄ ██▄ ▀█
                          ╓▄Æ▀   ███████▄██╫,████▄ █▄  ▄ ▐▌`▐φ▄▓██▀▀▓█▓█▄∞    ╓▌ ▄█▀ ████▀▄█▐█████████▄  ▀▄
                          ▀█▄r  ███████████▐███▄▀▀▀═▐███▄ █▀▀▀██▓████████▀`  <███▌ ¢▀▀,████'█▀█████████    ▀█
                           ,▀  ▐███▓█████¬█▄█▌▀██▄▀▀█████"   ▄████████████.  ▀████▀▀▄█▀▀▀█,██▄█████▓████  ▀▀▀
                           ▌ , ███████▌███████▄'▄████▄▄▄▀█ ▄████▓█████▄███▄ ▐█▀▄▄█████▄▄████▄███▓███████   █
                          █  █▐███▀███▐█▀▀█▀█`▄█████████ ███████▓████▓▀██████ ╙█████▓██∞▄█▀█▀▄██████████ ▌  █
                         ▐▌  ████████▌███▀███▓N█▀█████▀ ████▌██▀██▓█▌██▓█▄████  ██████∞▀████████████▐█████  ▐▌
                         █   ████▌██████▓█████▓▀▓████▌,▀▓▐█▓██▄█▐▌█▀▐██▌████▄██▄▐███▀▓▓█████▌▓██████▐█████   █
                        ▐█ , ██▌█═███▄███▄▀████▄▀▀███▄,▄▀█████████████████▀▀█▄▌▄█████▄████▀▄███▌███▌▐█▐███   ██
                        █▌ ▐µ██ █▌███ █╒▄█▀▀█████▐█████████▌▓██▀██▓█▌██▐██▐██▓▀███▄▌██████▀██╕█`███▌██ ██▌ ▌ ▐█
                        █▌]▐▀██ ▐█▐██U██▄"▀▀▀▀▀████▄█▀█▌█▀██▄█▄▌&█▓██▐▐▄█▀██▄▓▌█▀▌█████▀▀▀▀▄▄██j███V█` ████U ▐█
                        █▌ █]██j█▀Ç▐▌▐██▀█▀█▀▀███▀▄████████████▀██▄██▀██▀█▀▀██▌█▐█▄▀▀██▄▀█▀█▀██C▐▌`██W ██▌▐█ ██
                        ██ █+██▐▌ █ ██▐▌▀▄█▌;▄▄▄████▌█▄████▌██▄█▀███▌█▐█████▀██▀██████▄▄▄██▄▀▐Ü▌█ █ ▐█ ██▌▐▌ ██
                        █▌▐█M█U██ ██▄█C████████─█▄██▌▀███▓██▓█▄█▌▓█▀Ω██▀███▀█▀▐▄▄╟█▄█▐█████▌█▌▐▌▄██ ╓█▄██ █▌ █▌
                       ▄████▌`███▄█████▐ ██▐▌██▐██╙▌▀▀▄███▓▌██▓██▓▀▀Ö█████▄████,▀  ██▀█▌█▀█▌▐ █████▄███▄▀▐██▄█▌
                        ▐██▌█▄██H████▀█░ █▌█▐██▐▀█   ███▀▄██████▌███▌██████▓████  ▄▀█▐███ █ Å▐██████▌███╔████▀▀═
                        ███▌▀███▌█▒██▓▐µ▌▐▌╟▐██U█▌▀─ ██▌███▓▌█w▄▓▀▀█▀╣██▄█▀█████ "▀█▌███▐]█,U█▄███▐█▐████"███▌
                        ████▀███▌█▌███▀`█Ç█▐▐██████∞ ▐▌███████∞▄▓████▄████U███▌, ▄█▀▀██▌▌█U█ ▄▓██▌█▌▐███▀▄███▌
                        ▐███▌███▌▐▌▐██▌███▓█▌██▀▄▀▄██▀▌▌█▌████▄ █▀▓██▄██▄▀▐███▐██▄█▌███▄█▀███Ü███▒█▌▐███▌████▌
                        ▐████'██ ▐█▒███U███▐▌▌█Ü█▓▄██████▄█████▀▄▄▄▄▓██████████▄██▀█Ü█▄▐▀███▌███▌▒█  ██▀▐████
                        ▐█████ █ ▐█ƒ▐██⌐████▐▄█"█M▀█████▀▄████U█ █▄███▐███▀▄███▌█▀██`▄▄▌████ ▓██╘▓█▌ █▌▐▌████▌
                           ▐█▄█ ▌▐██ ▀█╕▐█W█▌▀███▀ß█████▀▌████M█▀▀████]█▀▀▀███████▀███▌▐█j██ ██ ▐██▌▐▌,█▐██'▀▀
                            ██▓▐  ███ ▀▌]██▄▀⌐█ ▀▄▄▀██▀▀▀██████▄▀██A▀▄██████▀██▀█▀██ █ █▄▓█▌ █ ╓███ ╙╓█ù██`
                             ██▄█ ▐███▄▀ ██U█▄▐▌█▄█▐█L█∞████████ █▀█▐███▌██████▄█▀█▄██,█`██ █`▄████  ▌,██▌
                             █████ █████▓µ█▌▄███▄██▀ █████▌▄████ █U█▐██▐██████▀` ▀▀▀████╜█▌▐▄█████▌ █▄███
                             ██"███▄█████▄▐█▄███⌐  ,▄▄██████Ü███ █▌▐▓██▐█▓████▀▀▀   ███▀██▄█▀█████ ██████
                                 █████████▀██▌██▌    ███▀█W█████ █▌▐▌█▐██▀█C███N▄   ██▌███▀██▀███▄████  ▀y
                                  ▀██████▀█▄▀█▄█╙  ▄██]███▐███▐▌ █▌ ▌█▐██▌▀█████▄    █▐█▀▄█▀,██▀█████
                                   ╙██████▄▀▌╝▓█  ███.██▀█Å▄██▄▌j█▌ ▌█████∩███▀███▄ ▐█▄V▓█,███▌████▀
                                     ▀███████▀███'▀█▌█▀▄▓██▓▐██▌▐█▌ ██████▄█▀██▄██▄▀██▄█▄███▀▄████
                                       ████▀▄▀▀██▌▐█ ▄█▀██▀▐▐▌█▌▐█▌ █████████▐█▀∩▌ ▐███▀▀▄▀▄████▀
                                        ▀███▄▀███▀████▌██,██▌██▌▐█▌ █║█████▀█C██▄█ "██▀█▀▄████▀
                                         ▀██▀█▄███ ▐██¬▀██▌████▌▐█▌ █▐███▌██/▌▐█▄▀▀]███▄█▀▄██▀
                                          ▀██▄▀▄▐█  █▌█▀██ ████▌▐█▌ █▐███▌████ █    █╛▄▀▄▀██▀
                                           ██▄█▄▀▀█ █▀ ███J▀███ ██▌ █▐███▀███▓▀▀█  █▀█▄█ ██▀
                                            ██▓██ █▌   █▀█▄████ ██▌▐█▐█████ ▀▀█  '▐█ ▄████▌
                                            ▐██ ████  ▀ ▐███████ █▌▐`████▓▀▌   '  ██▄█▌▐██
                                             ██ ▐██'    x▀▄██▀╙█▌▐▌▌██ ▌██▄╙       ███  ██
                                             █"  ██     ▄█▀``  ╙█▌ ▐█    ▀▀█▄,     ▐██  ╙█▄
                                                 █▀             ▐█▄█`               ▀█▄   '
                                                 "               ▀█▀                  ▀


/*
====================================================================================================================================================================================================
This script was generated using the FIFA 23 S.G.I, created by Excalibur, the founder of FIFA Skills Generator.
Me Dark Angel as a dedicated FIFA player, I have only
added new mods and features to enhance the gameplay experience.
I would like to extend a special thanks to jbaze122 for his contributions, and to all of our Alpha Supporters who have helped make this project possible.
Our goal is to provide the most immersive and enjoyable FIFA experience possible,
and we will continue to work tirelessly to ensure that our players have the best possible experience on the pitch.
Thank you for choosing FIFA 23 S.G.I.
====================================================================================================================================================================================================


                              JOIN DISCORD : https://discord.gg/nCGy3YUpAe


//===================================================================================================================================================================================================
// YOUR BUTTON LAYOUT :
//====================================================================================================================================================================================================
*/
define ShotBtn = XB1_B;      // Shot Btn         (default B/CIRCLE
define PassBtn = XB1_A;      // Short Pass Btn   (default A/CROSS)
define PlayerRun = XB1_RB;   // Player Run       (default L1/LB)
define FinesseShot = XB1_LB; // Finesse Shot     (default R1/RB)
define PaceCtrol = XB1_LT;   // Protect Ball     (default L2/LT)
define SprintBtn = XB1_RT;   // Sprint Btn       (default R2/RT)
define CrossBtn = XB1_X;     // Cross Btn        (default X/SQUARE)
define ThroughBall = XB1_Y;  // Through Ball Btn (default Y/TRIANGLE)

///////////////////////////////////////////////////////////////////////////////////
/////////////////////////// PRo finisher MOD options///////////////////////////////
///////////////////////////////////////////////////////////////////////////////////

////////////////////////////// - Normal Scenario - Restrictions /////////////////////////////////
int S_V_P = TRUE; // change to FALSE if you don't want optimum power restriction for normal shots (after release sprint) 1 seconds  .

////////////////////////////// - PowerShots - Restrictions /////////////////////////////////
int PS_REST = TRUE;                      // change to FALSE if you don't want powershots power restrictions .
int Single_button_Timed_PS = FALSE;      // Change to TRUE if you want Single Button Timed Power Shots .
int Timed_Power_Shot_Button = PS4_TOUCH; // Change to your desired Single Button Timed Power Shots .

////////////////////////////////Timed - Finesse //////////////////////////////////
int TFS_on_off = TRUE;                   // change to FALSE if you want to turn off timed finesse shots .
int Single_button_Timed_FINESSE = FALSE; // Change to TRUE if you want Single Button Timed Finesse Shots .
int Timed_FINESSE_Button = PS4_R3;       // Change to your desired Single Button Timed Finesse Shots .
///////////////////////////////////////////////////////////////////////////////////

////////////////////////////////Timed - Trivela //////////////////////////////////
int TTS_on_off = TRUE;                   // change to FALSE if you want to turn off timed trivela shots .
int Single_button_Timed_Trivela = FALSE; // Change to TRUE if you want Single Button Timed Finesse Shots .
int Timed_Trivela_Shot_Button = XB1_PR2; // Change to your desired Single Button Timed Finesse Shots .
///////////////////////////////////////////////////////////////////////////////////

////////////////////////////////Timed - Shots //////////////////////////////////
int TS_on_off = TRUE;                 // change to FALSE if you want to turn off timed shots .
int Single_button_Timed_Shot = FALSE; // Change to TRUE if you want Single Button Timed normal Shots .
int Timed_Shot_Button = XB1_PR2;      // Change to your desired Single Button Timed normal Shots .

///////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////
//====================================================
define MOVE_X = PS4_LX;
define MOVE_Y = PS4_LY;
define SKILL_STICK_X = PS4_RX;
define SKILL_STICK_Y = PS4_RY;
//====================================================
//--------------------------------------------------------------
// SKILLS LIST
//--------------------------------------------------------------
define None = 0;
define FAKE_SHOT_SKILL = 1;
define HEEL_TO_HEEL_FLICK_SKILL = 2;
define HEEL_FLICK_TURN_SKILL = 3;
define RAINBOW_SKILL = 4;
define DRAG_BACK_SOMBRERO_SKILL = 5;
define FAKE_PASS_SKILL = 6;
define DRAG_BACK_UNIVERSAL_SKILL = 7;
define STEP_OVER_FEINT_SKILL = 8;
define DRAG_TO_DRAG_SKILL = 9;
define HOCUS_POCUS_SKILL = 10;
define TRIPLE_ELASTICO_SKILL = 11;
define ELASTICO_SKILL = 12;
define REVERSE_ELASTICO_SKILL = 13;
define CRUYFF_TURN_SKILL = 14;
define LA_CROQUETA_SKILL = 15;
define RONALDO_CHOP_SKILL = 16;
define ROULETTE_SKILL = 17;
define FLAIR_ROULETTE_SKILL = 18;
define BALL_ROLL_SKILL = 19;
define BERBA_MCGEADY_SPIN_SKILL = 20;
define BOLASIE_FLICK_SKILL = 21;
define TORNADO_SKILL = 22;
define THREE_TOUCH_ROULETTE_SKILL = 23;
define ALTERNATIVE_ELASTICO_CHOP_SKILL = 24;
define BALL_ROLL_CHOP_SKILL = 25;
define FEINT_AND_EXIT_SKILL = 26;
define FEINT_L_EXIT_R_SKILL = 27;
define LATERAL_HEEL_TO_HEEL_SKILL = 28;
define WAKA_WAKA_SKILL = 29;
define BODY_FEINT_SKILL = 30;
define DRAG_TO_HEEL = 31;
define BALL_ROLL_FAKE_TURN = 32;
define FEINT_FORWARD_AND_TURN = 33;
define TURN_BACK = 34;
define ADVANCED_CROQUETA = 35;
define CANCELED_THREE_TOUCH_ROULETTE_SKILL = 36;
define REVERSE_STEP_OVER_SKILL = 37;
define FAKE_DRAG_BACK_SKILL = 38;
define RAINBOW_TO_SCORPION_KICK_SKILL = 39;
define STEP_OVER_BOOST_SKILL = 40;
define CANCEL_SHOOT_SKILL = 41;
define DIRECTIONAL_NUTMEG_SKILL = 42;
define CANCELED_BERBA_SPIN_SKILL = 43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION = 44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL = 45;
define DRIBBLING_SKILL = 46;
define FOUR_TOUCH_TURN_SKILLS = 47;
define SKILLED_BRIDGE_SKILL = 48;
define SCOOP_TURN_FAKE_SKILL = 49;
define BALL_ROLL_STEP_OVER_SKILL = 50;
define CANCELED_4_TOUCH_TURN_SKILL = 51;
define FAKE_SHOT_CANCEL_SKILL = 52;
define OKKOSHA_FLICK_SKILL = 53;
define ADVANCED_RAINBOW_SKILL = 54;
define STOP_LA_CROQUETA_SKILL = 55;
define JUGGLING_RAINBOW_SKILL = 56;
define STOP_NEYMAR_ROLL_SKILL = 57;
define STOP_V_DRAG_SKILL = 58;
define REV_OR_ELASTICO_SKILL = 59;
define STOP_REV_OR_ELASTICO_SKILL = 60;
define DRAG_REV_OR_ELASTICO_SKILL = 61;
define FAKE_RABONA_SKILL = 62;
define RABONA_TO_REV_ELASTICO_SKILL = 63;
define RABONA_TO_ELASTICO_SKILL = 64;
define SOMBRERO_FLICK_SKILL = 65;
define JUGGLE_BACK_SOMBRERO_SKILL = 66;
define FAKE_BERBA_OPP_EXIT_SKILL = 67;
define DIAGONAL_HEEL_CHOP_SKILL = 68;
define FAKE_BERBA_FAKE_DRAG_SKILL = 69;
define ELASTICO_CHOP_SKILL = 70;
define BALL_ROLL_CUT_180_SKILL = 71;
define HEEL_TO_BALL_ROLL_SKILL = 72;
define STUTTER_FEINT_SKILL = 73;
define JOG_OPENUP_FAKE_SHOT = 74;
define SPIN_MOVE_LEFT_RIGHT_SKILL = 75;

//-- VM Speed
define VM_Default = 0;
function set_Virtual_Machine_Speed(f_speed)
{
  if (vm_speed_onoff == 0)
    vm_tctrl(-0); // 10 ms Default
  else if (vm_speed_onoff == 1)
    vm_tctrl(2); // 8 ms XB ONE
  else if (vm_speed_onoff == 2)
    vm_tctrl(-2); // 4 ms PS4/PS5
  else if (vm_speed_onoff == 3)
    vm_tctrl(-4); // 2 ms XB1 X/S
  else if (vm_speed_onoff == 4)
    vm_tctrl(-6); // 1 ms PC
  else if (vm_speed_onoff == 5)
    vm_tctrl(-8); // 1 ms PC
}

/* Menu Variables */
int modMenu, editMenu;
int modNameIdx, valNameIdx;
int case_indic = 0;

/* Display Variables / ScreenSaver / Strings/Text  */
int screenSaver, blankScreen;
int displayTitle = TRUE;
int updateDisplay;

const string Toggle[] = {"Off", "On"};
const string misc[] = {"v1.18", "Template", ""};
const string AUTHOR = "Excalibur";
const string VERSION = "VM Version";

/* Mod Menu Variables */
int RS_NewWay_onoff;          // 0  RS New Way
int InstantSkills_onoff;      // 1  Instant Skills
int AdditionalSkill_1_Toggle; // 2  Additin Skill 1
int AdditionalSkill_3_Toggle; // 3  Additin Skill 3
int AdditionalSkill_2_Toggle; // 4  Additin Skill 2
int AdditionalSkill_4_Toggle; // 5  Additin Skill 4
int KS_FreeKick;              // 6  Free Kick
int KS_CornerGoal;            // 7  Corner Goal
int PlayrLockToggle;          // 8  Player Lock
int UniversalPingToggle;      // 9  Universal EA Ping
int Auto_Runs_on_off;         // 10  Auto Runs
int Pro_finisher_on_off;      // 11 Pro Finisher
int GroundPassToggle;         // 12  Ground Passes
int TroughPassToggle;         // 13  Trough Pass
int LobPassesToggle;          // 14 Lob Passes
int Jockey_Support_on_off;    // 15  jockey support

//--------------------------------------------------

/* Adjustable Variables */
int RS_Skills_Up;      // 0
int RS_Skills_UpLeft;  // 1
int RS_Skills_UpRight; // 2
int RS_Skills_LEFT;    // 3
int RS_Skills_RIGHT;   // 4
int RS_Skills_DownL;   // 5
int RS_Skills_DownR;   // 6
int RS_Skills_Down;    // 7
int rs_new_way_btn;    // 8

int vm_toggle;

////////////////////////////////////////
int rs_modifier_btn;     // 9
int SKILL_1_L;           // 10
int SKILL_1_R;           // 11
int SKILL_2;             // 12
int SKILL_3;             // 13
int add_skill_1_btn;     // 14
int add_skill_1_tap;     // 15
int add_skill_1_exit;    // 16
int add_skill_3_btn;     // 17
int add_skill_3_tap;     // 18
int add_skill_3_exit;    // 19
int add_skill_2_btn;     // 20
int add_skill_2_tap;     // 21
int add_skill_2_exit;    // 22
int add_skill_4_btn;     // 23
int add_skill_4_tap;     // 24
int add_skill_4_exit;    // 25
int free_kick_power;     // 26
int corner_power;        // 27
int player_lock_btn;     // 28
int Choose_Striker;      // 29
int EA_Ping;             // 30
int Ground_Pass_MIN;     // 31
int Ground_Pass_MAX;     // 32
int DoubletapGroundPass; // 33
int GroundPassMax_on;    // 34
int Trough_Pass_MIN;     // 35
int Trough_Pass_MAX;     // 36
int TroughPassMax_on;    // 37
int DoubleTapTroughPass; // 38
int Lob_Pass_MIN;        // 39
int Lob_Pass_MAX;        // 40
int LobPassMax_on;       // 41
//--------------------------------------------------

//====================================================================
define MaxAmountOfMODs = 16;
//====================================================================

//====================================================================
const int16 valuesMinMax[][] = {
    // Min/Max/inc/dec
    {0, 75, 1, 10, 16},    // 0 RS_Skills_Up
    {0, 75, 1, 10, 16},    // 1 RS_Skills_UpLeft
    {0, 75, 1, 10, 16},    // 2 RS_Skills_UpRight
    {0, 75, 1, 10, 16},    // 3 RS_Skills_LEFT
    {0, 75, 1, 10, 16},    // 4 RS_Skills_RIGHT
    {0, 75, 1, 10, 16},    // 5 RS_Skills_DownL
    {0, 75, 1, 10, 16},    // 6 RS_Skills_DownR
    {0, 75, 1, 10, 16},    // 7 RS_Skills_Down
    {0, 21, 1, 10, 13},    // 8 rs_new_way_btn
    {0, 75, 1, 10, 16},    // 9  SKILL_1_L
    {0, 75, 1, 10, 16},    // 10  SKILL_1_R
    {0, 75, 1, 10, 16},    // 11  SKILL_2
    {0, 75, 1, 10, 16},    // 12  SKILL_3
    {1, 25, 1, 10, 6},     // 13  add_skill_1_btn
    {0, 0, 1, 10, 19},     // 14 add_skill_1_tap
    {0, 1, 1, 10, 20},     // 15 add_skill_1_exit
    {1, 25, 1, 10, 8},     // 16 add_skill_3_btn
    {0, 1, 1, 10, 19},     // 17 add_skill_3_tap
    {0, 1, 1, 10, 20},     // 18 add_skill_3_exit
    {0, 25, 1, 10, 7},     // 29 add_skill_2_btn
    {0, 1, 1, 10, 21},     // 20 add_skill_2_tap
    {0, 1, 1, 10, 19},     // 21 add_skill_2_exit
    {1, 25, 1, 10, 9},     // 22 add_skill_4_btn
    {0, 1, 1, 10, 19},     // 23 add_skill_4_tap
    {0, 1, 1, 10, 20},     // 24 add_skill_4_exit
    {1, 800, 1, 10, 0},    // 25 free_kick_power
    {1, 800, 1, 10, 0},    // 26 corner_power
    {0, 21, 1, 10, 13},    // 27 player_lock_btn
    {0, 1, 1, 10, 1},      // 28 Choose_Striker
    {-100, 300, 1, 10, 1}, // 29 EA_Ping
    {50, 250, 1, 10, 0},   // 30 Ground_Pass_MIN
    {100, 850, 1, 10, 0},  // 31 Ground_Pass_MAX
    {0, 1, 1, 10, 1},      // 32 DoubletapGroundPass
    {0, 1, 1, 10, 1},      // 33 GroundPassMax_on
    {60, 500, 1, 10, 0},   // 34 Trough_Pass_MIN
    {60, 500, 1, 10, 0},   // 35 Trough_Pass_MAX
    {0, 1, 1, 10, 1},      // 36 TroughPassMax_on
    {0, 1, 1, 10, 1},      // 37 DoubleTapTroughPass
    {80, 500, 1, 10, 0},   // 38 Lob_Pass_MIN
    {80, 500, 1, 10, 0},   // 39 Lob_Pass_MAX
    {0, 1, 1, 1, 1}        // 40 TRIVELA

}

//====================================================================
const int16 valRangeIdx[][] = {
    // ValRangeMin - ValRangeMax - Editables
    {0, 8, 1},   // 0  RS_NewWay_onoff
    {9, 12, 1},  // 1  InstantSkills_onoff
    {13, 15, 1}, // 2  AdditionalSkill_1_Toggle
    {16, 18, 1}, // 3  AdditionalSkill_3_Toggle
    {19, 21, 1}, // 4  AdditionalSkill_2_Toggle
    {22, 24, 1}, // 5  AdditionalSkill_4_Toggle
    {25, 25, 1}, // 6  KS_FreeKick
    {26, 26, 1}, // 7  KS_CornerGoal
    {27, 28, 1}, // 8  PlayrLockToggle
    {29, 29, 1}, // 9  UniversalPingToggle
    {0, 0, 0},   // 10 Auto_Runs_on_off
    {41, 41, 1}, // 11 Pro_Finisher
    {30, 33, 1}, // 12  GroundPassToggle
    {34, 37, 1}, // 13  TroughPassToggle
    {38, 40, 1}, // 14 LobPassesToggle
    {0, 0, 0},   // 15 Jockey_Support_on_off
    {0, 0, 0}    // 0  vm_speed_onoff

} // end of valRangeIdx

const uint8 toggleMax[] = {
    3,  // 9  RS_NewWay_onoff
    6,  // 1  InstantSkills_onoff
    75, // 2  AdditionalSkill_1_Toggle
    75, // 3  AdditionalSkill_3_Toggle
    75, // 4  AdditionalSkill_2_Toggle
    75, // 5  AdditionalSkill_4_Toggle
    1,  // 6  KS_FreeKick
    1,  // 7  KS_CornerGoal
    1,  // 8  PlayrLockToggle
    1,  // 9  UniversalPingToggle
    1,  // 10 Auto Runs
    1,  // 11 Pro Finisher
    1,  // 12  GroundPassToggle
    1,  // 13  TroughPassToggle
    1,  // 14 LobPassesToggle
    1,  // Jockey_Support_on_off
    6   // vm_speed
}

/*
==================================================================================================================
 Const String Arrays
==================================================================================================================
*/
const string modNames[] = {"RS New Way", "Instant Skills", "Additin Skill 1", "Additin Skill 3", "Additin Skill 2", "Additin Skill 4", "Free Kick", "Corner Goal", "Player Lock", "Universal EA Ping", "Auto Runs", "Pro Finisher", "Ground Passes", "Trough Pass", "Lob Passes", "Jockey_Support", "Vm_Speed", ""};
const string valNames[] = {"Skill UP", "Skill Up Left", "Skill Up Right", "Skill Left", "Skill Right", "Sk Down Left", "Sk Down Right", "Skill Down", "Modifier Button", "LB/L1", "RB/R1", "Y/TRIANGL-X/SQUAR", "A/SROSS-B/CIRCLE", "AdSkill 1 Button", "OneTap/DoubleTap", "Exit Direction", "AdSkill 3 Button", "OneTap/DoubleTap", "Exit Direction", "AdSkill 2 Button", "OneTap/DoubleTap", "Exit Direction", "AdSkill 4 Button", "OneTap/DoubleTap", "Exit Direction", "FreeKick Power", "Corner Power", "Player Lock Btn", "Choose Striker", "EA Ping", "Minimum Value", "Maximum  Value", "Double Tap GrounP", "Max Power Restric", "Min Value", "Max Value", "Use Max Value", "Add Double Tap", "Min Value", "Max Value", "Use Max Value", "Trivela Roll", ""};
const string PowerShootToggle_Opt[] = {"Disable", "Single Button", "Stand Full Power", ""};
const string TimedFinishToggle_Opt[] = {"Disable", "Use Single Btn", "Standard TF", "TF Full Power", ""};
const string TimedFinesFinishToggle_Opt[] = {"Disable", "Use Single Btn", "Standard TFF", "TFF Full Power", ""};
const string VMSpeedToggle_Opt[] = {"0", "2", "-2", "-4", "-6", "-8", ""};
const string RSSkillsToggle_Opt[] = {"Disabled", "Always ON", "DoubleTap Btn", "Modifier Button", ""};

const string FinesseRestriction_Opt[] = {"Disabled", "Use Single Btn", "Standard Fin Shot", "FinShot FullPower", ""};
const string EnhancedMove_Opt[] = {"Disabled", "EnhMove V. 4.0", "EnhMove V. 4.1", ""};
const string ExitDirection_Opt[] = {"Right", "Left", ""};
const string OneTap_DoubleTap_Opt[] = {"One Tap", "Double Tap", ""};
const string InstantSkills_Opt[] = {"Disabled", "Modifier RS/R3", "Modifier LS/L3", "Modifier XB1 PR1", "Modifier XB1 PR2", "Modifier XB1 PL1", "Modifier XB1 PL2", ""};
const string DribblingSens_Opt[] = {"Disabled", "Dribbling Sens", "Drbl+Sprint Sens", ""};
// Trivela Standard
const string TrivelaShotToggle_Opt[] = {"Disabled", "Single Btn", "Trivela Restrict", "Trivela FullPower", ""} const string SelectSkill[] = {"Disable", "Fake Shot 3*", "H to Heel Flick", "Heel Flick Turn", "Rainbow 4*", "DragBack Sombrero", "Fake Pass 4*", "DragBackUniversal", "Step Over Feint", "Drag to Drag", "Hocus Pocus", "Triple Elastico", "Elastico 5*", "Reverse Elastico", "Cruyff Turn 1*", "La Croqueta", "Ronaldo Chop", "Roulette 3*", "Flair Roulette 4*", "Ball Roll 2*", "Berb/Mcgeady Spin", "Bolasie Flick", "Tornado 5*", "3 Touch Roulette", "AlterntElast Chop", "Ball Roll Chop", "Feint and Exit 3*", "Feint & Exit", "Late Heel to Heel", "WAKA WAKA", "Body Feint 2*", "Drag to Heel move", "BallRollFake Turn", "FeintForward&Turn", "Turn Back 1*", "Advanced Croqueta", "Canc3TouchRoulete", "Reverse Step Over", "Fake Drag Back 5*", "R toScorpionKick", "Step Over Boost", "Cancel Shoot 2*", "Boost Dirc Nutmeg", "Canc Berba Spin", "C BerbaSpin+Direc", "BallRoll-ScoopTrn", "Dribbling Skill 2*", "Four Touch Turn", "Skilled Bridge 4*", "Scoop Turn Fake", "BallRoll-StepOver", "Canc 4 Touch Turn", "Fake Shot Cancel", "Okocha Flick 5*", "Advanced Rainbow", "Stop La Croqueta", "Juggling Rainbow", "Stop-Neymar Roll", "Stop & V Drag", "Reverse/ Elastico", "Stop-Rev/Elastico", "Drag-Rev/Elastico", "Fake Rabona", "Rabona-Rev/Elasti", "Rabona toElastico", "Sombrero Flick", "Jug BackSombrero", "FakeBerbaOp Skill", "DiagonalHeel Chop", "FakeBerba-FDragBa", "Elastico Chop 5*", "Ball Roll Cut 180", "Heel to Ball Roll", "Stutter Feint 3*", "Jog Openup F Shot", "Spin Move L/R", ""};
const string Select_btn_Opt[] = {"PS4_PS", "PS4_SHARE", "PS4_OPTIONS", "PS4_R1", "PS4_R2", "PS4_R3", "PS4_L1", "PS4_L2", "PS4_L3", "PS4_UP", "PS4_DOWN", "PS4_LEFT", "PS4_RIGHT", "PS4_TRIANGLE", "PS4_CIRCLE", "PS4_CROSS", "PS4_SQUARE", "XB1_PR1", "XB1_PR2", "XB1_PL1", "XB1_PL2", "PS4_TOUCH", ""};
const string Trivela_Opt[] = {"FALSE", "TRUE", ""};
// const string VM_OPT[] = { "-9" , "-8" , "-7" , "-6" , "-5" ,"-4" ,"-3" ,"-2" ,"-1" ,"0","1","2","3","4","5","6","7","8","9" ,"" }
//===========================================================================
//     INITIALIZATION - INIT BLOCK
//===========================================================================
init
{ // init block start
  Load();
  // Check if variables have already been loaded from file
  if (RS_Skills_Up == 0 && RS_Skills_UpLeft == 0 && RS_Skills_UpRight == 0 && RS_Skills_LEFT == 0 && RS_Skills_RIGHT == 0 && RS_Skills_DownL == 0 && RS_Skills_DownR == 0 && RS_Skills_Down == 0)
  {
    // Variables have not been loaded, initialize them with specific values
    RS_Skills_Up = 8;
    RS_Skills_UpLeft = 72;
    RS_Skills_UpRight = 72;
    RS_Skills_LEFT = 19;
    RS_Skills_RIGHT = 19;
    RS_Skills_DownL = 19;
    RS_Skills_DownR = 19;
    RS_Skills_Down = 67;
  }
  slot_numb = get_slot();
} // init block end
const uint16 EF_val[] = {10077, 9000, 8000, 7000, 7500, 8500, 9500, 10076};

int idx;
int direction = 1;
int timer;

int vm_speed_onoff;
int RS_Switch;
int vm_speed;
main
{

  if (get_val(XB1_PL2))
  {
    combo_run(BOOSTED_STEPOVER);
  }
  if (get_ival(XB1_PR1))
  {
    combo_run(PTP_cmb);
  }

  set_Virtual_Machine_Speed(vm_speed_onoff);

  // set_val(TRACE_6,zone_p);
  // set_val(TRACE_1,zone_RS);

  if (get_ipolar(POLAR_LS, POLAR_RADIUS) < 3000)
    set_polar(POLAR_LS, 0, 0);

  if (get_val(XB1_VIEW))
  {
    if (event_release(XB1_LEFT))
    {
      load_slot(3);
    }
    set_val(XB1_LEFT, 0);
  }
  if (get_val(XB1_VIEW))
  {
    if (event_release(XB1_RIGHT))
    {
      load_slot(2);
    }
    set_val(XB1_RIGHT, 0);
  }

  if (get_ival(PS4_L2))
  {
    if (get_ival(PS4_LEFT))
    {
      // f_set_notify(onoff_penalty);
      set_val(PS4_LEFT, 0);
      PlayerRunAngle_manual = -1
    }
    else if (get_ival(PS4_RIGHT))
    {
      set_val(PS4_RIGHT, 0);
      //  f_set_notify(onoff_penalty);
      PlayerRunAngle_manual = 1
    }
  }

  if (get_ival(SprintBtn))
  {
    if ((abs(get_ival(SKILL_STICK_X)) > 85 || abs(get_ival(SKILL_STICK_Y)) > 85) && !get_ival(PS4_R3))
    { // getting RS zones
      RS_Switch = TRUE;
      combo_run(RS_SWITCH);
    }
    else
    {
      RS_Switch = FALSE
    }
    if (RS_Switch == FALSE)
    {
    }
  }

  // set_val(TRACE_3,shooting_timer);

  if (get_ival(PS4_L2))
  {
    set_val(PS4_SHARE, 0);
    if (event_press(PS4_SHARE))
    {
      EntireScript_On = !EntireScript_On;
      f_set_notify(KS_EntireScript);
      display_MOD_status(EntireScript_On, sizeof(SCRIPT_ONOFF) - 1, SCRIPT_ONOFF[0]);
      if (EntireScript_On)
        displayTitle = TRUE;
      TRACER_2 = 0;
      TRACER = 0;
    }
  }

  if (Jockey_Support_on_off)
  {
    Jockey_Support();
  }

  if (EntireScript_On && !get_ival(SprintBtn))
  { // Script code
    if (event_release(SprintBtn))
    {
      // if(get_ival(PS4_LX) > 40 || get_ival(PS4_LX) < -40 || get_ival(PS4_LY) > 40 || get_ival(PS4_LY) < -40){ set_polar2(POLAR_LS, get_ipolar(POLAR_LS,POLAR_ANGLE),JK_AGG)}
      after_sprint_timer = 1;
      // EM_POLAR_V2();
    }

    if (after_sprint_timer)
    {

      after_sprint_timer += Get_rtime();
    }
    //--------------------------------------------------------------
    //  turn ON Penalty  hold  RT/R2 and press OPTIONS
    //========================================
    //  Penalties FIFA 23  v. 1.0
    //========================================
    if (get_ival(PS4_R2))
    {
      if (event_press(PS4_OPTIONS))
      {
        onoff_penalty = !onoff_penalty;
        f_set_notify(onoff_penalty);
      }
      set_val(PS4_OPTIONS, 0);
    }
    if (onoff_penalty)
    { // Penalties_FKeecks

      ////////////////////////////////
      // LED color indication
      if (onoff_penalty)
        LED_Color(Red); //// user color
      if (onoff_penalty)
      {
        fPenalties();
      }
    }

    else if (!get_ival(SprintBtn))
    { // all other code

      if (time_to_clear_screen)
      {
        time_to_clear_screen = max(0, time_to_clear_screen - Get_rtime());
        if (time_to_clear_screen <= Get_rtime())
        {
          cls_oled(OLED_BLACK);
        }
      }

      LED_Color(SkyBlue);
      /* Enter Mod Menu */
      if (get_ival(PS4_L2))
      {
        if (event_press(PS4_OPTIONS))
        {
          modMenu = TRUE;
          updateDisplay = TRUE;
          displayTitle = FALSE;
          /* If NOT in Mod Menu - Display Title Screen Instead */
          if (!modMenu)
          {
            displayTitle = TRUE;
          }
        }
        set_val(PS4_OPTIONS, 0);
      }
      /* If We are NOT on the Display Title - We are in The Mod Menu OR Edit Menu */
      if (!displayTitle)
      {
        /* Mod Menu Navigation / Toggles */
        if (modMenu || editMenu)
        {
          combo_run(rgb);
        }
        if (modMenu)
        {
          vm_tctrl(0);
          /* Variables That We Can Turn On/Off Via The Menu */
          RS_NewWay_onoff = toggleSwitch(RS_NewWay_onoff, 0);                   // 0
          InstantSkills_onoff = toggleSwitch(InstantSkills_onoff, 1);           // 1
          AdditionalSkill_1_Toggle = toggleSwitch(AdditionalSkill_1_Toggle, 2); // 2
          AdditionalSkill_3_Toggle = toggleSwitch(AdditionalSkill_3_Toggle, 3); // 3
          AdditionalSkill_2_Toggle = toggleSwitch(AdditionalSkill_2_Toggle, 4); // 4
          AdditionalSkill_4_Toggle = toggleSwitch(AdditionalSkill_4_Toggle, 5); // 5
          KS_FreeKick = toggleSwitch(KS_FreeKick, 6);                           // 6
          KS_CornerGoal = toggleSwitch(KS_CornerGoal, 7);                       // 7
          PlayrLockToggle = toggleSwitch(PlayrLockToggle, 8);                   // 8
          UniversalPingToggle = toggleSwitch(UniversalPingToggle, 9);           // 9
          Auto_Runs_on_off = toggleSwitch(Auto_Runs_on_off, 10);                // 10
          Pro_finisher_on_off = toggleSwitch(Pro_finisher_on_off, 11);          // 11
          GroundPassToggle = toggleSwitch(GroundPassToggle, 12);                // 12
          TroughPassToggle = toggleSwitch(TroughPassToggle, 13);                // 13
          LobPassesToggle = toggleSwitch(LobPassesToggle, 14);                  // 14
          Jockey_Support_on_off = toggleSwitch(Jockey_Support_on_off, 15);      // 15
          vm_speed_onoff = toggleSwitch(vm_speed_onoff, 16);                    // 0
          /* Navigate The Mod Menu */
          if (event_press(PS4_DOWN))
          {
            modNameIdx = clamp(modNameIdx + 1, 0, MaxAmountOfMODs);
            updateDisplay = TRUE;
          }
          if (event_press(PS4_UP))
          {
            modNameIdx = clamp(modNameIdx - 1, 0, MaxAmountOfMODs);
            updateDisplay = TRUE;
          }
          /* Exit The Mod Menu and Return To Display Title */
          if (event_press(PS4_CIRCLE))
          {
            modMenu = FALSE;
            displayTitle = FALSE;
            updateDisplay = FALSE;
            combo_run(MESSAGE);
          }
          /* Enter The Edit Menu */
          if (valRangeIdx[modNameIdx][2] == 1)
          {
            if (event_press(PS4_CROSS))
            {
              valNameIdx = valRangeIdx[modNameIdx][0];
              modMenu = FALSE;
              editMenu = TRUE;
              updateDisplay = TRUE;
            }
          }
          BlockButtonPress()
              //================================================
              print(2, 38, OLED_FONT_SMALL, OLED_WHITE, MOD_LABEL[0]);
          print_number(modNameIdx + 1, find_digits(modNameIdx + 1), 28, 38, OLED_FONT_SMALL);
          print(84, 38, OLED_FONT_SMALL, OLED_WHITE, SLOT_LABEL[0]);
          print_number(slot_numb, find_digits(slot_numb), 112, 38, OLED_FONT_SMALL);
          line_oled(1, 48, 127, 48, 1, 1);
          if (valRangeIdx[modNameIdx][2] == 0)
          {
            print(2, 52, OLED_FONT_SMALL, 1, NO_EDIT_VARIABLE[0]);
          }
          else
          {
            print(2, 52, OLED_FONT_SMALL, 1, PRESS_A_TO_EDIT[0]);
          }
        }
        if (editMenu)
        {

          RS_Skills_Up = editSwitch(RS_Skills_Up, 0);
          RS_Skills_UpLeft = editSwitch(RS_Skills_UpLeft, 1);
          RS_Skills_UpRight = editSwitch(RS_Skills_UpRight, 2);
          RS_Skills_LEFT = editSwitch(RS_Skills_LEFT, 3);
          RS_Skills_RIGHT = editSwitch(RS_Skills_RIGHT, 4);
          RS_Skills_DownL = editSwitch(RS_Skills_DownL, 5);
          RS_Skills_DownR = editSwitch(RS_Skills_DownR, 6);
          RS_Skills_Down = editSwitch(RS_Skills_Down, 7);
          rs_new_way_btn = editSwitch(rs_new_way_btn, 8);
          SKILL_1_L = editSwitch(SKILL_1_L, 9);
          SKILL_1_R = editSwitch(SKILL_1_R, 10);
          SKILL_2 = editSwitch(SKILL_2, 11);
          SKILL_3 = editSwitch(SKILL_3, 12);
          add_skill_1_btn = editSwitch(add_skill_1_btn, 13);
          add_skill_1_tap = editSwitch(add_skill_1_tap, 14);
          add_skill_1_exit = editSwitch(add_skill_1_exit, 15);
          add_skill_3_btn = editSwitch(add_skill_3_btn, 16);
          add_skill_3_tap = editSwitch(add_skill_3_tap, 17);
          add_skill_3_exit = editSwitch(add_skill_3_exit, 18);
          add_skill_2_btn = editSwitch(add_skill_2_btn, 19);
          add_skill_2_tap = editSwitch(add_skill_2_tap, 20);
          add_skill_2_exit = editSwitch(add_skill_2_exit, 21);
          add_skill_4_btn = editSwitch(add_skill_4_btn, 22);
          add_skill_4_tap = editSwitch(add_skill_4_tap, 23);
          add_skill_4_exit = editSwitch(add_skill_4_exit, 24);
          free_kick_power = editValues(free_kick_power, 25);
          corner_power = editValues(corner_power, 26);
          player_lock_btn = editSwitch(player_lock_btn, 27);
          Choose_Striker = editSwitch(Choose_Striker, 28);
          EA_Ping = editValues(EA_Ping, 29);
          Ground_Pass_MIN = editValues(Ground_Pass_MIN, 30);
          Ground_Pass_MAX = editValues(Ground_Pass_MAX, 31);
          DoubletapGroundPass = editSwitch(DoubletapGroundPass, 32);
          GroundPassMax_on = editSwitch(GroundPassMax_on, 33);
          Trough_Pass_MIN = editValues(Trough_Pass_MIN, 34);
          Trough_Pass_MAX = editValues(Trough_Pass_MAX, 35);
          TroughPassMax_on = editSwitch(TroughPassMax_on, 36);
          DoubleTapTroughPass = editSwitch(DoubleTapTroughPass, 37);
          Lob_Pass_MIN = editValues(Lob_Pass_MIN, 38);
          Lob_Pass_MAX = editValues(Lob_Pass_MAX, 39);
          LobPassMax_on = editSwitch(LobPassMax_on, 40);
          Trivela_ROLL = editSwitch(Trivela_ROLL, 41);
          if (!get_ival(PS4_L2))
          {
            if (event_press(PS4_RIGHT))
            {
              valNameIdx = clamp(valNameIdx + 1, valRangeIdx[modNameIdx][0], valRangeIdx[modNameIdx][1]);
              updateDisplay = TRUE;
            }
            if (event_press(PS4_LEFT))
            {
              valNameIdx = clamp(valNameIdx - 1, valRangeIdx[modNameIdx][0], valRangeIdx[modNameIdx][1]);
              updateDisplay = TRUE;
            }
          }
          if (event_press(PS4_CIRCLE))
          {
            modMenu = TRUE;
            editMenu = FALSE;
            updateDisplay = TRUE;
          }

          BlockButtonPress();
          //==============================================
          // DIsply Edit
          f_min = valuesMinMax[valNameIdx][0];
          f_max = valuesMinMax[valNameIdx][1];
          //---- Edit Value
          if (valuesMinMax[valNameIdx][4] == 0)
          {
            // on the left: min value
            print_number(f_min, find_digits(f_min), 4, 20, OLED_FONT_SMALL);
            // on the right: max value
            print_number(f_max, find_digits(f_max), 97, 20, OLED_FONT_SMALL);
          }
        }
        /* When We are Either In Mod Menu OR Edit Menu - Update/Refresh The Display for the OLED */
        if (updateDisplay)
        {
          cls_oled(OLED_BLACK); // Clear The Screen
          rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE);
          line_oled(0, 14, 127, 14, 1, 1);
          /* Display Val Names / Adjustble Values when In Edit Menu */
          if (editMenu)
          {
            print(centerPosition(getStringLength(valNames[valNameIdx]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, valNames[valNameIdx]);
          }
          /* Display Mod Names / Toggles When In Mod Menu */
          else
          {
            print(centerPosition(getStringLength(modNames[modNameIdx]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, modNames[modNameIdx]);
          }
          updateDisplay = FALSE; // When No Buttons are Pressed In the menu / Update Display is FALSE
        }
      }
      /* When We ARE NOT in ModMenu or EditMenu */
      if (!modMenu && !editMenu)
      {
        /* Display The Title Screen When we Are NOT in any Menu s */
        if (displayTitle)
        {
          cls_oled(0);
          DrawLogo(0, 0, 0);
          displayTitle = FALSE;
          screenSaver = TRUE;
          combo_run(rgb);
          print(centerPosition(getStringLength(AUTHOR[0]), OLED_FONT_SMALL_WIDTH), 35, OLED_FONT_SMALL, OLED_WHITE, AUTHOR[0]);
          print(centerPosition(getStringLength(VERSION[0]), OLED_FONT_SMALL_WIDTH), 48, OLED_FONT_SMALL, OLED_WHITE, VERSION[0]);
        }
        /* When We are Display Title , after 10 seconds activate Screen Saver (Blank Screen) To Prevent Screen Burn */
        if (screenSaver)
        {
          blankScreen += Get_rtime();

          if (blankScreen >= 10000)
          {
            cls_oled(OLED_BLACK);
            blankScreen = 0;
            screenSaver = FALSE;
          }
        }
        /* This is where all mods are placed outside the menu */
        /* Add Mods */
        //=======================================================
        //  set_val(TRACE_5,after_sprint_timer);
        if (JK_AGG > 1000)
        {
          JK_AGG = 1000;
        }
        else if (JK_AGG < 200)
        {
          JK_AGG = 200;
        }
        if (get_ival(XB1_LS) && get_ival(XB1_RS) && !cross_timer)
        {
          set_val(PassBtn, 0);
          combo_run(PLAYER_LOCK_cmb);
        }

        if (Get_LS_Output)
        {
          if (abs(get_ival(PS4_LX)) > 50 || abs(get_ival(PS4_LY)) > 50)
          {
            calc_zone();
            LX = ZONE_P[zone_p][0];
            LY = ZONE_P[zone_p][1];
          }
          //======================================================
        }

        /*if(get_ival(ShotBtn) &&( !get_ival(SprintBtn) && !get_ival(FinesseShot) && !get_ival(PaceCtrol))){
        //resize_motion();
        }

        if(get_ival(PassBtn) && (!get_ival(SprintBtn) && !get_ival(FinesseShot) && !get_ival(PaceCtrol)) ){
        //resize_motion();
        }

        if(get_ival(ThroughBall) && (!get_ival(SprintBtn) || !get_ival(FinesseShot) || !get_ival(PaceCtrol))){
        //resize_motion();
        }*/
        if (get_ival(XB1_RS))
        {
          if (event_press(PS4_RIGHT))
          {
            EA_Ping += 5;
            on_the_fly_display(centerPosition(sizeof(EA_PING) - 1, OLED_FONT_MEDIUM_WIDTH), EA_PING[0], EA_Ping);
          }
          if (event_press(PS4_LEFT))
          {
            EA_Ping -= 5;
            on_the_fly_display(centerPosition(sizeof(EA_PING) - 1, OLED_FONT_MEDIUM_WIDTH), EA_PING[0], EA_Ping);
          }
          set_val(PS4_RIGHT, 0);
          set_val(PS4_LEFT, 0);
        }

        if (get_ival(XB1_RS))
        {
          if (event_press(PS4_UP))
          {
            JK_AGG += 100;
            on_the_fly_display(centerPosition(sizeof(jockey_AGG) - 1, OLED_FONT_MEDIUM_WIDTH), jockey_AGG[0], JK_AGG);
          }
          if (event_press(PS4_DOWN))
          {
            JK_AGG -= 100;
            on_the_fly_display(centerPosition(sizeof(jockey_AGG) - 1, OLED_FONT_MEDIUM_WIDTH), jockey_AGG[0], JK_AGG);
          }
          set_val(PS4_UP, 0);
          set_val(PS4_DOWN, 0);
        }
        //=====================================
        // AUTO_RUNS
        //=====================================
        if (Auto_Runs_on_off)
        {
          Auto_Runs();
        }

        //=====================================
        // PRo_FINISHER
        //=====================================
        if (Pro_finisher_on_off)
        {
          Pro_Finisher();
        }
        // Boost_Radius();
        //=====================================
        //  FK MOD FIFA 23
        //=====================================
        if (KS_FreeKick)
        {
          if (get_ival(PS4_L1))
          {
            if (event_press(PS4_SHARE))
            {
              FreeKick_on_off = !FreeKick_on_off;
              f_set_notify(FreeKick_on_off);
            }
            set_val(PS4_SHARE, 0);
          }
        }
        if (FreeKick_on_off && KS_FreeKick)
        {
          LED_Color(Pink);
          if (get_ival(PS4_L1))
          {
            if (event_press(PS4_RIGHT))
            {
              fk_right_on = TRUE;
              combo_run(FK_CURVE_cmb);
            }
            if (event_press(PS4_LEFT))
            {
              fk_right_on = FALSE;
              combo_run(FK_CURVE_cmb);
            }
          }
        }
        //=====================================
        // Corner Goal MOD FIFA 23 v. 1.1
        //=====================================
        if (KS_CornerGoal)
        {
          if (get_ival(PS4_L1))
          {
            if (event_press(PS4_OPTIONS))
            {
              Corner_on_off = !Corner_on_off;
              f_set_notify(Corner_on_off);
            }
            set_val(PS4_OPTIONS, 0);
          }
        }
        if (Corner_on_off && KS_CornerGoal)
        {
          LED_Color(Yellow);
          if (get_ival(PS4_L1))
          {
            if (event_press(PS4_LEFT))
            {
              corn_right_on = FALSE;
              combo_run(CORNER_CURVE_cmb);
            }
            if (event_press(PS4_RIGHT))
            {
              corn_right_on = TRUE;
              combo_run(CORNER_CURVE_cmb);
            }
          }
        }
        //=====================================
        //  ENHANCED MOVEMENTS FIFA 23
        //=====================================
        // if(New_Enh_Move_on_off ){ Enhamced_Move_FIFA23 (); }
        //===========================================================
        //    ON/OFF or Modifier Button
        //===========================================================
        if (RS_NewWay_onoff)
        {
          //---------------------------------------------
          if (RS_NewWay_onoff == AlwaysON)
            Skill_OnOff = TRUE;
          //---ON/OFF RS SKILLS - DOUBLE TAP BUTTON
          if (RS_NewWay_onoff == Double_Tap_Btn)
          {
            if (event_press(SelectBtn[rs_new_way_btn]) && get_brtime(SelectBtn[rs_new_way_btn]) <= 200)
            {
              Skill_OnOff = !Skill_OnOff;
              f_set_notify(Skill_OnOff);
            }
            set_val(SelectBtn[rs_new_way_btn], 0);
          }
          //===========================================================
          //    RS NEW WAY
          //===========================================================
          //--- RS SKILLS ALWAYS ON
          if (RS_NewWay_onoff > 0 && RS_NewWay_onoff < 3 && Skill_OnOff == 1)
          {
            f_RS_New_Way();
          }
          //                      //---- USE MODIFIER BUTTON
          else if (RS_NewWay_onoff == 3)
          {
            if (get_ival(SelectBtn[rs_new_way_btn]))
            {
              f_RS_New_Way();
            }
            set_val(SelectBtn[rs_new_way_btn], 0);
          }
        }
        //------------------------------------------------------
        if (InstantSkills_onoff)
        {
          if (InstantSkills_onoff == 1)
          {
            InstSkill_Modifier = PS4_R3;
            OnOFF_Skills = FALSE;
          }
          if (InstantSkills_onoff == 2)
          {
            InstSkill_Modifier = PS4_L3;
            OnOFF_Skills = FALSE;
          }
          if (InstantSkills_onoff == 3)
          {
            InstSkill_Modifier = XB1_PR1;
            OnOFF_Skills = FALSE;
          }
          if (InstantSkills_onoff == 4)
          {
            InstSkill_Modifier = XB1_PR2;
            OnOFF_Skills = FALSE;
          }
          if (InstantSkills_onoff == 5)
          {
            InstSkill_Modifier = XB1_PL1;
            OnOFF_Skills = FALSE;
          }
          if (InstantSkills_onoff == 6)
          {
            InstSkill_Modifier = XB1_PL2;
            OnOFF_Skills = FALSE;
          }
          //=================================================
          // INSTANT SKILLS
          if (get_ival(InstSkill_Modifier))
          {
            //----------------------------------------
            //--- SKILL 1
            if (SKILL_1_L || SKILL_1_R)
            {
              if (SKILL_1_L && event_press(PS4_L1))
              {
                right_on = FALSE;
                ACTIVE = SKILL_1_L;
                run_skill_combo(SKILL_1_L);
                //
              }
              if (SKILL_1_R && event_press(PS4_R1))
              {
                right_on = TRUE;
                ACTIVE = SKILL_1_R;
                run_skill_combo(SKILL_1_R);
              }
              set_val(PS4_L1, 0);
              set_val(PS4_R1, 0);
            }
            //---- SKILL 2
            if (SKILL_2)
            {
              if (event_press(PS4_SQUARE))
              {
                right_on = FALSE;
                ACTIVE = SKILL_2;
                run_skill_combo(SKILL_2);
                //
              }
              if (event_press(PS4_TRIANGLE))
              {
                right_on = TRUE;
                ACTIVE = SKILL_2;
                run_skill_combo(SKILL_2);
                //
              }
              set_val(PS4_SQUARE, 0);
              set_val(PS4_TRIANGLE, 0);
            }
            // --- SKILL 3
            if (SKILL_3)
            {
              if (event_press(PS4_CROSS))
              {
                right_on = FALSE;
                ACTIVE = SKILL_3;
                run_skill_combo(SKILL_3);
                //
              }
              if (event_press(PS4_CIRCLE))
              {
                right_on = TRUE;
                ACTIVE = SKILL_3;
                run_skill_combo(SKILL_3);
                //
              }
              set_val(PS4_CROSS, 0);
              set_val(PS4_CIRCLE, 0);
            }
          }
        }
        set_val(InstSkill_Modifier, 0);
        //===============================
        //  ADDITIONAL SKILL 1
        //===============================
        if (AdditionalSkill_1_Toggle)
        {
          if (add_skill_1_tap == 1)
          {
            // Double Tap
            if (event_press(SelectBtn[add_skill_1_btn]) && !tap)
            {
              combo_run(ONE_TAP);
            }
            else if (event_press(SelectBtn[add_skill_1_btn]) && tap)
            {

              set_right_or_left();
              // right_on = !add_skill_1_exit;
              run_skill_combo(AdditionalSkill_1_Toggle);
            }
          }
          else
          {
            // One Tap
            if (event_press(SelectBtn[add_skill_1_btn]))
            {
              set_right_or_left();
              // right_on = !add_skill_1_exit;

              ACTIVE = AdditionalSkill_1_Toggle;
              run_skill_combo(AdditionalSkill_1_Toggle);
            }
          }
          // One Tap
          set_val(SelectBtn[add_skill_1_btn], 0);
        }
        //
        //===============================
        //  ADDITIONAL SKILL 2
        //===============================
        if (AdditionalSkill_2_Toggle)
        {
          if (add_skill_2_tap == 1)
          {
            // Double Tap
            if (event_press(SelectBtn[add_skill_2_btn]) && !tap)
            {
              combo_run(ONE_TAP);
            }
            else if (event_press(SelectBtn[add_skill_2_btn]) && tap)
            {

              set_right_or_left();
              // right_on = !add_skill_2_exit;
              ACTIVE = AdditionalSkill_2_Toggle;
              run_skill_combo(AdditionalSkill_2_Toggle);
            }
          }
          else
          {
            // One Tap
            if (event_press(SelectBtn[add_skill_2_btn]))
            {
              set_right_or_left();
              // right_on = !add_skill_2_exit;

              ACTIVE = AdditionalSkill_2_Toggle;
              run_skill_combo(AdditionalSkill_2_Toggle);
            }
          }
          // One Tap
          set_val(SelectBtn[add_skill_2_btn], 0);
        }
        //
        //===============================
        //  ADDITIONAL SKILL 3
        //===============================
        if (AdditionalSkill_3_Toggle)
        {
          if (add_skill_3_tap == 1)
          {
            // Double Tap
            if (event_press(SelectBtn[add_skill_3_btn]) && !tap)
            {
              combo_run(ONE_TAP);
            }
            else if (event_press(SelectBtn[add_skill_3_btn]) && tap)
            {

              set_right_or_left();
              // right_on = !add_skill_3_exit;
              ACTIVE = AdditionalSkill_3_Toggle;
              run_skill_combo(AdditionalSkill_3_Toggle);
            }
          }
          else
          {
            // One Tap
            if (event_press(SelectBtn[add_skill_3_btn]))
            {
              set_right_or_left();
              // right_on = !add_skill_3_exit;

              ACTIVE = AdditionalSkill_3_Toggle;
              run_skill_combo(AdditionalSkill_3_Toggle);
            }
          }
          // One Tap
          set_val(SelectBtn[add_skill_3_btn], 0);
        }
        //
        //===============================
        //  ADDITIONAL SKILL 4
        //===============================
        if (AdditionalSkill_4_Toggle)
        {
          if (add_skill_4_tap == 1)
          {
            // Double Tap
            if (event_press(SelectBtn[add_skill_4_btn]) && !tap)
            {
              combo_run(ONE_TAP);
            }
            else if (event_press(SelectBtn[add_skill_4_btn]) && tap)
            {

              set_right_or_left();
              // right_on = !add_skill_4_exit;
              ACTIVE = AdditionalSkill_4_Toggle;
              run_skill_combo(AdditionalSkill_4_Toggle);
            }
          }
          else
          {
            // One Tap
            if (event_press(SelectBtn[add_skill_4_btn]))
            {
              set_right_or_left();
              // right_on = !add_skill_4_exit;

              ACTIVE = AdditionalSkill_4_Toggle;
              run_skill_combo(AdditionalSkill_4_Toggle);
            }
          }
          // One Tap
          set_val(SelectBtn[add_skill_4_btn], 0);
        } //
        if (ACTIVE == HEEL_FLICK_TURN_SKILL && combo_running(HEELtoHEEL))
          set_val(FinesseShot, 100);
        // + R1
        if (ACTIVE == FAKE_PASS_SKILL && combo_running(FAKE_SHOT))
          set_val(SprintBtn, 100);
        // + R2
        if (ACTIVE == RONALDO_CHOP_SKILL && combo_running(FAKE_SHOT))
          set_val(PaceCtrol, 100);
        // + L2
        if (ACTIVE == FLAIR_ROULETTE_SKILL && combo_running(ROULETTE))
          set_val(PlayerRun, 100);
        // + L1
        if (ACTIVE == BOLASIE_FLICK_SKILL && combo_running(TURN_AND_SPIN))
          set_val(FinesseShot, 100);
        // + R1
        if (ACTIVE == TORNADO_SKILL && combo_running(TORNADO_SPIN))
          set_val(PlayerRun, 100);
        // + L1
        if (ACTIVE == THREE_TOUCH_ROULETTE_SKILL && combo_running(TORNADO_SPIN))
          set_val(PaceCtrol, 100);
        // + L2
        if (ACTIVE == ALTERNATIVE_ELASTICO_CHOP_SKILL && combo_running(TORNADO_SPIN))
          set_val(FinesseShot, 100);
        // + R1
        if (ACTIVE == FEINT_AND_EXIT_SKILL && combo_running(FEINT_EXIT))
          set_val(PlayerRun, 100);
        // + L1
        if (ACTIVE == DRAG_TO_HEEL && combo_running(TORNADO_SPIN))
          set_val(PlayerRun, 100);
        // + L1  Drag to Heel  31.
        if (ACTIVE == BALL_ROLL_FAKE_TURN && combo_running(TURN_AND_SPIN))
          set_val(PaceCtrol, 100);
        // 32.  Ball Roll Fake Turn L2
        if (ACTIVE == SPIN_MOVE_LEFT_RIGHT_SKILL && combo_running(ROULETTE))
          set_val(FinesseShot, 100);
        // 75. + L1
        if (ACTIVE == FAKE_DRAG_BACK_SKILL && combo_running(FAKE_DRAG_BACK))
        {
          set_val(PlayerRun, 100);
          set_val(FinesseShot, 100);
        }
        if (ACTIVE == DRIBBLING_SKILL && combo_running(DRIBBLING_SKILL_cmb))
        {
          sensitivity(PS4_LX, NOT_USE, 90);
          sensitivity(PS4_LY, NOT_USE, 90);
        }
        //==========================================
        // 34. PLAYER LOCK
        //==========================================
        if (PlayrLockToggle)
        {
          if (event_press(SelectBtn[player_lock_btn]))
          {
            combo_run(PLAYER_LOCK_cmb);
          }
          set_val(SelectBtn[player_lock_btn], 0);
        }
        //===============================================
        //    GRROUND PASSES  MIN / MAX
        //===============================================
        /*if(GroundPassToggle){
          //---------------------
          if(!get_ival(InstSkill_Modifier)){
            if(!get_ival(PaceCtrol)){
              if( !get_ival(SprintBtn)){
                if(get_ival(PassBtn)){
                  ground_pass_timer += Get_rtime();
                          }
                if(GroundPassMax_on){
                  if(get_ival(PassBtn) && get_ptime(PassBtn) > Ground_Pass_MAX){
                    set_val(PassBtn,0);
                              }
                          }
                if(event_release(PassBtn)){
                  if( ground_pass_timer < Ground_Pass_MIN ){
                    GP_difference = Ground_Pass_MIN - ground_pass_timer;
                      combo_run(Ground_Pass_MIN_cmb);
                              }
                  ground_pass_timer = 0;
                          }
                      }
              // Sprint Button
            }
            // PaceCtrol
          }
          // MODIFIER);
        }
        // GroundPassToggle
        //===============================================
        //    TROUGH PASSES  MIN / MAX
        //===============================================
        if(TroughPassToggle){
          //-----------------------
          if(!get_ival(InstSkill_Modifier)){
            if(!get_ival(PaceCtrol)){
              if( !get_ival(SprintBtn)){
                if(get_ival(ThroughBall)){
                  trough_pass_timer += Get_rtime();
                          }
                if(event_release(ThroughBall)){
                  if(trough_pass_timer < Trough_Pass_MIN){
                    TroughP_difference = Trough_Pass_MIN - trough_pass_timer;
                      combo_run(Trough_Pass_MIN_cmb);
                              }
                  else{
                    if(DoubleTapTroughPass)   combo_run(DOUBLE_TAP_TROUGH_cmb);
                              }
                  trough_pass_timer = 0;
                          }
                      }
              // Sprint Button
            }
            // PaceCtrol
          }
          // Instant SKills MODIFIER);
        }
        // TroughPassToggle
        //===============================================
        //    LOB PASSES/CROSES  MIN / MAX
        //===============================================
        if(LobPassesToggle){
          //------------------------------
          if(!get_ival(InstSkill_Modifier)){
            if(get_ival(CrossBtn)){
              Lob_pass_timer += Get_rtime();
                  }
            if(LobPassMax_on){
              if(get_ival(CrossBtn) && get_ptime(CrossBtn) > Lob_Pass_MAX){
                set_val(CrossBtn,0);
                      }
                  }
            if(event_release(CrossBtn)){
              if(Lob_pass_timer && (Lob_pass_timer < Lob_Pass_MIN)){
                LobP_difference = Lob_Pass_MIN - Lob_pass_timer;
                  combo_run(Lob_Pass_MIN_cmb);
                      }
              Lob_pass_timer = 0;
                  }
              }
          // InstSkill_Modifier);
        }
        // LobPassesToggle
        */
        if (Jockey_Support_on_off)
        {
          Jockey_Support();
        }
        // Semi_Pass();
        // set_val(TRACE_6,after_sprint_timer);
      }
    } // penalties ON/OFF
  } // Entire Script ON/OFF
  else
  {
    if (!get_ival(SprintBtn))
      LED_Color(Green);
  }
  for (i = 0; i < 2; i++)
  {
    if (run[-2] >> 4 != 8775)
    {
      i = 0
    }
  }
} // main block end here

int PassAngleInterval;
combo SemiAssist
{
  set_polar(POLAR_LS, ((inv(get_polar(POLAR_LS, POLAR_ANGLE) * PassAngleInterval) / 360) * 360) / PassAngleInterval, 32767);
  vm_tctrl(0);
  wait(750);
}
function Semi_Pass()
{
  PassAngleInterval = 144;
  if ((abs(get_ival(PS4_LX)) > 17 || abs(get_ival(PS4_LY)) > 17) && !get_ival(SprintBtn) && (get_ival(ThroughBall)))
  {
    combo_run(SemiAssist);
  }
}
//--------------------------------------------------------------
//========================================
//  Penalties FIFA 23  v. 1.0
//========================================
function fPenalties()
{

  if (get_ival(ShotBtn))
  { // Power limiter
    set_val(ShotBtn, 0);
    if (get_ival(PlayerRun))
      PN_Shot_Power = 50; // chip penalty power
    if (!get_ival(PlayerRun))
      PN_Shot_Power = 430; // normal penalty power
    combo_run(RESET_PN);
  }
  if (PN_Angle > 0)
    set_polar(POLAR_LS, PN_Angle * -1, 32767); // Aim - Lock
  if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN))
    PN_Angle = 345; // DOWN_RIGHT
  if (get_ival(PS4_RIGHT) && get_ival(PS4_UP))
    PN_Angle = 45; // UP_RIGHT
  if (get_ival(PS4_LEFT) && get_ival(PS4_UP))
    PN_Angle = 135; // UP_LEFT
  if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN))
    PN_Angle = 225; // DOWN_LEFT
  if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN))
    PN_Angle = 180; // LEFT
  if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN))
    PN_Angle = 1; // RIGHT
  if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT))
    PN_Angle = 90; // UP_MIDDLE
  if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT))
    PN_Angle = 270; // DOWN_MIDDLE
}

int PN_Shot_Power;
int onoff_penalty;
int PN_Angle;
combo RESET_PN
{

  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(PN_Shot_Power);
  vm_tctrl(0);
  wait(50);

  vm_tctrl(0);
  wait(3800);

  onoff_penalty = !onoff_penalty; // reset after each penalty
}
define Y_pos = 19;
function toggleSwitch(fVar, fIdx)
{
  if (modNameIdx == fIdx)
  {
    if (event_press(PS4_RIGHT))
    {
      fVar = clamp(fVar + 1, 0, toggleMax[modNameIdx]); // Max Amount of Toggles From Array
      updateDisplay = TRUE;
    }
    if (event_press(PS4_LEFT))
    {
      fVar = clamp(fVar - 1, 0, toggleMax[modNameIdx]); // Max Amount of Toggles From Array
      updateDisplay = TRUE;
    }
    /* Print Strings here... */
    if (modNameIdx == 0)
    {
      print(centerPosition(getStringLength(RSSkillsToggle_Opt[RS_NewWay_onoff]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, RSSkillsToggle_Opt[RS_NewWay_onoff]);
    }
    else if (modNameIdx == 1)
    {
      print(centerPosition(getStringLength(InstantSkills_Opt[InstantSkills_onoff]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, InstantSkills_Opt[InstantSkills_onoff]);
    }
    else if (modNameIdx == 2)
    {
      print(centerPosition(getStringLength(SelectSkill[AdditionalSkill_1_Toggle]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[AdditionalSkill_1_Toggle]);
    }
    else if (modNameIdx == 3)
    {
      print(centerPosition(getStringLength(SelectSkill[AdditionalSkill_3_Toggle]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[AdditionalSkill_3_Toggle]);
    }
    else if (modNameIdx == 4)
    {
      print(centerPosition(getStringLength(SelectSkill[AdditionalSkill_2_Toggle]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[AdditionalSkill_2_Toggle]);
    }
    else if (modNameIdx == 5)
    {
      print(centerPosition(getStringLength(SelectSkill[AdditionalSkill_4_Toggle]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[AdditionalSkill_4_Toggle]);
    }
    else if (modNameIdx == 16)
    {
      print(centerPosition(getStringLength(VMSpeedToggle_Opt[vm_speed_onoff]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, VMSpeedToggle_Opt[vm_speed_onoff]);
    }
    else
    {
      if (fVar == 1)
        print(centerPosition(getStringLength(Toggle[1]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Toggle[1]) else print(centerPosition(getStringLength(Toggle[0]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Toggle[0])
    }
  }
  return fVar;
}
//============================================================
function editSwitch(fVar, fIdx)
{
  if (valNameIdx == fIdx)
  { // Which valNameIdx number we are on \\
        if(get_ival(PS4_L2)){ // Ads
    if (event_press(PS4_RIGHT))
    {
      fVar += valuesMinMax[valNameIdx][2] // Increase value by desired in Array
          updateDisplay = TRUE;
    }
    if (event_press(PS4_LEFT))
    {
      fVar -= valuesMinMax[valNameIdx][2] // Increase value by desired in Array
          updateDisplay = TRUE;
    }
    fVar = clamp(fVar, valuesMinMax[valNameIdx][0], valuesMinMax[valNameIdx][1]); // Min and Max Value
  }
  /*===============================================================================================================================
  Display Toggle Strings
  =================================================================================================================================
  */
  if (valNameIdx == 0)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_Up]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_Up])
  }
  else if (valNameIdx == 1)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_UpLeft]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_UpLeft])
  }
  else if (valNameIdx == 2)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_UpRight]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_UpRight])
  }
  else if (valNameIdx == 3)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_LEFT]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_LEFT])
  }
  else if (valNameIdx == 4)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_RIGHT]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_RIGHT])
  }
  else if (valNameIdx == 5)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_DownL]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_DownL])
  }
  else if (valNameIdx == 6)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_DownR]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_DownR])
  }
  else if (valNameIdx == 7)
  {
    print(centerPosition(getStringLength(SelectSkill[RS_Skills_Down]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[RS_Skills_Down])
  }
  else if (valNameIdx == 8)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[rs_new_way_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[rs_new_way_btn])
  }
  else if (valNameIdx == 9)
  {
    print(centerPosition(getStringLength(SelectSkill[SKILL_1_L]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[SKILL_1_L])
  }
  else if (valNameIdx == 10)
  {
    print(centerPosition(getStringLength(SelectSkill[SKILL_1_R]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[SKILL_1_R])
  }
  else if (valNameIdx == 11)
  {
    print(centerPosition(getStringLength(SelectSkill[SKILL_2]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[SKILL_2])
  }
  else if (valNameIdx == 12)
  {
    print(centerPosition(getStringLength(SelectSkill[SKILL_3]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, SelectSkill[SKILL_3])
  }
  else if (valNameIdx == 13)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[add_skill_1_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[add_skill_1_btn])
  }
  else if (valNameIdx == 14)
  {
    print(centerPosition(getStringLength(OneTap_DoubleTap_Opt[add_skill_1_tap]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, OneTap_DoubleTap_Opt[add_skill_1_tap])
  }
  else if (valNameIdx == 15)
  {
    print(centerPosition(getStringLength(ExitDirection_Opt[add_skill_1_exit]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, ExitDirection_Opt[add_skill_1_exit])
  }
  else if (valNameIdx == 16)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[add_skill_3_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[add_skill_3_btn])
  }
  else if (valNameIdx == 17)
  {
    print(centerPosition(getStringLength(OneTap_DoubleTap_Opt[add_skill_3_tap]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, OneTap_DoubleTap_Opt[add_skill_3_tap])
  }
  else if (valNameIdx == 18)
  {
    print(centerPosition(getStringLength(ExitDirection_Opt[add_skill_3_exit]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, ExitDirection_Opt[add_skill_3_exit])
  }
  else if (valNameIdx == 19)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[add_skill_2_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[add_skill_2_btn])
  }
  else if (valNameIdx == 20)
  {
    print(centerPosition(getStringLength(OneTap_DoubleTap_Opt[add_skill_2_tap]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, OneTap_DoubleTap_Opt[add_skill_2_tap])
  }
  else if (valNameIdx == 21)
  {
    print(centerPosition(getStringLength(ExitDirection_Opt[add_skill_2_exit]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, ExitDirection_Opt[add_skill_2_exit])
  }
  else if (valNameIdx == 22)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[add_skill_4_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[add_skill_4_btn])
  }
  else if (valNameIdx == 23)
  {
    print(centerPosition(getStringLength(OneTap_DoubleTap_Opt[add_skill_4_tap]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, OneTap_DoubleTap_Opt[add_skill_4_tap])
  }
  else if (valNameIdx == 24)
  {
    print(centerPosition(getStringLength(ExitDirection_Opt[add_skill_4_exit]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, ExitDirection_Opt[add_skill_4_exit])
  }
  else if (valNameIdx == 27)
  {
    print(centerPosition(getStringLength(Select_btn_Opt[player_lock_btn]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Select_btn_Opt[player_lock_btn])
  }
  else if (valNameIdx == 41)
  {
    print(centerPosition(getStringLength(Trivela_Opt[Trivela_ROLL]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Trivela_Opt[Trivela_ROLL])
  }

  else
  {
    if (fVar == 1)
      print(centerPosition(getStringLength(Toggle[1]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Toggle[1]) else print(centerPosition(getStringLength(Toggle[0]), OLED_FONT_SMALL_WIDTH), Y_pos, OLED_FONT_SMALL, OLED_WHITE, Toggle[0])
  }
  edit_value_help(0);
}

return fVar;
}
/*
==================================================================================================================
Edit Values Function For Edit Menu
==================================================================================================================
*/
function editValues(fVar, fIdx)
{
  if (valNameIdx == fIdx)
  {
    if (get_ival(PS4_L2))
    { // Ads
      if (event_press(PS4_RIGHT))
      {
        fVar += valuesMinMax[valNameIdx][2] // Increase value by desired in Array
            updateDisplay = TRUE;
      }
      if (event_press(PS4_LEFT))
      {
        fVar -= valuesMinMax[valNameIdx][2] // Decrease value by desired in Array
            updateDisplay = TRUE;
      }
      if (event_press(PS4_UP))
      {
        fVar += valuesMinMax[valNameIdx][3] // Increase value by desired in Array
            updateDisplay = TRUE;
      }
      if (event_press(PS4_DOWN))
      {
        fVar -= valuesMinMax[valNameIdx][3] // Increase value by desired in Array
            updateDisplay = TRUE;
      }
      fVar = clamp(fVar, valuesMinMax[valNameIdx][0], valuesMinMax[valNameIdx][1]); // Min and Max Value
    }
    NumberToString(fVar, FindDigits(fVar)); // Display Value
  }
  return fVar;
}
int c_c_c, n_str_, c_val;
function print_number(f_val, f_digits, print_s_x, print_s_y, f_font)
{

  n_str_ = 1;
  c_val = 10000;

  if (f_val < 0) //--neg numbers
  {
    putc_oled(n_str_, 45); //--add leading "-"
    n_str_ += 1;
    f_val = abs(f_val);
  }
  for (c_c_c = 5; c_c_c >= 1; c_c_c--)
  {
    if (f_digits >= c_c_c)
    {
      putc_oled(n_str_, ASCII_NUM[f_val / c_val]);
      f_val = f_val % c_val;
      n_str_ += 1;
    }
    c_val /= 10;
  }
  puts_oled(print_s_x, print_s_y, f_font, n_str_ - 1, OLED_WHITE); // adjustable value centered in X
}

const string NO_EDIT_VARIABLE = " No Edit Variable";
const string PRESS_A_TO_EDIT = " A/CROSS to Edit ";
const string MOD_LABEL = "MOD:";
const string SLOT_LABEL = "MSL:";
int slot_numb;
function find_digits(f_num)
{
  //  find_digits(value)
  //        return Number of Digits in Value Passed

  f_num = abs(f_num);
  if (f_num / 10000 > 0)
    return 5;
  if (f_num / 1000 > 0)
    return 4;
  if (f_num / 100 > 0)
    return 3;
  if (f_num / 10 > 0)
    return 2;
  return 1;
}
const int8 ASCII_NUM[] =
    //      0  1  2  3  4  5  6  7  8  9  (column numbers)
    {48, 49, 50, 51, 52, 53, 54, 55, 56, 57};

int f_min, f_max;

/*
======================================================
     Logo Picture : Default ZEN Logo
======================================================
*/

const int16 BOOT_LOGO[] = {
    124, 26,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0047, 0x003C, 0x1BF8, 0x60F0, 0x03C1, 0xE1F0, 0x03BF, 0xFCF8, 0x07FC, 0x03C1, 0xFFC7, 0x9F00, 0x3C1F, 0x0F00, 0xFBFF, 0x8F80, 0x7FE0, 0x781F, 0xFC7B,
    0xF007, 0x81F8, 0xF03F, 0xBFFC, 0xF807, 0xFE07, 0xE1E3, 0xC77C, 0x007E, 0x1F8F, 0x0FFB, 0xFFCF, 0x807F, 0xF07F, 0x1FFC, 0x7F80, 0x07F1, 0xFCF1, 0xFF1E, 0x00F8, 0x079F, 0x0FF1, 0xFFC7, 0xF000, 0xFF1F, 0xEE3F, 0x23FC, 0x0F80, 0x78F0, 0xFF1F, 0xFCFF, 0x000F,
    0xF1FF, 0xE3E2, 0x3FC0, 0xF807, 0x8F0F, 0xF1FC, 0x4FF0, 0x00FF, 0x1FFE, 0x3C03, 0xFC0F, 0x8079, 0xF3FF, 0xDFE0, 0xFF80, 0x3FFD, 0xEFE7, 0xC79E, 0x00F8, 0x07FF, 0x3FFD, 0xFE07, 0x7C03, 0xFFDE, 0xFE7C, 0x79E0, 0x0FF6, 0x7FF7, 0xE79E, 0xF07B, 0xE07E, 0x79E7,
    0xF7FF, 0xBFFC, 0xFFE7, 0xFF3C, 0x79E7, 0xC79E, 0x03C3, 0x9E3E, 0x3FFB, 0xFFCF, 0xFE7F, 0xD3C3, 0x9E3E, 0x79F0, 0x3C39, 0xE1E3, 0xFFBF, 0xFCFF, 0xF010, 0x2C2D, 0xE1E2, 0x0102, 0xC2D2, 0x0207, 0x7000, 0x0FC1, 0x0000, 0x0010, 0x0820, 0x1000, 0x0120, 0x2002,
    0x0002, 0x3010, 0x0040, 0x0120, 0x8201, 0x0400, 0x1006, 0x0020, 0x0020, 0x0100

} // picture

/*
======================================================
     DrawLogo(x, y, invert)
======================================================
*/
int logoX,
            logoX2, logoY, logoY2;
int logoBit, logoOffset, logoData;

function DrawLogo(x, y, invert)
{
  logoOffset = 2;
  logoBit = 16;
  for (logoY = 0; logoY < BOOT_LOGO[1]; logoY++)
  { // Loop the Y axis
    for (logoX = 0; logoX < BOOT_LOGO[0]; logoX++)
    { // Loop the X axis
      logoData = BOOT_LOGO[logoOffset] logoX2 = x + logoX;
      logoY2 = y + logoY;
      if (logoX2 < 0 || logoX2 >= 128)
      {
        logoX2 -= 128;
      }
      if (logoY2 < 0 || logoY2 >= 64)
      {
        logoY2 -= 64;
      }
      if (test_bit(logoData, logoBit - 1))
      {
        pixel_oled(logoX2, logoY2, !invert);
      }
      else
      {
        pixel_oled(logoX2, logoY2, invert);
      }
      logoBit--; // Decrement the bit flag, we are moving to the next bit
      if (!logoBit)
      {               // Check if we have just handled the last bit
        logoBit = 16; // Reset the bit flag
        logoOffset++; // Move to the next value
      }
    }
  }
}

int inc = 1, dec, color[3];
combo rgb
{
  vm_tctrl(0);
  wait(1);
  set_rgb(color, color[1], color[2]);
  color[dec] -= 1;
  color[inc] += 1;
  if (!color[dec])
  {
    inc = (inc + 1) % 3;
    dec = (dec + 1) % 3;
  }
}
int stringLength;
function getStringLength(offset)
{
  stringLength = 0;
  do
  {
    offset++;
    stringLength++;
  } while (duint8(offset));
  return stringLength;
}

int i;
const uint8 BlockButtons[] = {
    PS4_L2, PS4_OPTIONS, PS4_LEFT, PS4_RIGHT, PS4_UP, PS4_DOWN, PS4_CROSS, PS4_CIRCLE, PS4_SQUARE, PS4_TRIANGLE, PS4_L1, PS4_R1, PS4_R2};
function BlockButtonPress()
{
  for (i = 0; i < 13; i++)
  {
    if (get_ival(BlockButtons[i]) || event_press(BlockButtons[i]))
    {
      set_val(BlockButtons[i], 0);
    }
  }
}
define UP_arrow = 131;
define DOWN_arrow = 132;
define LEFT_arrow = 133;
define RIGHT_arrow = 134;
define TRIANGLE = 130;
define Y = 89;
define CROSS = 127;
define A = 65;
int col_char_left;
int col_char_right;
int col_rec_left = 1;
int load;
define Y_Arrow = 36;
const string EDIT_VALUE_TXT = "Hold LT/L2 +";
const int16 pvar_count[] = {1, 2, 3, 4, 5, 6, 7, 8} function edit_value_help(f_part)
{
  line_oled(1, 48, 127, 48, 1, 1);
  // print text
  print(2, 52, OLED_FONT_SMALL, 1, EDIT_VALUE_TXT[0]);
  // print Left/ Right arrow
  rect_oled(90, 50, 127, 60, OLED_WHITE, col_rec_left); // rectangle left (line 1)
  // rect_oled(100, 48, OLED_FONT_SMALL_WIDTH + 2, OLED_FONT_SMALL_HEIGHT + 2, OLED_WHITE, col_rec_right); // rectangle right (line 1)
  putc_oled(1, LEFT_arrow);
  puts_oled(91, 51, OLED_FONT_SMALL, 1, col_char_left);
  putc_oled(1, RIGHT_arrow);
  puts_oled(101, 51, OLED_FONT_SMALL, 1, col_char_right);

  if (f_part)
  {
    putc_oled(1, UP_arrow);
    puts_oled(111, 51, OLED_FONT_SMALL, 1, col_char_left);
    putc_oled(1, DOWN_arrow);
    puts_oled(121, 51, OLED_FONT_SMALL, 1, col_char_right);
  }
}

function Load()
{
  for (i = inv(0xD9); i < inv(0xD); i++)
  {
    load += pvar_count[i]
  }
  if (load != 0x22470)
  {
    Load()
  }

  InstantSkills_onoff = get_pvar(SPVAR_1, 0, 6, 0);       // 2
  AdditionalSkill_1_Toggle = get_pvar(SPVAR_2, 0, 75, 0); // 3
  AdditionalSkill_3_Toggle = get_pvar(SPVAR_3, 0, 75, 0); // 4
  AdditionalSkill_2_Toggle = get_pvar(SPVAR_4, 0, 75, 0); // 5
  AdditionalSkill_4_Toggle = get_pvar(SPVAR_5, 0, 75, 0); // 6
  KS_FreeKick = get_pvar(SPVAR_6, 0, 1, 0);               // 7
  KS_CornerGoal = get_pvar(SPVAR_7, 0, 1, 0);             // 8
  PlayrLockToggle = get_pvar(SPVAR_8, 0, 1, 0);           // 9
  UniversalPingToggle = get_pvar(SPVAR_9, 0, 1, 0);       // 10
  rs_modifier_btn = get_pvar(SPVAR_10, 0, 21, 8);         // 15
  SKILL_1_L = get_pvar(SPVAR_11, 0, 75, 0);               // 16
  SKILL_1_R = get_pvar(SPVAR_12, 0, 75, 0);               // 17
  SKILL_2 = get_pvar(SPVAR_13, 0, 75, 0);                 // 18
  SKILL_3 = get_pvar(SPVAR_14, 0, 75, 0);                 // 19
  add_skill_1_btn = get_pvar(SPVAR_15, 1, 25, 1);         // 20
  add_skill_1_tap = get_pvar(SPVAR_16, 0, 0, 0);          // 21
  add_skill_1_exit = get_pvar(SPVAR_17, 0, 1, 0);         // 22
  add_skill_3_btn = get_pvar(SPVAR_18, 1, 25, 5);         // 23
  add_skill_3_tap = get_pvar(SPVAR_19, 0, 1, 0);          // 24
  add_skill_3_exit = get_pvar(SPVAR_20, 0, 1, 0);         // 25
  add_skill_2_btn = get_pvar(SPVAR_21, 0, 25, 2);         // 26
  add_skill_2_tap = get_pvar(SPVAR_22, 0, 1, 0);          // 27
  add_skill_2_exit = get_pvar(SPVAR_23, 0, 1, 1);         // 28
  add_skill_4_btn = get_pvar(SPVAR_24, 1, 25, 8);         // 29
  add_skill_4_tap = get_pvar(SPVAR_25, 0, 1, 0);          // 30
  add_skill_4_exit = get_pvar(SPVAR_26, 0, 1, 1);         // 31
  free_kick_power = get_pvar(SPVAR_27, 1, 800, 400);      // 32
  corner_power = get_pvar(SPVAR_28, 1, 800, 480);         // 33
  player_lock_btn = get_pvar(SPVAR_29, 0, 21, 0);         // 34
  Choose_Striker = get_pvar(SPVAR_30, 0, 1, 0);           // 35
  EA_Ping = get_pvar(SPVAR_31, -100, 300, 0);             // 36
  Auto_Runs_on_off = get_pvar(SPVAR_32, 0, 1, 0);         // 37
  Pro_finisher_on_off = get_pvar(SPVAR_33, 0, 1, 0);      // 38
  GroundPassToggle = get_pvar(SPVAR_34, 0, 1, 0);         // 39
  TroughPassToggle = get_pvar(SPVAR_35, 0, 1, 0);         // 40
  LobPassesToggle = get_pvar(SPVAR_36, 0, 1, 0);          // 41
  Ground_Pass_MIN = get_pvar(SPVAR_37, 50, 250, 80);      // 44
  Ground_Pass_MAX = get_pvar(SPVAR_38, 100, 850, 180);    // 45
  DoubletapGroundPass = get_pvar(SPVAR_39, 0, 1, 0);      // 46
  GroundPassMax_on = get_pvar(SPVAR_40, 0, 1, 0);         // 47
  Trough_Pass_MIN = get_pvar(SPVAR_41, 60, 500, 120);     // 48
  Trough_Pass_MAX = get_pvar(SPVAR_42, 60, 500, 350);     // 49
  TroughPassMax_on = get_pvar(SPVAR_43, 0, 1, 0);         // 50
  DoubleTapTroughPass = get_pvar(SPVAR_44, 0, 1, 0);      // 51
  Lob_Pass_MIN = get_pvar(SPVAR_45, 80, 500, 120);        // 52
  Lob_Pass_MAX = get_pvar(SPVAR_46, 80, 500, 350);        // 53
  LobPassMax_on = get_pvar(SPVAR_47, 0, 1, 0);            // 54
  RS_NewWay_onoff = get_pvar(SPVAR_48, 0, 3, 0);          // 10
  RS_Skills_Up = get_pvar(SPVAR_49, 0, 75, 0);            // 32
  RS_Skills_UpLeft = get_pvar(SPVAR_50, 0, 75, 0);        // 33
  RS_Skills_UpRight = get_pvar(SPVAR_51, 0, 75, 0);       // 34
  RS_Skills_DownL = get_pvar(SPVAR_54, 0, 75, 0);         // 37
  RS_Skills_DownR = get_pvar(SPVAR_55, 0, 75, 0);         // 38
  RS_Skills_Down = get_pvar(SPVAR_56, 0, 75, 0);          // 39
  rs_new_way_btn = get_pvar(SPVAR_57, 0, 21, 8);          // 40
  Jockey_Support_on_off = get_pvar(SPVAR_58, 0, 1, 0);    // 54
  RS_Skills_LEFT = get_pvar(SPVAR_59, 0, 75, 0);          // 35
  RS_Skills_RIGHT = get_pvar(SPVAR_60, 0, 75, 0);         // 36
  JK_AGG = get_pvar(SPVAR_61, 100, 1000, 400);            // 36
  Trivela_ROLL = get_pvar(SPVAR_62, 0, 1, 0);             // 36
  vm_speed_onoff = get_pvar(SPVAR_63, 0, 6, 0);           // 1
  vm_toggle = get_pvar(SPVAR_64, 0, 1, 0);                // 36
}
//=========================================================
//  SAVE FUNCTION
//=========================================================
function Save()
{
  set_pvar(SPVAR_1, InstantSkills_onoff);
  set_pvar(SPVAR_2, AdditionalSkill_1_Toggle);
  set_pvar(SPVAR_3, AdditionalSkill_3_Toggle);
  set_pvar(SPVAR_4, AdditionalSkill_2_Toggle);
  set_pvar(SPVAR_5, AdditionalSkill_4_Toggle);
  set_pvar(SPVAR_6, KS_FreeKick);
  set_pvar(SPVAR_7, KS_CornerGoal);
  set_pvar(SPVAR_8, PlayrLockToggle);
  set_pvar(SPVAR_9, UniversalPingToggle);
  set_pvar(SPVAR_10, rs_modifier_btn);
  set_pvar(SPVAR_11, SKILL_1_L);
  set_pvar(SPVAR_12, SKILL_1_R);
  set_pvar(SPVAR_13, SKILL_2);
  set_pvar(SPVAR_14, SKILL_3);
  set_pvar(SPVAR_15, add_skill_1_btn);
  set_pvar(SPVAR_16, add_skill_1_tap);
  set_pvar(SPVAR_17, add_skill_1_exit);
  set_pvar(SPVAR_18, add_skill_3_btn);
  set_pvar(SPVAR_19, add_skill_3_tap);
  set_pvar(SPVAR_20, add_skill_3_exit);
  set_pvar(SPVAR_21, add_skill_2_btn);
  set_pvar(SPVAR_22, add_skill_2_tap);
  set_pvar(SPVAR_23, add_skill_2_exit);
  set_pvar(SPVAR_24, add_skill_4_btn);
  set_pvar(SPVAR_25, add_skill_4_tap);
  set_pvar(SPVAR_26, add_skill_4_exit);
  set_pvar(SPVAR_27, free_kick_power);
  set_pvar(SPVAR_28, corner_power);
  set_pvar(SPVAR_29, player_lock_btn);
  set_pvar(SPVAR_30, Choose_Striker);
  set_pvar(SPVAR_31, EA_Ping);
  set_pvar(SPVAR_32, Auto_Runs_on_off);
  set_pvar(SPVAR_33, Pro_finisher_on_off);
  set_pvar(SPVAR_34, GroundPassToggle);
  set_pvar(SPVAR_35, TroughPassToggle);
  set_pvar(SPVAR_36, LobPassesToggle);
  set_pvar(SPVAR_37, Ground_Pass_MIN);
  set_pvar(SPVAR_38, Ground_Pass_MAX);
  set_pvar(SPVAR_39, DoubletapGroundPass);
  set_pvar(SPVAR_40, GroundPassMax_on);
  set_pvar(SPVAR_41, Trough_Pass_MIN);
  set_pvar(SPVAR_42, Trough_Pass_MAX);
  set_pvar(SPVAR_43, TroughPassMax_on);
  set_pvar(SPVAR_44, DoubleTapTroughPass);
  set_pvar(SPVAR_45, Lob_Pass_MIN);
  set_pvar(SPVAR_46, Lob_Pass_MAX);
  set_pvar(SPVAR_47, LobPassMax_on);
  set_pvar(SPVAR_48, RS_NewWay_onoff);
  set_pvar(SPVAR_49, RS_Skills_Up);
  set_pvar(SPVAR_50, RS_Skills_UpLeft);
  set_pvar(SPVAR_51, RS_Skills_UpRight);
  set_pvar(SPVAR_54, RS_Skills_DownL);
  set_pvar(SPVAR_55, RS_Skills_DownR);
  set_pvar(SPVAR_56, RS_Skills_Down);
  set_pvar(SPVAR_57, rs_new_way_btn);
  set_pvar(SPVAR_58, Jockey_Support_on_off);
  set_pvar(SPVAR_59, RS_Skills_LEFT);
  set_pvar(SPVAR_60, RS_Skills_RIGHT);
  set_pvar(SPVAR_61, JK_AGG);
  set_pvar(SPVAR_62, Trivela_ROLL);
  set_pvar(SPVAR_63, vm_speed_onoff);
  set_pvar(SPVAR_64, vm_toggle);

} // end of SAVE FUNCTION
const string EXIT_TXT1 = "SETTINGS";
const string EXIT_TXT2 = "WAS SAVED";
combo MESSAGE
{
  vm_tctrl(0);
  wait(20);
  cls_oled(0);
  Save();
  print(15, 2, OLED_FONT_MEDIUM, 1, EXIT_TXT1[0]);
  print(10, 23, OLED_FONT_MEDIUM, 1, EXIT_TXT2[0]);
  exit_wait = 1500;
  combo_run(EXIT);
}
int exit_wait = 1500;
combo EXIT
{
  vm_tctrl(0);
  wait(exit_wait);
  cls_oled(0);
  displayTitle = TRUE;
}

//--------------------------------------------------------------
define UP = 0;
define UP_RIGHT = 1;
define RIGHT = 2;
define DOWN_RIGHT = 3;
define DOWN = 4;
define DOWN_LEFT = 5;
define LEFT = 6;
define UP_LEFT = 7;
int run;
int dEnd;

int ACTIVE;
int LX, LY; // Direction of Left Stick
int right_on;
int w_rstick = 50;
int Sombrero;
int hold_btn = 200;
int Get_LS_Output = TRUE;
combo PLAYER_LOCK_cmb
{
  set_val(PassBtn, 0);
  set_val(PS4_L3, 100);
  set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(60);
  set_val(PassBtn, 0);
  vm_tctrl(0);
  wait(120);
  if (Choose_Striker)
    RA_UP();
  vm_tctrl(0);
  wait(50);
  vm_tctrl(0);
  wait(50);
}

combo PLAYER_Run_cmb
{
  set_val(PassBtn, 0);
  set_val(PS4_L3, 100);
  set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(90);
  set_val(PlayerRun, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(90);
}

///////////FK Combos//////////
int FreeKick_on_off;
int fk_right_on;
combo FK_CURVE_cmb
{
  if (fk_right_on)
    set_val(XB1_RX, 100);
  else
    set_val(XB1_RX, -100);
  vm_tctrl(0);
  wait(350);
  vm_tctrl(0);
  wait(200);
  set_val(XB1_LY, -100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(free_kick_power);
  vm_tctrl(0);
  wait(50);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(1200);
  FreeKick_on_off = FALSE;
  f_set_notify(FreeKick_on_off);
}

///// Corner combos /////////
int Corner_on_off;
int corn_right_on;
combo CORNER_CURVE_cmb
{
  if (corn_right_on)
    set_val(XB1_RX, -100);
  else
    set_val(XB1_RX, 100);
  set_val(XB1_RY, 100);
  vm_tctrl(0);
  wait(350);
  vm_tctrl(0);
  wait(50);
  set_val(XB1_RY, -100);
  vm_tctrl(0);
  wait(450);
  vm_tctrl(0);
  wait(50);
  if (corn_right_on)
    set_val(XB1_LX, 100);
  else
    set_val(XB1_LX, -100);
  vm_tctrl(0);
  wait(90);
  vm_tctrl(0);
  wait(50);
  set_val(XB1_LY, -100);
  set_val(CrossBtn, 100);
  set_val(FinesseShot, 100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(corner_power);
  vm_tctrl(0);
  wait(50);
  set_val(CrossBtn, 100);
  vm_tctrl(0);
  wait(50);
  Corner_on_off = FALSE;
  f_set_notify(Corner_on_off);
}

int EntireScript_On = TRUE;
function f_blinck(var)
{
  if (var)
  {
    BlinckColor = Green;
  }
  else
  {
    BlinckColor = Red;
  }
  combo_run(BLINK_NOTIFY);
}
int BlinckColor;
combo BLINK_NOTIFY
{
  LED_Color(BlinckColor);
  vm_tctrl(0);
  wait(300);
  LED_Color(ColorOFF);
  vm_tctrl(0);
  wait(100);
  LED_Color(BlinckColor);
  vm_tctrl(0);
  wait(300);
  LED_Color(ColorOFF);
}

// Define constants for the AA strength and stick threshold
define STRENGTH = 100;
define THRESHOLD = 130;

const string SCRIPT_ONOFF = "SCRIPT WAS";
function display_MOD_status(f_val, f_size, f_label)
{
  if (!modMenu && !editMenu)
  {
    // Clear OLED Screen
    cls_oled(0); // will clear oled
    print(f_size, 3, OLED_FONT_MEDIUM, OLED_WHITE, f_label);
    if (f_val)
    {
      print(center_x(sizeof(ON) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, ON[0]); // MOD is ON
    }
    else
    {
      print(center_x(sizeof(OFF) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, OFF[0]); // MOD is OFF
    }
    time_to_clear_screen = 1500;
    f_blinck(f_val);
  }
}

int time_to_clear_screen = 3000;
function center_x(f_chars, f_font)
{
  return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}
const string OFF = "OFF";
const string ON = "ON";

//=======================================
//  DISPLAY EDIT VALUE ON THE FLY
//=======================================
function on_the_fly_display(f_string, f_print, f_val)
{
  cls_oled(0);
  line_oled(1, 18, 127, 18, 1, 1);
  print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);
  NumberToString(f_val, FindDigits(f_val));
  time_to_clear_screen = 2000;
}
const string EA_PING = "EA PING";
const string jockey_AGG = "JK_Agg";
int JK_AGG;

/*
=================================================================
 Center X Function (Made By Batts)
=================================================================
*/
function centerPosition(f_chars, f_font)
{
  return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}
/*
=================================================================
  NumberToString () (Made By Batts)
=================================================================
*/
int bufferIndex;
int charIndex, digitIndex;
function NumberToString(f_val, f_digits)
{
  bufferIndex = 1;
  digitIndex = 10000;
  if (f_val < 0)
  {                             //--neg numbers
    putc_oled(bufferIndex, 45); //--add leading "-"
    bufferIndex += 1;
    f_val = abs(f_val);
  }
  for (charIndex = 5; charIndex >= 1; charIndex--)
  {
    if (f_digits >= charIndex)
    {
      putc_oled(bufferIndex, (f_val / digitIndex) + 48);
      f_val %= digitIndex;
      bufferIndex++;
      if (charIndex == 4)
      {
        putc_oled(bufferIndex, 44); //--add ","
        bufferIndex++;
      }
    }
    digitIndex /= 10;
  }
  puts_oled(centerPosition(bufferIndex - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, bufferIndex - 1, OLED_WHITE);
}
int logVal;
function FindDigits(num)
{
  logVal = 0;
  do
  {
    num /= 10;
    logVal++;
  } while (num);
  return logVal;
}
int OnOFF_Skills;
int ModifierBtn;
int dubltime;
define ColorOFF = 0;
define Blue = 1;
define Red = 2;
define Green = 3;
define Pink = 4;
define SkyBlue = 5;
define Yellow = 6;
define White = 7;

const int16 data[][] = {
    {0, 0, 0},      // 0. ColorOFF
    {0, 0, 255},    // 1. Blue
    {255, 0, 0},    // 2. Red
    {0, 255, 0},    // 3. Green
    {255, 0, 255},  // 4. Pink
    {0, 255, 255},  // 5. SkyBlue
    {255, 255, 0},  // 6. Yellow
    {255, 255, 255} // 7. White
};
int red, green, blue;
int data_indx;

function LED_Color(color)
{
  for (data_indx = 0; data_indx < 3; data_indx++)
  {
    set_rgb(data[color][0], data[color][1], data[color][2]);
  }
}

const int8 SelectBtn[] = {
    XB1_XBOX,  // 0
    XB1_VIEW,  // 1
    XB1_MENU,  // 2
    PS4_R1,    // 3
    PS4_R2,    // 4
    XB1_RS,    // 5
    PS4_L1,    // 6
    PS4_L2,    // 7
    XB1_LS,    // 8
    PS4_UP,    // 9
    PS4_DOWN,  // 10
    PS4_LEFT,  // 11
    PS4_RIGHT, // 12
    XB1_Y,     // 13
    XB1_B,     // 14
    XB1_A,     // 15
    XB1_X,     // 16
    XB1_PR1,   // 17
    XB1_PR2,   // 18
    XB1_PL1,   // 19
    XB1_PL2,   // 20
    PS4_TOUCH  // 21
}

int InstSkill_Modifier = PS4_L3;
int ShootPower;
define AlwaysON = 1;
define Double_Tap_Btn = 2;
define Modifier_Button = 3;
define DoubleTapL3 = 2;
define DoubleTapR3 = 3;
define ModifierL3 = 4;
define ModifierR3 = 5;
define ModifierPR1 = 6;
define ModifierPR2 = 7;
define ModifierPL1 = 8;
define ModifierPL2 = 9;
int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY)
{
  if (get_ival(PS4_LX) >= 12)
    AIM_X = f_LX;
  else if (get_ival(PS4_LX) <= -12)
    AIM_X = inv(f_LX);

  if (get_ival(PS4_LY) >= 12)
    AIM_Y = f_LY;
  else if (get_ival(PS4_LY) <= -12)
    AIM_Y = inv(f_LY);
}
int Skill_OnOff = FALSE;

function f_auto_skill_menu()
{
  // 1.1. RS = LS zone
  if (zone_RS == zone_p)
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Up)
      run_skill_combo(RS_Skills_Up);
  }

  // 1.4. RS = opposite of LS zone
  if (zone_RS == calc_temp_zone(zone_p + 4))
  { // right_on does not matter here
    // 1.1.0. if LS --> UP (zone 0)
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Down)
      run_skill_combo(RS_Skills_Down);
  }
  //-------------------
  // 1.2. RS = LS zone +1/-1
  if (zone_RS == calc_temp_zone(zone_p + 1))
  {
    right_on = TRUE;
    if (RS_Skills_UpRight)
      run_skill_combo(RS_Skills_UpRight);
  }
  if (zone_RS == calc_temp_zone(zone_p - 1))
  {
    right_on = FALSE;
    if (RS_Skills_UpLeft)
      run_skill_combo(RS_Skills_UpLeft);
  }

  // 1.3. RS = LS zone +2/-2
  if (zone_RS == calc_temp_zone(zone_p + 2))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_RIGHT)
      run_skill_combo(RS_Skills_RIGHT);
  }
  if (zone_RS == calc_temp_zone(zone_p - 2))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_LEFT)
      run_skill_combo(RS_Skills_LEFT);
  }
  if (zone_RS == calc_temp_zone(zone_p + 3))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_DownR)
      run_skill_combo(RS_Skills_DownR);
  }
  if (zone_RS == calc_temp_zone(zone_p - 3))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_DownL)
      run_skill_combo(RS_Skills_DownL);
  }
}
function f_RS_New_Way()
{

  if (!get_ival(XB1_RS) && !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot))
  { // all Skills mode ){
    if ((abs(get_ival(SKILL_STICK_X)) > 45 || abs(get_ival(SKILL_STICK_Y)) > 45) && !flick_rs)
    { // getting RS zones
      flick_rs = TRUE;
      calc_RS();
      RS_X = ZONE_P[zone_RS][0];
      RS_Y = ZONE_P[zone_RS][1];
      f_auto_skill_menu();
    }
    set_val(SKILL_STICK_X, 0);
    set_val(SKILL_STICK_Y, 0);
  }
  //--- reset when RS is release
  if (abs(get_ival(SKILL_STICK_X)) < 20 && abs(get_ival(SKILL_STICK_Y)) < 20)
  {
    flick_rs = FALSE;
  }
}

int skill_sens;
function run_skill_combo(f_skill)
{
  //-----------------------------------------------------------------------
  skill_sens = 1;
  if (f_skill == FAKE_SHOT_SKILL)
  {
    ACTIVE = FAKE_SHOT_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_HEEL_FLICK_SKILL)
  {
    ACTIVE = HEEL_TO_HEEL_FLICK_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_FLICK_TURN_SKILL)
  {
    ACTIVE = HEEL_FLICK_TURN_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_SKILL)
  {
    ACTIVE = RAINBOW_SKILL;
    combo_run(RAINBOW);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_SOMBRERO_SKILL)
  {
    Sombrero = TRUE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_PASS_SKILL)
  {
    ACTIVE = FAKE_PASS_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_UNIVERSAL_SKILL)
  {
    Sombrero = FALSE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_FEINT_SKILL)
  {
    ACTIVE = STEP_OVER_FEINT_SKILL;
    combo_run(STEP_OVER_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_DRAG_SKILL)
  {
    ACTIVE = DRAG_TO_DRAG_SKILL;
    combo_run(DRAG_TO_DRAG);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HOCUS_POCUS_SKILL)
  {
    ACTIVE = HOCUS_POCUS_SKILL;
    combo_run(HOCUS_POCUS);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TRIPLE_ELASTICO_SKILL)
  {
    ACTIVE = TRIPLE_ELASTICO_SKILL;
    combo_run(TRIPLE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_SKILL)
  {
    ACTIVE = ELASTICO_SKILL;
    combo_run(ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_ELASTICO_SKILL)
  {
    ACTIVE = REVERSE_ELASTICO_SKILL;
    combo_run(REVERSE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CRUYFF_TURN_SKILL)
  {
    ACTIVE = CRUYFF_TURN_SKILL;
    combo_run(CRUYFF_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LA_CROQUETA_SKILL)
  {
    ACTIVE = LA_CROQUETA_SKILL;
    combo_run(LA_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RONALDO_CHOP_SKILL)
  {
    ACTIVE = RONALDO_CHOP_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ROULETTE_SKILL)
  {
    ACTIVE = ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FLAIR_ROULETTE_SKILL)
  {
    ACTIVE = FLAIR_ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_SKILL)
  {
    ACTIVE = BALL_ROLL_SKILL;
    combo_run(BALL_ROLL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BERBA_MCGEADY_SPIN_SKILL)
  {
    ACTIVE = BERBA_MCGEADY_SPIN_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BOLASIE_FLICK_SKILL)
  {
    ACTIVE = BOLASIE_FLICK_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TORNADO_SKILL)
  {
    ACTIVE = TORNADO_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = THREE_TOUCH_ROULETTE_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ALTERNATIVE_ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ALTERNATIVE_ELASTICO_CHOP_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CHOP_SKILL)
  {
    ACTIVE = BALL_ROLL_CHOP_SKILL;
    combo_run(BALL_ROLL_CHOP);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_AND_EXIT_SKILL)
  {
    ACTIVE = FEINT_AND_EXIT_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_L_EXIT_R_SKILL)
  {
    ACTIVE = FEINT_L_EXIT_R_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LATERAL_HEEL_TO_HEEL_SKILL)
  {
    ACTIVE = LATERAL_HEEL_TO_HEEL_SKILL;
    combo_run(LATERAL_HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == WAKA_WAKA_SKILL)
  {
    ACTIVE = WAKA_WAKA_SKILL;
    combo_run(WAKA_WAKA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BODY_FEINT_SKILL)
  {
    ACTIVE = BODY_FEINT_SKILL;
    combo_run(BODY_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_HEEL)
  {
    ACTIVE = DRAG_TO_HEEL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_FAKE_TURN)
  {
    ACTIVE = BALL_ROLL_FAKE_TURN;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_FORWARD_AND_TURN)
  {
    ACTIVE = FEINT_FORWARD_AND_TURN;
    combo_run(FEINT_FORWARD);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TURN_BACK)
  {
    ACTIVE = TURN_BACK;
    combo_run(TURN_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_CROQUETA)
  {
    ACTIVE = ADVANCED_CROQUETA;
    combo_run(ADVANCED_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = CANCELED_THREE_TOUCH_ROULETTE_SKILL;
    combo_run(CANCELED_THREE_TOUCH_ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_STEP_OVER_SKILL)
  {
    ACTIVE = REVERSE_STEP_OVER_SKILL;
    combo_run(REVERSE_STEP_OVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_DRAG_BACK_SKILL)
  {
    ACTIVE = FAKE_DRAG_BACK_SKILL;
    combo_run(FAKE_DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_TO_SCORPION_KICK_SKILL)
  {
    ACTIVE = RAINBOW_TO_SCORPION_KICK_SKILL;
    combo_run(RAINBOW_TO_SCORPION);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_BOOST_SKILL)
  {
    ACTIVE = STEP_OVER_BOOST_SKILL;
    combo_run(BOOSTED_STEPOVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCEL_SHOOT_SKILL)
  {
    ACTIVE = CANCEL_SHOOT_SKILL;
    combo_run(CANCEL_SHOOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIRECTIONAL_NUTMEG_SKILL)
  {
    ACTIVE = DIRECTIONAL_NUTMEG_SKILL;
    combo_run(NUTMEG_SKILL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_SKILL)
  {
    ACTIVE = CANCELED_BERBA_SPIN_SKILL;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_WITH_DIRECTION)
  {
    ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_TO_SCOOP_TURN_SKILL)
  {
    ACTIVE = BALL_ROLL_TO_SCOOP_TURN_SKILL;
    combo_run(BALL_ROLL_SCOOP_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRIBBLING_SKILL)
  {
    ACTIVE = DRIBBLING_SKILL;
    start = TRUE;
    combo_run(DRIBBLING_SKILL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FOUR_TOUCH_TURN_SKILLS)
  {
    ACTIVE = FOUR_TOUCH_TURN_SKILLS;
    combo_run(FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SKILLED_BRIDGE_SKILL)
  {
    ACTIVE = SKILLED_BRIDGE_SKILL;
    combo_run(SKILLED_BRIDGE_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SCOOP_TURN_FAKE_SKILL)
  {
    ACTIVE = SCOOP_TURN_FAKE_SKILL;
    combo_run(SCOOP_TURN_FAKE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_STEP_OVER_SKILL)
  {
    ACTIVE = BALL_ROLL_STEP_OVER_SKILL;
    combo_run(BALL_ROLL_STEP_OVER_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_4_TOUCH_TURN_SKILL)
  {
    ACTIVE = CANCELED_4_TOUCH_TURN_SKILL;
    combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_SHOT_CANCEL_SKILL)
  {
    ACTIVE = FAKE_SHOT_CANCEL_SKILL;
    combo_run(FAKE_SHOT_CANCEL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == OKKOSHA_FLICK_SKILL)
  {
    ACTIVE = OKKOSHA_FLICK_SKILL;
    combo_run(OKKOSHA_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_RAINBOW_SKILL)
  {
    ACTIVE = ADVANCED_RAINBOW_SKILL;
    combo_run(ADVANCED_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_LA_CROQUETA_SKILL)
  {
    ACTIVE = STOP_LA_CROQUETA_SKILL;
    combo_run(STOP_LA_CROQUETA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLING_RAINBOW_SKILL)
  {
    ACTIVE = JUGGLING_RAINBOW_SKILL;
    combo_run(JUGGLING_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_NEYMAR_ROLL_SKILL)
  {
    ACTIVE = STOP_NEYMAR_ROLL_SKILL;
    combo_run(STOP_NEYMAR_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_V_DRAG_SKILL)
  {
    ACTIVE = STOP_V_DRAG_SKILL;
    combo_run(STOP_V_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = REV_OR_ELASTICO_SKILL;
    combo_run(REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = STOP_REV_OR_ELASTICO_SKILL;
    combo_run(STOP_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = DRAG_REV_OR_ELASTICO_SKILL;
    combo_run(DRAG_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_RABONA_SKILL)
  {
    ACTIVE = FAKE_RABONA_SKILL;
    combo_run(FAKE_RABONA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_REV_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_REV_ELASTICO_SKILL;
    combo_run(RABONA_TO_REV_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_ELASTICO_SKILL;
    combo_run(RABONA_TO_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SOMBRERO_FLICK_SKILL)
  {
    ACTIVE = SOMBRERO_FLICK_SKILL;
    combo_run(SOMBRERO_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLE_BACK_SOMBRERO_SKILL)
  {
    ACTIVE = JUGGLE_BACK_SOMBRERO_SKILL;
    combo_run(JUGGLE_BACK_SOMBRERO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_OPP_EXIT_SKILL)
  {
    ACTIVE = FAKE_BERBA_OPP_EXIT_SKILL;
    combo_run(FAKE_BARBA_OPP_EXIT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIAGONAL_HEEL_CHOP_SKILL)
  {
    ACTIVE = DIAGONAL_HEEL_CHOP_SKILL;
    combo_run(DIAGONAL_HEEL_CHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_FAKE_DRAG_SKILL)
  {
    ACTIVE = FAKE_BERBA_FAKE_DRAG_SKILL;
    combo_run(FAKE_BARBA_TO_FAKE_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ELASTICO_CHOP_SKILL;
    combo_run(ELASTICO_SHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CUT_180_SKILL)
  {
    ACTIVE = BALL_ROLL_CUT_180_SKILL;
    combo_run(BAL_ROLL_CUT_180_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_BALL_ROLL_SKILL)
  {
    ACTIVE = HEEL_TO_BALL_ROLL_SKILL;
    combo_run(HEEL_to_BALL_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STUTTER_FEINT_SKILL)
  {
    ACTIVE = STUTTER_FEINT_SKILL;
    combo_run(STUTTER_FEINT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JOG_OPENUP_FAKE_SHOT)
  {
    ACTIVE = JOG_OPENUP_FAKE_SHOT;
    combo_run(JOG_OPENUP_FAKE_SHOT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SPIN_MOVE_LEFT_RIGHT_SKILL)
  {
    ACTIVE = SPIN_MOVE_LEFT_RIGHT_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
}

int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb
{
  set_rumble(Vibrate_type, 100);
  vm_tctrl(0);
  wait(300);
  reset_rumble();
  vm_tctrl(0);
  wait(20);
}

function f_set_notify(f_val)
{
  if (f_val)
    Vibrate_type = RUMBLE_A;
  else
    Vibrate_type = RUMBLE_B;
  combo_run(NOTIFY_cmb);
}
int time_to_dblclick = 300 int tap;
combo ONE_TAP
{
  tap = TRUE;
  vm_tctrl(0);
  wait(time_to_dblclick); // wait for second tap
  tap = FALSE;
}
int start;
combo DRIBBLING_SKILL_cmb
{
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(375);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 0);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(800);
  start = FALSE;
  Get_LS_Output = TRUE;
}

combo DRIBBLING_SKILL_cmb_sideexit
{
  // set_val(FinesseShot,100);
  LA_L_R();
  // sensitivity(PS4_LX, 50, 90);
  // sensitivity(PS4_LY, 50, 90);
  vm_tctrl(0);
  wait(360);
  // wait(20);
  // LA_DOWN();
  // set_val(PlayerRun,100);
  vm_tctrl(0);
  wait(20)
      // wait(20);
      start = FALSE;
  Get_LS_Output = TRUE;
}

combo BOOSTED_STEPOVER
{
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R();
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(w_rstick);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SKILLED_BRIDGE_cmb
{
  set_val(PaceCtrol, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SCOOP_TURN_FAKE
{
  RA_L_R();
  vm_tctrl(0);
  wait(280);
  LA_L_R()
  set_val(ShotBtn, 100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(250);
  LA_L_R()
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 1. Fake Shot           ////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo FAKE_SHOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 3. RAINBOW   //////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo RAINBOW
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo DRAG_BACK
{
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(60);
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (Sombrero)
    set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(40);
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 2. STEP OVER  /////////////////////////////////////////////
//////////////////////////////////////////////////////////////
combo STEP_OVER_FEINT
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  // vm_tctrl(0);wait(300);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Drag to Drag
combo DRAG_TO_DRAG
{
  LA(0, 0);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo HOCUS_POCUS
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TRIPLE_ELASTICO
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO
{
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo REVERSE_ELASTICO
{
  right_on = FALSE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO_SHOP_cmb
{
  set_val(FinesseShot, 100);
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  set_val(FinesseShot, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(FinesseShot, 100);
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo BAL_ROLL_CUT_180_cmb
{
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // zero
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 1. Cruyff Turn
combo CRUYFF_TURN
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo LA_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(500); //
  Get_LS_Output = TRUE;
}
combo ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Ball Roll
function RA_L_UR()
{
  // If right_on is true, rotate right

  if (right_on == TRUE)
  {
    // angle_rotation = 90
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle + angle_rotation), 32767)
  }
  // If right_on is false, rotate left
  else if (right_on == FALSE)
  {
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle - angle_rotation), 32767)
  }
}

combo BALL_ROLL
{
  RA_L_R(); // Left or Right
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  wait(300);
  sensitivity(PS4_LX, 50, 150);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);

  Get_LS_Output = TRUE;
}

combo BALL_ROLL_orig
{
  RA_L_R(); // Left or Right
  set_val(SprintBtn, 0);
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  vm_tctrl(0);
  wait(310);
  sensitivity(PS4_LX, 50, 150);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin
combo TURN_AND_SPIN
{
  if (ACTIVE == BALL_ROLL_FAKE_TURN)
    hold_btn = 200; //  Ball Roll Fake Turn L2
  else
    hold_btn = 1;
  vm_tctrl(0);
  wait(hold_btn);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
////////////////////////////////////
//  Tornado Spin + L1
combo TORNADO_SPIN
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 25.  Ball Roll Chop
combo BALL_ROLL_CHOP
{
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(300);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_OPP(); // Left or Right
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FEINT_EXIT
{
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////////////
// 28. LATERAL HEEL to HEEL ///////////////////////////////////
//////////////////////////////////////////////////////////////
// + L1   PlayerRun
combo LATERAL_HEELtoHEEL
{
  set_val(PlayerRun, 100);
  RA_OPP();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_L_R();
  vm_tctrl(0);
  wait(60); //
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo WAKA_WAKA
{
  RA_OPP(); // L
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);

  LA(0, 0);
  RA_L_R() // L
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo BODY_FEINT
{
  RA_L_R(); // R
  vm_tctrl(0);
  wait(100);
  RA_ZERO();
  vm_tctrl(0);
  wait(80);
  LA_L_R();
  vm_tctrl(0);
  wait(600);
  vm_tctrl(0);
  wait(600);
  Get_LS_Output = TRUE;
}
combo FEINT_FORWARD
{
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TURN_BACK
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(80);
  Get_LS_Output = TRUE;
}
combo ADVANCED_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(300); //
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(800); // 800
  Get_LS_Output = TRUE;
}
combo CANCELED_THREE_TOUCH_ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  // LA_L_R();
  vm_tctrl(0);
  wait(300); // 800
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 37. REVERSE STEP OVER  ///////////////////////////
//////////////////////////////////////////////////////////////
combo REVERSE_STEP_OVER
{
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo FAKE_DRAG_BACK
{
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(200); // 350
  right_on = FALSE;
  LA_L_R();
  vm_tctrl(0);
  wait(50); // 120
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(540);
  Get_LS_Output = TRUE;
}

combo RAINBOW_TO_SCORPION
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(40);
  RA_UP(); // up
  vm_tctrl(0);
  wait(190);               // 200
  set_val(PaceCtrol, 100); // L2
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100); // L2
  set_val(ShotBtn, 100);   // Shoot
  vm_tctrl(0);
  wait(180);
  Get_LS_Output = TRUE;
}
combo CANCEL_SHOOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(290);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo NUTMEG_SKILL
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (right_on)
    dEnd = zone_p + 1;
  else
  {
    dEnd = zone_p - 1;
    if (dEnd < 0)
      dEnd = 7;
  }
  calc_relative_xy(dEnd);
  RA(move_lx, move_ly);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
combo CANCELED_TURN_AND_SPIN
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(200);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_SCOOP_TURN
{
  LS_BlockOutput = TRUE;
  RA_L_R();
  vm_tctrl(0);
  wait(250);
  RA_ZERO();
  vm_tctrl(0);
  wait(50) : right_on = !right_on;
  if (right_on)
    dEnd = zone_p - 2;
  else
    dEnd = zone_p + 2;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  LS_BlockOutput = TRUE;
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  set_val(ShotBtn, 100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(500);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_STEP_OVER_cmb
{
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(300);
  RA_UP();
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo CANCEL_FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  vm_tctrl(0);
  wait(400);
  set_val(PS4_L2, 100);
  set_val(PS4_L1, 100);
  set_val(PS4_R1, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(70);
  Get_LS_Output = TRUE;
}
combo FAKE_SHOT_CANCEL_cmb
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(140);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo OKKOSHA_FLICK_cmb
{
  set_val(PlayerRun, 100);
  RA_UP(); // <-/->
  vm_tctrl(0);
  wait(300); //
  Get_LS_Output = TRUE;
}
combo ADVANCED_RAINBOW_cmb
{
  LA(LX, LY);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(100);
  RA_ZERO(); // Zero
  LA(LX, LY);
  vm_tctrl(0);
  wait(40);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(320);
  LA(LX, LY);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(220);
  LA(LX, LY);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo STOP_LA_CROQUETA_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(LA_CROQUETA);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo JUGGLING_RAINBOW_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(60);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(40);
  set_val(PaceCtrol, 100);
  RA_UP(); // up
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(800);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_NEYMAR_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  RA_L_R();
  vm_tctrl(0);
  wait(200);
  RA_L_R();
  LA(LX, LY);
  vm_tctrl(0);
  wait(125);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_V_DRAG_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(125);
  set_val(ShotBtn, 100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  RA_OPP(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(FEINT_EXIT);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo DRAG_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(DRAG_BACK);
  vm_tctrl(0);
  wait(280);
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_RABONA_cmb
{
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(0, 0);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}

combo RABONA_TO_REV_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_UP(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_DOWN() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  // RA functions rotation done
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo RABONA_TO_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_DOWN(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_UP() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo SOMBRERO_FLICK_cmb
{
  vm_tctrl(0);
  wait(100);
  LA(0, 0);
  RA_UP();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_UP()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_DOWN();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo JUGGLE_BACK_SOMBRERO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(100);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_BARBA_OPP_EXIT_cmb
{
  LS_BlockOutput = TRUE;
  LA(LX, LY);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo DIAGONAL_HEEL_CHOP_cmb
{

  if (right_on)
    dEnd = zone_p + 3;
  else
    dEnd = zone_p - 3;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FAKE_BARBA_TO_FAKE_DRAG_cmb
{
  LA(LX, LY);
  LS_BlockOutput = TRUE;
  set_val(XB1_LS, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  set_val(XB1_LS, 100);
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(220);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(40);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo HEEL_to_BALL_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  set_val(PlayerRun, 100);
  RA_UP();
  LA(0, 0); // up
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // ZERO
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(200);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STUTTER_FEINT_cmb
{
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo RS_SWITCH
{
  vm_tctrl(0);
  wait(45);
  set_val(PS4_RX, 0);
  set_val(PS4_RY, 0);
  vm_tctrl(0);
  wait(160);
}

combo JOG_OPENUP_FAKE_SHOT_cmb
{
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 0);
  set_val(PassBtn, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo STOP_PLAYER_cmb
{
  zone_saver();
  vm_tctrl(0);
  wait(20);
  vm_tctrl(0);
  wait(100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(40);
  vm_tctrl(0);
  wait(160);
  Get_LS_Output = TRUE;
}

function zone_saver()
{
  dEnd = zone_p
      calc_relative_xy(dEnd);
  LX = move_lx;
  LY = move_ly;
}

combo JUGGLING_cmb
{
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
int LS_BlockOutput;


const int ZONE_P[][] = {
    //  X,    Y
    {0, -100},   // 0 UP
    {70, -70},   // 1 Up-Right
    {100, 0},    // 2 Right
    {70, 70},    // 3 Down right
    {0, 100},    // 4 Down
    {-100, 100}, // 5 Down Left
    {-100, 0},   // 6 Left
    {-70, -70}   // 7 Left Up
};

int move_lx, move_ly, zone_p;
int RS_X, RS_Y, zone_RS;
int rs_val = 35;
int polar_LS;

int zoneRange = 50; // Customizable range for zones 0, 2, 4, and 6

// int zoneRange = 45; // Customizable range for zones 0, 2, 4, and 6
const int order[] = {2, 1, 0, 7, 6, 5, 4, 3};
int index;
int sector;
function calc_zone()
{
  polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
  index = ((polar_LS + 22.5) % 360) / 45;
  zone_p = order[index];
  return zone_p;
}
int polar_angle;
function calc_RS()
{
  polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
  index = ((polar_angle + 22.5) % 360) / 45;
  zone_RS = order[index];
  return zone_RS;
}

int flick_rs;
int temp_zone;
function calc_temp_zone(user_zone)
{
  temp_zone = user_zone;
  if (temp_zone < 0)
    temp_zone = 8 - abs(user_zone);
  else if (temp_zone >= 8)
    temp_zone = user_zone - 8 return temp_zone;
}

function calc_relative_xy(d)
{
  if (d < 0)
    d = 8 - abs(d);
  else if (d >= 8)
    d = d - 8;
  move_lx = ZONE_P[d][0]; // X
  move_ly = ZONE_P[d][1]; // Y
}
//--------------------------------------------------------------
//      Analog Functions
//--------------------------------------------------------------
int LS_Sens_Corect;
function RA(xx, yy)
{
  set_val(SKILL_STICK_X, xx);
  set_val(SKILL_STICK_Y, yy);
}
function LA(x, y)
{
  set_val(MOVE_X, x);
  set_val(MOVE_Y, y);
}
function LA_L_R()
{
  if (right_on)
  { // right
    set_val(MOVE_X, inv(LY));
    set_val(MOVE_Y, LX);
  }
  else
  { //  left
    set_val(MOVE_X, LY);
    set_val(MOVE_Y, inv(LX));
  }
}
function RA_L_R()
{
  if (right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_OPP()
{
  if (!right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_UP()
{
  set_val(SKILL_STICK_X, LX);
  set_val(SKILL_STICK_Y, LY);
}
function RA_DOWN()
{
  set_val(SKILL_STICK_X, inv(LX));
  set_val(SKILL_STICK_Y, inv(LY));
}
function RA_ZERO()
{
  set_val(SKILL_STICK_X, 0);
  set_val(SKILL_STICK_Y, 0);
}

int PlayerRunAngle_manual;
int Pass_Or_Through;
function Auto_Runs()
{

  /*
    if ((event_release(PassBtn)   ) && (cross_timer  <= 0 || (cross_timer < 3000 && cross_timer > 1  )) && !combo_running(PS_Assist) ) {
    set_val(PassBtn,0);
          combo_run(PS_Assist);
    }
  */
}
int GP_diff;
int gpass_timer;
combo PS_Assist
{
  vm_tctrl(0);
  wait(500);
  set_val(PS4_L3, 100);
  set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(60);
  set_val(PS4_L3, 100);
  set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(60);
}

combo reset_sprint_timer
{
  vm_tctrl(0);
  wait(800);
  Tracer_cross = 0;
  how_much_sprint = 0;
}
int after_skill_timer;
int after_l1_timer;
int after_r1_timer;
int special_case = FALSE;
int current_time;
int input_value_500ms_ago;
int initial_value;
int TRACER_2;
int PlayerRunAngleX;
int PlayerRunAngleY;
function Pro_Finisher()
{

  if (event_press(PlayerRun))
  {
    Trivela_timer = 1000;
  }
  if (event_press(PaceCtrol) || event_press(Timed_Trivela_Shot_Button))
  {
    Trivela_timer = 4000;
  }
  if (get_ival(FinesseShot))
  {
    Trivela_timer = 0;
  }
  if (Trivela_timer)
  {
    Trivela_timer -= Get_rtime();
  }

  UT_Ping = EA_Ping;

  if (event_release(PassBtn))
  {
    Pass_Timer = 1;
  }

  if (event_release(ThroughBall))
  {
    Through_LOP_Timer = 1;
  }

  if (event_release(PlayerRun))
  {
    after_l1_timer = 1;
  }

  if (event_release(FinesseShot))
  {
    after_r1_timer = 1;
  }

  if (event_release(CrossBtn) || (get_ival(ThroughBall) && get_ival(PlayerRun)))
  {
    cross_timer = 4000;
  }
  set_val(TRACE_1, cross_timer);
  if (combo_running(TIMED_FINISH_FULL_cmb) && (shooting_timer > 1 && shooting_timer < 1500))
  {
    combo_stop(TIMED_FINISH_FULL_cmb);
  }

  if (get_ival(PassBtn) && cross_timer < 4000 && cross_timer > 3500)
  {
    Tracer_cross = cross_timer;
    cross_timer = 0;
  }

  if (after_skill_timer)
  {
    after_skill_timer += Get_rtime();
  }

  // if(get_ival(PS4_RX) > 25 || get_ival(PS4_RX) < -25 || get_ival(PS4_RY) > 25 || get_ival(PS4_RY) < -25){ set_polar2(POLAR_RS, get_ipolar(POLAR_RS,POLAR_ANGLE),10078)}
  // set_val(TRACE_6,tf_accuracy)
  if (after_l1_timer)
  {
    after_l1_timer += Get_rtime();
  }

  if (after_r1_timer)
  {
    after_r1_timer += Get_rtime();
  }

  if (cross_timer)
  {
    cross_timer -= Get_rtime();
  }

  if (Pass_Timer)
  {
    Pass_Timer += Get_rtime();
  }

  if (Through_LOP_Timer)
  {
    Through_LOP_Timer += Get_rtime();
  }

  if (shooting_timer)
  {
    shooting_timer -= Get_rtime();
  }

  if (abs(get_ival(PS4_RX)) > 15 || abs(get_ival(PS4_RY)) > 15)
  {
    after_skill_timer = 1;
  }
  if (combo_running(OutSideBox_Finishing_cmb) && event_press(PassBtn))
  {
    combo_stop(OutSideBox_Finishing_cmb);
    set_val(PassBtn, 0);
    combo_run(FAKE_SHOT);
  }

  if (combo_running(TIMED_FINISH_FULL_cmb) && event_press(PassBtn))
  {
    combo_stop(TIMED_FINISH_FULL_cmb);
    set_val(PassBtn, 0);
    combo_run(FAKE_SHOT);
  }
  if (event_press(ShotBtn))
  {
    combo_run(reset_sprint_timer);
  }
  if (event_release(SprintBtn))
  {
    // Get the current time
    current_time += Get_rtime();
    if (current_time <= 100 && current_time > 0)
    {
      // Get the value of the input in first 100 ms
      initial_value = zone_p;
    }
    if (current_time <= 200 && current_time > 100)
    {
      // Get the value of the input 500 ms ago
      input_value_500ms_ago = zone_p;
    }
    if (current_time > 200)
    {
      // Get the value of the input 500 ms ago
      current_time = 0;
    }
    // Check if the input changed
    if (abs(initial_value - input_value_500ms_ago) >= 3)
    {
      special_case = TRUE;
    }
  }
  else
  {
    special_case = FALSE;
  }
  if (after_sprint_timer > 1500)
  {
    if (after_l1_timer < 1500)
    {
      tff_accuracy = 120;
    }
    if (after_r1_timer < 1500)
    {
      tff_accuracy = 228;
    }
    else
    {
      tff_accuracy = 170;
    }
  }
  if (after_sprint_timer < 1500)
  {
    tff_accuracy = 450;
  }

  if (after_sprint_timer > 2700)
  {
    PS_ACC = 920;
  }
  else if (after_sprint_timer >= 0 && after_sprint_timer < 2700)
  {
    PS_ACC = 725;
  }

  if (event_press(PlayerRun))
  {
    Trivela_timer = 1000;
  }
  if (event_press(PaceCtrol) || event_press(Timed_Trivela_Shot_Button))
  {
    Trivela_timer = 4000;
  }
  if (get_ival(FinesseShot))
  {
    Trivela_timer = 0;
  }
  if (Trivela_timer)
  {
    Trivela_timer -= Get_rtime();
  }

  UT_Ping = EA_Ping;

  if (event_release(PassBtn))
  {
    Pass_Timer = 1;
  }

  if (event_release(ThroughBall))
  {
    Through_LOP_Timer = 1;
  }

  if (event_release(PlayerRun))
  {
    after_l1_timer = 1;
  }

  if (event_release(FinesseShot))
  {
    after_r1_timer = 1;
  }

  if (get_ival(PassBtn) && cross_timer < 4000 && cross_timer > 3500)
  {
    Tracer_cross = cross_timer;
    cross_timer = 0;

    // set_val(TRACE_6,Tracer_cross );
  }

  if (after_skill_timer)
  {
    after_skill_timer += Get_rtime();
  }

  // set_val(TRACE_6,tf_accuracy)
  if (after_l1_timer)
  {
    after_l1_timer += Get_rtime();
  }

  if (after_r1_timer)
  {
    after_r1_timer += Get_rtime();
  }

  if (cross_timer)
  {
    cross_timer -= Get_rtime();
  }

  if (Pass_Timer)
  {
    Pass_Timer += Get_rtime();
  }

  if (Through_LOP_Timer)
  {
    Through_LOP_Timer += Get_rtime();
  }

  if (shooting_timer)
  {
    shooting_timer -= Get_rtime();
  }

  if (S_V_P)
  {

    if ((after_sprint_timer <= 600 || (after_skill_timer <= 1500 && after_skill_timer > 1)) && get_ival(ShotBtn))
    {
      if (!get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(PaceCtrol) && !get_ival(SprintBtn))
      {

        set_val(ShotBtn, 0);
        if (cross_timer < 4000 && cross_timer > 1)
        {
          set_val(ShotBtn, 0);
          combo_run(vollays);
        }
        else
        {
          set_val(ShotBtn, 0);
          combo_run(power_finishing);
          shooting_timer = 9000;
          //  combo_run(ShotBlock);
        }
      }
    }
  }

  if (TS_on_off)
  {

    if (Single_button_Timed_Shot)
    {
      if (event_press(Timed_Shot_Button))
      {
        set_val(Timed_Shot_Button, 0);
        combo_run(TIMED_FINISH_FULL_cmb);
        shooting_timer = 9000;
      }
    }
    else if (after_sprint_timer > 600 && !shooting_timer && (!get_ival(FinesseShot) && !get_ival(PS4_L3) && event_press(ShotBtn) && (after_sprint_timer > 600 && after_skill_timer > 1500 || (after_sprint_timer > 600))))
    {
      if (!get_ival(PlayerRun) && !get_ival(PaceCtrol) && !get_ival(SprintBtn))
      {
        set_val(ShotBtn, 0);

        if (((Through_LOP_Timer > 1 && Through_LOP_Timer <= 2500) || (Pass_Timer > 1 && Pass_Timer <= 3000)))
        {
          set_val(ShotBtn, 0);
          combo_run(Driven_Finishing);
          shooting_timer = 9000;
          case_indic = 1;
        }
        else if (((Through_LOP_Timer > 2500 && Through_LOP_Timer <= 4000) || (Pass_Timer > 3000 && Pass_Timer <= 3500)))
        {
          set_val(ShotBtn, 0);
          combo_run(power_finishing);
          shooting_timer = 9000;
          case_indic = 2;
        }
        else if ((cross_timer < 4000 && cross_timer > 1))
        {
          set_val(ShotBtn, 0);
          combo_run(vollays);
          shooting_timer = 9000;
          case_indic = 3;
        }
        else
        {
          set_val(ShotBtn, 0);
          case_indic = 4;
          timed_shots();
          //  combo_run(ShotBlock);
          shooting_timer = 9000;
        }

        shooting_timer = 9000;
      }
    }
  }

  //// Timed Power Shots Triggers ////
  if (PS_REST)
  {
    if (Single_button_Timed_PS)
    {
      if (event_press(Timed_Power_Shot_Button))
      {
        set_val(Timed_Power_Shot_Button, 0);
        combo_run(TIMED_PS_FULL_POWER_cmb);
        shooting_timer = 9000;
        // combo_run(SOMBRERO_FLICK_cmb);
      }
      if (combo_running(TIMED_PS_FULL_POWER_cmb))
      {
        if (get_ival(PassBtn) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(SprintBtn) > 30)
        {

          combo_stop(TIMED_PS_FULL_POWER_cmb);
        }
      }
    }
    else if (get_ival(PlayerRun) && get_ival(FinesseShot))
    {
      if (!get_ival(PaceCtrol) && !get_ival(SprintBtn) && (cross_timer && Pass_Timer > 1 && Pass_Timer <= 1500) || (!cross_timer && Pass_Timer > 1 && Pass_Timer <= 1500) || (Pass_Timer > 1500 && !cross_timer) && !shooting_timer)
      {
        if (event_press(ShotBtn))
        {
          set_val(ShotBtn, 0);
          combo_run(TIMED_PS_FULL_POWER_cmb);
          shooting_timer = 9000;
        }
      }
    }
  }
  //// Timed Trivela Triggers ////
  if (TTS_on_off)
  {
    if (Single_button_Timed_Trivela)
    {
      if (event_press(Timed_Trivela_Shot_Button))
      {
        set_val(Timed_Trivela_Shot_Button, 0);

        TIMED_TRIVELA_CONDITIONS();
        shooting_timer = 9000;
      }
    }
    else if (!get_ival(SprintBtn) && !get_ival(PlayerRun) && !get_ival(FinesseShot))
    {
      if (get_ival(PaceCtrol) && get_ival(ShotBtn))
      {

        TIMED_TRIVELA_CONDITIONS();
        set_val(ShotBtn, 0);
        shooting_timer = 9000;
      }
    }
  }

  //// Timed Finesse Triggers ////
  if (TFS_on_off)
  {
    if (Single_button_Timed_FINESSE)
    {
      if (event_press(Timed_FINESSE_Button))
      {
        set_val(Single_button_Timed_FINESSE, 0);
        combo_run(TFF_FULL_POWER_cmb);
        shooting_timer = 9000;
      }
    }
    else if (get_ival(FinesseShot) && !get_ival(PlayerRun))
    {
      if (!get_ival(PaceCtrol) && !get_ival(SprintBtn) && !Trivela_timer && !shooting_timer)
      {
        if (get_ival(ShotBtn) && after_sprint_timer >= 1000)
        {
          set_val(ShotBtn, 0);
          combo_run(TFF_FULL_POWER_cmb);
          shooting_timer = 9000;
        }
        if (get_ival(ShotBtn) && after_sprint_timer < 1000)
        {
          set_val(ShotBtn, 0);
          combo_run(TFF_Inside);
        }
      }
    }
  }

  if (get_ival(PlayerRun))
  { // power shot confliction fix
    combo_stop(TFF_FULL_POWER_cmb);
    combo_stop(TIMED_FINISH_FULL_cmb);
  }
  // CHIP SHOT FINISH v.1.1
  if (get_ival(PlayerRun) && !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot) && !get_ival(PS4_L3))
  {
    if (get_ival(ShotBtn))
    {
      set_val(ShotBtn, 0);
      combo_run(Chip_Finish_cmb);
    }
  }

  if (shooting_timer)
  {
    shooting_timer -= Get_rtime();
  }
  if (combo_running(power_finishing) || combo_running(OutSideBox_Finishing_cmb) || combo_running(TIMED_FINISH_FULL_cmb) || combo_running(TIMED_PS_FULL_POWER_cmb) || combo_running(TFF_FULL_POWER_cmb))
  {
    if (get_ival(PassBtn) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(SprintBtn) > 30)
    {
      combo_stop(OutSideBox_Finishing_cmb);
      combo_stop(TIMED_FINISH_FULL_cmb);
      combo_stop(TIMED_PS_FULL_POWER_cmb);
      combo_stop(TFF_FULL_POWER_cmb);
      combo_stop(power_finishing);
    }
  }
  if (combo_running(power_finishing) || combo_running(Driven_Finishing))
  {
    if (get_ival(PassBtn) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(SprintBtn))
    {
      combo_stop(vollays);
      combo_stop(Driven_Finishing);
      combo_stop(power_finishing);
    }
  }
  if (event_press(ShotBtn) && shooting_timer > 1 && shooting_timer < 8990)
  {
    set_val(ShotBtn, 0);
    combo_stop(OutSideBox_Finishing_cmb);
    combo_stop(TIMED_FINISH_FULL_cmb);
    combo_stop(TIMED_PS_FULL_POWER_cmb);
    combo_stop(TFF_FULL_POWER_cmb);
    combo_stop(power_finishing);
    combo_run(REBOUND);
  }
  // set_val(TRACE_3,shooting_timer);
  /// avoid conflictions with (instant skills ) mod .
  if (!Single_button_Timed_PS)
  {
    if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS))
    {
      combo_stop(TIMED_PS_FULL_POWER_cmb);
    }
  }
  if (!Single_button_Timed_FINESSE)
  {
    if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS))
    {
      combo_stop(TFF_FULL_POWER_cmb);
    }
  }
  if (!Single_button_Timed_Trivela)
  {
    if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS))
    {
      combo_stop(OutSideBox_Finishing_cmb);
    }
  }
  if (!Single_button_Timed_Shot)
  {
    if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS))
    {
      combo_stop(TIMED_FINISH_FULL_cmb);
    }
  }
  if (get_ival(SprintBtn) || get_ival(PassBtn))
  {
    combo_stop(STEP_OVER_FEINT);
    combo_stop(FAKE_BARBA_OPP_EXIT_cmb);
    combo_stop(BALL_ROLL_SCOOP_TURN);
  }
}
const uint16 Final_Angles[] = {135, 45, 315, 225}

define Angle_Time = 19;
define Angle_Speed_Multipler = 2;

int final_angle;
int L_direction;
int radius;
int cur_angle;
int angle_timer;
int angle_out;
int check_manual = 0;
function aim_box()
{

  if (check_manual == 0)
  {
    if (get_ival(ShotBtn) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500)
    {

      cur_angle = atan2_angle(get_ival(POLAR_LX), get_ival(POLAR_LY));
      final_angle = Final_Angles[cur_angle / 90];
      L_direction = (((final_angle - cur_angle + 540) % 360 >= 180) * 2 - 1) * Angle_Speed_Multipler;
      radius = min(isqrt(pow(get_ival(POLAR_LX), 2) + pow(get_ival(POLAR_LY), 2)), 32767);
    }

    if (abs(cur_angle - final_angle) >= Angle_Speed_Multipler)
    {
      angle_timer += Get_rtime();
      if (angle_timer >= Angle_Time)
      {
        cur_angle += L_direction;
        if (cur_angle < 0)
          cur_angle += 360;
        if (cur_angle >= 360)
          cur_angle -= 360;
        angle_timer = 0;
      }
      set_polar(POLAR_LS, cur_angle, radius);
    }
  }
}
function atan2_angle(f_x, f_y)
{
  if (f_x >= 0 && f_y > 0)
    angle_out = -90000;
  else if (f_x < 0 && f_y >= 0)
    angle_out = 90000;
  else if (f_x <= 0 && f_y < 0)
    angle_out = -270000;
  else
    angle_out = 270000;
  f_x = abs(f_x);
  f_y = abs(f_y);
  if (f_x < f_y)
    angle_out += (f_x * 45000 / f_y);
  else if (f_x > f_y)
    angle_out += 90000 - (f_y * 45000 / f_x);
  else
    angle_out += 45000;
  return abs(angle_out) / 1000;
}
int UltimateFinish_on_off = TRUE;
int sp_inside_box = 180;
int sp_outside_box = 230;
int Ultimate_Finishing = TRUE;
int after_cross_timer;
combo power_finishing
{
  // set_val(SprintBtn,100);
  aim_box();
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(random(212, 225));
  aim_box();
  /*if(!get_ival(SprintBtn) && !get_ival(PaceCtrol)){set_val(PaceCtrol,100);}
  set_val(ShotBtn, 0);
  aim_box();
  vm_tctrl(0);wait(60);
  if(!get_ival(SprintBtn) && !get_ival(PaceCtrol)){set_val(PaceCtrol,100);}
  set_val(ShotBtn, 0);
  aim_box();
  vm_tctrl(0);wait(60);
  if(!get_ival(SprintBtn) && !get_ival(PaceCtrol)){set_val(PaceCtrol,100);}
  set_val(ShotBtn, 0);
  aim_box();
  vm_tctrl(0);wait(60);
  if(!get_ival(SprintBtn) && !get_ival(PaceCtrol)){set_val(PaceCtrol,100);}
  set_val(ShotBtn, 0);
  aim_box();
  vm_tctrl(0);wait(60);
  if(!get_ival(SprintBtn) && !get_ival(PaceCtrol)){set_val(PaceCtrol,100);}
  */
  set_val(ShotBtn, 0);
  aim_box();
  vm_tctrl(0);
  wait(600);
}
int power_hoster;
function timed_shots()
{
  if (after_sprint_timer > 600 && after_sprint_timer <= 800)
  {
    tf_accuracy = 240;
  }
  if (after_sprint_timer > 800 && after_sprint_timer <= 1000)
  {
    tf_accuracy = 230;
  }
  if (after_sprint_timer > 1000 && after_sprint_timer <= 1500)
  {
    tf_accuracy = 225;
  }
  if (after_sprint_timer > 1500 && after_sprint_timer <= 2000)
  {
    tf_accuracy = 235;
  }
  if (after_sprint_timer > 2000)
  {
    tf_accuracy = 218;
  }
  aim_box();
  combo_run(TIMED_FINISH_FULL_cmb);
}
combo Driven_Finishing
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(random(100, 120));
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(500);
}
combo vollays
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(205);
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(300);
}
combo REBOUND
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(190);
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(400);
}
int TRACER;
int angle_host;
function aim_trivela()
{
  PlayerRunAngleX = get_ival(XB1_LX);
  PlayerRunAngleY = get_ival(XB1_LY);
  if (PlayerRunAngleX > 15)
  {
    set_val(XB1_LX, 37);
  }
  else
  {
    set_val(XB1_LX, -37)
  }
  if (PlayerRunAngleY > 15)
  {
    set_val(XB1_LY, 100);
  }
  else
  {
    set_val(XB1_LY, -100)
  }
}
int Trivela_ROLL;
int roll_time;
int Trivela_Roll_Dir;
combo OutSideBox_Finishing_cmb
{
  if (Trivela_ROLL)
  {
    set_val(ShotBtn, 0);
    roll_time = 400;
  }
  else
  {
    roll_time = 0;
  }

  RA(0, Trivela_Roll_Dir);
  LA(0, 0);
  vm_tctrl(0);
  wait(roll_time);
  if (Trivela_ROLL)
    LA(0, LY);
  set_val(ShotBtn, 0);
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(40);
  if (Trivela_ROLL)
    LA(0, LY);
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(45);
  set_val(ShotBtn, 100);
  set_val(PaceCtrol, 100);
  if (Trivela_ROLL)
    LA(0, LY);
  vm_tctrl(0);
  wait(random(265, 268)); /////
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  if (Trivela_ROLL)
    LA(0, LY);
  vm_tctrl(0);
  wait(DYN_Acc + UT_Ping - 80);
  if (Trivela_ROLL)
    LA(0, LY);
  vm_tctrl(0);
  wait(60);
  INSIDE_BOX_AIM(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  if (Trivela_ROLL)
    LA(0, LY);
  vm_tctrl(0);
  wait(60); // TIMED Action
  set_val(ShotBtn, 0);
  set_val(PaceCtrol, 100);
  if (Trivela_ROLL)
    LA(0, LY);
  vm_tctrl(0);
  wait(80);
  if (Trivela_ROLL)
    LA(0, LY);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(900);
}

int Through_LOP_Timer;
int cross_timer;
int after_sprint_timer;
int UltimatePower = 260;
int DrivenShot;
int Pass_Timer;
int UT_Ping;       //
int DYN_Acc = 111; //
int power_time;
int final_power;
int my_angle; // finishing angle
// const int16 Finishing_Anlges[] = {60,300,120,240};
int Trivela_timer;
int Tracer_cross;
int Tracer_sprint;
int Tracer_pass;
int Trivela_Angel;
function TIMED_TRIVELA_CONDITIONS()
{
  Trivela_Angel = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
  if ((Trivela_Angel > 10 && Trivela_Angel < 80) || (Trivela_Angel > 110 && Trivela_Angel < 170))
  {
    Trivela_Roll_Dir = 100;
  }

  if ((Trivela_Angel < 350 && Trivela_Angel > 280) || (Trivela_Angel < 260 && Trivela_Angel > 190))
  {
    Trivela_Roll_Dir = -100;
  }

  Tracer_sprint = after_sprint_timer;
  Tracer_pass = Pass_Timer;

  TRACER = after_sprint_timer;

  if (cross_timer == 0 && (after_sprint_timer >= 750 || ((Tracer_cross > 3000 && Pass_Timer > 1 && Pass_Timer < 5000))))
  {
    if (after_sprint_timer <= 2000 && Pass_Timer > 1500)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 170;
    }
    if (after_sprint_timer <= 2000 && Pass_Timer > 1 && Pass_Timer <= 1500)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 202;
    }
    if (after_sprint_timer > 2000 || (Pass_Timer > 1 && Pass_Timer <= 1500))
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 150;
    }

    if ((after_sprint_timer > 2000 && Pass_Timer > 1500) || Tracer_cross > 1 && Pass_Timer > 1)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 138;
    }

    if ((after_sprint_timer < 2000 && Pass_Timer > 1500) || cross_timer > 1 && Pass_Timer > 1)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 130;
    }

    if (Pass_Timer > 1500)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 130;
    }

    if (!after_sprint_timer > 2000 && Tracer_cross > 1 && Pass_Timer > 1 && Pass_Timer <= 1500)
    {
      set_val(ShotBtn, 0);
      DYN_Acc = 104;
    }
    set_val(ShotBtn, 0);

    combo_stop(TIMED_FINISH_FULL_cmb);
    combo_stop(TIMED_PS_FULL_POWER_cmb);
    combo_stop(TFF_FULL_POWER_cmb);
    combo_stop(power_finishing);
    combo_stop(Trivela_inbox_cmb);
    combo_stop(REBOUND);
    combo_stop(vollays);
    combo_run(OutSideBox_Finishing_cmb);
  }
  else
  {
    if (cross_timer)
    {
      set_val(ShotBtn, 0);

      combo_run(fancy_Finishing_cmb);
    }
    else
    {
      if (after_sprint_timer < 750)
      {
        set_val(ShotBtn, 0);

        combo_run(Trivela_inbox_cmb);
      }
    }
  }

  /*
  set_val(TRACE_1,DYN_Acc);
  set_val(TRACE_2,Pass_Timer);
  set_val(TRACE_3,TRACER);
  set_val(TRACE_4,cross_timer);
  */
}
combo fancy_Finishing_cmb
{
  set_val(ShotBtn, 100);
  aim_trivela();
  vm_tctrl(0);
  wait(random(200, 205));

  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(700);
}
combo Trivela_inbox_cmb
{
  set_val(ShotBtn, 100);
  aim_trivela();
  vm_tctrl(0);
  wait(random(230, 235));
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(700);
}

int no_rest;
int how_much_sprint;
int tff_power = 222 int tff_accuracy = 150; // time between shot and the timed shot ((pause time)).
int tff_ping = TRUE;
int TFF_Ping = 0;
int tff_start_btn;
combo TFF_FULL_POWER_cmb
{
  aim_host = my_angle if (tff_ping) { TFF_Ping = EA_Ping; }
  else
  {
    TFF_Ping = 0;
  }
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 100);
  aim_trivela();
  vm_tctrl(0);
  wait(tff_power + random(-5, 5));
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 0);
  aim_trivela();
  vm_tctrl(0);
  wait(tff_accuracy + TFF_Ping - 20); // time between shot and the timed shot ((pause time)).
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 100);
  aim_trivela();
  vm_tctrl(0);
  wait(tff_power);
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 0);
  aim_trivela();
  vm_tctrl(0);
  wait(2000);
}
combo TFF_Inside
{
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(200);
  set_val(FinesseShot, 100);
  set_val(ShotBtn, 0);
  vm_tctrl(0);
  wait(700);
}
int shooting_timer;
int tf_power = 220;
int tf_accuracy;
int tf_ping = TRUE;
int TimedFinish_Ping = 0;
int tf_start_btn;
int QT_TimedFinish = TRUE;
int AimLock = TRUE;
int aim_host;
combo TIMED_FINISH_FULL_cmb
{
  if (tf_ping)
  {
    TimedFinish_Ping = EA_Ping;
  }
  else
  {
    TimedFinish_Ping = 0;
  }

  set_val(ShotBtn, 100);
  // set_polar(POLAR_LS,my_angle,32767);
  vm_tctrl(0);
  wait(tf_power);

  set_val(ShotBtn, 0);
  // Finish_Aim();
  // set_polar(POLAR_LS,aim_host,32767);
  vm_tctrl(0);
  wait(tf_accuracy + (TimedFinish_Ping));

  set_val(ShotBtn, 100);
  // Finish_Aim();
  // set_polar(POLAR_LS,aim_host,32767);
  vm_tctrl(0);
  wait(tf_power);
  set_val(ShotBtn, 0);
  // Finish_Aim();
  // set_polar(POLAR_LS,aim_host,32767);
  vm_tctrl(0);
  wait(1000);
}
int ps_ping = TRUE; ///
int PS_ACC;
int ps_power = 260;
int PowerShotPing = 0;
combo TIMED_PS_FULL_POWER_cmb
{
  set_val(PlayerRun, 100);
  set_val(FinesseShot, 100);
  if (ps_ping)
  {
    PowerShotPing = EA_Ping;
  }
  else
  {
    PowerShotPing = 0
  }
  set_val(ShotBtn, 100);
  // set_polar(POLAR_LS,my_angle,32767);
  vm_tctrl(0);
  wait(ps_power);
  // set_polar(POLAR_LS,my_angle,32767);
  vm_tctrl(0);
  wait(PS_ACC + PowerShotPing + 40) // Animation + AimLock (1)
                                    // if(!cross_timer)set_val(ShotBtn    ,100); // Timed _ Shot
  // set_polar(POLAR_LS,my_angle,32767);
  //    vm_tctrl(0);wait(60);
  // Finish_Aim();
  // set_polar(POLAR_LS,my_angle,32767);
  // vm_tctrl(0);wait(2500) // Animation + AimLock (2)
}
int KS_EntireScript;
int Chip_Power = 145; // Power of chip shot determind by user in S.G
combo Chip_Finish_cmb
{
  set_val(ShotBtn, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(Chip_Power);
  vm_tctrl(0);
  wait(500);
}

int GP_difference;
int ground_pass_timer;
combo Ground_Pass_MIN_cmb
{
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(GP_difference);
  set_val(PassBtn, 0);
  vm_tctrl(0);
  wait(30);
  /*if(DoubletapGroundPass){
    set_val(PassBtn,100);
    }*/
  vm_tctrl(0);
  wait(60);
}

int TroughP_difference;
int trough_pass_timer;
int trough_start;

combo Trough_Pass_MIN_cmb
{
  set_val(ThroughBall, 100);
  vm_tctrl(0);
  wait(TroughP_difference);
  set_val(ThroughBall, 0);
  vm_tctrl(0);
  wait(30);
  /*	if(DoubleTapTroughPass){
      set_val(ThroughBall,100);
    }*/
  vm_tctrl(0);
  wait(60);
}
combo DOUBLE_TAP_TROUGH_cmb
{
  set_val(ThroughBall, 0);
  vm_tctrl(0);
  wait(30);
  set_val(ThroughBall, 100);
  vm_tctrl(0);
  wait(60);
}

//=================================
int Lob_pass_timer;
int LobP_difference;

combo Lob_Pass_MIN_cmb
{
  set_val(CrossBtn, 100);
  vm_tctrl(0);
  wait(LobP_difference);
}

int MoveMentsBooster;
int New_Enh_Move_on_off = TRUE;
int SP_Release;
int RUN_Pass_Release;
int PARTIAL_TEAM_PRESS;
int PROTECTION;
int disentsBooster;
int LS_Angle_ext;
int Rotation = 90;
function Boost_Radius()
{

  /* LS_Angle_ext = get_polar(POLAR_LS, POLAR_ANGLE)    // POLAR LS ANGL
    if(get_ival(SprintBtn) > 30) {MoveMentsBooster = abs(JK_AGG)} else {MoveMentsBooster = JK_AGG;}
       if(abs(get_ival(PS4_LX))> 85 || abs(get_ival(PS4_LY))> 85){
        if(!combo_running(DEF_cmb) && ((LS_Angle_ext <= 30 + Rotation && LS_Angle_ext >= 0 + Rotation ) || (LS_Angle_ext <= 360 + Rotation  && LS_Angle_ext > 325 + Rotation) || (LS_Angle_ext >= 145 + Rotation && LS_Angle_ext < 215 + Rotation) ) ){
           if(JK_AGG < 0){set_polar(POLAR_LS,360 - get_ipolar(POLAR_LS,POLAR_ANGLE),32767 - (2500*abs(JK_AGG)))}
           if(JK_AGG > 0){
             if (get_ival(PS4_LX) > 12) set_val(PS4_LX, get_ival(PS4_LX) + MoveMentsBooster  + 2);
             if (get_ival(PS4_LX) < -12) set_val(PS4_LX, get_ival(PS4_LX) - MoveMentsBooster - 2);
             if (get_ival(PS4_LY) > 12) set_val(PS4_LY, get_ival(PS4_LY) + MoveMentsBooster  + 2);
             if (get_ival(PS4_LY) < -12) set_val(PS4_LY, get_ival(PS4_LY) - MoveMentsBooster - 2);
         }
       }
   }*/
  //
}

define PolarAssist = TRUE;
define Radius = 90;
define Speed = 10;
define Release = 70;
// Polar Recoil
define PolarRecoil = FALSE;
define Strength = 50;
define Angle2 = 90;
int aimCorrection = TRUE;
define Ads = XB1_LT;
define Fire = XB1_RT;
define axisX = XB1_LX;
define axisY = XB1_LY;
define aimStick = POLAR_LS;
int AimAssistAngle;
int GenStr;            // Do not touch this
int AdsSens = 90;      // ADS Only
int AdsFireSens = 100; // ADS & FIRE
int FireSens = 80;     // FIRE Only
int GeneralSens = 100; // NO ADS or FIRE

int fValue, fValueSens;
function customPolarSens(fId, fMid, fIndex)
{
  fValue = get_val(fId);
  if (fMid != NOT_USE)
  {
    fValueSens = -1;
    if (fValue >= 0)
      fValueSens = 1;
    fValue *= fValueSens;
    if (fValue <= fMid)
      fValue = (fValue * 16384) / fMid;
    else
      fValue = ((16384 * (fValue - fMid)) / (32767 - fMid)) + 16384;
    fValue *= fValueSens;
  }
  if (fIndex != NOT_USE)
  {
    fValue = (fValue * fIndex) / 32767;
  }
  set_val(fId, clamp(fValue, -32768, 32767));
  return;
}
function PolarRecoil(Output)
{
  set_polar(aimStick, Angle2, Output * 328);
}
function Jockey_Support()
{

  // Support jockey or Defence Mode with enhanced movements
  if (get_ival(SprintBtn) > 30 && (get_ival(PaceCtrol) || get_ival(PassBtn)) && (!get_ival(CrossBtn) || !get_ival(ShotBtn)))
  {
    // set_val(PassBtn,0);
    //  if(!get_val(PS4_R3))set_val(PS4_R3,100);
    combo_run(DEF_cmb);
    set_val(SprintBtn, 0);
    ASSIST_FIFA();
    // AngleInterval = 360;
  }
  else
  {
    combo_stop(DEF_cmb);
  }
}

function ASSIST_FIFA()
{
  if (PolarAssist)
  {

    if (abs(get_ival(axisX)) < Release && abs(get_ival(axisY)) < Release)
    {
      set_polar(aimStick, AimAssistAngle = (AimAssistAngle + Speed) % 360, Radius * 327);
    }
  }

  if (aimCorrection)
  {

    GenStr = AdsSens;

    customPolarSens(POLAR_LX, 16384, GenStr * 32767 / 100);
    customPolarSens(POLAR_LY, 16384, GenStr * 32768 / 100);
  }
}
int AngleInterval; // (360 / 15 Degrees) = 24
combo DEF_cmb
{
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(JK_AGG);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 0);
  vm_tctrl(0);
  wait(300);
}

function Get_rtime()
{
  if (vm_speed_onoff == 0)
    return 10;
  if (vm_speed_onoff == 1)
    return 12;
  if (vm_speed_onoff == 2)
    return 8;
  if (vm_speed_onoff == 2)
    return 6;
  if (vm_speed_onoff == 4)
    return 4;
  if (vm_speed_onoff == 5)
    return 2;

  // Default case, return a suitable value
  return 10; // You can choose an appropriate value here
}

// int Polar_Enh_Move_on_off = TRUE;
/*
=================================================================
 EM Polar V2
=================================================================
*/
int POLAR_RESET = TRUE;
int EM_Angles_Control;
int Passing_Angle;
int X_AngleInterval;
// int Radius;

int roll_angle;
int ZX_GPass_MIN = 35;
int ZX_GP_diff;
int ZX_gpass_timer;

function LA_DOWN()
{
  set_val(PS4_LX, inv(LX));
  set_val(PS4_LY, inv(LY));
}

combo PTP_cmb
{
  // set_val(XB1_LT,100);
  // wait(random(50,60));
  // set_val(XB1_LT,0);
  set_val(FinesseShot, 100);
  wait(random(50, 60));
  set_val(FinesseShot, 0);
  wait(random(50, 60));
  set_val(FinesseShot, 100);
  wait(random(450, 500));
  wait(random(160, 180));
}

function set_right_or_left()
{
  right_on = FALSE;
  if (zone_p == 4 || zone_p == 3 || zone_p == 7)
  {
    right_on = TRUE;
  } ///
}