 
                                                                       
                                                                       
/* 
                                                                       
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\    $$\          
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$$$ |         
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\_$$ |         
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  |  $$ |         
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/   $$ |         
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |        $$ |         
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$\        
  \__|      \______|\__|      \__|  \__|      \________|\______|       
                                                                         
 */
                                                                         
//  SPEED BOOST DRIBBLE  v. 1.0



// YOUR BUTTON LAYOUT 
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_R1; // Finesse Shot
define PlayerRun     = PS4_L1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
                                                                 
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL          =40;  
define CANCEL_SHOOT_SKILL             =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;
define CANCELED_BERBA_SPIN_SKILL      =43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;

int switch;
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                
                                                
                                      
    // ON / OFF All Skills          
    if(get_val(PS4_SHARE)){                 
         if(event_press(PS4_CROSS))OnOffMods = !OnOffMods; 
         set_val(PS4_CROSS,0);                        
    }                                        
                                                
                                                
    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){    
	    LX = get_val(MOVE_X);                                      
	    LY = get_val(MOVE_Y);                                      
     calc_zone ();                                              
    }                                                           
    //-----------------------------------------------------------
                                      
   if(OnOffMods) { // all Skills mode           
                                                
		if(get_val(PS4_L2)){
			if(event_press(PS4_UP)) switch = TRUE;
			if(event_press(PS4_DOWN)) switch = FALSE;
			
			set_val(13,0);
			set_val(14,0);
			
		}
                                                
// Skill 1  : 42. Boosted Directional Nutmeg => one tap : PS4_L1
      
//                                                        
//                                                        
// Skill 3  : 20. Berba / Mcgeady Spin => one tap : PS4_L3
      if (event_press(PS4_L3)) {      
            
            if(switch) {
            	right_on = FALSE; 
            	ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION; 
            	combo_run(SPEED_BOOST);
            }else{
            	right_on = FALSE; 
            	ACTIVE = CANCELED_BERBA_SPIN_SKILL; 
            	combo_run(SPEED_BOOST_2);  
        	}
      }
      set_val(PS4_L3,0);       
//                                                        
// Skill 4  : 20. Berba / Mcgeady Spin => one tap : PS4_R3
      if (event_press(PS4_R3)){       
            if(switch) {
            	right_on = TRUE; 
            	ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION; 
            	combo_run(SPEED_BOOST);
            }else{
            	right_on = TRUE; 
            	ACTIVE = CANCELED_BERBA_SPIN_SKILL; 
            	combo_run(SPEED_BOOST_2);  
        	}
      }
      set_val(PS4_R3,0);       
//                                                        
                                                
                                                
                                                
   }//--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////   
// 43. Cancel Berba Spin  44. Cencel Berba Spin with Direction


combo SPEED_BOOST {  
    	        
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	
	wait(300);
	
	set_val(SprintBtn,100);
	
	wait(500);
	
	
} 

combo SPEED_BOOST_2 {  
    
    set_val(PlayerRun,100);
    
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
	
	wait(40);
	
    LA (move_lx,move_ly);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	
	wait(200);
	
	
	set_val(SprintBtn,100);
	
	wait(500);
	
	
}           
combo NUTMEG_SKILL {
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	wait(20);
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	wait(100);
    
}
///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             