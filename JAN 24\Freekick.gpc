int DA_FC370;
// 10  Free Kick

/* Adjustable Variables */
int DA_FC424;
// 26

int DA_FC447 ;
// 42

const string DA_FC611 = "FK_POWER";

main {
if (DA_FC370) {
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_SHARE)) {
			DA_FC609 = !DA_FC609;
			DA_FC243(DA_FC609);
										}
		set_val(PS5_SHARE, 0);
								}
						}
if (DA_FC609 && DA_FC370) {
	vm_tctrl(0);
	combo_stop(DA_FC86);
	if (get_ival(XB1_RS)) {
		if (event_press(PS5_UP)) {
			DA_FC424 += 10;
			DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
										}
		if (event_press(PS5_DOWN)) {
			DA_FC424 -= 10;
			DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
										}
		set_val(PS5_UP, 0);
		set_val(PS5_DOWN, 0);
								}
	DA_FC236(DA_FC1052);
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_RIGHT)) {
			DA_FC616 = FALSE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		if (event_press(PS5_LEFT)) {
			DA_FC616 = TRUE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		set_val(PS5_L1,0);
								}
						}
}

combo DA_FC86 {
vm_tctrl(0);
wait(750);
set_val(DA_FC449,100);
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);
if(DA_FC1123 == 1 ){
set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)}
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);

	}
	
combo DA_FC87 {
	vm_tctrl(0);
	wait( 800);
	DA_FC1147 = 0;
	}
	
///////////FK Combos//////////
int DA_FC609;
int DA_FC616;
combo DA_FC78 {
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, -100);
	vm_tctrl(0);
	wait( 70);
	if (DA_FC616) set_val(XB1_RX, 100);
	else set_val(XB1_RX, -100);
	set_val(XB1_RY, 100);
	vm_tctrl(0);
	wait( 2000);
	if (DA_FC616) set_val(XB1_RX, -100);
	else set_val(XB1_RX, 100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 200);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC424);
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, 100);
	set_val(XB1_LY,100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 1200);
	DA_FC609 = FALSE;
	DA_FC243(DA_FC609);
	}	
	
function DA_FC243(DA_FC127) {
	if (DA_FC127) DA_FC1096 = RUMBLE_A;
	else DA_FC1096 = RUMBLE_B;
	combo_run(DA_FC81);
	}
function DA_FC224(DA_FC133, DA_FC226, DA_FC127) {
	cls_oled(0);
	line_oled(1, 18, 127, 18, 1, 1);
	print(DA_FC133, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC226);
	DA_FC231(DA_FC127, DA_FC234(DA_FC127));
	DA_FC356 = TRUE;
	}
	
//=================================================================
//Center X Function (Made By Batts) 
//=================================================================

function DA_FC228(DA_FC142, DA_FC136) {
	return (OLED_WIDTH / 2) - ((DA_FC142 * DA_FC136) / 2);
	}
	
int DA_FC1056;
function DA_FC236(DA_FC169) {
	for (DA_FC1056 = 0;
	DA_FC1056 < 3;
	DA_FC1056++) {
		set_rgb(data[DA_FC169][0], data[DA_FC169][1], data[DA_FC169][2]);
			}
	}
	
	
	int DA_FC625;
define DA_FC1048 = 0;
define DA_FC1049 = 1;
define DA_FC1050 = 2;
define DA_FC1051 = 3;
define DA_FC1052 = 4;
define DA_FC1053 = 5;
define DA_FC1054 = 6;
define DA_FC1055 = 7;

int DA_FC1096;
combo DA_FC81 {
	set_rumble(DA_FC1096, 100);
	vm_tctrl(0);
	wait( 300);
	reset_rumble();
	vm_tctrl(0);
	wait( 20);
	}
	
function DA_FC231(DA_FC127, DA_FC159) {
	DA_FC1038 = 1;
	DA_FC1040 = 10000;
	if (DA_FC127 < 0) {
		//--neg numbers
		putc_oled(DA_FC1038, 45);
		//--add leading "-"
		DA_FC1038 += 1;
		DA_FC127 = abs(DA_FC127);
			}
	for (DA_FC1039 = 5;
	DA_FC1039 >= 1;
	DA_FC1039--) {
		if (DA_FC159 >= DA_FC1039) {
			putc_oled(DA_FC1038, (DA_FC127 / DA_FC1040) + 48);
			DA_FC127 %= DA_FC1040;
			DA_FC1038++;
			if (DA_FC1039 == 4) {
				putc_oled(DA_FC1038, 44);
				//--add ","
				DA_FC1038++;
							}
					}
		DA_FC1040 /= 10;
			}
	puts_oled(DA_FC228(DA_FC1038 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA_FC1038 - 1, OLED_WHITE);
	}

int DA_FC1044;
function DA_FC234(DA_FC235) {
	DA_FC1044 = 0;
	do {
		DA_FC235 /= 10;
		DA_FC1044++;
			}
	while (DA_FC235);
	return DA_FC1044;
	}
int DA_FC356 = FALSE, DA_FC357;

int DA_FC906;
function DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169) {
	// Decrement display buffer value for proper arithmetic
	DA_FC906--;
	// Check horizontal alignment
	switch(DA_FC166) {
		case DA_FC919 {
			DA_FC166 = OLED_WIDTH - (DA_FC906 * DA_FC1310[DA_FC168]) - 4;
			// Additional 4 for padding from border
			break;
					}
		case DA_FC918 {
			DA_FC166 = (OLED_WIDTH >> 1) - ((DA_FC906 * DA_FC1310[DA_FC168]) >> 1);
			break;
					}
		// No alignLeft case is needed because alignLeft is set to the proper left alignment already
	}
	// Check vertical alignment
	switch(DA_FC167) {
		case DA_FC918 {
			DA_FC167 = (OLED_HEIGHT >> 1) - (DA_FC1309[DA_FC168] >> 1);
			break;
					}
		case DA_FC921 {
			DA_FC167 = OLED_HEIGHT - DA_FC1309[DA_FC168] - 4;
			// Additional 4 for padding from border
			break;
					}
		// No alignTop case is needed because alignTop is set to the proper top alignment already
	}
	puts_oled(DA_FC166, DA_FC167, DA_FC168, DA_FC906, DA_FC169);
	// Output display buffer
	DA_FC906 = 1;
	// Reset display buffer
}

data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0);
const int16 data[][] = {
	{
		0,    0,    0  
	}
	, //0. ColorOFF
	  {
		0,    0,    255  
	}
	, //1. Blue     
	  {
		255,    0,    0  
	}
	, //2. Red      
	  {
		0,    255,    0  
	}
	, //3. Green    
	  {
		255,    0,    255  
	}
	, //4. Pink     
	  {
		0,    255,    255  
	}
	, //5. SkyBlue 
	  {
		255,    255,    0  
	}
	, //6. Yellow   
	  {
		255,    255,    255  
	}
	//7. White    
}
;

int DA_FC1038;
int DA_FC1039, DA_FC1040; 