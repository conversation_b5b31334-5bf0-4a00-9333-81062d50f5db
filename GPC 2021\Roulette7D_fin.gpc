unmap 24;  // Paddle Right 1
unmap 25;  // Paddle Right 2
unmap 26;  // Paddle Left  1
unmap 27;  // Paddle Left  2
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define DOnotUSE      =   1;
define DelayNormal   =  80;
define DelayRnbwHOLD = 160;
define DelayRest     = 200;
define DelayLeft_move= 500;
define time_to_dblclick     = 300; // Time to Double click     

define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_PL1; 
//define MODIFIER2     = XB1_PL2;  
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;     
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
define SCOOP_TURN_FAKE_SKILL          =49; // FIFA 22
define BALL_ROLL_STEP_OVER_SKILL      =50; // FIFA 22
define CANCELED_4_TOUCH_TURN_SKILL    =51; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;
define UP_RIGHT   = 1;
define RIGHT      = 2;
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;

int ACTIVE;                                     
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;
int OnOffMods = TRUE;      
int Sombrero;  
int hold_btn = 200;
int b_tap; 
int onoff_penalty;
int rs_move;
int start;
int LS_Sens_change;

define SKILLR_X      = PS4_RX
define SKILLR_Y      = PS4_RY 
define CrossingBtn =  XB1_X
                                           
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {   
	// SPRINT
	if(get_val(XB1_RT) && event_press(XB1_LB)){combo_run(SPRINT_cmb);} 
	if(get_val(XB1_LB) && event_press(XB1_B)){combo_run(OutSide_Box_Shoot);} 
   //--- DEFENSE                               
   if(defence_on) f_defence();   
   
    //========================================== 
    // SUPER KNOCK ON                            
    if(event_press(SprintBtn) && !tap ) combo_run(ONE_TAP); 
                                                 
    if(event_press(SprintBtn) &&  tap ){     
    	combo_run(SPRINT_cmb);       
    }                                               
	//--------------------------------------------------------------
	// Choose ZEN SLOT 2
	if (get_val(PS4_SHARE) ) {
      if(event_release(PS4_R3)) {
             load_slot (2);
      }
      set_val(PS4_R3,0);
	}
	// Choose ZEN SLOT 0 (STANDARD)
	if (get_val(PS4_SHARE) ) {
      if(event_release(XB1_A)) {
             load_slot (0);
      }
      set_val(PS4_R3,0);
	}
	//-------------------------------------------------------------- 
    //--- Use Open Up MOD                                                         
    if(event_press(ShotBtn) || event_press(PassBtn) || event_press(FinesseShot)){  
        combo_run(OPEN_UP_cmb);                                                   
    }                                                                             
	//--------------------------------------------------------------

	//--------------------------------------------------------------
    if(abs(get_val(MOVE_X))> 45 || abs(get_val(MOVE_Y))> 45){
        LS_Sens_Corect = 100 - LS_Sens;                      
        //================================================== 
        if(get_val(MOVE_X) > 45){
            LX = ( get_val(MOVE_X) + (LS_Sens_Corect) );
        }
        if(get_val(MOVE_X)< -45){
            LX = ( get_val(MOVE_X) - (LS_Sens_Corect) );
        }
        if(get_val(MOVE_Y)> 45){
            LY = ( get_val(MOVE_Y) + (LS_Sens_Corect) );
        }
        if(get_val(MOVE_Y)< -45){
            LY = ( get_val(MOVE_Y) - (LS_Sens_Corect) );
        }//=================================================
        calc_zone (); 
    }
    //----------------------------------------------------------- 
    vm_tctrl(-8); // Run the VM every  2 ms - XBOX X/S                                
    //========================================
    // *** LEFT STICK DRIBBLING ***
    //========================================
        if (get_val(SprintBtn)){
            sensitivity(PS4_LX, NOT_USE, LS_Sprint_Sens);
            sensitivity(PS4_LY, NOT_USE, LS_Sprint_Sens);
        }
        if (!get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)  ){
            sensitivity(PS4_LX, NOT_USE, LS_Sens);
            sensitivity(PS4_LY, NOT_USE, LS_Sens);
        }

//--------------------------------------------------------------
// Start XB1_PL1 Skills
//--------------------------------------------------------------
 if(get_val(MODIFIER)){
			
			if(get_val(XB1_PL1)){
			if(get_val(XB1_RT)) set_val(XB1_RT,91);{
			Directional_Dribbling();
      		sensitivity(PS4_LX,NOT_USE,91);
      		sensitivity(PS4_LY,NOT_USE,91);

		    }
      		}
	//--------------------------------------------------------------
	// Roullette RS START
		if(get_val(XB1_PL1)){
		    if(abs(get_ival(PS4_RX))> 60 || abs(get_ival(PS4_RY))> 60  ){                                       
				calc_zone_RS ();
				check_direction ();
				rs_move = TRUE;
				if(direction_ON) vm_tctrl(0); ACTIVE = FLAIR_ROULETTE_SKILL; combo_run(ROULETTE);
		    }
		}
	// Roullette RS END
	//--------------------------------------------------------------
	if (event_press(XB1_B)){combo_run(DRIVEN_SHOT) } 
	//--------------------------------------------------------------
	
	if (event_press(XB1_X)){combo_run(PlayerStop) } 

                if(event_press(XB1_Y)){
                        right_on = FALSE;             
                        vm_tctrl(0);                   
                        ACTIVE = ELASTICO_SKILL; combo_run(ELASTICO); 
                }                                                        
                if(event_press(XB1_A)){
                        right_on = TRUE;                
                        vm_tctrl(0);                   
                         ACTIVE = REVERSE_ELASTICO_SKILL; combo_run(REVERSE_ELASTICO); 
                }         
                set_val(XB1_A,0); 
                set_val(XB1_Y,0); 
 
		if(abs(get_ival(PS4_RX))< 20 && abs(get_ival(PS4_RY))< 20){
			rs_move = FALSE;
		}
		set_val(TRACE_6,rs_move);
		      if(start){
      		sensitivity(PS4_LX,NOT_USE,91);
      		sensitivity(PS4_LY,NOT_USE,91);
      }
		set_val(SKILL_STICK_X,0);   
		set_val(SKILL_STICK_Y,0);
	}
	
	if( event_release(MODIFIER)){combo_stop(PL_Dribble)}
	
/*	 if(get_val(MODIFIER2)){
    set_val(SprintBtn,91);
    combo_run(L1_Spam_Sprint_boost);  
	}
   
  if( event_release(MODIFIER2)){combo_stop(L1_Spam_Sprint_boost); }
*/		
	//--------------------------------------------------------------

	//--------------------------------------------------------------

	//--------------------------------------------------------------

// End XB1_PL1 Skills
//--------------------------------------------------------------
// PADDLES
	if (get_val(XB1_PL2)) {
			set_val(XB1_LB,100);
			set_val(XB1_RB,100);
			set_val(XB1_Y,100);
		}
		
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_A,100);
		}

//if (get_val(XB1_PL2)){combo_run(OutSide_Box_Shoot) }
if (event_press(XB1_PR1)){combo_run(FAKE_SHOT_CANCEL_cmb) } 
//if (event_press(XB1_PR1)){combo_run(FAKE_PASS_EXIT) } 

    // MIRZA
/*	if(get_val(XB1_PR1)) {
	set_val(XB1_RB,100);
	sensitivity(PS4_LX,NOT_USE,110);
     sensitivity(PS4_LY,NOT_USE,110);}	
	if(event_release(XB1_PR1)){
	combo_run(mirza);
} 
*/
//--------------------------------------------------------------
	if (event_press(XB1_RS)){vm_tctrl(0); combo_run(CHIP_SHOT);   }
   	set_val(XB1_RS,0); 

	//--------------------------------------------------------------
	//  turn ON Penalty  hold  L1 and press OPTIONS
	if(get_val(PS4_L1)){
		if(event_press(PS4_OPTIONS)){
			onoff_penalty = !onoff_penalty;
		}
		set_val(PS4_OPTIONS,0);
    }
    if(onoff_penalty){

		if (get_val(XB1_X))	    {set_val(PS4_LX,0);set_val(PS4_LY,0);}		// Middle 
		if (get_val(PS4_LEFT))	{set_val(PS4_LX,-78);set_val(PS4_LY,-38);} 	// Left Up 
		if (get_val(PS4_RIGHT))	{set_val(PS4_LX,78);set_val(PS4_LY,-38);} 	// Right Up 
		if (get_val(PS4_UP))	{set_val(PS4_LX,-54);set_val(PS4_LY, 90);} 	// Left Down 
		if (get_val(PS4_DOWN))	{set_val(PS4_LX,54);set_val(PS4_LY,90);} 	// Right Down 
    }
	
// CHIP SHOT
	if(get_val(XB1_RB) && event_press(XB1_B)){combo_run(CHIP_SHOT);} 
                                         
    //===============================================
    //    GRROUND PASSES  MIN / MAX
    //===============================================
    if(!get_val(MODIFIER)){
         if(!get_val(PaceCtrol)){    
        if( !get_val(SprintBtn) && !get_val(PlayerRun)){
	        if(get_val(PassBtn)){
	                combo_run(Ground_Pass_MIN_cmb2);                     
	        }
        }
    }	// PaceCtrol
  }  // MODIFIER);
    //===============================================
    //    TROUGH PASSES  MIN / MAX
    //===============================================
    if(!get_val(MODIFIER)){     
    if(!get_val(PaceCtrol)){    
	    if(get_val(ThroughBall)){
	    	trough_pass_timer += get_rtime();
	    }
	    if(event_release(ThroughBall)){
		    	if(trough_pass_timer < Trough_Pass_MIN){
		    		TroughP_difference = Trough_Pass_MIN - trough_pass_timer;
		    		
		    		combo_run(Trough_Pass_MIN_cmb);
		    	}else{
		    		if(DoubleTapTroughPass) combo_run(DOUBLE_TAP_TROUGH_cmb);
		    	}
	    	trough_pass_timer = 0;
	    }
   } // PaceCtrol
  }  // MODIFIER);

	//if (event_press(XB1_PR1)){ combo_run(OutSide_Box_Shoot);   }

    //========================================
    // *** DYNAMIC FINISHING ***
    //========================================
    if(Dynamic_Finish_onoff) f_dynamic_finish (); 


} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function f_dynamic_finish () { 
    if(event_release(CrossBtn)){
        cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1400;
    }
    
    if(cross_timer){
        cross_timer -= get_rtime();
    }
    
    if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && cross_timer <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){
            set_val(ShotBtn,0);
            INSIDE_BOX_AIM();     
            if( after_sprint_timer > 225  ) {
                UltimatePower = random(205,212 ) ; 
                drval=0;
                combo_restart(Dynamic_Shoot); 
            }
            if( after_sprint_timer <= 0  ) {
                UltimatePower = 240 ;
                drval = 100;
                combo_restart(Dynamic_Shoot); 
            }
        }
    } 
    /// FakeShot Support avoid Conflictions
    if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  
        combo_stop(Dynamic_Shoot);
    }
  
}
////////////////////////////////////////////////////////////////
int cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;
int Dynamic_Finish_onoff = TRUE;

combo Dynamic_Shoot {
    set_val(ShotBtn, 100);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(UltimatePower);
    
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM();
    wait(50);
    
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}
function INSIDE_BOX_AIM() { 
    // Moving to UP
    if((get_val(PS4_LX) > -50 && get_val(PS4_LX) <50) && get_val(PS4_LY) < -35 ) // UP
    {  
        LA (0, -96);
    }
    // Moving to DOwn     
    if((get_val(PS4_LX) > -50 && get_val(PS4_LX) <50) && get_val(PS4_LY) > 35 ) // Down
    {  
        LA (0, 96);
    }
    // Moving to Right     
    if((get_val(PS4_LY) > -50 && get_val(PS4_LY) <50) && get_val(PS4_LX) > 35 ) // Right
    { 
        LA (96, 0);
    }
    // Moving to LEFT     
    if((get_val(PS4_LY) > -50 && get_val(PS4_LY) <50) && get_val(PS4_LX) < -35 ) // Left
    {  
        LA (-96, 0);
    }
    // Moving to UP-Right
    if(get_val(PS4_LX) > 50  && get_val(PS4_LY) < -50 ) // UP-Right
    {  
        LA (96, -96);
    }
    // Moving to Down-Right     
    if(get_val(PS4_LX) > 50 && get_val(PS4_LY) > 50 ) // Down-Right
    {   
        LA (96, 96);
    }
    // Moving to UP-Left    
    if(get_val(PS4_LX) < -50 && get_val(PS4_LY) < -50)  // UP-Left
    {   
        LA (-96, -96);
    }
    // Moving to Down-Left     
    if(get_val(PS4_LX) < -50 && get_val(PS4_LY) > 50) // Down-Left
    {   
        LA (-96, 96);
    }    
}


int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo SPRINT_cmb { 
	RA_UP();   
	wait(40);  
	wait(40);  
	RA_UP();   
	wait(40);  
	wait(40);  
}      

int after_cross_timer;  
int Ultimate_Finishing = 1;
combo Inside_Box_Finishing {
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(210);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(SprintBtn,100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600); 
} 

combo DRIVEN_SHOT  {         
   set_val(FinesseShot,100); 
   set_val(PlayerRun,100);   
   set_val(ShotBtn,100);     
   wait(240);
} 

combo OutSide_Box_Shoot {
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(190);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, 100);
    wait(27);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    wait(23);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);            
}       



//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
int ground_pass_timer; 
int Ground_Pass_MIN = 80;
int Ground_Pass_MAX = 250;
int GP_difference;
int DoubletapGroundPass = FALSE;
combo Ground_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(GP_difference);
	set_val(PassBtn,  0);
	wait(30);
	if(DoubletapGroundPass){
		set_val(PassBtn,100);
    }
    wait(60);
}
combo DoubleTapGroundPass_cmb {
    set_val(PassBtn,  0);
    wait(30);
    set_val(PassBtn,100);
    wait(60);    
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
int RB_ground_pass_timer; 
int RB_Ground_Pass_MIN = 80;
int RB_Ground_Pass_MAX = 250;
int RB_GP_difference;
int RB_DoubletapGroundPass = FALSE;
combo RB_Ground_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(RB_GP_difference);
	set_val(PassBtn,  0);
	wait(80);
	set_val(XB1_RB,100);
    wait(80);
}

//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
int trough_pass_timer; 
int Trough_Pass_MIN = 80;
int Trough_Pass_MAX = 300;
int TroughP_difference;
int DoubleTapTroughPass = TRUE;
int trough_start;
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	wait(TroughP_difference);
	if(DoubleTapTroughPass){
		set_val(ThroughBall,100);
	}
	wait(60);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo L1_Spam_Sprint_boost {
sensitivity(PS4_LX, NOT_USE, 110);
sensitivity(PS4_LY, NOT_USE, 110);
set_val(MODIFIER2,0);
set_val(PlayerRun,100):
wait(300);
sensitivity(PS4_LX, NOT_USE, 110);
sensitivity(PS4_LY, NOT_USE, 110);
set_val(MODIFIER2,0);
set_val(PlayerRun,0):
wait(120);
}

combo PL_Dribble {
set_val(PlayerRun,100):
wait(400);
set_val(PlayerRun,0):
wait(600);
}
//+++++++++++++++++Ground_Pass_MIN_cmb+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    //END main//
combo Ground_Pass_MIN_cmb2 {
    set_val(SprintBtn,15);
	set_val(PassBtn,100);
	wait(95); //minimum power if short press.
    wait(400); //freedom to add more pass power
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo mirza {
    wait(100); 
    set_val(XB1_RT, 91);
    wait(300); 
}

combo FAKE_SHOT {        
	set_val(ShotBtn,100);  
	wait(40);              
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	wait(60);             
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo PlayerStop {
    set_val(XB1_LB, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 0);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 0);
    wait(50);
    set_val(XB1_LB, 0);
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FAKE_PASS_EXIT { 
	set_val(SprintBtn,100);
	set_val(ShotBtn,100);  
	wait(40);
	set_val(SprintBtn,100);
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	wait(60);
	set_val(SprintBtn,100);
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo STOP { 
	set_val(XB1_LB,100);  
	set_val(CrossBtn,100);  
	wait(40);
	set_val(XB1_LB,100); 
	set_val(CrossBtn,100);  
	set_val(PassBtn,100); 
	wait(60);
	set_val(XB1_LB,100); 
	set_val(CrossBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FAKE_SHOT_CANCEL_cmb {        
    set_val(ShotBtn,100);  
    wait(40);              
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);             
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    wait(140);
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
    wait(100);
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo STOPSUNNY { 
    set_val(SprintBtn,100);
    set_val(ShotBtn,100);
    wait(40);
    set_val(SprintBtn,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    set_val(SprintBtn,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60); 
	set_val(PS4_LX,0)
	set_val(PS4_LY,0)
    wait(175);
	set_val(PS4_LX,0)
	set_val(PS4_LY,0)
    set_val(XB1_LT,100);
    set_val(XB1_RT,100);
    wait(200);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}   
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo CHIP_SHOT {
    INSIDE_BOX_AIM();
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    set_val(FinesseShot,0);
  wait(187);
  INSIDE_BOX_AIM();
  set_val(ShotBtn,0);
  set_val(FinesseShot,0);
  wait(600);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo BRIDGE_DRIBBLE {
   set_val(FinesseShot,100);
   wait(40);
   set_val(FinesseShot,   0);
   wait(40);
   set_val(FinesseShot,100);
   wait(40);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo OPEN_UP_cmb {      
    set_val(PS4_L3,100); 
    wait(100);           
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++                       
//--- Defense V 4.1
// Credits to Dark.Angle 
//=======================================
int Teammate_Contain_on = FALSE;
int defence_on = TRUE;
define Sprint_Pass        = 0;
define Sprint_ProtectBall = 1;
int want_to_use = Sprint_ProtectBall;
int Second_Button ;
int Pre_jock_mod;
int Opp_Goal_dir;
function f_defence (){

if(abs(get_val(XB1_RX) >12 ) || abs(get_val(XB1_LY) >12)){ // Fix RS Switching , sometimes high - vm causing RS switching to wider distance than usual
vm_tctrl(0);
}

	if(get_val(SprintBtn) && !combo_running(Pre_JoCKEY) && !combo_running(JoCKEY)){ // provides higher sprinting speed .
		sensitivity(XB1_LX, NOT_USE, 110);
        sensitivity(XB1_LY, NOT_USE, 110);
    }
    // User in the start of the match will need to define opponent goal direction
    // in the start of 2nd half should do the same .
    if (get_val(XB1_LT) ){
        if (get_val(XB1_RIGHT)){
            Opp_Goal_dir =  100 ;
        }
        if (get_val(XB1_LEFT)){
            Opp_Goal_dir =  -100 ;
        }
        set_val(XB1_LEFT,0);
        set_val(XB1_RIGHT,0);  
    }
    
    if(want_to_use == Sprint_ProtectBall)Second_Button = PaceCtrol; 
    else  Second_Button = PassBtn;                
    
    if( get_val(SprintBtn) && get_val(Second_Button)){
    vm_tctrl(-4);
            if((get_val(ShotBtn) || get_val(ShotBtn)) && (combo_running(Sprint_Spam2) || combo_running(Sprint_Spam1) ) ){
            combo_stop(Sprint_Spam1);
            combo_stop(Sprint_Spam2);
            combo_run(Tackle);
            }
            
        if(!Pre_jock_mod && ( abs(get_val(XB1_LX))>20  || abs(get_val(XB1_LY))>20  )) { // first 500 ms pre Jockey , no sprint only jockey +  90 sens = (Player Solid Shape) .
            if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(XB1_X,100);// auto team mate contain
            directional_jockey();
            combo_run(Pre_JoCKEY);        
            combo_run(Sprint_Spam1);
        }
    
         if(Pre_jock_mod && ( abs(get_val(XB1_LX))>14  || abs(get_val(XB1_LY))>14  )){ // after 500 ms  Jockey + Sprint in loop .
            if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
            combo_run(Sprint_Spam2);
            combo_run(JoCKEY);
        }
            
        if( ( abs(get_val(XB1_LX))<12  && abs(get_val(XB1_LY))<12  )){ // when NO LS directions AI Switching and pressing AUTO.
            if(Second_Button == PassBtn) set_val(PassBtn,0);
            if(Second_Button == PaceCtrol) set_val(PaceCtrol,0);
            set_val(FinesseShot,0)
            combo_run(Auto_Switch);            
        }else{
            combo_stop(Auto_Switch);
        }
     }
    
    if( ( event_release(SprintBtn) || event_release(PaceCtrol) || event_release(PassBtn) ) || get_val(ShotBtn) || get_val(CrossBtn) ){
        Pre_jock_mod = FALSE;combo_stop(JoCKEY); vm_tctrl(0); combo_stop(Auto_Switch); combo_stop(Pre_JoCKEY);combo_stop(Sprint_Spam1);combo_stop(Sprint_Spam2)
    }
  
 
}

function directional_jockey() { 
    // Moving to UP
    if((get_val(XB1_LX) > -50 && get_val(XB1_LX) <50) && get_val(XB1_LY) < -35 ) // UP
    {  
        LA (0, -96);
    }
    // Moving to DOwn     
    if((get_val(XB1_LX) > -50 && get_val(XB1_LX) <50) && get_val(XB1_LY) > 35 ) // Down
    {  
        LA (0, 96);
    }
    // Moving to Right     
    if((get_val(XB1_LY) > -50 && get_val(XB1_LY) <50) && get_val(XB1_LX) > 35 ) // Right
    { 
        LA (96, 0);
    }
    // Moving to LEFT     
    if((get_val(XB1_LY) > -50 && get_val(XB1_LY) <50) && get_val(XB1_LX) < -35 ) // Left
    {  
        LA (-96, 0);
    }
    // Moving to UP-Right
    if(get_val(XB1_LX) > 50  && get_val(XB1_LY) < -50 ) // UP-Right
    {  
        LA (96, -96);
    }
    // Moving to Down-Right     
    if(get_val(XB1_LX) > 50 && get_val(XB1_LY) > 50 ) // Down-Right
    {   
        LA (96, 96);
    }
    // Moving to UP-Left    
    if(get_val(XB1_LX) < -50 && get_val(XB1_LY) < -50)  // UP-Left
    {   
        LA (-96, -96);
    }
    // Moving to Down-Left     
    if(get_val(XB1_LX) < -50 && get_val(XB1_LY) > 50) // Down-Left
    {   
        LA (-96, 96);
    }    
}

combo Auto_Switch {
    set_val(XB1_X,100);
    wait(600);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(XB1_X,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,0);
    set_val(XB1_X,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(XB1_X,100);
    combo_stop(JoCKEY);
    wait(10);
    set_val(XB1_RX,Opp_Goal_dir);
    set_val(XB1_X,0);
    combo_stop(JoCKEY);
    wait(10);
}

	
	function AUTO_EXIT_SK() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(XB1_LX) > 10 && get_val(XB1_LY) < -10 ) // TOP Right Corner
	{   right_on = TRUE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(XB1_LX) > 15 && get_val(XB1_LY) > 15) // Bottom Right Corner
	{   right_on = FALSE
	
		 
	}
	
	
	if(get_val(XB1_LX) < -15 && get_val(XB1_LY) < -15) // TOP Left Corner
	{   right_on = FALSE 
		 

	}
	
	if(get_val(XB1_LX) < -15 && get_val(XB1_LY) > 15) // Bottom Left Corner
	{   right_on = TRUE

		  
		 
	}
	}

combo Sprint_Spam1 {
    set_val(SprintBtn,100);
    wait(80);
    set_val(SprintBtn,0);
    wait(120);   
}

combo Sprint_Spam2 {
    set_val(SprintBtn,100);
    wait(80);
    set_val(SprintBtn,0);
    wait(220);   
}

combo Tackle {
    set_val(SprintBtn,100);
    set_val(PaceCtrol,100);
    wait(60);  
}

combo Pre_JoCKEY {
    directional_jockey()
    set_val(PaceCtrol,100);
    set_val(XB1_LS,100);
    wait(300); 
    Pre_jock_mod = TRUE
}

combo JoCKEY {
  sensitivity(XB1_LX, NOT_USE, 96);
  sensitivity(XB1_LY, NOT_USE, 96);
  set_val(XB1_LS,100);
  set_val(PaceCtrol,100);
  wait(1280);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo TT_PASS {
set_val(XB1_A, 0);
wait(20);
set_val(XB1_A, 100);
wait(170);
set_val(XB1_A,0);
wait(100);  // 100 ms
set_val(XB1_RB,100);
wait(100);}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
int LS_Sens        = 96;
int LS_Sprint_Sens = 110;

data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(PS4_LX) >= 50) move_lx = 100;
    else if(get_val(PS4_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(PS4_LY) >= 50) move_ly = 100;
    else if(get_val( PS4_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
int RS_x, RS_y ;
int zone_RS;
function calc_zone_RS( ){

    if(get_val(PS4_RX) >= 50) RS_x = 100;
    else if(get_val(PS4_RX) <= -50) RS_x = -100;
    else RS_x = 0;
    if(get_val(PS4_RY) >= 50) RS_y = 100;
    else if(get_val( PS4_RY) <= -50) RS_y = -100;
    else RS_y = 0;
    
    
    if(RS_x != 0 || RS_y != 0) {
        zone_RS = 0; while(zone_RS < 8) {
            if(dchar(zone_RS) == RS_x && dchar(8 + zone_RS) == RS_y) {
                break;
            } zone_RS += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//============================================================================================


function Directional_Dribbling() { 
    // Moving to UP
    if((get_val(PS4_LX) > -15 && get_val(PS4_LX) <15) && get_val(PS4_LY) < -50 ) // UP
    {   combo_run(PL_Dribble);
        LA (0, -100);
    }
    // Moving to DOwn     
    if((get_val(PS4_LX) > -15 && get_val(PS4_LX) <15) && get_val(PS4_LY) > 50 ) // Down
    {   combo_run(PL_Dribble);
        LA (0, 100);
    }
    // Moving to Right     
    if((get_val(PS4_LY) > -15 && get_val(PS4_LY) <15) && get_val(PS4_LX) > 50 ) // Right
    {   combo_run(PL_Dribble);
        LA (100, 0);
    }
    // Moving to LEFT     
    if((get_val(PS4_LY) > -15 && get_val(PS4_LY) <15) && get_val(PS4_LX) < -50 ) // Left
    {   combo_run(PL_Dribble);
        LA (-100, 0);
    }
    // Moving to UP-Right
    if(get_val(PS4_LX) > 85  && get_val(PS4_LY) < -85 ) // UP-Right
    {   combo_run(PL_Dribble);
        LA (100, -100);
    }
    // Moving to Down-Right     
    if(get_val(PS4_LX) > 85 && get_val(PS4_LY) > 85 ) // Down-Right
    {   combo_run(PL_Dribble);
        LA (100, 100);
    }
    // Moving to UP-Left    
    if(get_val(PS4_LX) < -85 && get_val(PS4_LY) < -85)  // UP-Left
    {   combo_run(PL_Dribble);
        LA (-100, -100);
    }
    // Moving to Down-Left     
    if(get_val(PS4_LX) < -85 && get_val(PS4_LY) > 85) // Down-Left
    {   combo_run(PL_Dribble);
        LA (-100, 100);
    }    
}



combo Sprint_Spam {
    set_val(SprintBtn,100);
    wait(80);
    set_val(SprintBtn,0);
    wait(120);
    set_val(SprintBtn,100);
    wait(120);
    set_val(SprintBtn,0);
    wait(700);

}


int temp_zone;
int direction_ON;
//============================================================================================
function check_direction (){
	
	direction_ON = FALSE;
	
	if(zone_p == 0 ){
	
		if(zone_RS > 0 && zone_RS < 4){ right_on =  TRUE; direction_ON = TRUE;}  
		if(zone_RS > 4 && zone_RS < 8){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 1 ){
	
		if(zone_RS > 1 && zone_RS < 5){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 5 && zone_RS < 8 || zone_RS == 0 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 2 ){
	
		if(zone_RS > 2 && zone_RS < 6){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS == 7 || zone_RS == 0 || zone_RS == 1 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 3 ){
	
		if(zone_RS > 3 && zone_RS < 7){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS >=0 && zone_RS < 3 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 4 ){
	
		if(zone_RS > 0 && zone_RS < 4){right_on = FALSE; direction_ON = TRUE; }  
		if(zone_RS > 4 && zone_RS < 8 ){right_on = TRUE; direction_ON = TRUE; }
	}
	
	else if(zone_p == 5 ){
	
		if(zone_RS > 5 && zone_RS < 8  || zone_RS == 0){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 1 && zone_RS < 5 || zone_RS == 0){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 6 ){
	
		if(zone_RS == 7 || zone_RS == 0 || zone_RS == 1){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 2 && zone_RS < 6){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	
	else if(zone_p == 7 ){
	
		if(zone_RS >= 0 && zone_RS < 3){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS >  3 && zone_RS < 7){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	
	set_val(TRACE_1,right_on);
	
	set_val(TRACE_2,zone_p);
	
	set_val(TRACE_3,zone_RS);
	
	
}
//============================================================================================
function RA (XX,YY){
	set_val(PS4_RX,XX);
	set_val(PS4_RY,YY);
}
//============================================================================================
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}
//============================================================================================

//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             