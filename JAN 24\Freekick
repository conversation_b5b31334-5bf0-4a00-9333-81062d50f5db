
main {
if (DA_FC370) {
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_SHARE)) {
			DA_FC609 = !DA_FC609;
			DA_FC243(DA_FC609);
										}
		set_val(PS5_SHARE, 0);
								}
						}
if (DA_FC609 && DA_FC370) {
	vm_tctrl(0);
	combo_stop(DA_FC86);
	if (get_ival(XB1_RS)) {
		if (event_press(PS5_UP)) {
			DA_FC424 += 10;
			DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
										}
		if (event_press(PS5_DOWN)) {
			DA_FC424 -= 10;
			DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
										}
		set_val(PS5_UP, 0);
		set_val(PS5_DOWN, 0);
								}
	DA_FC236(DA_FC1052);
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_RIGHT)) {
			DA_FC616 = FALSE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		if (event_press(PS5_LEFT)) {
			DA_FC616 = TRUE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		set_val(PS5_L1,0);
								}
						}
}

combo DA_FC86 {
vm_tctrl(0);
wait(750);
set_val(DA_FC449,100);
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);
if(DA_FC1123 == 1 ){
set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)}
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);

	}
	
combo DA_FC87 {
	vm_tctrl(0);
	wait( 800);
	DA_FC1147 = 0;
	}