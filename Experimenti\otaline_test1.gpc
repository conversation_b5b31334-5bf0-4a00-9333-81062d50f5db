// Octagonal boundary for analog stick
// Creates 8-directional movement with precise angles

// Constants for stick identification
define stick = POLAR_LS;  // Left stick in polar mode
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Constants for octagon shape
define MAX_VAL = 100;    // Maximum stick value
define RATIO = 75;       // Ratio for flat sides (percentage)

int x, y;
int abs_x, abs_y;
int max_val;
int scale;

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Calculate octagonal boundary
        // For a regular octagon:
        // At corners (45°): radius = MAX_VAL
        // At sides: radius = MAX_VAL * cos(22.5°) ≈ MAX_VAL * 0.92
        
        // The scale factor is MAX_VAL at corners and RATIO at sides
        if(abs_x > abs_y) {
            // We're closer to horizontal
            scale = RATIO + ((MAX_VAL - RATIO) * abs_y / abs_x);
        } else {
            // We're closer to vertical
            scale = RATIO + ((MAX_VAL - RATIO) * abs_x / abs_y);
        }
        
        // Apply scaling while maintaining angle
        max_val = (MAX_VAL * scale) / 100;
        
        // Scale both components equally to maintain direction
        x = (x * max_val) / MAX_VAL;
        y = (y * max_val) / MAX_VAL;
        
        // Set the modified values
        set_val(stickX, x);
        set_val(stickY, y);
    }
}