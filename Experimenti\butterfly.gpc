// Octagonal boundary for analog stick
// Creates 8-directional movement with precise angles

// Constants for stick identification
define stick = POLAR_LS;  // Left stick in polar mode
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Constants for octagon shape
define MAX_VAL = 100;    // Maximum stick value
define RATIO = 70;       // Ratio for flat sides (percentage)
define BLEND = 20;       // Blend range for transitions (percentage)

int x, y;
int abs_x, abs_y;
int max_val;
int blend_factor;

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Calculate blend factor based on how close we are to diagonal
        // When abs_x == abs_y, we're at diagonal (45 degrees)
        if(abs_x > abs_y) {
            blend_factor = (abs_y * 100) / abs_x;
        } else {
            blend_factor = (abs_x * 100) / abs_y;
        }
        
        // Sharp transition between sides and corners
        if(blend_factor > 100 - BLEND) {
            // Near diagonal (corner)
            max_val = MAX_VAL;
        } else if(blend_factor < BLEND) {
            // Near cardinal (side)
            max_val = (MAX_VAL * RATIO) / 100;
        } else {
            // Transition area - sharp blend
            max_val = (MAX_VAL * RATIO) / 100 + 
                     ((MAX_VAL - (MAX_VAL * RATIO) / 100) * 
                      (blend_factor - BLEND) / (100 - 2 * BLEND));
        }
        
        // Apply scaling while maintaining direction
        x = (x * max_val) / MAX_VAL;
        y = (y * max_val) / MAX_VAL;
        
        // Set the modified values
        set_val(stickX, x);
        set_val(stickY, y);
    }
}