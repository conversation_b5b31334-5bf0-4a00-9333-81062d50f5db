int rx, ry;
int mag_sq, mag, new_rx, new_ry;
int sqrt_num, sqrt_result, sqrt_bit; // For square root calculation

main {
    // Get raw stick values from Xbox One controller
    rx = get_ival(XB1_RX);
    ry = get_ival(XB1_RY);
    
    // Calculate squared magnitude
    mag_sq = rx * rx + ry * ry;
    
    // Threshold check (1700 radius)
    if (mag_sq >= 1700) {
        // Integer square root implementation
        sqrt_num = mag_sq;
        sqrt_result = 0;
        sqrt_bit = 1 << 30;
        
        // Find starting bit
        while (sqrt_bit > sqrt_num)
            sqrt_bit >>= 2;
        
        // Calculate square root
        while (sqrt_bit != 0) {
            if (sqrt_num >= sqrt_result + sqrt_bit) {
                sqrt_num -= sqrt_result + sqrt_bit;
                sqrt_result = (sqrt_result >> 1) + sqrt_bit;
            } else {
                sqrt_result >>= 1;
            }
            sqrt_bit >>= 2;
        }
        mag = sqrt_result;

        // Scale coordinates while maintaining direction
        new_rx = (rx * 100) / mag;
        new_ry = (ry * 100) / mag;
        
        // Apply to Xbox One sticks
        set_val(XB1_RX, new_rx);
        set_val(XB1_RY, new_ry);
    }
}