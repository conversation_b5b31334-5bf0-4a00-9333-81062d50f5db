//int lastPressTime; // Time of the last press
int toggle_mode = 0; // Global variable for toggling mode

init { 
  //lastPressTime = 0; 
  toggle_mode = 0;
}

main { 
  // On button press: Toggle mode
  if(event_press(XB1_LS)) { 
    toggle_mode = 1 - toggle_mode;
  }

  // When XB1_LS is held down, activate triggers based on toggle mode.
  if(get_val(XB1_LS)) { 
    if(toggle_mode == 0) { 
		set_val(XB1_LT, 100); 
		set_val(XB1_RT, 100);
		star_x = get_val(XB1_LX);
		star_y = get_val(XB1_LY);
		star_map_convex_octagon(star_x, star_y);
		set_val(XB1_LS, 0);
		set_val(XB1_LX, star_scaled_x);
		set_val(XB1_LY, star_scaled_y);
    } else { 
		star_x = get_val(XB1_LX);
		star_y = get_val(XB1_LY);
		star_map_convex_octagon(star_x, star_y);
		set_val(XB1_LS, 0);
		set_val(XB1_LX, star_scaled_x);
		set_val(XB1_LY, star_scaled_y);
    } 
  } 
 
}