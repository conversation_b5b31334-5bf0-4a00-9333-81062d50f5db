// Skriptname: DualButton_LeftStick_Priority
// Beschreibung:
// Dieses Skript steuert die X-Achse des linken Sticks (PS4_LX oder XB1_LX)
// basierend auf der gleichzeitigen Betätigung zweier Tasten (XB1_PL1 und XB1_PL2).
// Wenn beide Tasten gedrückt werden, entscheidet die Haltedauer, welche Richtung
// der linke Stick einnimmt. Wenn nur eine Taste gedrückt wird, wird der Stick entsprechend
// nach links oder rechts gesetzt.

int a = 0; // Variable zur Verfolgung der Haltedauer von XB1_PL1
int d = 0; // Variable zur Verfolgung der Haltedauer von XB1_PL2

main {
    // Überprüfen, ob die Taste XB1_PL1 gedrückt gehalten wird
    if (get_ival(XB1_PL1)) {
        a += get_rtime(); // Inkrementiere 'a' um die seit dem letzten Frame vergangene Zeit
    }
    else {
        a = 0; // Setze 'a' zurück, wenn die Taste nicht gedrückt wird
    }

    // Überprüfen, ob die Taste XB1_PL2 gedrückt gehalten wird
    if (get_ival(XB1_PL2)) {
        d += get_rtime(); // Inkrementiere 'd' um die seit dem letzten Frame vergangene Zeit
    }
    else {
        d = 0; // Setze 'd' zurück, wenn die Taste nicht gedrückt wird
    }

    // Wenn beide Tasten gleichzeitig gedrückt werden
    if(a > 0 && d > 0) {
        if(a < d) {
            // Wenn XB1_PL1 kürzer gedrückt wurde als XB1_PL2, setze den linken Stick nach links
            set_val(XB1_LX, -100);
        }
        else {
            // Wenn XB1_PL2 kürzer gedrückt wurde als XB1_PL1, setze den linken Stick nach rechts
            set_val(XB1_LX, 100);
        }
    } 
    else {
        // Wenn nur eine der beiden Tasten gedrückt wird
        if (get_ival(XB1_PL1)) {
            // Wenn nur XB1_PL1 gedrückt wird, setze den linken Stick nach links
            set_val(XB1_LX, -100);
        }
        if (get_ival(XB1_PL2)) {
            // Wenn nur XB1_PL2 gedrückt wird, setze den linken Stick nach rechts
            set_val(XB1_LX, 100);
        }
        // Wenn keine der beiden Tasten gedrückt wird, setze den linken Stick in die Neutralposition
        if (!get_ival(XB1_PL1) && !get_ival(XB1_PL2)) {
            set_val(XB1_LX, 0);
        }
    }
}
 