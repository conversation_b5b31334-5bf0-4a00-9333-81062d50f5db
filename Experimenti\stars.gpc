// Constants
define star_MAX_INPUT = 100;
define star_DEADZONE = 5;
define star_ARRAY_SIZE = 23;

// Variables
int star_octagonMax[star_ARRAY_SIZE];  // Array declaration
int star_x, star_y, star_mag_sq, star_mag;
int star_angle, star_index, star_extension;
int star_scaled_x, star_scaled_y;
int star_sign, star_abs_val, star_output;
int star_i;  // Loop counter

// Variables for intSqrt
int star_iSqrtValue;
int star_iSqrtRes;
int star_iSqrtBit;
int star_iSqrtTemp;

////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
    star_iSqrtValue = value;
    star_iSqrtRes   = 0;
    star_iSqrtBit   = 1 << 14; // 2^14 = 16384 (enough for up to ~20000)

    // Shift down until bit <= value
    while(star_iSqrtBit > star_iSqrtValue) {
        star_iSqrtBit = star_iSqrtBit >> 2;
    }

    while(star_iSqrtBit != 0) {
        star_iSqrtTemp = star_iSqrtRes + star_iSqrtBit;
        if(star_iSqrtValue >= star_iSqrtTemp) {
            star_iSqrtValue = star_iSqrtValue - star_iSqrtTemp;
            star_iSqrtRes   = star_iSqrtRes + (star_iSqrtBit << 1);
        }
        star_iSqrtRes = star_iSqrtRes >> 1;
        star_iSqrtBit = star_iSqrtBit >> 2;
    }
    return star_iSqrtRes;
}

// Initialize array values in main
function init_octagon_values() {
    star_octagonMax[0] = 100;   // 0°
    star_octagonMax[1] = 99;    // 2°
    star_octagonMax[2] = 98;    // 4°
    star_octagonMax[3] = 96;    // 6°
    star_octagonMax[4] = 94;    // 8°
    star_octagonMax[5] = 91;    // 10°
    star_octagonMax[6] = 88;    // 12°
    star_octagonMax[7] = 85;    // 14°
    star_octagonMax[8] = 82;    // 16°
    star_octagonMax[9] = 79;    // 18°
    star_octagonMax[10] = 76;   // 20°
    star_octagonMax[11] = 74;   // 22°
    star_octagonMax[12] = 72;   // 24°
    star_octagonMax[13] = 70;   // 26°
    star_octagonMax[14] = 69;   // 28°
    star_octagonMax[15] = 68;   // 30°
    star_octagonMax[16] = 67;   // 32°
    star_octagonMax[17] = 66;   // 34°
    star_octagonMax[18] = 66;   // 36°
    star_octagonMax[19] = 65;   // 38°
    star_octagonMax[20] = 65;   // 40°
    star_octagonMax[21] = 65;   // 42°
    star_octagonMax[22] = 65;   // 44°
}

function apply_deadzone(int val) {
    if(val >= 0) {
        star_sign = 1;
    } else {
        star_sign = -1;
    }
    star_abs_val = abs(val);
    
    if(star_abs_val <= star_DEADZONE)
        return 0;
    
    star_output = ((star_abs_val - star_DEADZONE) * star_MAX_INPUT) / (star_MAX_INPUT - star_DEADZONE);
    if(star_output > star_MAX_INPUT)
        star_output = star_MAX_INPUT;
        
    return star_sign * star_output;
}

function map_convex_octagon(int x, int y) {
    // Apply deadzone first
    star_scaled_x = apply_deadzone(x);
    star_scaled_y = apply_deadzone(y);
    
    // Calculate magnitude
    star_mag_sq = star_scaled_x * star_scaled_x + star_scaled_y * star_scaled_y;
    if(star_mag_sq == 0)
        return;
        
    star_mag = intSqrt(star_mag_sq);
    
    // Get current angle (0-360)
    star_angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Get index into extension table (normalize to 0-45 degrees)
    star_index = star_angle % 45;
    if(star_index > 22) {
        star_index = 45 - star_index;
    }
    
    // Apply convex octagonal boundary
    star_extension = star_octagonMax[star_index];
    star_scaled_x = (star_scaled_x * star_extension) / 100;
    star_scaled_y = (star_scaled_y * star_extension) / 100;
}

init {
    // Initialize the octagon values
    init_octagon_values();
}

main {
    // Get stick values
    star_x = get_val(XB1_LX);
    star_y = get_val(XB1_LY);
    
    // Apply convex octagonal mapping
    map_convex_octagon(star_x, star_y);
    
    // Set final values
    set_val(XB1_LX, star_scaled_x);
    set_val(XB1_LY, star_scaled_y);
}