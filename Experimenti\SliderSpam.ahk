#SingleInstance Force
#Requires AutoHotkey v2.0

; Initialize variables
activeTimer := ""  ; Keeps track of which timer is currently running
activeInterval := 0  ; Keeps track of current interval

; Function to stop all timers
StopAllTimers() {
    SetTimer SpamQ, 0
    activeTimer := ""
    activeInterval := 0
    ToolTip("Q spam stopped")
    SetTimer () => ToolTip(), -2000  ; <PERSON><PERSON><PERSON> stays for 2 seconds
}

; Function to start a timer
StartTimer(key, interval) {
    global activeTimer, activeInterval
    
    ; If this timer is already running, stop it
    if (activeTimer = key) {
        StopAllTimers()
        return
    }
    
    ; If a different timer is running, stop it first
    if (activeTimer != "") {
        StopAllTimers()
    }
    
    ; Start the new timer
    activeTimer := key
    activeInterval := interval
    SetTimer SpamQ, interval
    ToolTip("Q spam started - " interval/1000 " second interval")
    SetTimer () => ToolTip(), -2000  ; Tooltip stays for 2 seconds
}

; Timer function
SpamQ() {
    Send "{q}"
}

; Single key hotkeys (without Ctrl modifier)
$1::{
    if (activeTimer = "1") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("1", 1000)  ; 1 second interval
    }
}

$2::{
    if (activeTimer = "2") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("2", 2500)  ; 2.5 second interval
    }
}

$3::{
    if (activeTimer = "3") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("3", 5000)  ; 5 second interval
    }
}

; Press 4 to spam Q every 100ms
$4::{
    if (activeTimer = "4") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("4", 100)  ; 100ms interval
    }
}

; Press 0 to stop any active Q spamming
$0::{
    StopAllTimers()
}

; Escape key to exit the script
$Esc::{
    ExitApp()
}

; Block all other keys from triggering the script
#HotIf
SetKeyDelay(0, 0)  ; Ensure no key delay to prevent visibility of key presses
