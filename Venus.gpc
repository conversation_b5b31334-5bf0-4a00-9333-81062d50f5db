// Venus.gpc - <PERSON>ton Combo Script
// This script implements two automatic button combos:
// 1. After XB1_LB is released, automatically presses XB1_LT + XB1_RT for 1 second
// 2. After XB1_LT + XB1_RT are released, automatically presses XB1_LB for 1 second

// Constants
define COMBO_DURATION = 50 // Duration in frames (100 frames = 1 second at 100Hz)

// Global variables
int lb_released = 0;        // Flag to track if XB1_LB was just released
int combo_timer = 0;        // Timer for combo duration
int lt_rt_released = 0;     // Flag to track if XB1_LT + XB1_RT were just released
int lb_combo_timer = 0;     // Timer for XB1_LB combo duration
int combo_active_wl = 0;       // Flag to track if any combo is currently active
int lt_rt_pressed = 0;      // Flag to track if both triggers are currently pressed

// Combo 1: After XB1_LB release, press XB1_LT + XB1_RT for 1 second
function combo_lb_to_lt_rt() {
    // Check if XB1_LB was just released
    if (event_release(XB1_LB)) {
        lb_released = 1;
        combo_timer = COMBO_DURATION;
        combo_active_wl = 1;
    }

    // If combo is active, press XB1_LT + XB1_RT
    if (lb_released && combo_timer > 0) {
        set_val(XB1_LT, 100);
        set_val(XB1_RT, 100);
        combo_timer--;

        // When timer expires, end the combo
        if (combo_timer == 0) {
            lb_released = 0;
            combo_active_wl = 0;
        }
    }
}

// Combo 2: After XB1_LT + XB1_RT release, press XB1_LB for 1 second
function combo_lt_rt_to_lb() {
    // Track when both triggers are pressed
    if (get_val(XB1_LT) > 50 && get_val(XB1_RT) > 50) {
        lt_rt_pressed = 1;
    }

    // Check if both triggers were pressed and now both are released
    if (lt_rt_pressed && get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0 && !lb_released && !combo_active_wl) {
        lt_rt_pressed = 0;
        lt_rt_released = 1;
        lb_combo_timer = COMBO_DURATION;
        combo_active_wl = 1;
    }

    // If combo is active, press XB1_LB
    if (lt_rt_released && lb_combo_timer > 0) {
        set_val(XB1_LB, 100);
        lb_combo_timer--;

        // When timer expires, end the combo
        if (lb_combo_timer == 0) {
            lt_rt_released = 0;
            combo_active_wl = 0;
        }
    }
}

init {
    // Initialize variables
    combo_timer = 0;
    lb_combo_timer = 0;
    lb_released = 0;
    lt_rt_released = 0;
    combo_active_wl = 0;
}

main {
    // Run the combo functions
    combo_lb_to_lt_rt();
    combo_lt_rt_to_lb();

    // Pass through all other inputs when no combo is active
    if (!combo_active_wl) {
        // Pass through all controller inputs except those used in combos when active
        if (!lb_released) {
            set_val(XB1_LB, get_val(XB1_LB));
        }
        if (!lt_rt_released) {
            set_val(XB1_LT, get_val(XB1_LT));
            set_val(XB1_RT, get_val(XB1_RT));
        }
    }
}
