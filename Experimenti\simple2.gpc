int invertActive; // Track if inversion is active
// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;
int virtmach = -9;


int leftStickRadius;
int rightStickRadius;
int targetRadius;
int baseRadius = 28701;
int randomVariation;
int fullRadius = 32767;

// Variables for random radius control
int random_update_timer;
int current_random_radius;
define REDUCED_RADIUS = 32767;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;
int radius;
int current_angle;
int max_allowed_radius;

// Function to quantize angle into discrete zones
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

// Calculate radius using Pythagorean theorem
function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2));
}

main {


    // Update the random timer
    if(random_update_timer) {
        random_update_timer -= get_rtime();
    }

    vm_tctrl(virtmach)
    // When RB is pressed
    if(get_val(XB1_LS)) {
        // Get current left stick angle and invert it
        set_polar(POLAR_LS, -180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30000);
        invertActive = TRUE;
    } else {
        // When RB is released, stop modifying the stick
        if(invertActive) {
          set_polar(POLAR_LS, 0, 0);
            set_val(XB1_RB, 0);
            invertActive = FALSE;
        }
    }

    // Reset shared values at the start of each loop iteration
    rb_val = 0;
    lb_val = 0;

    // Only handle X and Y and A buttons if triggers are not pressed
    if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
        handle_rb_related_button(XB1_X);
        handle_rb_related_button(XB1_Y);
        handle_lb_related_button(XB1_A);
    }

    // **Directly handle XB1_RB press**
    if(get_ival(XB1_RB)) {
        rb_val = 100; // Activate XB1_RB directly
    }
    // **Directly handle XB1_LB press**
    if(get_ival(XB1_LB)) {
        lb_val = 100; // Activate XB1_LB directly
    }
    
    // Set shared buttons based on aggregated values
    set_val(XB1_RB, rb_val);
    set_val(XB1_LB, lb_val);
    


    pass();

// Finesse LB+B
if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
}

// Finesse with R3
if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
}

// Shots with B
if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);
		}
}
	
// Shots with RB+B
if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);
		}
}


}
  
function pass() {
    // Store the current analog stick radius
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
    // Default to reduced radius
    max_allowed_radius = REDUCED_RADIUS;
    

    
    if (radius < 100) {
        set_polar(POLAR_LS, 0, 0);
    } else {
        // Get the quantized angle
        current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2);
        
        // Handle random radius with delay
        if(!random_update_timer) {
            // Generate random variation (3% to 7% reduction)
            randomVariation = random(900, 5100); // Adjust these values as needed
            current_random_radius = max_allowed_radius - randomVariation;
            random_update_timer = 200; // Updates every 200ms
        }
        
        // Set the polar coordinates with quantized angle and current random radius
        set_polar(POLAR_LS, current_angle, current_random_radius);
    }
}

function handle_rb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            rb_val = 100; // Activate XB1_RB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

function handle_lb_related_button(int button) {
    if(get_ival(button)) {
        ptime = get_ptime(button);
        if(ptime < 250) {
            set_val(button, 100);
        }
        else if(ptime >= 250 && ptime <= 380) {
            set_val(button, 100);
            lb_val = 100; // Activate XB1_LB
        }
        else {
            set_val(button, 0);
        }
    }
    else {
        set_val(button, 0);
    }
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;

combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

int UltimatePower;

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function getPolar(Stick, AngleOrRadius) {
  if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
  return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));   
}
