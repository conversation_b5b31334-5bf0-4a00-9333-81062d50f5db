/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/
















































































int DA20196[0]; init { 	DA20113(); 	combo_run(DA201); 	combo_run(DA202); 	combo_run(DA203); 	combo_run(DA204); 	combo_run(DA205); 	combo_run(DA206); 	combo_run(DA207); 	combo_run(DA208); 	combo_run(DA209); 	combo_run(DA2010); 	combo_run(DA2011); 	combo_run(DA2012); 	combo_run(DA2013); 	combo_run(DA2014); 	combo_run(DA2015); 	combo_run(DA2016); 	combo_run(DA2017); 	combo_run(DA2018); 	combo_run(DA2019); 	combo_run(DA2020); 	combo_run(DA2021); 	combo_run(DA2022); 	combo_run(DA2023); 	combo_run(DA2024); 	combo_run(DA2025); 	combo_run(DA2026); 	combo_run(DA2027); 	combo_run(DA2028); 	combo_run(DA2029); 	combo_run(DA2030); 	combo_run(DA2031); 	combo_run(DA2032); 	combo_run(DA2033); 	combo_run(DA2034); 	combo_run(DA2035); 	combo_run(DA2036); 	combo_run(DA2037); 	combo_run(DA2038); 	combo_run(DA2039); 	combo_run(DA2040); 	combo_run(DA2041); 	combo_run(DA2042); 	combo_run(DA2043); 	combo_run(DA2044); 	combo_run(DA2045); 	combo_run(DA2046); 	combo_run(DA2047); 	combo_run(DA2048); 	combo_run(DA2049); 	combo_run(DA2050); 	combo_run(DA2051); 	combo_run(DA2052); 	combo_run(DA2053); 	combo_run(DA2054); 	combo_run(DA2055); 	combo_run(DA2056); 	combo_run(DA2057); 	combo_run(DA2058); 	combo_run(DA2059); 	combo_run(DA2060); 	combo_run(DA2061); 	combo_run(DA2062); 	combo_run(DA2063); 	combo_run(DA2064); 	combo_run(DA2065); 	combo_run(DA2066); 	combo_run(DA2067); 	combo_run(DA2068); 	combo_run(DA2069); 	combo_run(DA2070); 	combo_stop(DA201); 	combo_stop(DA202); 	combo_stop(DA203); 	combo_stop(DA204); 	combo_stop(DA205); 	combo_stop(DA206); 	combo_stop(DA207); 	combo_stop(DA208); 	combo_stop(DA209); 	combo_stop(DA2010); 	combo_stop(DA2011); 	combo_stop(DA2012); 	combo_stop(DA2013); 	combo_stop(DA2014); 	combo_stop(DA2015); 	combo_stop(DA2016); 	combo_stop(DA2017); 	combo_stop(DA2018); 	combo_stop(DA2019); 	combo_stop(DA2020); 	combo_stop(DA2021); 	combo_stop(DA2022); 	combo_stop(DA2023); 	combo_stop(DA2024); 	combo_stop(DA2025); 	combo_stop(DA2026); 	combo_stop(DA2027); 	combo_stop(DA2028); 	combo_stop(DA2029); 	combo_stop(DA2030); 	combo_stop(DA2031); 	combo_stop(DA2032); 	combo_stop(DA2033); 	combo_stop(DA2034); 	combo_stop(DA2035); 	combo_stop(DA2036); 	combo_stop(DA2037); 	combo_stop(DA2038); 	combo_stop(DA2039); 	combo_stop(DA2040); 	combo_stop(DA2041); 	combo_stop(DA2042); 	combo_stop(DA2043); 	combo_stop(DA2044); 	combo_stop(DA2045); 	combo_stop(DA2046); 	combo_stop(DA2047); 	combo_stop(DA2048); 	combo_stop(DA2049); 	combo_stop(DA2050); 	combo_stop(DA2051); 	combo_stop(DA2052); 	combo_stop(DA2053); 	combo_stop(DA2054); 	combo_stop(DA2055); 	combo_stop(DA2056); 	combo_stop(DA2057); 	combo_stop(DA2058); 	combo_stop(DA2059); 	combo_stop(DA2060); 	combo_stop(DA2061); 	combo_stop(DA2062); 	combo_stop(DA2063); 	combo_stop(DA2064); 	combo_stop(DA2065); 	combo_stop(DA2066); 	combo_stop(DA2067); 	combo_stop(DA2068); 	combo_stop(DA2069); 	combo_stop(DA2070); 	combo_run(DA20106); } int DA20255 ; int DA20256; int DA20257; int DA20258; int DA20259; define DA20260 = 0; define DA20261 = 1; define DA20262 = 2; define DA20263 = 3; define DA20264 = 4; define DA20265 = 5; define DA20266 = 6; define DA20267 = 7; define DA20268 = 8; define DA20269 = 9; define DA20270 = 10; define DA20271 = 11; define DA20272 = 12; define DA20273 = 13; define DA20274 = 14; define DA20275 = 15; define DA20276 = 16; define DA20277 = 17; define DA20278 = 18; define DA20279 = 19; define DA20280 = 20; define DA20281 = 21; define DA20282 = 22; define DA2023 = 23; define DA20284 = 24; define DA20285 = 25; define DA20286 = 26; define DA20287 = 27; define DA20288 = 28; define DA20289 = 29; define DA20290 = 30; define DA20291 = 31; define DA20292 = 32; define DA20293 = 33; define DA20294 = 34; define DA20295 = 35; define DA20296 = 36; define DA20297 = 37; define DA20298 = 38; define DA20299 = 39; define DA20300 = 40; define DA20301 = 41; define DA20302 = 42; define DA20303 = 43; define DA20304 = 44; define DA20305 = 45; define DA20306 = 46; define DA20307 = 47; define DA20308 = 48; define DA20309 = 49; define DA20310 = 50; define DA20311 = 51; define DA20312 = 52; define DA20313 = 53; define DA20314 = 54; define DA20315 = 55; define DA20316 = 56; define DA20317 = 57; define DA20318 = 58; define DA20319 = 59; define DA20320 = 60; define DA20321 = 61; define DA20322 = 62; define DA20323 = 63; define DA20324 = 64; define DA20325 = 65; define DA20326 = 66; define DA20327 = 67; define DA20328 = 68; define DA20329 = 69; define DA20330 = 70; define DA20331 = 0; function DA20107(DA20108) { 	if (DA20108 == 0) vm_tctrl(-0); 	else if (DA20108 == 1) vm_tctrl(2); 	else if (DA20108 == 2) vm_tctrl(-2); 	else if (DA20108 == 3) vm_tctrl(-4); 	else if (DA20108 == 4) vm_tctrl(-6); 	else if (DA20108 == 5) vm_tctrl(-8); 	else if (DA20108 == 6) vm_tctrl(-9); } int DA20332, DA20333; int DA20334, DA20335; int DA20336 = FALSE, DA20337; int DA20338 = TRUE; int DA20339; const string DA20777[] = { 	"Off",  "On" } ; int DA20340; int DA20341; int DA20342; int DA20343; int DA20344; int DA20345; int DA20346; int DA20347; int DA20348; int DA20349; int DA20350; int DA20351; int DA20352; int DA20353; int DA20354; int DA20355; int DA20356; int DA20357; int DA20358; int DA20359; int DA20108; int DA20361; int DA20362 ; int DA20363 ; int DA20364 ; define DA20365 = 24; int DA20366; int DA20367; int DA20368; int DA20369; int DA20370; int DA20371; int DA20372; int DA20373; int DA20374; int DA20375 ; int DA20376 ; int DA20377 ; int DA20378 ; int DA20379 ; int DA20380 ; int DA20381 ; int DA20382 ; int DA20383 ; int DA20384 ; int DA20385 ; int DA20386; int DA20387; int DA20388; int DA20389; int DA20390; int DA20391; int DA20392; int DA20393; int DA20394; int DA20395; int DA20396; int DA20397; int DA20398; int DA20399; int DA20400; int DA20401; int DA20402; int DA20403; int DA20404; int DA20405; int DA20406; int DA20407; int DA20408; int DA20409; int DA20410; int DA20411; int DA20412; int DA20413; int DA20414; int DA20415; int DA20416; int DA20417; int DA20418; int DA20419 ; int DA20420 ; int DA20421 ; int DA20422; int DA20423 ; int DA20424 ; int DA20425 ; int DA20426; int DA20427 ; int DA20428 ; int DA20429 ; int DA20430; int DA20431 ; int DA20432 ; int DA20433 ; int DA20434; int DA20435; int DA20436; int DA20437; int DA20438; int DA20439; const int16 DA20783[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		0,2500,25,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 DA20553[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 DA20755[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1,   	    1,  	1,  	1   } ; const string DA20562[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string DA20561[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","Double Tap GrounP","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string DA20759 [] = { 	"Classic","Alternative","Custom", ""  } ; const string DA20858 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string DA20775[] = { 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" } ; const string DA20761[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string DA20828[] = { 	"Right",  "Left",  "" } ; const string DA20826[] = { 	"One Tap",  "Double Tap",  "" } ; const string DA20765[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string DA20767[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string DA20802[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int DA20440 = -1; int DA20441 = -1; int DA20442 = -1; int DA20443 = -1; int DA20444 = -1; int DA20445; int DA20446; int DA20447; int DA20448; int DA20449; const uint8 DA201272[] = { 	4,4,4, 4,4,4, 4,4,4,4 } ; const uint8 DA201273[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 DA201274[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 DA201275[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 DA201276[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 DA201277[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA201278[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA201279[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21 } ; const uint8 DA201280[] = { 	4,4,4, 4,4,4, 4,4,4,4 } ; const uint8 DA201281[] = { 	9, 42, 41, 43, 9, 4, 9, 34, 70, 41, 33,29 } ; const uint8 DA201282[] = { 	7, 10, 7, 41, 10, 70, 41, 42, 43, 7, 33,29  } ; const uint8 DA201283[] = { 	41, 9, 41, 9, 41, 42, 43, 41, 42, 41, 33,29  } ; const uint8 DA201284[] = { 	7, 41, 10, 7, 10, 43, 41, 7, 41, 7, 33 ,29 } ; const uint8 DA201285[] = { 	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29 } ; const uint8 DA201286[] = { 	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29 } ; const uint8 DA201287[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function DA20109(DA20110) { 	if (DA20110 >= 9) { 		DA20450 = -1; 			} 	else if (DA20110 <= 0) { 		DA20450 = 1; 			} 	DA20110 += DA20450; 	return DA20110; 	} function DA20111() { 	vm_tctrl(0); 	if(DA2029 && DA20343){ 		if(DA20531 < 1000){ 			DA20452 = 10; 			DA20477   = 10; 			DA20475  = 10; 					} 			} 	if(DA20455 && DA20344){ 		DA20453 = FALSE; 		if(DA20531 < 1000){ 			DA20452 = 11; 			DA20477   = 11; 			DA20475  = 11; 					} 			} 	if(DA20453 && DA20344){ 		DA20455 = FALSE; 		if(DA20531 < 1000){ 			DA20452 = 10; 			DA20477   = 10; 			DA20475  = 10; 					} 			} 			       if(DA20531 >= 1000){     DA20457 = DA20109(DA20457);     DA20474 = DA20109(DA20474);     DA20475 = DA20109(DA20475);     DA20452 = DA20109(DA20452);     DA20477 = DA20109(DA20477);         while (DA20445 == DA20457 || DA20446 == DA20474 || DA20447 == DA20475 ||            DA20448 == DA20452 || DA20449 == DA20477) {         DA20457 = DA20109(DA20457);         DA20474 = DA20109(DA20474);         DA20475 = DA20109(DA20475);         DA20452 = DA20109(DA20452);         DA20477 = DA20109(DA20477);     }     } 	if(DA20343){ 		if(DA201082 == DA20568){ 			DA20459 = !DA20459; 			if(DA201272[DA20457]) DA20221(DA201272[DA20457]); 					} 		if(DA201082 == DA20226 (DA20568 + 4)){ 			DA20459 = FALSE; 			if(DA201279[DA20474]) DA20221(DA201279[DA20474]); 					} 		if(DA201082 == DA20226 (DA20568 + 1) ){ 			DA20459 = TRUE; 			if(DA201274[DA20452]) DA20221(DA201274[DA20452]); 					} 		if(DA201082 == DA20226 (DA20568 - 1) ){ 			DA20459 = FALSE; 			if(DA201273[DA20452]) DA20221(DA201273[DA20452]); 					} 		if(DA201082 == DA20226 (DA20568 + 2) ){ 			DA20459 = TRUE; 			if(DA201276[DA20477]) DA20221(DA201276[DA20477]); 					} 		if(DA201082 == DA20226 (DA20568 - 2) ){ 			DA20459 = FALSE; 			if(DA201275[DA20477]) DA20221(DA201275[DA20477]); 					} 		if(DA201082 == DA20226 (DA20568 + 3) ){ 			DA20459 = TRUE; 			if(DA201277[DA20475]) DA20221(DA201277[DA20475]); 					} 		if(DA201082 == DA20226 (DA20568 - 3) ){ 			DA20459 = FALSE; 			if(DA201278[DA20475]) DA20221(DA201278[DA20475]); 					} 			} 	if(DA20344){ 		if(DA201082 == DA20568){ 			DA20459 = !DA20459; 			if(DA201280[DA20457]) DA20221(DA201280[DA20457]); 					} 		if(DA201082 == DA20226 (DA20568 + 4)){ 			DA20459 = FALSE; 			if(DA201287[DA20474]) DA20221(DA201287[DA20474]); 					} 		if(DA201082 == DA20226 (DA20568 + 1) ){ 			DA20459 = TRUE; 			if(DA201282[DA20452]) DA20221(DA201282[DA20452]); 					} 		if(DA201082 == DA20226 (DA20568 - 1) ){ 			DA20459 = FALSE; 			if(DA201281[DA20452]) DA20221(DA201281[DA20452]); 					} 		if(DA201082 == DA20226 (DA20568 + 2) ){ 			DA20459 = TRUE; 			if(DA201284[DA20477]) DA20221(DA201284[DA20477]); 					} 		if(DA201082 == DA20226 (DA20568 - 2) ){ 			DA20459 = FALSE; 			if(DA201283[DA20477]) DA20221(DA201283[DA20477]); 					} 		if(DA201082 == DA20226 (DA20568 + 3) ){ 			DA20459 = TRUE; 			if(DA201285[DA20475]) DA20221(DA201285[DA20475]); 					} 		if(DA201082 == DA20226 (DA20568 - 3) ){ 			DA20459 = FALSE; 			if(DA201286[DA20475]) DA20221(DA201286[DA20475]); 					} 			} 			DA20445 = DA20457; DA20446 = DA20474; DA20447 = DA20475; DA20448 = DA20452; DA20449 = DA20477; } int DA20457; int DA20474; int DA20475; int DA20452; int DA20477; function DA20112() { 	if(DA201067){ 		DA20478 += get_rtime(); 			} 	if(DA20478 >= 3000){ 		DA20478 = 0; 		DA201067 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA20431) && !get_ival(DA20432) && !get_ival(DA20430) && !get_ival(DA20429)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA20480 && !combo_running(DA200)) { 			DA201082 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA20480 = TRUE; 			DA201067 = TRUE; 			DA20478 = 0; 			vm_tctrl(0); 			DA20111(); 					} 		set_val(DA201053, 0); 		set_val(DA201054, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		DA20480 = FALSE; 			} 	} function DA20113() { 	DA20166(); 	if (DA20366 == 0 && DA20367 == 0 && DA20368 == 0 && DA20369 == 0 && DA20370 == 0 && DA20371 == 0 && DA20372 == 0 && DA20373 == 0) { 		DA20366 = 4; 		DA20367 = 41; 		DA20368 = 41; 		DA20369 = 42; 		DA20370 = 42; 		DA20371 = 31; 		DA20372 = 31; 		DA20373 = 31; 			} 	DA20897 = get_slot(); 	} int DA20450 = 1; int DA20483; int DA20484; int DA20485 = TRUE; int DA20486[6]; int DA20487; int DA20488; int DA20489; int DA20490; function DA20114(DA20115, DA20116, DA20117) { 	DA20117 = (DA20117 * 14142) / 46340; 	if (DA20116 <= 0) { 		set_polar2(DA20115, (DA20116 = (abs(DA20116) + 360) % 360), min(DA20117, DA20494[DA20116 % 90])); 		return; 			} 	set_polar2(DA20115, inv(DA20116 % 360), min(DA20117, DA20494[DA20116 % 90])); 	} function DA20118(DA20115,DA20120) { 	if (DA20120) return (360 - get_ipolar(DA20115, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + DA20115), 2) + pow(get_ival(43 + DA20115), 2))) + 1; 	} const int16 DA20494[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int DA20497 = 1; combo DA200{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{  DA20107(DA20108); 	if(DA20361){ 		DA20251(); 	} 	if(!DA20484){ 		DA20484 = TRUE; 		DA20483 = random(11111, 99999); 		set_pvar(SPVAR_1,DA20484); 		set_pvar(SPVAR_3,DA20483); 		DA20485 = TRUE; 			} 	if(!DA20490){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			DA20489 = DA20123(DA20489 + 1 ,0,5)DA20485 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			DA20489 = DA20123(DA20489 - 1 ,0,5)DA20485 = TRUE 		} 		if(event_press(PS4_UP)){ 			DA20486[DA20489]  = DA20123(DA20486[DA20489] + 1 ,0,9)DA20485 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			DA20486[DA20489]  = DA20123(DA20486[DA20489] - 1 ,0,9)DA20485 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			DA20487 = 0; 			for(DA20488 = 5; 			DA20488 >= 0; 			DA20488--){ 				DA20487 += DA20486[DA20488] * pow(10,DA20488) 			} 			if(DA20487 == DA20121(DA20483)){ 				DA20490 = TRUE; 				set_pvar(SPVAR_2,DA20490)  			} 			DA20485 = TRUE; 					} 			} 	if(DA20485){ 		cls_oled(0)if(!DA20490){ 			DA20127(DA20483,DA20513,10,OLED_FONT_MEDIUM,OLED_WHITE,DA20514)for( DA20488 = 0; 			DA20488 < 6; 			DA20488++){ 				DA20127(DA20486[DA20488],85 - (DA20488 * 10),40,OLED_FONT_MEDIUM,!(DA20488 == DA20489),DA20514) 			} 					} 		DA20485 = FALSE; 			} 	if(DA20490){ 		if (get_ival(DA20427) || get_ival(DA20431) || get_ival(DA20429) || get_ival(DA20430) || DA20336 || combo_running(DA2072) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			DA20107(DA20108); 					} 		if(get_ival(DA20432) > 40 || (!get_ival(DA20429) && !get_ival(DA20430))){ 			if(get_ival(DA20427)){ 				vm_tctrl(0); 				if(get_ptime(DA20427) > DA20583){ 					set_val(DA20427,0); 									} 							} 					} 		if(!get_ival(DA20429)){ 			if(get_ival(DA20427)){ 				vm_tctrl(0); 				if(get_ptime(DA20427) > DA20583){ 					set_val(DA20427,0); 									} 							} 					} 		if (DA20336) { 			vm_tctrl(0); 			if(DA20337 < 8050){ 				DA20337 += get_rtime(); 							} 			if (DA20337 >= 8000) { 				cls_oled(OLED_BLACK); 				DA20337 = 0; 				DA20336 = FALSE; 							} 					} 		if (block) { 			if (DA20497 < 310) { 				DA20497 += get_rtime(); 							} 			if (DA20497 <= 300 ) { 				DA20163(); 							} 			if (DA20497 > 300 ) { 				block = FALSE; 				DA20497 = 1; 				DA20990 = TRUE; 							} 			if (DA20497 < 0) { 				DA20497 = 1; 							} 			if (DA20497 <= 100) { 				combo_stop(DA2088); 				combo_stop(DA2097); 				combo_stop(DA2089); 				combo_stop(DA2098); 				combo_stop(DA2095); 				combo_stop(DA2096); 				combo_stop(DA2092); 				combo_stop(DA2094); 				combo_stop(DA2091); 				combo_stop(DA2087); 				combo_stop(DA2085); 				combo_stop(DA2090); 				combo_stop(DA20103); 				combo_stop(DA20105); 				combo_stop(DA20100); 				combo_stop(DA20104); 				combo_stop(DA2099); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(DA20417){ 			DA20418 = FALSE; 					} 		if(DA20418){ 			DA20417 = FALSE; 					} 		if(DA20341){ 			DA20342 = FALSE; 			DA20343 = FALSE; 			DA20344 = FALSE; 					} 		if(DA20342){ 			DA20341 = FALSE; 			DA20343 = FALSE; 			DA20344 = FALSE; 					} 		if(DA20343){ 			DA20341 = FALSE; 			DA20342 = FALSE; 			DA20344 = FALSE; 					} 		if(DA20344){ 			DA20341 = FALSE; 			DA20342 = FALSE; 			DA20343 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				DA201100 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				DA201100 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				DA20995 = !DA20995; 				DA20223(DA201218); 				DA20197(DA20995, sizeof(DA20529) - 1, DA20529[0]); 				DA20336 = TRUE; 							} 					} 		if (DA20995) { 			if (DA20359) { 				DA20250(); 							} 			if (event_release(DA20432)) { 				DA20531 = 1; 							} 			if (DA20531 < 8000) { 				DA20531 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					DA20533 = !DA20533; 					DA20223(DA20533); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (DA20533) { 				if (DA20533) DA20216(DA201027); 				if (DA20533) { 					DA20142(); 									} 							} 			else if (!get_ival(DA20432)) { 				DA20216(DA201030); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						DA20332 = TRUE; 						DA20339 = TRUE; 						DA20338 = FALSE; 						if (!DA20332) { 							DA20338 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!DA20338) { 					if (DA20332 || DA20333) { 						vm_tctrl(0); 					} 					if (DA20332) { 						combo_stop(DA2072); 						vm_tctrl(0); 						DA20340= DA20143(DA20340,0  ); 						DA20341 = DA20143(DA20341, 1); 						DA20342  = DA20143(DA20342   ,2  ); 						DA20343  = DA20143(DA20343 , 3); 						DA20344  = DA20143(DA20344 , 4); 						DA20345 = DA20143(DA20345, 5); 						DA20346 = DA20143(DA20346, 6); 						DA20347 = DA20143(DA20347, 7); 						DA20348 = DA20143(DA20348, 8); 						DA20349 = DA20143(DA20349, 9); 						DA20350 = DA20143(DA20350, 10); 						DA20351 = DA20143(DA20351, 11); 						DA20352 = DA20143(DA20352, 12); 						DA20353 = DA20143(DA20353,13); 						DA20354 = DA20143(DA20354, 14); 						DA20355 = DA20143(DA20355, 15); 						DA20356 = DA20143(DA20356, 16); 						DA20357 = DA20143(DA20357, 17); 						DA20358 = DA20143(DA20358, 18); 						DA20359 = DA20143(DA20359, 19); 						DA20108 = DA20143(DA20108, 20); 						DA20361 = DA20143(DA20361, 21); 						DA20362              = DA20143(DA20362              ,22  ); 						DA20363              = DA20143(DA20363              ,23  ); 						DA20364               = DA20143(DA20364               ,24  ); 						if (event_press(PS4_DOWN)) { 							DA20334 = clamp(DA20334 + 1, 0, DA20365); 							DA20339 = TRUE; 													} 						if (event_press(PS4_UP)) { 							DA20334 = clamp(DA20334 - 1, 0, DA20365); 							DA20339 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							DA20332 = FALSE; 							DA20338 = FALSE; 							DA20339 = FALSE; 							vm_tctrl(0); 							combo_run(DA2075); 													} 						if (DA20553[DA20334][2] == 1) { 							if(DA20334 == 0 ){ 								if(DA20340 == 2 ){ 									if (event_press(PS4_CROSS)) { 										DA20335 = DA20553[DA20334][0]; 										DA20332 = FALSE; 										DA20333 = TRUE; 										DA20339 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									DA20335 = DA20553[DA20334][0]; 									DA20332 = FALSE; 									DA20333 = TRUE; 									DA20339 = TRUE; 																	} 															} 													} 						DA20163(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DA20544[0]); 						DA20152(DA20334 + 1, DA20158(DA20334 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DA20546[0]); 						DA20152(DA20897, DA20158(DA20897), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(DA20334 == 0 ){ 							if(DA20340 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, DA20548[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, DA20549[0]); 															} 													} 						else{ 							if (DA20553[DA20334][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, DA20549[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, DA20548[0]); 															} 													} 											} 					if (DA20333) { 						DA20419               = DA20146(DA20419, 0); 						DA20420               = DA20146(DA20420, 1); 						DA20421             = DA20146(DA20421, 2); 						DA20422           = DA20146(DA20422, 3); 						DA20423             = DA20146(DA20423, 4); 						DA20424             = DA20146(DA20424, 5); 						DA20425              = DA20146(DA20425, 6); 						DA20426           = DA20146(DA20426, 7); 						DA20366          = DA20146(DA20366, 8); 						DA20367   = DA20146(DA20367, 9); 						DA20368 = DA20146(DA20368, 10); 						DA20369      = DA20146(DA20369, 11); 						DA20370    = DA20146(DA20370, 12); 						DA20371    = DA20146(DA20371, 13); 						DA20372    = DA20146(DA20372, 14); 						DA20373      = DA20146(DA20373, 15); 						DA20374      = DA20146(DA20374, 16); 						DA20255              = DA20146(DA20255, 17); 						DA20256           = DA20146(DA20256, 18); 						DA20257            = DA20146(DA20257, 19); 						DA20258            = DA20146(DA20258, 20); 						DA20259= DA20146(DA20259, 21); 						DA20387               = DA20146(DA20387, 22); 						DA20388               = DA20146(DA20388, 23); 						DA20389                   = DA20146(DA20389, 24); 						DA20390                   = DA20146(DA20390, 25); 						DA20391                   = DA20146(DA20391, 26); 						DA20392   = DA20146(DA20392, 27); 						DA20393   = DA20146(DA20393, 28); 						DA20394 = DA20146(DA20394, 29); 						DA20395   = DA20146(DA20395, 30); 						DA20396   = DA20146(DA20396, 31); 						DA20397 = DA20146(DA20397, 32); 						DA20398   = DA20146(DA20398, 33); 						DA20399   = DA20146(DA20399, 34); 						DA20400 = DA20146(DA20400, 35); 						DA20401   = DA20146(DA20401, 36); 						DA20402   = DA20146(DA20402, 37); 						DA20403 = DA20146(DA20403, 38); 						DA20404   = DA20149(DA20404, 39); 						DA20405         = DA20149(DA20405, 40); 						DA20406   = DA20146(DA20406, 41); 						DA20407     = DA20146(DA20407, 42); 						DA20408                   = DA20149(DA20408, 43); 						DA201174 = DA20146(DA201174, 54); 						DA201167 = DA20146(DA201167, 55); 						DA20409               = DA20149(DA20409, 44); 						DA20410 = DA20149(DA20410, 45); 						DA20411     = DA20146(DA20411, 46); 						DA20412               = DA20149(DA20412, 47); 						DA20413 = DA20146(DA20413, 48); 						DA20414 = DA20146(DA20414, 49); 						DA20415 = DA20146(DA20415, 50); 						DA20416 = DA20146(DA20416, 51); 						DA20417               = DA20146(DA20417, 52); 						DA20418                 = DA20146(DA20418, 53); 						DA20375       = DA20149(DA20375     ,56 ); 						DA20376       = DA20149(DA20376     ,57 ); 						DA20377      = DA20146(DA20377    ,58 ); 						DA20378   = DA20146(DA20378 ,59 ); 						DA20379       = DA20149(DA20379     ,60 ); 						DA20380       = DA20149(DA20380     ,61 ); 						DA20381   = DA20146(DA20381 ,62 ); 						DA20382      = DA20146(DA20382    ,63 ); 						DA20383          = DA20149(DA20383        ,64 ); 						DA20384          = DA20149(DA20384        ,65 ); 						DA20385         = DA20146(DA20385       ,66 ); 						DA20435             = DA20149(DA20435           ,67 ); 						DA2029             = DA20146(DA2029           ,68); 						DA20455           = DA20146(DA20455         ,69); 						DA20453         = DA20146(DA20453       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								DA20335 = clamp(DA20335 + 1, DA20553[DA20334][0], DA20553[DA20334][1]); 								DA20339 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								DA20335 = clamp(DA20335 - 1, DA20553[DA20334][0], DA20553[DA20334][1]); 								DA20339 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							DA20332 = TRUE; 							DA20333 = FALSE; 							DA20339 = TRUE; 													} 						DA20163(); 						DA20898 = DA20783[DA20335][0]; 						DA20899 = DA20783[DA20335][1]; 						if (DA20783[DA20335][4] == 0) { 							DA20152(DA20898, DA20158(DA20898), 4, 20, OLED_FONT_SMALL); 							DA20152(DA20899, DA20158(DA20899), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (DA20339) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (DA20333) { 							print(DA20208(DA20161(DA20561[DA20335]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA20561[DA20335]); 													} 						else { 							print(DA20208(DA20161(DA20562[DA20334]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA20562[DA20334]); 													} 						DA20339 = FALSE; 					} 									} 				if (!DA20332 && !DA20333) { 					if (DA20338) { 						cls_oled(0); 						combo_run(DA2072); 						DA20338 = FALSE; 						DA20336 = TRUE; 						vm_tctrl(0); 					} 					if(DA20340 == 0){ 						DA20427      = PS4_CIRCLE; 						DA20428      = PS4_CROSS ; 						DA20429    = PS4_L1    ; 						DA20430  = PS4_R1; 						DA20431    = PS4_L2; 						DA20432    = PS4_R2; 						DA20433     = PS4_SQUARE; 						DA20434  = PS4_TRIANGLE; 					} 					else if(DA20340 == 1){ 						DA20427      = PS4_SQUARE; 						DA20428      = PS4_CROSS ; 						DA20429    = PS4_L1    ; 						DA20430  = PS4_R1; 						DA20431    = PS4_L2; 						DA20432    = PS4_R2; 						DA20433     = PS4_CIRCLE; 						DA20434  = PS4_TRIANGLE; 					} 					else if(DA20340 == 2){ 						DA20427      = DA201301[DA20419]; 						DA20428      = DA201301[DA20420] ; 						DA20429    = DA201301[DA20421]  ; 						DA20430  = DA201301[DA20422]; 						DA20431    = DA201301[DA20423]; 						DA20432    = DA201301[DA20424]; 						DA20433     = DA201301[DA20425]; 						DA20434  = DA201301[DA20426]; 					} 					if (DA20566 >= 2000) { 						DA20566 = 2000; 											} 					else if (DA20566 <= 50) { 						DA20566 = 50; 											} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA201123) { 						set_val(DA20428, 0); 						vm_tctrl(0); 						combo_run(DA2077); 											} 					if (DA20990) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 4000 ) ){ 							DA20568 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							DA20985 = DA201312[DA20568][0]; 							DA20644 = DA201312[DA20568][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							DA20408 += 5; 							DA20204(DA20208(sizeof(DA20570) - 1, OLED_FONT_MEDIUM_WIDTH), DA20570[0], DA20408); 													} 						if (event_press(PS4_LEFT)) { 							DA20408 -= 5; 							DA20204(DA20208(sizeof(DA20570) - 1, OLED_FONT_MEDIUM_WIDTH), DA20570[0], DA20408); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !DA20590 ) { 						if (event_press(PS4_UP)) { 							DA20566 += 50; 							DA20204(DA20208(sizeof(DA20576) - 1, OLED_FONT_MEDIUM_WIDTH), DA20576[0], DA20566); 													} 						if (event_press(PS4_DOWN)) { 							DA20566 -= 50; 							DA20204(DA20208(sizeof(DA20576) - 1, OLED_FONT_MEDIUM_WIDTH), DA20576[0], DA20566); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (DA20354) { 						DA20242(); 											} 					if (DA20355) { 						DA20243(); 						DA20244(); 											} 					if (!DA20355) { 						if (get_ival(DA20427)) { 							if (event_press(PS4_RIGHT)) { 								DA20583 += 2; 								DA20204(DA20208(sizeof(DA20584) - 1, OLED_FONT_MEDIUM_WIDTH), DA20584[0], DA20583); 															} 							if (event_press(PS4_LEFT)) { 								DA20583 -= 2; 								DA20204(DA20208(sizeof(DA20584) - 1, OLED_FONT_MEDIUM_WIDTH), DA20584[0], DA20583); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(DA20429) ){ 							if(get_ival(DA20427) && get_ptime(DA20427) > DA20583){ 								set_val(DA20427,0); 															} 													} 											} 					if(DA20358){ 						DA20247(); 											} 					if (DA20350) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								DA20590 = !DA20590; 								DA20223(DA20590); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (DA20590 && DA20350) { 						vm_tctrl(0); 						combo_stop(DA2085); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								DA20404 += 10; 								DA20204(DA20208(sizeof(DA20592) - 1, OLED_FONT_MEDIUM_WIDTH), DA20592[0], DA20404); 															} 							if (event_press(PS4_DOWN)) { 								DA20404 -= 10; 								DA20204(DA20208(sizeof(DA20592) - 1, OLED_FONT_MEDIUM_WIDTH), DA20592[0], DA20404); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						DA20216(DA201029); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								DA20597 = FALSE; 								vm_tctrl(0); 								combo_run(DA2078); 															} 							if (event_press(PS4_LEFT)) { 								DA20597 = TRUE; 								vm_tctrl(0); 								combo_run(DA2078); 															} 							set_val(PS4_L1,0); 													} 											} 					if (DA20351) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								DA20599 = !DA20599; 								DA20223(DA20599); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (DA20599 && DA20351) { 						vm_tctrl(0); 						DA20216(DA201031); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								DA20600 = FALSE; 								vm_tctrl(0); 								combo_run(DA2079); 															} 							if (event_press(PS4_RIGHT)) { 								DA20600 = TRUE; 								vm_tctrl(0); 								combo_run(DA2079); 															} 													} 											} 					if(DA20343 || DA20344 ){ 						DA20112(); 											} 					if (DA20341) { 						if (DA20341 == DA201035) DA20603 = TRUE; 						if (DA20341 == DA201036) { 							if (event_press(DA201311[-1 +DA20374]) && get_brtime(DA201311[-1 +DA20374]) <= 200) { 								DA20603 = !DA20603; 								DA20223(DA20603); 															} 							set_val(DA201311[-1 +DA20374], 0); 													} 						if (DA20341 > 0 && DA20341 < 3 && DA20603 == 1) { 							DA20220(); 													} 						else if (DA20341 == 3) { 							if (get_ival(DA201311[-1 +DA20374])) { 								DA20220(); 															} 							set_val(DA201311[-1 +DA20374], 0); 													} 											} 					if( DA20342 == 0)        DA20606 = FALSE; 					if( DA20342 == DA201035) DA20606 = TRUE; 					if( DA20342 == DA201036) { 						if (event_press( DA201311[ -1 +DA20259]) && get_brtime(DA201311[DA20259])<=200){ 							DA20606 = !DA20606; 							DA20223(DA20606); 													} 						set_val(DA201311[ -1 +DA20259],0); 											} 					if(DA20606 ){ 						DA20218(); 											} 					if(DA20342 > 2 ){ 						if(get_ival(DA201311[ -1 +DA20259])){ 							DA20218(); 													} 						set_val(DA201311[ -1 +DA20259],0); 											} 					if (DA20345) { 						if (DA20345 == 1) { 							DA20609 = PS4_R3; 							DA20606 = FALSE; 													} 						if (DA20345 == 2) { 							DA20609 = PS4_L3; 							DA20606 = FALSE; 													} 						if (DA20345 == 3) { 							DA20609 = XB1_PR1; 							DA20606 = FALSE; 													} 						if (DA20345 == 4) { 							DA20609 = XB1_PR2; 							DA20606 = FALSE; 													} 						if (DA20345 == 5) { 							DA20609 = XB1_PL1; 							DA20606 = FALSE; 													} 						if (DA20345 == 6) { 							DA20609 = XB1_PL2; 							DA20606 = FALSE; 													} 						if(get_ival(DA20609)){ 							if(DA20387 || DA20388){ 								if( DA20387 && event_press(PS4_L1)){ 									DA20459 = FALSE; 									DA20984 = DA20387  ; 									DA20221( DA20387   ); 								} 								if( DA20388 && event_press(PS4_R1)){ 									DA20459 = TRUE; 									DA20984 =  DA20388 ; 									DA20221( DA20388   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( DA20389 ){ 								if(event_press(PS4_SQUARE)){ 									DA20459 = FALSE; 									DA20984 =  DA20389  ; 													combo_stop(DA2088); 				combo_stop(DA2097); 				combo_stop(DA2089); 				combo_stop(DA2098); 				combo_stop(DA2095); 				combo_stop(DA2096); 				combo_stop(DA2092); 				combo_stop(DA2094); 				combo_stop(DA2091); 				combo_stop(DA2087); 				combo_stop(DA2085); 				combo_stop(DA2090); 				combo_stop(DA20103); 				combo_stop(DA20105); 				combo_stop(DA20100); 				combo_stop(DA20104); 				combo_stop(DA2099); 									DA20221( DA20389   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									DA20459 = TRUE; 									DA20984 =  DA20389  ; 									DA20221( DA20389   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( DA20390 ){ 								if(event_press(PS4_CROSS)){ 									DA20459 = FALSE; 									DA20984 =  DA20390  ; 									DA20221( DA20390   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(DA2088); 				combo_stop(DA2097); 				combo_stop(DA2089); 				combo_stop(DA2098); 				combo_stop(DA2095); 				combo_stop(DA2096); 				combo_stop(DA2092); 				combo_stop(DA2094); 				combo_stop(DA2091); 				combo_stop(DA2087); 				combo_stop(DA2085); 				combo_stop(DA2090); 				combo_stop(DA20103); 				combo_stop(DA20105); 				combo_stop(DA20100); 				combo_stop(DA20104); 				combo_stop(DA2099); 									DA20459 = TRUE; 									DA20984 =  DA20390  ; 									DA20221( DA20390   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( DA20391 ){ 								if(event_press(PS4_R3)){ 									DA20459 = FALSE; 									DA20984 =  DA20391  ; 									DA20221( DA20391   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(DA20609,0); 											} 					if (DA20346) { 						if (DA20393 == 1) { 							if (event_press(DA201311[-1 + DA20392]) && !DA201075) { 								vm_tctrl(0); 								combo_run(DA2082); 															} 							else if (event_press(DA201311[-1 + DA20392]) && DA201075) { 								set_val(DA201311[-1 + DA20392], 0); 								DA20459 = !DA20394; 								DA20984 = DA20346; 								DA20221(DA20346); 															} 													} 						else { 							if (event_press(DA201311[-1 + DA20392])) { 								DA20459 = !DA20394; 								set_val(DA201311[-1 + DA20392], 0); 								DA20984 = DA20346; 								DA20221(DA20346); 															} 													} 					} 					if (DA20348) { 						if (DA20399 == 1) { 							if (event_press(DA201311[-1 +DA20398]) && !DA201075) { 								vm_tctrl(0); 								combo_run(DA2082); 															} 							else if (event_press(DA201311[-1 +DA20398]) && DA201075) { 								set_val(DA201311[-1 +DA20398], 0); 								DA20459 = !DA20400; 								DA20984 = DA20348; 								DA20221(DA20348); 															} 													} 						else { 							if (event_press(DA201311[-1 +DA20398])) { 								DA20459 = !DA20400; 								set_val(DA201311[-1 +DA20398], 0); 								DA20984 = DA20348; 								DA20221(DA20348); 															} 													} 					} 					if (DA20347) { 						if (DA20396 == 1) { 							if (event_press(DA201311[-1 +DA20395]) && !DA201075) { 								vm_tctrl(0); 								combo_run(DA2082); 															} 							else if (event_press(DA201311[-1 +DA20395]) && DA201075) { 								set_val(DA201311[-1 +DA20395], 0); 								DA20459 = !DA20397; 								DA20984 = DA20347; 								DA20221(DA20347); 															} 													} 						else { 							if (event_press(DA201311[-1 +DA20395])) { 								DA20459 = !DA20397; 								set_val(DA201311[-1 +DA20395], 0); 								DA20984 = DA20347; 								DA20221(DA20347); 															} 													} 					} 					if (DA20349) { 						if (DA20402 == 1) { 							if (event_press(DA201311[-1 +DA20401]) && !DA201075) { 								vm_tctrl(0); 								combo_run(DA2082); 															} 							else if (event_press(DA201311[-1 +DA20401]) && DA201075) { 								set_val(DA201311[-1 +DA20401], 0); 								DA20459 = !DA20403; 								DA20984 = DA20349; 								DA20221(DA20349); 															} 													} 						else { 							if (event_press(DA201311[-1 +DA20401])) { 								DA20459 = !DA20403; 								set_val(DA201311[-1 +DA20401], 0); 								DA20984 = DA20349; 								DA20221(DA20349); 															} 													} 					} 					if (DA20984 == DA20290 && combo_running(DA2030)) set_val(DA20429, 100); 					if(DA20363){ 						if(!block){ 							if(!get_val(DA20431)){ 								if( !get_val(DA20432)){ 									if(get_val(DA20428)){ 										DA20625 += get_rtime(); 																			} 									if(DA20382){ 										if(get_ival(DA20428) && get_ptime(DA20428) > DA20380){ 											set_val(DA20428,0); 																					} 																			} 									if(event_release(DA20428)){ 										if( DA20625 < DA20379 ){ 											DA20626 = DA20379 - DA20625; 											combo_run(DA20103); 																					} 										else{ 											if(DA20381) combo_run(DA20104); 																					} 										DA20625 = 0; 																			} 																	} 							} 						} 											} 					if(DA20362){ 						if(!block){ 							if(!get_ival(DA20431)){ 								if( !get_ival(DA20432)){ 									if(get_ival(DA20434)){ 										DA20627 += get_rtime(); 																			} 									if(event_release(DA20434)){ 										if(DA20627 < DA20375){ 											DA20628 = DA20375 - DA20627; 											combo_run(DA20105); 																					} 										else{ 											if(DA20378) combo_run(DA2099); 																					} 										DA20627 = 0; 																			} 																	} 							} 						} 											} 					if(DA20364){ 						if(!block){ 							if(get_ival(DA20433)){ 								DA20629 += get_rtime(); 															} 							if(DA20385){ 								if(get_ival(DA20433) && get_ptime(DA20433) > DA20384){ 									set_val(DA20433,0); 																	} 															} 							if(event_release(DA20433)){ 								if(DA20629 && (DA20629 < DA20383)){ 									DA20630 = DA20383 - DA20629; 									combo_run(DA20100); 																	} 								DA20629 = 0; 															} 													} 											} 					if (DA20352) { 						if (event_press(DA201311[-1 +DA20406])) { 							vm_tctrl(0); 							combo_run(DA2077); 													} 						set_val(DA201311[-1 +DA20406], 0); 											} 					if(!DA20356){ 						DA20409 = 0 ; 						DA20410 = 0; 						DA20411 = FALSE; 						DA20412 = 0; 											} 					if (DA20357) { 						DA20243(); 						if (DA20413 == 0) { 							DA20633 = FALSE; 							DA20437 = 0; 													} 						else { 							DA20633 = TRUE; 							DA20437 = 40; 													} 						if (DA20414 == 0) { 							DA20635 = FALSE; 							DA20436 = 0; 													} 						else { 							DA20635 = TRUE; 							DA20436 = 85; 													} 						if (DA20415 == 0) { 							DA20637 = FALSE; 							DA20438 = 0; 													} 						else { 							DA20637 = TRUE; 							DA20438 = -15; 													} 						if (DA20416 == 0) { 							DA20639 = FALSE; 													} 						else { 							DA20639 = TRUE; 													} 						if(DA20415 == 6 || DA20414 == 6 || DA20413 == 6){ 							if (get_ival(DA201311[-1 + DA20415]) || get_ival(DA201311[-1 + DA20414]) || get_ival(DA201311[-1 + DA20413])){ 								combo_run(DA200); 															} 													} 						if (DA20637) { 							if (get_val(DA201311[-1 + DA20415])) { 								set_val(DA201311[-1 + DA20415], 0); 								combo_run(DA2097); 								DA201132 = 9000; 															} 													} 						if (DA20639) { 							if (get_val(DA201311[-1 + DA20416])) { 								set_val(DA201311[-1 + DA20416], 0); 								combo_run(DA2098); 								DA201132 = 9000; 							} 							if (combo_running(DA2098)) { 								if (get_ival(DA20428) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA20432) > 30) { 									combo_stop(DA2098); 																	} 															} 													} 						if (DA20635) { 							if (get_val(DA201311[-1 + DA20414])) { 								set_val(DA201311[-1 + DA20414], 0); 								DA20249(); 								DA201132 = 9000; 															} 													} 						if (DA20633) { 							if (get_val(DA201311[-1 + DA20413])) { 								set_val(DA201311[-1 + DA20413], 0); 								combo_run(DA2095); 								DA201132 = 9000; 															} 													} 											} 					else{ 						DA20437 = 0; 						DA20438 = 0; 						DA20436 = 0; 											} 					if (DA20359) { 						DA20250(); 											} 									} 							} 			if(get_ival(DA20432)){ 				DA20643 = 0; 				combo_stop(DA2087); 							} 					} 		else { 			if (!get_ival(DA20432)) DA20216(DA201028); 					} 			} 			DA20252(); 	} combo DA201 { 	set_val(DA20431, 100); 	set_val(DA20429,100); 	DA20237(); 	wait(400); 	set_val(DA20428,100); 	wait(90); 	vm_tctrl(0); 	wait( 400); 	} combo DA202 { 	set_val(DA20431, 100); 	set_val(DA20429,100); 	DA20237(); 	wait(400); 	set_val(DA20427,100); 	wait(220); 	vm_tctrl(0); 	wait( 400); 	} combo DA203 { 	call(DA2028); 	vm_tctrl(0); 	wait( 100); 	call(DA2098); 	DA20233(DA20985, DA20644); 	vm_tctrl(0); 	wait( 800); 	vm_tctrl(0); 	wait( 350); 	set_val(DA20430,100); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( 400); 	} combo DA204 { 	DA20239(); 	wait(50); 	DA20237(); 	wait(50); 	vm_tctrl(0); 	wait( 350); 	} combo DA205 { 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA206 { 	if (DA20459) DA20651 = DA20568 + 1; 	else DA20651 = DA20568 - 1; 	DA20228(DA20651); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 1000); 	vm_tctrl(0); 	wait( 350); 	} combo DA207 { 	DA20240(); 	DA20459 = FALSE; 	wait(DA20988); 	DA20237(); 	wait(DA20988); 	DA20240(); 	wait(DA20988); 	DA20459 = TRUE; 	DA20237(); 	wait(DA20988); 	wait(350); 	} combo DA208 { 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20459 = TRUE; 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20459 = FALSE; 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA209 { 	DA20459 = TRUE; 	DA20237(); 	wait(DA20988); 	DA20240(); 	wait(DA20988); 	DA20459 = FALSE; 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2010 { 	DA20459 = FALSE; 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20459 = TRUE; 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2011 { 	DA20233(0,0); 	set_val(DA20429,100); 	set_val(DA20430,100); 	DA20239(); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 60); 	} combo DA2012 { 	set_val(DA201051, inv(DA20985)); 	set_val(DA201052, inv(DA20644)); 	set_val(DA20430, 100); 	set_val(DA20429, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA201051, inv(DA20985)); 	set_val(DA201052, inv(DA20644)); 	set_val(DA20430, 100); 	set_val(DA20429, 100); 	vm_tctrl(0); 	wait( 500); 	vm_tctrl(0); 	wait( 350); 	} combo DA2013 { 	DA20233(0, 0); 	set_val(DA20431, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(0, 0); 	set_val(DA20431, 100); 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(0, 0); 	set_val(DA20431, 100); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 80); 	DA20233(0, 0); 	set_val(DA20431, 100); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2014 { 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(inv(DA20985), inv(DA20644)); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 80); 	DA20233(inv(DA20985), inv(DA20644)); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2015 { 	set_val(DA20429, 100); 	DA20237(); 	vm_tctrl(0); 	wait( 500); 	vm_tctrl(0); 	wait( 350); 	} combo DA2016 { 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	if(DA20459) DA20651 = DA20568 + 3; 	else  DA20651 = DA20568 - 3; 	DA20228(DA20651); 	DA20230(DA201079,DA20646); 	wait(DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	if(DA20459) DA20651 = DA20568 + 1; 	else  DA20651 = DA20568 - 1; 	DA20228(DA20651); 	DA20230(DA201079,DA20646); 	wait(DA20988); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2017 { 	set_val(DA20429,100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( DA20988); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2018 { 	set_val(DA20431,100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431,100); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431,100); 	DA20237(); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	set_val(DA20431,100); 	set_val(DA20432,100); 	wait(50); 	wait(350); 	} combo DA2019 { 	set_val(DA20431,100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431,100); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431,100); 	DA20237(); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2020 { 	DA20238(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	DA20237()   vm_tctrl(0); 	wait( DA20988); 	DA20459 = !DA20459; 	DA20236(); 	vm_tctrl(0); 	wait( 1000); 	vm_tctrl(0); 	wait( 350); 	} combo DA2021 { 	set_val(DA20429,100); 	DA20240(); 	wait(50); 	DA20233(0,0); 	set_val(DA20429,100); 	wait(50); 	set_val(DA20429,100); 	DA20240(); 	wait(50); 	vm_tctrl(0); 	wait( 350); 	} combo DA2022 { 	DA20233(0, 0); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2023 { 	DA20240(); 	wait(DA20988); 	DA20241()wait(DA20988); 	DA20240(); 	wait(DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2024 { 	set_val(DA20430, 100); 	set_val(DA20429, 100); 	vm_tctrl(0); 	wait( 20); 	set_val(DA20430, 100); 	set_val(DA20429, 100); 	if (DA20459) DA20651 = DA20568 + 4; 	else { 		DA20651 = DA20568 - 4; 			} 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	set_val(DA20432, 100); 	vm_tctrl(0); 	wait( 100); 	vm_tctrl(0); 	wait( 350); 	} combo DA2025 { 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431, 100); 	vm_tctrl(0); 	wait( 30); 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2026 { 	set_val(DA20431, 100); 	DA20239(); 	vm_tctrl(0); 	wait( 70); 	set_val(DA20431, 100); 	DA20241(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2027 { 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431, 100); 	vm_tctrl(0); 	wait( 30); 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(0, 0); 	vm_tctrl(0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	vm_tctrl(0); 	wait( 70); 	vm_tctrl(0); 	wait( 350); 	} combo DA2028 { 	DA20237(); 	vm_tctrl(0); 	wait( 300); 	set_val(PS4_R3,100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); } combo DA2029 { 	DA20237(); 	set_val(DA20432, 0); 	vm_tctrl(0); 	wait( 310); 	vm_tctrl(0); 	wait( 100); 	vm_tctrl(0); 	wait( 350); 	} combo DA2030 { 	if (DA20984 == DA20292) DA20989 = 200; 	else DA20989 = 1; 	vm_tctrl(0); 	wait( DA20989); 	DA20239(); 	vm_tctrl(0); 	wait( 70); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( 70); 	vm_tctrl(0); 	wait( 350); 	} combo DA2031 { 	set_val(DA20429, 100)DA20239(); 	DA20233(DA20985,DA20644); 	vm_tctrl(0); 	wait( 50); 	set_val(DA20429, 100)DA20241(); 	DA20233(DA20985,DA20644); 	vm_tctrl(0); 	wait( 50); 	set_val(DA20429, 100)DA20237(); 	DA20233(DA20985,DA20644); 	vm_tctrl(0); 	wait( 50); 	DA20233(DA20985,DA20644); 	wait(465); 	DA20233(DA20985,DA20644); 	set_val(DA20431, 100); 	set_val(DA20432, 100); 	wait(50); 	if (DA20459) DA20651 = DA20568 - 1; 	else DA20651 = DA20568 + 1; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 50); 	if (DA20459) DA20651 = DA20568 + 4; 	else DA20651 = DA20568 - 4; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 700); 	vm_tctrl(0); 	wait( 350); 	} combo DA2032 { 	if (DA20984 == DA20292) DA20989 = 200; 	else DA20989 = 1; 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20989); 	DA20239(); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2033 { 	if (DA20459) DA20651 = DA20568 - 2; 	else DA20651 = DA20568 + 2; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	vm_tctrl(0); 	wait( 280); 	DA20241(); 	vm_tctrl(0); 	wait( 50); 	if (DA20459) DA20651 = DA20568 + 2; 	else DA20651 = DA20568 - 2; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 45); 	set_val(DA20427, 100); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 45); 	DA20233(DA201079, DA20646); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 45); 	DA20233(DA201079, DA20646); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 45); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 100); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 500); 	vm_tctrl(0); 	wait( 350); 	} combo DA2034 { 	DA20237(); 	vm_tctrl(0); 	wait( 280); 	DA20236()  set_val(DA20427, 100); 	set_val(DA20431, 100); 	vm_tctrl(0); 	wait( 60); 	DA20236()  set_val(DA20431, 100); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	DA20236()  set_val(DA20431, 100); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 250); 	DA20236()  vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2035 { 	DA20237(); 	vm_tctrl(0); 	wait( 300); 	DA20239(); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2036 { 	set_val(DA20429, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20429, 100); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20429, 100); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2037 { 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2038 { 	set_val(DA20429, 100); 	DA20238(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20429, 100); 	DA20241(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20429, 100); 	DA20237(); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2039 { 	DA20240(); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( 70); 	DA20237(); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( 70); 	vm_tctrl(0); 	wait( 350); 	} combo DA2040 { 	if (DA20459) DA20651 = DA20568 + 3; 	else DA20651 = DA20568 - 3; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	set_val(DA20427, 100); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431,100); 	DA20233(DA201079, DA20646); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 80); 	set_val(DA20431,100); 	DA20233(DA201079, DA20646); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431,100); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2041 { 	set_val(DA20429, 100); 	DA20239(); 	DA20233(0, 0); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20429, 100); 	DA20241(); 	DA20233(0, 0); 	vm_tctrl(0); 	wait( 65); 	set_val(DA20429, 100); 	DA20233(0, 0); 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	if (DA20459) DA20651 = DA20568 + 1; 	else DA20651 = DA20568 - 1; 	DA20228(DA20651); 	set_val(DA20432,0); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 200); 	set_val(DA20432,0); 	vm_tctrl(0); 	wait( 350); 	} combo DA2042 { 	if (DA20984 == DA20292) DA20989 = 200; 	else DA20989 = 1; 	vm_tctrl(0); 	wait( DA20989); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	vm_tctrl(0); 	wait( 350); 	} combo DA2043 { 	DA20240(); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2044 { 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	if (DA20984 == DA20305) DA20236(); 	set_val(DA20431, 100); 	set_val(DA20432, 100); 	vm_tctrl(0); 	wait( 200); 	if (DA20984 == DA20305) DA20236(); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2045 { 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	if (DA20984 == DA20305) DA20236(); 	set_val(DA20431, 100); 	set_val(DA20432, 100); 	vm_tctrl(0); 	wait( 200); 	if (DA20984 == DA20305) DA20236(); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2046 { 	call(DA2033)call(DA2035); 	} combo DA2047 { 	DA20233(DA20985, DA20644); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(DA20985, DA20644); 	DA20241(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(DA20985, DA20644); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431, 100); 	set_val(DA20432, 100); 	DA20233(inv(DA20985), inv(DA20644)); 	vm_tctrl(0); 	wait( 600); 	vm_tctrl(0); 	wait( 350); 	} combo DA2048 { 	DA20233(DA20985, DA20644); 	set_val(XB1_LS, 100); 	DA20239(); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(DA20985, DA20644); 	DA20241(); 	set_val(XB1_LS, 100); 	vm_tctrl(0); 	wait( DA20988); 	DA20233(DA20985, DA20644); 	DA20237(); 	vm_tctrl(0); 	wait( DA20988); 	set_val(DA20431, 100); 	set_val(DA20432, 100); 	if (DA20459) DA20651 = DA20568 + 4; 	else DA20651 = DA20568 - 4; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 220); 	if (DA20459) DA20651 = DA20568 + 4; 	else DA20651 = DA20568 - 4; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 60); 	if (DA20459) DA20651 = DA20568 + 1; 	else DA20651 = DA20568 - 1; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 600); 	vm_tctrl(0); 	wait( 350); 	} combo DA2049 { 	set_val(DA20428, 0); 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 80); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 80); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 80); 	vm_tctrl(0); 	wait( 500); 	vm_tctrl(0); 	wait( 350); 	} combo DA2050 { 	set_val(DA20427, 100); 	set_val(DA20432,100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20432,100); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	set_val(DA20432,100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	set_val(DA20432,100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2051 { 	set_val(DA20429,100); 	set_val(DA20430,100); 	DA20233(inv(DA20985), inv(DA20644)); 	vm_tctrl(0); 	wait( 200); 	set_val(DA20429,100); 	set_val(DA20430,100); 	DA20459 = FALSE; 	DA20236(); 	vm_tctrl(0); 	wait( 50); 	set_val(DA20429,100); 	set_val(DA20430,100); 	DA20459 = !DA20459; 	DA20236(); 	set_val(DA20429,100); 	set_val(DA20430,100); 	vm_tctrl(0); 	wait( 540); 	vm_tctrl(0); 	wait( 350); 	} combo DA2052 { 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	vm_tctrl(0); 	wait( 100); 	vm_tctrl(0); 	wait( 350); 	} combo DA2053 { 	DA20233(inv(DA20985), inv(DA20644)); 	set_val(DA20431, 100); 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(inv(DA20985), inv(DA20644)); 	set_val(DA20431, 100); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(inv(DA20985), inv(DA20644)); 	set_val(DA20431, 100); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	DA20233(0, 0); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2054 { 	set_val(DA20429, 100); 	set_val(DA20433, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20429, 100); 	set_val(DA20433, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20429, 100); 	set_val(DA20433, 0); 	set_val(DA20428, 100); 	DA20236(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20429, 100); 	DA20236(); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2055 { 	set_val(DA20427, 100); 	vm_tctrl(0); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo DA2056 { 	set_val(DA20427, 100); 	set_val(DA20431,100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431,100); 	set_val(DA20427, 100); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431,100); 	set_val(DA20427, 0); 	set_val(DA20428, 100); 	vm_tctrl(0); 	wait( 60); 	vm_tctrl(0); 	wait( 350); 	} combo DA2057 { 	set_val(DA20429, 100); 	DA20239(); 	vm_tctrl(0); 	wait( 300); 	vm_tctrl(0); 	wait( 350); 	} combo DA2058 { 	DA20240(); 	vm_tctrl(0); 	wait( 70); 	DA20241(); 	vm_tctrl(0); 	wait( 70); 	DA20239(); 	vm_tctrl(0); 	wait( 70); 	vm_tctrl(0); 	wait( 350); 	} combo DA2059 { 	set_val(DA20429,100); 	DA20240(); 	vm_tctrl(0); 	wait( 70); 	set_val(DA20429,100); 	DA20241(); 	vm_tctrl(0); 	wait( 70); 	DA20239(); 	set_val(DA20429,100); 	wait(50); 	vm_tctrl(0); 	wait( 350); 	} combo DA2060 { 	DA20233(DA20985, DA20644); 	DA20240(); 	vm_tctrl(0); 	wait( 100); 	DA20241(); 	DA20233(DA20985, DA20644); 	vm_tctrl(0); 	wait( 60); 	DA20239(); 	DA20233(DA20985, DA20644); 	vm_tctrl(0); 	wait( 320); 	DA20233(DA20985, DA20644); 	DA20241(); 	vm_tctrl(0); 	wait( 220); 	DA20233(DA20985, DA20644); 	DA20239(); 	DA20233(DA20985, DA20644); 	vm_tctrl(0); 	wait( 100); 	vm_tctrl(0); 	wait( 350); 	} combo DA2061 { 	call(DA2083); 	DA20233(0, 0); 	call(DA2084); 	call(DA2084); 	call(DA2084); 	call(DA2084); 	call(DA2084); 	set_val(DA20431, 100); 	DA20240(); 	vm_tctrl(0); 	wait( 70); 	set_val(DA20431, 100); 	DA20241(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431, 100); 	DA20239(); 	vm_tctrl(0); 	wait( 60); 	set_val(DA20431, 100); 	vm_tctrl(0); 	wait( 600); 	vm_tctrl(0); 	wait( 350); 	} combo DA2062 { 	set_val(DA20431,100); 	set_val(DA20430,100); 	if (DA20459) DA20651 = DA20568 - 2; 	else DA20651 = DA20568 + 2; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	wait(50); 	set_val(DA20430,100); 	set_val(DA20431,100); 	if (DA20459) DA20651 = DA20568 - 3; 	else DA20651 = DA20568 + 3; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	wait(50); 	set_val(DA20430,100); 	set_val(DA20431,100); 	if (DA20459) DA20651 = DA20568 - 4; 	else DA20651 = DA20568 + 4; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	wait(50); 	set_val(DA20430,100); 	set_val(DA20431,100); 	if (DA20459) DA20651 = DA20568 - 5; 	else DA20651 = DA20568 + 5; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	set_val(DA20431,100); 	set_val(DA20430,100); 	wait(50); 	set_val(DA20430,100); 	set_val(DA20431,100); 	if (DA20459) DA20651 = DA20568 - 6; 	else DA20651 = DA20568 + 6; 	DA20228(DA20651); 	DA20230(DA201079, DA20646); 	wait(50); 	} combo DA2063 { 	vm_tctrl(0); 	wait( 100); 	DA20233(0, 0); 	DA20239(); 	vm_tctrl(0); 	wait( 70); 	DA20233(0, 0); 	DA20241()  vm_tctrl(0); 	wait( 70); 	DA20233(0, 0); 	DA20239()  vm_tctrl(0); 	wait( 70); 	DA20233(0, 0); 	DA20241()  vm_tctrl(0); 	wait( 70); 	DA20233(0, 0); 	DA20240(); 	vm_tctrl(0); 	wait( 70); 	DA20233(0, 0); 	vm_tctrl(0); 	wait( 350); 	} combo DA2064 { 	set_val(PS4_R3,100); 	if (DA20459) DA20651 = DA20568 + 1; 	else DA20651 = DA20568 - 1; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 70); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 400); 	vm_tctrl(0); 	wait( 350); 	} combo DA2065 { 	call(DA2083); 	DA20233(0,0); 	vm_tctrl(0); 	wait( 60); 	set_val(PS4_R3,100); 	if (DA20459) DA20651 = DA20568 + 1; 	else DA20651 = DA20568 - 1; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 70); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 400); 	vm_tctrl(0); 	wait( 350); 	} combo DA2066 { 	call(DA2083); 	DA20233(0,0); 	set_val(DA20431,100); 	set_val(DA20432,100); 	vm_tctrl(0); 	wait( 750); 	} combo DA2067 { 	set_val(PS4_R3,100); 	if (DA20459) DA20651 = DA20568 + 2; 	else DA20651 = DA20568 - 2; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 70); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 400); 	vm_tctrl(0); 	wait( 350); 	} combo DA2068 { 	set_val(DA20431,100); 	set_val(PS4_R3,100); 	if (DA20459) DA20651 = DA20568 ; 	else DA20651 = DA20568; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 70); 	set_val(DA20431,100); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 400); 	vm_tctrl(0); 	wait( 350); 	} combo DA2069 { 	call(DA2083); 	set_val(DA20431,100); 	set_val(PS4_R3,100); 	if (DA20459) DA20651 = DA20568 ; 	else DA20651 = DA20568; 	DA20228(DA20651); 	DA20233(DA201079, DA20646); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 70); 	set_val(DA20431,100); 	DA20233(DA201079, DA20646); 	vm_tctrl(0); 	wait( 400); 	vm_tctrl(0); 	wait( 350); 	} combo DA2070 { 	DA20233(0,0); 	set_val(DA20430,100); 	set_val(DA20429,100); 	DA20237(); 	vm_tctrl(0); 	wait( 350); 	vm_tctrl(0); 	wait( 350); 	set_val(DA20430,100); 	set_val(DA20429,100); 	vm_tctrl(0); 	wait( 400); 	} int DA20135 ; int DA20709 ; int DA20710 ; int DA20711; int DA20712; function DA20121(DA20122){ 	DA20709 = 2; 	DA20710 = 987654; 	DA20135 = 54321; 	DA20711 = (DA20122 >> DA20709) | (DA20122 << (32 - DA20709)); 	DA20712 = (((DA20711 >> ((DA20711 & 0xF) % 13)) & 0x7FFFF) + DA20135) % DA20710 + 123456; 	return DA20712; 	} define DA20714 = -1; define DA20513 = -2; define DA20716 = -3; define DA20717 = 0; define DA20514 = 1; function DA20123(DA20122, DA20125, DA20126) { 	if(DA20122 > DA20126) return DA20125; 	if(DA20122 < DA20125) return DA20126; 	return DA20122; 	} int DA20721,DA20722; function DA20127(DA20128,DA20129,DA20130,DA20131,DA20132,DA20133){ 	if(!DA20133){ 		print(DA20136(DA20134(DA20128),DA20131,DA20129),DA20130,DA20131,DA20132,DA20128)     	} 	else{ 		if(DA20128 < 0){ 			putc_oled(1,45); 					} 		if(DA20128){ 			for(DA20721 = DA20140(DA20128) + DA20722 = (DA20128 < 0 ),DA20128 = abs(DA20128); 			DA20128 > 0; 			DA20721-- , DA20722++){ 				putc_oled(DA20721,DA20128%10 + 48); 				DA20128 = DA20128/10; 							} 					} 		else{ 			putc_oled(1,48); 			DA20722 = 1         		} 		puts_oled(DA20136(DA20722,DA20131,DA20129),DA20130,DA20131,DA20722 ,DA20132); 			} 	} int DA20743; function DA20134(DA20135) { 	DA20743 = 0; 	do { 		DA20135++; 		DA20743++; 			} 	while (duint8(DA20135)); 	return DA20743; 	} function DA20136(DA20137,DA20131,DA20129) { 	if(DA20129 == -3){ 		return 128 - ((DA20137 * (7 + (DA20131 > 1) + DA20131 * 4)) + 3 ); 			} 	if(DA20129 == -2){ 		return 64 - ((DA20137 * (7 + (DA20131 > 1) + DA20131 * 4)) / 2); 			} 	if(DA20129 == -1){ 		return 3 	} 	return DA20129; 	} function DA20140(DA20141) { 	for(DA20721 = 1; 	DA20721 < 11; 	DA20721++){ 		if(!(abs(DA20141) / pow(10,DA20721))){ 			return DA20721; 			break; 					} 			} 	return 1; 	} function DA20142() { 	if (get_ival(DA20427)) { 		set_val(DA20427, 0); 		if (get_ival(DA20429)) DA20749 = 50; 		if (!get_ival(DA20429)) DA20749 = 410; 		combo_run(DA2071); 			} 	if (DA20748 > 0) set_polar(POLAR_LS, DA20748 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) DA20748 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) DA20748 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) DA20748 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) DA20748 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA20748 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA20748 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA20748 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA20748 = 270; } int DA20749; int DA20533; int DA20748; combo DA2071 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait( DA20749); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	DA20533 = !DA20533; } define DA20752 = 19; function DA20143(DA20144, DA20145) { 	if (DA20334 == DA20145) { 		if (event_press(PS4_RIGHT)) { 			DA20144 = clamp(DA20144 + 1, 0, DA20755[DA20334]); 			DA20339 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			DA20144 = clamp(DA20144 - 1, 0, DA20755[DA20334]); 			DA20339 = TRUE; 					} 		if (DA20334 == 0) { 			print(DA20208(DA20161(DA20759[DA20340]) ,OLED_FONT_SMALL_WIDTH),DA20752  ,OLED_FONT_SMALL , OLED_WHITE ,DA20759[DA20340]); 					} 		else if (DA20334 == 1) { 			print(DA20208(DA20161(DA20761[DA20341]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20761[DA20341]); 					} 		else if (DA20334 == 2) { 			print(DA20208(DA20161(DA20761[DA20342]) ,OLED_FONT_SMALL_WIDTH ),DA20752  ,OLED_FONT_SMALL , OLED_WHITE ,DA20761[DA20342]); 					} 		else if (DA20334 == 5) { 			print(DA20208(DA20161(DA20765[DA20345]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20765[DA20345]); 					} 		else if (DA20334 == 6) { 			print(DA20208(DA20161(DA20767[DA20346]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20346]); 					} 		else if (DA20334 == 7) { 			print(DA20208(DA20161(DA20767[DA20347]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20347]); 					} 		else if (DA20334 == 8) { 			print(DA20208(DA20161(DA20767[DA20348]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20348]); 					} 		else if (DA20334 == 9) { 			print(DA20208(DA20161(DA20767[DA20349]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20349]); 					} 		else if (DA20334 == 20) { 			print(DA20208(DA20161(DA20775[DA20108]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20775[DA20108]); 					} 		else { 			if (DA20144 == 1)        print(DA20208(DA20161(DA20777[1]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20777[1])      else        print(DA20208(DA20161(DA20777[0]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20777[0])     		} 			} 	return DA20144; 	} function DA20146(DA20144, DA20145) { 	if (DA20335 == DA20145) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA20144 += DA20783[DA20335][2]  				        DA20339 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA20144 -= DA20783[DA20335][2]  				        DA20339 = TRUE; 							} 			DA20144 = clamp(DA20144, DA20783[DA20335][0], DA20783[DA20335][1]); 		} 		if (DA20335 == 8) { 			print(DA20208(DA20161(DA20767[DA20366]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20366])     		} 		else if (DA20335 == 9) { 			print(DA20208(DA20161(DA20767[DA20367]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20367])     		} 		else if (DA20335 == 10) { 			print(DA20208(DA20161(DA20767[DA20368]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20368])     		} 		else if (DA20335 == 11) { 			print(DA20208(DA20161(DA20767[DA20369]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20369])     		} 		else if (DA20335 == 12) { 			print(DA20208(DA20161(DA20767[DA20370]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20370])     		} 		else if (DA20335 == 13) { 			print(DA20208(DA20161(DA20767[DA20371]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20371])     		} 		else if (DA20335 == 14) { 			print(DA20208(DA20161(DA20767[DA20372]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20372])     		} 		else if (DA20335 == 15) { 			print(DA20208(DA20161(DA20767[DA20373]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20373])     		} 		else if (DA20335 == 16) { 			print(DA20208(DA20161(DA20802[DA20374]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20374])     		} 		else if (DA20335 == 17) { 			print(DA20208(DA20161(DA20767[DA20255]),OLED_FONT_SMALL_WIDTH ),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20767[DA20255])  		} 		else if(DA20335 == 18){ 			print(DA20208(DA20161(DA20767[DA20256]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20767[DA20256])  		} 		else if(DA20335 == 19){ 			print(DA20208(DA20161(DA20767[DA20257]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20767[DA20257])  		} 		else if(DA20335 == 20){ 			print(DA20208(DA20161(DA20767[DA20258]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20767[DA20258])  		} 		else if(DA20335 == 21){ 			print(DA20208(DA20161(DA20802[DA20259]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20802[DA20259])       		} 		else if(DA20335 == 22){ 			print(DA20208(DA20161(DA20767[DA20387]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20387])     		} 		else if (DA20335 == 23) { 			print(DA20208(DA20161(DA20767[DA20388]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20388])     		} 		else if (DA20335 == 24) { 			print(DA20208(DA20161(DA20767[DA20389]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20389])     		} 		else if (DA20335 == 25) { 			print(DA20208(DA20161(DA20767[DA20390]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20390])     		} 		else if (DA20335 == 26) { 			print(DA20208(DA20161(DA20767[DA20391]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20767[DA20391])     		} 		else if (DA20335 == 27) { 			print(DA20208(DA20161(DA20802[DA20392]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20392])     		} 		else if (DA20335 == 28) { 			print(DA20208(DA20161(DA20826[DA20393]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20826[DA20393])     		} 		else if (DA20335 == 29) { 			print(DA20208(DA20161(DA20828[DA20394]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20828[DA20394])     		} 		else if (DA20335 == 30) { 			print(DA20208(DA20161(DA20802[DA20395]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20395])     		} 		else if (DA20335 == 31) { 			print(DA20208(DA20161(DA20826[DA20396]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20826[DA20396])     		} 		else if (DA20335 == 32) { 			print(DA20208(DA20161(DA20828[DA20397]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20828[DA20397])     		} 		else if (DA20335 == 33) { 			print(DA20208(DA20161(DA20802[DA20398]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20398])     		} 		else if (DA20335 == 34) { 			print(DA20208(DA20161(DA20826[DA20399]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20826[DA20399])     		} 		else if (DA20335 == 35) { 			print(DA20208(DA20161(DA20828[DA20400]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20828[DA20400])     		} 		else if (DA20335 == 36) { 			print(DA20208(DA20161(DA20802[DA20401]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20401])     		} 		else if (DA20335 == 37) { 			print(DA20208(DA20161(DA20826[DA20402]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20826[DA20402])     		} 		else if (DA20335 == 38) { 			print(DA20208(DA20161(DA20828[DA20403]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20828[DA20403])     		} 		else if (DA20335 == 41) { 			print(DA20208(DA20161(DA20802[DA20406]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20406])     		} 		else if (DA20335 == 48) { 			print(DA20208(DA20161(DA20802[DA20413]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20413])     		} 		else if (DA20335 == 49) { 			print(DA20208(DA20161(DA20802[DA20414]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20414])     		} 		else if (DA20335 == 50) { 			print(DA20208(DA20161(DA20802[DA20415]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20415])     		} 		else if (DA20335 == 51) { 			print(DA20208(DA20161(DA20802[DA20416]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20802[DA20416])     		} 		else if(DA20335 == 0){ 			print(DA20208(DA20161(DA20858[DA20419]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20419])  		} 		else if(DA20335 == 1){ 			print(DA20208(DA20161(DA20858[DA20420]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20420])  		} 		else if(DA20335 == 2){ 			print(DA20208(DA20161(DA20858[DA20421]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20421])  		} 		else if(DA20335 == 3){ 			print(DA20208(DA20161(DA20858[DA20422]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20422])  		} 		else if(DA20335 == 4){ 			print(DA20208(DA20161(DA20858[DA20423]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20423])  		} 		else if(DA20335 == 5){ 			print(DA20208(DA20161(DA20858[DA20424]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20424])  		} 		else if(DA20335 == 6){ 			print(DA20208(DA20161(DA20858[DA20425]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20425])  		} 		else if(DA20335 == 7){ 			print(DA20208(DA20161(DA20858[DA20426]),OLED_FONT_SMALL_WIDTH),DA20752,OLED_FONT_SMALL,OLED_WHITE,DA20858[DA20426])  		} 		else{ 			if (DA20144 == 1)        print(DA20208(DA20161(DA20777[1]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20777[1])      else        print(DA20208(DA20161(DA20777[0]), OLED_FONT_SMALL_WIDTH), DA20752, OLED_FONT_SMALL, OLED_WHITE, DA20777[0])     		} 		DA20164(0); 			} 	return DA20144; 	} function DA20149(DA20144, DA20145) { 	if (DA20335 == DA20145) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA20144 += DA20783[DA20335][2]  				        DA20339 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA20144 -= DA20783[DA20335][2]  				        DA20339 = TRUE; 							} 			if (event_press(PS4_UP)) { 				DA20144 += DA20783[DA20335][3]  				        DA20339 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				DA20144 -= DA20783[DA20335][3]  				        DA20339 = TRUE; 							} 			DA20144 = clamp(DA20144, DA20783[DA20335][0], DA20783[DA20335][1]); 		} 		DA20211(DA20144, DA20214(DA20144)); 	} 	return DA20144; 	} int DA20885, DA20886, DA20887; function DA20152(DA20122, DA20154, DA20155, DA20156, DA20131) { 	DA20886 = 1; 	DA20887 = 10000; 	if (DA20122 < 0)  	  { 		putc_oled(DA20886, 45); 		DA20886 += 1; 		DA20122 = abs(DA20122); 			} 	for (DA20885 = 5; 	DA20885 >= 1; 	DA20885--) { 		if (DA20154 >= DA20885) { 			putc_oled(DA20886, DA20893[DA20122 / DA20887]); 			DA20122 = DA20122 % DA20887; 			DA20886 += 1; 					} 		DA20887 /= 10; 			} 	puts_oled(DA20155, DA20156, DA20131, DA20886 - 1, OLED_WHITE); } const string DA20549 = " No Edit Variable"; const string DA20548 = " A/CROSS to Edit "; const string DA20544 = "MOD;"; const string DA20546 = "MSL;"; int DA20897; function DA20158(DA20141) { 	DA20141 = abs(DA20141); 	if (DA20141 / 10000 > 0) return 5; 	if (DA20141 / 1000 > 0) return 4; 	if (DA20141 / 100 > 0) return 3; 	if (DA20141 / 10 > 0) return 2; 	return 1; 	} const int8 DA20893[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int DA20898, DA20899; const image DA20901 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DA2072 { 	call(DA2073); 	DA20160(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, DA20901[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DA20901[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(DA2074); 	vm_tctrl(0);wait( 1000); 	DA20336 = TRUE; 	} combo DA2073 { 	cls_oled(OLED_BLACK); 	} int DA20903; enum { 	DA20904 = -2, DA20905, DA20906 = 5, DA20907 = -1, DA20908 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo DA2074 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 DA201295[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DA201296[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DA201297[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 DA201298[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int DA20909; int DA20910; int DA20911; int DA20912; int DA20913; int DA20914; int DA20915; function DA20160() { 	DA20915 = 3; 	DA20913 = DA20915 * DA201298[DA20914]; 	DA20912 = DA20915 * DA201295[DA20914]; 	DA20910 = ((DA20913 * DA201297[DA20909]) / 328) - ((DA20912 * DA201296[DA20909]) / 328); 	DA20911 = ((DA20913 * DA201296[DA20909]) / 328) + ((DA20912 * DA201297[DA20909]) / 328); 	DA20913 = DA20910; 	DA20912 = DA20911; 	DA20914 += 1; 	DA20909 += 45; 	if(DA20914 >= 360) { 		DA20914 %= 360; 			} 	if(DA20909 >= 360) { 		DA20909 %= 360; 			} 	pixel_oled(64 + (((DA20913 / DA20915) * 30) / 328), 32 + (((DA20912 / DA20915) * 30) / 328), OLED_WHITE); 	} int DA20918; function DA20161(DA20135) { 	DA20918 = 0; 	do { 		DA20135++; 		DA20918++; 			} 	while (duint8(DA20135)); 	return DA20918; 	} int DA20921; const uint8 DA201299[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function DA20163() { 	for (DA20921 = 0; 	DA20921 < sizeof(DA201299) / sizeof(DA201299[0]); 	DA20921++) { 		if (get_ival(DA201299[DA20921]) || event_press(DA201299[DA20921])) { 			set_val(DA201299[DA20921], 0); 		} 			} 	} define DA20922 = 131; define DA20923 = 132; define DA20924 = 133; define DA20925 = 134; define DA20926 = 130; define DA20927 = 89; define DA20928 = 127; define DA20929 = 65; int DA20930; int DA20931; int DA20932 = 1; define DA20933 = 36; const string DA20934 = "Hold LT/L2 +"; function DA20164(DA20165) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, DA20934[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, DA20932); 	putc_oled(1, DA20924); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DA20930); 	putc_oled(1, DA20925); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DA20931); 	if (DA20165) { 		putc_oled(1, DA20922); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DA20930); 		putc_oled(1, DA20923); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DA20931); 			} 	} const uint8 DA201301 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function DA20166() { 	DA20944 = sizeof(data); 	DA20484 = get_pvar(SPVAR_1,0,1,0); 	DA20490 = get_pvar(SPVAR_2,0,1,0); 	DA20483 = get_pvar(SPVAR_3,11111, 99999,11111); 	DA20168(); 	if (DA20193(0, 1, 0)) { 		DA20345 = DA20193(  0, 6, 0); 		DA20342 = DA20193(0, 3, 0); 		DA20343 = DA20193(0,1,0); 		DA20344 = DA20193(0,1,0); 		DA20255 = DA20193(0, 70, 0); 		DA20256 = DA20193(0, 70, 0); 		DA20257 = DA20193(0, 70, 0); 		DA20258 = DA20193(0, 70, 0); 		DA20259 = DA20193(0, 22, 8); 		DA20346 = DA20193(0, 70, 0); 		DA20347 = DA20193(0, 70, 0); 		DA20348 = DA20193(0, 70, 0); 		DA20349 = DA20193(0, 70, 0); 		DA20350 = DA20193(0, 1, 0); 		DA20351 = DA20193(0, 1, 0); 		DA20352 = DA20193(0, 1, 0); 		DA20353 = DA20193(0, 1, 0); 		DA20361 = DA20193(0, 1, 0); 		DA20387 = DA20193(0, 70, 0); 		DA20388 = DA20193(0, 70, 0); 		DA20389 = DA20193(0, 70, 0); 		DA20390 = DA20193(0, 70, 0); 		DA20391 = DA20193(0, 70, 0); 		DA20392 = DA20193(1, 25, 1); 		DA20393 = DA20193(0, 1, 0); 		DA20394 = DA20193(0, 1, 0); 		DA20395 = DA20193(1, 25, 5); 		DA20396 = DA20193(0, 1, 0); 		DA20397 = DA20193(0, 1, 0); 		DA20398 = DA20193(0, 25, 2); 		DA20399 = DA20193(0, 1, 0); 		DA20400 = DA20193(0, 1, 1); 		DA20401 = DA20193(1, 25, 8); 		DA20402 = DA20193(0, 1, 0); 		DA20403 = DA20193(0, 1, 1); 		DA20404 = DA20193(350, 600, 350); 		DA20405 = DA20193(350, 600, 445); 		DA20406 = DA20193(0, 22, 0); 		DA20407 = DA20193(0, 1, 0); 		DA20408 = DA20193(-100, 300, 0); 		DA20354 = DA20193(0, 1, 0); 		DA20355 = DA20193(0, 1, 0); 		DA20356 = DA20193(0, 1, 0); 		DA20357 = DA20193(0, 1, 0); 		DA20358 = DA20193(0, 1, 0); 		DA20409 = DA20193(-150, 150, 0); 		DA20410 = DA20193(-150, 150, 0); 		DA20411 = DA20193(0, 1, 0); 		DA20412 = DA20193(-150, 150, 0); 		DA20413 = DA20193(0, 22, 0); 		DA20414 = DA20193(0, 22, 0); 		DA20415 = DA20193(0, 22, 0); 		DA20416 = DA20193(0, 22, 0); 		DA20583 = DA20193(60, 400, 235); 		DA20418 = DA20193(0, 1, 0); 		DA20417 = DA20193(0, 1, 0); 		DA20341 = DA20193(0, 3, 0); 		DA20366 = DA20193(0, 70, 0); 		DA20367 = DA20193(0, 70, 0); 		DA20368 = DA20193(0, 70, 0); 		DA20371 = DA20193(0, 70, 0); 		DA20372 = DA20193(0, 70, 0); 		DA20373 = DA20193(0, 70, 0); 		DA20374 = DA20193(0, 22, 8); 		DA20359 = DA20193(0, 1, 0); 		DA20369 = DA20193(0, 70, 0); 		DA20370 = DA20193(0, 70, 0); 		DA20566 = DA20193(50, 2000, 1100); 		DA201174 = DA20193(0, 1, 0); 		DA201167 = DA20193(0, 1, 0); 		DA20108 = DA20193(0, 6, 0); 		DA20386 = DA20193(0, 1, 0); 		DA20340 = DA20193(0, 2, 0); 		DA20419 = DA20193(0, 9, 9); 		DA20420 = DA20193(0, 9, 8); 		DA20421 = DA20193(0, 9, 3); 		DA20422 = DA20193(0, 9, 1); 		DA20423 = DA20193(0, 9, 4); 		DA20424 = DA20193(0, 9, 0); 		DA20425 = DA20193(0, 9, 7); 		DA20426 = DA20193(0, 9, 6); 		DA20362    = DA20193(0, 1, 0); 		DA20363    = DA20193(0, 1, 0); 		DA20364     = DA20193(0, 1, 0); 		DA20375     = DA20193(60, 500, 120); 		DA20376     = DA20193(60, 500, 350); 		DA20377    = DA20193(0, 1, 0); 		DA20378 = DA20193(0, 1, 0); 		DA20379     = DA20193(50, 250, 80); 		DA20380     = DA20193(100, 850, 180); 		DA20381 = DA20193(0, 1, 0); 		DA20382    = DA20193(0, 1, 0); 		DA20383        = DA20193(80, 500, 120); 		DA20384        = DA20193(80, 500, 350); 		DA20385       = DA20193(0, 1, 0); 		DA20435           = DA20193(0, 2500, 750); 		DA2029           = DA20193(0, 1, 0); 		DA20455         = DA20193(0, 1, 0); 		DA20453       = DA20193(0, 1, 0); 	} 	else{ 		DA20345 = 0; 		DA20342 = 0; 		DA20343 = 0; 		DA20344 = 0; 		DA20255 = 0; 		DA20256 = 0; 		DA20257 = 0; 		DA20258 = 0; 		DA20259 = 8; 		DA20346 = 0; 		DA20347 = 0; 		DA20348 = 0; 		DA20349 = 0; 		DA20350 = 0; 		DA20351 = 0; 		DA20352 = 0; 		DA20353 = 0; 		DA20361 = 0; 		DA20387 = 0; 		DA20388 = 0; 		DA20389 = 0; 		DA20390 = 0; 		DA20391 = 0; 		DA20392 = 1; 		DA20393 = 0; 		DA20394 = 0; 		DA20395 = 5; 		DA20396 = 0; 		DA20397 = 0; 		DA20398 = 2; 		DA20399 = 0; 		DA20400 = 1; 		DA20401 = 8; 		DA20402 = 0; 		DA20403 = 1; 		DA20404 = 350; 		DA20405 = 445; 		DA20406 = 0; 		DA20407 = 0; 		DA20408 = 0; 		DA20354 = 0; 		DA20355 = 0; 		DA20356 = 0; 		DA20357 = 0; 		DA20358 = 0; 		DA20409 = 0; 		DA20410 = 0; 		DA20411 = 0; 		DA20412 = 0; 		DA20413 = 0; 		DA20414 = 0; 		DA20415 = 0; 		DA20416 = 0; 		DA20583 = 235; 		DA20418 = 0; 		DA20417 = 0; 		DA20341 = 0; 		DA20366 = 0; 		DA20367 = 0; 		DA20368 = 0; 		DA20371 = 0; 		DA20372 = 0; 		DA20373 = 0; 		DA20374 = 8; 		DA20359 = 0; 		DA20369 = 0; 		DA20370 = 0; 		DA20566 = 1100; 		DA201174 = 0; 		DA201167 = 0; 		DA20108 = 0; 		DA20386 = 0; 		DA20340 = 0; 		DA20419 = 9; 		DA20420 = 8; 		DA20421 = 3; 		DA20422 = 1; 		DA20423 = 4; 		DA20424 = 0; 		DA20425 = 7; 		DA20426 = 6; 		DA20362 = 0; 		DA20363 = 0; 		DA20364 = 0; 		DA20375 = 120; 		DA20376 = 350; 		DA20377 = 0; 		DA20378 = 0; 		DA20379 = 80; 		DA20380 = 180; 		DA20381 = 0; 		DA20382 = 0; 		DA20383 = 120; 		DA20384 = 360; 		DA20385 = 0; 		DA20435     = 750; 		DA2029     = 0; 		DA20455     = 0; 		DA20453     = 0; 			} 	if (DA20340 == 0) { 		DA20427 = PS4_CIRCLE; 		DA20428 = PS4_CROSS; 		DA20429 = PS4_L1; 		DA20430 = PS4_R1; 		DA20431 = PS4_L2; 		DA20432 = PS4_R2; 		DA20433 = PS4_SQUARE; 		DA20434 = PS4_TRIANGLE; 			} 	else if (DA20340 == 1) { 		DA20427      = PS4_SQUARE; 		DA20428      = PS4_CROSS ; 		DA20429    = PS4_L1    ; 		DA20430  = PS4_R1; 		DA20431    = PS4_L2; 		DA20432    = PS4_R2; 		DA20433     = PS4_CIRCLE; 		DA20434  = PS4_TRIANGLE; 	} 	else if (DA20340 == 2) { 		DA20427 = DA201301[DA20419]; 		DA20428 = DA201301[DA20420]; 		DA20429 = DA201301[DA20421]; 		DA20430 = DA201301[DA20422]; 		DA20431 = DA201301[DA20423]; 		DA20432 = DA201301[DA20424]; 		DA20433 = DA201301[DA20425]; 		DA20434 = DA201301[DA20426]; 			} 	} function DA20167() { 	DA20168(); 	DA20191(   1,0,     1); 	DA20191(DA20345, 0, 6); 	DA20191(DA20342, 0, 3); 	DA20191(DA20343, 0 , 1); 	DA20191(DA20344, 0 , 1); 	DA20191(DA20255, 0, 70); 	DA20191(DA20256, 0, 70); 	DA20191(DA20257, 0, 70); 	DA20191(DA20258, 0, 70); 	DA20191(DA20259, 0, 22); 	DA20191(DA20346, 0, 70); 	DA20191(DA20347, 0, 70); 	DA20191(DA20348, 0, 70); 	DA20191(DA20349, 0, 70); 	DA20191(DA20350, 0, 1); 	DA20191(DA20351, 0, 1); 	DA20191(DA20352, 0, 1); 	DA20191(DA20353, 0, 1); 	DA20191(DA20361, 0, 1); 	DA20191(DA20387, 0, 70); 	DA20191(DA20388, 0, 70); 	DA20191(DA20389, 0, 70); 	DA20191(DA20390, 0, 70); 	DA20191(DA20391, 0, 70); 	DA20191(DA20392, 1, 25); 	DA20191(DA20393, 0, 1); 	DA20191(DA20394, 0, 1); 	DA20191(DA20395, 1, 25); 	DA20191(DA20396, 0, 1); 	DA20191(DA20397, 0, 1); 	DA20191(DA20398, 0, 25); 	DA20191(DA20399, 0, 1); 	DA20191(DA20400, 0, 1); 	DA20191(DA20401, 1, 25); 	DA20191(DA20402, 0, 1); 	DA20191(DA20403, 0, 1); 	DA20191(DA20404, 350, 600); 	DA20191(DA20405, 350, 600); 	DA20191(DA20406, 0, 22); 	DA20191(DA20407, 0, 1); 	DA20191(DA20408, -100, 300); 	DA20191(DA20354, 0, 1); 	DA20191(DA20355, 0, 1); 	DA20191(DA20356, 0, 1); 	DA20191(DA20357, 0, 1); 	DA20191(DA20358, 0, 1); 	DA20191(DA20409, -150, 150); 	DA20191(DA20410, -150, 150); 	DA20191(DA20411, 0, 1); 	DA20191(DA20412, -150, 150); 	DA20191(DA20413, 0, 22); 	DA20191(DA20414, 0, 22); 	DA20191(DA20415, 0, 22); 	DA20191(DA20416, 0, 22); 	DA20191(DA20583, 60, 400); 	DA20191(DA20418, 0, 1); 	DA20191(DA20417, 0, 1); 	DA20191(DA20341, 0, 3); 	DA20191(DA20366, 0, 70); 	DA20191(DA20367, 0, 70); 	DA20191(DA20368, 0, 70); 	DA20191(DA20371, 0, 70); 	DA20191(DA20372, 0, 70); 	DA20191(DA20373, 0, 70); 	DA20191(DA20374, 0, 22); 	DA20191(DA20359, 0, 1); 	DA20191(DA20369, 0, 70); 	DA20191(DA20370, 0, 70); 	DA20191(DA20566, 50, 2000); 	DA20191(DA201174, 0, 1); 	DA20191(DA201167, 0, 1); 	DA20191(DA20108, 0, 6); 	DA20191(DA20386, 0, 1); 	DA20191(DA20340, 0, 2); 	DA20191(DA20419, 0, 9); 	DA20191(DA20420, 0, 9); 	DA20191(DA20421, 0, 9); 	DA20191(DA20422, 0, 9); 	DA20191(DA20423, 0, 9); 	DA20191(DA20424, 0, 9); 	DA20191(DA20425, 0, 9); 	DA20191(DA20426, 0, 9); 	DA20191(DA20362,    0, 1); 	DA20191(DA20363,    0, 1); 	DA20191(DA20364,     0, 1); 	DA20191(DA20375,     60, 500); 	DA20191(DA20376,     60, 500); 	DA20191(DA20377,    0, 1); 	DA20191(DA20378, 0, 1); 	DA20191(DA20379,     50, 250); 	DA20191(DA20380,     100, 850); 	DA20191(DA20381, 0, 1); 	DA20191(DA20382,    0, 1); 	DA20191(DA20383,        80, 500); 	DA20191(DA20384,        80, 500); 	DA20191(DA20385,       0, 1); 	DA20191(DA20435 ,         0,2500); 	DA20191(DA2029,           0,1); 	DA20191(DA20455,           0,1); 	DA20191(DA20453,           0,1); 	} function DA20168() { 	DA20951 = SPVAR_4; 	DA20952 = 0; 	DA20963 = 0; 	} int DA20952,  DA20951, DA20963, DA20953, DA20961; function DA20169(DA20170) { 	DA20953 = 0; 	while (DA20170) { 		DA20953++; 		DA20170 = abs(DA20170 >> 1); 	} 	return DA20953; 	} function DA20171(DA20172, DA20173) { 	DA20953 = max(DA20169(DA20172), DA20169(DA20173)); 	if (DA20174(DA20172, DA20173)) { 		DA20953++; 	} 	return DA20953; 	} function DA20174(DA20172, DA20173) { 	return DA20172 < 0 || DA20173 < 0; 	} function DA20177(DA20178) { 	return 1 << clamp(DA20178 - 1, 0, 31); 	} function DA20179(DA20178) { 	if (DA20178 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - DA20178); } function DA20181(DA20178) { 	return DA20179(DA20178 - 1); 	} function DA20183(DA20170, DA20178) { 	if (DA20170 < 0) { 		return (abs(DA20170) & DA20181(DA20178)) | DA20177(DA20178); 	} 	return DA20170 & DA20181(DA20178); } function DA20186(DA20170, DA20178) { 	if (DA20170 & DA20177(DA20178)) { 		return 0 - (DA20170 & DA20181(DA20178)); 	} 	return DA20170 & DA20181(DA20178); } function DA20189(DA20190) { 	return get_pvar(DA20190, 0x80000000, 0x7FFFFFFF, 0); 	} function DA20191(DA20170, min, max) { 	DA20961 = DA20171(min, max); 	DA20170 = clamp(DA20170, min, max); 	if (DA20174(min, max)) { 		DA20170 = DA20183(DA20170, DA20961); 	} 	DA20170 = DA20170 & DA20179(DA20961); 	if (DA20961 >= 32 - DA20952) { 		DA20963 = DA20963 | (DA20170 << DA20952); 		set_pvar(DA20951, DA20963); 		DA20951++; 		DA20961 -= (32 - DA20952); 		DA20170 = DA20170 >> (32 - DA20952); 		DA20952 = 0; 		DA20963 = 0; 	} 	DA20963 = DA20963 | (DA20170 << DA20952); 	DA20952 += DA20961; 	if (!DA20952) { 		DA20963 = 0; 	} 	set_pvar(DA20951, DA20963); } function DA20193(min, max, DA20194) { 	DA20961 = DA20171(min, max); 	DA20963 = (DA20189(DA20951) >> DA20952) & DA20179(DA20961); 	if (DA20961 >= 32 - DA20952) { 		DA20963 = (DA20963 & DA20179(32 - DA20952)) | ((DA20189(DA20951 + 1) & DA20179(DA20961 - (32 - DA20952))) << (32 - DA20952)); 	} 	DA20952 += DA20961; 	DA20963 = DA20963 & DA20179(DA20961); 	if (DA20952 >= 32) { 		DA20951++; 		DA20952 -= 32; 	} 	if (DA20174(min, max)) { 		DA20963 = DA20186(DA20963, DA20961); 	} 	if (DA20963 < min || DA20963 > max) { 		return DA20194; 	} 		if(DA20196[283] != 7585){ 	} 	return DA20963; 	} const string DA20972 = "SETTINGS"; const string DA20973 = "WAS SAVED"; combo DA2075 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	DA20167(); 	print(15, 2, OLED_FONT_MEDIUM, 1, DA20972[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, DA20973[0]); 	DA20974 = 1500; 	combo_run(DA2076); 	} int DA20974 = 1500; combo DA2076 { 	vm_tctrl(0);wait( DA20974); 	cls_oled(0); 	DA20338 = FALSE; 	} define DA20975 = 0; define DA20976 = 1; define DA20977 = 2; define DA20978 = 3; define DA20979 = 4; define DA20980 = 5; define DA20981 = 6; define DA20982 = 7; int DA20651; int DA20984; int DA20985, DA20644; int DA20459; int DA20988 = 50; int DA20989 = 200; int DA20990 = TRUE; combo DA2077 { 	set_val(DA20428, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA20428, 0); 	vm_tctrl(0);wait( 120); 	if (DA20407) DA20239(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int DA20590; int DA20597; combo DA2078 { 	if (DA20597) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (DA20597) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (DA20597) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(DA20427, 100); 	vm_tctrl(0);wait( DA20404); 	if (DA20597) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	DA20590 = FALSE; 	DA20223(DA20590); 	} int DA20599; int DA20600; combo DA2079 { 	if (DA20600) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (DA20600) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(DA20433, 100); 	set_val(DA20430, 100); 	set_val(DA20431, 100); 	DA201123 = 4000; 	vm_tctrl(0);wait( DA20405); 	vm_tctrl(0);wait( 50); 	set_val(DA20433, 100); 	vm_tctrl(0);wait( 50); 	DA20599 = FALSE; 	DA20223(DA20599); 	} int DA20995 = TRUE; function DA20195(DA20196) { 	if (DA20196) { 		DA20996 = DA201028; 			} 	else { 		DA20996 = DA201027; 			} 	combo_run(DA2080); 	} int DA20996; combo DA2080 { 	DA20216(DA20996); 	vm_tctrl(0);wait( 300); 	DA20216(DA201025); 	vm_tctrl(0);wait( 100); 	DA20216(DA20996); 	vm_tctrl(0);wait( 300); 	DA20216(DA201025); 	} define DA201000 = 100; define DA201001 = 130; const string DA20529 = "SCRIPT WAS"; function DA20197(DA20122, DA20199, DA20200) { 	if (!DA20332 && !DA20333) { 		cls_oled(0); 		print(DA20199, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA20200); 		if (DA20122) { 			print(DA20201(sizeof(DA201005) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA201005[0]); 		} 		else { 			print(DA20201(sizeof(DA201006) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA201006[0]); 		} 		DA20195(DA20122); 			} 	} function DA20201(DA20137, DA20131) { 	return (OLED_WIDTH / 2) - ((DA20137 * DA20131) / 2); 	} const string DA201006 = "OFF"; const string DA201005 = "ON"; function DA20204(DA20128, DA20206, DA20122) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(DA20128, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA20206); 	DA20211(DA20122, DA20214(DA20122)); 	DA20336 = TRUE; 	} const string DA20570 = "EA PING"; const string DA20592 = "FK_POWER"; const string DA20584 = "MaxFnshPwr"const string DA20576 = "JK_Agg"; int DA20566; int DA20583; function DA20208(DA20137, DA20131) { 	return (OLED_WIDTH / 2) - ((DA20137 * DA20131) / 2); 	} int DA201015; int DA201016, DA201017; function DA20211(DA20122, DA20154) { 	DA201015 = 1; 	DA201017 = 10000; 	if (DA20122 < 0) { 		putc_oled(DA201015, 45); 		DA201015 += 1; 		DA20122 = abs(DA20122); 			} 	for (DA201016 = 5; 	DA201016 >= 1; 	DA201016--) { 		if (DA20154 >= DA201016) { 			putc_oled(DA201015, (DA20122 / DA201017) + 48); 			DA20122 %= DA201017; 			DA201015++; 			if (DA201016 == 4) { 				putc_oled(DA201015, 44); 				DA201015++; 							} 					} 		DA201017 /= 10; 			} 	puts_oled(DA20208(DA201015 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA201015 - 1, OLED_WHITE); 	} int DA201021; function DA20214(DA20215) { 	DA201021 = 0; 	do { 		DA20215 /= 10; 		DA201021++; 			} 	while (DA20215); 	return DA201021; 	} int DA20606; define DA201025 = 0; define DA201026 = 1; define DA201027 = 2; define DA201028 = 3; define DA201029 = 4; define DA201030 = 5; define DA201031 = 6; define DA201032 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int DA201033; function DA20216(DA20217) { 	for (DA201033 = 0; 	DA201033 < 3; 	DA201033++) { 		set_rgb(data[DA20217][0], data[DA20217][1], data[DA20217][2]); 			} 	} const int8 DA201311[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int DA20609 = PS4_L3; define DA201035 = 1; define DA201036 = 2; define DA201037 = 3; define DA201038 = 2; define DA201039 = 3; define DA201040 = 4; define DA201041 = 5; define DA201042 = 6; define DA201043 = 7; define DA201044 = 8; define DA201045 = 9; int DA20603 = FALSE; int DA201047; int DA201048; int DA201049; int DA201050; define DA201051 = PS4_LX; define DA201052 = PS4_LY; define DA201053 = PS4_RX; define DA201054 = PS4_RY; function DA20218 () { 	if(!get_ival(XB1_RS) &&  !get_ival(DA20431) && !get_ival(DA20432) && !get_ival(DA20430)) { 		if( get_ival(PS4_RY) < -70  && !DA201047 && !combo_running(DA200) ) { 			DA201047 = TRUE; 			DA20459 = FALSE; 			DA20984 = DA20255; 			            DA20221(DA20255); 		} 		if( get_ival(PS4_RY) >  70  && !DA201048 && !combo_running(DA200)) { 			DA201048 = TRUE; 			DA20459 = TRUE; 			DA20984 = DA20257; 			           DA20221(DA20257); 		} 		if( get_ival(PS4_RX) < -70  && !DA201049 && !combo_running(DA200) ) { 			DA201049 = TRUE; 			DA20459 = FALSE; 			DA20984 = DA20258; 			              DA20221(DA20258); 		} 		if( get_ival(PS4_RX) >  70  && !DA201050 && !combo_running(DA200) ) { 			DA201050 = TRUE; 			DA20459 = TRUE; 			DA20984 = DA20256; 			            DA20221(DA20256); 		} 			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		DA201047 = 0; 		DA201048  = 0; 		DA201049  = 0; 		DA201050  = 0; 			} 			set_val(DA201053,0);              set_val(DA201054,0);  	} function DA20219() { 	if (DA201082 == DA20568) { 		DA20459 = FALSE; 		if (DA20366) DA20221(DA20366); 			} 	if (DA201082 == DA20226(DA20568 + 4)) { 		DA20459 = FALSE; 		if (DA20373) DA20221(DA20373); 			} 	if (DA201082 == DA20226(DA20568 + 1)) { 		DA20459 = TRUE; 		if (DA20368) DA20221(DA20368); 			} 	if (DA201082 == DA20226(DA20568 - 1)) { 		DA20459 = FALSE; 		if (DA20367) DA20221(DA20367); 			} 	if (DA201082 == DA20226(DA20568 + 2)) { 		DA20459 = TRUE; 		if (DA20370) DA20221(DA20370); 			} 	if (DA201082 == DA20226(DA20568 - 2)) { 		DA20459 = FALSE; 		if (DA20369) DA20221(DA20369); 			} 	if (DA201082 == DA20226(DA20568 + 3)) { 		DA20459 = TRUE; 		if (DA20372) DA20221(DA20372); 			} 	if (DA201082 == DA20226(DA20568 - 3)) { 		DA20459 = FALSE; 		if (DA20371) DA20221(DA20371); 			} 	} int DA201067; int DA20478 = 0; function DA20220() { 	if(DA201067){ 		DA20478 += get_rtime(); 			} 	if(DA20478 >= 3000){ 		DA20478 = 0; 		DA201067 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA20431) && !get_ival(DA20432) && !get_ival(DA20430) && !get_ival(DA20429)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA20480 && !combo_running(DA200)) { 			DA20480 = TRUE; 			DA201067 = TRUE; 			DA20478 = 0; 			DA201082 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA20219(); 					} 		set_val(DA201053, 0); 		set_val(DA201054, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		DA20480 = FALSE; 			} 	} function DA20221(DA20222) { 	DA20984 = DA20222; 	DA20196[-321 + (DA20222 * 3)] = TRUE; 	DA20990 = FALSE; 	block = TRUE; 	} int DA201073; combo DA2081 { 	set_rumble(DA201073, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function DA20223(DA20122) { 	if (DA20122) DA201073 = RUMBLE_A; 	else DA201073 = RUMBLE_B; 	combo_run(DA2081); 	} int DA201074 = 300; int DA201075 ; combo DA2082 { 	DA201075 = TRUE; 	vm_tctrl(0);wait( DA201074); 	DA201075 = FALSE; 	} combo DA2083 { 	DA20225(); 	DA20233(0, 0); 	vm_tctrl(0);wait( 20); 	DA20233(0, 0); 	vm_tctrl(0);wait( 100); 	DA20233(0, 0); 	set_val(DA20432, 100); 	DA20233(0, 0); 	vm_tctrl(0);wait( 60); 	DA20233(0, 0); 	vm_tctrl(0);wait( 150); 	DA20990 = TRUE; 	vm_tctrl(0);wait( 350); 	} function DA20225() { 	DA20651 = DA20568  DA20228(DA20651); 	DA20985 = DA201079; 	DA20644 = DA20646; 	} combo DA2084 { 	set_val(DA20431, 100); 	set_val(DA20430, 100); 	vm_tctrl(0);wait( 100); 	set_val(DA20431, 100); 	vm_tctrl(0);wait( 100); 	DA20990 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 DA201312[][] = { { 		0,    -100   	} 	,  	  { 		100,    -100   	} 	,  	  { 		100,    0   	} 	,  	  { 		100,    100   	} 	,  	  { 		0,    100   	} 	,  	  { 		-100,    100   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-100,    -100   	} } ; int DA201079, DA20646, DA20568; int DA201082; int DA20480; int DA201084; function DA20226(DA20227) { 	DA201084 = DA20227; 	if (DA201084 < 0) DA201084 = 8 - abs(DA20227); 	else if (DA201084 >= 8) DA201084 = DA20227 - 8  return DA201084; 	} function DA20228(DA20229) { 	if (DA20229 < 0) DA20229 = 8 - abs(DA20229); 	else if (DA20229 >= 8) DA20229 = DA20229 - 8; 	DA201079 = DA201312[DA20229][0]; 	DA20646 = DA201312[DA20229][1]; } function DA20230(DA20231, DA20232) { 	set_val(DA201053, DA20231); 	set_val(DA201054, DA20232); 	} function DA20233(DA20234, DA20235) { 	set_val(DA201051, DA20234); 	set_val(DA201052, DA20235); 	} function DA20236() { 	if (DA20459) { 		set_val(DA201051, inv(DA20644)); 		set_val(DA201052, DA20985); 			} 	else { 		set_val(DA201051, DA20644); 		set_val(DA201052, inv(DA20985)); 			} 	} function DA20237() { 	if (DA20459) { 		set_val(DA201053, inv(DA20644)); 		set_val(DA201054, DA20985); 			} 	else { 		set_val(DA201053, DA20644); 		set_val(DA201054, inv(DA20985)); 			} 	} function DA20238() { 	if (!DA20459) { 		set_val(DA201053, inv(DA20644)); 		set_val(DA201054, DA20985); 			} 	else { 		set_val(DA201053, DA20644); 		set_val(DA201054, inv(DA20985)); 			} 	} function DA20239() { 	set_val(DA201053, DA20985); 	set_val(DA201054, DA20644); 	} function DA20240() { 	set_val(DA201053, inv(DA20985)); 	set_val(DA201054, inv(DA20644)); 	} function DA20241() { 	set_val(DA201053, 0); 	set_val(DA201054, 0); 	} int DA201100; function DA20242() { 	if ((event_press(DA20428)  ) && !combo_running(DA2085) && (DA201123  <= 0 || (DA201123 < 3000 && DA201123 > 1  )) && !get_ival(DA20432) && DA20531 > 500 &&!get_ival(DA20431) &&!get_ival(DA20427) &&!get_ival(DA20430) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA2085) ) { 		combo_run(DA2085); 			} 	if (combo_running(DA2085) && (        get_ival(DA20432) ||        get_ival(DA20431) ||        get_ival(DA20427) ||        get_ival(DA20430) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA2085); 		DA201243 = TRUE; 			} 	} combo DA2085 { vm_tctrl(0);wait(750); set_val(DA20429,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(DA201100 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo DA2086 { 	vm_tctrl(0);wait( 800); 	DA201124 = 0; 	} int DA201101 = 1600; int DA201102 = 1600; int DA201103 = 1600; int DA201104 = TRUE; int DA201105 = TRUE; int DA20639 = FALSE; int DA201107 = TRUE; int DA20633 = FALSE; int DA201109 = TRUE; int DA20635 = FALSE; int DA201111 = TRUE; int DA20637 = FALSE; function DA20243(){ 	if (get_ival(DA20429)) { 		DA201113 = 1000; 		DA201132 = 0; 		DA20531 = 1; 		combo_stop(DA2095); 			} 	if (event_press(DA20431) || event_press(DA20414)) { 		DA201113 = 4000; 		DA201132 = 0; 		DA201101 = 1600; 			} 	if (get_ival(DA20430) && !get_ival(DA20429) ) { 		DA201113 = 0; 		DA201132 = 0; 		DA201101 = 1600; 			} 	else if (get_ival(DA20429)){ 		DA201113 = 1000; 			} 	if (DA201113 > 0) { 		DA201113 -= get_rtime(); 			} 	if (DA201113 < 0) { 		DA201113 = 0; 			} 	DA201190 = DA20408; 	if (event_release(DA20428)) { 		DA201119 = 1; 		DA201132 = 0; 		DA20531 = 1; 	} 	if (event_release(DA20434)) { 		DA201120 = 1; 		DA201132 = 0; 		DA20531 = 1; 	} 	if (event_release(DA20429)) { 		DA201102 = 1; 		DA201132 = 0; 		DA201101 = 1600; 			} 	if (event_release(DA20430)) { 		DA201103 = 1; 		DA201132 = 0; 		DA201101 = 1600; 			} 	if (event_release(DA20433) || (get_ival(DA20434) && get_ival(DA20429))) { 		DA201123 = 4000; 		DA201132 = 0; 	} 	if (get_ival(DA20428) && DA201123 < 4000 && DA201123 > 3500) { 		DA201124 = DA201123; 		DA201123 = 0; 			} 	if (DA201101 < 1510) { 		DA201101 += get_rtime(); 			} 	if (DA201102 < 1600) { 		DA201102 += get_rtime(); 			} 	if (DA201103 < 1600) { 		DA201103 += get_rtime(); 			} 	if (DA201123 > 0) { 		DA201123 -= get_rtime(); 			} 	if (DA201123 < 0) { 		DA201123 = 0; 			} 	if (DA201119 < 5100) { 		DA201119 += get_rtime(); 			} 	if (DA201120 < 4100) { 		DA201120 += get_rtime(); 			} 	if (DA201132 > 0) { 		DA201132 -= get_rtime(); 			} 	if (DA201132 < 0) { 		DA201132 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		DA201101 = 1; 		DA201132 = 0; 			} 	if (combo_running(DA2092)) { 		set_val(DA20428, 0); 		if(get_ival(DA20428)){ 			DA20643 = 0; 			combo_stop(DA2087); 			set_val(DA20428, 0); 			combo_stop(DA2092); 			combo_run(DA2049); 					} 			} 	if ((combo_running(DA2097) || combo_running(DA2088))) { 		set_val(DA20428, 0); 		if(get_ival(DA20428)){ 			DA20531 = 1; 			DA20643 = 0; 			combo_stop(DA2087); 			set_val(DA20428, 0); 			combo_stop(DA2097); 			combo_stop(DA2088); 			combo_run(DA2049); 					} 			} 	if (event_press(DA20427)) { 		combo_run(DA2086); 			} 	if (DA20531 > 1500) { 		if (DA201102 < 1500) { 			DA201137 = 120; 					} 		if (DA201103 < 1500) { 			DA201137 = 228; 					} 		else { 			DA201137 = 200; 					} 			} 	if (DA20531 < 1500) { 		DA201137 = 450; 			} 	if (DA20531 > 2700) { 		DA201141 = 920; 			} 	else if (DA20531 >= 0 && DA20531 < 2700) { 		DA201141 = 725; 			} 	} function DA20244() { 	if (DA201104) { 		if ((DA20531 <= 600 || (DA201101 <= 1500 && DA201101 > 1) || ( DA201102 <= 150 || DA201103 <= 150)) && event_press(DA20427) ) { 			if (!get_ival(DA20430) && !get_ival(DA20429) && !get_ival(DA20431) && !get_ival(DA20432)) { 				set_val(DA20427, 0); 				if (DA201123 < 4000 && DA201123 > 1) { 					set_val(DA20427, 0); 					combo_run(DA2090); 									} 				else { 					set_val(DA20427, 0); 					combo_run(DA2088); 					DA201132 = 9000; 				} 							} 					} 			} 	if (DA201111) { 		if (DA20531 > 1000 && !DA201132 && (!get_ival(DA20430) && !get_ival(PS4_L3) && event_press(DA20427)) &&  DA201102 > 150 &&  DA201103 > 150) { 			if (!get_ival(DA20429) && !get_ival(DA20431)) { 				set_val(DA20427, 0); 				if (((DA201120 > 1 && DA201120 <= 2500) || (DA201119 > 1 && DA201119 <= 3000)) &&  DA201101 != 1600) { 					set_val(DA20427, 0); 					combo_run(DA2089); 					DA201132 = 9000; 									} 				else if (((DA201120 > 2500 && DA201120 <= 4000) || (DA201119 > 3000 && DA201119 <= 3500))  &&  DA201101 != 1600) { 					set_val(DA20427, 0); 					combo_run(DA2088); 					DA201132 = 9000; 									} 				else if ((DA201123 < 4000 && DA201123 > 1)) { 					set_val(DA20427, 0); 					combo_run(DA2090); 					DA201132 = 9000; 									} 				else { 					set_val(DA20427, 0); 					DA20248(); 					DA201132 = 9000; 									} 				DA201132 = 9000; 							} 					} 			} 	if (DA201105) { 		if (get_ival(DA20429) && get_ival(DA20430)) { 			if (!get_ival(DA20431) && !get_ival(DA20432) && (DA201123 && DA201119 > 1 && DA201119 <= 1500) || (!DA201123 && DA201119 > 1 && DA201119 <= 1500) || (DA201119 > 1500 && !DA201123) && !DA201132) { 				if (event_press(DA20427)) { 					set_val(DA20427, 0); 					combo_run(DA2098); 					DA201132 = 9000; 									} 							} 					} 			} 	if (DA201109) { 		if (!get_ival(DA20432) && !get_ival(DA20429) && !get_ival(DA20430)) { 			if (get_ival(DA20431) && get_ival(DA20427)) { 				DA20249(); 				set_val(DA20427, 0); 				DA201132 = 9000; 							} 					} 			} 	if (DA201107) { 		if (get_ival(DA20430) && !get_ival(DA20429) && !DA201113) { 			if (!get_ival(DA20431) && !get_ival(DA20432) && !DA201132) { 				if (get_ival(DA20427) && DA20531 >= 1000) { 					set_val(DA20427, 0); 					combo_run(DA2095); 					DA201132 = 9000; 									} 				if (get_ival(DA20427) && DA20531 < 1000 && !DA201113) { 					set_val(DA20427, 0); 					combo_run(DA2096); 									} 							} 					} 			} 	if(combo_running(DA2090)){ 		DA20643 = 0; 		combo_stop(DA2087)   	} 	if (get_ival(DA20429) || DA201113 > 0) { 		combo_stop(DA2095); 		combo_stop(DA2097); 		combo_stop(DA2096); 			} 	if (combo_running(DA2088) || combo_running(DA2092) || combo_running(DA2097) || combo_running(DA2098) || combo_running(DA2095)) { 		if (get_ival(DA20428) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA20432) > 30) { 			combo_stop(DA2092); 			combo_stop(DA2097); 			combo_stop(DA2098); 			combo_stop(DA2095); 			combo_stop(DA2088); 			DA20643 = 0; 			combo_stop(DA2087)     		} 			} 	if (combo_running(DA2088) || combo_running(DA2089)) { 		if (get_ival(DA20428) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA20432)) { 			combo_stop(DA2090); 			combo_stop(DA2089); 			combo_stop(DA2088); 			DA20643 = 0; 			combo_stop(DA2087)     		} 			} 	if (event_press(DA20427) && DA201132 > 100 && DA201132 < 8990) { 		set_val(DA20427, 0); 		combo_stop(DA2092); 		combo_stop(DA2097); 		combo_stop(DA2098); 		combo_stop(DA2095); 		combo_stop(DA2088); 		DA20643 = 0; 		combo_stop(DA2087)    combo_run(DA2091); 			} 	if (!DA20639) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2098); 					} 			} 	if (!DA20633) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2095); 					} 			} 	if (!DA20635) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2092); 			DA20643 = 0; 			combo_stop(DA2087)     		} 			} 	if (!DA20637) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2097); 					} 			} 	if ((get_ival(DA20432) || get_ival(DA20428)) && !DA20345) { 		combo_stop(DA204); 		combo_stop(DA2047); 		combo_stop(DA2033); 			} 	} define DA201145 = 15; define DA201146 = 15; int DA201147 = 0; define DA201148 = 8000; define DA201149 = 4; define DA201150 = 2000; int DA20643 = 0; const int16 DA201313[] = { 	15, 20, 25 ,30,35    ,145,150 , 155, 160,165 ,    195, 200,205, 210,215,325  ,330, 335, 340,345 } ; const int16 DA201314[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int DA201152 = FALSE; int DA201153; int DA201154; int DA201155; int DA201156; function DA20245 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA201155 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA201152 = FALSE; 		for ( DA20921 = 0; 		DA20921 < sizeof(DA201314) / sizeof(DA201314[0]); 		DA20921++) { 			if (DA201155 == DA201314[DA20921]) { 				DA201152 = TRUE; 				break; 							} 					} 		if (!DA201152) { 			DA201153 = DA201314[0]; 			DA201154 = abs(DA201155 - DA201314[0]); 			for ( DA20921 = 1; 			DA20921 < sizeof(DA201314) / sizeof(DA201314[0]); 			DA20921++) { 				DA201156 = abs(DA201155 - DA201314[DA20921]); 				if (DA201156 < DA201154) { 					DA201153 = DA201314[DA20921]; 					DA201154 = DA201156; 									} 							} 			set_polar(POLAR_LS, DA201153, 32767); 					} 			} } function DA20246 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA201155 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA201152 = FALSE; 		for ( DA20921 = 0; 		DA20921 < sizeof(DA201313) / sizeof(DA201313[0]); 		DA20921++) { 			if (DA201155 == DA201313[DA20921]) { 				DA201152 = TRUE; 				break; 							} 					} 		if (!DA201152) { 			DA201153 = DA201313[0]; 			DA201154 = abs(DA201155 - DA201313[0]); 			for ( DA20921 = 1; 			DA20921 < sizeof(DA201313) / sizeof(DA201313[0]); 			DA20921++) { 				DA201156 = abs(DA201155 - DA201313[DA20921]); 				if (DA201156 < DA201154) { 					DA201153 = DA201313[DA20921]; 					DA201154 = DA201156; 									} 							} 			set_polar(POLAR_LS, DA201153, 32767); 					} 			} } int DA201167; function DA20247() { 	if (combo_running(DA2087) && (        get_ival(DA20432) ||        get_ival(DA20427) ||        get_ival(DA20428) ||        get_ival(DA20433) ||        get_ival(DA20434) ||        get_ival(DA20429)      )) { 		combo_stop(DA2087); 		DA20643 = 0; 			} 	if (DA201147 == 0) { 		if ( ( DA201123 == 0 && !combo_running(DA2090) && !combo_running(DA2098) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( event_press(DA20427) || (DA20643 == 1 ||     combo_running(DA2096) || combo_running(DA2094)|| combo_running(DA2092) ||  combo_running(DA2095)     || combo_running(DA2088) || combo_running(DA2097) || combo_running(DA2089) ||  combo_running(DA2091)  ))     ) { 			if(DA20417)DA20246 (); 			else if (DA20418)DA20245 (); 			combo_restart(DA2087); 					} 			} 	else{ 		combo_stop(DA2087); 			} 	} combo DA2087 { 	if(DA20417)DA20246 (); 	else if (DA20418)DA20245 (); 	vm_tctrl(0);wait( 4000); 	DA20643 = 0; 	} combo DA2088 { 	set_val(DA20429,0); 	set_val(DA20427, 100); 	vm_tctrl(0);wait( random(210, 215) + DA20410); 	set_val(DA20427, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function DA20248() { 	if (DA20531 > 600 && DA20531 <= 800) { 		DA201168 = 240; 			} 	if (DA20531 > 800 && DA20531 <= 1000) { 		DA201168 = 230; 			} 	if (DA20531 > 1000 && DA20531 <= 1500) { 		DA201168 = 225; 			} 	if (DA20531 > 1500 && DA20531 <= 2000) { 		DA201168 = 235; 			} 	if (DA20531 > 2000) { 		DA201168 = 218; 			} 	combo_run(DA2097); 	} combo DA2089 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 500); 	} combo DA2090 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait( 205); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 300); 	} combo DA2091 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait( 190); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 400); 	} int DA201173; int DA201174; int DA2029; int DA20455; int DA20453; int DA201178; int DA201179; combo DA2092 { 	if (DA201174) { 		set_val(DA20427, 0); 		DA201178 = 350; 			} 	else { 		DA201178 = 0; 			} 	if (DA201174) { 		DA20237(); 		DA20233(0, 0); 			} 	vm_tctrl(0); 	wait(DA201178); 	if (DA201174) { 		set_val(DA20430, 0); 		DA201178 = 60; 			} 	else { 		set_val(DA20431, 0); 		DA201178 = 60; 			} 	set_val(DA20427,0); 	vm_tctrl(0);wait(DA201178); 	set_val(DA20431, 0); 	set_val(DA20430, 0); 	set_val(DA20427,0); 	vm_tctrl(0);wait(DA201178); 	if (DA201174) { 		DA201178 = 0; 			} 	else { 		DA201178 = 60; 			} 	set_val(DA20430, 0); 	set_val(DA20431, 0); 	set_val(DA20427,0); 	vm_tctrl(0);wait(DA201178); 	set_val(DA20427, 100); 	set_val(DA20431, 100); 	vm_tctrl(0);wait(random(265, 268) +   DA20409 ); 	set_val(DA20431, 100); 	set_val(DA20427, 0); 	if (DA201174) { 		DA201178 = 15; 			} 	else { 		DA201178 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + DA201191 + DA201190 + DA201178 ); 	set_val(DA20431, 100); 	set_val(DA20427, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA20427, 0); 	set_val(DA20431, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(DA20431, 100); 	vm_tctrl(0);wait(2500); 	} int DA201120; int DA201123; int DA20531; int DA201119; int DA201190; int DA201191 = 111; int DA201113; int DA201124; int DA201194; function DA20249() { 	DA201194 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DA201194 > 5 && DA201194 < 40) ) {         DA20459 = FALSE; 			} 	if ((DA201194 > 40 && DA201194 < 85)){ 		DA20459 = TRUE; 		    } 	if ((DA201194 > 95 && DA201194 < 130) ) { 		DA20459 = FALSE; 		    } 	 if((DA201194 > 130 && DA201194 < 175)) { 		DA20459 = TRUE; 		} 			if ((DA201194 > 185 && DA201194 < 220) ) {        DA20459 = FALSE; 			} 	if ((DA201194 > 220 && DA201194 < 265)){        DA20459 = TRUE; 		    } 	if ((DA201194 > 275 && DA201194 < 310) ) {        DA20459 = FALSE; 		    } 	 if((DA201194 > 310 && DA201194 < 355)) { 		DA20459 = TRUE; 		} 	if (DA201123 == 0 && (DA20531 >= 750 || ((DA201124 > 3000 && DA201119 > 1 && DA201119 < 5000)))) { 		if (DA20531 <= 2000 && DA201119 > 1500) { 			set_val(DA20427, 0); 			DA201191 = 170; 		} 		if (DA20531 <= 2000 && DA201119 > 1 && DA201119 <= 1500) { 			set_val(DA20427, 0); 			DA201191 = 202; 					} 		if (DA20531 > 2000 || (DA201119 > 1 && DA201119 <= 1500)) { 			set_val(DA20427, 0); 			DA201191 = 151; 					} 		if ((DA20531 > 2000 && DA201119 > 1500) || DA201124 > 1 && DA201119 > 1) { 			set_val(DA20427, 0); 			DA201191 = 152; 					} 		if ((DA20531 < 2000 && DA201119 > 1500) || DA201123 > 1 && DA201119 > 1) { 			set_val(DA20427, 0); 			DA201191 = 149; 					} 		if (DA201119 > 1500) { 			set_val(DA20427, 0); 			DA201191 = 148; 					} 		if (!DA20531 > 2000 && DA201124 > 1 && DA201119 > 1 && DA201119 <= 1500) { 			set_val(DA20427, 0); 			DA201191 = 147; 					} 		set_val(DA20427, 0); 		combo_stop(DA2097); 		combo_stop(DA2098); 		combo_stop(DA2095); 		combo_stop(DA2088); 		combo_stop(DA2094); 		combo_stop(DA2091); 		combo_stop(DA2090); 		combo_run(DA2092); 			} 	else { 		if (DA201123) { 			set_val(DA20427, 0); 			combo_run(DA2093); 					} 		else { 			if (DA20531 < 750) { 				set_val(DA20427, 0); 				combo_run(DA2094); 							} 					} 			} } combo DA2093 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(DA20427, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo DA2094 { 	set_val(DA20427, 100); 	vm_tctrl(0);wait( random(200, 205) + DA20409 )  set_val(DA20427, 0); 	vm_tctrl(0);wait( 700); 	} int DA201204 = 246; int DA201137 = 150; int DA201206 = 0; combo DA2095 { 	set_val(DA20431, 100); 	set_val(DA20430, 0); 	set_val(DA20427,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA20431, 0); 	set_val(DA20430, 0); 	set_val(DA20427,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	DA201206 = DA20408; 	set_val(DA20430, 100); 	set_val(DA20427,0); 	vm_tctrl(0);wait( 60); 	set_val(DA20430, 100); 	set_val(DA20427, 100); 	vm_tctrl(0);wait( DA201204 + 10 + random(-2, 2) +  DA20412); 	set_val(DA20430, 100); 	set_val(DA20427, 0); 	DA201173 = DA201137; 	vm_tctrl(0);wait( DA201137 + DA201206 - 58 + DA20437 ); 	set_val(DA20430, 100); 	if(DA201167)set_val(DA20427, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA20430, 100); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 3000); 	} combo DA2096 { 	set_val(DA20430, 100); 	set_val(DA20427, 100); 	vm_tctrl(0);wait( 160 + DA20412 ); 	set_val(DA20430, 100); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 3000); 	} int DA201132; int DA201208 = 220; int DA201168; int DA201210 = 0; combo DA2097 { 	DA201210 = DA20408; 	set_val(DA20427, 100); 	vm_tctrl(0);wait( DA201208); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( DA201168 + (DA201210) + 22 + DA20438); 	if(DA201167)set_val(DA20427, 100); 	vm_tctrl(0);wait( DA201208); 	set_val(DA20427, 0); 	vm_tctrl(0);wait( 2000); 	} int DA201212 = TRUE; int DA201141; int DA201214 = 260; int DA201215 = 0; combo DA2098 { 	set_val(DA20429, 100); 	set_val(DA20430, 100); 	if (DA201212) { 		DA201215 = DA20408; 			} 	else { 		DA201215 = 0   	} 	set_val(DA20427, 100); 	vm_tctrl(0);wait( DA201214); 	vm_tctrl(0);wait( DA201141 + DA201215 + 40)  } int DA201218; int DA201219 = 145; int DA20626; int DA20625; int DA20628; int DA20627; combo DA2099 { 	set_val(DA20434, 0); 	vm_tctrl(0);wait( 30); 	set_val(DA20434, 100); 	vm_tctrl(0);wait( 60); 	} int DA20629; int DA20630; combo DA20100 { 	set_val(DA20433, 100); 	vm_tctrl(0);wait( DA20630); 	} define DA201226 = TRUE; define DA201227 = 95; define DA201228 = 10; define DA201229 = 70; define DA201230 = FALSE; define DA201231 = 50; define DA201232 = 95; define DA201233 = XB1_LT; define DA201234 = XB1_RT; define DA201235 = XB1_LX; define DA201236 = XB1_LY; define DA201237 = POLAR_LS; int DA201238; function DA20250() { 	if (    get_ival(DA20432) > 30 &&    (get_ival(DA20431) || get_ival(DA20428)) &&    (!get_ival(DA20433) || !get_ival(DA20427))  ) { set_val(DA20432, 0); 		if(!get_ival(DA20427)){ 			set_val(DA20431,100); 					} 		else{ 			set_val(DA20431,0); 					} 		  combo_run(DA20102); 		if(DA20411 == TRUE){ 			combo_run(DA20101); 					} 			} 	else { 		combo_stop(DA20102); 		combo_stop(DA20101); 			} 	} combo DA20101 { 	if (DA20411 == TRUE) { 		set_val(DA20430, 100); 		DA201239 = 60; 			} 	else { 		DA201239 = 0; 			} 	set_val(DA20431, 0); 	vm_tctrl(0);wait( DA201239); 	if (DA20411 == TRUE) { 		set_val(DA20430, 0); 		DA201239 = 60; 			} 	else { 		DA201239 = 0; 			} 	set_val(DA20431, 0); 	vm_tctrl(0);wait( DA201239); 	if (DA20411 == TRUE) { 		set_val(DA20430, 100); 			} 	vm_tctrl(0);wait( 750); 	vm_tctrl(0);wait( 750); 	} combo DA20102 { 	if(!get_ival(DA20427)){ 		set_val(DA20431,100); 			} 	else{ 		set_val(DA20431,0); 			} 	set_val(DA20432, 100); 	vm_tctrl(0);wait(DA20566); 	if(!get_ival(DA20427)){ 		set_val(DA20431,100); 			} 	else{ 		set_val(DA20431,0); 			}     set_val(DA20432, 0); 	vm_tctrl(0);wait(500); 	} int DA201241; int DA201239 ; int DA201243 = TRUE; int DA201244; int DA201245; int DA201246; int DA201247; int DA201248; function DA20251() { 			if((DA201246 >= DA20435) || get_ival(XB1_LS) || get_ival(XB1_RS) || get_ival(DA20430) || get_ival(DA20429)  || get_ival(DA20431) ||       get_ival(DA20428) || get_ival(DA20434) || get_ival(DA20427) || get_ival(DA20433)   || get_ival(XB1_PR1) ||      get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(DA201053))> 45 || abs(get_ival(DA201054))> 45))){ 				if(!get_ival(DA20432))DA20114(POLAR_LS, DA20118(POLAR_LS,POLAR_ANGLE), DA20118(POLAR_LS, POLAR_RADIUS)); 							} 	if( !get_ival(DA20431) && !get_ival(DA20432) && !get_ival(DA20429) && !combo_running(DA20102) ){ 		if (DA20118(POLAR_LS, POLAR_RADIUS) > 1800) { 			DA201246 += get_rtime(); 			if( (DA201246 > 2500) ) DA201246 = 0; 			if((DA201246 <  DA20435) && DA20531 > 2000){ 			       sensitivity(PS4_LX, 60, 90); 			       sensitivity(PS4_LY, 60, 90); 				} 					} 			} } combo DA20103 { 	set_val(DA20428,100); 	vm_tctrl(0);wait( DA20626); 	set_val(DA20428,  0); 	vm_tctrl(0);wait( 30); 	if(DA20381){ 		set_val(DA20428,100); 			} 	vm_tctrl(0);wait( 60); 	} combo DA20104 { 	set_val(DA20428,  0); 	vm_tctrl(0);wait( 30); 	set_val(DA20428,100); 	vm_tctrl(0);wait( 60); 	} combo DA20105 { 	set_val(DA20434,100); 	vm_tctrl(0);wait( DA20628); 	set_val(DA20434,  0); 	vm_tctrl(0);wait( 30); 	if(DA20378){ 		set_val(DA20434,100); 			} 	vm_tctrl(0);wait( 60); 	} int DA20944 int DA201253 combo DA20106 { 	combo_suspend(DA20106) 	vm_tctrl(0);wait(361) } function DA20252 (){ 	if(DA20944[DA201253] != 361){ 	    DA201253-- 	} 	else{ 		if(inv(DA201253) != 285){ 			DA20252(); 		} 	} } int DA201256;  