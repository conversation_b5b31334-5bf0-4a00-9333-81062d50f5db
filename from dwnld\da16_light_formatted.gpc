
/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																/*| This Script was made and intended for Dark-Angel vip discord members (Dark_Angel_FC_24)   .     | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																💻 most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																💻 My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																
																Your contributions have been invaluable, and I am truly grateful for your support."
																
																
																
																
															⚽💻 "IF YOU'RE INTERESTED IN DIVING INTO FIFA/FC GPC CODING IN DEPTH,  CHECK "FIFA CODE ACADEMY" there." ⚽💻 : https://discord.gg/CpcFqAvP 
																*/



































































 int DLA190[0];
 init {
 	DLA107();
 	combo_run(DLA1);
 	combo_run(DLA2);
 	combo_run(DLA3);
 	combo_run(DLA4);
 	combo_run(DLA5);
 	combo_run(DLA6);
 	combo_run(DLA7);
 	combo_run(DLA8);
 	combo_run(DLA9);
 	combo_run(DLA10);
 	combo_run(DLA11);
 	combo_run(DLA12);
 	combo_run(DLA13);
 	combo_run(DLA14);
 	combo_run(DLA15);
 	combo_run(DLA16);
 	combo_run(DLA17);
 	combo_run(DLA18);
 	combo_run(DLA19);
 	combo_run(DLA20);
 	combo_run(DLA21);
 	combo_run(DLA22);
 	combo_run(DLA23);
 	combo_run(DLA24);
 	combo_run(DLA25);
 	combo_run(DLA26);
 	combo_run(DLA27);
 	combo_run(DLA28);
 	combo_run(DLA29);
 	combo_run(DLA30);
 	combo_run(DLA31);
 	combo_run(DLA32);
 	combo_run(DLA33);
 	combo_run(DLA34);
 	combo_run(DLA35);
 	combo_run(DLA36);
 	combo_run(DLA37);
 	combo_run(DLA38);
 	combo_run(DLA39);
 	combo_run(DLA40);
 	combo_run(DLA41);
 	combo_run(DLA42);
 	combo_run(DLA43);
 	combo_run(DLA44);
 	combo_run(DLA45);
 	combo_run(DLA46);
 	combo_run(DLA47);
 	combo_run(DLA48);
 	combo_run(DLA49);
 	combo_run(DLA50);
 	combo_run(DLA51);
 	combo_run(DLA52);
 	combo_run(DLA53);
 	combo_run(DLA54);
 	combo_run(DLA55);
 	combo_run(DLA56);
 	combo_run(DLA57);
 	combo_run(DLA58);
 	combo_run(DLA59);
 	combo_run(DLA60);
 	combo_run(DLA61);
 	combo_run(DLA62);
 	combo_run(DLA63);
 	combo_run(DLA64);
 	combo_run(DLA65);
 	combo_run(DLA66);
 	combo_run(DLA67);
 	combo_run(DLA68);
 	combo_run(DLA69);
 	combo_run(DLA70);
 	combo_stop(DLA1);
 	combo_stop(DLA2);
 	combo_stop(DLA3);
 	combo_stop(DLA4);
 	combo_stop(DLA5);
 	combo_stop(DLA6);
 	combo_stop(DLA7);
 	combo_stop(DLA8);
 	combo_stop(DLA9);
 	combo_stop(DLA10);
 	combo_stop(DLA11);
 	combo_stop(DLA12);
 	combo_stop(DLA13);
 	combo_stop(DLA14);
 	combo_stop(DLA15);
 	combo_stop(DLA16);
 	combo_stop(DLA17);
 	combo_stop(DLA18);
 	combo_stop(DLA19);
 	combo_stop(DLA20);
 	combo_stop(DLA21);
 	combo_stop(DLA22);
 	combo_stop(DLA23);
 	combo_stop(DLA24);
 	combo_stop(DLA25);
 	combo_stop(DLA26);
 	combo_stop(DLA27);
 	combo_stop(DLA28);
 	combo_stop(DLA29);
 	combo_stop(DLA30);
 	combo_stop(DLA31);
 	combo_stop(DLA32);
 	combo_stop(DLA33);
 	combo_stop(DLA34);
 	combo_stop(DLA35);
 	combo_stop(DLA36);
 	combo_stop(DLA37);
 	combo_stop(DLA38);
 	combo_stop(DLA39);
 	combo_stop(DLA40);
 	combo_stop(DLA41);
 	combo_stop(DLA42);
 	combo_stop(DLA43);
 	combo_stop(DLA44);
 	combo_stop(DLA45);
 	combo_stop(DLA46);
 	combo_stop(DLA47);
 	combo_stop(DLA48);
 	combo_stop(DLA49);
 	combo_stop(DLA50);
 	combo_stop(DLA51);
 	combo_stop(DLA52);
 	combo_stop(DLA53);
 	combo_stop(DLA54);
 	combo_stop(DLA55);
 	combo_stop(DLA56);
 	combo_stop(DLA57);
 	combo_stop(DLA58);
 	combo_stop(DLA59);
 	combo_stop(DLA60);
 	combo_stop(DLA61);
 	combo_stop(DLA62);
 	combo_stop(DLA63);
 	combo_stop(DLA64);
 	combo_stop(DLA65);
 	combo_stop(DLA66);
 	combo_stop(DLA67);
 	combo_stop(DLA68);
 	combo_stop(DLA69);
 	combo_stop(DLA70);
 	combo_run(DLA100);
 }
 define DLA244 = 0;
 define DLA245 = 1;
 define DLA246 = 2;
 define DLA247 = 3;
 define DLA248 = 4;
 define DLA249 = 5;
 define DLA250 = 6;
 define DLA251 = 7;
 define DLA252 = 8;
 define DLA253 = 9;
 define DLA254 = 10;
 define DLA255 = 11;
 define DLA256 = 12;
 define DLA257 = 13;
 define DLA258 = 14;
 define DLA259 = 15;
 define DLA260 = 16;
 define DLA261 = 17;
 define DLA262 = 18;
 define DLA263 = 19;
 define DLA264 = 20;
 define DLA265 = 21;
 define DLA266 = 22;
 define DLA23 = 23;
 define DLA268 = 24;
 define DLA269 = 25;
 define DLA270 = 26;
 define DLA271 = 27;
 define DLA272 = 28;
 define DLA273 = 29;
 define DLA274 = 30;
 define DLA275 = 31;
 define DLA276 = 32;
 define DLA277 = 33;
 define DLA278 = 34;
 define DLA279 = 35;
 define DLA280 = 36;
 define DLA281 = 37;
 define DLA282 = 38;
 define DLA283 = 39;
 define DLA284 = 40;
 define DLA285 = 41;
 define DLA286 = 42;
 define DLA287 = 43;
 define DLA288 = 44;
 define DLA289 = 45;
 define DLA290 = 46;
 define DLA291 = 47;
 define DLA292 = 48;
 define DLA293 = 49;
 define DLA294 = 50;
 define DLA295 = 51;
 define DLA296 = 52;
 define DLA297 = 53;
 define DLA298 = 54;
 define DLA299 = 55;
 define DLA300 = 56;
 define DLA301 = 57;
 define DLA302 = 58;
 define DLA303 = 59;
 define DLA304 = 60;
 define DLA305 = 61;
 define DLA306 = 62;
 define DLA307 = 63;
 define DLA308 = 64;
 define DLA309 = 65;
 define DLA310 = 66;
 define DLA311 = 67;
 define DLA312 = 68;
 define DLA313 = 69;
 define DLA314 = 70;
 define DLA315 = 0;
 function DLA101(DLA102) {
 	if (DLA102 == 0) vm_tctrl(-0);
 	else if (DLA102 == 1) vm_tctrl(2);
 	else if (DLA102 == 2) vm_tctrl(-2);
 	else if (DLA102 == 3) vm_tctrl(-4);
 	else if (DLA102 == 4) vm_tctrl(-6);
 	else if (DLA102 == 5) vm_tctrl(-8);
 	else if (DLA102 == 6) vm_tctrl(-9);
 }
 int DLA316, DLA317;
 int DLA318, DLA319;
 int DLA320 = FALSE, DLA321;
 int DLA322 = TRUE;
 int DLA323;
 const string DLA712[] = {
 	"Off",  "On" }
 ;
 int DLA324;
 int DLA325;
 int DLA326;
 int DLA327;
 int DLA328;
 int DLA329;
 int DLA330;
 int DLA331;
 int DLA332;
 int DLA333;
 int DLA334;
 int DLA335;
 int DLA336;
 int DLA337;
 int DLA338;
 int DLA339;
 int DLA102;
 int DLA341;
 define DLA342 = 17;
 int DLA343;
 int DLA344;
 int DLA345;
 int DLA346;
 int DLA347;
 int DLA348;
 int DLA349;
 int DLA350;
 int DLA351;
 int DLA352;
 int DLA353;
 int DLA354;
 int DLA355;
 int DLA356;
 int DLA357;
 int DLA358;
 int DLA359;
 int DLA360;
 int DLA361;
 int DLA362;
 int DLA363;
 int DLA364;
 int DLA365;
 int DLA366;
 int DLA367;
 int DLA368;
 int DLA369;
 int DLA370;
 int DLA371;
 int DLA372;
 int DLA373;
 int DLA374 ;
 int DLA375 ;
 int DLA376 ;
 int DLA377;
 int DLA378 ;
 int DLA379 ;
 int DLA380 ;
 int DLA381;
 int DLA382 ;
 int DLA383 ;
 int DLA384 ;
 int DLA385;
 int DLA386 ;
 int DLA387 ;
 int DLA388 ;
 int DLA389;
 int DLA390;
 int DLA391;
 int DLA392;
 int DLA393;
 const int16 DLA718[][] = {
 {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 9, 1, 10, 2 	}
 	,       {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,   {
 		0, 70, 1, 10, 16 	}
 	,    {
 		1, 25, 1, 10, 6 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		0, 1, 1, 10, 20 	}
 	,     {
 		1, 25, 1, 10, 8 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		0, 1, 1, 10, 20 	}
 	,     {
 		0, 25, 1, 10, 7 	}
 	,     {
 		0, 1, 1, 10, 21 	}
 	,     {
 		0, 1, 1, 10, 19 	}
 	,     {
 		1, 25, 1, 10, 9 	}
 	,     {
 		0, 1, 1, 10, 28 	}
 	,     {
 		0, 1, 1, 10, 29 	}
 	,     {
 		1, 800, 1, 10, 0 	}
 	,    {
 		1, 800, 1, 10, 0 	}
 	,    {
 		0, 22, 1, 10, 13 	}
 	,    {
 		0, 1, 1, 10, 33 	}
 	,     {
 		-100, 300, 1, 10, 1 	}
 	,  {
 		-150, 150, 10, 10, 0 	}
 	, {
 		-150, 150, 10, 10, 0 	}
 	, {
 		0, 1, 1, 10, 37 	}
 	,      {
 		-150, 150, 10, 10, 0 	}
 	, {
 		0, 22, 1, 10, 49 	}
 	,     {
 		0, 22, 1, 10, 50 	}
 	,     {
 		0, 22, 1, 10, 51 	}
 	,     {
 		0, 22, 1, 10, 52 	}
 	,    {
 		0, 1, 1, 10, 1 	}
 	,       {
 		0, 1, 1, 10, 1 	}
 	,       {
 		-2500,2500,25,10,0 	}
 	,   {
 		0, 1, 1, 10, 1 	}
 }
 ;
 const int16 DLA483[][] = {
 {
 		0, 7, 1 	}
 	,   	    {
 		41,41,1 	}
 	,    	    {
 		8, 12, 1 	}
 	,   	    {
 		13, 15, 1 	}
 	,   	    {
 		16, 18, 1 	}
 	,   	    {
 		19, 21, 1 	}
 	,   	    {
 		22, 24, 1 	}
 	,   	    {
 		25, 25, 1 	}
 	,   	    {
 		26, 26, 1 	}
 	,   	    {
 		27, 28, 1 	}
 	,   	    {
 		29, 29, 1 	}
 	,   	    {
 		0,  0, 0 	}
 	,   	    {
 		38, 39, 1 	}
 	,   	    {
 		30, 33, 1 	}
 	,   {
 		34, 37, 1 	}
 	,  {
 		0, 0, 0 	}
 	,    {
 		0, 0, 0 	}
 	,    {
 		40, 40, 1 	}
 }
 ;
 const uint8 DLA694[] = {
 	2,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1   }
 ;
 const string DLA493[] = {
 	"Button Layout",  "RS 4* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",   "Jockey_Support",  "VM_Speed",  "E-Movements", "" }
 ;
 const string DLA492[] = {
 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS"  ,"Trivela Roll",  "Timed", "EM_FACTOR" ,"BALL_ROLL","" }
 ;
 const string DLA698 [] = {
 	"Classic","Alternative","Custom", ""  }
 ;
 const string DLA765 [] = {
 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  }
 ;
 const string DLA710[] = {
 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" }
 ;
 const string DLA735[] = {
 	"Right",  "Left",  "" }
 ;
 const string DLA733[] = {
 	"One Tap",  "Double Tap",  "" }
 ;
 const string DLA700[] = {
 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" }
 ;
 const string DLA702[] = {
 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"Feint && Exit",  	"Feint & Exit",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Ball Roll Chop",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel to Ball",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"Spin Move L/R",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Drag Back Som.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" }
 ;
 const string DLA731[] = {
 	"OFF",  "PS5_PS",  "PS5_SHARE",  "PS5_OPTIONS",  "PS5_R1",  "PS5_R2",  "PS5_R3",  "PS5_L1",  "PS5_L2",  "PS5_L3",  "PS5_UP",  "PS5_DOWN",  "PS5_LEFT",  "PS5_RIGHT",  "PS5_TRIANGLE",  "PS5_CIRCLE",  "PS5_CROSS",  "PS5_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS5_TOUCH",  "" }
 ;
 const uint8 DLA1135[] = {
 	4,4,4, 4,4,4, 4,4,4,4 }
 ;
 const uint8 DLA1136[] = {
 	41, 15, 4, 41, 15, 41, 41, 41, 4, 15,29 }
 ;
 const uint8 DLA1137[] = {
 	15, 4, 41, 41, 15, 41, 41, 4, 41, 15,29  }
 ;
 const uint8 DLA1138[] = {
 	15,29,70,41,70,15,29,70,41,15,29  }
 ;
 const uint8 DLA1139[] = {
 	29,41,15,29,70,15,70,41,70,15 ,29  }
 ;
 const uint8 DLA1140[] = {
 	29, 44, 44, 44, 29, 44, 44, 44,44,44,29 }
 ;
 const uint8 DLA1141[] = {
 	29, 44, 44, 44, 29, 44, 44, 44,44,44 ,29  }
 ;
 const uint8 DLA1142[] = {
 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21 }
 ;
 function DLA103(DLA104) {
 	if (DLA104 >= 9) {
 		DLA394 = -1;
 			}
 	else if (DLA104 <= 0) {
 		DLA394 = 1;
 			}
 	DLA104 += DLA394;
 	return DLA104;
 	}
 function DLA105() {
 	vm_tctrl(0);
 	if(DLA29 && DLA325){
 		if(DLA459 < 1000){
 			DLA396 = 10;
 			DLA410   = 10;
 			DLA408  = 10;
 					}
 			}
 	if(DLA459 >= 1000){
 		DLA397 = DLA103(DLA397);
 		DLA407 = DLA103(DLA407);
 		DLA408 = DLA103(DLA408);
 		DLA396 = DLA103(DLA396);
 		DLA410 = DLA103(DLA410);
 			}
 	if(DLA325){
 		if(DLA413 == DLA499){
 			DLA398 = !DLA398;
 			if(DLA1135[DLA397]) DLA212(DLA1135[DLA397]);
 					}
 		if(DLA413 == DLA217 (DLA499 + 4)){
 			DLA398 = FALSE;
 			if(DLA1142[DLA407]) DLA212(DLA1142[DLA407]);
 					}
 		if(DLA413 == DLA217 (DLA499 + 1) ){
 			DLA398 = TRUE;
 			if(DLA1137[DLA396]) DLA212(DLA1137[DLA396]);
 					}
 		if(DLA413 == DLA217 (DLA499 - 1) ){
 			DLA398 = FALSE;
 			if(DLA1136[DLA396]) DLA212(DLA1136[DLA396]);
 					}
 		if(DLA413 == DLA217 (DLA499 + 2) ){
 			DLA398 = TRUE;
 			if(DLA1139[DLA410]) DLA212(DLA1139[DLA410]);
 					}
 		if(DLA413 == DLA217 (DLA499 - 2) ){
 			DLA398 = FALSE;
 			if(DLA1138[DLA410]) DLA212(DLA1138[DLA410]);
 					}
 		if(DLA413 == DLA217 (DLA499 + 3) ){
 			DLA398 = TRUE;
 			if(DLA1140[DLA408]) DLA212(DLA1140[DLA408]);
 					}
 		if(DLA413 == DLA217 (DLA499 - 3) ){
 			DLA398 = FALSE;
 			if(DLA1141[DLA408]) DLA212(DLA1141[DLA408]);
 					}
 			}
 }
 int DLA397;
 int DLA407;
 int DLA408;
 int DLA396;
 int DLA410;
 function DLA106() {
 	if(DLA969){
 		DLA411 += get_rtime();
 			}
 	if(DLA411 >= 3000){
 		DLA411 = 0;
 		DLA969 = FALSE;
 			}
 	if (!get_ival(XB1_RS) && !get_ival(DLA386) && !get_ival(DLA387) && !get_ival(DLA385) && !get_ival(DLA384)) {
 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DLA414 && !combo_running(DLA0)) {
 			DLA413 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
 			DLA414 = TRUE;
 			DLA969 = TRUE;
 			DLA411 = 0;
 			vm_tctrl(0);
 			DLA105();
 					}
 		set_val(DLA967, 0);
 		set_val(DLA968, 0);
 			}
 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
 		DLA414 = FALSE;
 			}
 	}
 function DLA107() {
 	DLA160();
 	DLA804 = get_slot();
 	}
 int DLA394 = 1;
 int DLA417;
 int DLA418;
 int DLA419 = TRUE;
 int DLA420[6];
 int DLA421;
 int DLA422;
 int DLA423;
 int DLA424;
 function DLA108(DLA109, DLA110, DLA111) {
 	DLA111 = (DLA111 * 14142) / 46340;
 	if (DLA110 <= 0) {
 		set_polar2(DLA109, (DLA110 = (abs(DLA110) + 360) % 360), min(DLA111, DLA428[DLA110 % 90]));
 		return;
 			}
 	set_polar2(DLA109, inv(DLA110 % 360), min(DLA111, DLA428[DLA110 % 90]));
 	}
 function DLA112(DLA109,DLA114) {
 	if (DLA114) return (360 - get_ipolar(DLA109, POLAR_ANGLE)) % 360;
 	return isqrt(~(pow(get_ival(42 + DLA109), 2) + pow(get_ival(43 + DLA109), 2))) + 1;
 	}
 const int16 DLA428[] = {
 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  }
 ;
 int block = FALSE;
 int DLA431 = 1;
 combo DLA0{
 	set_polar(POLAR_RS,0,0);
 	wait(100);
 	wait(300);
 	}
 main{
 	DLA241();
 	if (DLA1118) vm_tctrl(0);
     else DLA101(DLA102);
 	if(DLA341){
 		DLA239();
 	}
 	if(!DLA418){
 		DLA418 = TRUE;
 		DLA417 = random(11111, 99999);
 		set_pvar(SPVAR_1,DLA418);
 		set_pvar(SPVAR_3,DLA417);
 		DLA419 = TRUE;
 			}
 	if(!DLA424){
 		vm_tctrl(0);
 		 		if(event_press(PS5_LEFT)){
 			DLA423 = DLA117(DLA423 + 1 ,0,5)DLA419 = TRUE 		}
 		if(event_press(PS5_RIGHT)){
 			DLA423 = DLA117(DLA423 - 1 ,0,5)DLA419 = TRUE 		}
 		if(event_press(PS5_UP)){
 			DLA420[DLA423]  = DLA117(DLA420[DLA423] + 1 ,0,9)DLA419 = TRUE 		}
 		if(event_press(PS5_DOWN)){
 			DLA420[DLA423]  = DLA117(DLA420[DLA423] - 1 ,0,9)DLA419 = TRUE 		}
 if(event_press(PS5_CROSS)){
    DLA424 = TRUE; // Bypassing the validation and directly setting DA515 to TRUE
    set_pvar(SPVAR_2,DLA424);
    DLA419 = TRUE;
}
 			/*
 		if(event_press(PS5_CROSS)){
 			DLA421 = 0;
 			for(DLA422 = 5;
 			DLA422 >= 0;
 			DLA422--){
 				DLA421 += DLA420[DLA422] * pow(10,DLA422) 			}
 			if(DLA421 == DLA115(DLA417)){
 				DLA424 = TRUE;
 				set_pvar(SPVAR_2,DLA424)  			}
 			DLA419 = TRUE;
 					}
 					*/
 			}
 	if(DLA419){
 		cls_oled(0)if(!DLA424){
 			DLA121(DLA417,DLA447,10,OLED_FONT_MEDIUM,OLED_WHITE,DLA448)for( DLA422 = 0;
 			DLA422 < 6;
 			DLA422++){
 				DLA121(DLA420[DLA422],85 - (DLA422 * 10),40,OLED_FONT_MEDIUM,!(DLA422 == DLA423),DLA448) 			}
 					}
 		DLA419 = FALSE;
 			}
 	if(DLA424){
 		if (get_ival(DLA382) || get_ival(DLA386) || get_ival(DLA384) || get_ival(DLA385) || DLA320 || combo_running(DLA72) || get_info(CPU_USAGE) > 95 ) {
 			vm_tctrl(0);
 					}
 		else{
 			DLA101(DLA102);
 					}
 		if(get_ival(DLA387) > 40 || (!get_ival(DLA384) && !get_ival(DLA385))){
 			if(get_ival(DLA382)){
 				vm_tctrl(0);
 				if(get_ptime(DLA382) > DLA514){
 					set_val(DLA382,0);
 									}
 							}
 					}
 		if(!get_ival(DLA384)){
 			if(get_ival(DLA382)){
 				vm_tctrl(0);
 				if(get_ptime(DLA382) > DLA514){
 					set_val(DLA382,0);
 									}
 							}
 					}
 		if (DLA320) {
 			vm_tctrl(0);
 			if(DLA321 < 8050){
 				DLA321 += get_rtime();
 							}
 			if (DLA321 >= 8000) {
 				cls_oled(OLED_BLACK);
 				DLA321 = 0;
 				DLA320 = FALSE;
 							}
 					}
 		if (block) {
 			if (DLA431 < 310) {
 				DLA431 += get_rtime();
 							}
 			if (DLA431 <= 300 ) {
 				DLA157();
 							}
 			if (DLA431 > 300 ) {
 				block = FALSE;
 				DLA431 = 1;
 				DLA910 = TRUE;
 							}
 			if (DLA431 < 0) {
 				DLA431 = 1;
 							}
 			if (DLA431 <= 100) {
 				combo_stop(DLA87);
 				combo_stop(DLA96);
 				combo_stop(DLA88);
 				combo_stop(DLA97);
 				combo_stop(DLA94);
 				combo_stop(DLA95);
 				combo_stop(DLA91);
 				combo_stop(DLA93);
 				combo_stop(DLA90);
 				combo_stop(DLA85);
 				combo_stop(DLA89);
 							}
 					}
 		if((get_ival(PS5_L2) && event_press(PS5_R2) || event_press(PS5_L2) && get_ival(PS5_R2) )){
 			block = TRUE;
 					}
 		if (get_ival(PS5_L2)) {
 			if (get_ival(PS5_LEFT)) {
 				set_val(PS5_LEFT, 0);
 				DLA999 = -1 			}
 			else if (get_ival(PS5_RIGHT)) {
 				set_val(PS5_RIGHT, 0);
 				DLA999 = 1 			}
 					}
 		if (get_ival(PS5_L2)) {
 			set_val(PS5_SHARE, 0);
 			if (event_press(PS5_SHARE)) {
 				vm_tctrl(0);
 				DLA915 = !DLA915;
 				DLA214(DLA1088);
 				DLA191(DLA915, sizeof(DLA457) - 1, DLA457[0]);
 				DLA320 = TRUE;
 							}
 					}
 		if (DLA915) {
 			if (DLA339) {
 				DLA238();
 							}
 			if (event_release(DLA387)) {
 				DLA459 = 1;
 							}
 			if (DLA459 < 8000) {
 				DLA459 += get_rtime();
 							}
 			if (get_ival(PS5_R2)) {
 				if (event_press(PS5_OPTIONS)) {
 					DLA461 = !DLA461;
 					DLA214(DLA461);
 									}
 				set_val(PS5_OPTIONS, 0);
 							}
 			if (DLA461) {
 				if (DLA461) DLA210(DLA946);
 				if (DLA461) {
 					DLA136();
 									}
 							}
 			else if (!get_ival(DLA387)) {
 				DLA210(DLA949);
 				if (get_ival(PS5_L2)) {
 					if (event_press(PS5_OPTIONS)) {
 						DLA316 = TRUE;
 						DLA323 = TRUE;
 						DLA322 = FALSE;
 						if (!DLA316) {
 							DLA322 = TRUE;
 													}
 											}
 					set_val(PS5_OPTIONS, 0);
 									}
 				if (!DLA322) {
 					if (DLA316 || DLA317) {
 						vm_tctrl(0);
 					}
 					if (DLA316) {
 						combo_stop(DLA72);
 						vm_tctrl(0);
 						DLA324= DLA137(DLA324,0  );
 						DLA325  = DLA137(DLA325 , 1);
 						DLA326 = DLA137(DLA326, 2);
 						DLA327 = DLA137(DLA327, 3);
 						DLA328 = DLA137(DLA328, 4);
 						DLA329 = DLA137(DLA329, 5);
 						DLA330 = DLA137(DLA330, 6);
 						DLA331 = DLA137(DLA331, 7);
 						DLA332 = DLA137(DLA332, 8);
 						DLA333 = DLA137(DLA333, 9);
 						DLA334 = DLA137(DLA334,10);
 						DLA335 = DLA137(DLA335, 11);
 						DLA336 = DLA137(DLA336, 12);
 						DLA337 = DLA137(DLA337, 13);
 						DLA338 = DLA137(DLA338, 14);
 						DLA339 = DLA137(DLA339, 15);
 						DLA102 = DLA137(DLA102, 16);
 						DLA341 = DLA137(DLA341, 17);
 						if (event_press(PS5_DOWN)) {
 							DLA318 = clamp(DLA318 + 1, 0, DLA342);
 							DLA323 = TRUE;
 													}
 						if (event_press(PS5_UP)) {
 							DLA318 = clamp(DLA318 - 1, 0, DLA342);
 							DLA323 = TRUE;
 													}
 						if (event_press(PS5_CIRCLE)) {
 							DLA316 = FALSE;
 							DLA322 = FALSE;
 							DLA323 = FALSE;
 							vm_tctrl(0);
 							combo_run(DLA75);
 													}
 						if (DLA483[DLA318][2] == 1) {
 							if(DLA318 == 0 ){
 								if(DLA324 == 2 ){
 									if (event_press(PS5_CROSS)) {
 										DLA319 = DLA483[DLA318][0];
 										DLA316 = FALSE;
 										DLA317 = TRUE;
 										DLA323 = TRUE;
 																			}
 																	}
 															}
 							else{
 								if (event_press(PS5_CROSS)) {
 									DLA319 = DLA483[DLA318][0];
 									DLA316 = FALSE;
 									DLA317 = TRUE;
 									DLA323 = TRUE;
 																	}
 															}
 													}
 						DLA157();
 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DLA473[0]);
 						DLA146(DLA318 + 1, DLA152(DLA318 + 1), 28, 38, OLED_FONT_SMALL);
 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DLA475[0]);
 						DLA146(DLA804, DLA152(DLA804), 112, 38, OLED_FONT_SMALL);
 						line_oled(1, 48, 127, 48, 1, 1);
 						if(DLA318 == 0 ){
 							if(DLA324 == 2 ){
 								print(2, 52, OLED_FONT_SMALL, 1, DLA477[0]);
 															}
 							else{
 								print(2, 52, OLED_FONT_SMALL, 1, DLA478[0]);
 															}
 													}
 						else{
 							if (DLA483[DLA318][2] == 0) {
 								print(2, 52, OLED_FONT_SMALL, 1, DLA478[0]);
 															}
 							else {
 								print(2, 52, OLED_FONT_SMALL, 1, DLA477[0]);
 															}
 													}
 											}
 					if (DLA317) {
 						DLA374               = DLA140(DLA374, 0);
 						DLA375               = DLA140(DLA375, 1);
 						DLA376             = DLA140(DLA376, 2);
 						DLA377           = DLA140(DLA377, 3);
 						DLA378             = DLA140(DLA378, 4);
 						DLA379             = DLA140(DLA379, 5);
 						DLA380              = DLA140(DLA380, 6);
 						DLA381           = DLA140(DLA381, 7);
 						DLA344               = DLA140(DLA344, 8);
 						DLA345               = DLA140(DLA345, 9);
 						DLA346                   = DLA140(DLA346, 10);
 						DLA347                   = DLA140(DLA347,11);
 						DLA348                   = DLA140(DLA348, 12);
 						DLA349   = DLA140(DLA349, 13);
 						DLA350   = DLA140(DLA350, 14);
 						DLA351 = DLA140(DLA351, 15);
 						DLA352   = DLA140(DLA352, 16);
 						DLA353   = DLA140(DLA353, 17);
 						DLA354 = DLA140(DLA354, 18);
 						DLA355   = DLA140(DLA355, 19);
 						DLA356   = DLA140(DLA356, 20);
 						DLA357 = DLA140(DLA357, 21);
 						DLA358   = DLA140(DLA358, 22);
 						DLA359   = DLA140(DLA359, 23);
 						DLA360 = DLA140(DLA360, 24);
 						DLA361   = DLA143(DLA361, 25);
 						DLA362         = DLA143(DLA362, 26);
 						DLA363   = DLA140(DLA363, 27);
 						DLA364     = DLA140(DLA364, 28);
 						DLA365                   = DLA143(DLA365, 29);
 						DLA366               = DLA143(DLA366, 30);
 						DLA367 = DLA143(DLA367, 31);
 						DLA368     = DLA140(DLA368, 32);
 						DLA369               = DLA143(DLA369, 33);
 						DLA370 = DLA140(DLA370, 34);
 						DLA371 = DLA140(DLA371, 35);
 						DLA372 = DLA140(DLA372, 36);
 						DLA373 = DLA140(DLA373, 37);
 						DLA1049 = DLA140(DLA1049, 38);
 						DLA1042 = DLA140(DLA1042, 39);
 						DLA390             = DLA143(DLA390           ,40 );
 						DLA29             = DLA140(DLA29           ,41);
 						if (!get_ival(PS5_L2)) {
 							if (event_press(PS5_RIGHT)) {
 								DLA319 = clamp(DLA319 + 1, DLA483[DLA318][0], DLA483[DLA318][1]);
 								DLA323 = TRUE;
 															}
 							if (event_press(PS5_LEFT)) {
 								DLA319 = clamp(DLA319 - 1, DLA483[DLA318][0], DLA483[DLA318][1]);
 								DLA323 = TRUE;
 															}
 													}
 						if (event_press(PS5_CIRCLE)) {
 							DLA316 = TRUE;
 							DLA317 = FALSE;
 							DLA323 = TRUE;
 													}
 						DLA157();
 						DLA806 = DLA718[DLA319][0];
 						DLA807 = DLA718[DLA319][1];
 						if (DLA718[DLA319][4] == 0) {
 							DLA146(DLA806, DLA152(DLA806), 4, 20, OLED_FONT_SMALL);
 							DLA146(DLA807, DLA152(DLA807), 97, 20, OLED_FONT_SMALL);
 													}
 											}
 					if (DLA323) {
 						cls_oled(OLED_BLACK);
 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE);
 						line_oled(0, 14, 127, 14, 1, 1);
 						if (DLA317) {
 							print(DLA202(DLA155(DLA492[DLA319]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DLA492[DLA319]);
 													}
 						else {
 							print(DLA202(DLA155(DLA493[DLA318]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DLA493[DLA318]);
 													}
 						DLA323 = FALSE;
 					}
 									}
 				if (!DLA316 && !DLA317) {
 					if (DLA322) {
 						cls_oled(0);
 						combo_run(DLA72);
 						DLA322 = FALSE;
 						DLA320 = TRUE;
 						vm_tctrl(0);
 					}
 					if(DLA324 == 0){
 						DLA382      = PS5_CIRCLE;
 						DLA383      = PS5_CROSS ;
 						DLA384    = PS5_L1    ;
 						DLA385  = PS5_R1;
 						DLA386    = PS5_L2;
 						DLA387    = PS5_R2;
 						DLA388     = PS5_SQUARE;
 						DLA389  = PS5_TRIANGLE;
 					}
 					else if(DLA324 == 1){
 						DLA382      = PS5_SQUARE;
 						DLA383      = PS5_CROSS ;
 						DLA384    = PS5_L1    ;
 						DLA385  = PS5_R1;
 						DLA386    = PS5_L2;
 						DLA387    = PS5_R2;
 						DLA388     = PS5_CIRCLE;
 						DLA389  = PS5_TRIANGLE;
 					}
 					else if(DLA324 == 2){
 						DLA382      = DLA1156[DLA374];
 						DLA383      = DLA1156[DLA375] ;
 						DLA384    = DLA1156[DLA376]  ;
 						DLA385  = DLA1156[DLA377];
 						DLA386    = DLA1156[DLA378];
 						DLA387    = DLA1156[DLA379];
 						DLA388     = DLA1156[DLA380];
 						DLA389  = DLA1156[DLA381];
 					}
 					if (DLA497 >= 2000) {
 						DLA497 = 2000;
 											}
 					else if (DLA497 <= 50) {
 						DLA497 = 50;
 											}
 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DLA1022) {
 						set_val(DLA383, 0);
 						vm_tctrl(0);
 						combo_run(DLA77);
 											}
 					if (DLA910) {
 						if ((get_ipolar(POLAR_LS,POLAR_RADIUS) > 5100) ){
 							DLA499 = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
 							DLA905 = DLA1167[DLA499][0];
 							DLA563 = DLA1167[DLA499][1];
 													}
 					}
 					if (get_ival(XB1_RS)) {
 						if (event_press(PS5_RIGHT)) {
 							DLA365 += 5;
 							DLA198(DLA202(sizeof(DLA501) - 1, OLED_FONT_MEDIUM_WIDTH), DLA501[0], DLA365);
 													}
 						if (event_press(PS5_LEFT)) {
 							DLA365 -= 5;
 							DLA198(DLA202(sizeof(DLA501) - 1, OLED_FONT_MEDIUM_WIDTH), DLA501[0], DLA365);
 													}
 						set_val(PS5_RIGHT, 0);
 						set_val(PS5_LEFT, 0);
 											}
 					if (get_ival(XB1_RS) && !DLA520 ) {
 						if (event_press(PS5_UP)) {
 							DLA497 += 50;
 							DLA198(DLA202(sizeof(DLA507) - 1, OLED_FONT_MEDIUM_WIDTH), DLA507[0], DLA497);
 													}
 						if (event_press(PS5_DOWN)) {
 							DLA497 -= 50;
 							DLA198(DLA202(sizeof(DLA507) - 1, OLED_FONT_MEDIUM_WIDTH), DLA507[0], DLA497);
 													}
 						set_val(PS5_UP, 0);
 						set_val(PS5_DOWN, 0);
 											}
 					if (DLA335) {
 						DLA233();
 											}
 					if (DLA336) {
 						DLA234();
 						DLA235();
 											}
 					if (!DLA336) {
 						if (get_ival(DLA382)) {
 							if (event_press(PS5_RIGHT)) {
 								DLA514 += 2;
 								DLA198(DLA202(sizeof(DLA515) - 1, OLED_FONT_MEDIUM_WIDTH), DLA515[0], DLA514);
 															}
 							if (event_press(PS5_LEFT)) {
 								DLA514 -= 2;
 								DLA198(DLA202(sizeof(DLA515) - 1, OLED_FONT_MEDIUM_WIDTH), DLA515[0], DLA514);
 															}
 							set_val(PS5_RIGHT, 0);
 							set_val(PS5_LEFT, 0);
 													}
 						if(!get_ival(DLA384) ){
 							if(get_ival(DLA382) && get_ptime(DLA382) > DLA514){
 								set_val(DLA382,0);
 															}
 													}
 											}
 					if (DLA331) {
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_SHARE)) {
 								DLA520 = !DLA520;
 								DLA214(DLA520);
 															}
 							set_val(PS5_SHARE, 0);
 													}
 											}
 					if (DLA520 && DLA331) {
 						vm_tctrl(0);
 						combo_stop(DLA85);
 						if (get_ival(XB1_RS)) {
 							if (event_press(PS5_UP)) {
 								DLA361 += 10;
 								DLA198(DLA202(sizeof(DLA522) - 1, OLED_FONT_MEDIUM_WIDTH), DLA522[0], DLA361);
 															}
 							if (event_press(PS5_DOWN)) {
 								DLA361 -= 10;
 								DLA198(DLA202(sizeof(DLA522) - 1, OLED_FONT_MEDIUM_WIDTH), DLA522[0], DLA361);
 															}
 							set_val(PS5_UP, 0);
 							set_val(PS5_DOWN, 0);
 													}
 						DLA210(DLA948);
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_RIGHT)) {
 								DLA527 = FALSE;
 								vm_tctrl(0);
 								combo_run(DLA78);
 															}
 							if (event_press(PS5_LEFT)) {
 								DLA527 = TRUE;
 								vm_tctrl(0);
 								combo_run(DLA78);
 															}
 							set_val(PS5_L1,0);
 													}
 											}
 					if (DLA332) {
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_OPTIONS)) {
 								DLA529 = !DLA529;
 								DLA214(DLA529);
 															}
 							set_val(PS5_OPTIONS, 0);
 													}
 											}
 					if (DLA529 && DLA332) {
 						vm_tctrl(0);
 						DLA210(DLA950);
 						if (get_ival(PS5_L1)) {
 							if (event_press(PS5_LEFT)) {
 								DLA530 = FALSE;
 								vm_tctrl(0);
 								combo_run(DLA79);
 															}
 							if (event_press(PS5_RIGHT)) {
 								DLA530 = TRUE;
 								vm_tctrl(0);
 								combo_run(DLA79);
 															}
 													}
 											}
 					if(DLA325  ){
 						DLA106();
 											}
 					if (DLA326) {
 						if (DLA326 == 1) {
 							DLA533 = PS5_R3;
 													}
 						if (DLA326 == 2) {
 							DLA533 = PS5_L3;
 													}
 						if (DLA326 == 3) {
 							DLA533 = XB1_PR1;
 													}
 						if (DLA326 == 4) {
 							DLA533 = XB1_PR2;
 													}
 						if (DLA326 == 5) {
 							DLA533 = XB1_PL1;
 													}
 						if (DLA326 == 6) {
 							DLA533 = XB1_PL2;
 													}
 						if(get_ival(DLA533)){
 							if(DLA344 || DLA345){
 								if( DLA344 && event_press(PS5_L1)){
 									DLA398 = FALSE;
 									DLA904 = DLA344  ;
 									DLA212( DLA344   );
 								}
 								if( DLA345 && event_press(PS5_R1)){
 									DLA398 = TRUE;
 									DLA904 =  DLA345 ;
 									DLA212( DLA345   );
 																	}
 								set_val(PS5_L1,0);
 								set_val(PS5_R1,0);
 								block = TRUE;
 															}
 							if( DLA346 ){
 								if(event_press(PS5_SQUARE)){
 									DLA398 = FALSE;
 									DLA904 =  DLA346  ;
 									DLA212( DLA346   );
 								}
 								if(event_press(PS5_TRIANGLE)){
 									DLA398 = TRUE;
 									DLA904 =  DLA346  ;
 									DLA212( DLA346   );
 								}
 								set_val(PS5_SQUARE,0);
 								set_val(PS5_TRIANGLE,0);
 								block = TRUE;
 															}
 							if( DLA347 ){
 								if(event_press(PS5_CROSS)){
 									DLA398 = FALSE;
 									DLA904 =  DLA347  ;
 									DLA212( DLA347   );
 								}
 								if(event_press(PS5_CIRCLE)){
 									DLA398 = TRUE;
 									DLA904 =  DLA347  ;
 									DLA212( DLA347   );
 								}
 								set_val(PS5_CROSS,0);
 								set_val(PS5_CIRCLE,0);
 								block = TRUE;
 															}
 							if( DLA348 ){
 								if(event_press(PS5_R3)){
 									DLA398 = FALSE;
 									DLA904 =  DLA348  ;
 									DLA212( DLA348   );
 								}
 								set_val(PS5_R3,0);
 								block = TRUE;
 															}
 													}
 						set_val(DLA533,0);
 											}
 					if (DLA327) {
 						if (DLA350 == 1) {
 							if (event_press(DLA1166[-1 + DLA349]) && !DLA974) {
 								vm_tctrl(0);
 								combo_run(DLA82);
 															}
 							else if (event_press(DLA1166[-1 + DLA349]) && DLA974) {
 								set_val(DLA1166[-1 + DLA349], 0);
 								DLA398 = !DLA351;
 								DLA904 = DLA327;
 								DLA212(DLA327);
 															}
 													}
 						else {
 							if (event_press(DLA1166[-1 + DLA349])) {
 								DLA398 = !DLA351;
 								set_val(DLA1166[-1 + DLA349], 0);
 								DLA904 = DLA327;
 								DLA212(DLA327);
 															}
 													}
 					}
 					if (DLA329) {
 						if (DLA356 == 1) {
 							if (event_press(DLA1166[-1 +DLA355]) && !DLA974) {
 								vm_tctrl(0);
 								combo_run(DLA82);
 															}
 							else if (event_press(DLA1166[-1 +DLA355]) && DLA974) {
 								set_val(DLA1166[-1 +DLA355], 0);
 								DLA398 = !DLA357;
 								DLA904 = DLA329;
 								DLA212(DLA329);
 															}
 													}
 						else {
 							if (event_press(DLA1166[-1 +DLA355])) {
 								DLA398 = !DLA357;
 								set_val(DLA1166[-1 +DLA355], 0);
 								DLA904 = DLA329;
 								DLA212(DLA329);
 															}
 													}
 					}
 					if (DLA328) {
 						if (DLA353 == 1) {
 							if (event_press(DLA1166[-1 +DLA352]) && !DLA974) {
 								vm_tctrl(0);
 								combo_run(DLA82);
 															}
 							else if (event_press(DLA1166[-1 +DLA352]) && DLA974) {
 								set_val(DLA1166[-1 +DLA352], 0);
 								DLA398 = !DLA354;
 								DLA904 = DLA328;
 								DLA212(DLA328);
 															}
 													}
 						else {
 							if (event_press(DLA1166[-1 +DLA352])) {
 								DLA398 = !DLA354;
 								set_val(DLA1166[-1 +DLA352], 0);
 								DLA904 = DLA328;
 								DLA212(DLA328);
 															}
 													}
 					}
 					if (DLA330) {
 						if (DLA359 == 1) {
 							if (event_press(DLA1166[-1 +DLA358]) && !DLA974) {
 								vm_tctrl(0);
 								combo_run(DLA82);
 															}
 							else if (event_press(DLA1166[-1 +DLA358]) && DLA974) {
 								set_val(DLA1166[-1 +DLA358], 0);
 								DLA398 = !DLA360;
 								DLA904 = DLA330;
 								DLA212(DLA330);
 															}
 													}
 						else {
 							if (event_press(DLA1166[-1 +DLA358])) {
 								DLA398 = !DLA360;
 								set_val(DLA1166[-1 +DLA358], 0);
 								DLA904 = DLA330;
 								DLA212(DLA330);
 															}
 													}
 					}
 					if (DLA904 == DLA274 && combo_running(DLA30)) set_val(DLA384, 100);
 					if (DLA333) {
 						if (event_press(DLA1166[-1 +DLA363])) {
 							vm_tctrl(0);
 							combo_run(DLA77);
 													}
 						set_val(DLA1166[-1 +DLA363], 0);
 											}
 					if(!DLA337){
 						DLA366 = 0 ;
 						DLA367 = 0;
 						DLA368 = FALSE;
 						DLA369 = 0;
 											}
 					if (DLA338) {
 						DLA234();
 						if (DLA370 == 0) {
 							DLA552 = FALSE;
 							DLA392 = 0;
 													}
 						else {
 							DLA552 = TRUE;
 							DLA392 = 40;
 													}
 						if (DLA371 == 0) {
 							DLA554 = FALSE;
 							DLA391 = 0;
 													}
 						else {
 							DLA554 = TRUE;
 							DLA391 = 85;
 													}
 						if (DLA372 == 0) {
 							DLA556 = FALSE;
 							DLA393 = 0;
 													}
 						else {
 							DLA556 = TRUE;
 							DLA393 = -15;
 													}
 						if (DLA373 == 0) {
 							DLA558 = FALSE;
 													}
 						else {
 							DLA558 = TRUE;
 													}
 						if(DLA372 == 6 || DLA371 == 6 || DLA370 == 6){
 							if (get_ival(DLA1166[-1 + DLA372]) || get_ival(DLA1166[-1 + DLA371]) || get_ival(DLA1166[-1 + DLA370])){
 								combo_run(DLA0);
 															}
 													}
 						if (DLA556) {
 							if (get_val(DLA1166[-1 + DLA372])) {
 								set_val(DLA1166[-1 + DLA372], 0);
 								combo_run(DLA96);
 								DLA1031 = 9000;
 															}
 													}
 						if (DLA558) {
 							if (get_val(DLA1166[-1 + DLA373])) {
 								set_val(DLA1166[-1 + DLA373], 0);
 								combo_run(DLA97);
 								DLA1031 = 9000;
 							}
 							if (combo_running(DLA97)) {
 								if (get_ival(DLA383) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DLA387) > 30) {
 									combo_stop(DLA97);
 																	}
 															}
 													}
 						if (DLA554) {
 							if (get_val(DLA1166[-1 + DLA371])) {
 								set_val(DLA1166[-1 + DLA371], 0);
 								DLA237();
 								DLA1031 = 9000;
 															}
 													}
 						if (DLA552) {
 							if (get_val(DLA1166[-1 + DLA370])) {
 								set_val(DLA1166[-1 + DLA370], 0);
 								combo_run(DLA94);
 								DLA1031 = 9000;
 															}
 													}
 											}
 					else{
 						DLA392 = 0;
 						DLA393 = 0;
 						DLA391 = 0;
 											}
 					if (DLA339) {
 						DLA238();
 											}
 									}
 							}
 					}
 		else {
 			if (!get_ival(DLA387)) DLA210(DLA947);
 					}
 			}
 			DLA240();
 	}
 combo DLA1 {
 	set_val(DLA386, 100);
 	set_val(DLA384,100);
 	DLA228();
 	wait(400);
 	set_val(DLA383,100);
 	wait(90);
 	wait( 400);
 	}
 combo DLA2 {
 	set_val(DLA386, 100);
 	set_val(DLA384,100);
 	DLA228();
 	wait(400);
 	set_val(DLA382,100);
 	wait(220);
 	wait( 400);
 	}
 combo DLA3 {
 	call(DLA28);
 	wait( 100);
 	call(DLA97);
 	DLA224(DLA905, DLA563);
 	wait( 800);
 	wait( 350);
 	set_val(DLA385,100);
 	set_val(DLA384,100);
 	wait( 400);
 	}
 combo DLA4 {
 	DLA230();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA5 {
 	DLA228();
 	wait( DLA908);
 	DLA230();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA6 {
 	if (DLA398) DLA580 = DLA499 + 1;
 	else DLA580 = DLA499 - 1;
 	DLA219(DLA580);
 	DLA230();
 	wait( DLA908);
 	DLA228();
 	DLA224(DLA978, DLA566);
 	wait( DLA908);
 	DLA224(DLA978, DLA566);
 	wait( 1000);
 	wait( 350);
 	}
 combo DLA7 {
 	DLA231();
 	wait( DLA908);
 	DLA398 = FALSE;
 	DLA228();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	DLA398 = TRUE;
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA8 {
 	DLA231();
 	wait( DLA908);
 	DLA398 = TRUE;
 	DLA228();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	DLA398 = FALSE;
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA9 {
 	DLA398 = TRUE;
 	DLA228();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	DLA398 = FALSE;
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA10 {
 	DLA398 = FALSE;
 	DLA228();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	DLA398 = TRUE;
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA11 {
 	DLA224(0,0);
 	set_val(DLA384,100);
 	set_val(DLA385,100);
 	DLA230();
 	wait( 60);
 	wait( 60);
 	}
 combo DLA12 {
 	set_val(DLA965, inv(DLA905));
 	set_val(DLA966, inv(DLA563));
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	wait( 60);
 	set_val(DLA965, inv(DLA905));
 	set_val(DLA966, inv(DLA563));
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	wait( 500);
 	wait( 350);
 	}
 combo DLA13 {
 	DLA224(0, 0);
 	set_val(DLA386, 100);
 	wait( 60);
 	DLA224(0, 0);
 	set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	wait( 60);
 	DLA224(0, 0);
 	set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 80);
 	DLA224(0, 0);
 	set_val(DLA386, 100);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	wait( 350);
 	}
 combo DLA14 {
 	set_val(DLA382, 100);
 	wait( 60);
 	DLA224(inv(DLA905), inv(DLA563));
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 80);
 	DLA224(inv(DLA905), inv(DLA563));
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	wait( 350);
 	}
 combo DLA15 {
 	set_val(DLA384, 100);
 	DLA228();
 	wait( 500);
 	wait( 350);
 	}
 combo DLA16 {
 	DLA231();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	DLA230();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA17 {
 	DLA231();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	DLA230();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA18 {
 	DLA229();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA19 {
 	DLA229();
 	set_val(DLA384,100);
 	wait( DLA908);
 	DLA231();
 	set_val(DLA384,100);
 	wait( DLA908);
 	DLA228();
 	set_val(DLA384,100);
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA20 {
 	DLA229();
 	wait( DLA908);
 	DLA224(0, 0);
 	DLA230();
 	wait( DLA908);
 	DLA224(0, 0);
 	DLA228()  	wait( DLA908);
 	DLA398 = !DLA398;
 	DLA227();
 	wait( 1000);
 	wait( 350);
 	}
 combo DLA21 {
 	set_val(DLA384,100);
 	DLA231();
 	wait(60);
 	DLA224(0,0);
 	set_val(DLA384,100);
 	wait(60);
 	set_val(DLA384,100);
 	DLA231();
 	wait(60);
 	wait( 350);
 	}
 combo DLA22 {
 	DLA224(0, 0);
 	wait( DLA908);
 	DLA224(0, 0);
 	DLA231();
 	wait( DLA908);
 	DLA224(0, 0);
 	DLA232();
 	wait( DLA908);
 	DLA224(0, 0);
 	DLA231();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA23 {
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	DLA231();
 	wait( 80);
 	wait( 350);
 	}
 combo DLA24 {
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	wait( 20);
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	if (DLA398) DLA580 = DLA499 + 4;
 	else {
 		DLA580 = DLA499 - 4;
 			}
 	DLA219(DLA580);
 	DLA221(DLA978, DLA566);
 	set_val(DLA387, 100);
 	wait( 100);
 	wait( 350);
 	}
 combo DLA25 {
 	set_val(DLA386, 100);
 	DLA231();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	wait( 30);
 	set_val(DLA386, 100);
 	DLA231();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA26 {
 	set_val(DLA386, 100);
 	DLA230();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	DLA232();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	DLA231();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA27 {
 	set_val(DLA386, 100);
 	DLA231();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	wait( 30);
 	set_val(DLA386, 100);
 	DLA231();
 	wait( DLA908);
 	DLA224(0, 0);
 	wait( 400);
 	set_val(PS5_L2, 100);
 	set_val(PS5_L1, 100);
 	set_val(PS5_R1, 100);
 	set_val(PS5_R2, 100);
 	wait( 70);
 	wait( 350);
 	}
 combo DLA28 {
 	DLA228();
 	wait( 300);
 	set_val(PS5_R3,100);
 	wait( 60);
 	wait( 60);
 	wait( 350);
 }
 combo DLA29 {
 	DLA228();
 	set_val(DLA387, 0);
 	wait( 310);
 	wait( 100);
 	wait( 350);
 	}
 combo DLA30 {
 	if (DLA904 == DLA276) DLA909 = 200;
 	else DLA909 = 1;
 	wait( DLA909);
 	DLA230();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA31 {
 	DLA228();
 	wait( 300);
 	DLA232();
 	wait( DLA908);
 	DLA229();
 	wait( 300);
 	wait( 350);
 	}
 combo DLA32 {
 	if (DLA904 == DLA276) DLA909 = 200;
 	else DLA909 = 1;
 	set_val(DLA386,100);
 	wait( DLA909);
 	DLA230();
 	set_val(DLA386,100);
 	wait( DLA908);
 	DLA232();
 	set_val(DLA386,100);
 	wait( DLA908);
 	DLA228();
 	set_val(DLA386,100);
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA33 {
 	DLA228();
 	wait( 250);
 	DLA232();
 	wait( 50);
 	DLA398 = !DLA398;
 	if (DLA398) DLA580 = DLA499 - 2;
 	else DLA580 = DLA499 + 2;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 45);
 	set_val(DLA382, 100);
 	DLA224(DLA978, DLA566);
 	wait( 45);
 	DLA224(DLA978, DLA566);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 45);
 	DLA224(DLA978, DLA566);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 45);
 	DLA224(DLA978, DLA566);
 	wait( 100);
 	DLA224(DLA978, DLA566);
 	wait( 500);
 	wait( 350);
 	}
 combo DLA34 {
 	DLA228();
 	wait( 280);
 	DLA227()  set_val(DLA382, 100);
 	set_val(DLA386, 100);
 	wait( 60);
 	DLA227()  set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 60);
 	DLA227()  set_val(DLA386, 100);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	wait( 250);
 	DLA227()   	wait( 300);
 	wait( 350);
 	}
 combo DLA35 {
 	DLA228();
 	wait( 300);
 	DLA230();
 	wait( 60);
 	wait( 350);
 	}
 combo DLA36 {
 	set_val(DLA384, 100);
 	DLA231();
 	wait( DLA908);
 	set_val(DLA384, 100);
 	DLA232();
 	wait( DLA908);
 	set_val(DLA384, 100);
 	DLA231();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA37 {
 	DLA230();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA231();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA38 {
 	set_val(DLA384, 100);
 	DLA229();
 	wait( 60);
 	set_val(DLA384, 100);
 	DLA232();
 	wait( 60);
 	set_val(DLA384, 100);
 	DLA228();
 	wait( 60);
 	wait( 300);
 	wait( 350);
 	}
 combo DLA39 {
 	DLA231();
 	set_val(DLA384,100);
 	wait( DLA908);
 	DLA232();
 	set_val(DLA384,100);
 	wait( DLA908);
 	DLA228();
 	set_val(DLA384,100);
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA40 {
 	if (DLA398) DLA580 = DLA499 + 3;
 	else DLA580 = DLA499 - 3;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	set_val(DLA382, 100);
 	wait( 60);
 	DLA224(DLA978, DLA566);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 80);
 	DLA224(DLA978, DLA566);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	DLA224(DLA978, DLA566);
 	wait( 300);
 	wait( 350);
 	}
 combo DLA41 {
 	set_val(DLA384, 100);
 	DLA230();
 	DLA224(0, 0);
 	wait( DLA908);
 	set_val(DLA384, 100);
 	DLA232();
 	DLA224(0, 0);
 	wait( DLA908);
 	set_val(DLA384, 100);
 	DLA224(0, 0);
 	DLA231();
 	wait( DLA908);
 	if (DLA398) DLA580 = DLA499 + 1;
 	else DLA580 = DLA499 - 1;
 	DLA219(DLA580);
 	set_val(DLA387,0);
 	DLA224(DLA978, DLA566);
 	wait( 200);
 	set_val(DLA387,0);
 	wait( 350);
 	}
 combo DLA42 {
 	if (DLA904 == DLA276) DLA909 = 200;
 	else DLA909 = 1;
 	wait( DLA909);
 	DLA230();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA43 {
 	DLA231();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	set_val(PS5_L2, 100);
 	set_val(PS5_R2, 100);
 	wait( 300);
 	wait( 350);
 	}
 combo DLA44 {
 	DLA230();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	if (DLA904 == DLA289) DLA227();
 	set_val(DLA386, 100);
 	set_val(DLA387, 100);
 	wait( 200);
 	if (DLA904 == DLA289) DLA227();
 	wait( 300);
 	wait( 350);
 	}
 combo DLA45 {
 	DLA230();
 	wait( DLA908);
 	DLA232();
 	wait( DLA908);
 	DLA228();
 	wait( DLA908);
 	if (DLA904 == DLA289) DLA227();
 	set_val(DLA386, 100);
 	set_val(DLA387, 100);
 	wait( 200);
 	if (DLA904 == DLA289) DLA227();
 	wait( 300);
 	wait( 350);
 	}
 combo DLA46 {
 	DLA231();
 	set_val(DLA385,100);
 	wait( DLA908);
 	DLA228();
 	set_val(DLA385,100);
 	wait( DLA908);
 	DLA230();
 	set_val(DLA385,100);
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA47 {
 	DLA224(DLA905, DLA563);
 	DLA230();
 	wait( DLA908);
 	DLA224(DLA905, DLA563);
 	DLA232();
 	wait( DLA908);
 	DLA224(DLA905, DLA563);
 	DLA228();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	set_val(DLA387, 100);
 	DLA224(inv(DLA905), inv(DLA563));
 	wait( 600);
 	wait( 350);
 	}
 combo DLA48 {
 	DLA224(DLA905, DLA563);
 	set_val(XB1_LS, 100);
 	DLA230();
 	wait( DLA908);
 	DLA224(DLA905, DLA563);
 	DLA232();
 	set_val(XB1_LS, 100);
 	wait( DLA908);
 	DLA224(DLA905, DLA563);
 	DLA228();
 	wait( DLA908);
 	set_val(DLA386, 100);
 	set_val(DLA387, 100);
 	if (DLA398) DLA580 = DLA499 + 4;
 	else DLA580 = DLA499 - 4;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	wait( 220);
 	if (DLA398) DLA580 = DLA499 + 4;
 	else DLA580 = DLA499 - 4;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	wait( 60);
 	if (DLA398) DLA580 = DLA499 + 1;
 	else DLA580 = DLA499 - 1;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	wait( 600);
 	wait( 350);
 	}
 combo DLA49 {
 	set_val(DLA383, 0);
 	set_val(DLA382, 100);
 	wait( 80);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 80);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 80);
 	wait( 500);
 	wait( 350);
 	}
 combo DLA50 {
 	set_val(DLA382, 100);
 	set_val(DLA387,100);
 	wait( 60);
 	set_val(DLA387,100);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	set_val(DLA387,100);
 	wait( 60);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	set_val(DLA387,100);
 	wait( 60);
 	wait( 350);
 	}
 combo DLA51 {
 	set_val(DLA384,100);
 	set_val(DLA385,100);
 	DLA224(inv(DLA905), inv(DLA563));
 	wait( 200);
 	set_val(DLA384,100);
 	set_val(DLA385,100);
 	DLA398 = FALSE;
 	DLA227();
 	wait( 50);
 	set_val(DLA384,100);
 	set_val(DLA385,100);
 	DLA398 = !DLA398;
 	DLA227();
 	set_val(DLA384,100);
 	set_val(DLA385,100);
 	wait( 540);
 	wait( 350);
 	}
 combo DLA52 {
 	set_val(DLA382, 100);
 	wait( 60);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 60);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	wait( 140);
 	set_val(PS5_L2, 100);
 	set_val(PS5_R2, 100);
 	wait( 100);
 	wait( 350);
 	}
 combo DLA53 {
 	DLA224(inv(DLA905), inv(DLA563));
 	set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	wait( 60);
 	DLA224(inv(DLA905), inv(DLA563));
 	set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 60);
 	DLA224(inv(DLA905), inv(DLA563));
 	set_val(DLA386, 100);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	DLA224(0, 0);
 	wait( 300);
 	wait( 350);
 	}
 combo DLA54 {
 	set_val(DLA384, 100);
 	set_val(DLA388, 100);
 	wait( 60);
 	set_val(DLA384, 100);
 	set_val(DLA388, 100);
 	set_val(DLA383, 100);
 	wait( 60);
 	set_val(DLA384, 100);
 	set_val(DLA388, 0);
 	set_val(DLA383, 100);
 	DLA227();
 	wait( 60);
 	set_val(DLA384, 100);
 	DLA227();
 	wait( 300);
 	wait( 350);
 	}
 combo DLA55 {
 	set_val(DLA382, 100);
 	wait( 290);
 	set_val(PS5_L2, 100);
 	set_val(PS5_R2, 100);
 	wait( 300);
 	wait( 350);
 	}
 combo DLA56 {
 	set_val(DLA382, 100);
 	set_val(DLA386,100);
 	wait( 60);
 	set_val(DLA386,100);
 	set_val(DLA382, 100);
 	set_val(DLA383, 100);
 	wait( 60);
 	set_val(DLA386,100);
 	set_val(DLA382, 0);
 	set_val(DLA383, 100);
 	wait( 60);
 	wait( 350);
 	}
 combo DLA57 {
 	set_val(DLA384, 100);
 	DLA230();
 	wait( 300);
 	wait( 350);
 	}
 combo DLA58 {
 	DLA231();
 	wait( DLA908);
 	DLA230();
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA59 {
 	set_val(DLA384,100);
 	DLA231();
 	wait( DLA908);
 	DLA230();
 	set_val(DLA384,100);
 	wait( DLA908);
 	wait( 350);
 	}
 combo DLA60 {
 	DLA224(DLA905, DLA563);
 	DLA231();
 	wait( 100);
 	DLA232();
 	DLA224(DLA905, DLA563);
 	wait( 60);
 	DLA230();
 	DLA224(DLA905, DLA563);
 	wait( 320);
 	DLA224(DLA905, DLA563);
 	DLA232();
 	wait( 220);
 	DLA224(DLA905, DLA563);
 	DLA230();
 	DLA224(DLA905, DLA563);
 	wait( 100);
 	wait( 350);
 	}
 combo DLA61 {
 	call(DLA83);
 	DLA224(0, 0);
 	call(DLA84);
 	call(DLA84);
 	call(DLA84);
 	call(DLA84);
 	call(DLA84);
 	set_val(DLA386, 100);
 	DLA231();
 	wait( 70);
 	set_val(DLA386, 100);
 	DLA232();
 	wait( 60);
 	set_val(DLA386, 100);
 	DLA230();
 	wait( 60);
 	set_val(DLA386, 100);
 	wait( 600);
 	wait( 350);
 	}
 combo DLA62 {
 	set_val(DLA965, inv(DLA905));
 	set_val(DLA966, inv(DLA563));
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	wait( 60);
 	set_val(DLA965, inv(DLA905));
 	set_val(DLA966, inv(DLA563));
 	set_val(DLA385, 100);
 	set_val(DLA384, 100);
 	set_val(PS5_R3, 100);
 	wait( 60);
 	wait( 350);
 	}
 combo DLA63 {
 	wait( 100);
 	DLA224(0, 0);
 	DLA230();
 	wait( 70);
 	DLA224(0, 0);
 	DLA232()   	wait( 70);
 	DLA224(0, 0);
 	DLA230()   	wait( 70);
 	DLA224(0, 0);
 	DLA232()   	wait( 70);
 	DLA224(0, 0);
 	DLA231();
 	wait( 70);
 	DLA224(0, 0);
 	wait( 350);
 	}
 combo DLA64 {
 	set_val(PS5_R3,100);
 	if (DLA398) DLA580 = DLA499 + 1;
 	else DLA580 = DLA499 - 1;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 70);
 	DLA224(DLA978, DLA566);
 	wait( 400);
 	wait( 350);
 	}
 combo DLA65 {
 	call(DLA83);
 	DLA224(0,0);
 	wait( 60);
 	set_val(PS5_R3,100);
 	if (DLA398) DLA580 = DLA499 + 1;
 	else DLA580 = DLA499 - 1;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 70);
 	DLA224(DLA978, DLA566);
 	wait( 400);
 	wait( 350);
 	}
 combo DLA66 {
 	call(DLA83);
 	DLA224(0,0);
 	set_val(DLA386,100);
 	set_val(DLA387,100);
 	wait( 750);
 	}
 combo DLA67 {
 	set_val(PS5_R3,100);
 	if (DLA398) DLA580 = DLA499 + 2;
 	else DLA580 = DLA499 - 2;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 70);
 	DLA224(DLA978, DLA566);
 	wait( 400);
 	wait( 350);
 	}
 combo DLA68 {
 	set_val(DLA386,100);
 	set_val(PS5_R3,100);
 	if (DLA398) DLA580 = DLA499 ;
 	else DLA580 = DLA499;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 70);
 	set_val(DLA386,100);
 	DLA224(DLA978, DLA566);
 	wait( 400);
 	wait( 350);
 	}
 combo DLA69 {
 	call(DLA83);
 	set_val(DLA386,100);
 	set_val(PS5_R3,100);
 	if (DLA398) DLA580 = DLA499 ;
 	else DLA580 = DLA499;
 	DLA219(DLA580);
 	DLA224(DLA978, DLA566);
 	DLA224(DLA978, DLA566);
 	wait( 70);
 	set_val(DLA386,100);
 	DLA224(DLA978, DLA566);
 	wait( 400);
 	wait( 350);
 	}
 combo DLA70 {
 	DLA224(0,0);
 	set_val(DLA385,100);
 	set_val(DLA384,100);
 	DLA228();
 	wait( 350);
 	wait( 350);
 	set_val(DLA385,100);
 	set_val(DLA384,100);
 	wait( 400);
 	}
 int DLA129 ;
 int DLA647 ;
 int DLA648 ;
 int DLA649;
 int DLA650;
 function DLA115(DLA116){
 	DLA647 = 2;
 	DLA648 = 987654;
 	DLA129 = 54321;
 	DLA649 = (DLA116 >> DLA647) | (DLA116 << (32 - DLA647));
 	DLA650 = (((DLA649 >> ((DLA649 & 0xF) % 13)) & 0x7FFFF) + DLA129) % DLA648 + 123456;
 	return DLA650;
 	}
 define DLA652 = -1;
 define DLA447 = -2;
 define DLA654 = -3;
 define DLA655 = 0;
 define DLA448 = 1;
 function DLA117(DLA116, DLA119, DLA120) {
 	if(DLA116 > DLA120) return DLA119;
 	if(DLA116 < DLA119) return DLA120;
 	return DLA116;
 	}
 int DLA659,DLA660;
 function DLA121(DLA122,DLA123,DLA124,DLA125,DLA126,DLA127){
 	if(!DLA127){
 		print(DLA130(DLA128(DLA122),DLA125,DLA123),DLA124,DLA125,DLA126,DLA122)     	}
 	else{
 		if(DLA122 < 0){
 			putc_oled(1,45);
 					}
 		if(DLA122){
 			for(DLA659 = DLA134(DLA122) + DLA660 = (DLA122 < 0 ),DLA122 = abs(DLA122);
 			DLA122 > 0;
 			DLA659-- , DLA660++){
 				putc_oled(DLA659,DLA122%10 + 48);
 				DLA122 = DLA122/10;
 							}
 					}
 		else{
 			putc_oled(1,48);
 			DLA660 = 1         		}
 		puts_oled(DLA130(DLA660,DLA125,DLA123),DLA124,DLA125,DLA660 ,DLA126);
 			}
 	}
 int DLA681;
 function DLA128(DLA129) {
 	DLA681 = 0;
 	do {
 		DLA129++;
 		DLA681++;
 			}
 	while (duint8(DLA129));
 	return DLA681;
 	}
 function DLA130(DLA131,DLA125,DLA123) {
 	if(DLA123 == -3){
 		return 128 - ((DLA131 * (7 + (DLA125 > 1) + DLA125 * 4)) + 3 );
 			}
 	if(DLA123 == -2){
 		return 64 - ((DLA131 * (7 + (DLA125 > 1) + DLA125 * 4)) / 2);
 			}
 	if(DLA123 == -1){
 		return 3 	}
 	return DLA123;
 	}
 function DLA134(DLA135) {
 	for(DLA659 = 1;
 	DLA659 < 11;
 	DLA659++){
 		if(!(abs(DLA135) / pow(10,DLA659))){
 			return DLA659;
 			break;
 					}
 			}
 	return 1;
 	}
 function DLA136() {
 	if (get_ival(DLA382)) {
 		set_val(DLA382, 0);
 		if (get_ival(DLA384)) DLA688 = 50;
 		if (!get_ival(DLA384)) DLA688 = 440;
 		combo_run(DLA71);
 			}
 	if (DLA687 > 0) set_polar(POLAR_LS, DLA687 * -1, 32767);
 	if (get_ival(PS5_RIGHT) && get_ival(PS5_DOWN)) DLA687 = 345;
 	if (get_ival(PS5_RIGHT) && get_ival(PS5_UP)) DLA687 = 45;
 	if (get_ival(PS5_LEFT) && get_ival(PS5_UP)) DLA687 = 135;
 	if (get_ival(PS5_LEFT) && get_ival(PS5_DOWN)) DLA687 = 225;
 	if (event_press(PS5_LEFT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DLA687 = 180;
 	if (event_press(PS5_RIGHT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DLA687 = 1;
 	if (event_press(PS5_UP) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DLA687 = 90;
 	if (event_press(PS5_DOWN) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DLA687 = 270;
 }
 int DLA688;
 int DLA461;
 int DLA687;
 combo DLA71 {
 	set_val(DLA382, 100);
 	wait( DLA688);
 	wait( 50);
 	wait( 3800);
 	DLA461 = !DLA461;
 }
 define DLA691 = 19;
 function DLA137(DLA138, DLA139) {
 	if (DLA318 == DLA139) {
 		if (event_press(PS5_RIGHT)) {
 			DLA138 = clamp(DLA138 + 1, 0, DLA694[DLA318]);
 			DLA323 = TRUE;
 					}
 		if (event_press(PS5_LEFT)) {
 			DLA138 = clamp(DLA138 - 1, 0, DLA694[DLA318]);
 			DLA323 = TRUE;
 					}
 		if (DLA318 == 0) {
 			print(DLA202(DLA155(DLA698[DLA324]) ,OLED_FONT_SMALL_WIDTH),DLA691  ,OLED_FONT_SMALL , OLED_WHITE ,DLA698[DLA324]);
 					}
 		else if (DLA318 == 2) {
 			print(DLA202(DLA155(DLA700[DLA326]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA700[DLA326]);
 					}
 		else if (DLA318 == 3) {
 			print(DLA202(DLA155(DLA702[DLA327]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA327]);
 					}
 		else if (DLA318 == 4) {
 			print(DLA202(DLA155(DLA702[DLA328]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA328]);
 					}
 		else if (DLA318 == 5) {
 			print(DLA202(DLA155(DLA702[DLA329]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA329]);
 					}
 		else if (DLA318 == 6) {
 			print(DLA202(DLA155(DLA702[DLA330]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA330]);
 					}
 		else if (DLA318 == 16) {
 			print(DLA202(DLA155(DLA710[DLA102]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA710[DLA102]);
 					}
 		else {
 			if (DLA138 == 1)        print(DLA202(DLA155(DLA712[1]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA712[1])      else        print(DLA202(DLA155(DLA712[0]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA712[0])     		}
 			}
 	return DLA138;
 	}
 function DLA140(DLA138, DLA139) {
 	if (DLA319 == DLA139) {
 		if (get_ival(PS5_L2)) {
 			if (event_press(PS5_RIGHT)) {
 				DLA138 += DLA718[DLA319][2]  				        DLA323 = TRUE;
 							}
 			if (event_press(PS5_LEFT)) {
 				DLA138 -= DLA718[DLA319][2]  				        DLA323 = TRUE;
 							}
 			DLA138 = clamp(DLA138, DLA718[DLA319][0], DLA718[DLA319][1]);
 		}
 		 if(DLA319 == 8){
 			print(DLA202(DLA155(DLA702[DLA344]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA344])     		}
 		else if (DLA319 == 9) {
 			print(DLA202(DLA155(DLA702[DLA345]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA345])     		}
 		else if (DLA319 == 10) {
 			print(DLA202(DLA155(DLA702[DLA346]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA346])     		}
 		else if (DLA319 == 11) {
 			print(DLA202(DLA155(DLA702[DLA347]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA347])     		}
 		else if (DLA319 == 12) {
 			print(DLA202(DLA155(DLA702[DLA348]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA702[DLA348])     		}
 		else if (DLA319 == 13) {
 			print(DLA202(DLA155(DLA731[DLA349]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA349])     		}
 		else if (DLA319 == 14) {
 			print(DLA202(DLA155(DLA733[DLA350]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA733[DLA350])     		}
 		else if (DLA319 == 15) {
 			print(DLA202(DLA155(DLA735[DLA351]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA735[DLA351])     		}
 		else if (DLA319 == 16) {
 			print(DLA202(DLA155(DLA731[DLA352]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA352])     		}
 		else if (DLA319 == 17) {
 			print(DLA202(DLA155(DLA733[DLA353]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA733[DLA353])     		}
 		else if (DLA319 == 18) {
 			print(DLA202(DLA155(DLA735[DLA354]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA735[DLA354])     		}
 		else if (DLA319 == 19) {
 			print(DLA202(DLA155(DLA731[DLA355]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA355])     		}
 		else if (DLA319 == 20) {
 			print(DLA202(DLA155(DLA733[DLA356]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA733[DLA356])     		}
 		else if (DLA319 == 21) {
 			print(DLA202(DLA155(DLA735[DLA357]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA735[DLA357])     		}
 		else if (DLA319 == 22) {
 			print(DLA202(DLA155(DLA731[DLA358]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA358])     		}
 		else if (DLA319 == 23) {
 			print(DLA202(DLA155(DLA733[DLA359]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA733[DLA359])     		}
 		else if (DLA319 == 24) {
 			print(DLA202(DLA155(DLA735[DLA360]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA735[DLA360])     		}
 		else if (DLA319 == 27) {
 			print(DLA202(DLA155(DLA731[DLA363]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA363])     		}
 		else if (DLA319 == 34) {
 			print(DLA202(DLA155(DLA731[DLA370]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA370])     		}
 		else if (DLA319 == 35) {
 			print(DLA202(DLA155(DLA731[DLA371]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA371])     		}
 		else if (DLA319 == 36) {
 			print(DLA202(DLA155(DLA731[DLA372]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA372])     		}
 		else if (DLA319 == 37) {
 			print(DLA202(DLA155(DLA731[DLA373]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA731[DLA373])     		}
 		else if(DLA319 == 0){
 			print(DLA202(DLA155(DLA765[DLA374]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA374])  		}
 		else if(DLA319 == 1){
 			print(DLA202(DLA155(DLA765[DLA375]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA375])  		}
 		else if(DLA319 == 2){
 			print(DLA202(DLA155(DLA765[DLA376]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA376])  		}
 		else if(DLA319 == 3){
 			print(DLA202(DLA155(DLA765[DLA377]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA377])  		}
 		else if(DLA319 == 4){
 			print(DLA202(DLA155(DLA765[DLA378]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA378])  		}
 		else if(DLA319 == 5){
 			print(DLA202(DLA155(DLA765[DLA379]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA379])  		}
 		else if(DLA319 == 6){
 			print(DLA202(DLA155(DLA765[DLA380]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA380])  		}
 		else if(DLA319 == 7){
 			print(DLA202(DLA155(DLA765[DLA381]),OLED_FONT_SMALL_WIDTH),DLA691,OLED_FONT_SMALL,OLED_WHITE,DLA765[DLA381])  		}
 		else{
 			if (DLA138 == 1)        print(DLA202(DLA155(DLA712[1]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA712[1])      else        print(DLA202(DLA155(DLA712[0]), OLED_FONT_SMALL_WIDTH), DLA691, OLED_FONT_SMALL, OLED_WHITE, DLA712[0])     		}
 		DLA158(0);
 			}
 	return DLA138;
 	}
 function DLA143(DLA138, DLA139) {
 	if (DLA319 == DLA139) {
 		if (get_ival(PS5_L2)) {
 			if (event_press(PS5_RIGHT)) {
 				DLA138 += DLA718[DLA319][2]  				        DLA323 = TRUE;
 							}
 			if (event_press(PS5_LEFT)) {
 				DLA138 -= DLA718[DLA319][2]  				        DLA323 = TRUE;
 							}
 			if (event_press(PS5_UP)) {
 				DLA138 += DLA718[DLA319][3]  				        DLA323 = TRUE;
 							}
 			if (event_press(PS5_DOWN)) {
 				DLA138 -= DLA718[DLA319][3]  				        DLA323 = TRUE;
 							}
 			DLA138 = clamp(DLA138, DLA718[DLA319][0], DLA718[DLA319][1]);
 		}
 		DLA205(DLA138, DLA208(DLA138));
 	}
 	return DLA138;
 	}
 int DLA792, DLA793, DLA794;
 function DLA146(DLA116, DLA148, DLA149, DLA150, DLA125) {
 	DLA793 = 1;
 	DLA794 = 10000;
 	if (DLA116 < 0)  	  {
 		putc_oled(DLA793, 45);
 		DLA793 += 1;
 		DLA116 = abs(DLA116);
 			}
 	for (DLA792 = 5;
 	DLA792 >= 1;
 	DLA792--) {
 		if (DLA148 >= DLA792) {
 			putc_oled(DLA793, DLA800[DLA116 / DLA794]);
 			DLA116 = DLA116 % DLA794;
 			DLA793 += 1;
 					}
 		DLA794 /= 10;
 			}
 	puts_oled(DLA149, DLA150, DLA125, DLA793 - 1, OLED_WHITE);
 }
 const string DLA478 = " No Edit Variable";
 const string DLA477 = " A/CROSS to Edit ";
 const string DLA473 = "MOD;";
 const string DLA475 = "MSL;";
 int DLA804;
 function DLA152(DLA135) {
 	DLA135 = abs(DLA135);
 	if (DLA135 / 10000 > 0) return 5;
 	if (DLA135 / 1000 > 0) return 4;
 	if (DLA135 / 100 > 0) return 3;
 	if (DLA135 / 10 > 0) return 2;
 	return 1;
 	}
 const int8 DLA800[] =     {
 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   }
 ;
 int DLA806, DLA807;
 const image DLA809 = {
 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF }
 ;
 combo DLA72 {
 	call(DLA73);
 	DLA154();
 	wait( 2400);
 	cls_oled(0);
 	image_oled(0, 0, TRUE, TRUE, DLA809[0]);
 	wait( get_rtime());
 	wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DLA809[0]);
 	wait( get_rtime());
 	wait( 1000)call(DLA74);
 	wait( 1000);
 	DLA320 = TRUE;
 	}
 combo DLA73 {
 	cls_oled(OLED_BLACK);
 	}
 int DLA811;
 enum {
 	DLA812 = -2, DLA813, DLA814 = 5, DLA815 = -1, DLA816 = 5  }
 data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0);
 combo DLA74 {
 	wait(360);
 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50);
 	wait( 720);
 	set_rumble(RUMBLE_A, 50);
 	set_rumble(RUMBLE_B, 100);
 	wait( 720);
 	reset_rumble();
 	wait( 1560);
 	}
 const int16 DLA1150[] = {
-0, 6, 11, 17, 21, 26, 30, 33}
;
 const int16 DLA1151[] = {
0, 6, 11, 17, 23, 29, 34, 40}
;
 const int16 DLA1152[] = {
328, 328, 327, 327, 327, 326, 326, 325}
;
 const int16 DLA1153[] = {
328, 328, 327, 327, 327, 326, 326, 325}
;
 int DLA817;
 int DLA818;
 int DLA819;
 int DLA820;
 int DLA821;
 int DLA822;
 int DLA823;
 function DLA154() {
 	DLA823 = 3;
 	DLA821 = DLA823 * DLA1153[DLA822];
 	DLA820 = DLA823 * DLA1150[DLA822];
 	DLA818 = ((DLA821 * DLA1152[DLA817]) / 328) - ((DLA820 * DLA1151[DLA817]) / 328);
 	DLA819 = ((DLA821 * DLA1151[DLA817]) / 328) + ((DLA820 * DLA1152[DLA817]) / 328);
 	DLA821 = DLA818;
 	DLA820 = DLA819;
 	DLA822 += 1;
 	DLA817 += 45;
 	if(DLA822 >= 360) {
 		DLA822 %= 360;
 			}
 	if(DLA817 >= 360) {
 		DLA817 %= 360;
 			}
 	pixel_oled(64 + (((DLA821 / DLA823) * 30) / 328), 32 + (((DLA820 / DLA823) * 30) / 328), OLED_WHITE);
 	}
 int DLA827;
 function DLA155(DLA129) {
 	DLA827 = 0;
 	do {
 		DLA129++;
 		DLA827++;
 			}
 	while (duint8(DLA129));
 	return DLA827;
 	}
 int DLA830;
 const uint8 DLA1154[] = {
 	PS5_OPTIONS,  PS5_LEFT,  PS5_RIGHT,  PS5_UP,  PS5_DOWN,  PS5_CROSS,  PS5_CIRCLE,  PS5_SQUARE,  PS5_TRIANGLE,  PS5_R3,  PS5_L3,  PS5_RX,  PS5_RY,  PS5_PS,  PS5_TOUCH,  PS5_SHARE }
 ;
 function DLA157() {
 	for (DLA830 = 0;
 	DLA830 < sizeof(DLA1154) / sizeof(DLA1154[0]);
 	DLA830++) {
 		if (get_ival(DLA1154[DLA830]) || event_press(DLA1154[DLA830])) {
 			set_val(DLA1154[DLA830], 0);
 		}
 			}
 	}
 define DLA831 = 131;
 define DLA832 = 132;
 define DLA833 = 133;
 define DLA834 = 134;
 define DLA835 = 130;
 define DLA836 = 89;
 define DLA837 = 127;
 define DLA838 = 65;
 int DLA839;
 int DLA840;
 int DLA841 = 1;
 define DLA842 = 36;
 const string DLA843 = "Hold LT/L2 +";
 function DLA158(DLA159) {
 	line_oled(1, 48, 127, 48, 1, 1);
 	print(2, 52, OLED_FONT_SMALL, 1, DLA843[0]);
 	rect_oled(90, 50, 127, 60, OLED_WHITE, DLA841);
 	putc_oled(1, DLA833);
 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DLA839);
 	putc_oled(1, DLA834);
 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DLA840);
 	if (DLA159) {
 		putc_oled(1, DLA831);
 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DLA839);
 		putc_oled(1, DLA832);
 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DLA840);
 			}
 	}
 const uint8 DLA1156 [] = {
 	  PS5_R1,        	  PS5_R2,        	  PS5_R3,        	  PS5_L1,        	  PS5_L2,        	  PS5_L3,        	  PS5_TRIANGLE,  	  PS5_CIRCLE,    	  PS5_CROSS,     	  PS5_SQUARE     }
 ;
 function DLA160() {
 	DLA853 = sizeof(data);
 	DLA418 = get_pvar(SPVAR_1,0,1,0);
 	DLA424 = get_pvar(SPVAR_2,0,1,0);
 	DLA417 = get_pvar(SPVAR_3,11111, 99999,11111);
 	DLA162();
 	if (DLA187(0, 1, 0)) {
 		DLA326 = DLA187(  0, 6, 0);
 		DLA325 = DLA187(0,1,0);
 		DLA327 = DLA187(0, 70, 0);
 		DLA328 = DLA187(0, 70, 0);
 		DLA329 = DLA187(0, 70, 0);
 		DLA330 = DLA187(0, 70, 0);
 		DLA331 = DLA187(0, 1, 0);
 		DLA332 = DLA187(0, 1, 0);
 		DLA333 = DLA187(0, 1, 0);
 		DLA334 = DLA187(0, 1, 0);
 		DLA341 = DLA187(0, 1, 0);
 		DLA344 = DLA187(0, 70, 0);
 		DLA345 = DLA187(0, 70, 0);
 		DLA346 = DLA187(0, 70, 0);
 		DLA347 = DLA187(0, 70, 0);
 		DLA348 = DLA187(0, 70, 0);
 		DLA349 = DLA187(1, 25, 1);
 		DLA350 = DLA187(0, 1, 0);
 		DLA351 = DLA187(0, 1, 0);
 		DLA352 = DLA187(1, 25, 5);
 		DLA353 = DLA187(0, 1, 0);
 		DLA354 = DLA187(0, 1, 0);
 		DLA355 = DLA187(0, 25, 2);
 		DLA356 = DLA187(0, 1, 0);
 		DLA357 = DLA187(0, 1, 1);
 		DLA358 = DLA187(1, 25, 8);
 		DLA359 = DLA187(0, 1, 0);
 		DLA360 = DLA187(0, 1, 1);
 		DLA361 = DLA187(350, 600, 350);
 		DLA362 = DLA187(350, 600, 445);
 		DLA363 = DLA187(0, 22, 0);
 		DLA364 = DLA187(0, 1, 0);
 		DLA365 = DLA187(-100, 300, 0);
 		DLA335 = DLA187(0, 1, 0);
 		DLA336 = DLA187(0, 1, 0);
 		DLA337 = DLA187(0, 1, 0);
 		DLA338 = DLA187(0, 1, 0);
 		DLA366 = DLA187(-150, 150, 0);
 		DLA367 = DLA187(-150, 150, 0);
 		DLA368 = DLA187(0, 1, 0);
 		DLA369 = DLA187(-150, 150, 0);
 		DLA370 = DLA187(0, 22, 0);
 		DLA371 = DLA187(0, 22, 0);
 		DLA372 = DLA187(0, 22, 0);
 		DLA373 = DLA187(0, 22, 0);
 		DLA514 = DLA187(60, 400, 235);
 		DLA339 = DLA187(0, 1, 0);
 		DLA497 = DLA187(50, 1000, 500);
 		DLA1049 = DLA187(0, 1, 0);
 		DLA1042 = DLA187(0, 1, 0);
 		DLA102 = DLA187(0, 6, 0);
 		DLA343 = DLA187(0, 1, 0);
 		DLA324 = DLA187(0, 2, 0);
 		DLA374 = DLA187(0, 9, 9);
 		DLA375 = DLA187(0, 9, 8);
 		DLA376 = DLA187(0, 9, 3);
 		DLA377 = DLA187(0, 9, 1);
 		DLA378 = DLA187(0, 9, 4);
 		DLA379 = DLA187(0, 9, 0);
 		DLA380 = DLA187(0, 9, 7);
 		DLA381 = DLA187(0, 9, 6);
 		DLA390           = DLA187(0, 2500, 750);
 		DLA29           = DLA187(0, 1, 0);
 		DLA1051         = DLA187(0, 1, 0);
 		DLA1052       = DLA187(0, 1, 0);
 	}
 	else{
 		DLA326 = 0;
 		DLA325 = 0;
 		DLA327 = 0;
 		DLA328 = 0;
 		DLA329 = 0;
 		DLA330 = 0;
 		DLA331 = 0;
 		DLA332 = 0;
 		DLA333 = 0;
 		DLA334 = 0;
 		DLA341 = 0;
 		DLA344 = 0;
 		DLA345 = 0;
 		DLA346 = 0;
 		DLA347 = 0;
 		DLA348 = 0;
 		DLA349 = 1;
 		DLA350 = 0;
 		DLA351 = 0;
 		DLA352 = 5;
 		DLA353 = 0;
 		DLA354 = 0;
 		DLA355 = 2;
 		DLA356 = 0;
 		DLA357 = 1;
 		DLA358 = 8;
 		DLA359 = 0;
 		DLA360 = 1;
 		DLA361 = 350;
 		DLA362 = 445;
 		DLA363 = 0;
 		DLA364 = 0;
 		DLA365 = 0;
 		DLA335 = 0;
 		DLA336 = 0;
 		DLA337 = 0;
 		DLA338 = 0;
 		DLA366 = 0;
 		DLA367 = 0;
 		DLA368 = 0;
 		DLA369 = 0;
 		DLA370 = 0;
 		DLA371 = 0;
 		DLA372 = 0;
 		DLA373 = 0;
 		DLA514 = 235;
 		DLA339 = 0;
 		DLA497 = 500;
 		DLA1049 = 0;
 		DLA1042 = 0;
 		DLA102 = 0;
 		DLA343 = 0;
 		DLA324 = 0;
 		DLA374 = 9;
 		DLA375 = 8;
 		DLA376 = 3;
 		DLA377 = 1;
 		DLA378 = 4;
 		DLA379 = 0;
 		DLA380 = 7;
 		DLA381 = 6;
 		DLA390     = 750;
 		DLA29     = 0;
 		DLA1051     = 0;
 		DLA1052     = 0;
 			}
 	if (DLA324 == 0) {
 		DLA382 = PS5_CIRCLE;
 		DLA383 = PS5_CROSS;
 		DLA384 = PS5_L1;
 		DLA385 = PS5_R1;
 		DLA386 = PS5_L2;
 		DLA387 = PS5_R2;
 		DLA388 = PS5_SQUARE;
 		DLA389 = PS5_TRIANGLE;
 			}
 	else if (DLA324 == 1) {
 		DLA382      = PS5_SQUARE;
 		DLA383      = PS5_CROSS ;
 		DLA384    = PS5_L1    ;
 		DLA385  = PS5_R1;
 		DLA386    = PS5_L2;
 		DLA387    = PS5_R2;
 		DLA388     = PS5_CIRCLE;
 		DLA389  = PS5_TRIANGLE;
 	}
 	else if (DLA324 == 2) {
 		DLA382 = DLA1156[DLA374];
 		DLA383 = DLA1156[DLA375];
 		DLA384 = DLA1156[DLA376];
 		DLA385 = DLA1156[DLA377];
 		DLA386 = DLA1156[DLA378];
 		DLA387 = DLA1156[DLA379];
 		DLA388 = DLA1156[DLA380];
 		DLA389 = DLA1156[DLA381];
 			}
 	}
 function DLA161() {
 	DLA162();
 	DLA185(   1,0,     1);
 	DLA185(DLA326, 0, 6);
 	DLA185(DLA325, 0 , 1);
 	DLA185(DLA327, 0, 70);
 	DLA185(DLA328, 0, 70);
 	DLA185(DLA329, 0, 70);
 	DLA185(DLA330, 0, 70);
 	DLA185(DLA331, 0, 1);
 	DLA185(DLA332, 0, 1);
 	DLA185(DLA333, 0, 1);
 	DLA185(DLA334, 0, 1);
 	DLA185(DLA341, 0, 1);
 	DLA185(DLA344, 0, 70);
 	DLA185(DLA345, 0, 70);
 	DLA185(DLA346, 0, 70);
 	DLA185(DLA347, 0, 70);
 	DLA185(DLA348, 0, 70);
 	DLA185(DLA349, 1, 25);
 	DLA185(DLA350, 0, 1);
 	DLA185(DLA351, 0, 1);
 	DLA185(DLA352, 1, 25);
 	DLA185(DLA353, 0, 1);
 	DLA185(DLA354, 0, 1);
 	DLA185(DLA355, 0, 25);
 	DLA185(DLA356, 0, 1);
 	DLA185(DLA357, 0, 1);
 	DLA185(DLA358, 1, 25);
 	DLA185(DLA359, 0, 1);
 	DLA185(DLA360, 0, 1);
 	DLA185(DLA361, 350, 600);
 	DLA185(DLA362, 350, 600);
 	DLA185(DLA363, 0, 22);
 	DLA185(DLA364, 0, 1);
 	DLA185(DLA365, -100, 300);
 	DLA185(DLA335, 0, 1);
 	DLA185(DLA336, 0, 1);
 	DLA185(DLA337, 0, 1);
 	DLA185(DLA338, 0, 1);
 	DLA185(DLA366, -150, 150);
 	DLA185(DLA367, -150, 150);
 	DLA185(DLA368, 0, 1);
 	DLA185(DLA369, -150, 150);
 	DLA185(DLA370, 0, 22);
 	DLA185(DLA371, 0, 22);
 	DLA185(DLA372, 0, 22);
 	DLA185(DLA373, 0, 22);
 	DLA185(DLA514, 60, 400);
 	DLA185(DLA339, 0, 1);
 	DLA185(DLA497, 50, 1000);
 	DLA185(DLA1049, 0, 1);
 	DLA185(DLA1042, 0, 1);
 	DLA185(DLA102, 0, 6);
 	DLA185(DLA343, 0, 1);
 	DLA185(DLA324, 0, 2);
 	DLA185(DLA374, 0, 9);
 	DLA185(DLA375, 0, 9);
 	DLA185(DLA376, 0, 9);
 	DLA185(DLA377, 0, 9);
 	DLA185(DLA378, 0, 9);
 	DLA185(DLA379, 0, 9);
 	DLA185(DLA380, 0, 9);
 	DLA185(DLA381, 0, 9);
 	DLA185(DLA390 ,         0,2500);
 	DLA185(DLA29,           0,1);
 	DLA185(DLA1051,           0,1);
 	DLA185(DLA1052,           0,1);
 	}
 function DLA162() {
 	DLA860 = SPVAR_4;
 	DLA861 = 0;
 	DLA863 = 0;
 	}
 int DLA861,  DLA860, DLA863, DLA864, DLA865;
 function DLA163(DLA164) {
 	DLA864 = 0;
 	while (DLA164) {
 		DLA864++;
 		DLA164 = abs(DLA164 >> 1);
 	}
 	return DLA864;
 	}
 function DLA165(DLA166, DLA167) {
 	DLA864 = max(DLA163(DLA166), DLA163(DLA167));
 	if (DLA168(DLA166, DLA167)) {
 		DLA864++;
 	}
 	return DLA864;
 	}
 function DLA168(DLA166, DLA167) {
 	return DLA166 < 0 || DLA167 < 0;
 	}
 function DLA171(DLA172) {
 	return 1 << clamp(DLA172 - 1, 0, 31);
 	}
 function DLA173(DLA172) {
 	if (DLA172 == 32) {
 		return -1;
 			}
 	return 0x7FFFFFFF >> (31 - DLA172);
 }
 function DLA175(DLA172) {
 	return DLA173(DLA172 - 1);
 	}
 function DLA177(DLA164, DLA172) {
 	if (DLA164 < 0) {
 		return (abs(DLA164) & DLA175(DLA172)) | DLA171(DLA172);
 	}
 	return DLA164 & DLA175(DLA172);
 }
 function DLA180(DLA164, DLA172) {
 	if (DLA164 & DLA171(DLA172)) {
 		return 0 - (DLA164 & DLA175(DLA172));
 	}
 	return DLA164 & DLA175(DLA172);
 }
 function DLA183(DLA184) {
 	return get_pvar(DLA184, 0x80000000, 0x7FFFFFFF, 0);
 	}
 function DLA185(DLA164, min, max) {
 	DLA865 = DLA165(min, max);
 	DLA164 = clamp(DLA164, min, max);
 	if (DLA168(min, max)) {
 		DLA164 = DLA177(DLA164, DLA865);
 	}
 	DLA164 = DLA164 & DLA173(DLA865);
 	if (DLA865 >= 32 - DLA861) {
 		DLA863 = DLA863 | (DLA164 << DLA861);
 		set_pvar(DLA860, DLA863);
 		DLA860++;
 		DLA865 -= (32 - DLA861);
 		DLA164 = DLA164 >> (32 - DLA861);
 		DLA861 = 0;
 		DLA863 = 0;
 	}
 	DLA863 = DLA863 | (DLA164 << DLA861);
 	DLA861 += DLA865;
 	if (!DLA861) {
 		DLA863 = 0;
 	}
 	set_pvar(DLA860, DLA863);
 }
 function DLA187(min, max, DLA188) {
 	DLA865 = DLA165(min, max);
 	DLA863 = (DLA183(DLA860) >> DLA861) & DLA173(DLA865);
 	if (DLA865 >= 32 - DLA861) {
 		DLA863 = (DLA863 & DLA173(32 - DLA861)) | ((DLA183(DLA860 + 1) & DLA173(DLA865 - (32 - DLA861))) << (32 - DLA861));
 	}
 	DLA861 += DLA865;
 	DLA863 = DLA863 & DLA173(DLA865);
 	if (DLA861 >= 32) {
 		DLA860++;
 		DLA861 -= 32;
 	}
 	if (DLA168(min, max)) {
 		DLA863 = DLA180(DLA863, DLA865);
 	}
 	if (DLA863 < min || DLA863 > max) {
 		return DLA188;
 	}
 	set_val(TRACE_2,sizeof(data)) 		if(DLA190[217] != 4145){
 		DLA187(min, max, DLA188);
 	}
 	return DLA863;
 	}
 const string DLA892 = "SETTINGS";
 const string DLA893 = "WAS SAVED";
 combo DLA75 {
 	wait( 20);
 	cls_oled(0);
 	DLA161();
 	print(15, 2, OLED_FONT_MEDIUM, 1, DLA892[0]);
 	print(10, 23, OLED_FONT_MEDIUM, 1, DLA893[0]);
 	DLA894 = 1500;
 	combo_run(DLA76);
 	}
 int DLA894 = 1500;
 combo DLA76 {
 	wait( DLA894);
 	cls_oled(0);
 	DLA322 = FALSE;
 	}
 define DLA895 = 0;
 define DLA896 = 1;
 define DLA897 = 2;
 define DLA898 = 3;
 define DLA899 = 4;
 define DLA900 = 5;
 define DLA901 = 6;
 define DLA902 = 7;
 int DLA580;
 int DLA904;
 int DLA905, DLA563;
 int DLA398;
 int DLA908 = 50;
 int DLA909 = 200;
 int DLA910 = TRUE;
 combo DLA77 {
 	set_val(DLA383, 0);
 	set_val(PS5_L3, 100);
 	set_val(PS5_R3, 100);
 	wait( 60);
 	set_val(DLA383, 0);
 	wait( 120);
 	if (DLA364) DLA230();
 	wait( 50);
 	wait( 50);
 	}
 int DLA520;
 int DLA527;
 combo DLA78 {
 	if (DLA527) set_val(XB1_LX, 100);
 	else set_val(XB1_LX, -100);
 	wait( 70);
 	if (DLA527) set_val(XB1_RX, 100);
 	else set_val(XB1_RX, -100);
 	set_val(XB1_RY, 100);
 	wait( 2000);
 	if (DLA527) set_val(XB1_RX, -100);
 	else set_val(XB1_RX, 100);
 	wait( 50);
 	wait( 200);
 	set_val(DLA382, 100);
 	wait( DLA361);
 	if (DLA527) set_val(XB1_LX, 100);
 	else set_val(XB1_LX, 100);
 	set_val(XB1_LY,100);
 	wait( 50);
 	wait( 1200);
 	DLA520 = FALSE;
 	DLA214(DLA520);
 	}
 int DLA529;
 int DLA530;
 combo DLA79 {
 	if (DLA530) set_val(XB1_RX, -100);
 	else set_val(XB1_RX, 100);
 	set_val(XB1_RY, 100);
 	wait( 320);
 	wait( 50);
 	set_val(XB1_RY, -60);
 	wait( 1100);
 	wait( 50);
 	if (DLA530) set_val(XB1_LX, 60);
 	else set_val(XB1_LX, -60);
 	wait( 120);
 	wait( 50);
 	set_val(XB1_LY, -100);
 	set_val(DLA388, 100);
 	set_val(DLA385, 100);
 	set_val(DLA386, 100);
 	DLA1022 = 4000;
 	wait( DLA362);
 	wait( 50);
 	set_val(DLA388, 100);
 	wait( 50);
 	DLA529 = FALSE;
 	DLA214(DLA529);
 	}
 int DLA915 = TRUE;
 function DLA189(DLA190) {
 	if (DLA190) {
 		DLA916 = DLA947;
 			}
 	else {
 		DLA916 = DLA946;
 			}
 	combo_run(DLA80);
 	}
 int DLA916;
 combo DLA80 {
 	DLA210(DLA916);
 	wait( 300);
 	DLA210(DLA944);
 	wait( 100);
 	DLA210(DLA916);
 	wait( 300);
 	DLA210(DLA944);
 	}
 define DLA920 = 100;
 define DLA921 = 130;
 const string DLA457 = "SCRIPT WAS";
 function DLA191(DLA116, DLA193, DLA194) {
 	if (!DLA316 && !DLA317) {
 		cls_oled(0);
 		print(DLA193, 3, OLED_FONT_MEDIUM, OLED_WHITE, DLA194);
 		if (DLA116) {
 			print(DLA195(sizeof(DLA925) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DLA925[0]);
 		}
 		else {
 			print(DLA195(sizeof(DLA926) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DLA926[0]);
 		}
 		DLA189(DLA116);
 			}
 	}
 function DLA195(DLA131, DLA125) {
 	return (OLED_WIDTH / 2) - ((DLA131 * DLA125) / 2);
 	}
 const string DLA926 = "OFF";
 const string DLA925 = "ON";
 function DLA198(DLA122, DLA200, DLA116) {
 	cls_oled(0);
 	line_oled(1, 18, 127, 18, 1, 1);
 	print(DLA122, 0, OLED_FONT_MEDIUM, OLED_WHITE, DLA200);
 	DLA205(DLA116, DLA208(DLA116));
 	DLA320 = TRUE;
 	}
 const string DLA501 = "EA PING";
 const string DLA522 = "FK_POWER";
 const string DLA515 = "MaxFnshPwr"const string DLA507 = "JK_Agg";
 int DLA497;
 int DLA514;
 function DLA202(DLA131, DLA125) {
 	return (OLED_WIDTH / 2) - ((DLA131 * DLA125) / 2);
 	}
 int DLA935;
 int DLA936, DLA937;
 function DLA205(DLA116, DLA148) {
 	DLA935 = 1;
 	DLA937 = 10000;
 	if (DLA116 < 0) {
 		putc_oled(DLA935, 45);
 		DLA935 += 1;
 		DLA116 = abs(DLA116);
 			}
 	for (DLA936 = 5;
 	DLA936 >= 1;
 	DLA936--) {
 		if (DLA148 >= DLA936) {
 			putc_oled(DLA935, (DLA116 / DLA937) + 48);
 			DLA116 %= DLA937;
 			DLA935++;
 			if (DLA936 == 4) {
 				putc_oled(DLA935, 44);
 				DLA935++;
 							}
 					}
 		DLA937 /= 10;
 			}
 	puts_oled(DLA202(DLA935 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DLA935 - 1, OLED_WHITE);
 	}
 int DLA941;
 function DLA208(DLA209) {
 	DLA941 = 0;
 	do {
 		DLA209 /= 10;
 		DLA941++;
 			}
 	while (DLA209);
 	return DLA941;
 	}
 define DLA944 = 0;
 define DLA945 = 1;
 define DLA946 = 2;
 define DLA947 = 3;
 define DLA948 = 4;
 define DLA949 = 5;
 define DLA950 = 6;
 define DLA951 = 7;
 const int16 data[][] = {
 	{
 		0,    0,    0   	}
 	,  	  {
 		0,    0,    255   	}
 	,  	  {
 		255,    0,    0   	}
 	,  	  {
 		0,    255,    0   	}
 	,  	  {
 		255,    0,    255   	}
 	,  	  {
 		0,    255,    255   	}
 	,  	  {
 		255,    255,    0   	}
 	,  	  {
 		255,    255,    255   	}
 }
 ;
 int DLA952;
 function DLA210(DLA211) {
 	for (DLA952 = 0;
 	DLA952 < 3;
 	DLA952++) {
 		set_rgb(data[DLA211][0], data[DLA211][1], data[DLA211][2]);
 			}
 	}
 const int8 DLA1166[] = {
 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS5_R1,  	  PS5_R2,  	  XB1_RS,  	  PS5_L1,  	  PS5_L2,  	  XB1_LS,  	  PS5_UP,  	  PS5_DOWN,  	  PS5_LEFT,  	  PS5_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS5_TOUCH  }
 int DLA533 = PS5_L3;
 define DLA954 = 1;
 define DLA955 = 2;
 define DLA956 = 3;
 define DLA957 = 2;
 define DLA958 = 3;
 define DLA959 = 4;
 define DLA960 = 5;
 define DLA961 = 6;
 define DLA962 = 7;
 define DLA963 = 8;
 define DLA964 = 9;
 define DLA965 = PS5_LX;
 define DLA966 = PS5_LY;
 define DLA967 = PS5_RX;
 define DLA968 = PS5_RY;
 int DLA969;
 int DLA411 = 0;
 function DLA212(DLA213) {
 	DLA904 = DLA213;
 	DLA190[-303 + (DLA213 * 3)] = TRUE;
 	DLA910 = FALSE;
 	block = TRUE;
 	}
 int DLA972;
 combo DLA81 {
 	set_rumble(DLA972, 100);
 	wait( 300);
 	reset_rumble();
 	wait( 20);
 	}
 function DLA214(DLA116) {
 	if (DLA116) DLA972 = RUMBLE_A;
 	else DLA972 = RUMBLE_B;
 	combo_run(DLA81);
 	}
 int DLA973 = 300;
 int DLA974 ;
 combo DLA82 {
 	DLA974 = TRUE;
 	wait( DLA973);
 	DLA974 = FALSE;
 	}
 combo DLA83 {
 	DLA216();
 	DLA224(0, 0);
 	wait( 20);
 	DLA224(0, 0);
 	wait( 100);
 	DLA224(0, 0);
 	set_val(DLA387, 100);
 	DLA224(0, 0);
 	wait( 60);
 	DLA224(0, 0);
 	wait( 150);
 	DLA910 = TRUE;
 	wait( 350);
 	}
 function DLA216() {
 	DLA580 = DLA499  DLA219(DLA580);
 	DLA905 = DLA978;
 	DLA563 = DLA566;
 	}
 combo DLA84 {
 	set_val(DLA386, 100);
 	set_val(DLA385, 100);
 	wait( 100);
 	set_val(DLA386, 100);
 	wait( 100);
 	DLA910 = TRUE;
 	wait( 350);
 	}
 const int8 DLA1167[][] = {
 {
 		0,    -99   	}
 	,  	  {
 		98,    -100   	}
 	,  	  {
 		97,    0   	}
 	,  	  {
 		96,    99   	}
 	,  	  {
 		0,    99   	}
 	,  	  {
 		-96,    98   	}
 	,  	  {
 		-95,    0   	}
 	,  	  {
 		-94,    -96   	}
 }
 ;
 int DLA978, DLA566, DLA499;
 int DLA413;
 int DLA414;
 int DLA983;
 function DLA217(DLA218) {
 	DLA983 = DLA218;
 	if (DLA983 < 0) DLA983 = 8 - abs(DLA218);
 	else if (DLA983 >= 8) DLA983 = DLA218 - 8  return DLA983;
 	}
 function DLA219(DLA220) {
 	if (DLA220 < 0) DLA220 = 8 - abs(DLA220);
 	else if (DLA220 >= 8) DLA220 = DLA220 - 8;
 	DLA978 = DLA1167[DLA220][0];
 	DLA566 = DLA1167[DLA220][1];
 }
 function DLA221(DLA222, DLA223) {
 	set_val(DLA967, DLA222);
 	set_val(DLA968, DLA223);
 	}
 function DLA224(DLA225, DLA226) {
 	set_val(DLA965, DLA225);
 	set_val(DLA966, DLA226);
 	}
 function DLA227() {
 	if (DLA398) {
 		set_val(DLA965, inv(DLA563));
 		set_val(DLA966, DLA905);
 			}
 	else {
 		set_val(DLA965, DLA563);
 		set_val(DLA966, inv(DLA905));
 			}
 	}
 function DLA228() {
 	if (DLA398) {
 		set_val(DLA967, inv(DLA563));
 		set_val(DLA968, DLA905);
 			}
 	else {
 		set_val(DLA967, DLA563);
 		set_val(DLA968, inv(DLA905));
 			}
 	}
 function DLA229() {
 	if (!DLA398) {
 		set_val(DLA967, inv(DLA563));
 		set_val(DLA968, DLA905);
 			}
 	else {
 		set_val(DLA967, DLA563);
 		set_val(DLA968, inv(DLA905));
 			}
 	}
 function DLA230() {
 	set_val(DLA967, DLA905);
 	set_val(DLA968, DLA563);
 	}
 function DLA231() {
 	set_val(DLA967, inv(DLA905));
 	set_val(DLA968, inv(DLA563));
 	}
 function DLA232() {
 	set_val(DLA967, 0);
 	set_val(DLA968, 0);
 	}
 int DLA999;
 function DLA233() {
 	if ((event_press(DLA383)  ) && !combo_running(DLA85) && (DLA1022  <= 0 || (DLA1022 < 3000 && DLA1022 > 1  )) && !get_ival(DLA387) && DLA459 > 500 &&!get_ival(DLA386) &&!get_ival(DLA382) &&!get_ival(DLA385) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DLA85) ) {
 		combo_run(DLA85);
 			}
 	if (combo_running(DLA85) && (        get_ival(DLA387) ||        get_ival(DLA386) ||        get_ival(DLA382) ||        get_ival(DLA385) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_ipolar(POLAR_RS,POLAR_RADIUS) >= 1500      )) {
 		combo_stop(DLA85);
 			}
 	}
 combo DLA85 {
 wait(750);
 set_val(DLA384,100);
 wait(60);
 wait(60);
 if(DLA999 == 1 ){
 set_val(XB1_RX,100)}
else{
set_val(XB1_RX,-100)}
 wait(60);
 wait(60);
 	}
 combo DLA86 {
 	wait( 800);
 	DLA1023 = 0;
 	}
 int DLA1000 = 1600;
 int DLA1001 = 1600;
 int DLA1002 = 1600;
 int DLA1003 = TRUE;
 int DLA1004 = TRUE;
 int DLA558 = FALSE;
 int DLA1006 = TRUE;
 int DLA552 = FALSE;
 int DLA1008 = TRUE;
 int DLA554 = FALSE;
 int DLA1010 = TRUE;
 int DLA556 = FALSE;
 function DLA234(){
 	if (get_ival(DLA384)) {
 		DLA1012 = 1000;
 		DLA1031 = 0;
 		DLA459 = 1;
 		combo_stop(DLA94);
 			}
 	if (event_press(DLA386) || event_press(DLA371)) {
 		DLA1012 = 4000;
 		DLA1031 = 0;
 		DLA1000 = 1600;
 			}
 	if (get_ival(DLA385) && !get_ival(DLA384) ) {
 		DLA1012 = 0;
 		DLA1031 = 0;
 		DLA1000 = 1600;
 			}
 	else if (get_ival(DLA384)){
 		DLA1012 = 1000;
 			}
 	if (DLA1012 > 0) {
 		DLA1012 -= get_rtime();
 			}
 	if (DLA1012 < 0) {
 		DLA1012 = 0;
 			}
 	DLA1066 = DLA365;
 	if (event_release(DLA383)) {
 		DLA1018 = 1;
 		DLA1031 = 0;
 		DLA459 = 1;
 	}
 	if (event_release(DLA389)) {
 		DLA1019 = 1;
 		DLA1031 = 0;
 		DLA459 = 1;
 	}
 	if (event_release(DLA384)) {
 		DLA1001 = 1;
 		DLA1031 = 0;
 		DLA1000 = 1600;
 			}
 	if (event_release(DLA385)) {
 		DLA1002 = 1;
 		DLA1031 = 0;
 		DLA1000 = 1600;
 			}
 	if (event_release(DLA388) || (get_ival(DLA389) && get_ival(DLA384))) {
 		DLA1022 = 4000;
 		DLA1031 = 0;
 	}
 	if (get_ival(DLA383) && DLA1022 < 4000 && DLA1022 > 3500) {
 		DLA1023 = DLA1022;
 		DLA1022 = 0;
 			}
 	if (DLA1000 < 1510) {
 		DLA1000 += get_rtime();
 			}
 	if (DLA1001 < 1600) {
 		DLA1001 += get_rtime();
 			}
 	if (DLA1002 < 1600) {
 		DLA1002 += get_rtime();
 			}
 	if (DLA1022 > 0) {
 		DLA1022 -= get_rtime();
 			}
 	if (DLA1022 < 0) {
 		DLA1022 = 0;
 			}
 	if (DLA1018 < 5100) {
 		DLA1018 += get_rtime();
 			}
 	if (DLA1019 < 4100) {
 		DLA1019 += get_rtime();
 			}
 	if (DLA1031 > 0) {
 		DLA1031 -= get_rtime();
 			}
 	if (DLA1031 < 0) {
 		DLA1031 = 0;
 			}
 	if (abs(get_ival(PS5_RX)) > 30 || abs(get_ival(PS5_RY)) > 30) {
 		DLA1000 = 1;
 		DLA1031 = 0;
 			}
 	if (combo_running(DLA91)) {
 		set_val(DLA383, 0);
 		if(get_ival(DLA383)){
 			set_val(DLA383, 0);
 			combo_stop(DLA91);
 			combo_run(DLA49);
 					}
 			}
 	if ((combo_running(DLA96) || combo_running(DLA87))) {
 		set_val(DLA383, 0);
 		if(get_ival(DLA383)){
 			DLA459 = 1;
 			set_val(DLA383, 0);
 			combo_stop(DLA96);
 			combo_stop(DLA87);
 			combo_run(DLA49);
 					}
 			}
 	if (event_press(DLA382)) {
 		combo_run(DLA86);
 			}
 	if (DLA459 > 1500) {
 		if (DLA1001 < 1500) {
 			DLA1035 = 120;
 					}
 		if (DLA1002 < 1500) {
 			DLA1035 = 228;
 					}
 		else {
 			DLA1035 = 200;
 					}
 			}
 	if (DLA459 < 1500) {
 		DLA1035 = 450;
 			}
 	if (DLA459 > 2700) {
 		DLA1039 = 920;
 			}
 	else if (DLA459 >= 0 && DLA459 < 2700) {
 		DLA1039 = 725;
 			}
 	}
 function DLA235() {
 	if (DLA1003) {
 		if ((DLA459 <= 600 || (DLA1000 <= 1500 && DLA1000 > 1) || ( DLA1001 <= 150 || DLA1002 <= 150)) && event_press(DLA382) ) {
 			if (!get_ival(DLA385) && !get_ival(DLA384) && !get_ival(DLA386) && !get_ival(DLA387)) {
 				set_val(DLA382, 0);
 				if (DLA1022 < 4000 && DLA1022 > 1) {
 					set_val(DLA382, 0);
 					combo_run(DLA89);
 									}
 				else {
 					set_val(DLA382, 0);
 					combo_run(DLA87);
 					DLA1031 = 9000;
 				}
 							}
 					}
 			}
 	if (DLA1010) {
 		if (DLA459 > 1000 && !DLA1031 && (!get_ival(DLA385) && !get_ival(PS5_L3) && event_press(DLA382)) &&  DLA1001 > 150 &&  DLA1002 > 150) {
 			if (!get_ival(DLA384) && !get_ival(DLA386)) {
 				set_val(DLA382, 0);
 				if (((DLA1019 > 1 && DLA1019 <= 2500) || (DLA1018 > 1 && DLA1018 <= 3000)) &&  DLA1000 != 1600) {
 					set_val(DLA382, 0);
 					combo_run(DLA88);
 					DLA1031 = 9000;
 									}
 				else if (((DLA1019 > 2500 && DLA1019 <= 4000) || (DLA1018 > 3000 && DLA1018 <= 3500))  &&  DLA1000 != 1600) {
 					set_val(DLA382, 0);
 					combo_run(DLA87);
 					DLA1031 = 9000;
 									}
 				else if ((DLA1022 < 4000 && DLA1022 > 1)) {
 					set_val(DLA382, 0);
 					combo_run(DLA89);
 					DLA1031 = 9000;
 									}
 				else {
 					set_val(DLA382, 0);
 					DLA236();
 					DLA1031 = 9000;
 									}
 				DLA1031 = 9000;
 							}
 					}
 			}
 	if (DLA1004) {
 		if (get_ival(DLA384) && get_ival(DLA385)) {
 			if (!get_ival(DLA386) && !get_ival(DLA387) && (DLA1022 && DLA1018 > 1 && DLA1018 <= 1500) || (!DLA1022 && DLA1018 > 1 && DLA1018 <= 1500) || (DLA1018 > 1500 && !DLA1022) && !DLA1031) {
 				if (event_press(DLA382)) {
 					set_val(DLA382, 0);
 					combo_run(DLA97);
 					DLA1031 = 9000;
 									}
 							}
 					}
 			}
 	if (DLA1008) {
 		if (!get_ival(DLA387) && !get_ival(DLA384) && !get_ival(DLA385)) {
 			if (get_ival(DLA386) && get_ival(DLA382)) {
 				DLA237();
 				set_val(DLA382, 0);
 				DLA1031 = 9000;
 							}
 					}
 			}
 	if (DLA1006) {
 		if (get_ival(DLA385) && !get_ival(DLA384) && !DLA1012) {
 			if (!get_ival(DLA386) && !get_ival(DLA387) && !DLA1031) {
 				if (get_ival(DLA382) && DLA459 >= 1000) {
 					set_val(DLA382, 0);
 					combo_run(DLA94);
 					DLA1031 = 9000;
 									}
 				if (get_ival(DLA382) && DLA459 < 1000 && !DLA1012) {
 					set_val(DLA382, 0);
 					combo_run(DLA95);
 									}
 							}
 					}
 			}
 	if (get_ival(DLA384) || DLA1012 > 0) {
 		combo_stop(DLA94);
 		combo_stop(DLA96);
 		combo_stop(DLA95);
 			}
 	if (combo_running(DLA87) || combo_running(DLA91) || combo_running(DLA96) || combo_running(DLA97) || combo_running(DLA94)) {
 		if (get_ival(DLA383) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DLA387) > 30) {
 			combo_stop(DLA91);
 			combo_stop(DLA96);
 			combo_stop(DLA97);
 			combo_stop(DLA94);
 			combo_stop(DLA87);
   		}
 			}
 	if (combo_running(DLA87) || combo_running(DLA88)) {
 		if (get_ival(DLA383) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DLA387)) {
 			combo_stop(DLA89);
 			combo_stop(DLA88);
 			combo_stop(DLA87);
    		}
 			}
 	if (event_press(DLA382) && DLA1031 > 100 && DLA1031 < 8990) {
 		set_val(DLA382, 0);
 		combo_stop(DLA91);
 		combo_stop(DLA96);
 		combo_stop(DLA97);
 		combo_stop(DLA94);
 		combo_stop(DLA87);
         combo_run(DLA90);
 			}
 	if (!DLA558) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DLA97);
 					}
 			}
 	if (!DLA552) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DLA94);
 					}
 			}
 	if (!DLA554) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DLA91);
     		}
 			}
 	if (!DLA556) {
 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
 			combo_stop(DLA96);
 					}
 			}
 	if ((get_ival(DLA387) || get_ival(DLA383)) && !DLA326) {
 		combo_stop(DLA4);
 		combo_stop(DLA47);
 		combo_stop(DLA33);
 			}
 	}
 int DLA1042;
 combo DLA87 {
 	set_val(DLA384,0);
 	set_val(DLA382, 100);
 	wait( random(210, 215) + DLA367);
 	set_val(DLA382, 0);
 	wait(600);
 	wait( 2000);
 	}
 function DLA236() {
 	if (DLA459 > 600 && DLA459 <= 800) {
 		DLA1043 = 240;
 			}
 	if (DLA459 > 800 && DLA459 <= 1000) {
 		DLA1043 = 230;
 			}
 	if (DLA459 > 1000 && DLA459 <= 1500) {
 		DLA1043 = 225;
 			}
 	if (DLA459 > 1500 && DLA459 <= 2000) {
 		DLA1043 = 235;
 			}
 	if (DLA459 > 2000) {
 		DLA1043 = 218;
 			}
 	combo_run(DLA96);
 	}
 combo DLA88 {
 	set_val(DLA382, 100);
 	wait( random(170, 190));
 	set_val(DLA382, 0);
 	wait( 500);
 	}
 combo DLA89 {
 	set_val(DLA382, 100);
 	wait( 205);
 	set_val(DLA382, 0);
 	wait( 300);
 	}
 combo DLA90 {
 	set_val(DLA382, 100);
 	wait( 190);
 	set_val(DLA382, 0);
 	wait( 400);
 	}
 int DLA1048;
 int DLA1049;
 int DLA29;
 int DLA1051;
 int DLA1052;
 int DLA1053;
 int DLA1054;
 combo DLA91 {
 	if (DLA1049) {
 		set_val(DLA382, 0);
 		DLA1053 = 400;
 			}
 	else {
 		DLA1053 = 0;
 			}
 	if (DLA1049) {
 		DLA221(0, DLA1054);
 		DLA224(0, 0);
 			}
 	wait(DLA1053);
 	if (DLA1049) {
 		set_val(DLA386, 100);
 		set_val(DLA385, 0);
 		DLA1053 = 60;
 			}
 	else {
 		set_val(DLA385, 100);
 		DLA1053 = 60;
 			}
 	set_val(DLA382,0);
 	wait(DLA1053);
 	set_val(DLA386, 0);
 	set_val(DLA385, 0);
 	set_val(DLA382,0);
 	wait(DLA1053);
 	if (DLA1049) {
 		DLA1053 = 0;
 			}
 	else {
 		DLA1053 = 60;
 			}
 	set_val(DLA385, 0);
 	set_val(DLA386, 0);
 	set_val(DLA382,0);
 	wait(DLA1053);
 	set_val(DLA382, 100);
 	set_val(DLA386, 100);
 	wait(random(265, 268) +   DLA366 );
 	set_val(DLA386, 100);
 	set_val(DLA382, 0);
 	if (DLA1049) {
 		DLA1053 = 25;
 			}
 	else {
 		DLA1053 = 30;
 			}
 	wait(random(0,2) + DLA1067 + DLA1066 + DLA1053 );
 	set_val(DLA386, 100);
 	set_val(DLA382, 100);
 	wait(random(0,2) + 60);
 	set_val(DLA382, 0);
 	set_val(DLA386, 100);
 	wait(random(0,2) + 80);
 	set_val(DLA386, 100);
 	wait(2500);
 	}
 int DLA1019;
 int DLA1022;
 int DLA459;
 int DLA1018;
 int DLA1066;
 int DLA1067 = 111;
 int DLA1012;
 int DLA1023;
 int DLA1070;
 function DLA237() {
 	DLA1070 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
 	if ((DLA1070 > 10 && DLA1070 < 80) || (DLA1070 > 110 && DLA1070 < 170)) {
 		DLA1054 = 100;
 			}
 	if ((DLA1070 < 350 && DLA1070 > 280) || (DLA1070 < 260 && DLA1070 > 190)) {
 		DLA1054 = -100;
 			}
 	if (DLA1022 == 0 && (DLA459 >= 750 || ((DLA1023 > 3000 && DLA1018 > 1 && DLA1018 < 5000)))) {
 		if (DLA459 <= 2000 && DLA1018 > 1500) {
 			set_val(DLA382, 0);
 			DLA1067 = 170;
 		}
 		if (DLA459 <= 2000 && DLA1018 > 1 && DLA1018 <= 1500) {
 			set_val(DLA382, 0);
 			DLA1067 = 202;
 					}
 		if (DLA459 > 2000 || (DLA1018 > 1 && DLA1018 <= 1500)) {
 			set_val(DLA382, 0);
 			DLA1067 = 151;
 					}
 		if ((DLA459 > 2000 && DLA1018 > 1500) || DLA1023 > 1 && DLA1018 > 1) {
 			set_val(DLA382, 0);
 			DLA1067 = 152;
 					}
 		if ((DLA459 < 2000 && DLA1018 > 1500) || DLA1022 > 1 && DLA1018 > 1) {
 			set_val(DLA382, 0);
 			DLA1067 = 149;
 					}
 		if (DLA1018 > 1500) {
 			set_val(DLA382, 0);
 			DLA1067 = 148;
 					}
 		if (!DLA459 > 2000 && DLA1023 > 1 && DLA1018 > 1 && DLA1018 <= 1500) {
 			set_val(DLA382, 0);
 			DLA1067 = 147;
 					}
 		set_val(DLA382, 0);
 		combo_stop(DLA96);
 		combo_stop(DLA97);
 		combo_stop(DLA94);
 		combo_stop(DLA87);
 		combo_stop(DLA93);
 		combo_stop(DLA90);
 		combo_stop(DLA89);
 		combo_run(DLA91);
 			}
 	else {
 		if (DLA1022) {
 			set_val(DLA382, 0);
 			combo_run(DLA92);
 					}
 		else {
 			if (DLA459 < 750) {
 				set_val(DLA382, 0);
 				combo_run(DLA93);
 							}
 					}
 			}
 }
 combo DLA92 {
 	set_val(DLA382, 100);
 	wait(random(0, 6) + random(200, 205));
 	set_val(DLA382, 0);
 	wait(random(0, 6) + 700);
 	}
 combo DLA93 {
 	set_val(DLA382, 100);
 	wait( random(200, 205) + DLA366 )  set_val(DLA382, 0);
 	wait( 700);
 	}
 int DLA1074 = 246;
 int DLA1035 = 150;
 int DLA1076 = 0;
 combo DLA94 {
 	set_val(DLA386, 100);
 	set_val(DLA385, 0);
 	set_val(DLA382,0);
 	wait(random(0,2) + 60);
 	set_val(DLA386, 0);
 	set_val(DLA385, 0);
 	set_val(DLA382,0);
 	wait(random(0,2) + 60);
 	DLA1076 = DLA365;
 	set_val(DLA385, 100);
 	set_val(DLA382,0);
 	wait( 60);
 	set_val(DLA385, 65);
 	set_val(DLA382, 100);
 	wait( DLA1074 + random(-2, 2) +  DLA369);
 	set_val(DLA385, 75);
 	set_val(DLA382, 0);
 	DLA1048 = DLA1035;
 	wait( DLA1035 + DLA1076 - 58 + DLA392 );
 	set_val(DLA385, 85);
 	if(DLA1042)set_val(DLA382, 100);
 	wait( 60);
 	set_val(DLA385, 100);
 	set_val(DLA382, 0);
 	wait( 3000);
 	}
 combo DLA95 {
 	set_val(DLA385, 100);
 	set_val(DLA382, 100);
 	wait( 160 + DLA369 );
 	set_val(DLA385, 100);
 	set_val(DLA382, 0);
 	wait( 3000);
 	}
 int DLA1031;
 int DLA1078 = 220;
 int DLA1043;
 int DLA1080 = 0;
 combo DLA96 {
 	DLA1080 = DLA365;
 	set_val(DLA382, 100);
 	wait( DLA1078);
 	set_val(DLA382, 0);
 	wait( DLA1043 + (DLA1080) + 22 + DLA393);
 	if(DLA1042)set_val(DLA382, 100);
 	wait( DLA1078);
 	set_val(DLA382, 0);
 	wait( 2000);
 	}
 int DLA1082 = TRUE;
 int DLA1039;
 int DLA1084 = 260;
 int DLA1085 = 0;
 combo DLA97 {
 	set_val(DLA384, 100);
 	set_val(DLA385, 100);
 	if (DLA1082) {
 		DLA1085 = DLA365;
 			}
 	else {
 		DLA1085 = 0   	}
 	set_val(DLA382, 100);
 	wait( DLA1084);
 	wait( DLA1039 + DLA1085 + 40)  }
 int DLA1088;
 define DLA1089 = TRUE;
 define DLA1090 = 95;
 define DLA1091 = 10;
 define DLA1092 = 70;
 define DLA1093 = FALSE;
 define DLA1094 = 50;
 define DLA1095 = 95;
 define DLA1096 = XB1_LT;
 define DLA1097 = XB1_RT;
 define DLA1098 = XB1_LX;
 define DLA1099 = XB1_LY;
 define DLA1100 = POLAR_LS;
 int DLA1101;
 function DLA238() {
 	if (    get_ival(DLA387) > 30 &&    (get_ival(DLA386) || get_ival(DLA383)) &&    (!get_ival(DLA388) || !get_ival(DLA382))  ) {
 set_val(DLA387, 0);
 		if(!get_ival(DLA382)){
 			set_val(DLA386,100);
 					}
 		else{
 			set_val(DLA386,0);
 					}
 		  combo_run(DLA99);
 		if(DLA368 == TRUE){
 			combo_run(DLA98);
 					}
 			}
 	else {
 		combo_stop(DLA99);
 		combo_stop(DLA98);
 			}
 	}
 combo DLA98 {
 	if (DLA368 == TRUE) {
 		set_val(DLA385, 100);
 		DLA1102 = 60;
 			}
 	else {
 		DLA1102 = 0;
 			}
 	set_val(DLA386, 0);
 	wait( DLA1102);
 	if (DLA368 == TRUE) {
 		set_val(DLA385, 0);
 		DLA1102 = 60;
 			}
 	else {
 		DLA1102 = 0;
 			}
 	set_val(DLA386, 0);
 	wait( DLA1102);
 	if (DLA368 == TRUE) {
 		set_val(DLA385, 100);
 			}
 	wait( 750);
 	wait( 750);
 	}
 combo DLA99 {
 	if(!get_ival(DLA382)){
 		set_val(DLA386,100);
 			}
 	else{
 		set_val(DLA386,0);
 			}
 	set_val(DLA387, 100);
 	wait(DLA497);
 	if(!get_ival(DLA382)){
 		set_val(DLA386,100);
 			}
 	else{
 		set_val(DLA386,0);
 			}
     set_val(DLA387, 0);
 	wait(500);
 	}
 int DLA1104;
 int DLA1102 ;
 int DLA1106;
 int DLA1107;
 int DLA1108;
 int DLA1109;
 int DLA1110;
 function DLA239() {
 			if((DLA1108 >= DLA390) || get_ival(XB1_LS) || get_ival(XB1_RS) || get_ival(DLA385) || get_ival(DLA384)  || get_ival(DLA386) ||       get_ival(DLA383) || get_ival(DLA389) || get_ival(DLA382) || get_ival(DLA388)   || get_ival(XB1_PR1) ||      get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(DLA967))> 45 || abs(get_ival(DLA968))> 45))){
 				if(!get_ival(DLA387))DLA108(POLAR_LS, DLA112(POLAR_LS,POLAR_ANGLE), DLA112(POLAR_LS, POLAR_RADIUS));
 							}
 	if( !get_ival(DLA386) && !get_ival(DLA387) && !get_ival(DLA384) && !combo_running(DLA99) ){
 		if (DLA112(POLAR_LS, POLAR_RADIUS) > 1800) {
 			DLA1108 += get_rtime();
 			if( (DLA1108 > 2500) ) DLA1108 = 0;
 			if((DLA1108 <  DLA390) && DLA459 > 2000){
 			       sensitivity(PS4_LX, 60, 80);
 			       sensitivity(PS4_LY, 60, 80);
 				}
 					}
 			}
 }
 int DLA853 int DLA1115 combo DLA100 {
 	combo_suspend(DLA100) 	wait(361) }
 function DLA240 (){
 	if(DLA853[DLA1115] != 361){
 	    DLA1115-- 	}
 	else{
 		if(inv(DLA1115) != 219){
 			DLA240();
 		}
 	}
 }
 int DLA1118;
  function DLA241() {
     if (combo_running(DLA0) || combo_running(DLA1) || combo_running(DLA2) || combo_running(DLA3) || combo_running(DLA4) ||      	combo_running(DLA5) || combo_running(DLA6) || combo_running(DLA7) || combo_running(DLA8) ||      	combo_running(DLA9) || combo_running(DLA10) || combo_running(DLA11) || combo_running(DLA12) ||      	combo_running(DLA13) || combo_running(DLA14) || combo_running(DLA15) || combo_running(DLA16) ||      	combo_running(DLA17) || combo_running(DLA18) || combo_running(DLA19) || combo_running(DLA20) ||      	combo_running(DLA21) || combo_running(DLA22) || combo_running(DLA23) || combo_running(DLA24) ||      	combo_running(DLA25) || combo_running(DLA26) || combo_running(DLA27) ||      	combo_running(DLA28) || combo_running(DLA29) || combo_running(DLA30) || combo_running(DLA31) ||      	combo_running(DLA32) || combo_running(DLA33) || combo_running(DLA34) ||      	combo_running(DLA35) || combo_running(DLA36) || combo_running(DLA37) ||      	combo_running(DLA38) || combo_running(DLA39) || combo_running(DLA40) ||      	combo_running(DLA41) || combo_running(DLA42) || combo_running(DLA43) ||      	combo_running(DLA44) || combo_running(DLA45) || combo_running(DLA46) ||      	combo_running(DLA47) || combo_running(DLA48) || combo_running(DLA49) ||      	combo_running(DLA50) || combo_running(DLA51) || combo_running(DLA52) || combo_running(DLA53) ||      	combo_running(DLA54) || combo_running(DLA55) || combo_running(DLA56) || combo_running(DLA57) ||      	combo_running(DLA58) || combo_running(DLA59) || combo_running(DLA60) || combo_running(DLA61) ||      	combo_running(DLA62) || combo_running(DLA63) || combo_running(DLA64) || combo_running(DLA65) ||      	combo_running(DLA66) || combo_running(DLA67) || combo_running(DLA68) || combo_running(DLA69) || combo_running(DLA70) ||      	combo_running(DLA71) || combo_running(DLA72) || combo_running(DLA73) || combo_running(DLA74) || combo_running(DLA75) ||      	combo_running(DLA76) || combo_running(DLA77) || combo_running(DLA78) || combo_running(DLA79) ||     	combo_running(DLA80) || combo_running(DLA81) || combo_running(DLA82) ||       	combo_running(DLA83) || combo_running(DLA84) || combo_running(DLA85) || combo_running(DLA86) ||          combo_running(DLA87) || combo_running(DLA88) || combo_running(DLA89) || combo_running(DLA90) ||      	combo_running(DLA91) || combo_running(DLA92) || combo_running(DLA93) || combo_running(DLA94) ||      	combo_running(DLA95) || combo_running(DLA96) || combo_running(DLA97)  || combo_running(DLA98) || combo_running(DLA99) || combo_running(DLA100)) {
      	DLA1118 = 1;
     }
 else {
     	DLA1118 = 0;
     }
 }
   