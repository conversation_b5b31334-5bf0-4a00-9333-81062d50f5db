define Off = 0;
define <PERSON>m_Blue = 1;
define Dim_Red = 2;
define Dim_Green = 3;
define Dim_Pink = 4;
define Dim_SkyBlue = 5;
define Dim_Yellow = 6;
define Dim_White = 7;
define Blue = 8;
define Red = 9;
define <PERSON> = 10;
define Pink = 11;
define SkyBlue = 12;
define Yellow = 13;
define <PERSON> = 14;
define Bright_Blue = 15;
define Bright_Red = 16;
define Bright_Green = 17;
define Bright_Pink = 18;
define Bright_SkyBlue = 19;
define Bright_Yellow = 20;
define Bright_White = 21;
 
data (
 
0,0,0,0, // Off
1,0,0,0, // Dim Blue
0,1,0,0, // Dim Red
0,0,1,0, // Dim Green
0,0,0,1, // Dim Pink
1,0,1,0, // Dim SkyBlue
0,1,1,0, // Dim Yellow
1,1,1,1, // Dim White
2,0,0,0, // Blue
0,2,0,0, // Red
0,0,2,0, // Green
0,0,0,2, // Pink
2,0,2,0, // SkyBlue
0,2,2,0, // Yellow
2,2,2,2, // White
3,0,0,0, // Bright Blue
0,3,0,0, // Bright Red
0,0,3,0, // Bright Green
0,0,0,3, // Bright Pink
3,0,3,0, // Bright SkyBlue
0,3,3,0, // Bright Yellow
3,3,3,3  // Bright white
 
); 

define Stick_Press_LB_Threshold = 95;
int stick_swap_toggle;
int rightStickMagnitude;
int UltimatePower;
int DYN_Acc;

int dd_value = 35;
//const string dd = "dd_value"; 

function icos(x) { return isin(x + 8192); }
function isin(x) {
	x = (x % 32767) << 17;
	if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
	x = x >> 17;
	return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggle_active;
int cos_angle, sin_angle;
	// Increase the frequency of peaks and dips
	int multiplier = 24;
function set_polar_dd(stick, angle, radius, dd_factor){
	dd_factor = (dd_factor * 32767) / 80;
	radius = (radius * 32767) / 10000;
	angle = (360 - angle) * 91;

	sin_angle = isin(angle); 
	cos_angle = icos(angle);


	angle = (angle * multiplier) % 32767;

	// Adjusted radius calculation for more peaks and dips
	radius = radius * (32767 - ((angle * dd_factor) >> 15)) >> 15;

	set_val(42 + stick, clamp((radius * cos_angle) >> 15, -32767, 32767));
	set_val(43 + stick, clamp((radius * sin_angle) >> 15, -32767, 32767));

	return;
}

int r, a, dd;

int Stop_Detector;
int Stop_Ran = 0;  // Integer flag to check if combo has already been run

main {
vm_tctrl(-9)

   if(toggle_active)        set_ds4_led(Green); 
   else if(!toggle_active)   set_ds4_led(Red);
   
   
     if (abs(get_ival(XB1_LX)) < 30 && abs(get_ival(XB1_LY)) < 30) {
    Stop_Detector += get_rtime();
    if (Stop_Detector > 100 && Stop_Ran == 0) {
      combo_run(Stop_my);
      Stop_Ran = 1;  // Set flag to 1 after running the combo
    }
  } else {
    combo_stop(Stop_my);
    Stop_Detector = 0;   
    Stop_Ran = 0;  // Reset flag when conditions are not met
  }
/*
if(get_ival(PS4_L1)){
    if(event_press(PS4_R3)){ 
        KS_EntireScript = !KS_EntireScript;
        f_set_notify (KS_EntireScript);
    }
    set_val(PS4_R3,0);
}
*/
//if( !KS_EntireScript){  

   // LED_Color(Blue);
 if(time_to_clear_screen){
    time_to_clear_screen -= get_rtime();
    if(time_to_clear_screen <= 0)combo_run(CLEAR_SCREEN);
 }
            if(get_ival(XB1_RT)){                              
	            if(event_press(XB1_RIGHT)) {
	                dd_value +=5;
	                on_the_fly_display(centerPosition(sizeof(dd_value)- 1,OLED_FONT_MEDIUM_WIDTH),dd_value[0],dd_value);
            	}
            	if(event_press(XB1_LEFT)) {
	                dd_value -=5;
	                on_the_fly_display(centerPosition(sizeof(dd_value)- 1,OLED_FONT_MEDIUM_WIDTH),dd_value[0],dd_value);
                }
                set_val(PS4_RIGHT,0);
                set_val(PS4_LEFT ,0);
            }

    // Check if both LT and UP are pressed simultaneously to toggle the functionality
    if(get_val(XB1_LT)) {
        if(event_press(XB1_UP)) {
            toggle_active = !toggle_active;  // Toggle the active state
            combo_run(Feedback);  // Run feedback only when both buttons trigger the toggle
        }
    }   

    // Only execute this block if the toggle is active
    if(toggle_active) {
        stickize(POLAR_LX, POLAR_LY, 31129);
        r = get_polar(POLAR_LS, POLAR_RADIUS);
        a = get_polar(POLAR_LS, POLAR_ANGLE);
        dd = dd_value; // Range -50 < - > 100 
        set_polar_dd(POLAR_LS, a, r, dd);
    }

	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_LEFT)) {
			load_slot (2);
	}
		set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_RIGHT)) {
			load_slot (2);
	}
	set_val(XB1_RIGHT,0);
	}
	// LT+DOWN Stick Toogle
	if(get_ival(XB1_LT)) {
		if(event_press(XB1_DOWN)) {
			stick_swap_toggle = !stick_swap_toggle;
			combo_run(Feedback2);  // Run feedback only when both buttons trigger the toggle
		}
	}
 
	if(stick_swap_toggle) {
		rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
		if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
			set_val(POLAR_LX, get_val(POLAR_RX));
			set_val(POLAR_LY, get_val(POLAR_RY));
		}
		if(rightStickMagnitude >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
			combo_run(Press_LB);
	}
 

	// Finesse LB+B
	if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
		}

	// Finesse with R3
	if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
	}

	// Shots with B
	if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);
		}
	}
	// Shots with RB+B
	if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);
		}
	}

//}
   // else {LED_Color(Green); }
}	// End of main

// Optional: A combo to provide some form of feedback that the toggle has occurred
combo Feedback {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(400);
    reset_rumble();
}

combo Feedback2 {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    set_rumble(RUMBLE_A, 0);  // Vibrate controller briefly
    wait(100);
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    reset_rumble();
}

combo banks {
	set_val(XB1_A, 100);
	combo_run(pressing);
}

combo PressB {
	set_val(XB1_B, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

combo finesse {
	set_val(XB1_B, 100);
	wait(260);
	set_val(XB1_B, 0);
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(440);
	set_val(XB1_LB, 100);
	wait(80);
	set_val(XB1_LB, 0);
	wait(40);
	set_val(XB1_LB, 100);
	wait(250);
	set_val(XB1_LB, 0);
	wait(250);  
}

combo CHIP_SHOT {
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	set_val(PS4_L3,100);
	wait( 80);
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	wait(100);    
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

combo Press_LB {
	set_val(XB1_LB, 100);
	set_val(XB1_RB, 100);
	wait(100);
	wait(20);
	set_val(XB1_A, 100);
	wait(200);
	wait(20);
}

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
}

combo Stop_my {
  set_val(XB1_RT, 100);
  set_val(XB1_LX, 0);
  set_val(XB1_LY, 0);
  wait(100);
  set_val(XB1_RT, 0);
  set_val(XB1_LX, 0);
  set_val(XB1_LY, 0);
  wait(60);
    set_val(XB1_RT, 100);
  set_val(XB1_LX, 0);
  set_val(XB1_LY, 0);
  wait(100);
}


int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function set_ds4_led(colour) {
 
    set_led(LED_1, duint8 (colour * 4));
    set_led(LED_2, duint8 ((colour * 4) + 1));
    set_led(LED_3, duint8 ((colour * 4) + 2));
    set_led(LED_4, duint8 ((colour * 4) + 3));
 
}

int KS_EntireScript = FALSE;
function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2); 
} 
const string OFF   = "OFF";       
const string ON    = "ON";

   //=======================================
   //  DISPLAY EDIT VALUE ON THE FLY        
   //=======================================
function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 


combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}  


/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}


int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

int data_indx;

/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
} 
int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
} 