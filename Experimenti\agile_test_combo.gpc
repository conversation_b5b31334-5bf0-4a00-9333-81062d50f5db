// Variables for tracking state
int currentMove = 0;  // 0=down, 1=up, 2=left, 3=right
int moveTimer = 0;    // Timer for movement changes
int MOVE_DURATION = 170; // Duration for each movement in milliseconds
int lastLsAngle = 0;  // Store last left stick angle

// Movement functions adjusted to use lastLsAngle
function move_up() {
    // Same direction as last angle (0° change)
    set_polar(POLAR_LS, lastLsAngle % 360, 30800);
}

function move_down() {
    // Opposite direction from last angle (180° change)
    set_polar(POLAR_LS, (lastLsAngle + 180) % 360, 30800);
}

function move_left() {
    // 90° counterclockwise from last angle
    set_polar(POLAR_LS, (lastLsAngle - 90 + 360) % 360, 30800);
}

function move_right() {
    // 90° clockwise from last angle
    set_polar(POLAR_LS, (lastLsAngle + 90) % 360, 30800);
}

// Combo definition for the movement cycle
combo agile_movement {    
    // Update last known angle when stick is active
    if (get_polar(POLAR_LS, POLAR_RADIUS) >= 2000) {
        lastLsAngle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    }
    
    // Only proceed if LS is pressed and left stick magnitude is < 2000
    if (get_val(XB1_LS) && get_polar(POLAR_LS, POLAR_RADIUS) < 2000) {
        // Increment timer
        moveTimer += get_rtime();
        
        // Check if it's time to change movement
        if (moveTimer >= MOVE_DURATION) {
            // Reset timer and move to next movement
            moveTimer = 0;
            currentMove = (currentMove + 1) % 4;
        }
        
        set_val(XB1_LS, 0);
        // Execute current movement
        if (currentMove == 0) {
            move_down();
            set_val(XB1_LT, 100);
            set_val(XB1_RT, 100);
        }
        else if (currentMove == 1) {
            move_up();
            set_val(XB1_LT, 100);
            set_val(XB1_RT, 100);
        }
        else if (currentMove == 2) {
            move_left();
            set_val(XB1_LT, 100);
            set_val(XB1_RT, 100);
        }
        else {  // currentMove == 3
            move_right();
            set_val(XB1_LT, 100);
            set_val(XB1_RT, 100);
        }
    }
    else {
        // Reset timer when conditions are not met
        moveTimer = 0;
        combo_stop();
    }
}

// Example of how to use in another script:
/*
main {
    // Start the combo when needed
    if (your_condition) {
        combo_run(agile_movement);
    }
    
    // Stop the combo when needed
    if (your_stop_condition) {
        combo_stop(agile_movement);
    }
}
*/
