// Perfect Octagonal Boundary
// Creates 8-directional movement with octagonal border

// Constants
define DEADZONE = 25;          // Reduced deadzone for better responsiveness
define MAX_INPUT = 90;         // Maximum stick range
define DIAGONAL_MAX = 80;     // Maximum at diagonal (100 = same as cardinal)

// Octagonal boundary adjustments (8 segments, 45 degrees each)
const int anglesMax[] = {
    100, 100, 100, 100, 100,  // 0-20 degrees (cardinal)
    85,  75,  70,  70,  70,   // 20-40 degrees (transition)
    70,  70,  70,  70,  70,   // 40-60 degrees (diagonal)
    70,  70,  70,  75,  85,   // 60-80 degrees (transition)
    100, 100, 100, 100, 100,  // 80-100 degrees (cardinal)
    85,  75,  70,  70,  70,   // 100-120 degrees (transition)
    70,  70,  70,  70,  70,   // 120-140 degrees (diagonal)
    70,  70,  70,  75,  85    // 140-160 degrees (transition)
};

// Global variables
int LX, LY;
int scaled_x, scaled_y;
int angle, radius;
int sign, abs_val, output;
int mag_sq, limit_sq, mag;
int iSqrtValue, iSqrtRes, iSqrtBit;
int extension, new_radius;

// Integer square root using bitwise method
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes = 0;
    iSqrtBit = 1 << 14;

    while(iSqrtBit > iSqrtValue)
        iSqrtBit >>= 2;

    while(iSqrtBit != 0) {
        if(iSqrtValue >= iSqrtRes + iSqrtBit) {
            iSqrtValue -= iSqrtRes + iSqrtBit;
            iSqrtRes = (iSqrtRes >> 1) + iSqrtBit;
        } else {
            iSqrtRes >>= 1;
        }
        iSqrtBit >>= 2;
    }
    return iSqrtRes;
}

// Apply deadzone with smooth transition
function applyDeadzone(val) {
    if(val >= 0)
        sign = 1;
    else
        sign = -1;

    abs_val = abs(val);
    if(abs_val <= DEADZONE)
        return 0;
    
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    if(output > MAX_INPUT)
        output = MAX_INPUT;
        
    return sign * output;
}

// Create perfect octagonal boundary
function mapToOctagon(x, y) {
    scaled_x = applyDeadzone(x);
    scaled_y = applyDeadzone(y);
    
    // Calculate magnitude
    mag_sq = scaled_x * scaled_x + scaled_y * scaled_y;
    if(mag_sq == 0)
        return;
        
    mag = intSqrt(mag_sq);
    
    // Get current angle (0-360)
    angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Apply octagonal boundary
    extension = anglesMax[angle % 45];
    new_radius = (mag * extension) / 100;
    
    // Scale coordinates to create octagonal boundary
    if(new_radius > MAX_INPUT)
        new_radius = MAX_INPUT;
        
    scaled_x = (scaled_x * new_radius) / mag;
    scaled_y = (scaled_y * new_radius) / mag;
}

main {
    // Get stick values
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);
    
    // Apply octagonal mapping
    if(abs(LX) > DEADZONE || abs(LY) > DEADZONE) {
        mapToOctagon(LX, LY);
        set_val(XB1_LX, scaled_x);
        set_val(XB1_LY, scaled_y);
    }
} 