
// ============  HOW it WORKs ============// : 
//1 - the script triggered by pressing Pass button or Triangle "Through Ball button" , 
//     while nothing else pressed Sprint bottun or jockey button or Player run button .. etc . 
//  
//2-  short tap will generate fixed Minimum power that suitable for short passes and through balls , so finally no wasted powerless passes 
//    and sure if you need more power you can hold pass buttton as much as you want. 
// 
//   
//3 - passes and through balls will feel more accurate and powerfull , with little curve in some setiuations , coz the script is triggering sprint button after each pass
//    which I discovered it's really powerful along with Open_up ( L3 ) thing .
//
//
//4-  The Timed Through Ball Mode included , it is different than short passes and normal through ball , it will trigger double Triangle tab after 
//    the through ball performed , some of bro players adviced that and they said it make long through balls specially more accurate and OP.
  
  //==================================================// 
 // ============ COPY BEFORE MAIN SECTION ============ //
 
 
 int Pass_Or_Through ;
 
 
  //=====================================================//
  

  // ============ COPY BEFORE THE END OF MAIN SECTION ============ //
    if(event_release(PS4_TRIANGLE) && get_val(PlayerRun)){
    combo_run(LONG_THROUGH);
    }
    
      if (event_press(PassBtn)){
    Pass_Or_Through = PassBtn ; } 
     if (event_press(PS4_TRIANGLE)){
    Pass_Or_Through = PS4_TRIANGLE ; } 
    
    
      if (( event_press(PassBtn) || event_press(PS4_TRIANGLE)) &&
          !get_val(FinesseShot) && !get_val(SprintBtn) 
          && !get_val(PlayerRun) && ( abs(get_val(MOVE_X))> 40 || abs(get_val(MOVE_Y))> 40))
          {
                   set_val(PassBtn,0);
                   set_val(PS4_TRIANGLE,0);
                   combo_run(lol_pass);  
               
          }

          
    // ============ COPY THis TO COMBOS SECTION ============ //
    combo lol_pass {
set_val(PS4_L3,100);
set_val(Pass_Or_Through,100);
wait(130);
set_val(SprintBtn,100);
wait(30);
set_val(PS4_L3,100);
wait(600);
}

    combo LONG_THROUGH{
set_val(PS4_TRIANGLE,0);
set_val(PS4_L3,100);
wait(90);
set_val(PS4_TRIANGLE,100);
set_val(PS4_L3,100);
wait(60);
set_val(PS4_L3,100);
wait(2500);
}

 
 
 
 
 