
int current_random_radius;
define REDUCED_RADIUS = 30000;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;
int radius;
int current_angle;


//int AngleInterval = 8;

//int current_index = 0;

int phase_counter = 0;
int oscillation_factor;
define PHASE_MULTIPLIER = 5;
int phase_speed = 100;
int actual_phase;
//int current_base;
int oscillation_range;


main {

pass();
}

function pass() {
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2);
        
        phase_counter = (phase_counter + phase_speed) % (360 * PHASE_MULTIPLIER);
        actual_phase = phase_counter / PHASE_MULTIPLIER;
        
        oscillation_factor = actual_phase;
        if (oscillation_factor > 180) {
            oscillation_factor = 360 - oscillation_factor;
        }
        oscillation_factor = ((oscillation_factor * 200) / 180) - 100;  // Convert to -100 to +100 range
        
        // Calculate 14% of current radius for oscillation range
        oscillation_range = (radius * 14) / 100;
        
        // Oscillate between current radius and (current radius - 14%)
        current_random_radius = radius * 3 - ((oscillation_range * (oscillation_factor - 220)) / 200);
        //current_random_radius = (radius * 3) + ((oscillation_range * oscillation_factor) / 100);
        
        if (current_random_radius > MAX_RADIUS) {
            current_random_radius = MAX_RADIUS;
        }
        if (current_random_radius < 0) {
            current_random_radius = 0;
        }
        
        set_polar(POLAR_LS, current_angle, current_random_radius);
}

function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}