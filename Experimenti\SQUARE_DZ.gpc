int LX, LY;              // Raw stick inputs
int scaled_x, scaled_y;  // Output after applying dead zone
int DEADZONE   = 40;     // Large deadzone for clear square shape
int MAX_INPUT  = 100;    // Maximum raw input value
int sign;                // For storing sign of input
int abs_val;             // For storing absolute value
int output;              // For storing output value

main {
    // Get raw inputs
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);

    // Apply the square dead zone mapping
    map_square_deadzone(LX, LY);

    // Set the adjusted values back to the controller
    set_val(XB1_LX, scaled_x);
    set_val(XB1_LY, scaled_y);
}

// This function applies a SQUARE dead zone to X and Y independently.
function map_square_deadzone(int x, int y) {
    scaled_x = apply_one_axis_deadzone(x);
    scaled_y = apply_one_axis_deadzone(y);
}

// Utility function to clamp and scale a single axis
function apply_one_axis_deadzone(int val) {
    // Determine sign
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
        val = -val;  // Make positive for easier processing
    }
    
    // Hard deadzone cutoff
    if(val <= DEADZONE) {
        return 0;
    }
    
    // Aggressive scaling outside deadzone
    output = val;
    if(output > MAX_INPUT) {
        output = MAX_INPUT;
    }
    
    // Scale the output to maintain full range
    output = ((output - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    
    return sign * output;
}