int modMenu,editMenu; 
int modNameIdx,valNameIdx;
int case_indic = 0;

define ShotBtn       = XB1_B          ; // Shot Btn         (default B/CIRCLE 
define PassBtn       = XB1_A          ; // Short Pass Btn   (default A/CROSS)
define PlayerRun     = XB1_LB         ; // Player Run       (default L1/LB) 
define FinesseShot   = XB1_RB         ; // Finesse Shot     (default R1/RB)
define PaceCtrol     = XB1_LT         ; // Protect Ball     (default L2/LT)
define SprintBtn     = XB1_RT         ; // Sprint Btn       (default R2/RT)
define CrossBtn      = XB1_X          ; // Cross Btn        (default X/SQUARE)
define ThroughBall   = XB1_Y          ; // Through Ball Btn (default Y/TRIANGLE) 

/* Display Variables / ScreenSaver / Strings/Text  */
int screenSaver,blankScreen;
int displayTitle = TRUE;
int updateDisplay;

const string Toggle  [] = {"Off","On"};
const string misc[]     = {"v1.18","Template",""}; 
const string LEFT  = "PENALTIES";
const string RIGHT = "USE DPAD";
const string UP  = "FOR DIRECTIONS";
const string DOWN = "FIRST B + DPAD";
const string X = "OPTIONAL RB-CHIP";
main {


    if(!modMenu && !editMenu){
        /* Display The Title Screen When we Are NOT in any Menu s */
        if(displayTitle){ 
            cls_oled(0);

            displayTitle = FALSE;
            screenSaver  = TRUE;

            print(centerPosition(getStringLength( LEFT[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFT[0]);
            print(centerPosition(getStringLength(RIGHT[0]) ,OLED_FONT_SMALL_WIDTH), 18  ,OLED_FONT_SMALL , OLED_WHITE , RIGHT[0]);
            print(centerPosition(getStringLength( UP[0]) ,OLED_FONT_SMALL_WIDTH), 31  ,OLED_FONT_SMALL , OLED_WHITE , UP[0]);
            print(centerPosition(getStringLength(DOWN[0]) ,OLED_FONT_SMALL_WIDTH), 44  ,OLED_FONT_SMALL , OLED_WHITE , DOWN[0]);
			print(centerPosition(getStringLength(X[0]) ,OLED_FONT_SMALL_WIDTH), 55  ,OLED_FONT_SMALL , OLED_WHITE , X[0]);
        }
        }




if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (4);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (4);
      }
      set_val(XB1_RIGHT,0);
	}

    LED_Color(Green);
  if (get_ival(ShotBtn)) { // Power limiter
    set_val(ShotBtn, 0);
    if (get_ival(PlayerRun)) PN_Shot_Power = 50; // chip penalty power
    if (!get_ival(PlayerRun)) PN_Shot_Power = 430; // normal penalty power
      combo_run(RESET_PN);
  }
  if (PN_Angle > 0) set_polar(POLAR_LS, PN_Angle * -1, 32767); // Aim - Lock
  if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) PN_Angle = 345; // DOWN_RIGHT 
  if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) PN_Angle = 45; // UP_RIGHT
  if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) PN_Angle = 135; // UP_LEFT
  if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) PN_Angle = 225; // DOWN_LEFT
  if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) PN_Angle = 180; // LEFT
  if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) PN_Angle = 1; // RIGHT
  if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) PN_Angle = 90; // UP_MIDDLE
  if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) PN_Angle = 270; // DOWN_MIDDLE
  }






int PN_Shot_Power;
int onoff_penalty;
int PN_Angle;				
combo RESET_PN {

  set_val(ShotBtn, 100);
wait(PN_Shot_Power);
wait(50);
  
wait(3800);
  
  //onoff_penalty = !onoff_penalty; // reset after each penalty
  load_slot (2);
}

combo p23 {
  set_polar(POLAR_LS, PN_Angle, 32767);
  set_val(XB1_B, 100);
  wait(420);
  set_polar(POLAR_LS, PN_Angle, 32767);
  wait(1500); //Aim_Lock
  //wait(200); //Aim_Lock
  //set_val(XB1_RB, 100);
  //set_val(XB1_LB, 100);
  //wait(500); //Aim_Lock
  wait(5000);
  // load_slot (1);
}

define ColorOFF = 0;
define Blue = 1;
define Red = 2;
define Green = 3;
define Pink = 4;
define SkyBlue = 5;
define Yellow = 6;
define White = 7;

data(
  0, 0, 0, //0. ColorOFF
  2, 0, 0, //1. Blue     
  0, 2, 0, //2. Red      
  0, 0, 2, //3. Green    
  2, 2, 0, //4. Pink     
  2, 0, 2, //5. SkyBlue 
  0, 2, 2, //6. Yellow   
  2, 2, 2 //7. White    
); // end of data segment-------------- 
// COLOR LED function        
//-------------------------------------------------------------- 

int data_indx;

function LED_Color(color) {
  for (data_indx = 0; data_indx < 3; data_indx++) {
    set_led(data_indx, duint8((color * 3) + data_indx));
  }
}

function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
 }  