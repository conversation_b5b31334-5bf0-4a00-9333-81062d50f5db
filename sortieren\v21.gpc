																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/






































































































int D24201[0]; init { 	D24118(); 	combo_run(D241); 	combo_run(D242); 	combo_run(D243); 	combo_run(D244); 	combo_run(D245); 	combo_run(D246); 	combo_run(D247); 	combo_run(D248); 	combo_run(D249); 	combo_run(D2410); 	combo_run(D2411); 	combo_run(D2412); 	combo_run(D2413); 	combo_run(D2414); 	combo_run(D2415); 	combo_run(D2416); 	combo_run(D2417); 	combo_run(D2418); 	combo_run(D2419); 	combo_run(D2420); 	combo_run(D2421); 	combo_run(D2422); 	combo_run(D2423); 	combo_run(D2424); 	combo_run(D2425); 	combo_run(D2426); 	combo_run(D2427); 	combo_run(D2428); 	combo_run(D2429); 	combo_run(D2430); 	combo_run(D2431); 	combo_run(D2432); 	combo_run(D2433); 	combo_run(D2434); 	combo_run(D2435); 	combo_run(D2436); 	combo_run(D2437); 	combo_run(D2438); 	combo_run(D2439); 	combo_run(D2440); 	combo_run(D2441); 	combo_run(D2442); 	combo_run(D2443); 	combo_run(D2444); 	combo_run(D2445); 	combo_run(D2446); 	combo_run(D2447); 	combo_run(D2448); 	combo_run(D2449); 	combo_run(D2450); 	combo_run(D2451); 	combo_run(D2452); 	combo_run(D2453); 	combo_run(D2454); 	combo_run(D2455); 	combo_run(D2456); 	combo_run(D2457); 	combo_run(D2458); 	combo_run(D2459); 	combo_run(D2460); 	combo_run(D2461); 	combo_run(D2462); 	combo_run(D2463); 	combo_run(D2464); 	combo_run(D2465); 	combo_run(D2466); 	combo_run(D2467); 	combo_run(D2468); 	combo_run(D2469); 	combo_run(D2470); 	combo_stop(D241); 	combo_stop(D242); 	combo_stop(D243); 	combo_stop(D244); 	combo_stop(D245); 	combo_stop(D246); 	combo_stop(D247); 	combo_stop(D248); 	combo_stop(D249); 	combo_stop(D2410); 	combo_stop(D2411); 	combo_stop(D2412); 	combo_stop(D2413); 	combo_stop(D2414); 	combo_stop(D2415); 	combo_stop(D2416); 	combo_stop(D2417); 	combo_stop(D2418); 	combo_stop(D2419); 	combo_stop(D2420); 	combo_stop(D2421); 	combo_stop(D2422); 	combo_stop(D2423); 	combo_stop(D2424); 	combo_stop(D2425); 	combo_stop(D2426); 	combo_stop(D2427); 	combo_stop(D2428); 	combo_stop(D2429); 	combo_stop(D2430); 	combo_stop(D2431); 	combo_stop(D2432); 	combo_stop(D2433); 	combo_stop(D2434); 	combo_stop(D2435); 	combo_stop(D2436); 	combo_stop(D2437); 	combo_stop(D2438); 	combo_stop(D2439); 	combo_stop(D2440); 	combo_stop(D2441); 	combo_stop(D2442); 	combo_stop(D2443); 	combo_stop(D2444); 	combo_stop(D2445); 	combo_stop(D2446); 	combo_stop(D2447); 	combo_stop(D2448); 	combo_stop(D2449); 	combo_stop(D2450); 	combo_stop(D2451); 	combo_stop(D2452); 	combo_stop(D2453); 	combo_stop(D2454); 	combo_stop(D2455); 	combo_stop(D2456); 	combo_stop(D2457); 	combo_stop(D2458); 	combo_stop(D2459); 	combo_stop(D2460); 	combo_stop(D2461); 	combo_stop(D2462); 	combo_stop(D2463); 	combo_stop(D2464); 	combo_stop(D2465); 	combo_stop(D2466); 	combo_stop(D2467); 	combo_stop(D2468); 	combo_stop(D2469); 	combo_stop(D2470); 	combo_run(D24110); } int D24264 ; int D24265; int D24266; int D24267; int D24268; define D24269 = 0; define D24270 = 1; define D24271 = 2; define D24272 = 3; define D24273 = 4; define D24274 = 5; define D24275 = 6; define D24276 = 7; define D24277 = 8; define D24278 = 9; define D24279 = 10; define D24280 = 11; define D24281 = 12; define D24282 = 13; define D24283 = 14; define D24284 = 15; define D24285 = 16; define D24286 = 17; define D24287 = 18; define D24288 = 19; define D24289 = 20; define D24290 = 21; define D24291 = 22; define D2423 = 23; define D24293 = 24; define D24294 = 25; define D24295 = 26; define D24296 = 27; define D24297 = 28; define D24298 = 29; define D24299 = 30; define D24300 = 31; define D24301 = 32; define D24302 = 33; define D24303 = 34; define D24304 = 35; define D24305 = 36; define D24306 = 37; define D24307 = 38; define D24308 = 39; define D24309 = 40; define D24310 = 41; define D24311 = 42; define D24312 = 43; define D24313 = 44; define D24314 = 45; define D24315 = 46; define D24316 = 47; define D24317 = 48; define D24318 = 49; define D24319 = 50; define D24320 = 51; define D24321 = 52; define D24322 = 53; define D24323 = 54; define D24324 = 55; define D24325 = 56; define D24326 = 57; define D24327 = 58; define D24328 = 59; define D24329 = 60; define D24330 = 61; define D24331 = 62; define D24332 = 63; define D24333 = 64; define D24334 = 65; define D24335 = 66; define D24336 = 67; define D24337 = 68; define D24338 = 69; define D24339 = 70; define D24340 = 0; function D24112(D24113) { 	if (D24113 == 0) vm_tctrl(-0); 	else if (D24113 == 1) vm_tctrl(2); 	else if (D24113 == 2) vm_tctrl(-2); 	else if (D24113 == 3) vm_tctrl(-4); 	else if (D24113 == 4) vm_tctrl(-6); 	else if (D24113 == 5) vm_tctrl(-8); 	else if (D24113 == 6) vm_tctrl(-9); } int D24341, D24342; int D24343, D24344; int D24345 = FALSE, D24346; int D24347 = TRUE; int D24348; const string D24821[] = { 	"Off",  "On" } ; int D24349; int D24350; int D24351; int D24352; int D24353; int D24354; int D24355; int D24356; int D24357; int D24358; int D24359; int D24360; int D24361; int D24362; int D24363; int D24364; int D24365; int D24366; int D24367; int D24368; int D24113; int D24370; int D24371 ; int D24372 ; int D24373 ; define D24374 = 24; int D24375; int D24376; int D24377; int D24378; int D24379; int D24380; int D24381; int D24382; int D24383; int D24384 ; int D24385 ; int D24386 ; int D24387 ; int D24388 ; int D24389 ; int D24390 ; int D24391 ; int D24392 ; int D24393 ; int D24394 ; int D24395; int D24396; int D24397; int D24398; int D24399; int D24400; int D24401; int D24402; int D24403; int D24404; int D24405; int D24406; int D24407; int D24408; int D24409; int D24410; int D24411; int D24412; int D24413; int D24414; int D24415; int D24416; int D24417; int D24418; int D24419; int D24420; int D24421; int D24422; int D24423; int D24424; int D24425; int D24426; int D24427; int D24428 ; int D24429 ; int D24430 ; int D24431; int D24432 ; int D24433 ; int D24434 ; int D24435; int D24436 ; int D24437 ; int D24438 ; int D24439; int D24440 ; int D24441 ; int D24442 ; int D24443; int D24444; int D24445; int D24446; int D24447; int D24448; const int16 D24827[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		2800,12000,100,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 D24568[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 D24799[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1,   	    1,  	1,  	1   } ; const string D24578[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string D24577[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string D24803 [] = { 	"Classic","Alternative","Custom", ""  } ; const string D24902 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string D24819[] = { 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" } ; const string D24805[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string D24872[] = { 	"Right",  "Left",  "" } ; const string D24870[] = { 	"One Tap",  "Double Tap",  "" } ; const string D24809[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string D24811[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string D24846[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int D24449 = -1; int D24450 = -1; int D24451 = -1; int D24452 = -1; int D24453 = -1; int D24454; int D24455; int D24456; int D24457; int D24458; const uint8 D241351[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 D241352[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 D241353[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 D241354[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 D241355[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 D241356[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D241357[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D241358[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 D241359[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 D241360[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 D241361[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 D241362[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 D241363[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 D241364[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D241365[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D241366[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function D24114(D24115) { 	if (D24115 == 9) { 		D24459 = -1; 			} 	else if (D24115 <= 0) { 		D24459 = 1; 			} 	else if (D24115 > 9 ) { 		D24115 = 0; 			} 	D24115 += D24459; 	return D24115; 	} function D24116() { 	vm_tctrl(0); 	if(D2429 && D24352){ 		if(D24544 < 1000){ 			D24462 = 10; 			D24488   = 10; 			D24486  = 10; 					} 			} 	if(D24465 && D24353){ 		D24463 = FALSE; 		if(D24544 < 1000){ 			D24462 = 11; 			D24488   = 11; 			D24486  = 11; 					} 			} 	if(D24463 && D24353){ 		D24465 = FALSE; 		if(D24544 < 1000){ 			D24462 = 10; 			D24488   = 10; 			D24486  = 10; 					} 			} 			       if(D24544 >= 1000){     D24467 = D24114(D24467);     D24485 = D24114(D24485);     D24486 = D24114(D24486);     D24462 = D24114(D24462);     D24488 = D24114(D24488);     } 	if(D24352){ 		if(D24491 == D24582){ 			D24468 = !D24468; 			if(D241351[D24467]) D24226(D241351[D24467]); 					} 		if(D24491 == D24231 (D24582 + 4)){ 			D24468 = FALSE; 			if(D241358[D24485]) D24226(D241358[D24485]); 					} 		if(D24491 == D24231 (D24582 + 1) ){ 			D24468 = TRUE; 			if(D241353[D24462]) D24226(D241353[D24462]); 					} 		if(D24491 == D24231 (D24582 - 1) ){ 			D24468 = FALSE; 			if(D241352[D24462]) D24226(D241352[D24462]); 					} 		if(D24491 == D24231 (D24582 + 2) ){ 			D24468 = TRUE; 			if(D241355[D24488]) D24226(D241355[D24488]); 					} 		if(D24491 == D24231 (D24582 - 2) ){ 			D24468 = FALSE; 			if(D241354[D24488]) D24226(D241354[D24488]); 					} 		if(D24491 == D24231 (D24582 + 3) ){ 			D24468 = TRUE; 			if(D241356[D24486]) D24226(D241356[D24486]); 					} 		if(D24491 == D24231 (D24582 - 3) ){ 			D24468 = FALSE; 			if(D241357[D24486]) D24226(D241357[D24486]); 					} 			} 	else if(D24353){ 		if(D24491 == D24582){ 			D24468 = !D24468; 			if(D241359[D24467]) D24226(D241359[D24467]); 					} 		if(D24491 == D24231 (D24582 + 4)){ 			D24468 = FALSE; 			if(D241366[D24485]) D24226(D241366[D24485]); 					} 		if(D24491 == D24231 (D24582 + 1) ){ 			D24468 = TRUE; 			if(D241361[D24462]) D24226(D241361[D24462]); 					} 		if(D24491 == D24231 (D24582 - 1) ){ 			D24468 = FALSE; 			if(D241360[D24462]) D24226(D241360[D24462]); 					} 		if(D24491 == D24231 (D24582 + 2) ){ 			D24468 = TRUE; 			if(D241363[D24488]) D24226(D241363[D24488]); 					} 		if(D24491 == D24231 (D24582 - 2) ){ 			D24468 = FALSE; 			if(D241362[D24488]) D24226(D241362[D24488]); 					} 		if(D24491 == D24231 (D24582 + 3) ){ 			D24468 = TRUE; 			if(D241364[D24486]) D24226(D241364[D24486]); 					} 		if(D24491 == D24231 (D24582 - 3) ){ 			D24468 = FALSE; 			if(D241365[D24486]) D24226(D241365[D24486]); 					} 			} } int D24467; int D24485; int D24486; int D24462; int D24488; function D24117() { 	if(D241128){ 		D24489 += get_rtime(); 			} 	if(D24489 >= 3000){ 		D24489 = 0; 		D241128 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(D24440) && !get_ival(D24441) && !get_ival(D24439) && !get_ival(D24438)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !D24492 && !combo_running(D240)) { 			D24491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D24492 = TRUE; 			D241128 = TRUE; 			D24489 = 0; 			vm_tctrl(0); 			D24116(); 					} 		set_val(D241109, 0); 		set_val(D241110, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		D24492 = FALSE; 			} 	} function D24118() { 	D24171(); 	if (D24375 == 0 && D24376 == 0 && D24377 == 0 && D24378 == 0 && D24379 == 0 && D24380 == 0 && D24381 == 0 && D24382 == 0) { 		D24375 = 4; 		D24376 = 41; 		D24377 = 41; 		D24378 = 42; 		D24379 = 42; 		D24380 = 31; 		D24381 = 31; 		D24382 = 31; 			} 	D24941 = get_slot(); 	} int D24459 = 1; int D24496; int D24497; int D24498 = TRUE; int D24499[6]; int D24500; int D24501; int D24502; int D24503; function D24119(D24120, D24121, D24122) { 	D24122 = (D24122 * 14142) / 46340; 	if (D24121 <= 0) { 		set_polar2(D24120, (D24121 = (abs(D24121) + 360) % 360), min(D24122, D24507[D24121 % 90])); 		return; 			} 	set_polar2(D24120, inv(D24121 % 360), min(D24122, D24507[D24121 % 90])); 	} function D24123(D24120,D24125) { 	if (D24125) return (get_ipolar(D24120, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + D24120), 2) + pow(get_ival(43 + D24120), 2))) + 1; 	} const int16 D24507[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int D24510 = 1; combo D240{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && D24123(POLAR_RS,POLAR_RADIUS) > 2000){ 		D24258(POLAR_RS, 2500, 12000); 		}  D24112(D24113); 	if(!D24497){ 		D24497 = TRUE; 		D24496 = random(0x2B67, 0x1869F) 		set_pvar(SPVAR_1,D24497); 		set_pvar(SPVAR_3,D24496); 		D24498 = TRUE; 			} 	if(!D24503){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			D24502 = D24128(D24502 + 1 ,0,5)D24498 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			D24502 = D24128(D24502 - 1 ,0,5)D24498 = TRUE 		} 		if(event_press(PS4_UP)){ 			D24499[D24502]  = D24128(D24499[D24502] + 1 ,0,9)D24498 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			D24499[D24502]  = D24128(D24499[D24502] - 1 ,0,9)D24498 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			D24500 = 0; 			for(D24501 = 5; 			D24501 >= 0; 			D24501--){ 				D24500 += D24499[D24501] * pow(10,D24501) 			} 			if(D24500 == D24126(D24496)){ 				D24503 = TRUE; 				set_pvar(SPVAR_2,D24503)  			} 			D24498 = TRUE; 					} 			} 	if(D24498){ 		cls_oled(0)if(!D24503){ 			D24132(D24496,D24525,10,OLED_FONT_MEDIUM,OLED_WHITE,D24526)for( D24501 = 0; 			D24501 < 6; 			D24501++){ 				D24132(D24499[D24501],85 - (D24501 * 10),40,OLED_FONT_MEDIUM,!(D24501 == D24502),D24526) 			} 					} 		D24498 = FALSE; 			} 	if(D24503){ 		if (get_ival(D24436) || get_ival(D24440) || get_ival(D24438) || get_ival(D24439) || D24345 || combo_running(D2472) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			D24112(D24113); 					} 		if(get_ival(D24441) > 40 || (!get_ival(D24438) && !get_ival(D24439))){ 			if(get_ival(D24436)){ 				vm_tctrl(0); 				if(get_ptime(D24436) > D24597){ 					set_val(D24436,0); 									} 							} 					} 		if(!get_ival(D24438)){ 			if(get_ival(D24436)){ 				vm_tctrl(0); 				if(get_ptime(D24436) > D24597){ 					set_val(D24436,0); 									} 							} 					} 		if (D24345) { 			vm_tctrl(0); 			if(D24346 < 8050){ 				D24346 += get_rtime(); 							} 			if (D24346 >= 8000) { 				cls_oled(OLED_BLACK); 				D24346 = 0; 				D24345 = FALSE; 							} 					} 		if (block) { 			if (D24510 < 310) { 				D24510 += get_rtime(); 							} 			if (D24510 <= 300 ) { 				D24168(); 							} 			if (D24510 > 300 ) { 				block = FALSE; 				D24510 = 1; 				D24706 = TRUE; 							} 			if (D24510 < 0) { 				D24510 = 1; 							} 			if (D24510 <= 100) { 				combo_stop(D2488); 				combo_stop(D2497); 				combo_stop(D2489); 				combo_stop(D2498); 				combo_stop(D2495); 				combo_stop(D2496); 				combo_stop(D2492); 				combo_stop(D2494); 				combo_stop(D2491); 				combo_stop(D2487); 				combo_stop(D2485); 				combo_stop(D2490); 				combo_stop(D24107); 				combo_stop(D24109); 				combo_stop(D24100); 				combo_stop(D24108); 				combo_stop(D2499); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(D24426){ 			D24427 = FALSE; 					} 		if(D24427){ 			D24426 = FALSE; 					} 		if(D24350){ 			D24351 = FALSE; 			D24352 = FALSE; 			D24353 = FALSE; 					} 		if(D24351){ 			D24350 = FALSE; 			D24352 = FALSE; 			D24353 = FALSE; 					} 		if(D24352){ 			D24350 = FALSE; 			D24351 = FALSE; 			D24353 = FALSE; 					} 		if(D24353){ 			D24350 = FALSE; 			D24351 = FALSE; 			D24352 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				D241163 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				D241163 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				D241051 = !D241051; 				D24228(D241283); 				D24202(D241051, sizeof(D24541) - 1, D24541[0]); 				D24345 = TRUE; 							} 					} 		if (D241051) { 			if(D24370){ 				D24256(); 			} 			if (D24368) { 				D24255(); 							} 			if (event_release(D24441)) { 				D24544 = 1; 							} 			if (D24544 < 8000) { 				D24544 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					D24546 = !D24546; 					D24228(D24546); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (D24546) { 				if (D24546) D24221(D241083); 				if (D24546) { 					D24147(); 									} 							} 			else if (!get_ival(D24441)) { 				D24221(D241086); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						D24341 = TRUE; 						D24348 = TRUE; 						D24347 = FALSE; 						if (!D24341) { 							D24347 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!D24347) { 					if (D24341 || D24342) { 						vm_tctrl(0); 					} 					if (D24341) { 						combo_stop(D2472); 						vm_tctrl(0); 						D24349= D24148(D24349,0  ); 						D24350 = D24148(D24350, 1); 						D24351  = D24148(D24351   ,2  ); 						D24352  = D24148(D24352 , 3); 						D24353  = D24148(D24353 , 4); 						D24354 = D24148(D24354, 5); 						D24355 = D24148(D24355, 6); 						D24356 = D24148(D24356, 7); 						D24357 = D24148(D24357, 8); 						D24358 = D24148(D24358, 9); 						D24359 = D24148(D24359, 10); 						D24360 = D24148(D24360, 11); 						D24361 = D24148(D24361, 12); 						D24362 = D24148(D24362,13); 						D24363 = D24148(D24363, 14); 						D24364 = D24148(D24364, 15); 						D24365 = D24148(D24365, 16); 						D24366 = D24148(D24366, 17); 						D24367 = D24148(D24367, 18); 						D24368 = D24148(D24368, 19); 						D24113 = D24148(D24113, 20); 						D24370 = D24148(D24370, 21); 						D24371              = D24148(D24371              ,22  ); 						D24372              = D24148(D24372              ,23  ); 						D24373               = D24148(D24373               ,24  ); 						if (event_press(PS4_DOWN)) { 							D24343 = clamp(D24343 + 1, 0, D24374); 							D24348 = TRUE; 													} 						if (event_press(PS4_UP)) { 							D24343 = clamp(D24343 - 1, 0, D24374); 							D24348 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							D24341 = FALSE; 							D24347 = FALSE; 							D24348 = FALSE; 							vm_tctrl(0); 							combo_run(D2475); 													} 						if (D24568[D24343][2] == 1) { 							if(D24343 == 0 ){ 								if(D24349 == 2 ){ 									if (event_press(PS4_CROSS)) { 										D24344 = D24568[D24343][0]; 										D24341 = FALSE; 										D24342 = TRUE; 										D24348 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									D24344 = D24568[D24343][0]; 									D24341 = FALSE; 									D24342 = TRUE; 									D24348 = TRUE; 																	} 															} 													} 						D24168(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, D24558[0]); 						D24157(D24343 + 1, D24163(D24343 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, D24560[0]); 						D24157(D24941, D24163(D24941), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(D24343 == 0 ){ 							if(D24349 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, D24562[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, D24563[0]); 															} 													} 						else{ 							if (D24568[D24343][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, D24563[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, D24562[0]); 															} 													} 											} 					if (D24342) { 						D24428               = D24151(D24428, 0); 						D24429               = D24151(D24429, 1); 						D24430             = D24151(D24430, 2); 						D24431           = D24151(D24431, 3); 						D24432             = D24151(D24432, 4); 						D24433             = D24151(D24433, 5); 						D24434              = D24151(D24434, 6); 						D24435           = D24151(D24435, 7); 						D24375          = D24151(D24375, 8); 						D24376   = D24151(D24376, 9); 						D24377 = D24151(D24377, 10); 						D24378      = D24151(D24378, 11); 						D24379    = D24151(D24379, 12); 						D24380    = D24151(D24380, 13); 						D24381    = D24151(D24381, 14); 						D24382      = D24151(D24382, 15); 						D24383      = D24151(D24383, 16); 						D24264              = D24151(D24264, 17); 						D24265           = D24151(D24265, 18); 						D24266            = D24151(D24266, 19); 						D24267            = D24151(D24267, 20); 						D24268= D24151(D24268, 21); 						D24396               = D24151(D24396, 22); 						D24397               = D24151(D24397, 23); 						D24398                   = D24151(D24398, 24); 						D24399                   = D24151(D24399, 25); 						D24400                   = D24151(D24400, 26); 						D24401   = D24151(D24401, 27); 						D24402   = D24151(D24402, 28); 						D24403 = D24151(D24403, 29); 						D24404   = D24151(D24404, 30); 						D24405   = D24151(D24405, 31); 						D24406 = D24151(D24406, 32); 						D24407   = D24151(D24407, 33); 						D24408   = D24151(D24408, 34); 						D24409 = D24151(D24409, 35); 						D24410   = D24151(D24410, 36); 						D24411   = D24151(D24411, 37); 						D24412 = D24151(D24412, 38); 						D24413   = D24154(D24413, 39); 						D24414         = D24154(D24414, 40); 						D24415   = D24151(D24415, 41); 						D24416     = D24151(D24416, 42); 						D24417                   = D24154(D24417, 43); 						D241239 = D24151(D241239, 54); 						D241232 = D24151(D241232, 55); 						D24418               = D24154(D24418, 44); 						D24419 = D24154(D24419, 45); 						D24420     = D24151(D24420, 46); 						D24421               = D24154(D24421, 47); 						D24422 = D24151(D24422, 48); 						D24423 = D24151(D24423, 49); 						D24424 = D24151(D24424, 50); 						D24425 = D24151(D24425, 51); 						D24426               = D24151(D24426, 52); 						D24427                 = D24151(D24427, 53); 						D24384       = D24154(D24384     ,56 ); 						D24385       = D24154(D24385     ,57 ); 						D24386      = D24151(D24386    ,58 ); 						D24387   = D24151(D24387 ,59 ); 						D24388       = D24154(D24388     ,60 ); 						D24389       = D24154(D24389     ,61 ); 						D24390   = D24151(D24390 ,62 ); 						D24391      = D24151(D24391    ,63 ); 						D24392          = D24154(D24392        ,64 ); 						D24393          = D24154(D24393        ,65 ); 						D24394         = D24151(D24394       ,66 ); 						D24444             = D24154(D24444           ,67 ); 						D2429             = D24151(D2429           ,68); 						D24465           = D24151(D24465         ,69); 						D24463         = D24151(D24463       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								D24344 = clamp(D24344 + 1, D24568[D24343][0], D24568[D24343][1]); 								D24348 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								D24344 = clamp(D24344 - 1, D24568[D24343][0], D24568[D24343][1]); 								D24348 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							D24341 = TRUE; 							D24342 = FALSE; 							D24348 = TRUE; 													} 						D24168(); 						D24943 = D24827[D24344][0]; 						D24944 = D24827[D24344][1]; 						if (D24827[D24344][4] == 0) { 							D24157(D24943, D24163(D24943), 4, 20, OLED_FONT_SMALL); 							D24157(D24944, D24163(D24944), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (D24348) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (D24342) { 							print(D24213(D24166(D24577[D24344]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D24577[D24344]); 													} 						else { 							print(D24213(D24166(D24578[D24343]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D24578[D24343]); 													} 						D24348 = FALSE; 					} 									} 				if (!D24341 && !D24342) { 					if (D24347) { 						cls_oled(0); 						combo_run(D2472); 						D24347 = FALSE; 						D24345 = TRUE; 						vm_tctrl(0); 					} 					if(D24349 == 0){ 						D24436      = PS4_CIRCLE; 						D24437      = PS4_CROSS ; 						D24438    = PS4_L1    ; 						D24439  = PS4_R1; 						D24440    = PS4_L2; 						D24441    = PS4_R2; 						D24442     = PS4_SQUARE; 						D24443  = PS4_TRIANGLE; 					} 					else if(D24349 == 1){ 						D24436      = PS4_SQUARE; 						D24437      = PS4_CROSS ; 						D24438    = PS4_L1    ; 						D24439  = PS4_R1; 						D24440    = PS4_L2; 						D24441    = PS4_R2; 						D24442     = PS4_CIRCLE; 						D24443  = PS4_TRIANGLE; 					} 					else if(D24349 == 2){ 						D24436      = D241380[D24428]; 						D24437      = D241380[D24429] ; 						D24438    = D241380[D24430]  ; 						D24439  = D241380[D24431]; 						D24440    = D241380[D24432]; 						D24441    = D241380[D24433]; 						D24442     = D241380[D24434]; 						D24443  = D241380[D24435]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !D241186) { 						set_val(D24437, 0); 						vm_tctrl(0); 						combo_run(D2477); 											} 					if (D24706) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							D24582 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							D241041 = D241391[D24582][0]; 							D24658 = D241391[D24582][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							D24417 += 5; 							D24209(D24213(sizeof(D24584) - 1, OLED_FONT_MEDIUM_WIDTH), D24584[0], D24417); 													} 						if (event_press(PS4_LEFT)) { 							D24417 -= 5; 							D24209(D24213(sizeof(D24584) - 1, OLED_FONT_MEDIUM_WIDTH), D24584[0], D24417); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !D24604 ) { 						if (event_press(PS4_UP)) { 							D24589 += 25; 							D24209(D24213(sizeof(D24590) - 1, OLED_FONT_MEDIUM_WIDTH), D24590[0], D24589); 													} 						if (event_press(PS4_DOWN)) { 							D24589 -= 25; 							D24209(D24213(sizeof(D24590) - 1, OLED_FONT_MEDIUM_WIDTH), D24590[0], D24589); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (D24363) { 						D24247(); 											} 					if (D24364) { 						D24248(); 						D24249(); 											} 					if (!D24364) { 						if (get_ival(D24436)) { 							if (event_press(PS4_RIGHT)) { 								D24597 += 2; 								D24209(D24213(sizeof(D24598) - 1, OLED_FONT_MEDIUM_WIDTH), D24598[0], D24597); 															} 							if (event_press(PS4_LEFT)) { 								D24597 -= 2; 								D24209(D24213(sizeof(D24598) - 1, OLED_FONT_MEDIUM_WIDTH), D24598[0], D24597); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(D24438) ){ 							if(get_ival(D24436) && get_ptime(D24436) > D24597){ 								set_val(D24436,0); 															} 													} 											} 					if(D24367){ 						D24252(); 											} 					if (D24359) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								D24604 = !D24604; 								D24228(D24604); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (D24604 && D24359) { 						vm_tctrl(0); 						combo_stop(D2485); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								D24413 += 10; 								D24209(D24213(sizeof(D24606) - 1, OLED_FONT_MEDIUM_WIDTH), D24606[0], D24413); 															} 							if (event_press(PS4_DOWN)) { 								D24413 -= 10; 								D24209(D24213(sizeof(D24606) - 1, OLED_FONT_MEDIUM_WIDTH), D24606[0], D24413); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						D24221(D241085); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								D24611 = FALSE; 								vm_tctrl(0); 								combo_run(D2478); 															} 							if (event_press(PS4_LEFT)) { 								D24611 = TRUE; 								vm_tctrl(0); 								combo_run(D2478); 															} 							set_val(PS4_L1,0); 													} 											} 					if (D24360) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								D24613 = !D24613; 								D24228(D24613); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (D24613 && D24360) { 						vm_tctrl(0); 						D24221(D241087); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								D24614 = FALSE; 								vm_tctrl(0); 								combo_run(D2479); 															} 							if (event_press(PS4_RIGHT)) { 								D24614 = TRUE; 								vm_tctrl(0); 								combo_run(D2479); 															} 													} 											} 					if(D24352 || D24353 ){ 						D24117(); 											} 					if (D24350) { 						if (D24350 == D241091) D24617 = TRUE; 						if (D24350 == D241092) { 							if (event_press(D241390[-1 +D24383]) && get_brtime(D241390[-1 +D24383]) <= 200) { 								D24617 = !D24617; 								D24228(D24617); 															} 							set_val(D241390[-1 +D24383], 0); 													} 						if (D24350 > 0 && D24350 < 3 && D24617 == 1) { 							D24225(); 													} 						else if (D24350 == 3) { 							if (get_ival(D241390[-1 +D24383])) { 								D24225(); 															} 							set_val(D241390[-1 +D24383], 0); 													} 											} 									if (D24351) { 						if (D24351 == D241091) D24620 = TRUE; 						if (D24351 == D241092) { 							if (event_press(D241390[-1 +D24268]) && get_brtime(D241390[-1 +D24268]) <= 200) { 								D24620 = !D24620; 								D24228(D24620); 															} 							set_val(D241390[-1 +D24268], 0); 													} 						if (D24351 > 0 && D24351 < 3 && D24620 == 1) { 							D24223(); 													} 						else if (D24351 == 3) { 							if (get_ival(D241390[-1 +D24268])) { 								D24223(); 															} 							set_val(D241390[-1 +D24268], 0); 													} 											} 					if (D24354) { 						if (D24354 == 1) { 							D24623 = PS4_R3; 							D24620 = FALSE; 													} 						if (D24354 == 2) { 							D24623 = PS4_L3; 							D24620 = FALSE; 													} 						if (D24354 == 3) { 							D24623 = XB1_PR1; 							D24620 = FALSE; 													} 						if (D24354 == 4) { 							D24623 = XB1_PR2; 							D24620 = FALSE; 													} 						if (D24354 == 5) { 							D24623 = XB1_PL1; 							D24620 = FALSE; 													} 						if (D24354 == 6) { 							D24623 = XB1_PL2; 							D24620 = FALSE; 													} 						if(get_ival(D24623)){ 							if(D24396 || D24397){ 								if( D24396 && event_press(PS4_L1)){ 									D24468 = FALSE; 									D241040 = D24396  ; 									D24226( D24396   ); 								} 								if( D24397 && event_press(PS4_R1)){ 									D24468 = TRUE; 									D241040 =  D24397 ; 									D24226( D24397   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( D24398 ){ 								if(event_press(PS4_SQUARE)){ 									D24468 = FALSE; 									D241040 =  D24398  ; 													combo_stop(D2488); 				combo_stop(D2497); 				combo_stop(D2489); 				combo_stop(D2498); 				combo_stop(D2495); 				combo_stop(D2496); 				combo_stop(D2492); 				combo_stop(D2494); 				combo_stop(D2491); 				combo_stop(D2487); 				combo_stop(D2485); 				combo_stop(D2490); 				combo_stop(D24107); 				combo_stop(D24109); 				combo_stop(D24100); 				combo_stop(D24108); 				combo_stop(D2499); 									D24226( D24398   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									D24468 = TRUE; 									D241040 =  D24398  ; 									D24226( D24398   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( D24399 ){ 								if(event_press(PS4_CROSS)){ 									D24468 = FALSE; 									D241040 =  D24399  ; 									D24226( D24399   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(D2488); 				combo_stop(D2497); 				combo_stop(D2489); 				combo_stop(D2498); 				combo_stop(D2495); 				combo_stop(D2496); 				combo_stop(D2492); 				combo_stop(D2494); 				combo_stop(D2491); 				combo_stop(D2487); 				combo_stop(D2485); 				combo_stop(D2490); 				combo_stop(D24107); 				combo_stop(D24109); 				combo_stop(D24100); 				combo_stop(D24108); 				combo_stop(D2499); 									D24468 = TRUE; 									D241040 =  D24399  ; 									D24226( D24399   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( D24400 ){ 								if(event_press(PS4_R3)){ 									D24468 = FALSE; 									D241040 =  D24400  ; 									D24226( D24400   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(D24623,0); 											} 					if (D24355) { 						if (D24402 == 1) { 							if (event_press(D241390[-1 + D24401]) && !D241138) { 								vm_tctrl(0); 								combo_run(D2482); 															} 							else if (event_press(D241390[-1 + D24401]) && D241138) { 								set_val(D241390[-1 + D24401], 0); 								D24468 = !D24403; 								D241040 = D24355; 								D24226(D24355); 															} 													} 						else { 							if (event_press(D241390[-1 + D24401])) { 								D24468 = !D24403; 								set_val(D241390[-1 + D24401], 0); 								D241040 = D24355; 								D24226(D24355); 															} 													} 					} 					if (D24357) { 						if (D24408 == 1) { 							if (event_press(D241390[-1 +D24407]) && !D241138) { 								vm_tctrl(0); 								combo_run(D2482); 															} 							else if (event_press(D241390[-1 +D24407]) && D241138) { 								set_val(D241390[-1 +D24407], 0); 								D24468 = !D24409; 								D241040 = D24357; 								D24226(D24357); 															} 													} 						else { 							if (event_press(D241390[-1 +D24407])) { 								D24468 = !D24409; 								set_val(D241390[-1 +D24407], 0); 								D241040 = D24357; 								D24226(D24357); 															} 													} 					} 					if (D24356) { 						if (D24405 == 1) { 							if (event_press(D241390[-1 +D24404]) && !D241138) { 								vm_tctrl(0); 								combo_run(D2482); 															} 							else if (event_press(D241390[-1 +D24404]) && D241138) { 								set_val(D241390[-1 +D24404], 0); 								D24468 = !D24406; 								D241040 = D24356; 								D24226(D24356); 															} 													} 						else { 							if (event_press(D241390[-1 +D24404])) { 								D24468 = !D24406; 								set_val(D241390[-1 +D24404], 0); 								D241040 = D24356; 								D24226(D24356); 															} 													} 					} 					if (D24358) { 						if (D24411 == 1) { 							if (event_press(D241390[-1 +D24410]) && !D241138) { 								vm_tctrl(0); 								combo_run(D2482); 															} 							else if (event_press(D241390[-1 +D24410]) && D241138) { 								set_val(D241390[-1 +D24410], 0); 								D24468 = !D24412; 								D241040 = D24358; 								D24226(D24358); 															} 													} 						else { 							if (event_press(D241390[-1 +D24410])) { 								D24468 = !D24412; 								set_val(D241390[-1 +D24410], 0); 								D241040 = D24358; 								D24226(D24358); 															} 													} 					} 					if (D241040 == D24299 && combo_running(D2430)) set_val(D24438, 100); 					if(D24372){ 						if(!block){ 							if(!get_val(D24440)){ 								if( !get_val(D24441)){ 									if(get_val(D24437)){ 										D24639 += get_rtime(); 																			} 									if(D24391){ 										if(get_ival(D24437) && get_ptime(D24437) > D24389){ 											set_val(D24437,0); 																					} 																			} 									if(event_release(D24437)){ 										if( D24639 < D24388 ){ 											D24640 = D24388 - D24639; 											combo_run(D24107); 																					} 										else{ 											if(D24390) combo_run(D24108); 																					} 										D24639 = 0; 																			} 																	} 							} 						} 											} 					if(D24371){ 						if(!block){ 							if(!get_ival(D24440)){ 								if( !get_ival(D24441)){ 									if(get_ival(D24443)){ 										D24641 += get_rtime(); 																			} 									if(event_release(D24443)){ 										if(D24641 < D24384){ 											D24642 = D24384 - D24641; 											combo_run(D24109); 																					} 										else{ 											if(D24387) combo_run(D2499); 																					} 										D24641 = 0; 																			} 																	} 							} 						} 											} 					if(D24373){ 						if(!block){ 							if(get_ival(D24442)){ 								D24643 += get_rtime(); 															} 							if(D24394){ 								if(get_ival(D24442) && get_ptime(D24442) > D24393){ 									set_val(D24442,0); 																	} 															} 							if(event_release(D24442)){ 								if(D24643 && (D24643 < D24392)){ 									D24644 = D24392 - D24643; 									combo_run(D24100); 																	} 								D24643 = 0; 															} 													} 											} 					if (D24361) { 						if (event_press(D241390[-1 +D24415])) { 							vm_tctrl(0); 							combo_run(D2477); 													} 						set_val(D241390[-1 +D24415], 0); 											} 					if(!D24365){ 						D24418 = 0 ; 						D24419 = 0; 						D24420 = FALSE; 						D24421 = 0; 											} 					if (D24366) { 						D24248(); 						if (D24422 == 0) { 							D24647 = FALSE; 							D24446 = 0; 													} 						else { 							D24647 = TRUE; 							D24446 = 40; 													} 						if (D24423 == 0) { 							D24649 = FALSE; 							D24445 = 0; 													} 						else { 							D24649 = TRUE; 							D24445 = 85; 													} 						if (D24424 == 0) { 							D24651 = FALSE; 							D24447 = 0; 													} 						else { 							D24651 = TRUE; 							D24447 = -15; 													} 						if (D24425 == 0) { 							D24653 = FALSE; 													} 						else { 							D24653 = TRUE; 													} 						if(D24424 == 6 || D24423 == 6 || D24422 == 6){ 							if (get_ival(D241390[-1 + D24424]) || get_ival(D241390[-1 + D24423]) || get_ival(D241390[-1 + D24422])){ 								combo_run(D240); 															} 													} 						if (D24651) { 							if (get_val(D241390[-1 + D24424])) { 								set_val(D241390[-1 + D24424], 0); 								combo_run(D2497); 								D241195 = 9000; 															} 													} 						if (D24653) { 							if (get_val(D241390[-1 + D24425])) { 								set_val(D241390[-1 + D24425], 0); 								combo_run(D2498); 								D241195 = 9000; 							} 							if (combo_running(D2498)) { 								if (get_ival(D24437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D24441) > 30) { 									combo_stop(D2498); 																	} 															} 													} 						if (D24649) { 							if (get_val(D241390[-1 + D24423])) { 								set_val(D241390[-1 + D24423], 0); 								D24254(); 								D241195 = 9000; 															} 													} 						if (D24647) { 							if (get_val(D241390[-1 + D24422])) { 								set_val(D241390[-1 + D24422], 0); 								combo_run(D2495); 								D241195 = 9000; 															} 													} 											} 					else{ 						D24446 = 0; 						D24447 = 0; 						D24445 = 0; 											} 					if (D24368) { 						D24255(); 											} 									} 							} 								if (combo_running(D24111) && (  get_ival(D24437) ||   get_ival(D24443) ||         get_ival(D24440) ||        get_ival(D24436) ||        get_ival(D24439) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(D24111); 			} 					} 		else { 			if (!get_ival(D24441)) D24221(D241084); 					} 			} 			D24257(); 	} combo D241 { 	set_val(D24440, 100); 	set_val(D24438,100); 	D24242(); 	wait(400); 	set_val(D24437,100); 	wait(90); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D242 { 	set_val(D24440, 100); 	set_val(D24438,100); 	D24242(); 	wait(400); 	set_val(D24436,100); 	wait(220); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D243 { 	call(D2428); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	call(D2498); 	D24238(D241041, D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 800); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D24439,100); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D244 { 	D24244(); 	wait(50); 	D24242(); 	wait(50); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D245 { 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D246 { 	if (D24468) D24675 = D24582 + 1; 	else D24675 = D24582 - 1; 	D24233(D24675); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 1000); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D247 { 	D24245(); 	D24468 = FALSE; 	wait(D241044 + random(1,5)); 	D24242(); 	wait(D241044 + random(1,5)); 	D24245(); 	wait(D241044 + random(1,5)); 	D24468 = TRUE; 	D24242(); 	wait(D241044 + random(1,5)); 	wait(350); 	} combo D248 { 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24468 = TRUE; 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24468 = FALSE; 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D249 { 	D24468 = TRUE; 	D24242(); 	wait(D241044 + random(1,5)); 	D24245(); 	wait(D241044 + random(1,5)); 	D24468 = FALSE; 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2410 { 	D24468 = FALSE; 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24468 = TRUE; 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2411 { 	D24238(0,0); 	set_val(D24438,100); 	set_val(D24439,100); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	} combo D2412 { 	set_val(D241107, inv(D241041)); 	set_val(D241108, inv(D24658)); 	set_val(D24439, 100); 	set_val(D24438, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D241107, inv(D241041)); 	set_val(D241108, inv(D24658)); 	set_val(D24439, 100); 	set_val(D24438, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 500); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2413 { 	D24238(0, 0); 	set_val(D24440, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(0, 0); 	set_val(D24440, 100); 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(0, 0); 	set_val(D24440, 100); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	D24238(0, 0); 	set_val(D24440, 100); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2414 { 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(inv(D241041), inv(D24658)); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	D24238(inv(D241041), inv(D24658)); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2415 { 	set_val(D24438, 100); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 500); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2416 { 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24468) D24675 = D24582 + 3; 	else  D24675 = D24582 - 3; 	D24233(D24675); 	D24235(D241142,D24661); 	wait(D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24468) D24675 = D24582 + 1; 	else  D24675 = D24582 - 1; 	D24233(D24675); 	D24235(D241142,D24661); 	wait(D241044 + random(1,5)); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2417 { 	set_val(D24438,100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2418 { 	set_val(D24440,100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440,100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440,100); 	D24242(); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D24440,100); 	set_val(D24441,100); 	wait(50); 	wait(350); 	} combo D2419 { 	set_val(D24440,100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440,100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440,100); 	D24242(); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2420 { 	D24243(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	D24242()   if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24468 = !D24468; 	D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 1000); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2421 { 	set_val(D24438,100); 	D24245(); 	wait(50); 	D24238(0,0); 	set_val(D24438,100); 	wait(50); 	set_val(D24438,100); 	D24245(); 	wait(50); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2422 { 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2423 { 	D24245(); 	wait(D241044 + random(1,5)); 	D24246()wait(D241044 + random(1,5)); 	D24245(); 	wait(D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2424 { 	set_val(D24439, 100); 	set_val(D24438, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 20); 	set_val(D24439, 100); 	set_val(D24438, 100); 	if (D24468) D24675 = D24582 + 4; 	else { 		D24675 = D24582 - 4; 			} 	D24233(D24675); 	D24235(D241142, D24661); 	set_val(D24441, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2425 { 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 30); 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2426 { 	set_val(D24440, 100); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D24440, 100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2427 { 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 30); 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2428 { 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	set_val(PS4_R3,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); } combo D2429 { 	D24242(); 	set_val(D24441, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 310); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2430 { 	if (D241040 == D24301) D241045 = 200; 	else D241045 = 1; 	if(D24113 > 4)vm_tctrl(0); 	wait( D241045); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2431 { 	set_val(D24438, 100)D24244(); 	D24238(D241041,D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D24438, 100)D24246(); 	D24238(D241041,D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D24438, 100)D24242(); 	D24238(D241041,D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	D24238(D241041,D24658); 	wait(465); 	D24238(D241041,D24658); 	set_val(D24440, 100); 	set_val(D24441, 100); 	wait(50); 	if (D24468) D24675 = D24582 - 1; 	else D24675 = D24582 + 1; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	if (D24468) D24675 = D24582 + 4; 	else D24675 = D24582 - 4; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 700); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2432 { 	if (D241040 == D24301) D241045 = 200; 	else D241045 = 1; 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241045); 	D24244(); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2433 { 	if (D24468) D24675 = D24582 - 2; 	else D24675 = D24582 + 2; 	D24233(D24675); 	D24235(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 280); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	if (D24468) D24675 = D24582 + 2; 	else D24675 = D24582 - 2; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 45); 	set_val(D24436, 100); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 45); 	D24238(D241142, D24661); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 45); 	D24238(D241142, D24661); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 45); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 500); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2434 { 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 280); 	D24241()  set_val(D24436, 100); 	set_val(D24440, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24241()  set_val(D24440, 100); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24241()  set_val(D24440, 100); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 250); 	D24241()  if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2435 { 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2436 { 	set_val(D24438, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24438, 100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24438, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2437 { 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2438 { 	set_val(D24438, 100); 	D24243(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24438, 100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24438, 100); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2439 { 	D24245(); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24242(); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2440 { 	if (D24468) D24675 = D24582 + 3; 	else D24675 = D24582 - 3; 	D24233(D24675); 	D24238(D241142, D24661); 	set_val(D24436, 100); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440,100); 	D24238(D241142, D24661); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D24440,100); 	D24238(D241142, D24661); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440,100); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2441 { 	set_val(D24438, 100); 	D24244(); 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24438, 100); 	D24246(); 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 65); 	set_val(D24438, 100); 	D24238(0, 0); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if (D24468) D24675 = D24582 + 1; 	else D24675 = D24582 - 1; 	D24233(D24675); 	set_val(D24441,0); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 200); 	set_val(D24441,0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2442 { 	if (D241040 == D24301) D241045 = 200; 	else D241045 = 1; 	if(D24113 > 4)vm_tctrl(0); 	wait( D241045); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2443 { 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2444 { 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if (D241040 == D24314) D24241(); 	set_val(D24440, 100); 	set_val(D24441, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 200); 	if (D241040 == D24314) D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2445 { 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	if (D241040 == D24314) D24241(); 	set_val(D24440, 100); 	set_val(D24441, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 200); 	if (D241040 == D24314) D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2446 { 	call(D2433)call(D2435); 	} combo D2447 {    D24706 = FALSE; 	D24238(D241041, D24658); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(D241041, D24658); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	D24706 = FALSE; 	wait( D241044 + random(1,5)); 	D24238(D241041, D24658); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	D24706 = FALSE; 	wait( D241044 + random(1,5)); 	set_val(D24440, 100); 	set_val(D24441, 100); 	D24238(inv(D241041), inv(D24658)); 	if(D24113 > 4)vm_tctrl(0); 	D24706 = FALSE; 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	D24706 = TRUE; 	} combo D2448 { 	D24238(D241041, D24658); 	set_val(XB1_LS, 100); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(D241041, D24658); 	D24246(); 	set_val(XB1_LS, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	D24238(D241041, D24658); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( D241044 + random(1,5)); 	set_val(D24440, 100); 	set_val(D24441, 100); 	if (D24468) D24675 = D24582 + 4; 	else D24675 = D24582 - 4; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 220); 	if (D24468) D24675 = D24582 + 4; 	else D24675 = D24582 - 4; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if (D24468) D24675 = D24582 + 1; 	else D24675 = D24582 - 1; 	D24233(D24675); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 600); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2449 { 	set_val(D24437, 0); 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 80); 	if(D24113 > 4)vm_tctrl(0); 	wait( 500); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2450 { 	set_val(D24436, 100); 	set_val(D24441,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24441,100); 	set_val(D24436, 100); 	set_val(D24437, 100); 	set_val(D24441,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24436, 0); 	set_val(D24437, 100); 	set_val(D24441,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2451 { 	set_val(D24438,100); 	set_val(D24439,100); 	D24238(inv(D241041), inv(D24658)); 	if(D24113 > 4)vm_tctrl(0); 	wait( 200); 	set_val(D24438,100); 	set_val(D24439,100); 	D24468 = FALSE; 	D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D24438,100); 	set_val(D24439,100); 	D24468 = !D24468; 	D24241(); 	set_val(D24438,100); 	set_val(D24439,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 540); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2452 { 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2453 { 	D24238(inv(D241041), inv(D24658)); 	set_val(D24440, 100); 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(inv(D241041), inv(D24658)); 	set_val(D24440, 100); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(inv(D241041), inv(D24658)); 	set_val(D24440, 100); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2454 { 	set_val(D24438, 100); 	set_val(D24442, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24438, 100); 	set_val(D24442, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24438, 100); 	set_val(D24442, 0); 	set_val(D24437, 100); 	D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24438, 100); 	D24241(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2455 { 	set_val(D24436, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo D2456 { 	set_val(D24436, 100); 	set_val(D24440,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440,100); 	set_val(D24436, 100); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440,100); 	set_val(D24436, 0); 	set_val(D24437, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2457 { 	set_val(D24438, 100); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 300); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2458 { 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2459 { 	set_val(D24438,100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D24438,100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24244(); 	set_val(D24438,100); 	wait(50); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2460 { 	D24238(D241041, D24658); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	D24246(); 	D24238(D241041, D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	D24244(); 	D24238(D241041, D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 320); 	D24238(D241041, D24658); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 220); 	D24238(D241041, D24658); 	D24244(); 	D24238(D241041, D24658); 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2461 { 	call(D2483); 	D24238(0, 0); 	call(D2484); 	call(D2484); 	call(D2484); 	call(D2484); 	call(D2484); 	set_val(D24440, 100); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D24440, 100); 	D24246(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440, 100); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D24440, 100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 600); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2462 { 	set_val(D24440,100); 	set_val(D24439,100); 	if (D24468) D24675 = D24582 - 2; 	else D24675 = D24582 + 2; 	D24233(D24675); 	D24235(D241142, D24661); 	wait(50); 	set_val(D24439,100); 	set_val(D24440,100); 	if (D24468) D24675 = D24582 - 3; 	else D24675 = D24582 + 3; 	D24233(D24675); 	D24235(D241142, D24661); 	wait(50); 	set_val(D24439,100); 	set_val(D24440,100); 	if (D24468) D24675 = D24582 - 4; 	else D24675 = D24582 + 4; 	D24233(D24675); 	D24235(D241142, D24661); 	wait(50); 	set_val(D24439,100); 	set_val(D24440,100); 	if (D24468) D24675 = D24582 - 5; 	else D24675 = D24582 + 5; 	D24233(D24675); 	D24235(D241142, D24661); 	set_val(D24440,100); 	set_val(D24439,100); 	wait(50); 	set_val(D24439,100); 	set_val(D24440,100); 	if (D24468) D24675 = D24582 - 6; 	else D24675 = D24582 + 6; 	D24233(D24675); 	D24235(D241142, D24661); 	wait(50); 	} combo D2463 { 	if(D24113 > 4)vm_tctrl(0); 	wait( 100); 	D24238(0, 0); 	D24244(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(0, 0); 	D24246()  if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(0, 0); 	D24244()  if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(0, 0); 	D24246()  if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(0, 0); 	D24245(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(0, 0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2464 { 	set_val(PS4_R3,100); 	if (D24468) D24675 = D24582 + 1; 	else D24675 = D24582 - 1; 	D24233(D24675); 	D24238(D241142, D24661); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2465 { 	call(D2483); 	D24238(0,0); 	if(D24113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(PS4_R3,100); 	if (D24468) D24675 = D24582 + 1; 	else D24675 = D24582 - 1; 	D24233(D24675); 	D24238(D241142, D24661); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2466 { 	call(D2483); 	D24238(0,0); 	set_val(D24440,100); 	set_val(D24441,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 750); 	} combo D2467 { 	set_val(PS4_R3,100); 	if (D24468) D24675 = D24582 + 2; 	else D24675 = D24582 - 2; 	D24233(D24675); 	D24238(D241142, D24661); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2468 { 	set_val(D24440,100); 	set_val(PS4_R3,100); 	if (D24468) D24675 = D24582 ; 	else D24675 = D24582; 	D24233(D24675); 	D24238(D241142, D24661); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D24440,100); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2469 { 	call(D2483); 	set_val(D24440,100); 	set_val(PS4_R3,100); 	if (D24468) D24675 = D24582 ; 	else D24675 = D24582; 	D24233(D24675); 	D24238(D241142, D24661); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D24440,100); 	D24238(D241142, D24661); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2470 { 	D24238(0,0); 	set_val(D24439,100); 	set_val(D24438,100); 	D24242(); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	if(D24113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D24439,100); 	set_val(D24438,100); 	if(D24113 > 4)vm_tctrl(0); 	wait( 400); 	} int D24140 ; int D24752 ; int D24753 ; int D24754; int D24755; function D24126(D24127){ 	D24752 = 2; 	D24753 = 987654; 	D24140 = 54321; 	D24754 = (D24127 >> D24752) | (D24127 << (32 - D24752)); 	D24755 = (((D24754 >> ((D24754 & 0xF) % 13)) & 0x7FFFF) + D24140) % D24753 + 123456; 	return D24755; 	} define D24757 = -1; define D24525 = -2; define D24759 = -3; define D24760 = 0; define D24526 = 1; function D24128(D24127, D24130, D24131) { 	if(D24127 > D24131) return D24130; 	if(D24127 < D24130) return D24131; 	return D24127; 	} int D24764,D24765; function D24132(D24133,D24134,D24135,D24136,D24137,D24138){ 	if(!D24138){ 		print(D24141(D24139(D24133),D24136,D24134),D24135,D24136,D24137,D24133)     	} 	else{ 		if(D24133 < 0){ 			putc_oled(1,45); 					} 		if(D24133){ 			for(D24764 = D24145(D24133) + D24765 = (D24133 < 0 ),D24133 = abs(D24133); 			D24133 > 0; 			D24764-- , D24765++){ 				putc_oled(D24764,D24133%10 + 48); 				D24133 = D24133/10; 							} 					} 		else{ 			putc_oled(1,48); 			D24765 = 1         		} 		puts_oled(D24141(D24765,D24136,D24134),D24135,D24136,D24765 ,D24137); 			} 	} int D24786; function D24139(D24140) { 	D24786 = 0; 	do { 		D24140++; 		D24786++; 			} 	while (duint8(D24140)); 	return D24786; 	} function D24141(D24142,D24136,D24134) { 	if(D24134 == -3){ 		return 128 - ((D24142 * (7 + (D24136 > 1) + D24136 * 4)) + 3 ); 			} 	if(D24134 == -2){ 		return 64 - ((D24142 * (7 + (D24136 > 1) + D24136 * 4)) / 2); 			} 	if(D24134 == -1){ 		return 3 	} 	return D24134; 	} function D24145(D24146) { 	for(D24764 = 1; 	D24764 < 11; 	D24764++){ 		if(!(abs(D24146) / pow(10,D24764))){ 			return D24764; 			break; 					} 			} 	return 1; 	} function D24147() { 	if (get_ival(D24436)) { 		set_val(D24436, 0); 		if (get_ival(D24438)) D24793 = 50; 		if (!get_ival(D24438)) D24793 = 410; 		combo_run(D2471); 			} 	if (D24792 > 0) set_polar(POLAR_LS, D24792 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) D24792 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) D24792 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) D24792 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) D24792 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D24792 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D24792 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D24792 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D24792 = 270; } int D24793; int D24546; int D24792; combo D2471 { 	set_val(D24436, 100); 	vm_tctrl(0);wait( D24793); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	D24546 = !D24546; } define D24796 = 19; function D24148(D24149, D24150) { 	if (D24343 == D24150) { 		if (event_press(PS4_RIGHT)) { 			D24149 = clamp(D24149 + 1, 0, D24799[D24343]); 			D24348 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			D24149 = clamp(D24149 - 1, 0, D24799[D24343]); 			D24348 = TRUE; 					} 		if (D24343 == 0) { 			print(D24213(D24166(D24803[D24349]) ,OLED_FONT_SMALL_WIDTH),D24796  ,OLED_FONT_SMALL , OLED_WHITE ,D24803[D24349]); 					} 		else if (D24343 == 1) { 			print(D24213(D24166(D24805[D24350]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24805[D24350]); 					} 		else if (D24343 == 2) { 			print(D24213(D24166(D24805[D24351]) ,OLED_FONT_SMALL_WIDTH ),D24796  ,OLED_FONT_SMALL , OLED_WHITE ,D24805[D24351]); 					} 		else if (D24343 == 5) { 			print(D24213(D24166(D24809[D24354]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24809[D24354]); 					} 		else if (D24343 == 6) { 			print(D24213(D24166(D24811[D24355]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24355]); 					} 		else if (D24343 == 7) { 			print(D24213(D24166(D24811[D24356]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24356]); 					} 		else if (D24343 == 8) { 			print(D24213(D24166(D24811[D24357]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24357]); 					} 		else if (D24343 == 9) { 			print(D24213(D24166(D24811[D24358]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24358]); 					} 		else if (D24343 == 20) { 			print(D24213(D24166(D24819[D24113]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24819[D24113]); 					} 		else { 			if (D24149 == 1)        print(D24213(D24166(D24821[1]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24821[1])      else        print(D24213(D24166(D24821[0]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24821[0])     		} 			} 	return D24149; 	} function D24151(D24149, D24150) { 	if (D24344 == D24150) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D24149 += D24827[D24344][2]  				        D24348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D24149 -= D24827[D24344][2]  				        D24348 = TRUE; 							} 			D24149 = clamp(D24149, D24827[D24344][0], D24827[D24344][1]); 		} 		if (D24344 == 8) { 			print(D24213(D24166(D24811[D24375]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24375])     		} 		else if (D24344 == 9) { 			print(D24213(D24166(D24811[D24376]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24376])     		} 		else if (D24344 == 10) { 			print(D24213(D24166(D24811[D24377]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24377])     		} 		else if (D24344 == 11) { 			print(D24213(D24166(D24811[D24378]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24378])     		} 		else if (D24344 == 12) { 			print(D24213(D24166(D24811[D24379]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24379])     		} 		else if (D24344 == 13) { 			print(D24213(D24166(D24811[D24380]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24380])     		} 		else if (D24344 == 14) { 			print(D24213(D24166(D24811[D24381]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24381])     		} 		else if (D24344 == 15) { 			print(D24213(D24166(D24811[D24382]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24382])     		} 		else if (D24344 == 16) { 			print(D24213(D24166(D24846[D24383]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24383])     		} 		else if (D24344 == 17) { 			print(D24213(D24166(D24811[D24264]),OLED_FONT_SMALL_WIDTH ),D24796,OLED_FONT_SMALL,OLED_WHITE,D24811[D24264])  		} 		else if(D24344 == 18){ 			print(D24213(D24166(D24811[D24265]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24811[D24265])  		} 		else if(D24344 == 19){ 			print(D24213(D24166(D24811[D24266]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24811[D24266])  		} 		else if(D24344 == 20){ 			print(D24213(D24166(D24811[D24267]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24811[D24267])  		} 		else if(D24344 == 21){ 			print(D24213(D24166(D24846[D24268]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24846[D24268])       		} 		else if(D24344 == 22){ 			print(D24213(D24166(D24811[D24396]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24396])     		} 		else if (D24344 == 23) { 			print(D24213(D24166(D24811[D24397]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24397])     		} 		else if (D24344 == 24) { 			print(D24213(D24166(D24811[D24398]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24398])     		} 		else if (D24344 == 25) { 			print(D24213(D24166(D24811[D24399]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24399])     		} 		else if (D24344 == 26) { 			print(D24213(D24166(D24811[D24400]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24811[D24400])     		} 		else if (D24344 == 27) { 			print(D24213(D24166(D24846[D24401]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24401])     		} 		else if (D24344 == 28) { 			print(D24213(D24166(D24870[D24402]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24870[D24402])     		} 		else if (D24344 == 29) { 			print(D24213(D24166(D24872[D24403]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24872[D24403])     		} 		else if (D24344 == 30) { 			print(D24213(D24166(D24846[D24404]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24404])     		} 		else if (D24344 == 31) { 			print(D24213(D24166(D24870[D24405]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24870[D24405])     		} 		else if (D24344 == 32) { 			print(D24213(D24166(D24872[D24406]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24872[D24406])     		} 		else if (D24344 == 33) { 			print(D24213(D24166(D24846[D24407]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24407])     		} 		else if (D24344 == 34) { 			print(D24213(D24166(D24870[D24408]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24870[D24408])     		} 		else if (D24344 == 35) { 			print(D24213(D24166(D24872[D24409]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24872[D24409])     		} 		else if (D24344 == 36) { 			print(D24213(D24166(D24846[D24410]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24410])     		} 		else if (D24344 == 37) { 			print(D24213(D24166(D24870[D24411]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24870[D24411])     		} 		else if (D24344 == 38) { 			print(D24213(D24166(D24872[D24412]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24872[D24412])     		} 		else if (D24344 == 41) { 			print(D24213(D24166(D24846[D24415]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24415])     		} 		else if (D24344 == 48) { 			print(D24213(D24166(D24846[D24422]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24422])     		} 		else if (D24344 == 49) { 			print(D24213(D24166(D24846[D24423]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24423])     		} 		else if (D24344 == 50) { 			print(D24213(D24166(D24846[D24424]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24424])     		} 		else if (D24344 == 51) { 			print(D24213(D24166(D24846[D24425]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24846[D24425])     		} 		else if(D24344 == 0){ 			print(D24213(D24166(D24902[D24428]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24428])  		} 		else if(D24344 == 1){ 			print(D24213(D24166(D24902[D24429]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24429])  		} 		else if(D24344 == 2){ 			print(D24213(D24166(D24902[D24430]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24430])  		} 		else if(D24344 == 3){ 			print(D24213(D24166(D24902[D24431]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24431])  		} 		else if(D24344 == 4){ 			print(D24213(D24166(D24902[D24432]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24432])  		} 		else if(D24344 == 5){ 			print(D24213(D24166(D24902[D24433]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24433])  		} 		else if(D24344 == 6){ 			print(D24213(D24166(D24902[D24434]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24434])  		} 		else if(D24344 == 7){ 			print(D24213(D24166(D24902[D24435]),OLED_FONT_SMALL_WIDTH),D24796,OLED_FONT_SMALL,OLED_WHITE,D24902[D24435])  		} 		else{ 			if (D24149 == 1)        print(D24213(D24166(D24821[1]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24821[1])      else        print(D24213(D24166(D24821[0]), OLED_FONT_SMALL_WIDTH), D24796, OLED_FONT_SMALL, OLED_WHITE, D24821[0])     		} 		D24169(0); 			} 	return D24149; 	} function D24154(D24149, D24150) { 	if (D24344 == D24150) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D24149 += D24827[D24344][2]  				        D24348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D24149 -= D24827[D24344][2]  				        D24348 = TRUE; 							} 			if (event_press(PS4_UP)) { 				D24149 += D24827[D24344][3]  				        D24348 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				D24149 -= D24827[D24344][3]  				        D24348 = TRUE; 							} 			D24149 = clamp(D24149, D24827[D24344][0], D24827[D24344][1]); 		} 		D24216(D24149, D24219(D24149)); 	} 	return D24149; 	} int D24929, D24930, D24931; function D24157(D24127, D24159, D24160, D24161, D24136) { 	D24930 = 1; 	D24931 = 10000; 	if (D24127 < 0)  	  { 		putc_oled(D24930, 45); 		D24930 += 1; 		D24127 = abs(D24127); 			} 	for (D24929 = 5; 	D24929 >= 1; 	D24929--) { 		if (D24159 >= D24929) { 			putc_oled(D24930, D24937[D24127 / D24931]); 			D24127 = D24127 % D24931; 			D24930 += 1; 					} 		D24931 /= 10; 			} 	puts_oled(D24160, D24161, D24136, D24930 - 1, OLED_WHITE); } const string D24563 = " No Edit Variable"; const string D24562 = " A/CROSS to Edit "; const string D24558 = "MOD;"; const string D24560 = "MSL;"; int D24941; function D24163(D24146) { 	D24146 = abs(D24146); 	if (D24146 / 10000 > 0) return 5; 	if (D24146 / 1000 > 0) return 4; 	if (D24146 / 100 > 0) return 3; 	if (D24146 / 10 > 0) return 2; 	return 1; 	} const int8 D24937[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int D24943, D24944; const image D24946 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo D2472 { 	call(D2473); 	D24165(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, D24946[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, D24946[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(D2474); 	vm_tctrl(0);wait( 1000); 	D24345 = TRUE; 	} combo D2473 { 	cls_oled(OLED_BLACK); 	} int D24948; enum { 	D24949 = -2, D24950, D24951 = 5, D24952 = -1, D24953 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo D2474 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 D241374[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 D241375[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 D241376[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 D241377[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int D24954; int D24955; int D24956; int D24957; int D24958; int D24959; int D24960; function D24165() { 	D24960 = 3; 	D24958 = D24960 * D241377[D24959]; 	D24957 = D24960 * D241374[D24959]; 	D24955 = ((D24958 * D241376[D24954]) / 328) - ((D24957 * D241375[D24954]) / 328); 	D24956 = ((D24958 * D241375[D24954]) / 328) + ((D24957 * D241376[D24954]) / 328); 	D24958 = D24955; 	D24957 = D24956; 	D24959 += 1; 	D24954 += 45; 	if(D24959 >= 360) { 		D24959 %= 360; 			} 	if(D24954 >= 360) { 		D24954 %= 360; 			} 	pixel_oled(64 + (((D24958 / D24960) * 30) / 328), 32 + (((D24957 / D24960) * 30) / 328), OLED_WHITE); 	} int D24964; function D24166(D24140) { 	D24964 = 0; 	do { 		D24140++; 		D24964++; 			} 	while (duint8(D24140)); 	return D24964; 	} int D24967; const uint8 D241378[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function D24168() { 	for (D24967 = 0; 	D24967 < sizeof(D241378) / sizeof(D241378[0]); 	D24967++) { 		if (get_ival(D241378[D24967]) || event_press(D241378[D24967])) { 			set_val(D241378[D24967], 0); 		} 			} 	} define D24968 = 131; define D24969 = 132; define D24970 = 133; define D24971 = 134; define D24972 = 130; define D24973 = 89; define D24974 = 127; define D24975 = 65; int D24976; int D24977; int D24978 = 1; define D24979 = 36; const string D24980 = "Hold LT/L2 +"; function D24169(D24170) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, D24980[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, D24978); 	putc_oled(1, D24970); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, D24976); 	putc_oled(1, D24971); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, D24977); 	if (D24170) { 		putc_oled(1, D24968); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, D24976); 		putc_oled(1, D24969); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, D24977); 			} 	} const uint8 D241380 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function D24171() { 	D24990 = sizeof(data); 	D24497 = get_pvar(SPVAR_1,0,1,0); 	D24503 = get_pvar(SPVAR_2,0,1,0); 	D24496 = get_pvar(SPVAR_3,11111, 99999,11111); 	D24173(); 	if (D24198(0, 1, 0)) { 		D24354 = D24198(  0, 6, 0); 		D24351 = D24198(0, 3, 0); 		D24352 = D24198(0,1,0); 		D24353 = D24198(0,1,0); 		D24264 = D24198(0, 70, 0); 		D24265 = D24198(0, 70, 0); 		D24266 = D24198(0, 70, 0); 		D24267 = D24198(0, 70, 0); 		D24268 = D24198(0, 22, 8); 		D24355 = D24198(0, 70, 0); 		D24356 = D24198(0, 70, 0); 		D24357 = D24198(0, 70, 0); 		D24358 = D24198(0, 70, 0); 		D24359 = D24198(0, 1, 0); 		D24360 = D24198(0, 1, 0); 		D24361 = D24198(0, 1, 0); 		D24362 = D24198(0, 1, 0); 		D24370 = D24198(0, 1, 0); 		D24396 = D24198(0, 70, 0); 		D24397 = D24198(0, 70, 0); 		D24398 = D24198(0, 70, 0); 		D24399 = D24198(0, 70, 0); 		D24400 = D24198(0, 70, 0); 		D24401 = D24198(1, 25, 1); 		D24402 = D24198(0, 1, 0); 		D24403 = D24198(0, 1, 0); 		D24404 = D24198(1, 25, 5); 		D24405 = D24198(0, 1, 0); 		D24406 = D24198(0, 1, 0); 		D24407 = D24198(0, 25, 2); 		D24408 = D24198(0, 1, 0); 		D24409 = D24198(0, 1, 1); 		D24410 = D24198(1, 25, 8); 		D24411 = D24198(0, 1, 0); 		D24412 = D24198(0, 1, 1); 		D24413 = D24198(350, 600, 350); 		D24414 = D24198(350, 600, 445); 		D24415 = D24198(0, 22, 0); 		D24416 = D24198(0, 1, 0); 		D24417 = D24198(-100, 300, 0); 		D24363 = D24198(0, 1, 0); 		D24364 = D24198(0, 1, 0); 		D24365 = D24198(0, 1, 0); 		D24366 = D24198(0, 1, 0); 		D24367 = D24198(0, 1, 0); 		D24418 = D24198(-150, 150, 0); 		D24419 = D24198(-150, 150, 0); 		D24420 = D24198(0, 1, 0); 		D24421 = D24198(-150, 150, 0); 		D24422 = D24198(0, 22, 0); 		D24423 = D24198(0, 22, 0); 		D24424 = D24198(0, 22, 0); 		D24425 = D24198(0, 22, 0); 		D24597 = D24198(60, 400, 235); 		D24427 = D24198(0, 1, 0); 		D24426 = D24198(0, 1, 0); 		D24350 = D24198(0, 3, 0); 		D24375 = D24198(0, 70, 0); 		D24376 = D24198(0, 70, 0); 		D24377 = D24198(0, 70, 0); 		D24380 = D24198(0, 70, 0); 		D24381 = D24198(0, 70, 0); 		D24382 = D24198(0, 70, 0); 		D24383 = D24198(0, 22, 8); 		D24368 = D24198(0, 1, 0); 		D24378 = D24198(0, 70, 0); 		D24379 = D24198(0, 70, 0); 		D24589 = D24198(0, 2500, 1100); 		D241239 = D24198(0, 1, 0); 		D241232 = D24198(0, 1, 0); 		D24113 = D24198(0, 6, 0); 		D24395 = D24198(0, 1, 0); 		D24349 = D24198(0, 2, 0); 		D24428 = D24198(0, 9, 9); 		D24429 = D24198(0, 9, 8); 		D24430 = D24198(0, 9, 3); 		D24431 = D24198(0, 9, 1); 		D24432 = D24198(0, 9, 4); 		D24433 = D24198(0, 9, 0); 		D24434 = D24198(0, 9, 7); 		D24435 = D24198(0, 9, 6); 		D24371    = D24198(0, 1, 0); 		D24372    = D24198(0, 1, 0); 		D24373     = D24198(0, 1, 0); 		D24384     = D24198(60, 500, 120); 		D24385     = D24198(60, 500, 350); 		D24386    = D24198(0, 1, 0); 		D24387 = D24198(0, 1, 0); 		D24388     = D24198(50, 250, 50); 		D24389     = D24198(100, 850, 100); 		D24390 = D24198(0, 1, 0); 		D24391    = D24198(0, 1, 0); 		D24392        = D24198(80, 500, 120); 		D24393        = D24198(80, 500, 350); 		D24394       = D24198(0, 1, 0); 		D24444           = D24198(2800, 12000, 5000); 		D2429           = D24198(0, 1, 0); 		D24465         = D24198(0, 1, 0); 		D24463       = D24198(0, 1, 0); 	} 	else{ 		D24354 = 0; 		D24351 = 0; 		D24352 = 0; 		D24353 = 0; 		D24264 = 0; 		D24265 = 0; 		D24266 = 0; 		D24267 = 0; 		D24268 = 8; 		D24355 = 0; 		D24356 = 0; 		D24357 = 0; 		D24358 = 0; 		D24359 = 0; 		D24360 = 0; 		D24361 = 0; 		D24362 = 0; 		D24370 = 0; 		D24396 = 0; 		D24397 = 0; 		D24398 = 0; 		D24399 = 0; 		D24400 = 0; 		D24401 = 1; 		D24402 = 0; 		D24403 = 0; 		D24404 = 5; 		D24405 = 0; 		D24406 = 0; 		D24407 = 2; 		D24408 = 0; 		D24409 = 1; 		D24410 = 8; 		D24411 = 0; 		D24412 = 1; 		D24413 = 350; 		D24414 = 445; 		D24415 = 0; 		D24416 = 0; 		D24417 = 0; 		D24363 = 0; 		D24364 = 0; 		D24365 = 0; 		D24366 = 0; 		D24367 = 0; 		D24418 = 0; 		D24419 = 0; 		D24420 = 0; 		D24421 = 0; 		D24422 = 0; 		D24423 = 0; 		D24424 = 0; 		D24425 = 0; 		D24597 = 235; 		D24427 = 0; 		D24426 = 0; 		D24350 = 0; 		D24375 = 0; 		D24376 = 0; 		D24377 = 0; 		D24380 = 0; 		D24381 = 0; 		D24382 = 0; 		D24383 = 8; 		D24368 = 0; 		D24378 = 0; 		D24379 = 0; 		D24589 = 1100; 		D241239 = 0; 		D241232 = 0; 		D24113 = 0; 		D24395 = 0; 		D24349 = 0; 		D24428 = 9; 		D24429 = 8; 		D24430 = 3; 		D24431 = 1; 		D24432 = 4; 		D24433 = 0; 		D24434 = 7; 		D24435 = 6; 		D24371 = 0; 		D24372 = 0; 		D24373 = 0; 		D24384 = 120; 		D24385 = 350; 		D24386 = 0; 		D24387 = 0; 		D24388 = 50; 		D24389 = 100; 		D24390 = 0; 		D24391 = 0; 		D24392 = 120; 		D24393 = 360; 		D24394 = 0; 		D24444     = 5000; 		D2429     = 0; 		D24465     = 0; 		D24463     = 0; 			} 	if (D24349 == 0) { 		D24436 = PS4_CIRCLE; 		D24437 = PS4_CROSS; 		D24438 = PS4_L1; 		D24439 = PS4_R1; 		D24440 = PS4_L2; 		D24441 = PS4_R2; 		D24442 = PS4_SQUARE; 		D24443 = PS4_TRIANGLE; 			} 	else if (D24349 == 1) { 		D24436      = PS4_SQUARE; 		D24437      = PS4_CROSS ; 		D24438    = PS4_L1    ; 		D24439  = PS4_R1; 		D24440    = PS4_L2; 		D24441    = PS4_R2; 		D24442     = PS4_CIRCLE; 		D24443  = PS4_TRIANGLE; 	} 	else if (D24349 == 2) { 		D24436 = D241380[D24428]; 		D24437 = D241380[D24429]; 		D24438 = D241380[D24430]; 		D24439 = D241380[D24431]; 		D24440 = D241380[D24432]; 		D24441 = D241380[D24433]; 		D24442 = D241380[D24434]; 		D24443 = D241380[D24435]; 			} 	} function D24172() { 	D24173(); 	D24196(   1,0,     1); 	D24196(D24354, 0, 6); 	D24196(D24351, 0, 3); 	D24196(D24352, 0 , 1); 	D24196(D24353, 0 , 1); 	D24196(D24264, 0, 70); 	D24196(D24265, 0, 70); 	D24196(D24266, 0, 70); 	D24196(D24267, 0, 70); 	D24196(D24268, 0, 22); 	D24196(D24355, 0, 70); 	D24196(D24356, 0, 70); 	D24196(D24357, 0, 70); 	D24196(D24358, 0, 70); 	D24196(D24359, 0, 1); 	D24196(D24360, 0, 1); 	D24196(D24361, 0, 1); 	D24196(D24362, 0, 1); 	D24196(D24370, 0, 1); 	D24196(D24396, 0, 70); 	D24196(D24397, 0, 70); 	D24196(D24398, 0, 70); 	D24196(D24399, 0, 70); 	D24196(D24400, 0, 70); 	D24196(D24401, 1, 25); 	D24196(D24402, 0, 1); 	D24196(D24403, 0, 1); 	D24196(D24404, 1, 25); 	D24196(D24405, 0, 1); 	D24196(D24406, 0, 1); 	D24196(D24407, 0, 25); 	D24196(D24408, 0, 1); 	D24196(D24409, 0, 1); 	D24196(D24410, 1, 25); 	D24196(D24411, 0, 1); 	D24196(D24412, 0, 1); 	D24196(D24413, 350, 600); 	D24196(D24414, 350, 600); 	D24196(D24415, 0, 22); 	D24196(D24416, 0, 1); 	D24196(D24417, -100, 300); 	D24196(D24363, 0, 1); 	D24196(D24364, 0, 1); 	D24196(D24365, 0, 1); 	D24196(D24366, 0, 1); 	D24196(D24367, 0, 1); 	D24196(D24418, -150, 150); 	D24196(D24419, -150, 150); 	D24196(D24420, 0, 1); 	D24196(D24421, -150, 150); 	D24196(D24422, 0, 22); 	D24196(D24423, 0, 22); 	D24196(D24424, 0, 22); 	D24196(D24425, 0, 22); 	D24196(D24597, 60, 400); 	D24196(D24427, 0, 1); 	D24196(D24426, 0, 1); 	D24196(D24350, 0, 3); 	D24196(D24375, 0, 70); 	D24196(D24376, 0, 70); 	D24196(D24377, 0, 70); 	D24196(D24380, 0, 70); 	D24196(D24381, 0, 70); 	D24196(D24382, 0, 70); 	D24196(D24383, 0, 22); 	D24196(D24368, 0, 1); 	D24196(D24378, 0, 70); 	D24196(D24379, 0, 70); 	D24196(D24589, 0, 2500); 	D24196(D241239, 0, 1); 	D24196(D241232, 0, 1); 	D24196(D24113, 0, 6); 	D24196(D24395, 0, 1); 	D24196(D24349, 0, 2); 	D24196(D24428, 0, 9); 	D24196(D24429, 0, 9); 	D24196(D24430, 0, 9); 	D24196(D24431, 0, 9); 	D24196(D24432, 0, 9); 	D24196(D24433, 0, 9); 	D24196(D24434, 0, 9); 	D24196(D24435, 0, 9); 	D24196(D24371,    0, 1); 	D24196(D24372,    0, 1); 	D24196(D24373,     0, 1); 	D24196(D24384,     60, 500); 	D24196(D24385,     60, 500); 	D24196(D24386,    0, 1); 	D24196(D24387, 0, 1); 	D24196(D24388,     50, 250); 	D24196(D24389,     100, 850); 	D24196(D24390, 0, 1); 	D24196(D24391,    0, 1); 	D24196(D24392,        80, 500); 	D24196(D24393,        80, 500); 	D24196(D24394,       0, 1); 	D24196(D24444 ,         2800,12000); 	D24196(D2429,           0,1); 	D24196(D24465,           0,1); 	D24196(D24463,           0,1); 	} function D24173() { 	D24997 = SPVAR_4; 	D24998 = 0; 	D241000 = 0; 	} int D24998,  D24997, D241000, D241001, D241002; function D24174(D24175) { 	D241001 = 0; 	while (D24175) { 		D241001++; 		D24175 = abs(D24175 >> 1); 	} 	return D241001; 	} function D24176(D24177, D24178) { 	D241001 = max(D24174(D24177), D24174(D24178)); 	if (D24179(D24177, D24178)) { 		D241001++; 	} 	return D241001; 	} function D24179(D24177, D24178) { 	return D24177 < 0 || D24178 < 0; 	} function D24182(D24183) { 	return 1 << clamp(D24183 - 1, 0, 31); 	} function D24184(D24183) { 	if (D24183 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - D24183); } function D24186(D24183) { 	return D24184(D24183 - 1); 	} function D24188(D24175, D24183) { 	if (D24175 < 0) { 		return (abs(D24175) & D24186(D24183)) | D24182(D24183); 	} 	return D24175 & D24186(D24183); } function D24191(D24175, D24183) { 	if (D24175 & D24182(D24183)) { 		return 0 - (D24175 & D24186(D24183)); 	} 	return D24175 & D24186(D24183); } function D24194(D24195) { 	return get_pvar(D24195, 0x80000000, 0x7FFFFFFF, 0); 	} function D24196(D24175, min, max) { 	D241002 = D24176(min, max); 	D24175 = clamp(D24175, min, max); 	if (D24179(min, max)) { 		D24175 = D24188(D24175, D241002); 	} 	D24175 = D24175 & D24184(D241002); 	if (D241002 >= 32 - D24998) { 		D241000 = D241000 | (D24175 << D24998); 		set_pvar(D24997, D241000); 		D24997++; 		D241002 -= (32 - D24998); 		D24175 = D24175 >> (32 - D24998); 		D24998 = 0; 		D241000 = 0; 	} 	D241000 = D241000 | (D24175 << D24998); 	D24998 += D241002; 	if (!D24998) { 		D241000 = 0; 	} 	set_pvar(D24997, D241000); } function D24198(min, max, D24199) { 	D241002 = D24176(min, max); 	D241000 = (D24194(D24997) >> D24998) & D24184(D241002); 	if (D241002 >= 32 - D24998) { 		D241000 = (D241000 & D24184(32 - D24998)) | ((D24194(D24997 + 1) & D24184(D241002 - (32 - D24998))) << (32 - D24998)); 	} 	D24998 += D241002; 	D241000 = D241000 & D24184(D241002); 	if (D24998 >= 32) { 		D24997++; 		D24998 -= 32; 	} 	if (D24179(min, max)) { 		D241000 = D24191(D241000, D241002); 	} 	if (D241000 < min || D241000 > max) { 		return D24199; 	} 		if(D24201[285] != 7581){      D24198(min, max, D24199); 	} 	return D241000; 	} const string D241028 = "SETTINGS"; const string D241029 = "WAS SAVED"; combo D2475 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	D24172(); 	print(15, 2, OLED_FONT_MEDIUM, 1, D241028[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, D241029[0]); 	D241030 = 1500; 	combo_run(D2476); 	} int D241030 = 1500; combo D2476 { 	vm_tctrl(0);wait( D241030); 	cls_oled(0); 	D24347 = FALSE; 	} define D241031 = 0; define D241032 = 1; define D241033 = 2; define D241034 = 3; define D241035 = 4; define D241036 = 5; define D241037 = 6; define D241038 = 7; int D24675; int D241040; int D241041, D24658; int D24468; int D241044 = 49; int D241045 = 200; int D24706 = TRUE; combo D2477 { 	set_val(D24437, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(D24437, 0); 	vm_tctrl(0);wait( 120); 	if (D24416) D24244(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int D24604; int D24611; combo D2478 { 	if (D24611) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (D24611) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (D24611) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(D24436, 100); 	vm_tctrl(0);wait( D24413); 	if (D24611) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	D24604 = FALSE; 	D24228(D24604); 	} int D24613; int D24614; combo D2479 { 	if (D24614) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (D24614) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(D24442, 100); 	set_val(D24439, 100); 	set_val(D24440, 100); 	D241186 = 4000; 	vm_tctrl(0);wait( D24414); 	vm_tctrl(0);wait( 50); 	set_val(D24442, 100); 	vm_tctrl(0);wait( 50); 	D24613 = FALSE; 	D24228(D24613); 	} int D241051 = TRUE; function D24200(D24201) { 	if (D24201) { 		D241052 = D241084; 			} 	else { 		D241052 = D241083; 			} 	combo_run(D2480); 	} int D241052; combo D2480 { 	D24221(D241052); 	vm_tctrl(0);wait( 300); 	D24221(D241081); 	vm_tctrl(0);wait( 100); 	D24221(D241052); 	vm_tctrl(0);wait( 300); 	D24221(D241081); 	} define D241056 = 100; define D241057 = 130; const string D24541 = "SCRIPT WAS"; function D24202(D24127, D24204, D24205) { 	if (!D24341 && !D24342) { 		cls_oled(0); 		print(D24204, 3, OLED_FONT_MEDIUM, OLED_WHITE, D24205); 		if (D24127) { 			print(D24206(sizeof(D241061) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D241061[0]); 		} 		else { 			print(D24206(sizeof(D241062) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D241062[0]); 		} 		D24200(D24127); 			} 	} function D24206(D24142, D24136) { 	return (OLED_WIDTH / 2) - ((D24142 * D24136) / 2); 	} const string D241062 = "OFF"; const string D241061 = "ON"; function D24209(D24133, D24211, D24127) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(D24133, 0, OLED_FONT_MEDIUM, OLED_WHITE, D24211); 	D24216(D24127, D24219(D24127)); 	D24345 = TRUE; 	} const string D24584 = "EA PING"; const string D24606 = "FK_POWER"; const string D24598 = "MaxFnshPwr"const string D24590 = "JK_Agg"; int D24589; int D24597; function D24213(D24142, D24136) { 	return (OLED_WIDTH / 2) - ((D24142 * D24136) / 2); 	} int D241071; int D241072, D241073; function D24216(D24127, D24159) { 	D241071 = 1; 	D241073 = 10000; 	if (D24127 < 0) { 		putc_oled(D241071, 45); 		D241071 += 1; 		D24127 = abs(D24127); 			} 	for (D241072 = 5; 	D241072 >= 1; 	D241072--) { 		if (D24159 >= D241072) { 			putc_oled(D241071, (D24127 / D241073) + 48); 			D24127 %= D241073; 			D241071++; 			if (D241072 == 4) { 				putc_oled(D241071, 44); 				D241071++; 							} 					} 		D241073 /= 10; 			} 	puts_oled(D24213(D241071 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, D241071 - 1, OLED_WHITE); 	} int D241077; function D24219(D24220) { 	D241077 = 0; 	do { 		D24220 /= 10; 		D241077++; 			} 	while (D24220); 	return D241077; 	} int D24620; define D241081 = 0; define D241082 = 1; define D241083 = 2; define D241084 = 3; define D241085 = 4; define D241086 = 5; define D241087 = 6; define D241088 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int D241089; function D24221(D24222) { 	for (D241089 = 0; 	D241089 < 3; 	D241089++) { 		set_rgb(data[D24222][0], data[D24222][1], data[D24222][2]); 			} 	} const int8 D241390[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int D24623 = PS4_L3; define D241091 = 1; define D241092 = 2; define D241093 = 3; define D241094 = 2; define D241095 = 3; define D241096 = 4; define D241097 = 5; define D241098 = 6; define D241099 = 7; define D241100 = 8; define D241101 = 9; int D24617 = FALSE; int D241103; int D241104; int D241105; int D241106; define D241107 = PS4_LX; define D241108 = PS4_LY; define D241109 = PS4_RX; define D241110 = PS4_RY; function D24223 () { if(D24351 == 3){ 		if( get_ival(PS4_RY) < -70  && !D241103 && !combo_running(D240) ) { 			D241103 = TRUE; 			D24468 = FALSE; 			D241040 = D24264; 			            D24226(D24264); 		} 		if( get_ival(PS4_RY) >  70  && !D241104 && !combo_running(D240)) { 			D241104 = TRUE; 			D24468 = TRUE; 			D241040 = D24266; 			           D24226(D24266); 		} 		if( get_ival(PS4_RX) < -70  && !D241105 && !combo_running(D240) ) { 			D241105 = TRUE; 			D24468 = FALSE; 			D241040 = D24267; 			              D24226(D24267); 		} 		if( get_ival(PS4_RX) >  70  && !D241106 && !combo_running(D240) ) { 			D241106 = TRUE; 			D24468 = TRUE; 			D241040 = D24265; 			            D24226(D24265); 		} 			set_val(D241109,0);              set_val(D241110,0);  			} 	else if(D24351 < 3 && !get_ival(XB1_RS) &&  !get_ival(D24440) && !get_ival(D24441) && !get_ival(D24439)) { 		if( get_ival(PS4_RY) < -70  && !D241103 && !combo_running(D240) ) { 			D241103 = TRUE; 			D24468 = FALSE; 			D241040 = D24264; 			            D24226(D24264); 		} 		if( get_ival(PS4_RY) >  70  && !D241104 && !combo_running(D240)) { 			D241104 = TRUE; 			D24468 = TRUE; 			D241040 = D24266; 			           D24226(D24266); 		} 		if( get_ival(PS4_RX) < -70  && !D241105 && !combo_running(D240) ) { 			D241105 = TRUE; 			D24468 = FALSE; 			D241040 = D24267; 			              D24226(D24267); 		} 		if( get_ival(PS4_RX) >  70  && !D241106 && !combo_running(D240) ) { 			D241106 = TRUE; 			D24468 = TRUE; 			D241040 = D24265; 			            D24226(D24265); 		} 			set_val(D241109,0);              set_val(D241110,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		D241103 = 0; 		D241104  = 0; 		D241105  = 0; 		D241106  = 0; 			} 	} function D24224() { 	if (D24491 == D24582) { 		D24468 = FALSE; 		if (D24375) D24226(D24375); 			} 	if (D24491 == D24231(D24582 + 4)) { 		D24468 = FALSE; 		if (D24382) D24226(D24382); 			} 	if (D24491 == D24231(D24582 + 1)) { 		D24468 = TRUE; 		if (D24377) D24226(D24377); 			} 	if (D24491 == D24231(D24582 - 1)) { 		D24468 = FALSE; 		if (D24376) D24226(D24376); 			} 	if (D24491 == D24231(D24582 + 2)) { 		D24468 = TRUE; 		if (D24379) D24226(D24379); 			} 	if (D24491 == D24231(D24582 - 2)) { 		D24468 = FALSE; 		if (D24378) D24226(D24378); 			} 	if (D24491 == D24231(D24582 + 3)) { 		D24468 = TRUE; 		if (D24381) D24226(D24381); 			} 	if (D24491 == D24231(D24582 - 3)) { 		D24468 = FALSE; 		if (D24380) D24226(D24380); 			} 	} int D241128; int D24489 = 0; function D24225() { 	if(D241128){ 		D24489 += get_rtime(); 			} 	if(D24489 >= 3000){ 		D24489 = 0; 		D241128 = FALSE; 			} 	if(D24350 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D24492 && !combo_running(D240)) { 			D24492 = TRUE; 			D241128 = TRUE; 			D24489 = 0; 			D24491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D24224(); 					} 		set_val(D241109, 0); 		set_val(D241110, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(D24440) && !get_ival(D24441) && !get_ival(D24439) && !get_ival(D24438)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D24492 && !combo_running(D240)) { 			D24492 = TRUE; 			D241128 = TRUE; 			D24489 = 0; 			D24491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D24224(); 					} 		set_val(D241109, 0); 		set_val(D241110, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		D24492 = FALSE; 			} 	} function D24226(D24227) { 	D241040 = D24227; 	D24201[-336 + (D24227 * 3)] = TRUE; 	D24706 = FALSE; 	block = TRUE; 	} int D241136; combo D2481 { 	set_rumble(D241136, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function D24228(D24127) { 	if (D24127) D241136 = RUMBLE_A; 	else D241136 = RUMBLE_B; 	combo_run(D2481); 	} int D241137 = 300; int D241138 ; combo D2482 { 	D241138 = TRUE; 	vm_tctrl(0);wait( D241137); 	D241138 = FALSE; 	} combo D2483 { 	D24230(); 	D24238(0, 0); 	vm_tctrl(0);wait( 20); 	D24238(0, 0); 	vm_tctrl(0);wait( 100); 	D24238(0, 0); 	set_val(D24441, 100); 	D24238(0, 0); 	vm_tctrl(0);wait( 60); 	D24238(0, 0); 	vm_tctrl(0);wait( 150); 	D24706 = TRUE; 	vm_tctrl(0);wait( 350); 	} function D24230() { 	D24675 = D24582  D24233(D24675); 	D241041 = D241142; 	D24658 = D24661; 	} combo D2484 { 	set_val(D24440, 100); 	set_val(D24439, 100); 	vm_tctrl(0);wait( 100); 	set_val(D24440, 100); 	vm_tctrl(0);wait( 100); 	D24706 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 D241391[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int D241142, D24661, D24582; int D24491; int D24492; int D241147; function D24231(D24232) { 	D241147 = D24232; 	if (D241147 < 0) D241147 = 8 - abs(D24232); 	else if (D241147 >= 8) D241147 = D24232 - 8  return D241147; 	} function D24233(D24234) { 	if (D24234 < 0) D24234 = 8 - abs(D24234); 	else if (D24234 >= 8) D24234 = D24234 - 8; 	D241142 = D241391[D24234][0]; 	D24661 = D241391[D24234][1]; } function D24235(D24236, D24237) { 	set_val(D241109, D24236); 	set_val(D241110, D24237); 	} function D24238(D24239, D24240) { 	set_val(D241107, D24239); 	set_val(D241108, D24240); 	} function D24241() { 	if (D24468) { 		set_val(D241107, inv(D24658)); 		set_val(D241108, D241041); 			} 	else { 		set_val(D241107, D24658); 		set_val(D241108, inv(D241041)); 			} 	} function D24242() { 	if (D24468) { 		set_val(D241109, inv(D24658)); 		set_val(D241110, D241041); 			} 	else { 		set_val(D241109, D24658); 		set_val(D241110, inv(D241041)); 			} 	} function D24243() { 	if (!D24468) { 		set_val(D241109, inv(D24658)); 		set_val(D241110, D241041); 			} 	else { 		set_val(D241109, D24658); 		set_val(D241110, inv(D241041)); 			} 	} function D24244() { 	set_val(D241109, D241041); 	set_val(D241110, D24658); 	} function D24245() { 	set_val(D241109, inv(D241041)); 	set_val(D241110, inv(D24658)); 	} function D24246() { 	set_val(D241109, 0); 	set_val(D241110, 0); 	} int D241163; function D24247() { 	if ((event_press(D24437)  ) && !combo_running(D2485) && (D241186  <= 0 || (D241186 < 3000 && D241186 > 1  )) && !get_ival(D24441) && D24544 > 500 &&!get_ival(D24440) &&!get_ival(D24436) &&!get_ival(D24439) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(D2485) ) { 		combo_run(D2485); 			} 	if (combo_running(D2485) && (        get_ival(D24441) ||        get_ival(D24440) ||        get_ival(D24436) ||        get_ival(D24439) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(D2485); 		D241309 = TRUE; 			} 	} combo D2485 { vm_tctrl(0);wait(750); set_val(D24438,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(D241163 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo D2486 { 	vm_tctrl(0);wait( 800); 	D241187 = 0; 	} int D241164 = 1600; int D241165 = 1600; int D241166 = 1600; int D241167 = TRUE; int D241168 = TRUE; int D24653 = FALSE; int D241170 = TRUE; int D24647 = FALSE; int D241172 = TRUE; int D24649 = FALSE; int D241174 = TRUE; int D24651 = FALSE; function D24248(){ 	if (get_ival(D24438)) { 		D241176 = 1000; 		D241195 = 0; 		D24544 = 1; 		combo_stop(D2495); 			} 	if (event_press(D24440) || event_press(D24423)) { 		D241176 = 4000; 		D241195 = 0; 		D241164 = 1600; 			} 	if (get_ival(D24439) && !get_ival(D24438) ) { 		D241176 = 0; 		D241195 = 0; 		D241164 = 1600; 			} 	else if (get_ival(D24438)){ 		D241176 = 1000; 			} 	if (D241176 > 0) { 		D241176 -= get_rtime(); 			} 	if (D241176 < 0) { 		D241176 = 0; 			} 	D241255 = D24417; 	if (event_release(D24437)) { 		D241182 = 1; 		D241195 = 0; 		D24544 = 1; 	} 	if (event_release(D24443)) { 		D241183 = 1; 		D241195 = 0; 		D24544 = 1; 	} 	if (event_release(D24438)) { 		D241165 = 1; 		D241195 = 0; 		D241164 = 1600; 			} 	if (event_release(D24439)) { 		D241166 = 1; 		D241195 = 0; 		D241164 = 1600; 			} 	if (event_release(D24442) || (get_ival(D24443) && get_ival(D24438))) { 		D241186 = 4000; 		D241195 = 0; 	} 	if (get_ival(D24437) && D241186 < 4000 && D241186 > 3500) { 		D241187 = D241186; 		D241186 = 0; 			} 	if (D241164 < 1510) { 		D241164 += get_rtime(); 			} 	if (D241165 < 1600) { 		D241165 += get_rtime(); 			} 	if (D241166 < 1600) { 		D241166 += get_rtime(); 			} 	if (D241186 > 0) { 		D241186 -= get_rtime(); 			} 	if (D241186 < 0) { 		D241186 = 0; 			} 	if (D241182 < 5100) { 		D241182 += get_rtime(); 			} 	if (D241183 < 4100) { 		D241183 += get_rtime(); 			} 	if (D241195 > 0) { 		D241195 -= get_rtime(); 			} 	if (D241195 < 0) { 		D241195 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		D241164 = 1; 		D241195 = 0; 			} 	if (combo_running(D2492)) { 		set_val(D24437, 0); 		if(get_ival(D24437)){ 			D241198 = 0; 			combo_stop(D2487); 			set_val(D24437, 0); 			combo_stop(D2492); 			combo_run(D2449); 					} 			} 	if ((combo_running(D2497) || combo_running(D2488))) { 		set_val(D24437, 0); 		if(get_ival(D24437)){ 			D24544 = 1; 			D241198 = 0; 			combo_stop(D2487); 			set_val(D24437, 0); 			combo_stop(D2497); 			combo_stop(D2488); 			combo_run(D2449); 					} 			} 	if (event_press(D24436)) { 		combo_run(D2486); 			} 	if (D24544 > 1500) { 		if (D241165 < 1500) { 			D241200 = 120; 					} 		if (D241166 < 1500) { 			D241200 = 228; 					} 		else { 			D241200 = 200; 					} 			} 	if (D24544 < 1500) { 		D241200 = 450; 			} 	if (D24544 > 2700) { 		D241204 = 920; 			} 	else if (D24544 >= 0 && D24544 < 2700) { 		D241204 = 725; 			} 	} function D24249() { 	if (D241167) { 		if ((D24544 <= 600 || (D241164 <= 1500 && D241164 > 1) || ( D241165 <= 150 || D241166 <= 150)) && event_press(D24436) ) { 			if (!get_ival(D24439) && !get_ival(D24438) && !get_ival(D24440) && !get_ival(D24441)) { 				set_val(D24436, 0); 				if (D241186 < 4000 && D241186 > 1) { 					set_val(D24436, 0); 					combo_run(D2490); 									} 				else { 					set_val(D24436, 0); 					combo_run(D2488); 					D241195 = 9000; 				} 							} 					} 			} 	if (D241174) { 		if (D24544 > 1000 && !D241195 && (!get_ival(D24439) && !get_ival(PS4_L3) && event_press(D24436)) &&  D241165 > 150 &&  D241166 > 150) { 			if (!get_ival(D24438) && !get_ival(D24440)) { 				set_val(D24436, 0); 				if (((D241183 > 1 && D241183 <= 2500) || (D241182 > 1 && D241182 <= 3000)) &&  D241164 != 1600) { 					set_val(D24436, 0); 					combo_run(D2489); 					D241195 = 9000; 									} 				else if (((D241183 > 2500 && D241183 <= 4000) || (D241182 > 3000 && D241182 <= 3500))  &&  D241164 != 1600) { 					set_val(D24436, 0); 					combo_run(D2488); 					D241195 = 9000; 									} 				else if ((D241186 < 4000 && D241186 > 1)) { 					set_val(D24436, 0); 					combo_run(D2490); 					D241195 = 9000; 									} 				else { 					set_val(D24436, 0); 					D24253(); 					D241195 = 9000; 									} 				D241195 = 9000; 							} 					} 			} 	if (D241168) { 		if (get_ival(D24438) && get_ival(D24439)) { 			if (!get_ival(D24440) && !get_ival(D24441) && (D241186 && D241182 > 1 && D241182 <= 1500) || (!D241186 && D241182 > 1 && D241182 <= 1500) || (D241182 > 1500 && !D241186) && !D241195) { 				if (event_press(D24436)) { 					set_val(D24436, 0); 					combo_run(D2498); 					D241195 = 9000; 									} 							} 					} 			} 	if (D241172) { 		if (!get_ival(D24441) && !get_ival(D24438) && !get_ival(D24439)) { 			if (get_ival(D24440) && get_ival(D24436)) { 				D24254(); 				set_val(D24436, 0); 				D241195 = 9000; 							} 					} 			} 	if (D241170) { 		if (get_ival(D24439) && !get_ival(D24438) && !D241176) { 			if (!get_ival(D24440) && !get_ival(D24441) && !D241195) { 				if (get_ival(D24436) && D24544 >= 1000) { 					set_val(D24436, 0); 					combo_run(D2495); 					D241195 = 9000; 									} 				if (get_ival(D24436) && D24544 < 1000 && !D241176) { 					set_val(D24436, 0); 					combo_run(D2496); 									} 							} 					} 			} 	if(combo_running(D2490)){ 		D241198 = 0; 		combo_stop(D2487)   	} 	if (get_ival(D24438) || D241176 > 0) { 		combo_stop(D2495); 		combo_stop(D2497); 		combo_stop(D2496); 			} 	if (combo_running(D2488) || combo_running(D2492) || combo_running(D2497) || combo_running(D2498) || combo_running(D2495)) { 		if (get_ival(D24437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D24441) > 30) { 			combo_stop(D2492); 			combo_stop(D2497); 			combo_stop(D2498); 			combo_stop(D2495); 			combo_stop(D2488); 			D241198 = 0; 			combo_stop(D2487)     		} 			} 	if (combo_running(D2488) || combo_running(D2489)) { 		if (get_ival(D24437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D24441)) { 			combo_stop(D2490); 			combo_stop(D2489); 			combo_stop(D2488); 			D241198 = 0; 			combo_stop(D2487)     		} 			} 	if (event_press(D24436) && D241195 > 100 && D241195 < 8990) { 		set_val(D24436, 0); 		combo_stop(D2492); 		combo_stop(D2497); 		combo_stop(D2498); 		combo_stop(D2495); 		combo_stop(D2488); 		D241198 = 0; 		combo_stop(D2487)    combo_run(D2491); 			} 	if (!D24653) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2498); 					} 			} 	if (!D24647) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2495); 					} 			} 	if (!D24649) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2492); 			D241198 = 0; 			combo_stop(D2487)     		} 			} 	if (!D24651) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2497); 					} 			} 	if ((get_ival(D24441) || get_ival(D24437)) && !D24354) { 		combo_stop(D244); 		combo_stop(D2447); 		combo_stop(D2433); 			} 	} define D241208 = 15; define D241209 = 15; int D241210 = 0; define D241211 = 8000; define D241212 = 4; define D241213 = 2000; int D241198 = 0; const int16 D241392[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 D241393[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int D241215 = FALSE; int D241216; int D241217; int D241218; int D241219; function D24250 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D241218 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D241215 = FALSE; 		for ( D24967 = 0; 		D24967 < sizeof(D241393) / sizeof(D241393[0]); 		D24967++) { 			if (D241218 == D241393[D24967]) { 				D241215 = TRUE; 				break; 							} 					} 		if (!D241215) { 			D241216 = D241393[0]; 			D241217 = abs(D241218 - D241393[0]); 			for ( D24967 = 1; 			D24967 < sizeof(D241393) / sizeof(D241393[0]); 			D24967++) { 				D241219 = abs(D241218 - D241393[D24967]); 				if (D241219 < D241217) { 					D241216 = D241393[D24967]; 					D241217 = D241219; 									} 							} 			set_polar(POLAR_LS, D241216, 32767); 					} 			} } function D24251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D241218 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D241215 = FALSE; 		for ( D24967 = 0; 		D24967 < sizeof(D241392) / sizeof(D241392[0]); 		D24967++) { 			if (D241218 == D241392[D24967]) { 				D241215 = TRUE; 				break; 							} 					} 		if (!D241215) { 			D241216 = D241392[0]; 			D241217 = abs(D241218 - D241392[0]); 			for ( D24967 = 1; 			D24967 < sizeof(D241392) / sizeof(D241392[0]); 			D24967++) { 				D241219 = abs(D241218 - D241392[D24967]); 				if (D241219 < D241217) { 					D241216 = D241392[D24967]; 					D241217 = D241219; 									} 							} 			set_polar(POLAR_LS, D241216, 32767); 					} 			} } int D241232; function D24252() { 	if (combo_running(D2487) && (        get_ival(D24441) ||        get_ival(D24436) ||        get_ival(D24437) ||        get_ival(D24442) ||        get_ival(D24443) ||        get_ival(D24438)      )) { 		combo_stop(D2487); 		D241198 = 0; 			} 	if (D241210 == 0) { 		if ( ( D241186 == 0 && !combo_running(D2490) && !combo_running(D2498) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( event_press(D24436) || (D241198 == 1 ||     combo_running(D2496) || combo_running(D2494)|| combo_running(D2492) ||  combo_running(D2495)     || combo_running(D2488) || combo_running(D2497) || combo_running(D2489) ||  combo_running(D2491)  ))     ) { 			if(D24426)D24251 (); 			else if (D24427)D24250 (); 			combo_restart(D2487); 					} 			} 	else{ 		combo_stop(D2487); 			} 	} combo D2487 { 	combo_stop(D24103); 	if(D24426)D24251 (); 	else if (D24427)D24250 (); 	vm_tctrl(0);wait( 4000); 	D241198 = 0; 	} combo D2488 { 	set_val(D24438,0); 	set_val(D24436, 100); 	vm_tctrl(0);wait( random(210, 215) + D24419); 	set_val(D24436, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function D24253() { 	if (D24544 > 600 && D24544 <= 800) { 		D241233 = 240; 			} 	if (D24544 > 800 && D24544 <= 1000) { 		D241233 = 230; 			} 	if (D24544 > 1000 && D24544 <= 1500) { 		D241233 = 225; 			} 	if (D24544 > 1500 && D24544 <= 2000) { 		D241233 = 235; 			} 	if (D24544 > 2000) { 		D241233 = 218; 			} 	combo_run(D2497); 	} combo D2489 { 	set_val(D24436, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 500); 	} combo D2490 { 	set_val(D24436, 100); 	vm_tctrl(0);wait( 205); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 300); 	} combo D2491 { 	set_val(D24436, 100); 	vm_tctrl(0);wait( 190); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 400); 	} int D241238; int D241239; int D2429; int D24465; int D24463; int D241243; int D241244; combo D2492 { 	if (D241239) { 		set_val(D24436, 0); 		D241243 = 350; 			} 	else { 		D241243 = 0; 			} 	if (D241239) { 		D24242(); 		D24238(0, 0); 			} 	vm_tctrl(0); 	wait(D241243); 	if (D241239) { 		set_val(D24439, 0); 		D241243 = 60; 			} 	else { 		set_val(D24440, 0); 		D241243 = 60; 			} 	set_val(D24436,0); 	vm_tctrl(0);wait(D241243); 	set_val(D24440, 0); 	set_val(D24439, 0); 	set_val(D24436,0); 	vm_tctrl(0);wait(D241243); 	if (D241239) { 		D241243 = 0; 			} 	else { 		D241243 = 60; 			} 	set_val(D24439, 0); 	set_val(D24440, 0); 	set_val(D24436,0); 	vm_tctrl(0);wait(D241243); 	set_val(D24436, 100); 	set_val(D24440, 100); 	vm_tctrl(0);wait(random(265, 268) +   D24418 ); 	set_val(D24440, 100); 	set_val(D24436, 0); 	if (D241239) { 		D241243 = 15; 			} 	else { 		D241243 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + D241256 + D241255 + D241243 ); 	set_val(D24440, 100); 	set_val(D24436, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D24436, 0); 	set_val(D24440, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(D24440, 100); 	vm_tctrl(0);wait(2500); 	} int D241183; int D241186; int D24544; int D241182; int D241255; int D241256 = 111; int D241176; int D241187; int D241259; function D24254() { 	D241259 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((D241259 > 5 && D241259 < 40) ) {         D24468 = FALSE; 			} 	if ((D241259 > 40 && D241259 < 85)){ 		D24468 = TRUE; 		    } 	if ((D241259 > 95 && D241259 < 130) ) { 		D24468 = FALSE; 		    } 	 if((D241259 > 130 && D241259 < 175)) { 		D24468 = TRUE; 		} 			if ((D241259 > 185 && D241259 < 220) ) {        D24468 = FALSE; 			} 	if ((D241259 > 220 && D241259 < 265)){        D24468 = TRUE; 		    } 	if ((D241259 > 275 && D241259 < 310) ) {        D24468 = FALSE; 		    } 	 if((D241259 > 310 && D241259 < 355)) { 		D24468 = TRUE; 		} 	if (D241186 == 0 && (D24544 >= 750 || ((D241187 > 3000 && D241182 > 1 && D241182 < 5000)))) { 		if (D24544 <= 2000 && D241182 > 1500) { 			set_val(D24436, 0); 			D241256 = 170; 		} 		if (D24544 <= 2000 && D241182 > 1 && D241182 <= 1500) { 			set_val(D24436, 0); 			D241256 = 202; 					} 		if (D24544 > 2000 || (D241182 > 1 && D241182 <= 1500)) { 			set_val(D24436, 0); 			D241256 = 151; 					} 		if ((D24544 > 2000 && D241182 > 1500) || D241187 > 1 && D241182 > 1) { 			set_val(D24436, 0); 			D241256 = 152; 					} 		if ((D24544 < 2000 && D241182 > 1500) || D241186 > 1 && D241182 > 1) { 			set_val(D24436, 0); 			D241256 = 149; 					} 		if (D241182 > 1500) { 			set_val(D24436, 0); 			D241256 = 148; 					} 		if (!D24544 > 2000 && D241187 > 1 && D241182 > 1 && D241182 <= 1500) { 			set_val(D24436, 0); 			D241256 = 147; 					} 		set_val(D24436, 0); 		combo_stop(D2497); 		combo_stop(D2498); 		combo_stop(D2495); 		combo_stop(D2488); 		combo_stop(D2494); 		combo_stop(D2491); 		combo_stop(D2490); 		combo_run(D2492); 			} 	else { 		if (D241186) { 			set_val(D24436, 0); 			combo_run(D2493); 					} 		else { 			if (D24544 < 750) { 				set_val(D24436, 0); 				combo_run(D2494); 							} 					} 			} } combo D2493 { 	set_val(D24436, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(D24436, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo D2494 { 	set_val(D24436, 100); 	vm_tctrl(0);wait( random(200, 205) + D24418 )  set_val(D24436, 0); 	vm_tctrl(0);wait( 700); 	} int D241269 = 246; int D241200 = 150; int D241271 = 0; combo D2495 { 	set_val(D24440, 100); 	set_val(D24439, 0); 	set_val(D24436,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D24440, 0); 	set_val(D24439, 0); 	set_val(D24436,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	D241271 = D24417; 	set_val(D24439, 100); 	set_val(D24436,0); 	vm_tctrl(0);wait( 60); 	set_val(D24439, 100); 	set_val(D24436, 100); 	vm_tctrl(0);wait( D241269 + 10 + random(-2, 2) +  D24421); 	set_val(D24439, 100); 	set_val(D24436, 0); 	D241238 = D241200; 	vm_tctrl(0);wait( D241200 + D241271 - 58 + D24446 ); 	set_val(D24439, 100); 	if(D241232)set_val(D24436, 100); 	vm_tctrl(0);wait( 60); 	set_val(D24439, 100); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 3000); 	} combo D2496 { 	set_val(D24439, 100); 	set_val(D24436, 100); 	vm_tctrl(0);wait( 160 + D24421 ); 	set_val(D24439, 100); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 3000); 	} int D241195; int D241273 = 220; int D241233; int D241275 = 0; combo D2497 { 	D241275 = D24417; 	set_val(D24436, 100); 	vm_tctrl(0);wait( D241273); 	set_val(D24436, 0); 	vm_tctrl(0);wait( D241233 + (D241275) + 22 + D24447); 	if(D241232)set_val(D24436, 100); 	vm_tctrl(0);wait( D241273); 	set_val(D24436, 0); 	vm_tctrl(0);wait( 2000); 	} int D241277 = TRUE; int D241204; int D241279 = 260; int D241280 = 0; combo D2498 { 	set_val(D24438, 100); 	set_val(D24439, 100); 	if (D241277) { 		D241280 = D24417; 			} 	else { 		D241280 = 0   	} 	set_val(D24436, 100); 	vm_tctrl(0);wait( D241279); 	vm_tctrl(0);wait( D241204 + D241280 + 40)  } int D241283; int D241284 = 145; int D24640; int D24639; int D24642; int D24641; combo D2499 { 	set_val(D24443, 0); 	vm_tctrl(0);wait( 30); 	set_val(D24443, 100); 	vm_tctrl(0);wait( 60); 	} int D24643; int D24644; combo D24100 { 	set_val(D24442, 100); 	vm_tctrl(0);wait( D24644); 	} define D241291 = TRUE; define D241292 = 95; define D241293 = 10; define D241294 = 70; define D241295 = FALSE; define D241296 = 50; define D241297 = 95; define D241298 = XB1_LT; define D241299 = XB1_RT; define D241300 = XB1_LX; define D241301 = XB1_LY; define D241302 = POLAR_LS; int D241303; function D24255() { 	if (    get_ival(D24441) > 30 &&    (get_ival(D24440) || get_ival(D24437)) &&    (!get_ival(D24442) || !get_ival(D24436))  ) { set_val(D24441, 0); 	if( D24123(POLAR_LS,POLAR_RADIUS) > 2000){ 		D24258(POLAR_LS, 5000, 2500); 		} 		if(!get_ival(D24436)){ 			set_val(D24440,100); 					} 		else{ 			set_val(D24440,0); 					} 		  combo_run(D24102); 		if(D24420 == TRUE){ 			combo_run(D24101); 					} 			} 	else { 		combo_stop(D24102); 		combo_stop(D24101); 			} 	} combo D24101 { 	if (D24420 == TRUE) { 		set_val(D24439, 100);set_val(D24437, 100); 		D241305 = 60; 			} 	else { 		D241305 = 0; 			} 	set_val(D24440, 0); 	vm_tctrl(0);wait( D241305); 	if (D24420 == TRUE) { 		set_val(D24439, 0);set_val(D24437, 0); 		D241305 = 60; 			} 	else { 		D241305 = 0; 			} 	set_val(D24440, 0); 	vm_tctrl(0);wait( D241305); 	if (D24420 == TRUE) { 		set_val(D24439, 100);set_val(D24437, 100); 			} 	vm_tctrl(0);wait( 750); 	vm_tctrl(0);wait( 750); 	} combo D24102 { 	if(!get_ival(D24436)){ 		set_val(D24440,100); 			} 	else{ 		set_val(D24440,0); 			} 	set_val(D24441, 100); 	vm_tctrl(0);wait(D24589); 	if(!get_ival(D24436)){ 		set_val(D24440,100); 			} 	else{ 		set_val(D24440,0); 			}     set_val(D24441, 0); 	vm_tctrl(0);wait(500); 	} int D241307; int D241305 ; int D241309 = TRUE; int D241310; int D241311; int D241312; int D241313; int D241314; function D24256() { 	  if(event_release(D24441)){        D241315 = 500;     }    if(event_release(D24443) || (get_ival(D24438) && ( event_release(D24437) ))){        D241316 = 3000;     }    if(D241315){        D241315 -= get_rtime();    }    if(D241316){        D241316 -= get_rtime();    }    if ( get_ival(D24441) && (!get_ival(D24440)) && D241315 <= 0 && D241316 <= 0 ) {           combo_stop(D24103);         combo_stop(D24104);         combo_run(D24105);    }    if (( get_ival(XB1_LS) || get_ival(XB1_RS)  || get_ival(D24438)  || get_ival(D24440) ||      get_ival(D24437) || get_ival(D24443) || get_ival(D24436) || get_ival(D24442)   || get_ival(XB1_PR1) ||     get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(D241109))> 20 || abs(get_ival(D241110))> 20) ) )){        combo_stop(D24103);        combo_run(D24104);        combo_stop(D24105);    }    if ( get_val(D24441)&&( (abs(get_ival(D241109))> 20 || abs(get_ival(D241110))> 20) )){    combo_stop(D24105);    }    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(D24440) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(D24104)     && !get_ival(D24438) &&  !get_ival(D24441) && !get_ival(D24437) && !get_ival(D24443) && !get_ival(D24436)){        combo_run(D24103);    } } int D241315; int D241316; combo D24103 {    	if( D24123(POLAR_LS,POLAR_RADIUS) > 2000){ 		D24258(POLAR_LS, 5000, D24444); 		} 	wait(60); 	if( D24123(POLAR_LS,POLAR_RADIUS) > 2000){ 	D24258(POLAR_LS, 5000, 1500); 	} 	wait(120); 	} combo D24104 { D24258(POLAR_LS, 5000, 10000); 	wait(200); }  combo D24105 { 	D24258(POLAR_LS, 5000, 12000); 	wait(2000); } combo D24106{ } combo D24107 { 	set_val(D24437,100); 	vm_tctrl(0);wait( D24640); 	set_val(D24437,  0); 	vm_tctrl(0);wait( 30); 	if(D24390){ 		set_val(D24439,100); 			} 	vm_tctrl(0);wait( 60); 	} combo D24108 { 	set_val(D24439,  100); 	vm_tctrl(0);wait( 60);     wait( 60); 	} combo D24109 { 	set_val(D24443,100); 	vm_tctrl(0);wait( D24642); 	set_val(D24443,  0); 	vm_tctrl(0);wait( 30); 	if(D24387){ 		set_val(D24443,100); 			} 	vm_tctrl(0);wait( 60); 	} int D24990 int D241327 combo D24110 { 	combo_suspend(D24110) 	vm_tctrl(0);wait(361) } function D24257 (){ 	if(D24990[D241327] != 361){ 	    D241327-- 	} 	else{ 		if(inv(D241327) != 290){ 			D24257(); 		} 	} } int D241330; combo D24111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int D24175; function D24258(D24259, D24260, D24261) {   D24175 = D24123(D24259, POLAR_RADIUS);   if(D24260) {     if(D24175 <= D24260) D24175 = (D24175 * 5000) / D24260;     else D24175 = ((5000 * (D24175 - D24260)) / (10000 - D24260)) + 5000;   }   if (D24261) D24175 = (D24175 * D24261) / 10000;   set_polar2(D24259, D24123(D24259, POLAR_ANGLE), min(D24175, 14142));   if (D24259 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; }  