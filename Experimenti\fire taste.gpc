// Definition der "Jump"-Taste, die der XB1_A-Taste (Xbox Controller A-Taste) entspricht
define Jump = XB1_A;

// Hauptfunktion, die kontinuierlich ausgeführt wird
main {
    // Überprüft, ob die "Jump"-Taste gedrückt wird und ob sie seit mindestens 250 Millisekunden gehalten wird
    if(get_ival(Jump) && get_ptime(Jump) >= 250) {
        // Startet die Combo namens 'Turbo_Jump'
        combo_run(Turbo_Jump);
    }
    else {
        // Stoppt die Combo 'Turbo_Jump', wenn die Bedingung nicht mehr erfüllt ist
        combo_stop(Turbo_Jump);
    }
}

// Definition der Combo namens 'Turbo_Jump'
combo Turbo_Jump {
    // Setzt den Wert der "Jump"-Taste auf 100 (vollständiges Drücken der Taste)
    set_val(Jump, 100);
    
    // Wartet für 40 Millisekunden
    wait(40);
    
    // Setzt den Wert der "Jump"-Taste auf 0 (loslassen der Taste)
    set_val(Jump, 0);
    
    // Wartet für (40 Millisekunden minus die vergangene Zeit seit dem letzten Reset)
    wait(40 - get_rtime());
    
    // Setzt den Wert der "Jump"-Taste erneut auf 0, um sicherzustellen, dass die Taste losgelassen bleibt
    set_val(Jump, 0);
}