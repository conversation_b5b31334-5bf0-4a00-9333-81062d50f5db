#pragma METAINFO("stick_movement", 1, 5, "AssistantAI")

// Constants
define STICK_PRESS_LB_THRESHOLD = 95;
define OVERRIDE_DURATION = 18; // Duration in frames (approximately 300ms at 60fps)

// Variables
int rightStickAngle;
int rightStickMagnitude;
int leftStickAngle;
int leftStickMagnitude;
int angleDifference;
int newLeftStickAngle;
int overrideCounter;
int isOverriding;

main {
    // Get stick information
    rightStickAngle = get_polar(POLAR_RS, POLAR_ANGLE);
    rightStickMagnitude = get_polar(POLAR_RS, POLAR_RADIUS);
    leftStickAngle = get_polar(POLAR_LS, POLAR_ANGLE);
    leftStickMagnitude = get_polar(POLAR_LS, POLAR_RADIUS);

    // Check if right stick is moved and we're not already overriding
    if (rightStickMagnitude > 1500 && !isOverriding) {
        // Calculate angle difference between right and left sticks
        angleDifference = (rightStickAngle - leftStickAngle + 360) % 360;

        // Determine whether to add or subtract 45 degrees
        if (angleDifference > 180) {
            // Right stick is counterclockwise from left stick, rotate left stick clockwise
            newLeftStickAngle = (leftStickAngle + 45) % 360;
        } else {
            // Right stick is clockwise from left stick, rotate left stick counterclockwise
            newLeftStickAngle = (leftStickAngle - 45 + 360) % 360;
        }
        
        isOverriding = TRUE;
        overrideCounter = OVERRIDE_DURATION;
    }

    // Apply override if active
    if (isOverriding) {
        set_polar(POLAR_LS, newLeftStickAngle, leftStickMagnitude);
        overrideCounter--;

        // Check if override duration has elapsed
        if (overrideCounter <= 0) {
            isOverriding = FALSE;
        }
    }

    // Handle LB press when right stick reaches threshold
    if (rightStickMagnitude >= STICK_PRESS_LB_THRESHOLD && 
        get_polar(POLAR_RS, POLAR_RADIUS) < STICK_PRESS_LB_THRESHOLD) {
        combo_run(Press_LB);
    }
}

combo Press_LB {
    set_val(XB1_LB, 100);
    wait(100);
    wait(20);
}