define Stick_Press_LB_Threshold = 95;
define TIMEOUT = 300;
int stick_swap_toggle;
int rightStickMagnitude; // Declare rightStickMagnitude at the top
int rs_active_timer = 0;  // Timer to track how long right stick has been active

define MAX_RADIUS_LS = 32767;
define REDUCED_RADIUS_LS = 30000;
int radius_LS;
int max_allowed_radius_LS;



main {
technicaldribbler()

 run_LS()
}

function technicaldribbler() {
    rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
    
    if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
        // Copy right stick to left stick immediately
        set_val(POLAR_LX, get_val(POLAR_RX));
        set_val(POLAR_LY, get_val(POLAR_RY));
        
        // Increment timer while stick is active
        rs_active_timer += get_rtime();
        
        // Zero out right stick after 200ms
        if(rs_active_timer >= 200) {
            set_val(XB1_RX, 0);
            set_val(XB1_RY, 0);
        }
    } else {
        // Reset timer when stick is below threshold
        rs_active_timer = 0;
    }
    
    if(rightStickMagnitude >= Stick_Press_LB_Threshold && 
       isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
        combo_run(Press_LB);
}

combo Press_LB {
            set_val(POLAR_LX, get_val(POLAR_RX));
            set_val(POLAR_LY, get_val(POLAR_RY));
    set_val(XB1_LB, 100);
    wait(100);
    wait(20);
}


function run_LS() {
    // Store the radius value to avoid repeated function calls
    radius_LS = get_polar(POLAR_LS, POLAR_RADIUS);
    
    // Check if either LT or RT is pressed
    if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
        max_allowed_radius_LS = MAX_RADIUS_LS;
    } else {
        max_allowed_radius_LS = REDUCED_RADIUS_LS;
    }
    
    set_polar(POLAR_LS, 
        quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_LS),
        min(calculate_radius_LS(), max_allowed_radius_LS) // Ensure radius is within allowed limit
    );
}

define AngleInterval_LS = 12;    // Changed from 12 to 30 for 16 zones
// This defines the number of zones.  A value of 16 creates 16 zones.


function calculate_radius_LS() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)); // Calculate radius using Pythagorean theorem
}

function quantize_angle(angle, interval) {
    // This function quantizes the angle into discrete zones.
    return (((inv(angle) * interval) / 360) * 360) / interval;
}
 ����������������
