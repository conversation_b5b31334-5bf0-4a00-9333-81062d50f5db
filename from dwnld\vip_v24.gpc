																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
				
				
				
		*/
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		int Vx202[0]; init { 	Vx119(); 	combo_run(Vx1); 	combo_run(Vx2); 	combo_run(Vx3); 	combo_run(Vx4); 	combo_run(Vx5); 	combo_run(Vx6); 	combo_run(Vx7); 	combo_run(Vx8); 	combo_run(Vx9); 	combo_run(Vx10); 	combo_run(Vx11); 	combo_run(Vx12); 	combo_run(Vx13); 	combo_run(Vx14); 	combo_run(Vx15); 	combo_run(Vx16); 	combo_run(Vx17); 	combo_run(Vx18); 	combo_run(Vx19); 	combo_run(Vx20); 	combo_run(Vx21); 	combo_run(Vx22); 	combo_run(Vx23); 	combo_run(Vx24); 	combo_run(Vx25); 	combo_run(Vx26); 	combo_run(Vx27); 	combo_run(Vx28); 	combo_run(Vx29); 	combo_run(Vx30); 	combo_run(Vx31); 	combo_run(Vx32); 	combo_run(Vx33); 	combo_run(Vx34); 	combo_run(Vx35); 	combo_run(Vx36); 	combo_run(Vx37); 	combo_run(Vx38); 	combo_run(Vx39); 	combo_run(Vx40); 	combo_run(Vx41); 	combo_run(Vx42); 	combo_run(Vx43); 	combo_run(Vx44); 	combo_run(Vx45); 	combo_run(Vx46); 	combo_run(Vx47); 	combo_run(Vx48); 	combo_run(Vx49); 	combo_run(Vx50); 	combo_run(Vx51); 	combo_run(Vx52); 	combo_run(Vx53); 	combo_run(Vx54); 	combo_run(Vx55); 	combo_run(Vx56); 	combo_run(Vx57); 	combo_run(Vx58); 	combo_run(Vx59); 	combo_run(Vx60); 	combo_run(Vx61); 	combo_run(Vx62); 	combo_run(Vx63); 	combo_run(Vx64); 	combo_run(Vx65); 	combo_run(Vx66); 	combo_run(Vx67); 	combo_run(Vx68); 	combo_run(Vx69); 	combo_run(Vx70); 	combo_stop(Vx1); 	combo_stop(Vx2); 	combo_stop(Vx3); 	combo_stop(Vx4); 	combo_stop(Vx5); 	combo_stop(Vx6); 	combo_stop(Vx7); 	combo_stop(Vx8); 	combo_stop(Vx9); 	combo_stop(Vx10); 	combo_stop(Vx11); 	combo_stop(Vx12); 	combo_stop(Vx13); 	combo_stop(Vx14); 	combo_stop(Vx15); 	combo_stop(Vx16); 	combo_stop(Vx17); 	combo_stop(Vx18); 	combo_stop(Vx19); 	combo_stop(Vx20); 	combo_stop(Vx21); 	combo_stop(Vx22); 	combo_stop(Vx23); 	combo_stop(Vx24); 	combo_stop(Vx25); 	combo_stop(Vx26); 	combo_stop(Vx27); 	combo_stop(Vx28); 	combo_stop(Vx29); 	combo_stop(Vx30); 	combo_stop(Vx31); 	combo_stop(Vx32); 	combo_stop(Vx33); 	combo_stop(Vx34); 	combo_stop(Vx35); 	combo_stop(Vx36); 	combo_stop(Vx37); 	combo_stop(Vx38); 	combo_stop(Vx39); 	combo_stop(Vx40); 	combo_stop(Vx41); 	combo_stop(Vx42); 	combo_stop(Vx43); 	combo_stop(Vx44); 	combo_stop(Vx45); 	combo_stop(Vx46); 	combo_stop(Vx47); 	combo_stop(Vx48); 	combo_stop(Vx49); 	combo_stop(Vx50); 	combo_stop(Vx51); 	combo_stop(Vx52); 	combo_stop(Vx53); 	combo_stop(Vx54); 	combo_stop(Vx55); 	combo_stop(Vx56); 	combo_stop(Vx57); 	combo_stop(Vx58); 	combo_stop(Vx59); 	combo_stop(Vx60); 	combo_stop(Vx61); 	combo_stop(Vx62); 	combo_stop(Vx63); 	combo_stop(Vx64); 	combo_stop(Vx65); 	combo_stop(Vx66); 	combo_stop(Vx67); 	combo_stop(Vx68); 	combo_stop(Vx69); 	combo_stop(Vx70); 	combo_run(Vx110); } int Vx275 ; int Vx276; int Vx277; int Vx278; int Vx279; define Vx280 = 0; define Vx281 = 1; define Vx282 = 2; define Vx283 = 3; define Vx284 = 4; define Vx285 = 5; define Vx286 = 6; define Vx287 = 7; define Vx288 = 8; define Vx289 = 9; define Vx290 = 10; define Vx291 = 11; define Vx292 = 12; define Vx293 = 13; define Vx294 = 14; define Vx295 = 15; define Vx296 = 16; define Vx297 = 17; define Vx298 = 18; define Vx299 = 19; define Vx300 = 20; define Vx301 = 21; define Vx302 = 22; define Vx23 = 23; define Vx304 = 24; define Vx305 = 25; define Vx306 = 26; define Vx307 = 27; define Vx308 = 28; define Vx309 = 29; define Vx310 = 30; define Vx311 = 31; define Vx312 = 32; define Vx313 = 33; define Vx314 = 34; define Vx315 = 35; define Vx316 = 36; define Vx317 = 37; define Vx318 = 38; define Vx319 = 39; define Vx320 = 40; define Vx321 = 41; define Vx322 = 42; define Vx323 = 43; define Vx324 = 44; define Vx325 = 45; define Vx326 = 46; define Vx327 = 47; define Vx328 = 48; define Vx329 = 49; define Vx330 = 50; define Vx331 = 51; define Vx332 = 52; define Vx333 = 53; define Vx334 = 54; define Vx335 = 55; define Vx336 = 56; define Vx337 = 57; define Vx338 = 58; define Vx339 = 59; define Vx340 = 60; define Vx341 = 61; define Vx342 = 62; define Vx343 = 63; define Vx344 = 64; define Vx345 = 65; define Vx346 = 66; define Vx347 = 67; define Vx348 = 68; define Vx349 = 69; define Vx350 = 70; define Vx351 = 0; function Vx113(Vx114) { 	if (Vx114 == 0) vm_tctrl(-0); 	else if (Vx114 == 1) vm_tctrl(-1); 	else if (Vx114 == 2) vm_tctrl(-2); 	else if (Vx114 == 3) vm_tctrl(-3); 	else if (Vx114 == 4) vm_tctrl(-4); 	else if (Vx114 == 5) vm_tctrl(-5); 	else if (Vx114 == 6) vm_tctrl(-6); 	else if (Vx114 == 7) vm_tctrl(-7); 	else if (Vx114 == 8) vm_tctrl(-8); 	else if (Vx114 == 9) vm_tctrl(-9); } int Vx352, Vx353; int Vx354, Vx355; int Vx356 = FALSE, Vx357; int Vx358 = TRUE; int Vx359; const string Vx832[] = { 	"Off",  "On" } ; int Vx360; int Vx361; int Vx362; int Vx363; int Vx364; int Vx365; int Vx366; int Vx367; int Vx368; int Vx369; int Vx370; int Vx371; int Vx372; int Vx373; int Vx374; int Vx375; int Vx376; int Vx377; int Vx378; int Vx379; int Vx114; int Vx381; int Vx382 ; int Vx383 ; int Vx384 ; define Vx385 = 24; int Vx386; int Vx387; int Vx388; int Vx389; int Vx390; int Vx391; int Vx392; int Vx393; int Vx394; int Vx395 ; int Vx396 ; int Vx397 ; int Vx398 ; int Vx399 ; int Vx400 ; int Vx401 ; int Vx402 ; int Vx403 ; int Vx404 ; int Vx405 ; int Vx406; int Vx407; int Vx408; int Vx409; int Vx410; int Vx411; int Vx412; int Vx413; int Vx414; int Vx415; int Vx416; int Vx417; int Vx418; int Vx419; int Vx420; int Vx421; int Vx422; int Vx423; int Vx424; int Vx425; int Vx426; int Vx427; int Vx428; int Vx429; int Vx430; int Vx431; int Vx432; int Vx433; int Vx434; int Vx435; int Vx436; int Vx437; int Vx438; int Vx439 ; int Vx440 ; int Vx441 ; int Vx442; int Vx443 ; int Vx444 ; int Vx445 ; int Vx446; int Vx447 ; int Vx448 ; int Vx449 ; int Vx450; int Vx451 ; int Vx452 ; int Vx453 ; int Vx454; int Vx455; int Vx456; int Vx457; int Vx458; int Vx459; const int16 Vx838[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		13,30,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 Vx579[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 Vx810[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string Vx589[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string Vx588[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string Vx814 [] = { 	"Classic","Alternative","Custom", ""  } ; const string Vx913 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string Vx830[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string Vx816[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string Vx883[] = { 	"Right",  "Left",  "" } ; const string Vx881[] = { 	"One Tap",  "Double Tap",  "" } ; const string Vx820[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string Vx822[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string Vx857[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int Vx460 = -1; int Vx461 = -1; int Vx462 = -1; int Vx463 = -1; int Vx464 = -1; int Vx465; int Vx466; int Vx467; int Vx468; int Vx469; const uint8 Vx1366[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 Vx1367[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 Vx1368[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 Vx1369[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 Vx1370[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 Vx1371[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 Vx1372[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 Vx1373[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 Vx1374[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 Vx1375[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 Vx1376[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 Vx1377[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 Vx1378[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 Vx1379[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 Vx1380[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 Vx1381[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function Vx115(Vx116) { 	if (Vx116 == 9) { 		Vx470 = -1; 			} 	else if (Vx116 <= 0) { 		Vx470 = 1; 			} 	else if (Vx116 > 9 ) { 		Vx116 = 0; 			} 	Vx116 += Vx470; 	return Vx116; 	} function Vx117() { 	vm_tctrl(0); 	if(Vx29 && Vx363){ 		if(Vx555 < 1000){ 			Vx473 = 10; 			Vx499   = 10; 			Vx497  = 10; 					} 			} 	if(Vx476 && Vx364){ 		Vx474 = FALSE; 		if(Vx555 < 1000){ 			Vx473 = 11; 			Vx499   = 11; 			Vx497  = 11; 					} 			} 	if(Vx474 && Vx364){ 		Vx476 = FALSE; 		if(Vx555 < 1000){ 			Vx473 = 10; 			Vx499   = 10; 			Vx497  = 10; 					} 			} 			       if(Vx555 >= 1000){     Vx478 = Vx115(Vx478);     Vx496 = Vx115(Vx496);     Vx497 = Vx115(Vx497);     Vx473 = Vx115(Vx473);     Vx499 = Vx115(Vx499);     } 	if(Vx363){ 		if(Vx502 == Vx593){ 			Vx479 = !Vx479; 			if(Vx1366[Vx478]) Vx227(Vx1366[Vx478]); 					} 		if(Vx502 == Vx232 (Vx593 + 4)){ 			Vx479 = FALSE; 			if(Vx1373[Vx496]) Vx227(Vx1373[Vx496]); 					} 		if(Vx502 == Vx232 (Vx593 + 1) ){ 			Vx479 = TRUE; 			if(Vx1368[Vx473]) Vx227(Vx1368[Vx473]); 					} 		if(Vx502 == Vx232 (Vx593 - 1) ){ 			Vx479 = FALSE; 			if(Vx1367[Vx473]) Vx227(Vx1367[Vx473]); 					} 		if(Vx502 == Vx232 (Vx593 + 2) ){ 			Vx479 = TRUE; 			if(Vx1370[Vx499]) Vx227(Vx1370[Vx499]); 					} 		if(Vx502 == Vx232 (Vx593 - 2) ){ 			Vx479 = FALSE; 			if(Vx1369[Vx499]) Vx227(Vx1369[Vx499]); 					} 		if(Vx502 == Vx232 (Vx593 + 3) ){ 			Vx479 = TRUE; 			if(Vx1371[Vx497]) Vx227(Vx1371[Vx497]); 					} 		if(Vx502 == Vx232 (Vx593 - 3) ){ 			Vx479 = FALSE; 			if(Vx1372[Vx497]) Vx227(Vx1372[Vx497]); 					} 			} 	else if(Vx364){ 		if(Vx502 == Vx593){ 			Vx479 = !Vx479; 			if(Vx1374[Vx478]) Vx227(Vx1374[Vx478]); 					} 		if(Vx502 == Vx232 (Vx593 + 4)){ 			Vx479 = FALSE; 			if(Vx1381[Vx496]) Vx227(Vx1381[Vx496]); 					} 		if(Vx502 == Vx232 (Vx593 + 1) ){ 			Vx479 = TRUE; 			if(Vx1376[Vx473]) Vx227(Vx1376[Vx473]); 					} 		if(Vx502 == Vx232 (Vx593 - 1) ){ 			Vx479 = FALSE; 			if(Vx1375[Vx473]) Vx227(Vx1375[Vx473]); 					} 		if(Vx502 == Vx232 (Vx593 + 2) ){ 			Vx479 = TRUE; 			if(Vx1378[Vx499]) Vx227(Vx1378[Vx499]); 					} 		if(Vx502 == Vx232 (Vx593 - 2) ){ 			Vx479 = FALSE; 			if(Vx1377[Vx499]) Vx227(Vx1377[Vx499]); 					} 		if(Vx502 == Vx232 (Vx593 + 3) ){ 			Vx479 = TRUE; 			if(Vx1379[Vx497]) Vx227(Vx1379[Vx497]); 					} 		if(Vx502 == Vx232 (Vx593 - 3) ){ 			Vx479 = FALSE; 			if(Vx1380[Vx497]) Vx227(Vx1380[Vx497]); 					} 			} } int Vx478; int Vx496; int Vx497; int Vx473; int Vx499; function Vx118() { 	if(Vx1139){ 		Vx500 += get_rtime(); 			} 	if(Vx500 >= 3000){ 		Vx500 = 0; 		Vx1139 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(Vx451) && !get_ival(Vx452) && !get_ival(Vx450) && !get_ival(Vx449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !Vx503 && !combo_running(Vx0)) { 			Vx502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			Vx503 = TRUE; 			Vx1139 = TRUE; 			Vx500 = 0; 			vm_tctrl(0); 			Vx117(); 					} 		set_val(Vx1120, 0); 		set_val(Vx1121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		Vx503 = FALSE; 			} 	} function Vx119() { 	Vx172(); 	if (Vx386 == 0 && Vx387 == 0 && Vx388 == 0 && Vx389 == 0 && Vx390 == 0 && Vx391 == 0 && Vx392 == 0 && Vx393 == 0) { 		Vx386 = 4; 		Vx387 = 41; 		Vx388 = 41; 		Vx389 = 42; 		Vx390 = 42; 		Vx391 = 31; 		Vx392 = 31; 		Vx393 = 31; 			} 	Vx952 = get_slot(); 	} int Vx470 = 1; int Vx507; int Vx508; int Vx509 = TRUE; int Vx510[6]; int Vx511; int Vx512; int Vx513; int Vx514; function Vx120(Vx121, Vx122, Vx123) { 	Vx123 = (Vx123 * 14142) / 46340; 	if (Vx122 <= 0) { 		set_polar2(Vx121, (Vx122 = (abs(Vx122) + 360) % 360), min(Vx123, Vx518[Vx122 % 90])); 		return; 			} 	set_polar2(Vx121, inv(Vx122 % 360), min(Vx123, Vx518[Vx122 % 90])); 	} function Vx124(Vx121,Vx126) { 	if (Vx126) return (get_ipolar(Vx121, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + Vx121), 2) + pow(get_ival(43 + Vx121), 2))) + 1; 	} const int16 Vx518[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int Vx521 = 1; combo Vx0{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && Vx124(POLAR_RS,POLAR_RADIUS) > 2000){ 		Vx269(POLAR_RS, 2500, 12000); 		}  Vx113(Vx114); 	if(!Vx508){ 		Vx508 = TRUE; 		Vx507 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,Vx508); 		set_pvar(SPVAR_3,Vx507); 		Vx509 = TRUE; 			} 	if(!Vx514){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			Vx513 = Vx129(Vx513 + 1 ,0,5)Vx509 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			Vx513 = Vx129(Vx513 - 1 ,0,5)Vx509 = TRUE 		} 		if(event_press(PS4_UP)){ 			Vx510[Vx513]  = Vx129(Vx510[Vx513] + 1 ,0,9)Vx509 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			Vx510[Vx513]  = Vx129(Vx510[Vx513] - 1 ,0,9)Vx509 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			Vx511 = 0; 			for(Vx512 = 5; 			Vx512 >= 0; 			Vx512--){ 				Vx511 += Vx510[Vx512] * pow(10,Vx512) 			} 			if(Vx511 == Vx127(Vx507)){ 				Vx514 = TRUE; 				set_pvar(SPVAR_2,Vx514)  			} 			Vx509 = TRUE; 					} 			} 	if(Vx509){ 		cls_oled(0)if(!Vx514){ 			Vx133(Vx507,Vx536,10,OLED_FONT_MEDIUM,OLED_WHITE,Vx537)for( Vx512 = 0; 			Vx512 < 6; 			Vx512++){ 				Vx133(Vx510[Vx512],85 - (Vx512 * 10),40,OLED_FONT_MEDIUM,!(Vx512 == Vx513),Vx537) 			} 					} 		Vx509 = FALSE; 			} 	if(Vx514){ 		if (get_ival(Vx447) || get_ival(Vx451) || get_ival(Vx449) || get_ival(Vx450) || Vx356 || combo_running(Vx72) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			Vx113(Vx114); 					} 		if(get_ival(Vx452) > 40 || (!get_ival(Vx449) && !get_ival(Vx450))){ 			if(get_ival(Vx447)){ 				vm_tctrl(0); 				if(get_ptime(Vx447) > Vx608){ 					set_val(Vx447,0); 									} 							} 					} 		if(!get_ival(Vx449)){ 			if(get_ival(Vx447)){ 				vm_tctrl(0); 				if(get_ptime(Vx447) > Vx608){ 					set_val(Vx447,0); 									} 							} 					} 		if (Vx356) { 			vm_tctrl(0); 			if(Vx357 < 8050){ 				Vx357 += get_rtime(); 							} 			if (Vx357 >= 8000) { 				cls_oled(OLED_BLACK); 				Vx357 = 0; 				Vx356 = FALSE; 							} 					} 		if (block) { 		if (Vx114 > 7)combo_run(Vx112); 			if (Vx521 < 310) { 				Vx521 += get_rtime(); 							} 			if (Vx521 <= 300 ) { 				Vx169(); 							} 			if (Vx521 > 300 ) { 				block = FALSE; 				Vx521 = 1; 				Vx717 = TRUE; 							} 			if (Vx521 < 0) { 				Vx521 = 1; 							} 			if (Vx521 <= 100) { 				combo_stop(Vx88); 				combo_stop(Vx97); 				combo_stop(Vx89); 				combo_stop(Vx98); 				combo_stop(Vx95); 				combo_stop(Vx96); 				combo_stop(Vx92); 				combo_stop(Vx94); 				combo_stop(Vx91); 				combo_stop(Vx87); 				combo_stop(Vx85); 				combo_stop(Vx90); 				combo_stop(Vx107); 				combo_stop(Vx109); 				combo_stop(Vx100); 				combo_stop(Vx108); 				combo_stop(Vx99); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(Vx437){ 			Vx438 = FALSE; 					} 		if(Vx438){ 			Vx437 = FALSE; 					} 		if(Vx361){ 			Vx362 = FALSE; 			Vx363 = FALSE; 			Vx364 = FALSE; 					} 		if(Vx362){ 			Vx361 = FALSE; 			Vx363 = FALSE; 			Vx364 = FALSE; 					} 		if(Vx363){ 			Vx361 = FALSE; 			Vx362 = FALSE; 			Vx364 = FALSE; 					} 		if(Vx364){ 			Vx361 = FALSE; 			Vx362 = FALSE; 			Vx363 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				Vx1174 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				Vx1174 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				Vx1062 = !Vx1062; 				Vx229(Vx1294); 				Vx203(Vx1062, sizeof(Vx552) - 1, Vx552[0]); 				Vx356 = TRUE; 							} 					} 		if (Vx1062) { 			if(Vx381){ 				Vx266(); 			} 			if (Vx379) { 				Vx256(); 							} 			if (event_release(Vx452)) { 				Vx555 = 1; 							} 			if (Vx555 < 8000) { 				Vx555 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					Vx557 = !Vx557; 					Vx229(Vx557); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (Vx557) { 				if (Vx557) Vx222(Vx1094); 				if (Vx557) { 					Vx148(); 									} 							} 			else if (!get_ival(Vx452)) { 				Vx222(Vx1097); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						Vx352 = TRUE; 						Vx359 = TRUE; 						Vx358 = FALSE; 						if (!Vx352) { 							Vx358 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!Vx358) { 					if (Vx352 || Vx353) { 						vm_tctrl(0); 					} 					if (Vx352) { 						combo_stop(Vx72); 						vm_tctrl(0); 						Vx360= Vx149(Vx360,0  ); 						Vx361 = Vx149(Vx361, 1); 						Vx362  = Vx149(Vx362   ,2  ); 						Vx363  = Vx149(Vx363 , 3); 						Vx364  = Vx149(Vx364 , 4); 						Vx365 = Vx149(Vx365, 5); 						Vx366 = Vx149(Vx366, 6); 						Vx367 = Vx149(Vx367, 7); 						Vx368 = Vx149(Vx368, 8); 						Vx369 = Vx149(Vx369, 9); 						Vx370 = Vx149(Vx370, 10); 						Vx371 = Vx149(Vx371, 11); 						Vx372 = Vx149(Vx372, 12); 						Vx373 = Vx149(Vx373,13); 						Vx374 = Vx149(Vx374, 14); 						Vx375 = Vx149(Vx375, 15); 						Vx376 = Vx149(Vx376, 16); 						Vx377 = Vx149(Vx377, 17); 						Vx378 = Vx149(Vx378, 18); 						Vx379 = Vx149(Vx379, 19); 						Vx114 = Vx149(Vx114, 20); 						Vx381 = Vx149(Vx381, 21); 						Vx382              = Vx149(Vx382              ,22  ); 						Vx383              = Vx149(Vx383              ,23  ); 						Vx384               = Vx149(Vx384               ,24  ); 						if (event_press(PS4_DOWN)) { 							Vx354 = clamp(Vx354 + 1, 0, Vx385); 							Vx359 = TRUE; 													} 						if (event_press(PS4_UP)) { 							Vx354 = clamp(Vx354 - 1, 0, Vx385); 							Vx359 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							Vx352 = FALSE; 							Vx358 = FALSE; 							Vx359 = FALSE; 							vm_tctrl(0); 							combo_run(Vx75); 													} 						if (Vx579[Vx354][2] == 1) { 							if(Vx354 == 0 ){ 								if(Vx360 == 2 ){ 									if (event_press(PS4_CROSS)) { 										Vx355 = Vx579[Vx354][0]; 										Vx352 = FALSE; 										Vx353 = TRUE; 										Vx359 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									Vx355 = Vx579[Vx354][0]; 									Vx352 = FALSE; 									Vx353 = TRUE; 									Vx359 = TRUE; 																	} 															} 													} 						Vx169(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, Vx569[0]); 						Vx158(Vx354 + 1, Vx164(Vx354 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, Vx571[0]); 						Vx158(Vx952, Vx164(Vx952), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(Vx354 == 0 ){ 							if(Vx360 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, Vx573[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, Vx574[0]); 															} 													} 						else{ 							if (Vx579[Vx354][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, Vx574[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, Vx573[0]); 															} 													} 											} 					if (Vx353) { 						Vx439               = Vx152(Vx439, 0); 						Vx440               = Vx152(Vx440, 1); 						Vx441             = Vx152(Vx441, 2); 						Vx442           = Vx152(Vx442, 3); 						Vx443             = Vx152(Vx443, 4); 						Vx444             = Vx152(Vx444, 5); 						Vx445              = Vx152(Vx445, 6); 						Vx446           = Vx152(Vx446, 7); 						Vx386          = Vx152(Vx386, 8); 						Vx387   = Vx152(Vx387, 9); 						Vx388 = Vx152(Vx388, 10); 						Vx389      = Vx152(Vx389, 11); 						Vx390    = Vx152(Vx390, 12); 						Vx391    = Vx152(Vx391, 13); 						Vx392    = Vx152(Vx392, 14); 						Vx393      = Vx152(Vx393, 15); 						Vx394      = Vx152(Vx394, 16); 						Vx275              = Vx152(Vx275, 17); 						Vx276           = Vx152(Vx276, 18); 						Vx277            = Vx152(Vx277, 19); 						Vx278            = Vx152(Vx278, 20); 						Vx279= Vx152(Vx279, 21); 						Vx407               = Vx152(Vx407, 22); 						Vx408               = Vx152(Vx408, 23); 						Vx409                   = Vx152(Vx409, 24); 						Vx410                   = Vx152(Vx410, 25); 						Vx411                   = Vx152(Vx411, 26); 						Vx412   = Vx152(Vx412, 27); 						Vx413   = Vx152(Vx413, 28); 						Vx414 = Vx152(Vx414, 29); 						Vx415   = Vx152(Vx415, 30); 						Vx416   = Vx152(Vx416, 31); 						Vx417 = Vx152(Vx417, 32); 						Vx418   = Vx152(Vx418, 33); 						Vx419   = Vx152(Vx419, 34); 						Vx420 = Vx152(Vx420, 35); 						Vx421   = Vx152(Vx421, 36); 						Vx422   = Vx152(Vx422, 37); 						Vx423 = Vx152(Vx423, 38); 						Vx424   = Vx155(Vx424, 39); 						Vx425         = Vx155(Vx425, 40); 						Vx426   = Vx152(Vx426, 41); 						Vx427     = Vx152(Vx427, 42); 						Vx428                   = Vx155(Vx428, 43); 						Vx1250 = Vx152(Vx1250, 54); 						Vx1243 = Vx152(Vx1243, 55); 						Vx429               = Vx155(Vx429, 44); 						Vx430 = Vx155(Vx430, 45); 						Vx431     = Vx152(Vx431, 46); 						Vx432               = Vx155(Vx432, 47); 						Vx433 = Vx152(Vx433, 48); 						Vx434 = Vx152(Vx434, 49); 						Vx435 = Vx152(Vx435, 50); 						Vx436 = Vx152(Vx436, 51); 						Vx437               = Vx152(Vx437, 52); 						Vx438                 = Vx152(Vx438, 53); 						Vx395       = Vx155(Vx395     ,56 ); 						Vx396       = Vx155(Vx396     ,57 ); 						Vx397      = Vx152(Vx397    ,58 ); 						Vx398   = Vx152(Vx398 ,59 ); 						Vx399       = Vx155(Vx399     ,60 ); 						Vx400       = Vx155(Vx400     ,61 ); 						Vx401   = Vx152(Vx401 ,62 ); 						Vx402      = Vx152(Vx402    ,63 ); 						Vx403          = Vx155(Vx403        ,64 ); 						Vx404          = Vx155(Vx404        ,65 ); 						Vx405         = Vx152(Vx405       ,66 ); 						Vx455             = Vx155(Vx455           ,67 ); 						Vx29             = Vx152(Vx29           ,68); 						Vx476           = Vx152(Vx476         ,69); 						Vx474         = Vx152(Vx474       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								Vx355 = clamp(Vx355 + 1, Vx579[Vx354][0], Vx579[Vx354][1]); 								Vx359 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								Vx355 = clamp(Vx355 - 1, Vx579[Vx354][0], Vx579[Vx354][1]); 								Vx359 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							Vx352 = TRUE; 							Vx353 = FALSE; 							Vx359 = TRUE; 													} 						Vx169(); 						Vx954 = Vx838[Vx355][0]; 						Vx955 = Vx838[Vx355][1]; 						if (Vx838[Vx355][4] == 0) { 							Vx158(Vx954, Vx164(Vx954), 4, 20, OLED_FONT_SMALL); 							Vx158(Vx955, Vx164(Vx955), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (Vx359) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (Vx353) { 							print(Vx214(Vx167(Vx588[Vx355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, Vx588[Vx355]); 													} 						else { 							print(Vx214(Vx167(Vx589[Vx354]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, Vx589[Vx354]); 													} 						Vx359 = FALSE; 					} 									} 				if (!Vx352 && !Vx353) { 					if (Vx358) { 						cls_oled(0); 						combo_run(Vx72); 						Vx358 = FALSE; 						Vx356 = TRUE; 						vm_tctrl(0); 					} 					if(Vx360 == 0){ 						Vx447      = PS4_CIRCLE; 						Vx448      = PS4_CROSS ; 						Vx449    = PS4_L1    ; 						Vx450  = PS4_R1; 						Vx451    = PS4_L2; 						Vx452    = PS4_R2; 						Vx453     = PS4_SQUARE; 						Vx454  = PS4_TRIANGLE; 					} 					else if(Vx360 == 1){ 						Vx447      = PS4_SQUARE; 						Vx448      = PS4_CROSS ; 						Vx449    = PS4_L1    ; 						Vx450  = PS4_R1; 						Vx451    = PS4_L2; 						Vx452    = PS4_R2; 						Vx453     = PS4_CIRCLE; 						Vx454  = PS4_TRIANGLE; 					} 					else if(Vx360 == 2){ 						Vx447      = Vx1395[Vx439]; 						Vx448      = Vx1395[Vx440] ; 						Vx449    = Vx1395[Vx441]  ; 						Vx450  = Vx1395[Vx442]; 						Vx451    = Vx1395[Vx443]; 						Vx452    = Vx1395[Vx444]; 						Vx453     = Vx1395[Vx445]; 						Vx454  = Vx1395[Vx446]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !Vx1197) { 						set_val(Vx448, 0); 						vm_tctrl(0); 						combo_run(Vx77); 											} 					if (Vx717) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							Vx593 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							Vx1052 = Vx1406[Vx593][0]; 							Vx669 = Vx1406[Vx593][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							Vx428 += 5; 							Vx210(Vx214(sizeof(Vx595) - 1, OLED_FONT_MEDIUM_WIDTH), Vx595[0], Vx428); 													} 						if (event_press(PS4_LEFT)) { 							Vx428 -= 5; 							Vx210(Vx214(sizeof(Vx595) - 1, OLED_FONT_MEDIUM_WIDTH), Vx595[0], Vx428); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !Vx615 ) { 						if (event_press(PS4_UP)) { 							Vx600 += 25; 							Vx210(Vx214(sizeof(Vx601) - 1, OLED_FONT_MEDIUM_WIDTH), Vx601[0], Vx600); 													} 						if (event_press(PS4_DOWN)) { 							Vx600 -= 25; 							Vx210(Vx214(sizeof(Vx601) - 1, OLED_FONT_MEDIUM_WIDTH), Vx601[0], Vx600); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (Vx374) { 						Vx248(); 											} 					if (Vx375) { 						Vx249(); 						Vx250(); 											} 					if (!Vx375) { 						if (get_ival(Vx447)) { 							if (event_press(PS4_RIGHT)) { 								Vx608 += 2; 								Vx210(Vx214(sizeof(Vx609) - 1, OLED_FONT_MEDIUM_WIDTH), Vx609[0], Vx608); 															} 							if (event_press(PS4_LEFT)) { 								Vx608 -= 2; 								Vx210(Vx214(sizeof(Vx609) - 1, OLED_FONT_MEDIUM_WIDTH), Vx609[0], Vx608); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(Vx449) ){ 							if(get_ival(Vx447) && get_ptime(Vx447) > Vx608){ 								set_val(Vx447,0); 															} 													} 											} 					if(Vx378){ 						Vx253(); 											} 					if (Vx370) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								Vx615 = !Vx615; 								Vx229(Vx615); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (Vx615 && Vx370) { 						vm_tctrl(0); 						combo_stop(Vx85); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								Vx424 += 10; 								Vx210(Vx214(sizeof(Vx617) - 1, OLED_FONT_MEDIUM_WIDTH), Vx617[0], Vx424); 															} 							if (event_press(PS4_DOWN)) { 								Vx424 -= 10; 								Vx210(Vx214(sizeof(Vx617) - 1, OLED_FONT_MEDIUM_WIDTH), Vx617[0], Vx424); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						Vx222(Vx1096); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								Vx622 = FALSE; 								vm_tctrl(0); 								combo_run(Vx78); 															} 							if (event_press(PS4_LEFT)) { 								Vx622 = TRUE; 								vm_tctrl(0); 								combo_run(Vx78); 															} 							set_val(PS4_L1,0); 													} 											} 					if (Vx371) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								Vx624 = !Vx624; 								Vx229(Vx624); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (Vx624 && Vx371) { 						vm_tctrl(0); 						Vx222(Vx1098); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								Vx625 = FALSE; 								vm_tctrl(0); 								combo_run(Vx79); 															} 							if (event_press(PS4_RIGHT)) { 								Vx625 = TRUE; 								vm_tctrl(0); 								combo_run(Vx79); 															} 													} 											} 					if(Vx363 || Vx364 ){ 						Vx118(); 											} 					if (Vx361) { 						if (Vx361 == Vx1102) Vx628 = TRUE; 						if (Vx361 == Vx1103) { 							if (event_press(Vx1405[-1 +Vx394]) && get_brtime(Vx1405[-1 +Vx394]) <= 200) { 								Vx628 = !Vx628; 								Vx229(Vx628); 															} 							set_val(Vx1405[-1 +Vx394], 0); 													} 						if (Vx361 > 0 && Vx361 < 3 && Vx628 == 1) { 							Vx226(); 													} 						else if (Vx361 == 3) { 							if (get_ival(Vx1405[-1 +Vx394])) { 								Vx226(); 															} 							set_val(Vx1405[-1 +Vx394], 0); 													} 											} 									if (Vx362) { 						if (Vx362 == Vx1102) Vx631 = TRUE; 						if (Vx362 == Vx1103) { 							if (event_press(Vx1405[-1 +Vx279]) && get_brtime(Vx1405[-1 +Vx279]) <= 200) { 								Vx631 = !Vx631; 								Vx229(Vx631); 															} 							set_val(Vx1405[-1 +Vx279], 0); 													} 						if (Vx362 > 0 && Vx362 < 3 && Vx631 == 1) { 							Vx224(); 													} 						else if (Vx362 == 3) { 							if (get_ival(Vx1405[-1 +Vx279])) { 								Vx224(); 															} 							set_val(Vx1405[-1 +Vx279], 0); 													} 											} 					if (Vx365) { 						if (Vx365 == 1) { 							Vx634 = PS4_R3; 							Vx631 = FALSE; 													} 						if (Vx365 == 2) { 							Vx634 = PS4_L3; 							Vx631 = FALSE; 													} 						if (Vx365 == 3) { 							Vx634 = XB1_PR1; 							Vx631 = FALSE; 													} 						if (Vx365 == 4) { 							Vx634 = XB1_PR2; 							Vx631 = FALSE; 													} 						if (Vx365 == 5) { 							Vx634 = XB1_PL1; 							Vx631 = FALSE; 													} 						if (Vx365 == 6) { 							Vx634 = XB1_PL2; 							Vx631 = FALSE; 													} 						if(get_ival(Vx634)){ 							if(Vx407 || Vx408){ 								if( Vx407 && event_press(PS4_L1)){ 									Vx479 = FALSE; 									Vx1051 = Vx407  ; 									Vx227( Vx407   ); 								} 								if( Vx408 && event_press(PS4_R1)){ 									Vx479 = TRUE; 									Vx1051 =  Vx408 ; 									Vx227( Vx408   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( Vx409 ){ 								if(event_press(PS4_SQUARE)){ 									Vx479 = FALSE; 									Vx1051 =  Vx409  ; 													combo_stop(Vx88); 				combo_stop(Vx97); 				combo_stop(Vx89); 				combo_stop(Vx98); 				combo_stop(Vx95); 				combo_stop(Vx96); 				combo_stop(Vx92); 				combo_stop(Vx94); 				combo_stop(Vx91); 				combo_stop(Vx87); 				combo_stop(Vx85); 				combo_stop(Vx90); 				combo_stop(Vx107); 				combo_stop(Vx109); 				combo_stop(Vx100); 				combo_stop(Vx108); 				combo_stop(Vx99); 									Vx227( Vx409   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									Vx479 = TRUE; 									Vx1051 =  Vx409  ; 									Vx227( Vx409   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( Vx410 ){ 								if(event_press(PS4_CROSS)){ 									Vx479 = FALSE; 									Vx1051 =  Vx410  ; 									Vx227( Vx410   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(Vx88); 				combo_stop(Vx97); 				combo_stop(Vx89); 				combo_stop(Vx98); 				combo_stop(Vx95); 				combo_stop(Vx96); 				combo_stop(Vx92); 				combo_stop(Vx94); 				combo_stop(Vx91); 				combo_stop(Vx87); 				combo_stop(Vx85); 				combo_stop(Vx90); 				combo_stop(Vx107); 				combo_stop(Vx109); 				combo_stop(Vx100); 				combo_stop(Vx108); 				combo_stop(Vx99); 									Vx479 = TRUE; 									Vx1051 =  Vx410  ; 									Vx227( Vx410   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( Vx411 ){ 								if(event_press(PS4_R3)){ 									Vx479 = FALSE; 									Vx1051 =  Vx411  ; 									Vx227( Vx411   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(Vx634,0); 											} 					if (Vx366) { 						if (Vx413 == 1) { 							if (event_press(Vx1405[-1 + Vx412]) && !Vx1149) { 								vm_tctrl(0); 								combo_run(Vx82); 															} 							else if (event_press(Vx1405[-1 + Vx412]) && Vx1149) { 								set_val(Vx1405[-1 + Vx412], 0); 								Vx479 = !Vx414; 								Vx1051 = Vx366; 								Vx227(Vx366); 															} 													} 						else { 							if (event_press(Vx1405[-1 + Vx412])) { 								Vx479 = !Vx414; 								set_val(Vx1405[-1 + Vx412], 0); 								Vx1051 = Vx366; 								Vx227(Vx366); 															} 													} 					} 					if (Vx368) { 						if (Vx419 == 1) { 							if (event_press(Vx1405[-1 +Vx418]) && !Vx1149) { 								vm_tctrl(0); 								combo_run(Vx82); 															} 							else if (event_press(Vx1405[-1 +Vx418]) && Vx1149) { 								set_val(Vx1405[-1 +Vx418], 0); 								Vx479 = !Vx420; 								Vx1051 = Vx368; 								Vx227(Vx368); 															} 													} 						else { 							if (event_press(Vx1405[-1 +Vx418])) { 								Vx479 = !Vx420; 								set_val(Vx1405[-1 +Vx418], 0); 								Vx1051 = Vx368; 								Vx227(Vx368); 															} 													} 					} 					if (Vx367) { 						if (Vx416 == 1) { 							if (event_press(Vx1405[-1 +Vx415]) && !Vx1149) { 								vm_tctrl(0); 								combo_run(Vx82); 															} 							else if (event_press(Vx1405[-1 +Vx415]) && Vx1149) { 								set_val(Vx1405[-1 +Vx415], 0); 								Vx479 = !Vx417; 								Vx1051 = Vx367; 								Vx227(Vx367); 															} 													} 						else { 							if (event_press(Vx1405[-1 +Vx415])) { 								Vx479 = !Vx417; 								set_val(Vx1405[-1 +Vx415], 0); 								Vx1051 = Vx367; 								Vx227(Vx367); 															} 													} 					} 					if (Vx369) { 						if (Vx422 == 1) { 							if (event_press(Vx1405[-1 +Vx421]) && !Vx1149) { 								vm_tctrl(0); 								combo_run(Vx82); 															} 							else if (event_press(Vx1405[-1 +Vx421]) && Vx1149) { 								set_val(Vx1405[-1 +Vx421], 0); 								Vx479 = !Vx423; 								Vx1051 = Vx369; 								Vx227(Vx369); 															} 													} 						else { 							if (event_press(Vx1405[-1 +Vx421])) { 								Vx479 = !Vx423; 								set_val(Vx1405[-1 +Vx421], 0); 								Vx1051 = Vx369; 								Vx227(Vx369); 															} 													} 					} 					if (Vx1051 == Vx310 && combo_running(Vx30)) set_val(Vx449, 100); 					if(Vx383){ 						if(!block){ 							if(!get_val(Vx451)){ 								if( !get_val(Vx452)){ 									if(get_val(Vx448)){ 										Vx650 += get_rtime(); 																			} 									if(Vx402){ 										if(get_ival(Vx448) && get_ptime(Vx448) > Vx400){ 											set_val(Vx448,0); 																					} 																			} 									if(event_release(Vx448)){ 										if( Vx650 < Vx399 ){ 											Vx651 = Vx399 - Vx650; 											combo_run(Vx107); 																					} 										else{ 											if(Vx401) combo_run(Vx108); 																					} 										Vx650 = 0; 																			} 																	} 							} 						} 											} 					if(Vx382){ 						if(!block){ 							if(!get_ival(Vx451)){ 								if( !get_ival(Vx452)){ 									if(get_ival(Vx454)){ 										Vx652 += get_rtime(); 																			} 									if(event_release(Vx454)){ 										if(Vx652 < Vx395){ 											Vx653 = Vx395 - Vx652; 											combo_run(Vx109); 																					} 										else{ 											if(Vx398) combo_run(Vx99); 																					} 										Vx652 = 0; 																			} 																	} 							} 						} 											} 					if(Vx384){ 						if(!block){ 							if(get_ival(Vx453)){ 								Vx654 += get_rtime(); 															} 							if(Vx405){ 								if(get_ival(Vx453) && get_ptime(Vx453) > Vx404){ 									set_val(Vx453,0); 																	} 															} 							if(event_release(Vx453)){ 								if(Vx654 && (Vx654 < Vx403)){ 									Vx655 = Vx403 - Vx654; 									combo_run(Vx100); 																	} 								Vx654 = 0; 															} 													} 											} 					if (Vx372) { 						if (event_press(Vx1405[-1 +Vx426])) { 							vm_tctrl(0); 							combo_run(Vx77); 													} 						set_val(Vx1405[-1 +Vx426], 0); 											} 					if(!Vx376){ 						Vx429 = 0 ; 						Vx430 = 0; 						Vx431 = FALSE; 						Vx432 = 0; 											} 					if (Vx377) { 						Vx249(); 						if (Vx433 == 0) { 							Vx658 = FALSE; 							Vx457 = 0; 													} 						else { 							Vx658 = TRUE; 							Vx457 = 40; 													} 						if (Vx434 == 0) { 							Vx660 = FALSE; 							Vx456 = 0; 													} 						else { 							Vx660 = TRUE; 							Vx456 = 85; 													} 						if (Vx435 == 0) { 							Vx662 = FALSE; 							Vx458 = 0; 													} 						else { 							Vx662 = TRUE; 							Vx458 = -15; 													} 						if (Vx436 == 0) { 							Vx664 = FALSE; 													} 						else { 							Vx664 = TRUE; 													} 						if(Vx435 == 6 || Vx434 == 6 || Vx433 == 6){ 							if (get_ival(Vx1405[-1 + Vx435]) || get_ival(Vx1405[-1 + Vx434]) || get_ival(Vx1405[-1 + Vx433])){ 								combo_run(Vx0); 															} 													} 						if (Vx662) { 							if (get_val(Vx1405[-1 + Vx435])) { 								set_val(Vx1405[-1 + Vx435], 0); 								combo_run(Vx97); 								Vx1206 = 9000; 															} 													} 						if (Vx664) { 							if (get_val(Vx1405[-1 + Vx436])) { 								set_val(Vx1405[-1 + Vx436], 0); 								combo_run(Vx98); 								Vx1206 = 9000; 							} 							if (combo_running(Vx98)) { 								if (get_ival(Vx448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(Vx452) > 30) { 									combo_stop(Vx98); 																	} 															} 													} 						if (Vx660) { 							if (get_val(Vx1405[-1 + Vx434])) { 								set_val(Vx1405[-1 + Vx434], 0); 								Vx255(); 								Vx1206 = 9000; 															} 													} 						if (Vx658) { 							if (get_val(Vx1405[-1 + Vx433])) { 								set_val(Vx1405[-1 + Vx433], 0); 								combo_run(Vx95); 								Vx1206 = 9000; 															} 													} 											} 					else{ 						Vx457 = 0; 						Vx458 = 0; 						Vx456 = 0; 											} 					if (Vx379) { 						Vx256(); 											} 									} 							} 								if (combo_running(Vx111) && (  get_ival(Vx448) ||   get_ival(Vx454) ||         get_ival(Vx451) ||        get_ival(Vx447) ||        get_ival(Vx450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(Vx111); 			} 					} 		else { 			if (!get_ival(Vx452)) Vx222(Vx1095); 					} 			} 			Vx268(); 	} combo Vx1 { 	set_val(Vx451, 100); 	set_val(Vx449,100); 	Vx243(); 	wait(400); 	set_val(Vx448,100); 	wait(90); 	wait( 400); 	} combo Vx2 { 	set_val(Vx451, 100); 	set_val(Vx449,100); 	Vx243(); 	wait(400); 	set_val(Vx447,100); 	wait(220); 	wait( 400); 	} combo Vx3 { 	call(Vx28); 	wait( 100); 	call(Vx98); 	Vx239(Vx1052, Vx669); 	wait( 800); 	wait( 350); 	set_val(Vx450,100); 	set_val(Vx449,100); 	wait( 400); 	} combo Vx4 { 	Vx245(); 	wait(50); 	Vx243(); 	wait(50); 	wait( 350); 	} combo Vx5 { 	Vx243(); 	wait( Vx1055 + random(1,5)); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx6 { 	if (Vx479) Vx686 = Vx593 + 1; 	else Vx686 = Vx593 - 1; 	Vx234(Vx686); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	Vx239(Vx1153, Vx672); 	wait( Vx1055 + random(1,5)); 	Vx239(Vx1153, Vx672); 	wait( 1000); 	wait( 350); 	} combo Vx7 { 	Vx246(); 	Vx479 = FALSE; 	wait(Vx1055 + random(1,5)); 	Vx243(); 	wait(Vx1055 + random(1,5)); 	Vx246(); 	wait(Vx1055 + random(1,5)); 	Vx479 = TRUE; 	Vx243(); 	wait(Vx1055 + random(1,5)); 	wait(350); 	} combo Vx8 { 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx479 = TRUE; 	Vx243(); 	wait( Vx1055 + random(1,5)); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx479 = FALSE; 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx9 { 	Vx479 = TRUE; 	Vx243(); 	wait(Vx1055 + random(1,5)); 	Vx246(); 	wait(Vx1055 + random(1,5)); 	Vx479 = FALSE; 	Vx243(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx10 { 	Vx479 = FALSE; 	Vx243(); 	wait( Vx1055 + random(1,5)); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx479 = TRUE; 	Vx243(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx11 { 	Vx239(0,0); 	set_val(Vx449,100); 	set_val(Vx450,100); 	Vx245(); 	wait( 60); 	wait( 60); 	} combo Vx12 { 	set_val(Vx1118, inv(Vx1052)); 	set_val(Vx1119, inv(Vx669)); 	set_val(Vx450, 100); 	set_val(Vx449, 100); 	wait( 60); 	set_val(Vx1118, inv(Vx1052)); 	set_val(Vx1119, inv(Vx669)); 	set_val(Vx450, 100); 	set_val(Vx449, 100); 	wait( 500); 	wait( 350); 	} combo Vx13 { 	Vx239(0, 0); 	set_val(Vx451, 100); 	wait( 60); 	Vx239(0, 0); 	set_val(Vx451, 100); 	set_val(Vx447, 100); 	wait( 60); 	Vx239(0, 0); 	set_val(Vx451, 100); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 80); 	Vx239(0, 0); 	set_val(Vx451, 100); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	wait( 350); 	} combo Vx14 { 	set_val(Vx447, 100); 	wait( 60); 	Vx239(inv(Vx1052), inv(Vx669)); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 80); 	Vx239(inv(Vx1052), inv(Vx669)); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	wait( 350); 	} combo Vx15 { 	set_val(Vx449, 100); 	Vx243(); 	wait( 500); 	wait( 350); 	} combo Vx16 { 	Vx246(); 	wait( Vx1055 + random(1,5)); 	if(Vx479) Vx686 = Vx593 + 3; 	else  Vx686 = Vx593 - 3; 	Vx234(Vx686); 	Vx236(Vx1153,Vx672); 	wait(Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	if(Vx479) Vx686 = Vx593 + 1; 	else  Vx686 = Vx593 - 1; 	Vx234(Vx686); 	Vx236(Vx1153,Vx672); 	wait(Vx1055 + random(1,5)); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx17 { 	set_val(Vx449,100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	set_val(Vx449,100); 	wait( Vx1055 + random(1,5)); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx18 { 	set_val(Vx451,100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451,100); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451,100); 	Vx243(); 	set_val(Vx451,100); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	set_val(Vx451,100); 	set_val(Vx452,100); 	wait(50); 	wait(350); 	} combo Vx19 { 	set_val(Vx451,100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451,100); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451,100); 	Vx243(); 	set_val(Vx451,100); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx20 { 	Vx244(); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	Vx243()    	wait( Vx1055 + random(1,5)); 	Vx479 = !Vx479; 	Vx242(); 	wait( 1000); 	wait( 350); 	} combo Vx21 { 	set_val(Vx449,100); 	Vx246(); 	wait(50); 	Vx239(0,0); 	set_val(Vx449,100); 	wait(50); 	set_val(Vx449,100); 	Vx246(); 	wait(50); 	wait( 350); 	} combo Vx22 { 	Vx239(0, 0); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx23 { 	Vx246(); 	wait(Vx1055 + random(1,5)); 	Vx247()wait(Vx1055 + random(1,5)); 	Vx246(); 	wait(Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx24 { 	set_val(Vx450, 100); 	set_val(Vx449, 100); 	wait( 20); 	set_val(Vx450, 100); 	set_val(Vx449, 100); 	if (Vx479) Vx686 = Vx593 + 4; 	else { 		Vx686 = Vx593 - 4; 			} 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	set_val(Vx452, 100); 	wait( 100); 	wait( 350); 	} combo Vx25 { 	set_val(Vx451, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451, 100); 	wait( 30); 	set_val(Vx451, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx26 { 	set_val(Vx451, 100); 	Vx245(); 	wait( 70); 	set_val(Vx451, 100); 	Vx247(); 	wait( 60); 	set_val(Vx451, 100); 	Vx246(); 	wait( 60); 	wait( 350); 	} combo Vx27 { 	set_val(Vx451, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451, 100); 	wait( 30); 	set_val(Vx451, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx239(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} combo Vx28 { 	Vx243(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo Vx29 { 	Vx243(); 	set_val(Vx452, 0); 	wait( 310); 	wait( 100); 	wait( 350); 	} combo Vx30 { 	if (Vx1051 == Vx312) Vx1056 = 200; 	else Vx1056 = 1; 	wait( Vx1056); 	Vx245(); 	wait( 70); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( 70); 	wait( 350); 	} combo Vx31 { 	set_val(Vx449, 100)Vx245(); 	Vx239(Vx1052,Vx669); 	wait( 50); 	set_val(Vx449, 100)Vx247(); 	Vx239(Vx1052,Vx669); 	wait( 50); 	set_val(Vx449, 100)Vx243(); 	Vx239(Vx1052,Vx669); 	wait( 50); 	Vx239(Vx1052,Vx669); 	wait(465); 	Vx239(Vx1052,Vx669); 	set_val(Vx451, 100); 	set_val(Vx452, 100); 	wait(50); 	if (Vx479) Vx686 = Vx593 - 1; 	else Vx686 = Vx593 + 1; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 50); 	if (Vx479) Vx686 = Vx593 + 4; 	else Vx686 = Vx593 - 4; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 700); 	wait( 350); 	} combo Vx32 { 	if (Vx1051 == Vx312) Vx1056 = 200; 	else Vx1056 = 1; 	set_val(Vx451,100); 	wait( Vx1056); 	Vx245(); 	set_val(Vx451,100); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	set_val(Vx451,100); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	set_val(Vx451,100); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx33 { 	if (Vx479) Vx686 = Vx593 - 2; 	else Vx686 = Vx593 + 2; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	wait( 280); 	Vx247(); 	wait( 50); 	if (Vx479) Vx686 = Vx593 + 2; 	else Vx686 = Vx593 - 2; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 45); 	set_val(Vx447, 100); 	Vx239(Vx1153, Vx672); 	wait( 45); 	Vx239(Vx1153, Vx672); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 45); 	Vx239(Vx1153, Vx672); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 45); 	Vx239(Vx1153, Vx672); 	wait( 100); 	Vx239(Vx1153, Vx672); 	wait( 500); 	wait( 350); 	} combo Vx34 { 	Vx243(); 	wait( 280); 	Vx242()  set_val(Vx447, 100); 	set_val(Vx451, 100); 	wait( 60); 	Vx242()  set_val(Vx451, 100); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 60); 	Vx242()  set_val(Vx451, 100); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	wait( 250); 	Vx242()   	wait( 300); 	wait( 350); 	} combo Vx35 { 	Vx243(); 	wait( 300); 	Vx245(); 	wait( 60); 	wait( 350); 	} combo Vx36 { 	set_val(Vx449, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx449, 100); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx449, 100); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx37 { 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx38 { 	set_val(Vx449, 100); 	Vx244(); 	wait( 60); 	set_val(Vx449, 100); 	Vx247(); 	wait( 60); 	set_val(Vx449, 100); 	Vx243(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo Vx39 { 	Vx246(); 	set_val(Vx449,100); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	set_val(Vx449,100); 	wait( 70); 	Vx243(); 	set_val(Vx449,100); 	wait( 70); 	wait( 350); 	} combo Vx40 { 	if (Vx479) Vx686 = Vx593 + 3; 	else Vx686 = Vx593 - 3; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	set_val(Vx447, 100); 	set_val(Vx451,100); 	wait( 60); 	set_val(Vx451,100); 	Vx239(Vx1153, Vx672); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 80); 	set_val(Vx451,100); 	Vx239(Vx1153, Vx672); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	set_val(Vx451,100); 	Vx239(Vx1153, Vx672); 	wait( 300); 	wait( 350); 	} combo Vx41 { 	set_val(Vx449, 100); 	Vx245(); 	Vx239(0, 0); 	wait( Vx1055 + random(1,5)); 	set_val(Vx449, 100); 	Vx247(); 	Vx239(0, 0); 	wait( 65); 	set_val(Vx449, 100); 	Vx239(0, 0); 	Vx246(); 	wait( Vx1055 + random(1,5)); 	if (Vx479) Vx686 = Vx593 + 1; 	else Vx686 = Vx593 - 1; 	Vx234(Vx686); 	set_val(Vx452,0); 	Vx239(Vx1153, Vx672); 	wait( 200); 	set_val(Vx452,0); 	wait( 350); 	} combo Vx42 { 	if (Vx1051 == Vx312) Vx1056 = 200; 	else Vx1056 = 1; 	wait( Vx1056); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	wait( 350); 	} combo Vx43 { 	Vx246(); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} combo Vx44 { 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	if (Vx1051 == Vx325) Vx242(); 	set_val(Vx451, 100); 	set_val(Vx452, 100); 	wait( 200); 	if (Vx1051 == Vx325) Vx242(); 	wait( 300); 	wait( 350); 	} combo Vx45 { 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx247(); 	wait( Vx1055 + random(1,5)); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	if (Vx1051 == Vx325) Vx242(); 	set_val(Vx451, 100); 	set_val(Vx452, 100); 	wait( 200); 	if (Vx1051 == Vx325) Vx242(); 	wait( 300); 	wait( 350); 	} combo Vx46 { 	call(Vx33)call(Vx35); 	} combo Vx47 {    Vx717 = FALSE; 	Vx239(Vx1052, Vx669); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx239(Vx1052, Vx669); 	Vx247(); 	Vx717 = FALSE; 	wait( Vx1055 + random(1,5)); 	Vx239(Vx1052, Vx669); 	Vx243(); 	Vx717 = FALSE; 	wait( Vx1055 + random(1,5)); 	set_val(Vx451, 100); 	set_val(Vx452, 100); 	Vx239(inv(Vx1052), inv(Vx669)); 	Vx717 = FALSE; 	wait( 400); 	wait( 350); 	Vx717 = TRUE; 	} combo Vx48 { 	Vx239(Vx1052, Vx669); 	set_val(XB1_LS, 100); 	Vx245(); 	wait( Vx1055 + random(1,5)); 	Vx239(Vx1052, Vx669); 	Vx247(); 	set_val(XB1_LS, 100); 	wait( Vx1055 + random(1,5)); 	Vx239(Vx1052, Vx669); 	Vx243(); 	wait( Vx1055 + random(1,5)); 	set_val(Vx451, 100); 	set_val(Vx452, 100); 	if (Vx479) Vx686 = Vx593 + 4; 	else Vx686 = Vx593 - 4; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 220); 	if (Vx479) Vx686 = Vx593 + 4; 	else Vx686 = Vx593 - 4; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 60); 	if (Vx479) Vx686 = Vx593 + 1; 	else Vx686 = Vx593 - 1; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	wait( 600); 	wait( 350); 	} combo Vx49 { 	set_val(Vx448, 0); 	set_val(Vx447, 100); 	wait( 80); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 80); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo Vx50 { 	set_val(Vx447, 100); 	set_val(Vx452,100); 	wait( 60); 	set_val(Vx452,100); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	set_val(Vx452,100); 	wait( 60); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	set_val(Vx452,100); 	wait( 60); 	wait( 350); 	} combo Vx51 { 	set_val(Vx449,100); 	set_val(Vx450,100); 	Vx239(inv(Vx1052), inv(Vx669)); 	wait( 200); 	set_val(Vx449,100); 	set_val(Vx450,100); 	Vx479 = FALSE; 	Vx242(); 	wait( 50); 	set_val(Vx449,100); 	set_val(Vx450,100); 	Vx479 = !Vx479; 	Vx242(); 	set_val(Vx449,100); 	set_val(Vx450,100); 	wait( 540); 	wait( 350); 	} combo Vx52 { 	set_val(Vx447, 100); 	wait( 60); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 60); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} combo Vx53 { 	Vx239(inv(Vx1052), inv(Vx669)); 	set_val(Vx451, 100); 	set_val(Vx447, 100); 	wait( 60); 	Vx239(inv(Vx1052), inv(Vx669)); 	set_val(Vx451, 100); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 60); 	Vx239(inv(Vx1052), inv(Vx669)); 	set_val(Vx451, 100); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	Vx239(0, 0); 	wait( 300); 	wait( 350); 	} combo Vx54 { 	set_val(Vx449, 100); 	set_val(Vx453, 100); 	wait( 60); 	set_val(Vx449, 100); 	set_val(Vx453, 100); 	set_val(Vx448, 100); 	wait( 60); 	set_val(Vx449, 100); 	set_val(Vx453, 0); 	set_val(Vx448, 100); 	Vx242(); 	wait( 60); 	set_val(Vx449, 100); 	Vx242(); 	wait( 300); 	wait( 350); 	} combo Vx55 { 	set_val(Vx447, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo Vx56 { 	set_val(Vx447, 100); 	set_val(Vx451,100); 	wait( 60); 	set_val(Vx451,100); 	set_val(Vx447, 100); 	set_val(Vx448, 100); 	wait( 60); 	set_val(Vx451,100); 	set_val(Vx447, 0); 	set_val(Vx448, 100); 	wait( 60); 	wait( 350); 	} combo Vx57 { 	set_val(Vx449, 100); 	Vx245(); 	wait( 300); 	wait( 350); 	} combo Vx58 { 	Vx246(); 	wait( 70); 	Vx247(); 	wait( 70); 	Vx245(); 	wait( 70); 	wait( 350); 	} combo Vx59 { 	set_val(Vx449,100); 	Vx246(); 	wait( 70); 	set_val(Vx449,100); 	Vx247(); 	wait( 70); 	Vx245(); 	set_val(Vx449,100); 	wait(50); 	wait( 350); 	} combo Vx60 { 	Vx239(Vx1052, Vx669); 	Vx246(); 	wait( 100); 	Vx247(); 	Vx239(Vx1052, Vx669); 	wait( 60); 	Vx245(); 	Vx239(Vx1052, Vx669); 	wait( 320); 	Vx239(Vx1052, Vx669); 	Vx247(); 	wait( 220); 	Vx239(Vx1052, Vx669); 	Vx245(); 	Vx239(Vx1052, Vx669); 	wait( 100); 	wait( 350); 	} combo Vx61 { 	call(Vx83); 	Vx239(0, 0); 	call(Vx84); 	call(Vx84); 	call(Vx84); 	call(Vx84); 	call(Vx84); 	set_val(Vx451, 100); 	Vx246(); 	wait( 70); 	set_val(Vx451, 100); 	Vx247(); 	wait( 60); 	set_val(Vx451, 100); 	Vx245(); 	wait( 60); 	set_val(Vx451, 100); 	wait( 600); 	wait( 350); 	} combo Vx62 { 	set_val(Vx451,100); 	set_val(Vx450,100); 	if (Vx479) Vx686 = Vx593 - 2; 	else Vx686 = Vx593 + 2; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	wait(50); 	set_val(Vx450,100); 	set_val(Vx451,100); 	if (Vx479) Vx686 = Vx593 - 3; 	else Vx686 = Vx593 + 3; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	wait(50); 	set_val(Vx450,100); 	set_val(Vx451,100); 	if (Vx479) Vx686 = Vx593 - 4; 	else Vx686 = Vx593 + 4; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	wait(50); 	set_val(Vx450,100); 	set_val(Vx451,100); 	if (Vx479) Vx686 = Vx593 - 5; 	else Vx686 = Vx593 + 5; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	set_val(Vx451,100); 	set_val(Vx450,100); 	wait(50); 	set_val(Vx450,100); 	set_val(Vx451,100); 	if (Vx479) Vx686 = Vx593 - 6; 	else Vx686 = Vx593 + 6; 	Vx234(Vx686); 	Vx236(Vx1153, Vx672); 	wait(50); 	} combo Vx63 { 	wait( 100); 	Vx239(0, 0); 	Vx245(); 	wait( 70); 	Vx239(0, 0); 	Vx247()   	wait( 70); 	Vx239(0, 0); 	Vx245()   	wait( 70); 	Vx239(0, 0); 	Vx247()   	wait( 70); 	Vx239(0, 0); 	Vx246(); 	wait( 70); 	Vx239(0, 0); 	wait( 350); 	} combo Vx64 { 	set_val(PS4_R3,100); 	if (Vx479) Vx686 = Vx593 + 1; 	else Vx686 = Vx593 - 1; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	Vx239(Vx1153, Vx672); 	wait( 70); 	Vx239(Vx1153, Vx672); 	wait( 400); 	wait( 350); 	} combo Vx65 { 	call(Vx83); 	Vx239(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (Vx479) Vx686 = Vx593 + 1; 	else Vx686 = Vx593 - 1; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	Vx239(Vx1153, Vx672); 	wait( 70); 	Vx239(Vx1153, Vx672); 	wait( 400); 	wait( 350); 	} combo Vx66 { 	call(Vx83); 	Vx239(0,0); 	set_val(Vx451,100); 	set_val(Vx452,100); 	wait( 750); 	} combo Vx67 { 	set_val(PS4_R3,100); 	if (Vx479) Vx686 = Vx593 + 2; 	else Vx686 = Vx593 - 2; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	Vx239(Vx1153, Vx672); 	wait( 70); 	Vx239(Vx1153, Vx672); 	wait( 400); 	wait( 350); 	} combo Vx68 { 	set_val(Vx451,100); 	set_val(PS4_R3,100); 	if (Vx479) Vx686 = Vx593 ; 	else Vx686 = Vx593; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	Vx239(Vx1153, Vx672); 	wait( 70); 	set_val(Vx451,100); 	Vx239(Vx1153, Vx672); 	wait( 400); 	wait( 350); 	} combo Vx69 { 	call(Vx83); 	set_val(Vx451,100); 	set_val(PS4_R3,100); 	if (Vx479) Vx686 = Vx593 ; 	else Vx686 = Vx593; 	Vx234(Vx686); 	Vx239(Vx1153, Vx672); 	Vx239(Vx1153, Vx672); 	wait( 70); 	set_val(Vx451,100); 	Vx239(Vx1153, Vx672); 	wait( 400); 	wait( 350); 	} combo Vx70 { 	Vx239(0,0); 	set_val(Vx450,100); 	set_val(Vx449,100); 	Vx243(); 	wait( 350); 	wait( 350); 	set_val(Vx450,100); 	set_val(Vx449,100); 	wait( 400); 	} int Vx141 ; int Vx763 ; int Vx764 ; int Vx765; int Vx766; function Vx127(Vx128){ 	Vx763 = 2; 	Vx764 = 987654; 	Vx141 = 54321; 	Vx765 = (Vx128 >> Vx763) | (Vx128 << (32 - Vx763)); 	Vx766 = (((Vx765 >> ((Vx765 & 0xF) % 13)) & 0x7FFFF) + Vx141) % Vx764 + 123456; 	return Vx766; 	} define Vx768 = -1; define Vx536 = -2; define Vx770 = -3; define Vx771 = 0; define Vx537 = 1; function Vx129(Vx128, Vx131, Vx132) { 	if(Vx128 > Vx132) return Vx131; 	if(Vx128 < Vx131) return Vx132; 	return Vx128; 	} int Vx775,Vx776; function Vx133(Vx134,Vx135,Vx136,Vx137,Vx138,Vx139){ 	if(!Vx139){ 		print(Vx142(Vx140(Vx134),Vx137,Vx135),Vx136,Vx137,Vx138,Vx134)     	} 	else{ 		if(Vx134 < 0){ 			putc_oled(1,45); 					} 		if(Vx134){ 			for(Vx775 = Vx146(Vx134) + Vx776 = (Vx134 < 0 ),Vx134 = abs(Vx134); 			Vx134 > 0; 			Vx775-- , Vx776++){ 				putc_oled(Vx775,Vx134%10 + 48); 				Vx134 = Vx134/10; 							} 					} 		else{ 			putc_oled(1,48); 			Vx776 = 1         		} 		puts_oled(Vx142(Vx776,Vx137,Vx135),Vx136,Vx137,Vx776 ,Vx138); 			} 	} int Vx797; function Vx140(Vx141) { 	Vx797 = 0; 	do { 		Vx141++; 		Vx797++; 			} 	while (duint8(Vx141)); 	return Vx797; 	} function Vx142(Vx143,Vx137,Vx135) { 	if(Vx135 == -3){ 		return 128 - ((Vx143 * (7 + (Vx137 > 1) + Vx137 * 4)) + 3 ); 			} 	if(Vx135 == -2){ 		return 64 - ((Vx143 * (7 + (Vx137 > 1) + Vx137 * 4)) / 2); 			} 	if(Vx135 == -1){ 		return 3 	} 	return Vx135; 	} function Vx146(Vx147) { 	for(Vx775 = 1; 	Vx775 < 11; 	Vx775++){ 		if(!(abs(Vx147) / pow(10,Vx775))){ 			return Vx775; 			break; 					} 			} 	return 1; 	} function Vx148() { 	if (get_ival(Vx447)) { 		set_val(Vx447, 0); 		if (get_ival(Vx449)) Vx804 = 50; 		if (!get_ival(Vx449)) Vx804 = 410; 		combo_run(Vx71); 			} 	if (Vx803 > 0) set_polar(POLAR_LS, Vx803 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) Vx803 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) Vx803 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) Vx803 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) Vx803 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) Vx803 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) Vx803 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) Vx803 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) Vx803 = 270; } int Vx804; int Vx557; int Vx803; combo Vx71 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait( Vx804); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	Vx557 = !Vx557; } define Vx807 = 19; function Vx149(Vx150, Vx151) { 	if (Vx354 == Vx151) { 		if (event_press(PS4_RIGHT)) { 			Vx150 = clamp(Vx150 + 1, 0, Vx810[Vx354]); 			Vx359 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			Vx150 = clamp(Vx150 - 1, 0, Vx810[Vx354]); 			Vx359 = TRUE; 					} 		if (Vx354 == 0) { 			print(Vx214(Vx167(Vx814[Vx360]) ,OLED_FONT_SMALL_WIDTH),Vx807  ,OLED_FONT_SMALL , OLED_WHITE ,Vx814[Vx360]); 					} 		else if (Vx354 == 1) { 			print(Vx214(Vx167(Vx816[Vx361]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx816[Vx361]); 					} 		else if (Vx354 == 2) { 			print(Vx214(Vx167(Vx816[Vx362]) ,OLED_FONT_SMALL_WIDTH ),Vx807  ,OLED_FONT_SMALL , OLED_WHITE ,Vx816[Vx362]); 					} 		else if (Vx354 == 5) { 			print(Vx214(Vx167(Vx820[Vx365]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx820[Vx365]); 					} 		else if (Vx354 == 6) { 			print(Vx214(Vx167(Vx822[Vx366]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx366]); 					} 		else if (Vx354 == 7) { 			print(Vx214(Vx167(Vx822[Vx367]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx367]); 					} 		else if (Vx354 == 8) { 			print(Vx214(Vx167(Vx822[Vx368]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx368]); 					} 		else if (Vx354 == 9) { 			print(Vx214(Vx167(Vx822[Vx369]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx369]); 					} 		else if (Vx354 == 20) { 			print(Vx214(Vx167(Vx830[Vx114]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx830[Vx114]); 					} 		else { 			if (Vx150 == 1)        print(Vx214(Vx167(Vx832[1]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx832[1])      else        print(Vx214(Vx167(Vx832[0]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx832[0])     		} 			} 	return Vx150; 	} function Vx152(Vx150, Vx151) { 	if (Vx355 == Vx151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				Vx150 += Vx838[Vx355][2]  				        Vx359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				Vx150 -= Vx838[Vx355][2]  				        Vx359 = TRUE; 							} 			Vx150 = clamp(Vx150, Vx838[Vx355][0], Vx838[Vx355][1]); 		} 		if (Vx355 == 8) { 			print(Vx214(Vx167(Vx822[Vx386]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx386])     		} 		else if (Vx355 == 9) { 			print(Vx214(Vx167(Vx822[Vx387]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx387])     		} 		else if (Vx355 == 10) { 			print(Vx214(Vx167(Vx822[Vx388]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx388])     		} 		else if (Vx355 == 11) { 			print(Vx214(Vx167(Vx822[Vx389]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx389])     		} 		else if (Vx355 == 12) { 			print(Vx214(Vx167(Vx822[Vx390]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx390])     		} 		else if (Vx355 == 13) { 			print(Vx214(Vx167(Vx822[Vx391]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx391])     		} 		else if (Vx355 == 14) { 			print(Vx214(Vx167(Vx822[Vx392]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx392])     		} 		else if (Vx355 == 15) { 			print(Vx214(Vx167(Vx822[Vx393]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx393])     		} 		else if (Vx355 == 16) { 			print(Vx214(Vx167(Vx857[Vx394]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx394])     		} 		else if (Vx355 == 17) { 			print(Vx214(Vx167(Vx822[Vx275]),OLED_FONT_SMALL_WIDTH ),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx822[Vx275])  		} 		else if(Vx355 == 18){ 			print(Vx214(Vx167(Vx822[Vx276]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx822[Vx276])  		} 		else if(Vx355 == 19){ 			print(Vx214(Vx167(Vx822[Vx277]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx822[Vx277])  		} 		else if(Vx355 == 20){ 			print(Vx214(Vx167(Vx822[Vx278]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx822[Vx278])  		} 		else if(Vx355 == 21){ 			print(Vx214(Vx167(Vx857[Vx279]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx857[Vx279])       		} 		else if(Vx355 == 22){ 			print(Vx214(Vx167(Vx822[Vx407]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx407])     		} 		else if (Vx355 == 23) { 			print(Vx214(Vx167(Vx822[Vx408]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx408])     		} 		else if (Vx355 == 24) { 			print(Vx214(Vx167(Vx822[Vx409]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx409])     		} 		else if (Vx355 == 25) { 			print(Vx214(Vx167(Vx822[Vx410]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx410])     		} 		else if (Vx355 == 26) { 			print(Vx214(Vx167(Vx822[Vx411]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx822[Vx411])     		} 		else if (Vx355 == 27) { 			print(Vx214(Vx167(Vx857[Vx412]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx412])     		} 		else if (Vx355 == 28) { 			print(Vx214(Vx167(Vx881[Vx413]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx881[Vx413])     		} 		else if (Vx355 == 29) { 			print(Vx214(Vx167(Vx883[Vx414]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx883[Vx414])     		} 		else if (Vx355 == 30) { 			print(Vx214(Vx167(Vx857[Vx415]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx415])     		} 		else if (Vx355 == 31) { 			print(Vx214(Vx167(Vx881[Vx416]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx881[Vx416])     		} 		else if (Vx355 == 32) { 			print(Vx214(Vx167(Vx883[Vx417]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx883[Vx417])     		} 		else if (Vx355 == 33) { 			print(Vx214(Vx167(Vx857[Vx418]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx418])     		} 		else if (Vx355 == 34) { 			print(Vx214(Vx167(Vx881[Vx419]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx881[Vx419])     		} 		else if (Vx355 == 35) { 			print(Vx214(Vx167(Vx883[Vx420]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx883[Vx420])     		} 		else if (Vx355 == 36) { 			print(Vx214(Vx167(Vx857[Vx421]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx421])     		} 		else if (Vx355 == 37) { 			print(Vx214(Vx167(Vx881[Vx422]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx881[Vx422])     		} 		else if (Vx355 == 38) { 			print(Vx214(Vx167(Vx883[Vx423]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx883[Vx423])     		} 		else if (Vx355 == 41) { 			print(Vx214(Vx167(Vx857[Vx426]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx426])     		} 		else if (Vx355 == 48) { 			print(Vx214(Vx167(Vx857[Vx433]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx433])     		} 		else if (Vx355 == 49) { 			print(Vx214(Vx167(Vx857[Vx434]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx434])     		} 		else if (Vx355 == 50) { 			print(Vx214(Vx167(Vx857[Vx435]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx435])     		} 		else if (Vx355 == 51) { 			print(Vx214(Vx167(Vx857[Vx436]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx857[Vx436])     		} 		else if(Vx355 == 0){ 			print(Vx214(Vx167(Vx913[Vx439]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx439])  		} 		else if(Vx355 == 1){ 			print(Vx214(Vx167(Vx913[Vx440]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx440])  		} 		else if(Vx355 == 2){ 			print(Vx214(Vx167(Vx913[Vx441]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx441])  		} 		else if(Vx355 == 3){ 			print(Vx214(Vx167(Vx913[Vx442]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx442])  		} 		else if(Vx355 == 4){ 			print(Vx214(Vx167(Vx913[Vx443]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx443])  		} 		else if(Vx355 == 5){ 			print(Vx214(Vx167(Vx913[Vx444]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx444])  		} 		else if(Vx355 == 6){ 			print(Vx214(Vx167(Vx913[Vx445]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx445])  		} 		else if(Vx355 == 7){ 			print(Vx214(Vx167(Vx913[Vx446]),OLED_FONT_SMALL_WIDTH),Vx807,OLED_FONT_SMALL,OLED_WHITE,Vx913[Vx446])  		} 		else{ 			if (Vx150 == 1)        print(Vx214(Vx167(Vx832[1]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx832[1])      else        print(Vx214(Vx167(Vx832[0]), OLED_FONT_SMALL_WIDTH), Vx807, OLED_FONT_SMALL, OLED_WHITE, Vx832[0])     		} 		Vx170(0); 			} 	return Vx150; 	} function Vx155(Vx150, Vx151) { 	if (Vx355 == Vx151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				Vx150 += Vx838[Vx355][2]  				        Vx359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				Vx150 -= Vx838[Vx355][2]  				        Vx359 = TRUE; 							} 			if (event_press(PS4_UP)) { 				Vx150 += Vx838[Vx355][3]  				        Vx359 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				Vx150 -= Vx838[Vx355][3]  				        Vx359 = TRUE; 							} 			Vx150 = clamp(Vx150, Vx838[Vx355][0], Vx838[Vx355][1]); 		} 		Vx217(Vx150, Vx220(Vx150)); 	} 	return Vx150; 	} int Vx940, Vx941, Vx942; function Vx158(Vx128, Vx160, Vx161, Vx162, Vx137) { 	Vx941 = 1; 	Vx942 = 10000; 	if (Vx128 < 0)  	  { 		putc_oled(Vx941, 45); 		Vx941 += 1; 		Vx128 = abs(Vx128); 			} 	for (Vx940 = 5; 	Vx940 >= 1; 	Vx940--) { 		if (Vx160 >= Vx940) { 			putc_oled(Vx941, Vx948[Vx128 / Vx942]); 			Vx128 = Vx128 % Vx942; 			Vx941 += 1; 					} 		Vx942 /= 10; 			} 	puts_oled(Vx161, Vx162, Vx137, Vx941 - 1, OLED_WHITE); } const string Vx574 = " No Edit Variable"; const string Vx573 = " A/CROSS to Edit "; const string Vx569 = "MOD;"; const string Vx571 = "MSL;"; int Vx952; function Vx164(Vx147) { 	Vx147 = abs(Vx147); 	if (Vx147 / 10000 > 0) return 5; 	if (Vx147 / 1000 > 0) return 4; 	if (Vx147 / 100 > 0) return 3; 	if (Vx147 / 10 > 0) return 2; 	return 1; 	} const int8 Vx948[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int Vx954, Vx955; const image Vx957 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo Vx72 { 	call(Vx73); 	Vx166(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, Vx957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, Vx957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(Vx74); 	vm_tctrl(0);wait( 1000); 	Vx356 = TRUE; 	} combo Vx73 { 	cls_oled(OLED_BLACK); 	} int Vx959; enum { 	Vx960 = -2, Vx961, Vx962 = 5, Vx963 = -1, Vx964 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo Vx74 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 Vx1389[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 Vx1390[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 Vx1391[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 Vx1392[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int Vx965; int Vx966; int Vx967; int Vx968; int Vx969; int Vx970; int Vx971; function Vx166() { 	Vx971 = 3; 	Vx969 = Vx971 * Vx1392[Vx970]; 	Vx968 = Vx971 * Vx1389[Vx970]; 	Vx966 = ((Vx969 * Vx1391[Vx965]) / 328) - ((Vx968 * Vx1390[Vx965]) / 328); 	Vx967 = ((Vx969 * Vx1390[Vx965]) / 328) + ((Vx968 * Vx1391[Vx965]) / 328); 	Vx969 = Vx966; 	Vx968 = Vx967; 	Vx970 += 1; 	Vx965 += 45; 	if(Vx970 >= 360) { 		Vx970 %= 360; 			} 	if(Vx965 >= 360) { 		Vx965 %= 360; 			} 	pixel_oled(64 + (((Vx969 / Vx971) * 30) / 328), 32 + (((Vx968 / Vx971) * 30) / 328), OLED_WHITE); 	} int Vx975; function Vx167(Vx141) { 	Vx975 = 0; 	do { 		Vx141++; 		Vx975++; 			} 	while (duint8(Vx141)); 	return Vx975; 	} int Vx978; const uint8 Vx1393[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function Vx169() { 	for (Vx978 = 0; 	Vx978 < sizeof(Vx1393) / sizeof(Vx1393[0]); 	Vx978++) { 		if (get_ival(Vx1393[Vx978]) || event_press(Vx1393[Vx978])) { 			set_val(Vx1393[Vx978], 0); 		} 			} 	} define Vx979 = 131; define Vx980 = 132; define Vx981 = 133; define Vx982 = 134; define Vx983 = 130; define Vx984 = 89; define Vx985 = 127; define Vx986 = 65; int Vx987; int Vx988; int Vx989 = 1; define Vx990 = 36; const string Vx991 = "Hold LT/L2 +"; function Vx170(Vx171) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, Vx991[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, Vx989); 	putc_oled(1, Vx981); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, Vx987); 	putc_oled(1, Vx982); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, Vx988); 	if (Vx171) { 		putc_oled(1, Vx979); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, Vx987); 		putc_oled(1, Vx980); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, Vx988); 			} 	} const uint8 Vx1395 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function Vx172() { 	Vx1001 = sizeof(data); 	Vx508 = get_pvar(SPVAR_1,0,1,0); 	Vx514 = get_pvar(SPVAR_2,0,1,0); 	Vx507 = get_pvar(SPVAR_3,11111, 99999,11111); 	Vx174(); 	if (Vx199(0, 1, 0)) { 		Vx365 = Vx199(  0, 6, 0); 		Vx362 = Vx199(0, 3, 0); 		Vx363 = Vx199(0,1,0); 		Vx364 = Vx199(0,1,0); 		Vx275 = Vx199(0, 70, 0); 		Vx276 = Vx199(0, 70, 0); 		Vx277 = Vx199(0, 70, 0); 		Vx278 = Vx199(0, 70, 0); 		Vx279 = Vx199(0, 22, 8); 		Vx366 = Vx199(0, 70, 0); 		Vx367 = Vx199(0, 70, 0); 		Vx368 = Vx199(0, 70, 0); 		Vx369 = Vx199(0, 70, 0); 		Vx370 = Vx199(0, 1, 0); 		Vx371 = Vx199(0, 1, 0); 		Vx372 = Vx199(0, 1, 0); 		Vx373 = Vx199(0, 1, 0); 		Vx381 = Vx199(0, 1, 0); 		Vx407 = Vx199(0, 70, 0); 		Vx408 = Vx199(0, 70, 0); 		Vx409 = Vx199(0, 70, 0); 		Vx410 = Vx199(0, 70, 0); 		Vx411 = Vx199(0, 70, 0); 		Vx412 = Vx199(1, 25, 1); 		Vx413 = Vx199(0, 1, 0); 		Vx414 = Vx199(0, 1, 0); 		Vx415 = Vx199(1, 25, 5); 		Vx416 = Vx199(0, 1, 0); 		Vx417 = Vx199(0, 1, 0); 		Vx418 = Vx199(0, 25, 2); 		Vx419 = Vx199(0, 1, 0); 		Vx420 = Vx199(0, 1, 1); 		Vx421 = Vx199(1, 25, 8); 		Vx422 = Vx199(0, 1, 0); 		Vx423 = Vx199(0, 1, 1); 		Vx424 = Vx199(350, 600, 350); 		Vx425 = Vx199(350, 600, 445); 		Vx426 = Vx199(0, 22, 0); 		Vx427 = Vx199(0, 1, 0); 		Vx428 = Vx199(-100, 300, 0); 		Vx374 = Vx199(0, 1, 0); 		Vx375 = Vx199(0, 1, 0); 		Vx376 = Vx199(0, 1, 0); 		Vx377 = Vx199(0, 1, 0); 		Vx378 = Vx199(0, 1, 0); 		Vx429 = Vx199(-150, 150, 0); 		Vx430 = Vx199(-150, 150, 0); 		Vx431 = Vx199(0, 1, 0); 		Vx432 = Vx199(-150, 150, 0); 		Vx433 = Vx199(0, 22, 0); 		Vx434 = Vx199(0, 22, 0); 		Vx435 = Vx199(0, 22, 0); 		Vx436 = Vx199(0, 22, 0); 		Vx608 = Vx199(60, 400, 235); 		Vx438 = Vx199(0, 1, 0); 		Vx437 = Vx199(0, 1, 0); 		Vx361 = Vx199(0, 3, 0); 		Vx386 = Vx199(0, 70, 0); 		Vx387 = Vx199(0, 70, 0); 		Vx388 = Vx199(0, 70, 0); 		Vx391 = Vx199(0, 70, 0); 		Vx392 = Vx199(0, 70, 0); 		Vx393 = Vx199(0, 70, 0); 		Vx394 = Vx199(0, 22, 8); 		Vx379 = Vx199(0, 1, 0); 		Vx389 = Vx199(0, 70, 0); 		Vx390 = Vx199(0, 70, 0); 		Vx600 = Vx199(0, 2500, 1100); 		Vx1250 = Vx199(0, 1, 0); 		Vx1243 = Vx199(0, 1, 0); 		Vx114 = Vx199(0, 10, 0); 		Vx406 = Vx199(0, 1, 0); 		Vx360 = Vx199(0, 2, 0); 		Vx439 = Vx199(0, 9, 9); 		Vx440 = Vx199(0, 9, 8); 		Vx441 = Vx199(0, 9, 3); 		Vx442 = Vx199(0, 9, 1); 		Vx443 = Vx199(0, 9, 4); 		Vx444 = Vx199(0, 9, 0); 		Vx445 = Vx199(0, 9, 7); 		Vx446 = Vx199(0, 9, 6); 		Vx382    = Vx199(0, 1, 0); 		Vx383    = Vx199(0, 1, 0); 		Vx384     = Vx199(0, 1, 0); 		Vx395     = Vx199(60, 500, 120); 		Vx396     = Vx199(60, 500, 350); 		Vx397    = Vx199(0, 1, 0); 		Vx398 = Vx199(0, 1, 0); 		Vx399     = Vx199(50, 250, 80); 		Vx400     = Vx199(100, 850, 180); 		Vx401 = Vx199(0, 1, 0); 		Vx402    = Vx199(0, 1, 0); 		Vx403        = Vx199(80, 500, 120); 		Vx404        = Vx199(80, 500, 350); 		Vx405       = Vx199(0, 1, 0); 		Vx455           = Vx199(13, 30, 13); 		Vx29           = Vx199(0, 1, 0); 		Vx476         = Vx199(0, 1, 0); 		Vx474       = Vx199(0, 1, 0); 	} 	else{ 		Vx365 = 0; 		Vx362 = 0; 		Vx363 = 0; 		Vx364 = 0; 		Vx275 = 0; 		Vx276 = 0; 		Vx277 = 0; 		Vx278 = 0; 		Vx279 = 8; 		Vx366 = 0; 		Vx367 = 0; 		Vx368 = 0; 		Vx369 = 0; 		Vx370 = 0; 		Vx371 = 0; 		Vx372 = 0; 		Vx373 = 0; 		Vx381 = 0; 		Vx407 = 0; 		Vx408 = 0; 		Vx409 = 0; 		Vx410 = 0; 		Vx411 = 0; 		Vx412 = 1; 		Vx413 = 0; 		Vx414 = 0; 		Vx415 = 5; 		Vx416 = 0; 		Vx417 = 0; 		Vx418 = 2; 		Vx419 = 0; 		Vx420 = 1; 		Vx421 = 8; 		Vx422 = 0; 		Vx423 = 1; 		Vx424 = 350; 		Vx425 = 445; 		Vx426 = 0; 		Vx427 = 0; 		Vx428 = 0; 		Vx374 = 0; 		Vx375 = 0; 		Vx376 = 0; 		Vx377 = 0; 		Vx378 = 0; 		Vx429 = 0; 		Vx430 = 0; 		Vx431 = 0; 		Vx432 = 0; 		Vx433 = 0; 		Vx434 = 0; 		Vx435 = 0; 		Vx436 = 0; 		Vx608 = 235; 		Vx438 = 0; 		Vx437 = 0; 		Vx361 = 0; 		Vx386 = 0; 		Vx387 = 0; 		Vx388 = 0; 		Vx391 = 0; 		Vx392 = 0; 		Vx393 = 0; 		Vx394 = 8; 		Vx379 = 0; 		Vx389 = 0; 		Vx390 = 0; 		Vx600 = 1100; 		Vx1250 = 0; 		Vx1243 = 0; 		Vx114 = 0; 		Vx406 = 0; 		Vx360 = 0; 		Vx439 = 9; 		Vx440 = 8; 		Vx441 = 3; 		Vx442 = 1; 		Vx443 = 4; 		Vx444 = 0; 		Vx445 = 7; 		Vx446 = 6; 		Vx382 = 0; 		Vx383 = 0; 		Vx384 = 0; 		Vx395 = 120; 		Vx396 = 350; 		Vx397 = 0; 		Vx398 = 0; 		Vx399 = 80; 		Vx400 = 180; 		Vx401 = 0; 		Vx402 = 0; 		Vx403 = 120; 		Vx404 = 360; 		Vx405 = 0; 		Vx455     = 13; 		Vx29     = 0; 		Vx476     = 0; 		Vx474     = 0; 			} 	if (Vx360 == 0) { 		Vx447 = PS4_CIRCLE; 		Vx448 = PS4_CROSS; 		Vx449 = PS4_L1; 		Vx450 = PS4_R1; 		Vx451 = PS4_L2; 		Vx452 = PS4_R2; 		Vx453 = PS4_SQUARE; 		Vx454 = PS4_TRIANGLE; 			} 	else if (Vx360 == 1) { 		Vx447      = PS4_SQUARE; 		Vx448      = PS4_CROSS ; 		Vx449    = PS4_L1    ; 		Vx450  = PS4_R1; 		Vx451    = PS4_L2; 		Vx452    = PS4_R2; 		Vx453     = PS4_CIRCLE; 		Vx454  = PS4_TRIANGLE; 	} 	else if (Vx360 == 2) { 		Vx447 = Vx1395[Vx439]; 		Vx448 = Vx1395[Vx440]; 		Vx449 = Vx1395[Vx441]; 		Vx450 = Vx1395[Vx442]; 		Vx451 = Vx1395[Vx443]; 		Vx452 = Vx1395[Vx444]; 		Vx453 = Vx1395[Vx445]; 		Vx454 = Vx1395[Vx446]; 			} 	} function Vx173() { 	Vx174(); 	Vx197(   1,0,     1); 	Vx197(Vx365, 0, 6); 	Vx197(Vx362, 0, 3); 	Vx197(Vx363, 0 , 1); 	Vx197(Vx364, 0 , 1); 	Vx197(Vx275, 0, 70); 	Vx197(Vx276, 0, 70); 	Vx197(Vx277, 0, 70); 	Vx197(Vx278, 0, 70); 	Vx197(Vx279, 0, 22); 	Vx197(Vx366, 0, 70); 	Vx197(Vx367, 0, 70); 	Vx197(Vx368, 0, 70); 	Vx197(Vx369, 0, 70); 	Vx197(Vx370, 0, 1); 	Vx197(Vx371, 0, 1); 	Vx197(Vx372, 0, 1); 	Vx197(Vx373, 0, 1); 	Vx197(Vx381, 0, 1); 	Vx197(Vx407, 0, 70); 	Vx197(Vx408, 0, 70); 	Vx197(Vx409, 0, 70); 	Vx197(Vx410, 0, 70); 	Vx197(Vx411, 0, 70); 	Vx197(Vx412, 1, 25); 	Vx197(Vx413, 0, 1); 	Vx197(Vx414, 0, 1); 	Vx197(Vx415, 1, 25); 	Vx197(Vx416, 0, 1); 	Vx197(Vx417, 0, 1); 	Vx197(Vx418, 0, 25); 	Vx197(Vx419, 0, 1); 	Vx197(Vx420, 0, 1); 	Vx197(Vx421, 1, 25); 	Vx197(Vx422, 0, 1); 	Vx197(Vx423, 0, 1); 	Vx197(Vx424, 350, 600); 	Vx197(Vx425, 350, 600); 	Vx197(Vx426, 0, 22); 	Vx197(Vx427, 0, 1); 	Vx197(Vx428, -100, 300); 	Vx197(Vx374, 0, 1); 	Vx197(Vx375, 0, 1); 	Vx197(Vx376, 0, 1); 	Vx197(Vx377, 0, 1); 	Vx197(Vx378, 0, 1); 	Vx197(Vx429, -150, 150); 	Vx197(Vx430, -150, 150); 	Vx197(Vx431, 0, 1); 	Vx197(Vx432, -150, 150); 	Vx197(Vx433, 0, 22); 	Vx197(Vx434, 0, 22); 	Vx197(Vx435, 0, 22); 	Vx197(Vx436, 0, 22); 	Vx197(Vx608, 60, 400); 	Vx197(Vx438, 0, 1); 	Vx197(Vx437, 0, 1); 	Vx197(Vx361, 0, 3); 	Vx197(Vx386, 0, 70); 	Vx197(Vx387, 0, 70); 	Vx197(Vx388, 0, 70); 	Vx197(Vx391, 0, 70); 	Vx197(Vx392, 0, 70); 	Vx197(Vx393, 0, 70); 	Vx197(Vx394, 0, 22); 	Vx197(Vx379, 0, 1); 	Vx197(Vx389, 0, 70); 	Vx197(Vx390, 0, 70); 	Vx197(Vx600, 0, 2500); 	Vx197(Vx1250, 0, 1); 	Vx197(Vx1243, 0, 1); 	Vx197(Vx114, 0, 10); 	Vx197(Vx406, 0, 1); 	Vx197(Vx360, 0, 2); 	Vx197(Vx439, 0, 9); 	Vx197(Vx440, 0, 9); 	Vx197(Vx441, 0, 9); 	Vx197(Vx442, 0, 9); 	Vx197(Vx443, 0, 9); 	Vx197(Vx444, 0, 9); 	Vx197(Vx445, 0, 9); 	Vx197(Vx446, 0, 9); 	Vx197(Vx382,    0, 1); 	Vx197(Vx383,    0, 1); 	Vx197(Vx384,     0, 1); 	Vx197(Vx395,     60, 500); 	Vx197(Vx396,     60, 500); 	Vx197(Vx397,    0, 1); 	Vx197(Vx398, 0, 1); 	Vx197(Vx399,     50, 250); 	Vx197(Vx400,     100, 850); 	Vx197(Vx401, 0, 1); 	Vx197(Vx402,    0, 1); 	Vx197(Vx403,        80, 500); 	Vx197(Vx404,        80, 500); 	Vx197(Vx405,       0, 1); 	Vx197(Vx455 ,         13,30); 	Vx197(Vx29,           0,1); 	Vx197(Vx476,           0,1); 	Vx197(Vx474,           0,1); 	} function Vx174() { 	Vx1008 = SPVAR_4; 	Vx1009 = 0; 	Vx1011 = 0; 	} int Vx1009,  Vx1008, Vx1011, Vx1012, Vx1013; function Vx175(Vx176) { 	Vx1012 = 0; 	while (Vx176) { 		Vx1012++; 		Vx176 = abs(Vx176 >> 1); 	} 	return Vx1012; 	} function Vx177(Vx178, Vx179) { 	Vx1012 = max(Vx175(Vx178), Vx175(Vx179)); 	if (Vx180(Vx178, Vx179)) { 		Vx1012++; 	} 	return Vx1012; 	} function Vx180(Vx178, Vx179) { 	return Vx178 < 0 || Vx179 < 0; 	} function Vx183(Vx184) { 	return 1 << clamp(Vx184 - 1, 0, 31); 	} function Vx185(Vx184) { 	if (Vx184 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - Vx184); } function Vx187(Vx184) { 	return Vx185(Vx184 - 1); 	} function Vx189(Vx176, Vx184) { 	if (Vx176 < 0) { 		return (abs(Vx176) & Vx187(Vx184)) | Vx183(Vx184); 	} 	return Vx176 & Vx187(Vx184); } function Vx192(Vx176, Vx184) { 	if (Vx176 & Vx183(Vx184)) { 		return 0 - (Vx176 & Vx187(Vx184)); 	} 	return Vx176 & Vx187(Vx184); } function Vx195(Vx196) { 	return get_pvar(Vx196, 0x80000000, 0x7FFFFFFF, 0); 	} function Vx197(Vx176, min, max) { 	Vx1013 = Vx177(min, max); 	Vx176 = clamp(Vx176, min, max); 	if (Vx180(min, max)) { 		Vx176 = Vx189(Vx176, Vx1013); 	} 	Vx176 = Vx176 & Vx185(Vx1013); 	if (Vx1013 >= 32 - Vx1009) { 		Vx1011 = Vx1011 | (Vx176 << Vx1009); 		set_pvar(Vx1008, Vx1011); 		Vx1008++; 		Vx1013 -= (32 - Vx1009); 		Vx176 = Vx176 >> (32 - Vx1009); 		Vx1009 = 0; 		Vx1011 = 0; 	} 	Vx1011 = Vx1011 | (Vx176 << Vx1009); 	Vx1009 += Vx1013; 	if (!Vx1009) { 		Vx1011 = 0; 	} 	set_pvar(Vx1008, Vx1011); } function Vx199(min, max, Vx200) { 	Vx1013 = Vx177(min, max); 	Vx1011 = (Vx195(Vx1008) >> Vx1009) & Vx185(Vx1013); 	if (Vx1013 >= 32 - Vx1009) { 		Vx1011 = (Vx1011 & Vx185(32 - Vx1009)) | ((Vx195(Vx1008 + 1) & Vx185(Vx1013 - (32 - Vx1009))) << (32 - Vx1009)); 	} 	Vx1009 += Vx1013; 	Vx1011 = Vx1011 & Vx185(Vx1013); 	if (Vx1009 >= 32) { 		Vx1008++; 		Vx1009 -= 32; 	} 	if (Vx180(min, max)) { 		Vx1011 = Vx192(Vx1011, Vx1013); 	} 	if (Vx1011 < min || Vx1011 > max) { 		return Vx200; 	} 		if(Vx202[289] != 7591){     Vx199(min, max, Vx200); 	} 	return Vx1011; 	} const string Vx1039 = "SETTINGS"; const string Vx1040 = "WAS SAVED"; combo Vx75 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	Vx173(); 	print(15, 2, OLED_FONT_MEDIUM, 1, Vx1039[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, Vx1040[0]); 	Vx1041 = 1500; 	combo_run(Vx76); 	} int Vx1041 = 1500; combo Vx76 { 	vm_tctrl(0);wait( Vx1041); 	cls_oled(0); 	Vx358 = FALSE; 	} define Vx1042 = 0; define Vx1043 = 1; define Vx1044 = 2; define Vx1045 = 3; define Vx1046 = 4; define Vx1047 = 5; define Vx1048 = 6; define Vx1049 = 7; int Vx686; int Vx1051; int Vx1052, Vx669; int Vx479; int Vx1055 = 49; int Vx1056 = 200; int Vx717 = TRUE; combo Vx77 { 	set_val(Vx448, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(Vx448, 0); 	vm_tctrl(0);wait( 120); 	if (Vx427) Vx245(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int Vx615; int Vx622; combo Vx78 { 	if (Vx622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (Vx622) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (Vx622) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(Vx447, 100); 	vm_tctrl(0);wait( Vx424); 	if (Vx622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	Vx615 = FALSE; 	Vx229(Vx615); 	} int Vx624; int Vx625; combo Vx79 { 	if (Vx625) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (Vx625) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(Vx453, 100); 	set_val(Vx450, 100); 	set_val(Vx451, 100); 	Vx1197 = 4000; 	vm_tctrl(0);wait( Vx425); 	vm_tctrl(0);wait( 50); 	set_val(Vx453, 100); 	vm_tctrl(0);wait( 50); 	Vx624 = FALSE; 	Vx229(Vx624); 	} int Vx1062 = TRUE; function Vx201(Vx202) { 	if (Vx202) { 		Vx1063 = Vx1095; 			} 	else { 		Vx1063 = Vx1094; 			} 	combo_run(Vx80); 	} int Vx1063; combo Vx80 { 	Vx222(Vx1063); 	vm_tctrl(0);wait( 300); 	Vx222(Vx1092); 	vm_tctrl(0);wait( 100); 	Vx222(Vx1063); 	vm_tctrl(0);wait( 300); 	Vx222(Vx1092); 	} define Vx1067 = 100; define Vx1068 = 130; const string Vx552 = "SCRIPT WAS"; function Vx203(Vx128, Vx205, Vx206) { 	if (!Vx352 && !Vx353) { 		cls_oled(0); 		print(Vx205, 3, OLED_FONT_MEDIUM, OLED_WHITE, Vx206); 		if (Vx128) { 			print(Vx207(sizeof(Vx1072) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, Vx1072[0]); 		} 		else { 			print(Vx207(sizeof(Vx1073) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, Vx1073[0]); 		} 		Vx201(Vx128); 			} 	} function Vx207(Vx143, Vx137) { 	return (OLED_WIDTH / 2) - ((Vx143 * Vx137) / 2); 	} const string Vx1073 = "OFF"; const string Vx1072 = "ON"; function Vx210(Vx134, Vx212, Vx128) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(Vx134, 0, OLED_FONT_MEDIUM, OLED_WHITE, Vx212); 	Vx217(Vx128, Vx220(Vx128)); 	Vx356 = TRUE; 	} const string Vx595 = "EA PING"; const string Vx617 = "FK_POWER"; const string Vx609 = "MaxFnshPwr"const string Vx601 = "JK_Agg"; int Vx600; int Vx608; function Vx214(Vx143, Vx137) { 	return (OLED_WIDTH / 2) - ((Vx143 * Vx137) / 2); 	} int Vx1082; int Vx1083, Vx1084; function Vx217(Vx128, Vx160) { 	Vx1082 = 1; 	Vx1084 = 10000; 	if (Vx128 < 0) { 		putc_oled(Vx1082, 45); 		Vx1082 += 1; 		Vx128 = abs(Vx128); 			} 	for (Vx1083 = 5; 	Vx1083 >= 1; 	Vx1083--) { 		if (Vx160 >= Vx1083) { 			putc_oled(Vx1082, (Vx128 / Vx1084) + 48); 			Vx128 %= Vx1084; 			Vx1082++; 			if (Vx1083 == 4) { 				putc_oled(Vx1082, 44); 				Vx1082++; 							} 					} 		Vx1084 /= 10; 			} 	puts_oled(Vx214(Vx1082 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, Vx1082 - 1, OLED_WHITE); 	} int Vx1088; function Vx220(Vx221) { 	Vx1088 = 0; 	do { 		Vx221 /= 10; 		Vx1088++; 			} 	while (Vx221); 	return Vx1088; 	} int Vx631; define Vx1092 = 0; define Vx1093 = 1; define Vx1094 = 2; define Vx1095 = 3; define Vx1096 = 4; define Vx1097 = 5; define Vx1098 = 6; define Vx1099 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int Vx1100; function Vx222(Vx223) { 	for (Vx1100 = 0; 	Vx1100 < 3; 	Vx1100++) { 		set_rgb(data[Vx223][0], data[Vx223][1], data[Vx223][2]); 			} 	} const int8 Vx1405[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int Vx634 = PS4_L3; define Vx1102 = 1; define Vx1103 = 2; define Vx1104 = 3; define Vx1105 = 2; define Vx1106 = 3; define Vx1107 = 4; define Vx1108 = 5; define Vx1109 = 6; define Vx1110 = 7; define Vx1111 = 8; define Vx1112 = 9; int Vx628 = FALSE; int Vx1114; int Vx1115; int Vx1116; int Vx1117; define Vx1118 = PS4_LX; define Vx1119 = PS4_LY; define Vx1120 = PS4_RX; define Vx1121 = PS4_RY; function Vx224 () { if(Vx362 == 3){ 		if( get_ival(PS4_RY) < -70  && !Vx1114 && !combo_running(Vx0) ) { 			Vx1114 = TRUE; 			Vx479 = FALSE; 			Vx1051 = Vx275; 			            Vx227(Vx275); 		} 		if( get_ival(PS4_RY) >  70  && !Vx1115 && !combo_running(Vx0)) { 			Vx1115 = TRUE; 			Vx479 = TRUE; 			Vx1051 = Vx277; 			           Vx227(Vx277); 		} 		if( get_ival(PS4_RX) < -70  && !Vx1116 && !combo_running(Vx0) ) { 			Vx1116 = TRUE; 			Vx479 = FALSE; 			Vx1051 = Vx278; 			              Vx227(Vx278); 		} 		if( get_ival(PS4_RX) >  70  && !Vx1117 && !combo_running(Vx0) ) { 			Vx1117 = TRUE; 			Vx479 = TRUE; 			Vx1051 = Vx276; 			            Vx227(Vx276); 		} 			set_val(Vx1120,0);              set_val(Vx1121,0);  			} 	else if(Vx362 < 3 && !get_ival(XB1_RS) &&  !get_ival(Vx451) && !get_ival(Vx452) && !get_ival(Vx450)) { 		if( get_ival(PS4_RY) < -70  && !Vx1114 && !combo_running(Vx0) ) { 			Vx1114 = TRUE; 			Vx479 = FALSE; 			Vx1051 = Vx275; 			            Vx227(Vx275); 		} 		if( get_ival(PS4_RY) >  70  && !Vx1115 && !combo_running(Vx0)) { 			Vx1115 = TRUE; 			Vx479 = TRUE; 			Vx1051 = Vx277; 			           Vx227(Vx277); 		} 		if( get_ival(PS4_RX) < -70  && !Vx1116 && !combo_running(Vx0) ) { 			Vx1116 = TRUE; 			Vx479 = FALSE; 			Vx1051 = Vx278; 			              Vx227(Vx278); 		} 		if( get_ival(PS4_RX) >  70  && !Vx1117 && !combo_running(Vx0) ) { 			Vx1117 = TRUE; 			Vx479 = TRUE; 			Vx1051 = Vx276; 			            Vx227(Vx276); 		} 			set_val(Vx1120,0);              set_val(Vx1121,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		Vx1114 = 0; 		Vx1115  = 0; 		Vx1116  = 0; 		Vx1117  = 0; 			} 	} function Vx225() { 	if (Vx502 == Vx593) { 		Vx479 = FALSE; 		if (Vx386) Vx227(Vx386); 			} 	if (Vx502 == Vx232(Vx593 + 4)) { 		Vx479 = FALSE; 		if (Vx393) Vx227(Vx393); 			} 	if (Vx502 == Vx232(Vx593 + 1)) { 		Vx479 = TRUE; 		if (Vx388) Vx227(Vx388); 			} 	if (Vx502 == Vx232(Vx593 - 1)) { 		Vx479 = FALSE; 		if (Vx387) Vx227(Vx387); 			} 	if (Vx502 == Vx232(Vx593 + 2)) { 		Vx479 = TRUE; 		if (Vx390) Vx227(Vx390); 			} 	if (Vx502 == Vx232(Vx593 - 2)) { 		Vx479 = FALSE; 		if (Vx389) Vx227(Vx389); 			} 	if (Vx502 == Vx232(Vx593 + 3)) { 		Vx479 = TRUE; 		if (Vx392) Vx227(Vx392); 			} 	if (Vx502 == Vx232(Vx593 - 3)) { 		Vx479 = FALSE; 		if (Vx391) Vx227(Vx391); 			} 	} int Vx1139; int Vx500 = 0; function Vx226() { 	if(Vx1139){ 		Vx500 += get_rtime(); 			} 	if(Vx500 >= 3000){ 		Vx500 = 0; 		Vx1139 = FALSE; 			} 	if(Vx361 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !Vx503 && !combo_running(Vx0)) { 			Vx503 = TRUE; 			Vx1139 = TRUE; 			Vx500 = 0; 			Vx502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			Vx225(); 					} 		set_val(Vx1120, 0); 		set_val(Vx1121, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(Vx451) && !get_ival(Vx452) && !get_ival(Vx450) && !get_ival(Vx449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !Vx503 && !combo_running(Vx0)) { 			Vx503 = TRUE; 			Vx1139 = TRUE; 			Vx500 = 0; 			Vx502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			Vx225(); 					} 		set_val(Vx1120, 0); 		set_val(Vx1121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		Vx503 = FALSE; 			} 	} function Vx227(Vx228) { 	Vx1051 = Vx228; 	Vx202[-339 + (Vx228 * 3)] = TRUE; 	Vx717 = FALSE; 	block = TRUE; 	if (Vx114 > 7)vm_tctrl(0); 	} int Vx1147; combo Vx81 { 	set_rumble(Vx1147, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function Vx229(Vx128) { 	if (Vx128) Vx1147 = RUMBLE_A; 	else Vx1147 = RUMBLE_B; 	combo_run(Vx81); 	} int Vx1148 = 300; int Vx1149 ; combo Vx82 { 	Vx1149 = TRUE; 	vm_tctrl(0);wait( Vx1148); 	Vx1149 = FALSE; 	} combo Vx83 { 	Vx231(); 	Vx239(0, 0); 	vm_tctrl(0);wait( 20); 	Vx239(0, 0); 	vm_tctrl(0);wait( 100); 	Vx239(0, 0); 	set_val(Vx452, 100); 	Vx239(0, 0); 	vm_tctrl(0);wait( 60); 	Vx239(0, 0); 	vm_tctrl(0);wait( 150); 	Vx717 = TRUE; 	vm_tctrl(0);wait( 350); 	} function Vx231() { 	Vx686 = Vx593  Vx234(Vx686); 	Vx1052 = Vx1153; 	Vx669 = Vx672; 	} combo Vx84 { 	set_val(Vx451, 100); 	set_val(Vx450, 100); 	vm_tctrl(0);wait( 100); 	set_val(Vx451, 100); 	vm_tctrl(0);wait( 100); 	Vx717 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 Vx1406[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int Vx1153, Vx672, Vx593; int Vx502; int Vx503; int Vx1158; function Vx232(Vx233) { 	Vx1158 = Vx233; 	if (Vx1158 < 0) Vx1158 = 8 - abs(Vx233); 	else if (Vx1158 >= 8) Vx1158 = Vx233 - 8  return Vx1158; 	} function Vx234(Vx235) { 	if (Vx235 < 0) Vx235 = 8 - abs(Vx235); 	else if (Vx235 >= 8) Vx235 = Vx235 - 8; 	Vx1153 = Vx1406[Vx235][0]; 	Vx672 = Vx1406[Vx235][1]; } function Vx236(Vx237, Vx238) { 	set_val(Vx1120, Vx237); 	set_val(Vx1121, Vx238); 	} function Vx239(Vx240, Vx241) { 	set_val(Vx1118, Vx240); 	set_val(Vx1119, Vx241); 	} function Vx242() { 	if (Vx479) { 		set_val(Vx1118, inv(Vx669)); 		set_val(Vx1119, Vx1052); 			} 	else { 		set_val(Vx1118, Vx669); 		set_val(Vx1119, inv(Vx1052)); 			} 	} function Vx243() { 	if (Vx479) { 		set_val(Vx1120, inv(Vx669)); 		set_val(Vx1121, Vx1052); 			} 	else { 		set_val(Vx1120, Vx669); 		set_val(Vx1121, inv(Vx1052)); 			} 	} function Vx244() { 	if (!Vx479) { 		set_val(Vx1120, inv(Vx669)); 		set_val(Vx1121, Vx1052); 			} 	else { 		set_val(Vx1120, Vx669); 		set_val(Vx1121, inv(Vx1052)); 			} 	} function Vx245() { 	set_val(Vx1120, Vx1052); 	set_val(Vx1121, Vx669); 	} function Vx246() { 	set_val(Vx1120, inv(Vx1052)); 	set_val(Vx1121, inv(Vx669)); 	} function Vx247() { 	set_val(Vx1120, 0); 	set_val(Vx1121, 0); 	} int Vx1174; function Vx248() { 	if ((event_press(Vx448)  ) && !combo_running(Vx85) && (Vx1197  <= 0 || (Vx1197 < 3000 && Vx1197 > 1  )) && !get_ival(Vx452) && Vx555 > 500 &&!get_ival(Vx451) &&!get_ival(Vx447) &&!get_ival(Vx450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(Vx85) ) { 		combo_run(Vx85); 			} 	if (combo_running(Vx85) && (        get_ival(Vx452) ||        get_ival(Vx451) ||        get_ival(Vx447) ||        get_ival(Vx450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(Vx85); 		Vx1329 = TRUE; 			} 	} combo Vx85 { vm_tctrl(0);wait(750); set_val(Vx449,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(Vx1174 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo Vx86 { 	vm_tctrl(0);wait( 800); 	Vx1198 = 0; 	} int Vx1175 = 1600; int Vx1176 = 1600; int Vx1177 = 1600; int Vx1178 = TRUE; int Vx1179 = TRUE; int Vx664 = FALSE; int Vx1181 = TRUE; int Vx658 = FALSE; int Vx1183 = TRUE; int Vx660 = FALSE; int Vx1185 = TRUE; int Vx662 = FALSE; function Vx249(){ 	if (get_ival(Vx449)) { 		Vx1187 = 1000; 		Vx1206 = 0; 		Vx555 = 1; 		combo_stop(Vx95); 			} 	if (event_press(Vx451) || event_press(Vx434)) { 		Vx1187 = 4000; 		Vx1206 = 0; 		Vx1175 = 1600; 			} 	if (get_ival(Vx450) && !get_ival(Vx449) ) { 		Vx1187 = 0; 		Vx1206 = 0; 		Vx1175 = 1600; 			} 	else if (get_ival(Vx449)){ 		Vx1187 = 1000; 			} 	if (Vx1187 > 0) { 		Vx1187 -= get_rtime(); 			} 	if (Vx1187 < 0) { 		Vx1187 = 0; 			} 	Vx1266 = Vx428; 	if (event_release(Vx448)) { 		Vx1193 = 1; 		Vx1206 = 0; 		Vx555 = 1; 	} 	if (event_release(Vx454)) { 		Vx1194 = 1; 		Vx1206 = 0; 		Vx555 = 1; 	} 	if (event_release(Vx449)) { 		Vx1176 = 1; 		Vx1206 = 0; 		Vx1175 = 1600; 			} 	if (event_release(Vx450)) { 		Vx1177 = 1; 		Vx1206 = 0; 		Vx1175 = 1600; 			} 	if (event_release(Vx453) || (get_ival(Vx454) && get_ival(Vx449))) { 		Vx1197 = 4000; 		Vx1206 = 0; 	} 	if (get_ival(Vx448) && Vx1197 < 4000 && Vx1197 > 3500) { 		Vx1198 = Vx1197; 		Vx1197 = 0; 			} 	if (Vx1175 < 1510) { 		Vx1175 += get_rtime(); 			} 	if (Vx1176 < 1600) { 		Vx1176 += get_rtime(); 			} 	if (Vx1177 < 1600) { 		Vx1177 += get_rtime(); 			} 	if (Vx1197 > 0) { 		Vx1197 -= get_rtime(); 			} 	if (Vx1197 < 0) { 		Vx1197 = 0; 			} 	if (Vx1193 < 5100) { 		Vx1193 += get_rtime(); 			} 	if (Vx1194 < 4100) { 		Vx1194 += get_rtime(); 			} 	if (Vx1206 > 0) { 		Vx1206 -= get_rtime(); 			} 	if (Vx1206 < 0) { 		Vx1206 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		Vx1175 = 1; 		Vx1206 = 0; 			} 	if (combo_running(Vx92)) { 		set_val(Vx448, 0); 		if(get_ival(Vx448)){ 			Vx1209 = 0; 			combo_stop(Vx87); 			set_val(Vx448, 0); 			combo_stop(Vx92); 			combo_run(Vx49); 					} 			} 	if ((combo_running(Vx97) || combo_running(Vx88))) { 		set_val(Vx448, 0); 		if(get_ival(Vx448)){ 			Vx555 = 1; 			Vx1209 = 0; 			combo_stop(Vx87); 			set_val(Vx448, 0); 			combo_stop(Vx97); 			combo_stop(Vx88); 			combo_run(Vx49); 					} 			} 	if (event_press(Vx447)) { 		combo_run(Vx86); 			} 	if (Vx555 > 1500) { 		if (Vx1176 < 1500) { 			Vx1211 = 120; 					} 		if (Vx1177 < 1500) { 			Vx1211 = 228; 					} 		else { 			Vx1211 = 200; 					} 			} 	if (Vx555 < 1500) { 		Vx1211 = 450; 			} 	if (Vx555 > 2700) { 		Vx1215 = 920; 			} 	else if (Vx555 >= 0 && Vx555 < 2700) { 		Vx1215 = 725; 			} 	} function Vx250() { 	if (Vx1178) { 		if ((Vx555 <= 600 || (Vx1175 <= 1500 && Vx1175 > 1) || ( Vx1176 <= 150 || Vx1177 <= 150)) && event_press(Vx447) ) { 			if (!get_ival(Vx450) && !get_ival(Vx449) && !get_ival(Vx451) && !get_ival(Vx452)) { 				set_val(Vx447, 0); 				if (Vx1197 < 4000 && Vx1197 > 1) { 					set_val(Vx447, 0); 					combo_run(Vx90); 									} 				else { 					set_val(Vx447, 0); 					combo_run(Vx88); 					Vx1206 = 9000; 				} 							} 					} 			} 	if (Vx1185) { 		if (Vx555 > 1000 && !Vx1206 && (!get_ival(Vx450) && !get_ival(PS4_L3) && event_press(Vx447)) &&  Vx1176 > 150 &&  Vx1177 > 150) { 			if (!get_ival(Vx449) && !get_ival(Vx451)) { 				set_val(Vx447, 0); 				if (((Vx1194 > 1 && Vx1194 <= 2500) || (Vx1193 > 1 && Vx1193 <= 3000)) &&  Vx1175 != 1600) { 					set_val(Vx447, 0); 					combo_run(Vx89); 					Vx1206 = 9000; 									} 				else if (((Vx1194 > 2500 && Vx1194 <= 4000) || (Vx1193 > 3000 && Vx1193 <= 3500))  &&  Vx1175 != 1600) { 					set_val(Vx447, 0); 					combo_run(Vx88); 					Vx1206 = 9000; 									} 				else if ((Vx1197 < 4000 && Vx1197 > 1)) { 					set_val(Vx447, 0); 					combo_run(Vx90); 					Vx1206 = 9000; 									} 				else { 					set_val(Vx447, 0); 					Vx254(); 					Vx1206 = 9000; 									} 				Vx1206 = 9000; 							} 					} 			} 	if (Vx1179) { 		if (get_ival(Vx449) && get_ival(Vx450)) { 			if (!get_ival(Vx451) && !get_ival(Vx452) && (Vx1197 && Vx1193 > 1 && Vx1193 <= 1500) || (!Vx1197 && Vx1193 > 1 && Vx1193 <= 1500) || (Vx1193 > 1500 && !Vx1197) && !Vx1206) { 				if (event_press(Vx447)) { 					set_val(Vx447, 0); 					combo_run(Vx98); 					Vx1206 = 9000; 									} 							} 					} 			} 	if (Vx1183) { 		if (!get_ival(Vx452) && !get_ival(Vx449) && !get_ival(Vx450)) { 			if (get_ival(Vx451) && get_ival(Vx447)) { 				Vx255(); 				set_val(Vx447, 0); 				Vx1206 = 9000; 							} 					} 			} 	if (Vx1181) { 		if (get_ival(Vx450) && !get_ival(Vx449) && !Vx1187) { 			if (!get_ival(Vx451) && !get_ival(Vx452) && !Vx1206) { 				if (get_ival(Vx447) && Vx555 >= 1000) { 					set_val(Vx447, 0); 					combo_run(Vx95); 					Vx1206 = 9000; 									} 				if (get_ival(Vx447) && Vx555 < 1000 && !Vx1187) { 					set_val(Vx447, 0); 					combo_run(Vx96); 									} 							} 					} 			} 	if(combo_running(Vx90)){ 		Vx1209 = 0; 		combo_stop(Vx87)   	} 	if (get_ival(Vx449) || Vx1187 > 0) { 		combo_stop(Vx95); 		combo_stop(Vx97); 		combo_stop(Vx96); 			} 	if (combo_running(Vx88) || combo_running(Vx92) || combo_running(Vx97) || combo_running(Vx98) || combo_running(Vx95)) { 		if (get_ival(Vx448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(Vx452) > 30) { 			combo_stop(Vx92); 			combo_stop(Vx97); 			combo_stop(Vx98); 			combo_stop(Vx95); 			combo_stop(Vx88); 			Vx1209 = 0; 			combo_stop(Vx87)     		} 			} 	if (combo_running(Vx88) || combo_running(Vx89)) { 		if (get_ival(Vx448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(Vx452)) { 			combo_stop(Vx90); 			combo_stop(Vx89); 			combo_stop(Vx88); 			Vx1209 = 0; 			combo_stop(Vx87)     		} 			} 	if (event_press(Vx447) && Vx1206 > 100 && Vx1206 < 8990) { 		set_val(Vx447, 0); 		combo_stop(Vx92); 		combo_stop(Vx97); 		combo_stop(Vx98); 		combo_stop(Vx95); 		combo_stop(Vx88); 		Vx1209 = 0; 		combo_stop(Vx87)    combo_run(Vx91); 			} 	if (!Vx664) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(Vx98); 					} 			} 	if (!Vx658) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(Vx95); 					} 			} 	if (!Vx660) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(Vx92); 			Vx1209 = 0; 			combo_stop(Vx87)     		} 			} 	if (!Vx662) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(Vx97); 					} 			} 	if ((get_ival(Vx452) || get_ival(Vx448)) && !Vx365) { 		combo_stop(Vx4); 		combo_stop(Vx47); 		combo_stop(Vx33); 			} 	} define Vx1219 = 15; define Vx1220 = 15; int Vx1221 = 0; define Vx1222 = 8000; define Vx1223 = 4; define Vx1224 = 2000; int Vx1209 = 0; const int16 Vx1407[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 Vx1408[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int Vx1226 = FALSE; int Vx1227; int Vx1228; int Vx1229; int Vx1230; function Vx251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		Vx1229 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		Vx1226 = FALSE; 		for ( Vx978 = 0; 		Vx978 < sizeof(Vx1408) / sizeof(Vx1408[0]); 		Vx978++) { 			if (Vx1229 == Vx1408[Vx978]) { 				Vx1226 = TRUE; 				break; 							} 					} 		if (!Vx1226) { 			Vx1227 = Vx1408[0]; 			Vx1228 = abs(Vx1229 - Vx1408[0]); 			for ( Vx978 = 1; 			Vx978 < sizeof(Vx1408) / sizeof(Vx1408[0]); 			Vx978++) { 				Vx1230 = abs(Vx1229 - Vx1408[Vx978]); 				if (Vx1230 < Vx1228) { 					Vx1227 = Vx1408[Vx978]; 					Vx1228 = Vx1230; 									} 							} 			set_polar(POLAR_LS, Vx1227, 32767); 					} 			} } function Vx252 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		Vx1229 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		Vx1226 = FALSE; 		for ( Vx978 = 0; 		Vx978 < sizeof(Vx1407) / sizeof(Vx1407[0]); 		Vx978++) { 			if (Vx1229 == Vx1407[Vx978]) { 				Vx1226 = TRUE; 				break; 							} 					} 		if (!Vx1226) { 			Vx1227 = Vx1407[0]; 			Vx1228 = abs(Vx1229 - Vx1407[0]); 			for ( Vx978 = 1; 			Vx978 < sizeof(Vx1407) / sizeof(Vx1407[0]); 			Vx978++) { 				Vx1230 = abs(Vx1229 - Vx1407[Vx978]); 				if (Vx1230 < Vx1228) { 					Vx1227 = Vx1407[Vx978]; 					Vx1228 = Vx1230; 									} 							} 			set_polar(POLAR_LS, Vx1227, 32767); 					} 			} } int Vx1243; function Vx253() { 	if (combo_running(Vx87) && ( event_press(Vx447)    ||   get_ival(Vx452) ||         get_ival(Vx448) ||        get_ival(Vx453) ||        get_ival(Vx454) ||        get_ival(Vx449)      )) { 		combo_stop(Vx87); 		Vx1209 = 0; 			} 	if (Vx1221 == 0) { 		if ( ( Vx1197 == 0 && !combo_running(Vx90) && !combo_running(Vx98) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(Vx447) || (Vx1209 == 1 ||     combo_running(Vx96) || combo_running(Vx94)|| combo_running(Vx92) ||  combo_running(Vx95)     || combo_running(Vx88) || combo_running(Vx97) || combo_running(Vx89) ||  combo_running(Vx91)  ))     ) { 			if(Vx437)Vx252 (); 			else if (Vx438)Vx251 (); 			combo_stop(Vx103); 			combo_run(Vx87); 					} 			} 	else{ 		combo_stop(Vx87); 			} 	} combo Vx87 { 	if(Vx437)Vx252 (); 	else if (Vx438)Vx251 (); 	combo_stop(Vx103); 	vm_tctrl(0); 	wait(4000); 	Vx1209 = 0; 	} combo Vx88 { 	set_val(Vx449,0); 	set_val(Vx447, 100); 	vm_tctrl(0);wait( random(210, 215) + Vx430); 	set_val(Vx447, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function Vx254() { 	if (Vx555 > 600 && Vx555 <= 800) { 		Vx1244 = 240; 			} 	if (Vx555 > 800 && Vx555 <= 1000) { 		Vx1244 = 230; 			} 	if (Vx555 > 1000 && Vx555 <= 1500) { 		Vx1244 = 225; 			} 	if (Vx555 > 1500 && Vx555 <= 2000) { 		Vx1244 = 235; 			} 	if (Vx555 > 2000) { 		Vx1244 = 218; 			} 	combo_run(Vx97); 	} combo Vx89 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 500); 	} combo Vx90 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait( 205); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 300); 	} combo Vx91 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait( 190); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 400); 	} int Vx1249; int Vx1250; int Vx29; int Vx476; int Vx474; int Vx1254; int Vx1255; combo Vx92 { 	if (Vx1250) { 		set_val(Vx447, 0); 		Vx1254 = 350; 			} 	else { 		Vx1254 = 0; 			} 	if (Vx1250) { 		Vx243(); 		Vx239(0, 0); 			} 	vm_tctrl(0); 	wait(Vx1254); 	if (Vx1250) { 		set_val(Vx450, 0); 		Vx1254 = 60; 			} 	else { 		set_val(Vx451, 0); 		Vx1254 = 60; 			} 	set_val(Vx447,0); 	vm_tctrl(0);wait(Vx1254); 	set_val(Vx451, 0); 	set_val(Vx450, 0); 	set_val(Vx447,0); 	vm_tctrl(0);wait(Vx1254); 	if (Vx1250) { 		Vx1254 = 0; 			} 	else { 		Vx1254 = 60; 			} 	set_val(Vx450, 0); 	set_val(Vx451, 0); 	set_val(Vx447,0); 	vm_tctrl(0);wait(Vx1254); 	set_val(Vx447, 100); 	set_val(Vx451, 100); 	vm_tctrl(0);wait(random(265, 268) +   Vx429 ); 	set_val(Vx451, 100); 	set_val(Vx447, 0); 	if (Vx1250) { 		Vx1254 = 15; 			} 	else { 		Vx1254 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + Vx1267 + Vx1266 + Vx1254 ); 	set_val(Vx451, 100); 	set_val(Vx447, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(Vx447, 0); 	set_val(Vx451, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(Vx451, 100); 	vm_tctrl(0);wait(2500); 	} int Vx1194; int Vx1197; int Vx555; int Vx1193; int Vx1266; int Vx1267 = 111; int Vx1187; int Vx1198; int Vx1270; function Vx255() { 	Vx1270 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((Vx1270 > 5 && Vx1270 < 40) ) {         Vx479 = FALSE; 			} 	if ((Vx1270 > 40 && Vx1270 < 85)){ 		Vx479 = TRUE; 		    } 	if ((Vx1270 > 95 && Vx1270 < 130) ) { 		Vx479 = FALSE; 		    } 	 if((Vx1270 > 130 && Vx1270 < 175)) { 		Vx479 = TRUE; 		} 			if ((Vx1270 > 185 && Vx1270 < 220) ) {        Vx479 = FALSE; 			} 	if ((Vx1270 > 220 && Vx1270 < 265)){        Vx479 = TRUE; 		    } 	if ((Vx1270 > 275 && Vx1270 < 310) ) {        Vx479 = FALSE; 		    } 	 if((Vx1270 > 310 && Vx1270 < 355)) { 		Vx479 = TRUE; 		} 	if (Vx1197 == 0 && (Vx555 >= 750 || ((Vx1198 > 3000 && Vx1193 > 1 && Vx1193 < 5000)))) { 		if (Vx555 <= 2000 && Vx1193 > 1500) { 			set_val(Vx447, 0); 			Vx1267 = 170; 		} 		if (Vx555 <= 2000 && Vx1193 > 1 && Vx1193 <= 1500) { 			set_val(Vx447, 0); 			Vx1267 = 202; 					} 		if (Vx555 > 2000 || (Vx1193 > 1 && Vx1193 <= 1500)) { 			set_val(Vx447, 0); 			Vx1267 = 151; 					} 		if ((Vx555 > 2000 && Vx1193 > 1500) || Vx1198 > 1 && Vx1193 > 1) { 			set_val(Vx447, 0); 			Vx1267 = 152; 					} 		if ((Vx555 < 2000 && Vx1193 > 1500) || Vx1197 > 1 && Vx1193 > 1) { 			set_val(Vx447, 0); 			Vx1267 = 149; 					} 		if (Vx1193 > 1500) { 			set_val(Vx447, 0); 			Vx1267 = 148; 					} 		if (!Vx555 > 2000 && Vx1198 > 1 && Vx1193 > 1 && Vx1193 <= 1500) { 			set_val(Vx447, 0); 			Vx1267 = 147; 					} 		set_val(Vx447, 0); 		combo_stop(Vx97); 		combo_stop(Vx98); 		combo_stop(Vx95); 		combo_stop(Vx88); 		combo_stop(Vx94); 		combo_stop(Vx91); 		combo_stop(Vx90); 		combo_run(Vx92); 			} 	else { 		if (Vx1197) { 			set_val(Vx447, 0); 			combo_run(Vx93); 					} 		else { 			if (Vx555 < 750) { 				set_val(Vx447, 0); 				combo_run(Vx94); 							} 					} 			} } combo Vx93 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(Vx447, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo Vx94 { 	set_val(Vx447, 100); 	vm_tctrl(0);wait( random(200, 205) + Vx429 )  set_val(Vx447, 0); 	vm_tctrl(0);wait( 700); 	} int Vx1280 = 246; int Vx1211 = 150; int Vx1282 = 0; combo Vx95 { 	set_val(Vx451, 100); 	set_val(Vx450, 0); 	set_val(Vx447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(Vx451, 0); 	set_val(Vx450, 0); 	set_val(Vx447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	Vx1282 = Vx428; 	set_val(Vx450, 100); 	set_val(Vx447,0); 	vm_tctrl(0);wait( 60); 	set_val(Vx450, 100); 	set_val(Vx447, 100); 	vm_tctrl(0);wait( Vx1280 + 10 + random(-2, 2) +  Vx432); 	set_val(Vx450, 100); 	set_val(Vx447, 0); 	Vx1249 = Vx1211; 	vm_tctrl(0);wait( Vx1211 + Vx1282 - 58 + Vx457 ); 	set_val(Vx450, 100); 	if(Vx1243)set_val(Vx447, 100); 	vm_tctrl(0);wait( 60); 	set_val(Vx450, 100); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 3000); 	} combo Vx96 { 	set_val(Vx450, 100); 	set_val(Vx447, 100); 	vm_tctrl(0);wait( 160 + Vx432 ); 	set_val(Vx450, 100); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 3000); 	} int Vx1206; int Vx1284 = 220; int Vx1244; int Vx1286 = 0; combo Vx97 { 	Vx1286 = Vx428; 	set_val(Vx447, 100); 	vm_tctrl(0);wait( Vx1284 + Vx430); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( Vx1244 + (Vx1286) + 22 + Vx458); 	if(Vx1243)set_val(Vx447, 100); 	vm_tctrl(0);wait( 60); 	set_val(Vx447, 0); 	vm_tctrl(0);wait( 2000); 	} int Vx1288 = TRUE; int Vx1215; int Vx1290 = 245; int Vx1291 = 0; combo Vx98 { 	set_val(Vx449, 100); 	set_val(Vx450, 100); 	if (Vx1288) { 		Vx1291 = Vx428; 			} 	else { 		Vx1291 = 0   	} 	set_val(Vx447, 100); 	vm_tctrl(0);wait( Vx1290); 	vm_tctrl(0);wait( Vx1215 + Vx1291 + 40)  } int Vx1294; int Vx1295 = 145; int Vx651; int Vx650; int Vx653; int Vx652; combo Vx99 { 	set_val(Vx454, 0); 	vm_tctrl(0);wait( 30); 	set_val(Vx454, 100); 	vm_tctrl(0);wait( 60); 	} int Vx654; int Vx655; combo Vx100 { 	set_val(Vx453, 100); 	vm_tctrl(0);wait( Vx655); 	} define Vx1302 = TRUE; define Vx1303 = 95; define Vx1304 = 10; define Vx1305 = 70; define Vx1306 = FALSE; define Vx1307 = 50; define Vx1308 = 95; define Vx1309 = XB1_LT; define Vx1310 = XB1_RT; define Vx1311 = XB1_LX; define Vx1312 = XB1_LY; define Vx1313 = POLAR_LS; int Vx1314; function Vx256() { 	if (    get_ival(Vx452) > 30 &&    (get_ival(Vx451) || get_ival(Vx448)) &&    (!get_ival(Vx453) || !get_ival(Vx447))  ) { set_val(Vx452, 0); 		if(!get_ival(Vx447)){ 			set_val(Vx451,100); 					} 		else{ 			set_val(Vx451,0); 					} 		  combo_run(Vx102); 		if(Vx431 == TRUE){ 			combo_run(Vx101); 					} 			} 	else { 		combo_stop(Vx102); 		combo_stop(Vx101); 			} 	} combo Vx101 { 	if (Vx431 == TRUE) { 		set_val(Vx450, 100); 		Vx1315 = 60; 			} 	else { 		Vx1315 = 0; 			} 	set_val(Vx451, 0); 	vm_tctrl(0);wait( Vx1315); 	if (Vx431 == TRUE) { 		set_val(Vx450, 0); 		Vx1315 = 60; 			} 	else { 		Vx1315 = 0; 			} 	set_val(Vx451, 0); 	vm_tctrl(0);wait( Vx1315); 	if (Vx431 == TRUE) { 		set_val(Vx450, 100); 			} 	vm_tctrl(0);wait( 750); 	vm_tctrl(0);wait( 750); 	} combo Vx102 { 	if(!get_ival(Vx447)){ 		set_val(Vx451,100); 			} 	else{ 		set_val(Vx451,0); 			} 	set_val(Vx452, 100); 	vm_tctrl(0);wait(Vx600); 	if(!get_ival(Vx447)){ 		set_val(Vx451,100); 			} 	else{ 		set_val(Vx451,0); 			}     set_val(Vx452, 0); 	vm_tctrl(0);wait(500); 	} int Vx1317; int Vx1315 ; function Vx257(Vx240) { return Vx259(Vx240 + 8192); } function Vx259(Vx240) {   Vx240 = (Vx240 % 32767) << 17;   if((Vx240 ^ (Vx240 * 2)) < 0) { Vx240 = (-2147483648) - Vx240; }   Vx240 = Vx240 >> 17;   return Vx240 * ((98304) - (Vx240 * Vx240) >> 11) >> 14; } int Vx1321, Vx1322; function Vx261(Vx121, Vx122, Vx123, Vx265){   Vx265 = (Vx265 * 32767) / 100;   Vx123 = (Vx123 * 32767) / 10000;   Vx122 = (360 - Vx122) * 91;   Vx1322 = Vx259(Vx122); Vx1321 = Vx257(Vx122);   Vx122 = 32767 - Vx257(abs(abs(Vx1322) - abs(Vx1321)));   Vx123 = Vx123 * (32767 - ((Vx122 * Vx265) >> 15)) >> 15;   set_val(42 + Vx121, clamp((Vx123 * Vx1321) >> 15, -32767, 32767));   set_val(43 + Vx121, clamp((Vx123 * Vx1322) >> 15, -32767, 32767));   return; } int Vx1327, Vx1328; int Vx1329 = TRUE; int Vx1330; int Vx1331; int Vx1332; int Vx1333; int Vx1334; function Vx266() {    if ( get_ival(Vx452) && (!get_ival(Vx451)) && Vx1335 <= 0 && Vx1336 <= 0 ) {           combo_stop(Vx103);         combo_stop(Vx104);    }    if (( get_ival(XB1_LS) || get_ival(XB1_RS)  || get_ival(Vx449)  || get_ival(Vx451) ||      get_ival(Vx448) || get_ival(Vx454) || get_ival(Vx447) || get_ival(Vx453)   || get_ival(XB1_PR1) ||     get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(Vx1120))> 20 || abs(get_ival(Vx1121))> 20) ) )){        combo_stop(Vx103);        combo_run(Vx104);    }    if ( get_val(Vx452)&&( (abs(get_ival(Vx1120))> 20 || abs(get_ival(Vx1121))> 20) )){    combo_stop(Vx105);    }    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(Vx451) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(Vx104)     && !get_ival(Vx449) &&  !get_ival(Vx452) && !get_ival(Vx448) && !get_ival(Vx454) && !get_ival(Vx447)){        combo_run(Vx103);    } } int Vx1335; int Vx1336; function Vx267(){   stickize(POLAR_RX, POLAR_RY, 32767);   Vx1327 = get_polar(POLAR_LS, POLAR_RADIUS);   Vx1328 = get_polar(POLAR_LS, POLAR_ANGLE);   Vx261(POLAR_LS, Vx1328, Vx1327, Vx455);   } combo Vx103 {    Vx267(); vm_tctrl(0); wait(400); vm_tctrl(0); wait(1000); 	} combo Vx104 { }  combo Vx105 { } combo Vx106{ } combo Vx107 { 	set_val(Vx448,100); 	vm_tctrl(0);wait( Vx651); 	set_val(Vx448,  0); 	vm_tctrl(0);wait( 30); 	if(Vx401){ 		set_val(Vx450,100); 			} 	vm_tctrl(0);wait( 60); 	} combo Vx108 { 	set_val(Vx450,  100); 	vm_tctrl(0);wait( 60);     wait( 60); 	} combo Vx109 { 	set_val(Vx454,100); 	vm_tctrl(0);wait( Vx653); 	set_val(Vx454,  0); 	vm_tctrl(0);wait( 30); 	if(Vx398){ 		set_val(Vx454,100); 			} 	vm_tctrl(0);wait( 60); 	} int Vx1001 int Vx1342 combo Vx110 { 	combo_suspend(Vx110) 	vm_tctrl(0);wait(361) } function Vx268 (){ 	if(Vx1001[Vx1342] != 361){ 	    Vx1342-- 	} 	else{ 		if(inv(Vx1342) != 297){ 			Vx268(); 		} 	} } int Vx1345; combo Vx111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int Vx176; function Vx269(Vx270, Vx271, Vx272) {   Vx176 = Vx124(Vx270, POLAR_RADIUS);   if(Vx271) {     if(Vx176 <= Vx271) Vx176 = (Vx176 * 5000) / Vx271;     else Vx176 = ((5000 * (Vx176 - Vx271)) / (10000 - Vx271)) + 5000;   }   if (Vx272) Vx176 = (Vx176 * Vx272) / 10000;   set_polar2(Vx270, Vx124(Vx270, POLAR_ANGLE), min(Vx176, 14142));   if (Vx270 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; } combo Vx112{ vm_tctrl(-7); wait(1000); }