define STICK_IDLE_TIMEOUT = 200;
define CYCLE_TIME_MS = 10;
define IDLE_CYCLES_THRESHOLD = STICK_IDLE_TIMEOUT / CYCLE_TIME_MS;
int last_lx = 0;
int last_ly = 0;
int idle_counter = 0;
int current_lx;
int current_ly;
int target_radius;

main {
    current_lx = get_val(XB1_LX);
    current_ly = get_val(XB1_LY);

    if (current_lx != last_lx || current_ly != last_ly) {
        idle_counter = 0;
        last_lx = current_lx;
        last_ly = current_ly;
    } else {
        if (idle_counter <= IDLE_CYCLES_THRESHOLD) {
             idle_counter = idle_counter + 1;
        }
    }
    
    if (get_val(XB1_LS)) {
        target_radius = 0;
        if (idle_counter <= IDLE_CYCLES_THRESHOLD) {
            target_radius = 9800;
        }
        set_polar2(POLAR_LS, get_polar(POLAR_LS, POLAR_ANGLE), target_radius);
        combo_run(Stop_my);
    }
}

combo Stop_my {
	set_val(XB1_RB, 100);
		wait(100);
	set_val(XB1_RB, 0);
		wait(100);
	//set_val(XB1_RT, 100);
		//wait(100);
        	wait(3000);
	//set_val(XB1_RT, 0);
}