int LRInterval = 9000;
int startDirection; // Variable to store the random starting direction
int randomSeed = 12345; // Initial seed value

init {
    // Initialize random seed using system time
    randomSeed = get_rtime();
}

main {
    if(get_val(XB1_LS)) {
        startDirection = getRandomRange(0, 1); // Random value: 0 or 1
        combo_run(aaabbbccc);
    }
}

// Function to generate a pseudo-random number
function getRandomNumber() {
    randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
    return randomSeed;
}

// Get random number between min and max
function getRandomRange(min, max) {
    return (getRandomNumber() % (max - min + 1)) + min;
}

combo aaabbbccc {
    if(startDirection == 0) {
        // Start with left (-45)
        set_polar(POLAR_LS, -90 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    } else {
        // Start with right (90)
        set_polar(POLAR_LS, 90 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    }
    wait(LRInterval);
    
    if(startDirection == 0) {
        set_polar(POLAR_LS, 90 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    } else {
        set_polar(POLAR_LS, -90 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    }
    wait(LRInterval);
}
