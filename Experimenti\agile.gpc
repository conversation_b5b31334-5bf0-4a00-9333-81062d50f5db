////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////
int angle, radius;

////////////////////////////////////////////////////////////////////////////////
// COMBOS
////////////////////////////////////////////////////////////////////////////////
combo LeftRightCombo {
    // 1) Linken Stick nach links
    set_val(XB1_LX, -100);
    wait(160);

    // 2) Linken Stick nach rechts
    set_val(XB1_LX, 100);
    wait(160);
}

////////////////////////////////////////////////////////////////////////////////
// MAIN
////////////////////////////////////////////////////////////////////////////////
main {
    // Polarisierungs-Werte abfragen
    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

    // Bedingung: LB gedrückt und Radius < 2000
    if(get_ival(XB1_LB) && (radius < 2000)) {
        // Beide Trigger auf 100 "gedrückt" setzen
        set_val(XB1_LT, 100);
        set_val(XB1_RT, 100);

        // Falls die Combo noch nicht läuft, starten
        if(!combo_running(LeftRightCombo)) {
            combo_run(LeftRightCombo);
        }
    } 
    else {
        // Sonst Combo stoppen und damit Stick-Bewegung beenden
        combo_stop(LeftRightCombo);
    }
}
