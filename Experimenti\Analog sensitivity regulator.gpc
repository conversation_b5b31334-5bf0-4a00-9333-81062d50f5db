// Global sensitivity value in percentage (0-100)
int sensitivityValue = 200;
main
{
  // Entry point: setting sensitivity for the LX axis
  _sensitivity(POLAR_LX, 16384, sensitivityValue * 32767 / 100);
  // Setting sensitivity for the LY axis
  _sensitivity(POLAR_LY, 16384, sensitivityValue * 32767 / 100);
}
int _val_s, _val;
function _sensitivity(id, mid, sen)
{
  // Get the current value for the axis and clamp it within valid range
  _val = clamp(get_val(id), -32767, 32766);

  // If the mid value is not used, adjust the value accordingly
  if (mid == NOT_USE)
  {
    _val_s = -1;
    // Determine axis direction (negative or positive)
    if (_val >= 0)
      _val_s = 1;
    _val *= _val_s;
    // Scale value when under the threshold
    if (_val <= mid)
      _val = (_val * 16384) / mid;
    else
      // Scale value for the upper portion beyond mid
      _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
    _val *= _val_s;
  }

  // Apply sensitivity adjustment if specified
  if (sen != NOT_USE)
  {
    _val = (_val * sen) >> 15;
  }

  // Set the adjusted value back after clamping it within the valid range
  set_val(id, clamp(_val, -32768, 32767));
}