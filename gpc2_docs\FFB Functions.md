# FFB Functions

ffb_set
ffb_set — Set FFB speed and duration

Description
void ffb_set(<FFB_IDENTIFIER>, fix32 speed, uint32 duration);
Set the speed and duration values of the FFB identified by <FFB_IDENTIFIER>.

Once ffb_set() is executed, the force feedback signals from the console will not be transmitted to the connected controllers until ffb_reset() is called.
ATTENTION: The ffb_set() should NOT be called on every interaction of main.
Force Feedback Map for Gamepads
FFB_1	Strong Engine
FFB_2	Weak Engine
FFB_3	Right Trigger 1)
FFB_4	Left Trigger 2)
Parameters
<FFB_IDENTIFIER>: An valid FFB identifier FFB_1, FFB_2, FFB_3 or FFB_4. Note: the FFB identifier is passed as direct parameter of the ffb_set() opcode, therefore stack values can't be used.
speed: The FFB speed in percentage, from 0.00 to 100.00 (%).
duration: How long, in milli-seconds, the FFB engine should be kept activated.
Return Value
No value is returned.

Examples
Example #1 ffb_set() example
main {
    if(event_active(BUTTON_16)) {
        ffb_set(FFB_1, 100.0, 500);
    }
}

ffb_get
ffb_get — Get FFB speed and duration

Description
fix32 ffb_get(<FFB_IDENTIFIER>, uint32 *duration);
Retrieve the speed and duration values of the FFB identified by <FFB_IDENTIFIER>.

If the speed and duration were programmatically set by ffb_set(), ffb_get() will return the values programmatically set.
Parameters
<FFB_IDENTIFIER>: An valid FFB identifier FFB_1, FFB_2, FFB_3 or FFB_4. Note: the FFB identifier is passed as direct parameter of the ffb_get() opcode, therefore stack values can't be used.
duration: Address of an uint32 variable to receive the duration value. This parameter can be NULL.
Return Value
The FFB speed in percentage, from 0.00 to 100.00 (%).

Examples
Example #1 ffb_get() example
main {
    if(ffb_get(FFB_2, NULL) > 0.0) {
        set_val(BUTTON_8, 100.0);
    }
}

ffb_get_actual
ffb_get_actual — Get actual FFB speed and duration

Description
fix32 ffb_get_actual(<FFB_IDENTIFIER>, uint32 *duration);
Retrieve the original speed and duration values of the FFB identified by <FFB_IDENTIFIER>.

ffb_get_actual() will always return the original values, even if ffb_set() is used to set an new speed and duration.
Parameters
<FFB_IDENTIFIER>: An valid FFB identifier FFB_1, FFB_2, FFB_3 or FFB_4. Note: the FFB identifier is passed as direct parameter of the ffb_get_actual() opcode, therefore stack values can't be used.
duration: Address of an uint32 variable to receive the duration value. This parameter can be NULL.
Return Value
The FFB speed in percentage, from 0.00 to 100.00 (%).

Examples
Example #1 ffb_get_actual() example
uint32 duration, duration_actual;
 
main {
    if(
        ffb_get(FFB_1, &duration) != ffb_get_actual(FFB_1, &duration_actual) || 
        duration != duration_actual
    ) {
        ffb_set(FFB_1, ffb_get_actual(FFB_1, NULL), duration_actual);
    }
}

ffb_reset
ffb_reset — Reset all FFBs speed and duration

Description
void ffb_reset();
Reset all FFBs speed and duration to the original values and restore the transmission of force feedback signals from the console to the connected controllers.

ATTENTION: The ffb_reset() should NOT be called on every interaction of main.
Return Value
No value is returned.

Examples
Example #1 ffb_reset() example
main {
    if(event_active(BUTTON_16)) {
        ffb_set(FFB_1, 100.0, 500);
    } else if(is_release(BUTTON_16)) {
        uint32 duration;
        ffb_get(FFB_1, &duration);
        if(duration == 0) {
            ffb_reset();
        }
    }
}