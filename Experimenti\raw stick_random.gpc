define RIGHT_MAG_THRESHOLD = 17;
// Random seed will be updated each frame
int randomSeed = 0;

int rxVal;
int ryVal;
int lxVal;
int lyVal;
int rightMagnitude;
int leftMagnitude;

int scaledRX;
int scaledRY;
int scaledLX;
int scaledLY;
int result;
int temp;
int targetMagnitude;

// Function to generate a pseudo-random number
function getRandomNumber() {
    randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
    return randomSeed;
}

// Get random number between min and max
function getRandomRange(min, max) {
    return (getRandomNumber() % (max - min + 1)) + min;
}

main {
    // Update random seed each frame using stick values to add entropy
    randomSeed = randomSeed + rxVal + ryVal + lxVal + lyVal;
    
    rxVal = get_val(XB1_RX);
    ryVal = get_val(XB1_RY);
    lxVal = get_val(XB1_LX);
    lyVal = get_val(XB1_LY);

    // Calculate magnitudes for both sticks
    rightMagnitude = sqrt(rxVal * rxVal + ryVal * ryVal);
    leftMagnitude = sqrt(lxVal * lxVal + lyVal * lyVal);

    // Get random target magnitude between 98 and 100
    targetMagnitude = getRandomRange(98, 100);

    // Handle right stick
    if(rightMagnitude >= RIGHT_MAG_THRESHOLD && rightMagnitude != 0) {
        scaledRX = (rxVal * targetMagnitude) / rightMagnitude;
        scaledRY = (ryVal * targetMagnitude) / rightMagnitude;
        set_val(XB1_RX, scaledRX);
        set_val(XB1_RY, scaledRY);
    }

    // Handle left stick
    if(leftMagnitude >= RIGHT_MAG_THRESHOLD && leftMagnitude != 0) {
        scaledLX = (lxVal * targetMagnitude) / leftMagnitude;
        scaledLY = (lyVal * targetMagnitude) / leftMagnitude;
        set_val(XB1_LX, scaledLX);
        set_val(XB1_LY, scaledLY);
    }
}

function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;
    } while (temp > result);
    
    return result;
}