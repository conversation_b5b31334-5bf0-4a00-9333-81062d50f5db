int oscillate_value = 75;
int direction = 1; // 1 for increasing, -1 for decreasing
int step_size = 5; // How much to change per update

main {
    // Check if RT is being pressed
    if(get_val(XB1_RT)) {
        if(get_val(XB1_RT) < 100) {
            // Oscillate between 50 and 100
            if(oscillate_value >= 100) {
                direction = -1;
            } else if(oscillate_value <= 50) {
                direction = 1;
            }
            
            oscillate_value = oscillate_value + (step_size * direction);
            set_val(XB1_RT, oscillate_value);
        } else {
            // Keep it at 100 if fully pressed
            set_val(XB1_RT, 100);
        }
    }
    
        // Check if RT is being pressed
    if(get_val(XB1_LT)) {
        if(get_val(XB1_LT) < 100) {
            // Oscillate between 50 and 100
            if(oscillate_value >= 100) {
                direction = -1;
            } else if(oscillate_value <= 50) {
                direction = 1;
            }
            
            oscillate_value = oscillate_value + (step_size * direction);
            set_val(XB1_LT, oscillate_value);
        } else {
            // Keep it at 100 if fully pressed
            set_val(XB1_LT, 100);
        }
    }
}
