// Rotating ellipse for analog stick

// Constants for stick identification
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Ellipse axis ratios (percentage of maximum)
define X_RATIO = 100;    // X-axis scale (100 = full range)
define Y_RATIO = 30;     // Y-axis scale (10% of full range)

// Rotation settings
define ANGLE_STEP = 8;   // Angle increment per frame

int x, y;
int current_angle;       // Track rotation angle

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Check if both triggers are pressed
    if (get_val(XB1_LT) && get_val(XB1_RT)) {
        // Update rotation angle
        current_angle = (current_angle + ANGLE_STEP) % 360;
        
        // Apply elliptical scaling and rotation in one step using set_polar
        set_polar(POLAR_LS, current_angle, 32767);
        x = get_val(stickX);
        y = get_val(stickY);
        
        // Apply elliptical scaling
        x = (x * X_RATIO) / 100;
        y = (y * Y_RATIO) / 100;
        
        // Set final values
        set_val(stickX, x);
        set_val(stickY, y);
    } else if (abs(x) > 0 || abs(y) > 0) {
        // Original elliptical scaling when triggers not pressed
        x = (x * X_RATIO) / 100;
        y = (y * Y_RATIO) / 100;
        
        set_val(stickX, x);
        set_val(stickY, y);
    }
}
