define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_LB;     
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;   

main {

//===============================================
//    MIRZA L1 + TRIANGLE TROUGH PASSES  MIN / MAX
//===============================================
    if(!get_val(PaceCtrol)){    
	    if(get_val(XB1_PL2)){
	    	fly_trough_pass_timer += get_rtime();
	    }
	    if(event_release(XB1_PL2)){
		    	if(fly_trough_pass_timer < fly_Trough_Pass_MIN){
		    		fly_TroughP_difference = fly_Trough_Pass_MIN - fly_trough_pass_timer;
		    		
		    		combo_run(fly_Trough_Pass_MIN_cmb);
		    	}else{
		    		if(fly_DoubleTapTroughPass) combo_run(fly_DOUBLE_TAP_TROUGH_cmb);
		    	}
	    	fly_trough_pass_timer = 0;
	    }
   } // PaceCtrol 

}


   //=================================
int fly_trough_pass_timer; 
int fly_Trough_Pass_MIN = 80;
int fly_Trough_Pass_MAX = 300;
int fly_TroughP_difference;
int fly_DoubleTapTroughPass = TRUE;
int trough_start;

combo fly_Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	set_val(PlayerRun,100);
	wait(fly_TroughP_difference);
	if(fly_DoubleTapTroughPass){
		set_val(ThroughBall,100);
		set_val(PlayerRun,100);
	}
	wait(60);
}
combo fly_DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}