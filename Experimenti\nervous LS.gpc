// Define constants for analog stick radius and aim assist parameters
define MAX_RADIUS = 32767;          // Maximum possible analog stick radius
define REDUCED_RADIUS = 28000;      // Reduced radius for more controlled movement
define AA_Size = 400;                // Aim Assist size parameter
define AA_Interval = 150;             // Interval for aim assist movements

// Variable to store left stick angle
int lsAngle;

// Variables to manage analog stick radius
int radius;
int max_allowed_radius;

main {
  // Activate Aim Assist Abuse combo when R2 is pressed
  if(get_ival(XB1_RS)) {
  set_val(XB1_RS,0);
 set_val(XB1_RT,100);set_val(XB1_LT,100);
    combo_run(AIM_ASSIST_ABUSE);
     }
  else combo_stop(AIM_ASSIST_ABUSE);

  // Call pass function to modify analog stick behavior
  pass();
}

// Function to modify analog stick input behavior
function pass() {
    // Store the current analog stick radius to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
    // Adjust maximum allowed radius based on trigger input
    // Full radius when LT or RT are pressed, reduced radius otherwise
    if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
        max_allowed_radius = MAX_RADIUS;
    } else {
        max_allowed_radius = REDUCED_RADIUS;
    }
    
    // Set analog stick position with:
    // 1. Quantized angle to reduce precision
    // 2. Limited radius based on current input and trigger state
    set_polar(POLAR_LS, 
        quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
        min(calculate_radius(), max_allowed_radius)
    );
}

// Define angle interval for quantization (divides circle into 16 zones)
define AngleInterval_2 = 16;

// Function to quantize analog stick angle into discrete zones
// This reduces the precision of analog stick movement
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

// Calculate the radius of analog stick input using Pythagorean theorem
function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2));
}

// Aim Assist Abuse combo: rapidly changes analog stick angle
// Potentially used to manipulate aim assist mechanics in games
combo AIM_ASSIST_ABUSE {
   // Quickly move analog stick to 45-degree angle
   set_polar(POLAR_LS, 45 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
   wait(AA_Interval);
   
   // Quickly move analog stick to 315-degree angle
   set_polar(POLAR_LS, 315 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
   wait(AA_Interval);
}