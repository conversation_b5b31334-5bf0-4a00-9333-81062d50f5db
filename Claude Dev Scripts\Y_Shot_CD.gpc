// Script: Button Press Duration Handler
// Description: This script handles the duration of button presses for the Y button on an Xbox controller,
// with specific behavior when the left bumper (LB) is held down.

// Constants for timing thresholds (in milliseconds)
define MIN_PRESS_TIME = 99
define MAX_PRESS_TIME = 300

int press_time = 0;

main {
    // Check if the left bumper is held down
    //if (get_val(XB1_LB)) {
        Y_shot();
   // }
}

// Function to handle Y button pressshot_CD
function Y_shot() {
    // Check if Y button is currently pressed
    if (get_val(XB1_Y)) {
        // Get the current press time of the Y button
        press_time = get_ptime(XB1_Y);
        
        // If press time exceeds MAX_PRESS_TIME, reset the button
        if (press_time > MAX_PRESS_TIME) {
            set_val(XB1_Y, 0);
            press_time = MAX_PRESS_TIME;
        }
    }
    
    // Check for the release of the Y button
    if (event_release(XB1_Y)) {
        // Get the actual press time of the Y button
        press_time = get_ptime(XB1_Y);
        
        // Adjust press time based on thresholds
        if (press_time < MIN_PRESS_TIME) {
            press_time = MIN_PRESS_TIME;
        } else if (press_time > MAX_PRESS_TIME) {
            press_time = MAX_PRESS_TIME;
        }
        
        // Use press_time for your desired action
        do_action(press_time);
    }
}

// Function to perform an action based on the press time
function do_action(int duration) {
    // Implement your desired action here
    // For example:
    if (duration == MIN_PRESS_TIME) {
        // Perform action for minimum press time
    } else if (duration == MAX_PRESS_TIME) {
        // Perform action for maximum press time
    } else {
        // Perform action for intermediate press times
    }
}