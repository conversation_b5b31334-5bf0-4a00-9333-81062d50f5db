// Configuration variables for the circular motion
int setFrequency = 10;    // Controls how fast the circular motion occurs
int radiusSize   = 35;    // Controls the size of the circular motion
int setTime = 1;          // Controls the time scaling factor
int angle, cosAngle, sinAngle, timeAdj;  // Variables for circular motion calculations
int rightStickMagnitude;

main {

    if(get_val(XB1_LS)){
    set_val(XB1_LS,0);
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        if(get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500) {
            set_val(POLAR_RX, get_val(POLAR_LX));
            set_val(POLAR_RY, get_val(POLAR_LY));
        }

    }

    if(get_ival(XB1_RS)) {
    set_val(XB1_RS, 0);
    // Calculate the current angle based on time and frequency
    // Uses modulo 0x8000 (32768) to keep angle within valid range
    angle = (timeAdj * (setFrequency * 327)) % 0x8000;
    
    // Increment time adjustment factor
    timeAdj += (setTime * 100);
    
    // Calculate X and Y components of the circular motion
    // radiusSize is scaled to match the stick's range (-32767 to 32767)
    // Uses fixed-point math with 15-bit precision
    cosAngle = ((radiusSize * 32767/100) * (icos(angle)) >> 15); 
    sinAngle = ((radiusSize * 32767/100) * (isin(angle)) >> 15);
    
    // Apply circular motion to the left stick input
    // Clamp values to valid range (-32767 to 32767)
    set_val(POLAR_LX, clamp(get_val(POLAR_LX) + cosAngle, -32767, 32767));
    set_val(POLAR_LY, clamp(get_val(POLAR_LY) + sinAngle, -32767, 32767));
    
    // Control bindings for adjusting parameters in real-time
    if(event_press(PS4_UP)) {    // Increase time scaling
        setTime++;
    }
    if(event_press(PS4_DOWN)) {  // Decrease time scaling
        setTime--;
    }
    if(event_press(PS4_RIGHT)) { // Increase frequency
        setFrequency++;
    }
    if(event_press(PS4_LEFT)) {  // Decrease frequency
        setFrequency--;
    }
    
    // Output current settings to trace variables for debugging
    set_val(TRACE_1, setFrequency);
    set_val(TRACE_2, setTime);
    }

}

// Custom cosine function using integer math
// Calculates cosine by shifting sine wave by quarter period (0x2000)
function icos(x) {
    return (isin((x + 0x2000) % 0x8000));
}

// Custom sine function using integer math
// Implements a fast approximation of sine using fixed-point arithmetic
// Input range: 0 to 0x8000 (0 to 2π scaled to integers)
// Output range: -32768 to 32767 (scaled sine values)
function isin(x) {
    // Shift left by 17 to prepare for fixed-point calculations
    x = x << 17;
    
    // Handle negative quadrants of the sine wave
    if((x ^ (x * 2)) < 0) {
        x = (-2147483648) - x;
    }
    
    // Shift back and apply polynomial approximation
    x = x >> 17;
    return (x * ((0x18000) - (x * x) >> 11) >> 14);
}