//const int8 anglesMax[] = {100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 97, 98, 98, 99, 100, 100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 96, 97, 98, 98, 99, 100};
const int8 anglesMax[] = {100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 97, 98, 100, 100, 100, 100, 100, 100, 100, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 96, 97, 98, 98, 99, 100};
int radius, angle;

main {

if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (2);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (3);
      }
      set_val(XB1_RIGHT,0);
	}

	if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(pressing);
    } else if(combo_running(pressing)) {
        combo_stop(pressing);
    }

	angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
	radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
	//set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle%45]));
	set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[(angle / 2) % 45]));
  
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
        combo_run(TapB);
    
    } else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {
        set_val(XB1_B,0);
        if (event_press(XB1_B)) combo_run(PressB);
    }
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100); //750
	wait(440);
    set_val(XB1_LB, 100); // Press XB1_LB
    wait(80);             // Wait for 80ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(40);             // Wait for 40ms pause
    set_val(XB1_LB, 100); // Press XB1_LB again
    wait(250);            // Wait for 150ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(250);  
}

combo PressB {
    set_val(XB1_B, 100);
    wait(100);
}

combo TapB {
    set_val(XB1_B, 0);
    wait(250);
} 