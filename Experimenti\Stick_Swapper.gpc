// Skriptname: Stick_Swapper
// Beschreibung:
// Dieses Skript ermöglicht das Tauschen der Eingaben der linken und rechten Analogsticks
// des Controllers, wenn die Taste `XB1_PL1` gedrückt wird. Dadurch werden die Bewegungen
// des linken Sticks auf den rechten Stick und umgekehrt übertragen.

// Hauptfunktion, die kontinuierlich ausgeführt wird
main {
    // Überprüft, ob die Taste `XB1_PL1` gedrückt wird
    if(get_key(XB1_PL1)) {
        // Tauscht die Y-Achse des linken Sticks mit der Y-Achse des rechten Sticks
        swap(XB1_LY, XB1_RY);
        
        // Tauscht die X-Achse des linken Sticks mit der X-Achse des rechten Sticks
        swap(XB1_LX, XB1_RX);
    }
}

// Funktion zur Überprüfung des Tastenzustands
function get_key(key) {
    // Überprüft, ob der Schlüsselcode größer oder gleich 0xE0 ist
    if (key >= 0xE0) {
        // Gibt den Status der Modifier-Tasten zurück, indem ein Bitmaskenwert erstellt wird
        return get_modifiers(1 << (key ^ 0xE0));
    }
    // Gibt den aktuellen Wert der Taste zurück (1 für gedrückt, 0 für nicht gedrückt)
    return get_ival(key);
}