define Off = 0;
define Dim_Blue = 1;
define Dim_Red = 2;
define Dim_Green = 3;
define Dim_Pink = 4;
define Dim_SkyBlue = 5;
define Dim_Yellow = 6;
define Dim_White = 7;
define Blue = 8;
define Red = 9;
define Green = 10;
define Pink = 11;
define SkyBlue = 12;
define Yellow = 13;
define <PERSON> = 14;
define Bright_Blue = 15;
define Bright_Red = 16;
define Bright_Green = 17;
define Bright_Pink = 18;
define Bright_SkyBlue = 19;
define Bright_Yellow = 20;
define Bright_White = 21;
 
data (
 
0,0,0,0, // Off
1,0,0,0, // Dim Blue
0,1,0,0, // Dim Red
0,0,1,0, // Dim Green
0,0,0,1, // Dim Pink
1,0,1,0, // Dim SkyBlue
0,1,1,0, // Dim Yellow
1,1,1,1, // Dim White
2,0,0,0, // Blue
0,2,0,0, // Red
0,0,2,0, // Green
0,0,0,2, // Pink
2,0,2,0, // SkyBlue
0,2,2,0, // Yellow
2,2,2,2, // White
3,0,0,0, // Bright Blue
0,3,0,0, // Bright Red
0,0,3,0, // Bright Green
0,0,0,3, // Bright Pink
3,0,3,0, // Bright SkyBlue
0,3,3,0, // Bright Yellow
3,3,3,3  // Bright white
 
); 


// Script was generated with < FIFA Series Skills Generator > ver. 16 Date :10/01/21 Time: 11:05:08
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
$$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
$$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
$$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
$$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
$$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
$$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
$$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
\__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
$$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
$$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
$$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
$$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
$$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
$$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
$$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
\__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_L1; // Finesse Shot
define PlayerRun     = PS4_R1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MODIFIER      = PS4_L1;     
define CrossBtn      = PS4_SQUARE; // Cross Btn 
define ThroughBall   = PS4_TRIANGLE; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;  
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
define SKILLR_X      = PS4_RX ;
define SKILLR_Y      = PS4_RY ;
int Mx , My ; 
int Pass_Or_Through
define Somb_Trigger=FinesseShot;
//unmap PS4_SHARE;
int ShotBoxWait ;
  int defence    ;  
                                               
   int Sombrero_Trigger  = PS4_R2  ;   
int LS_Sens = 90 ;


//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main { 
                                
         if(onoff_penalty || onoff_FK ) { Dynamic_Finish_onoff = FALSE;
}else{Dynamic_Finish_onoff = TRUE ; }         
    
   if( get_val(PlayerRun)){
    if(event_release(ThroughBall)){
    vm_tctrl(0);
     combo_run(DOUBLE_TAP_TROUGH_cmb) ;
     }
     }

 ////////////////////////////////  
   // LED color indication           
   if(onoff_FK)        set_ds4_led(Pink); 
   else if(onoff_penalty)   set_ds4_led(Yellow);
   else        set_ds4_led(Blue);
	//-----------------------------------------  
	// ON / OFF FREE KICK MODE                   
	// hold L1/LB and press SHARE/VIEW           
	if(get_val(PS4_L1)){                          
        if(event_press(PS4_SHARE)){             
            onoff_FK = !onoff_FK;               
        }                                       
       set_val(PS4_SHARE,0);                    
    }                                           
    if(onoff_FK){ f_FREE_KICK (); vm_tctrl(0);  }          

  
        
        
     //--------------------------------------------------------------
    //  turn ON Penalty  hold  L1 and press OPTIONS 
    if(get_val(PS4_L1)){                      
        if(event_press(PS4_OPTIONS)){             
            onoff_penalty = !onoff_penalty;   
        }                                         
       set_val(PS4_OPTIONS,0);                    
    }                                              
    if(onoff_penalty){ fPenalties (); vm_tctrl(0); }  
                                                   
     



if (get_val(PS4_SHARE) ) {
      if(event_release(PS4_R3)) {
             load_slot (2);
      }
      set_val(PS4_R3,0);
}
    if(abs(get_val(SKILLR_X))> 60 || abs(get_val(SKILLR_Y))> 60){ // getting where the player facing using by aim Right analog skill direction to there
	       Mx = get_val(SKILLR_X)                                   
	       My = get_val(SKILLR_Y)                                     
    } 
    
       if ( !onoff_penalty && !onoff_FK && get_val(Sombrero_Trigger) && ( get_val(PS4_RX) > 60 || get_val(PS4_RX) < -60 || get_val(PS4_RY) < -60 || get_val(PS4_RY) > 60 )
       && !get_val(PassBtn) && !get_val(PlayerRun) && !get_val(SprintBtn)&& !get_val(PS4_R3)  ) { 
       

    combo_run(SOMBRERO_FLICK)   
    }
    
    if(combo_running(SOMBRERO_FLICK)){
        set_val(Somb_Trigger,0);
        set_val(PS4_RX,0);
        set_val(PS4_RY,0);
        }
    
   if (combo_running(STOP_PLAYER)){
        set_val(PS4_LX,0);
        set_val(PS4_LY,0);
        }
        
        
        
        



                                                
   //--- DEFENSE                               
   if(defence_on) f_defence();                 
                                                
    //--- Use Open Up MOD                                                         
    if( ( !onoff_penalty && !onoff_FK   ) && (event_press(ShotBtn) || event_press(PassBtn) || event_press(FinesseShot))){  
        combo_run(OPEN_UP_cmb);                                                   
    }                                                                            
     
     vm_tctrl(-8);
      
if( onoff_penalty || onoff_FK || combo_running(OPEN_UP_cmb) || combo_running(SOMBRERO_FLICK) || get_val(PassBtn) || get_val(ShotBtn) || get_val(CrossBtn) || get_val(ThroughBall)  || combo_running(CHIP_SHOT) || combo_running(Dynamic_Shoot)  || combo_running(Nutmeg_TO_TFF)   ){
vm_tctrl(0);
}

	if(event_press(XB1_PR1) ){ 
	vm_tctrl(0);
	   

		combo_run(CHIP_SHOT);
		 
	}
	
	
                                                          
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot) &&  !onoff_penalty && !onoff_FK ){ 
	      if(event_press(ShotBtn) ){ 
	      vm_tctrl(0);
	      
		        combo_run(Timed_Finesse_Finish); 
	      } 
         set_val(ShotBtn,0);
    } 
    
    
        if(Dynamic_Finish_onoff) f_dynamic_finish (); 

    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(!onoff_penalty && !onoff_FK ){
        if (!get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)  ){
            sensitivity(PS4_LX, NOT_USE, LS_Sens);
            sensitivity(PS4_LY, NOT_USE, LS_Sens);
        }

    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(abs(get_val(MOVE_X))> 45 || abs(get_val(MOVE_Y))> 45){     // THE New update that should fix all skills with low LS sens .                                   
        calc_zone ();                                              
         
                 if(get_val(MOVE_X) > 45){
        LX = ( get_val(MOVE_X) + (100-LS_Sens) ) // the Fixing logic is too simple , we add the diffrence of the workable sens to user LS_sens , so direction is the same 
        }                                        // but now no more Feints , and skill functions and combos should work fine .
                if(get_val(MOVE_X)< -45){
        LX = ( get_val(MOVE_X) - (100-LS_Sens) )
        }
        
                if(get_val(MOVE_Y)> 45){
        LY = ( get_val(MOVE_Y) + (100-LS_Sens) )
        }
                if(get_val(MOVE_Y)< -45){
        LY = ( get_val(MOVE_Y) - (100-LS_Sens) )
        }
        
        }
    }
    set_val(TRACE_2,LX);
    set_val(TRACE_3,LY);
    //----------------------------------------------------------- 
                                      
   if( !onoff_penalty && !onoff_FK && !get_val(SprintBtn) &&  !get_val(PS4_R3) && !get_val(FinesseShot)  && !get_val(PS4_R3) && !get_val(PS4_L1)  && !get_val(ShotBtn) 
  && !get_val(PS4_CIRCLE) && !get_val(PS4_CROSS)  && !get_val(SprintBtn)) { // all Skills mode 
  
        if(event_press(PS4_L2)){ 
	      vm_tctrl(0);
        
                set_val(PS4_L2,0);
	      		right_on = TRUE;      
	      		combo_run(ROULETTE);
      }                     
                                                 
	      //  Right Stick -->  UP                          
	      if( get_val(PS4_RY) < -55  && !flick_up ) {  
	      vm_tctrl(0);
	      		flick_up = TRUE;                          
	      			AUTO_EXIT_SK()         
	      		combo_run(TURN_AND_SPIN);   //2. Heel to Heel
	      }                                              
	      //  Right Stick -->  DOWN                               
	      if( get_val(PS4_RY) >  55  && !flick_d ) { 
	      vm_tctrl(0);
	      		flick_d = TRUE;                            
	      		right_on = TRUE;              
	      		combo_run(BALL_ROLL);
	      }                                               
                                                        
	      //  Right Stick --> LEFT                                
	      if( get_val(PS4_RX) < -55  && !flick_l ) {  
	      vm_tctrl(0);
	      		flick_l = TRUE;                             
	      		right_on = FALSE;               
	      		combo_run(HEELtoHEEL); // 17. ROULETTE_SKILL
	      }                                               
                                                        
	      // Right Stick --> RIGHT                                   
	      if( get_val(PS4_RX) >  55  && !flick_r ) { 
	      vm_tctrl(0);
	      		flick_r = TRUE;                             
	      			          
	      		combo_run(FOUR_TOUCH_TURN_cmb); 
	      }                                                
                                                         
                                                          
        if(abs(get_val(PS4_RY))<20  && abs(get_val(PS4_RX))<20){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }                                              
        set_val(SKILL_STICK_X,0); 
        set_val(SKILL_STICK_Y,0); 
    }// end of ALWAYS ON  
                       
  
    }
 
    // all Skills mode                
   //--------------------------------------------------------------
 // end of main block                          
function f_dynamic_finish () { 
    if(event_release(CrossBtn)){
        cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1400;
    }
    
    if(cross_timer){
        cross_timer -= get_rtime();
    }
    
    if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && cross_timer <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){
            set_val(ShotBtn,0);
                
            if( after_sprint_timer > 225  ) {
	            UltimatePower = random(206,210) ; // since the ms the sprint is released >> 190 up to 215 ms shooting power generated. 
	            drval=0;
	            combo_restart(Dynamic_Shoot); 
	            
            }
            if( after_sprint_timer <= 0  ) {
	            UltimatePower = 240 ;
	            DrivenShot = 15 ;
                drval=100;
                combo_restart(Dynamic_Shoot); 
            }
        }
    } 
    /// FakeShot Support avoid Conflictions
    if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  
        combo_stop(Dynamic_Shoot);
    }
    
   
  
}
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;
int Dynamic_Finish_onoff;



combo Dynamic_Shoot {
    INSIDE_BOX_AIM()
    set_val(ShotBtn, 100);
    wait(UltimatePower );
    INSIDE_BOX_AIM()
    set_val(ShotBtn, 0);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    wait(DrivenShot + 15);
    INSIDE_BOX_AIM()
    set_val(ShotBtn, 0);
    wait(50);
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
 INSIDE_BOX_AIM()
    wait(50);
    INSIDE_BOX_AIM()
    set_val(ShotBtn, 0);
    set_val(PS4_R3,100);
    set_val(PS4_L3,100);
INSIDE_BOX_AIM()
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}

combo Dynamic_Shoot2 {
    
    
    set_val(ShotBtn, 100);
    set_val(PlayerRun  , drval);
    set_val(FinesseShot, drval);
    wait(UltimatePower + 20);       
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    wait(50);
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}


     int LxDYN;
 function INSIDE_BOX_AIM() { 
 vm_tctrl(0);
     if(get_val(PS4_LX) > 8 && get_val(PS4_LY) < -8 ) // TOP Right 
     {  
     //LxDYN = clamp(90 + ( 0.0067*(after_sprint_timer)),100,100 ) ;
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 8 && get_val(PS4_LY) > 8) // Bottom Right 
     { 
          //LxDYN = clamp(90 + ( 0.0067*(after_sprint_timer)),90,100 ) ;
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -8 && get_val(PS4_LY) < -8) // TOP Left 
     { 
          // LxDYN = clamp(90 + ( 0.0067*(after_sprint_timer)),90,100 ) ;
         LA (-100, -100);
     }
     if(get_val(PS4_LX) < -8 && get_val(PS4_LY) > 8) // Bottom Left 
     {
          // LxDYN = clamp(90 + ( 0.0067*(after_sprint_timer)),90,100 ) ;
         LA (-100, 100);

     }
 }
                                
combo OPEN_UP_cmb {      
    set_val(PS4_L3,100); 
    wait(100);           
}                       

int Teammate_Contain_on = FALSE;
int defence_on = TRUE;
define Sprint_Pass        = 0;
define Sprint_ProtectBall = 1;
int want_to_use = Sprint_ProtectBall;
int Second_Button ;
int Pre_jock_mod;
int Opp_Goal_dir;
function f_defence (){

if(get_val(SprintBtn) && !combo_running(Pre_JoCKEY) && !combo_running(JoCKEY)){
 sensitivity(PS4_LX, NOT_USE, 110);
 sensitivity(PS4_LY, NOT_USE, 110);
 }
if (get_val(PS4_L2) ){
if (get_val(PS4_RIGHT)){set_val(PS4_RIGHT,0); Opp_Goal_dir =  100 ;} // User in the start of the match will need to define opponent goal direction ,, in the start of 2nd half should do the same .
}
if (get_val(PS4_L2) ){
if (get_val(PS4_LEFT)){set_val(PS4_LEFT,0);Opp_Goal_dir =  -100 ;}
}

    
    if(want_to_use == Sprint_ProtectBall)Second_Button = PaceCtrol; 
    else  Second_Button = PassBtn;                
    
    if( get_val(SprintBtn) && get_val(Second_Button)  ){
           
    
         if (get_val(ShotBtn)){set_val(ShotBtn,0);combo_run(Assisted_Tackle);combo_stop(JoCKEY);combo_stop(Pre_JoCKEY);combo_stop(Sprint_Spam);}

            
        if(!Pre_jock_mod && ( abs(get_val(PS4_LX))>20  || abs(get_val(PS4_LY))>20  )) { // first 500 ms pre Jockey , no sprint only jockey +  90 sens = (Player Solid Shape) .
            if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
            vm_tctrl(-6);
            combo_run(Pre_JoCKEY);
		    
		    combo_run(Sprint_Spam);
            }
    
 	    if(Pre_jock_mod && ( abs(get_val(PS4_LX))>14  || abs(get_val(PS4_LY))>14  )){ // after 500 ms  Jockey + Sprint in loop .
		    if(Second_Button == PassBtn) set_val(PassBtn,0);        
            if(Teammate_Contain_on) set_val(FinesseShot,100);// auto team mate contain
		    vm_tctrl(-6);
		    combo_run(JoCKEY);
		    
		    combo_run(Sprint_Spam);
		    }
		    
		if( ( abs(get_val(PS4_LX))<12  && abs(get_val(PS4_LY))<12  )){ // when NO LS directions AI Switching and pressing AUTO.
		    if(Second_Button == PassBtn) set_val(PassBtn,0);
		    if(Second_Button == PaceCtrol) set_val(PaceCtrol,0);
		    combo_run(Auto_Switch);			
		    }else{combo_stop(Auto_Switch);
		    }
     }
    
       if( ( event_release(SprintBtn) || event_release(PaceCtrol) || event_release(PassBtn) ) ){
        Pre_jock_mod = FALSE;combo_stop(JoCKEY); vm_tctrl(0); combo_stop(Auto_Switch); combo_stop(Pre_JoCKEY);combo_stop(Sprint_Spam);
            }
  
 
}

  
    


function directional_jockey() { 
    // Moving to UP
    if((get_val(PS4_LX) > -50 && get_val(PS4_LX) <50) && get_val(PS4_LY) < -35 ) // UP
    {  
         
        LA (0, -94);
    }
          
    // Moving to DOwn     
    if((get_val(PS4_LX) > -50 && get_val(PS4_LX) <50) && get_val(PS4_LY) > 35 ) // Down
    {  
         
        LA (0, 94);
    }
    
    
    // Moving to Right     
    if((get_val(PS4_LY) > -50 && get_val(PS4_LY) <50) && get_val(PS4_LX) > 35 ) // Right
    
    { 
     
        LA (94, 0);
    }
    
    // Moving to LEFT     
    if((get_val(PS4_LY) > -50 && get_val(PS4_LY) <50) && get_val(PS4_LX) < -35 ) // Left
    {  
     
        LA (-94, 0);
    }
    
    
            // Moving to UP-Right
    if(get_val(PS4_LX) > 50  && get_val(PS4_LY) < -50 ) // UP-Right
    
    {  
     
        LA (94, -94);
    }
          
    // Moving to Down-Right     
    if(get_val(PS4_LX) > 50 && get_val(PS4_LY) > 50 ) // Down-Right
    {   
        LA (94, 94);
    }
    
    
    // Moving to UP-Left    
    if(get_val(PS4_LX) < -50 && get_val(PS4_LY) < -50)  // UP-Left
    {   
        LA (-94, -94);
    }
    
    // Moving to Down-Left     
    if(get_val(PS4_LX) < -50 && get_val(PS4_LY) > 50) // Down-Left
    {   
        LA (-94, 94);
    }

    
    }






combo Auto_Switch {
set_val(FinesseShot,100);
wait(600);
set_val(PS4_RX,Opp_Goal_dir);
set_val(FinesseShot,100);
combo_stop(JoCKEY);
wait(10);
set_val(PS4_RX,0);
set_val(FinesseShot,100);
combo_stop(JoCKEY);
wait(10);
set_val(PS4_RX,Opp_Goal_dir);
set_val(FinesseShot,100);
combo_stop(JoCKEY);
wait(10);
set_val(PS4_RX,Opp_Goal_dir);
set_val(FinesseShot,0);
combo_stop(JoCKEY);
wait(10);

}

combo Assisted_Tackle { 
set_val(ShotBtn,100);
wait(80);
}

combo Sprint_Spam {
set_val(SprintBtn,100);
wait(80);
set_val(SprintBtn,0);
wait(80);
}



combo Pre_JoCKEY {
  directional_jockey()
  set_val(PaceCtrol,100);
  set_val(PS4_L3,100);
  wait(300); 
  Pre_jock_mod = TRUE;
}

combo JoCKEY {

  sensitivity(PS4_LX, NOT_USE, 85);
  sensitivity(PS4_LY, NOT_USE, 85);
  set_val(PS4_L3,100);
  set_val(PaceCtrol,100);
  wait(1280);
 
}


combo ROULETTE {    
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}    

combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}

combo FOUR_TOUCH_TURN_cmb {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PaceCtrol,100);
    wait(40); 
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
}

                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {  
  ////Shot_Stop/Shot_Stop);
    // call(STOP_PLAYER);
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);   
  
}                                        
                                         
combo ELASTICO  { 
    //Shot_Stop/Shot_Stop);
    // call(STOP_PLAYER);
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   


combo SOMBRERO_FLICK {
    ////Shot_Stop/Shot_Stop);
    // call(STOP_PLAYER);
    RA_ZERO()
	wait(30);
	set_val(PS4_RX,Mx )
	set_val(PS4_RY,My  )
	wait(50);
	RA_ZERO ()
	wait(50);
	set_val(PS4_RX,Mx )
	set_val(PS4_RY,My  )
	wait(50);
	RA_ZERO ()
	wait(50);
	set_val(PS4_RX,inv(Mx))
	set_val(PS4_RY,inv(My))   
	wait(50);
	RA_ZERO ();
	wait(700);
  
}
int ground_pass_timer; 
int Ground_Pass_MIN = 80;
int Ground_Pass_MAX = 250;
int GP_difference;

combo Ground_Pass_MIN_cmb {
    //set_val(PlayerRun,100);
	set_val(PassBtn,100);
	set_val(FinesseShot,100);
	wait(80);
	
}

combo Ground_THrough_MIN_cmb {
    //set_val(PlayerRun,100);
	set_val(ThroughBall,100);
	set_val(FinesseShot,100);
	wait(65);
	
}
                        
combo TURN_AND_SPIN { 
    RA_UP ();      // up   
    wait(w_rstick);         
    RA_ZERO ();    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    LA_L_R();
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
    wait(200);
    LA_L_R();
    wait(300);
}           
///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);
combo InstantTimedFinish {
    CORNER()
    set_val(ShotBtn, 100);             
    wait(220);
    set_val(ShotBtn, 0);
    CORNER()
    wait(160+ping)
    set_val(ShotBtn, 100);             
    wait(220);  
} 

int long_finesse;
function CORNER() { 
  
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {  
        right_on = FALSE;
        LA (-50, -100);
    }
          
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    { 
        right_on = TRUE;
        LA (-50, 100);
    }
    
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    { 
        right_on = TRUE;
        LA (50, -100);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {
        right_on = FALSE;
        LA (50,  100);
    }
          
}




int move_lx, move_ly, zone_p;

int ping = 40;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 230;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
                 
    
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause + ping );
    
    
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn) 
    
    wait(600);
           
} 
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////

int Finish_Power = 217;// how long to hold shot
int Finish_pause    = 180;// pause before second shot
int pingN_tff =85;
combo Nutmeg_TO_TFF {
    set_val(ShotBtn, 0);  
    set_val(PS4_LX,0);                
    call(DIRECTIONAL_NUTMEG_cmb)
    // SKILL THAT USER WANTS (Nutmig  , Lateral heel to heel  ,,  ronaldo chop )
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(PS4_LX,0);
    wait(700);
    INSIDE_BOX_AIM();
    set_val(PlayerRun, 100);
    set_val(ShotBtn, 100);             
    wait(Finish_Power);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    wait(600);
           
} 
combo Dyn_LA_CROQUETA {
  set_val(ShotBtn, 0);
  INSIDE_BOX_AIM(); 
  wait(25);   
  set_val(ShotBtn, 0);// <-/-> 
  INSIDE_BOX_AIM();
  RA(LX,LY);
  wait(250);//            
} 

     /// this will stop the player , combined with if running condition related to it to force zero LS from user.
combo STOP_PLAYER {
      wait(60); // space between LS = 0 and sprint . needed
      set_val(SprintBtn,100);
      wait(200);
      set_val(SprintBtn,100);
      wait(60);
      set_val(SprintBtn,0);
      wait(60);
      wait(300); // space for the player reaction.
      }
      
combo Shot_Stop {      
      set_val(ShotBtn,100); //Fake_Shot to slow down Player reaction , Grant the stop .
      wait(90);
      set_val(PS4_L2,100);
      set_val(PS4_L1,100);
      set_val(PS4_R2,100);
      set_val(PS4_R1,100);
      wait(80);
      wait(10);
      set_val(PS4_L2,100);
      set_val(PS4_L1,100);
      set_val(PS4_R2,100);
      set_val(PS4_R1,100);
      wait(20);
      wait(30);
      }
      


combo DIRECTIONAL_NUTMEG_cmb {
    if (get_val(PS4_LY) > 20 ) { LY=100;}
    if (get_val(PS4_LY) < -20 ) { LY=-100;}
	set_val(FinesseShot,100);
	set_val(PlayerRun  ,100);
    RA (0,LY);
    wait(100);
}

combo BALL_ROLL { 
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    LA(0,0);
    wait(400);
    set_val(PS4_L2,100);
    set_val(PS4_L1,100);
    set_val(PS4_R1,100);
    set_val(PS4_R2,100);
    wait(70);   
} 

combo CHIP_SHOT {
    INSIDE_BOX_AIM();
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    set_val(FinesseShot,0);
  wait(187);
  INSIDE_BOX_AIM();
  set_val(ShotBtn,0);
  set_val(FinesseShot,0);
  wait(600);
}

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------


function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function set_ds4_led(colour) {
 
    set_led(LED_1, dbyte(colour * 4));
    set_led(LED_2, dbyte((colour * 4) + 1));
    set_led(LED_3, dbyte((colour * 4) + 2));
    set_led(LED_4, dbyte((colour * 4) + 3));
 
}

	
	function AUTO_EXIT_SK() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 10 && get_val(PS4_LY) < -10 ) // TOP Right Corner
	{   right_on = TRUE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = FALSE
	
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = FALSE 
		 

	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = TRUE

		  
		 
	}
	}

////////////////////////////////////////////////////////////////
// VARIABLES  for FREE KICK MOD 
//-------------------------------------------- 
define TOP_SPIN       = 1;                     
define SIDE_SPIN      = 2;                     
define KNUCKLEBALL_FK = 3;                     
define spin_time      = 80;                    
//-------------------------------------------- 
int onoff_FK;                                  
int shot_power = 355;                          
int side_dir   = 100;                          
int FK_mode = 1;
combo sens_fix {
            sensitivity(PS4_LX, NOT_USE, 100);
            sensitivity(PS4_LY, NOT_USE, 100);
            wait(15);
            }
function f_FREE_KICK (){
vm_tctrl(0);
   
	// AIMING FIX :                                 
	// AIM and then press and hold R2/RT to Lock Aim 
	if(!get_val(PS4_R2)){                             
		LX = get_val(PS4_LX);                           
		LY = get_val(PS4_LY);                           
        if(get_val(PS4_L1)){                     
			///////////////////////////////////////////   
			// SET THE POWER of the SHOOT                 
			if(event_press(PS4_UP))    shot_power = 390;//
            if(event_press(PS4_RIGHT)) shot_power = 390;//
            if(event_press(PS4_DOWN))  shot_power = 390;//
            if(event_press(PS4_LEFT))  shot_power = 390;//
		}                                               
		set_val(PS4_UP,0);                              
		set_val(PS4_RIGHT,0);                           
		set_val(PS4_LEFT,0);                            
		set_val(PS4_DOWN,0);                            
	}else if(get_val(PS4_R2)){                       
		set_val(PS4_LX, LX );                           
		set_val(PS4_LY, LY );                          
		///////////////////////////////////////////    
		// SET THE POWER of the SHOOT                  
        ///////////////////////////////////////////
        // Fine tune Aim                         
        if(press_hold(PS4_LEFT)) {               
			LX = LX - 1;                                  
		}                                               
		if(press_hold(PS4_RIGHT)){                      
			LX = LX + 1;                                  
		}                                               
		if(press_hold(PS4_UP)){                         
			LY = LY - 1;                                  
		}                                               
		if(press_hold(PS4_DOWN)){                       
			LY = LY + 1;                                  
		}                                               
                                                 
		set_val(PS4_UP,0);                             
		set_val(PS4_RIGHT,0);                          
		set_val(PS4_LEFT,0);                           
		set_val(PS4_DOWN,0);                           
	}                                                
	///////////////////////////////////////////      
	// TOP SPIN FK                                   
	if(get_val(XB1_Y)){                              
		FK_mode = TOP_SPIN ;                          
		//shot_power = 450;// 380 / 400               
		combo_run(SHOT_POWER);                        
		set_val(XB1_Y,0);                             
	}                                               
	///////////////////////////////////////////     
	// SIDE SPIN FK                                 
	// left side                                    
	if(get_val(XB1_X)){                             
		FK_mode = SIDE_SPIN;                          
		side_dir = -100;                              
		//shot_power = 300;                           
		combo_run(SHOT_POWER);                       
		set_val(XB1_X,0);                            
	}                                              
	///////////////////////////////////////////    
	// SIDE SPIN FK                                 
	// right side                                   
	if(get_val(XB1_B)){                             
		FK_mode = SIDE_SPIN;                          
		side_dir =  100;                              
		//shot_power = 300;                           
		combo_run(SHOT_POWER);                        
		set_val(XB1_B,0);                             
	}                                               
	///////////////////////////////////////////     
	// KNUCKLEBALL_FK                               
	//                                              
	if(get_val(XB1_A)){                             
		FK_mode = KNUCKLEBALL_FK;                     
		shot_power = 435;                             
		combo_run(SHOT_POWER);                        
		set_val(XB1_A,0);                             
	}                                               
                                             
set_val(PS4_R2,0);       
}
function press_hold(f_btn) {   
    return event_press(f_btn) || get_val(f_btn) && get_ptime(f_btn) > 250
           && get_ptime(f_btn) % (get_rtime() * 8) == 0;  
} 

combo SHOT_POWER {                                    
	set_val(ShotBtn,100);                               
	wait(shot_power);                                   
	wait(330);          

	/////////////////////////////////////////////////   
	//  FREE KICK MODE                                  
	if(FK_mode == TOP_SPIN )  combo_run(TOP_SPIN_FK);    
	if(FK_mode == SIDE_SPIN ) combo_run(SIDE_SPIN_FK);    
	if(FK_mode == KNUCKLEBALL_FK ) combo_run(KNUCKLEBALL);
}                                                       
//--------------------------------------------------- 
combo TOP_SPIN_FK  {                                  
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY, 100);                               
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100);                               
	wait(spin_time); 
	wait(1500);
	onoff_FK = !onoff_FK
}                                                     
//--------------------------------------------------- 
combo SIDE_SPIN_FK  {                                 
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,100);// DOWN                         
	wait(spin_time);                                           
	set_val(PS4_RX,side_dir);// LEFT or RIGHT           
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100);// UP                          
	wait(spin_time);
		wait(1500);
	onoff_FK = !onoff_FK
}                                                     
//--------------------------------------------------- 
combo KNUCKLEBALL {                                   
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,100);// DOWN                         
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100); // UP                         
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY, 100);// DOWN                        
	wait(spin_time); 
		wait(1500);
	onoff_FK = !onoff_FK
}                            


//=========================================================== 
// --- PENALTIES 
//*************************************************   
int onoff_penalty;                                   
int dir;                                      
int LeftRight,UpDown; 
int Pen_X,Pen_Y;
int correct_X,correct_Y;
const int PenaltiX_Y [] []= {
{  87,   0},//0. 1 Right
{ -86,   0},//1. 2 Left
{   0, 100},//2. 3 Down
{   0, -70},//3. 4 Up
{  56,  90},//4. 8 Right Down
{  78, -38},//5. 9 Right Up
{ -56,  90},//6.11 Left Down  
{ -78, -38} //7.13 Left Up 
};
//---------------------------   
function fPenalties () {                              
    
    if(!get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )LeftRight = 1;// Right
		                                              
		if(event_press(PS4_LEFT) ) LeftRight = 2;// Left
		                                              
		if(event_press(PS4_DOWN))  UpDown    = 3;// Down
		                                              
		if(event_press(PS4_UP))    UpDown    = 4;// Up  
		                                              
		if(LeftRight && !UpDown){                       
		if(LeftRight == 1) dir = 1; // Right          
		else   dir = 2;             // Left           
		                                              
		if(dir == 1 ){                                
		   Pen_X = PenaltiX_Y [0][0] ;  //0.          
		   Pen_Y = PenaltiX_Y [0][1] ;                        
		}                                             
		                                              
		if(dir == 2 ){                                
		   Pen_X = PenaltiX_Y [1][0] ;  //1.           
		   Pen_Y = PenaltiX_Y [1][1] ;                     
		}                                             
		}                                               
		else if(!LeftRight && UpDown){                  
		if(UpDown == 3) dir = 3; // Down              
		else   dir = 4;          // Up                
		if(dir == 3 ){                                
		   Pen_X = PenaltiX_Y [2][0] ;  //2.           
		   Pen_Y = PenaltiX_Y [2][1] ;                   
		}                                             
		                                              
		if(dir == 4 ){                                
		   Pen_X = PenaltiX_Y [3][0] ;  //3.           
		   Pen_Y = PenaltiX_Y [3][1] ;                    
		}                                             
		                                              
		}                                               
		else if(LeftRight && UpDown){                   
		//---------------------------------------       
		  dir = (LeftRight * UpDown) + 5 ;            
		  // Right Down                               
		  if(dir == 8 ){                              
		      Pen_X = PenaltiX_Y [4][0] ;  //4.           
		      Pen_Y = PenaltiX_Y [4][1] ;                      
		  }                                           
		  //Right Up                                  
		  if(dir == 9 ){                              
		      Pen_X = PenaltiX_Y [5][0] ;  //5.           
		      Pen_Y = PenaltiX_Y [5][1] ;                     
		  }
		  // Left Down                                
		  if(dir == 11 ){                             
		      Pen_X = PenaltiX_Y [6][0] ;  //6.           
		      Pen_Y = PenaltiX_Y [6][1] ;                      
		  }           
		  // Left Up                                  
		  if(dir == 13 ){                             
		      Pen_X = PenaltiX_Y [7][0] ;  //7.           
		      Pen_Y = PenaltiX_Y [7][1] ;                        
		  }                                           
		                                  
		}                                               
     }else if(get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )correct_X += 1;// Right
		                                          
		if(event_press(PS4_LEFT) ) correct_X -= 1;// Left
		                                          
		if(event_press(PS4_DOWN))  correct_Y += 1;// Down
		                                          
		if(event_press(PS4_UP))    correct_Y -= 1;// Up  
     }
     
     	set_val(PS4_LX, Pen_X + correct_X);
     	set_val(PS4_LY, Pen_Y + correct_Y);
     	
      set_val(PS4_UP,   0);                       
      set_val(PS4_DOWN, 0);                       
      set_val(PS4_LEFT, 0);                      
      set_val(PS4_RIGHT,0);                      
      //----  reset the aiming direction  
      if(event_press(XB1_RS)){           
      	LeftRight = 0;                   
      	UpDown    = 0;                  
      	dir       = 0;
      	Pen_X     = 0;
      	Pen_Y     = 0;
      	correct_X = 0;
      	correct_Y = 0;
      }                              
      set_val(XB1_RS,0);   
      
      
      if (event_release(ShotBtn)){
      combo_run(SwitchBack);
      }
}     

combo SwitchBack {
	wait(2500);
	onoff_penalty = !onoff_penalty
	}