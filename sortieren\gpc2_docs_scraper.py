import requests
from bs4 import BeautifulSoup
import os
import time

def scrape_gpc2_docs(urls, output_dir="gpc2_docs"):
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    for url in urls:
        try:
            # Extract filename from URL
            page_name = url.split('=')[-1].replace(':', '_')
            output_file = os.path.join(output_dir, f"{page_name}.md")
            
            print(f"Fetching: {url}")
            
            # Fetch the page
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the main content div
            content = soup.find('div', {'class': 'page'})
            
            if content:
                # Extract title
                title = soup.find('h1').text.strip() if soup.find('h1') else "GPC2 Documentation"
                
                # Create markdown content
                markdown_content = f"# {title}\n\n"
                markdown_content += f"Source: {url}\n\n"
                
                # Convert content to markdown
                for element in content.find_all(['h1', 'h2', 'h3', 'p', 'pre', 'code', 'ul', 'li', 'table']):
                    if element.name in ['h1', 'h2', 'h3']:
                        markdown_content += f"{'#' * int(element.name[1])} {element.text.strip()}\n\n"
                    elif element.name == 'p':
                        markdown_content += f"{element.text.strip()}\n\n"
                    elif element.name == 'pre' or element.name == 'code':
                        markdown_content += f"```\n{element.text.strip()}\n```\n\n"
                    elif element.name == 'ul':
                        for li in element.find_all('li'):
                            markdown_content += f"- {li.text.strip()}\n"
                        markdown_content += "\n"
                    elif element.name == 'table':
                        # Handle tables
                        for row in element.find_all('tr'):
                            cols = row.find_all(['td', 'th'])
                            markdown_content += "| " + " | ".join(col.text.strip() for col in cols) + " |\n"
                            # Add table header separator if this is the first row
                            if row == element.find('tr'):
                                markdown_content += "|" + "|".join("---" for _ in cols) + "|\n"
                
                # Save to file
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                
                print(f"Documentation saved to: {output_file}")
                
                # Add a small delay between requests to be nice to the server
                time.sleep(1)
                
            else:
                print(f"Could not find main content on the page: {url}")
                
        except requests.RequestException as e:
            print(f"Error fetching the documentation from {url}: {e}")
        except Exception as e:
            print(f"An error occurred while processing {url}: {e}")

if __name__ == "__main__":
    urls = [
        "https://www.consoletuner.com/wiki/index.php?id=t2:gpc_scripting",
        "https://www.consoletuner.com/wiki/index.php?id=t2:gpc_language_reference",
        "https://www.consoletuner.com/wiki/index.php?id=t2:gpc_headers",
        "https://www.consoletuner.com/wiki/index.php?id=t2:math_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:combo_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:macro_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:remapper_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:mxyconverter_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:io_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:battery_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:led_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:ffb_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:keyboard_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:mouse_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:gcv_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:pmem_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:mslot_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:time_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:port_functions",
        "https://www.consoletuner.com/wiki/index.php?id=t2:misc_functions"
    ]
    scrape_gpc2_docs(urls)
