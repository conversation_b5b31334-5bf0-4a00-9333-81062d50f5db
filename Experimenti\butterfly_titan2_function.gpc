#include <titanone.gph>
#pragma METAINFO("Butterfly Octagonal Gate", 1, 0, "Yamikase")

// Constants for stick identification
#define STICK_X PS4_LX  // Left stick X axis
#define STICK_Y PS4_LY  // Left stick Y axis

// Constants for octagon shape
#define MAX_VAL 100    // Maximum stick value
#define RATIO 70       // Ratio for flat sides (percentage)
#define BLEND 20       // Blend range for transitions (percentage)

// Variables
int x;             // Current stick values
int y;
int abs_x;     // Absolute values
int abs_y;
int max_val;          // Maximum value for current position
int blend_factor;     // Blending factor for transitions

init {
    printf("Butterfly Octagonal Gate initialized\n");
}

function butterfly_gate() {
    // Get current stick values
    x = get_val(PS4_LX);
    y = get_val(PS4_LY);
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Calculate blend factor based on how close we are to diagonal
        if(abs_x > abs_y) {
            blend_factor = (abs_y * 100) / abs_x;
        } else {
            blend_factor = (abs_x * 100) / abs_y;
        }
        
        // Determine max_val based on position (sharp transition)
        if(blend_factor > 100 - BLEND) {
            // Near diagonal (corner)
            max_val = MAX_VAL;
        } else if(blend_factor < BLEND) {
            // Near cardinal (side)
            max_val = (MAX_VAL * RATIO) / 100;
        } else {
            // Transition area - sharp blend
            max_val = (
                (MAX_VAL * RATIO) / 100 + 
                ((MAX_VAL - (MAX_VAL * RATIO) / 100) * 
                (blend_factor - BLEND)) / (100 - 2 * BLEND)
            );
        }
        
        // Apply scaling while maintaining direction
        x = (x * max_val) / MAX_VAL;
        y = (y * max_val) / MAX_VAL;
        
        // Set the modified values
        set_val(PS4_LX, x);
        set_val(PS4_LY, y);
    }
}

main {
    butterfly_gate();
}