// Octagonal Deadzone Pattern
define DEADZONE = 20;     // Base deadzone size
define RATIO = 70;        // Ratio for octagon corners (70 = 0.7)

int LX;                   // Left stick X
int LY;                   // Left stick Y
int abs_x;               // Absolute X value
int abs_y;               // Absolute Y value
int slope;              // For storing slope calculation

main {
    // Get raw inputs
    LX = get_val(PS4_LX);
    LY = get_val(PS4_LY);
    
    // Get absolute values
    abs_x = abs(LX);
    abs_y = abs(LY);
    
    // Calculate slope ratio (* 100 for integer math)
    if (abs_x > 0) {
        slope = (abs_y * 100) / abs_x;
    } else {
        slope = 200;  // Vertical line
    }
    
    // Check if within octagonal deadzone
    if (
        // For horizontal/vertical (close to axes)
        (slope < RATIO && abs_x <= DEADZONE) ||     // Vertical zones
        (slope > 150 && abs_y <= DEADZONE) ||       // Horizontal zones
        
        // For diagonals (around 45 degrees)
        (slope >= RATIO && slope <= 150 && 
         (abs_x + abs_y) <= DEADZONE * 3 / 2)       // Diagonal zones
    ) {
        LX = 0;
        LY = 0;
    }
    
    // Set output values
    set_val(PS4_LX, LX);
    set_val(PS4_LY, LY);
}