// Shot timing script
// Quick press (≤99ms): Outputs exact press duration
// Medium press (99-270ms): Runs 270ms combo
// Long press (>270ms): Turns off B button

int press_start;    // Stores when B was pressed
int pressing;       // Flag for button state
int press_duration; // Stores duration of press

init {
    // Initialize variables
    pressing = FALSE;
    press_start = 0;
    press_duration = 0;
}

main {
    // Check for button press
    if(event_press(XB1_B)) {
        pressing = TRUE;
        press_start = get_rtime();
    }
    
    // Check for button release
    if(event_release(XB1_B)) {
        press_duration = get_rtime() - press_start;
        pressing = FALSE;
        
        if(press_duration <= 99) {
            // Quick press - output exact duration
            combo_run(shot_quick);
        } else if(press_duration <= 270) {
            // Medium press - run 270ms combo
            combo_run(shot270);
        }
        // Long press - do nothing (button disabled)
    }
    
    // Block B button if it's a long press
    if(pressing && (get_rtime() - press_start > 270)) {
        set_val(XB1_B, 0);
    }
}

combo shot_quick {
    set_val(XB1_B, 100);
    wait(get_rtime() - press_start);
    set_val(XB1_B, 0);
}

combo shot270 {
    set_val(XB1_B, 100);
    wait(270);
    set_val(XB1_B, 0);
}
