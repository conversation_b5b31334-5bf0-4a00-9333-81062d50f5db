// Constants for stick identification and scaling
define o22_stickX = XB1_LX;
define o22_stickY = XB1_LY;
int o22_MAX_VAL = 90;     // Adjust this to scale octagon size (1-100)

// Constants for octagonal normalization
define o22_INV_SQRT2_NUM = 707;    // Approximately 1/√2 * 1000
define o22_INV_SQRT2_DEN = 1000;   // Denominator for diagonal scaling

// Constants for 22.5° rotation (clockwise)
// cos(22.5°) ≈ 0.92388 and sin(22.5°) ≈ 0.382683,
// scaled by 1000 for fixed-point math.
define o22_COS22_NUM = 924;   // Approximation of cos(22.5°) * 1000
define o22_SIN22_NUM = 383;   // Approximation of sin(22.5°) * 1000
define o22_ROT_DEN   = 1000;  // Denominator for rotation scaling

// Global variables (must be global)
int x, y;
int o22_abs_x, o22_abs_y;
int o22_L_inf, o22_L_1;      // For L-infinity and L1 norms
int o22_octNorm;         // Octagonal norm
int o22_scaled_L1;       // Scaled L1 norm
int o22_output_x, o22_output_y;
int o22_rotated_x, o22_rotated_y;   // For rotated outputs

////////////////////////////////////////////////////////////////////////////////
// LS_Dribbling_CoPilot.gpc
// Enhanced left stick dribbling script with improved mathematics and mechanics
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// GLOBAL VARIABLES
////////////////////////////////////////////////////////////////////////////////
int LX, LY;                  // Raw stick inputs
int scaled_x, scaled_y;      // Final outputs after processing
int prev_x, prev_y;         // Previous frame values for smoothing
int velocity_x, velocity_y; // Velocity tracking for acceleration

// Tunable Constants
int A_DEADZONE = 5;          // Inner deadzone threshold (0-100)
int A_MAX_INPUT = 70;        // Maximum stick value
int A_SMOOTH_FACTOR = 0;     // Input smoothing (0-100)
int A_ACCEL_THRESHOLD = 10;   // Speed threshold for acceleration (0-100)
int A_SENSITIVITY = 65;      // Base sensitivity percentage (100 = normal)

// Octagon shaping constants
int INV_SQRT2_NUM = 700;    // For octagonal corners (700-725 range)
int INV_SQRT2_DEN = 1024;   // Denominator for diagonal scaling

// Processing variables
int sign_x, sign_y, abs_x, abs_y;
int L_inf, L_1, octNorm;
int accel_factor;
int smooth_x, smooth_y;
int scaled_L1;              // For octagonal shape calculation
int sensitivity_scaled;      // For sensitivity calculations
int scaledNorm;             // For proper stick scaling
int raw_scaled_x, raw_scaled_y; // Pre-smoothing values


// Octagonal boundary for analog stick
// Creates 8-directional movement with precise angles
// Constants for stick identification
define stick_B = POLAR_LS;
// Left stick in polar mode
define stickX = XB1_LX;
// Left stick X axis
define stickY = XB1_LY;
// Left stick Y axis
// Constants for octagon shape
define MAX_VAL_B = 100;
// Maximum stick value
define RATIO_B = 70;
// Ratio for flat sides (percentage)
define BLEND_B = 20;
// Blend range for transitions (percentage)
//int x, y;
//int abs_x, abs_y;
int max_val_B;
int blend_factor;
// Constants for octagon shape
define MAX_VAL = 100;
// Maximum stick value
define RATIO = 60;
// Ratio for flat sides (percentage)
int start_value = 30;
// Starting value for ratio_radius
int max_val_radius = 100;
int ratio_radius = 60;
int max_component;
int scale;
//int LX, LY;            
//int scaled_x, scaled_y;
////////////////////////////////////////////////////////////////////////////////
// GLOBAL
////////////////////////////////////////////////////////////////////////////////
const int8 anglesMax[] = {
	// Index: 0..9
	100, 99, 98, 97, 96, 95, 94, 94, 93, 92,   // 10..19
	    91, 90, 89, 88, 87, 86, 85, 85, 84, 83,   // 20..29
	    82, 78, 70, 78, 82, 83, 84, 85, 85, 86,   // 30..39
	    87, 88, 89, 90, 91, 92, 93, 94, 94, 95,   // 40..44
	    96, 97, 98, 99, 100
}
;
int angle, radius;
const string INFO1  = "OCTAGONE";
const string INFO2 = "CONVEX";
const string LEFTP  = "PING";
const string RIGHTP = "AI ANGLE";
const string MAXRAD = "MAX RADIUS";
// Add this line for phase display
const string ANGLEP = "AGILE FREQ";
// Add this line for angle display
const string RADIUSP = "LOW RADIUS";
define RIGHT_MAG_THRESHOLD = 17;
// Random seed will be updated each frame
int randomSeed = 0;
// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;
int virtmach = -9;
// Variables for random radius control
int random_update_timer;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;
int AngleInterval = 165;
int AI_VALUES[7];
int AI_VALUES_COUNT;
int current_index = 0;
// Virtual Machine Speed
init {
	AI_VALUES[0] = 160;
	AI_VALUES[1] = 165;
	AI_VALUES[2] = 170;
	AI_VALUES[3] = 180;
	AI_VALUES[4] = 190;
	AI_VALUES[5] = 200;
	AI_VALUES[6] = 150;
	AI_VALUES_COUNT = 7;
	}
init {
	AI_VALUES_COUNT = sizeof(AI_VALUES) / sizeof(AI_VALUES[0]);
	}
define PHASE_MULTIPLIER = 10;
int lt_counter;
// For LT oscillation
int going_up;
// For LT direction
int combo_active;
// Track if LT+RT is pressed
int rt_timer;
// For RT spam
int rb_timer;
// For RB timing
init {
	lt_counter = 50;
	// Start at 50 for LT
	going_up = 1;
	combo_active = 0;
	rt_timer = 0;
	rb_timer = 0;
	}
////////////////////////////////////////////////////////////////////////////////
// Global Variables (all int declarations outside functions)
////////////////////////////////////////////////////////////////////////////////
//int LX, LY;              // Raw stick inputs
//int scaled_x, scaled_y;  // Final outputs after shaping
int DEADZONE   = 30;
// Quadratische Deadzone
int MAX_INPUT  = 70;
// Maximaler Wert je Achse
int MAX_SUM    = 140;
// Sum(|x|+|y|) -> Grenze fürs Oktagon
// Helper globals for deadzone scaling
int sign, abs_val, output, sum, scale_factor;
// Helper globals for circular clamp
int mag_sq, limit_sq, mag;
// Variables for the integer square-root function
int iSqrtValue, iSqrtRes, iSqrtBit, iSqrtTemp;
main {
	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_LEFT)) {
			load_slot (2);
					}
		set_val(XB1_LEFT,0);
			}
	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_RIGHT)) {
			load_slot (2);
					}
		set_val(XB1_RIGHT,0);
			}
	//   DISABLE / ENABLE ENTIRE SCRIPT
	if(get_ival(XB1_LT)){
		if(event_press(XB1_VIEW)){
			KS_EntireScript = !KS_EntireScript;
			f_set_notify (KS_EntireScript);
					}
		set_val(XB1_VIEW,0);
			}
	//   DISABLE / ENABLE ENTIRE SCRIPT
	if( !KS_EntireScript){
		set_rgb(0,0,255);
		// BLUE
// Update the random timer
		if(random_update_timer) {
			random_update_timer -= get_rtime();
					}
		LX = get_val(XB1_LX);
		// Get stick values first
		LY = get_val(XB1_LY);
		// Only process stick inputs if both triggers are pressed or both triggers are released
		if((get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0) ||       (get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0)) {
			// Execute FAKE_SHOT combo when either X or LS is pressed and triggers are active
			if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
				if(event_press(XB1_X) || event_press(XB1_LS)) {
					set_val(XB1_LT, 0);
					set_val(XB1_RT, 0);
					set_val(XB1_X, 0);
					combo_run(FAKE_SHOT);
									}
							}
			angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
			radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
			if (get_val(XB1_LS)) {
				//set_val(XB1_LT, 100);
				//set_val(XB1_RT, 100);
				set_val(XB1_LS, 0);
    			// Get stick values
    			star_x = get_val(XB1_LX);
    			star_y = get_val(XB1_LY);
    
    			// Apply convex octagonal mapping
    			star_map_convex_octagon(star_x, star_y);
    
    			// Set final values
    			set_val(XB1_LX, star_scaled_x);
    			set_val(XB1_LY, star_scaled_y);
							}
			else if (get_val(XB1_RS)) {
				set_val(XB1_RS, 0);
				o22()
							}
			else {
				// Apply set_polar2 with anglesMax when XB1_LS is not active
				set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
							}
					}
		else {
			scaled_x = LX;
			scaled_y = LY;
					}
		vm_tctrl(virtmach)      // Polarisierungs-Werte abfragen
		    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
		radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
		// Reset shared values at the start of each loop iteration
		rb_val = 0;
		lb_val = 0;
		// Handle radius-based LB behavior first
		if(radius < 2000) {
			if(get_ival(XB1_LB)) {
				set_val(XB1_LT, 100);
				set_val(XB1_RT, 100);
				set_val(XB1_LB, 0);
				if(!combo_running(LeftRightCombo)) {
					combo_run(LeftRightCombo);
									}
							}
			else {
				combo_stop(LeftRightCombo);
				set_val(XB1_LT, 0);
				set_val(XB1_RT, 0);
							}
					}
		// When radius >= 2000, we keep the original LB value
		else {
			if(get_ival(XB1_LB)) {
				lb_val = 100;
							}
			else {
				lb_val = 0;
							}
					}
		// Only handle X and Y and A buttons if triggers are not pressed
		if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
			handle_rb_related_button(XB1_X);
			handle_rb_related_button(XB1_Y);
			handle_lb_related_button(XB1_A);
					}
		// **Directly handle XB1_RB press**
		if(get_ival(XB1_RB)) {
			rb_val = 100;
					}
		// Set RB based on aggregated value
		set_val(XB1_RB, rb_val);
		// Only set LB from lb_val if radius >= 2000
		if(radius >= 2000) {
			set_val(XB1_LB, lb_val);
					}
		//pass();
/*
 		if(get_val(XB1_RS)) {
        combo_run(FAKE_SHOT);set_val(XB1_RS,0);
    }
    */
// Finesse LB+B
		if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);
					}
		/*
    // Finesse with R3
    if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
    }
	*/
// Shots with B
		if (!get_val(XB1_LT)) {
			if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
				combo_run(TapB);
							}
			else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {
				set_val(XB1_B,0);
				if (event_press(XB1_B)) combo_run(PressB);
							}
					}
		// Shots with RB+B
		if (get_val(XB1_RB)) {
			if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
				combo_run(TapB180);
							}
			else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {
				set_val(XB1_B,0);
				if (event_press(XB1_B)) combo_run(PressB99);
							}
					}
		if(get_ival(XB1_RT)){
			if(event_press(XB1_RIGHT)) {
				current_index = (current_index + 1) % AI_VALUES_COUNT;
				AngleInterval = AI_VALUES[current_index];
				on_the_fly_display(10, ANGLEP[0], AngleInterval);
				//print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
			}
			if(event_press(XB1_LEFT)) {
				current_index = (current_index - 1 + AI_VALUES_COUNT) % AI_VALUES_COUNT;
				AngleInterval = AI_VALUES[current_index];
				on_the_fly_display(10, ANGLEP[0], AngleInterval);
				//print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
			}
			set_val(PS4_RIGHT, 0);
			set_val(PS4_LEFT, 0);
					}
		if(get_ival(XB1_LT)){
			if(event_press(XB1_UP) && o22_MAX_VAL < 100) {
				o22_MAX_VAL += 2;
				on_the_fly_display(10, MAXRAD[0], o22_MAX_VAL);
							}
			if(event_press(XB1_DOWN) && o22_MAX_VAL > 1) {
				o22_MAX_VAL -= 2;
				on_the_fly_display(10, MAXRAD[0], o22_MAX_VAL);
							}
			set_val(PS4_UP, 0);
			set_val(PS4_DOWN, 0);
			// Ratio of radius
			if(event_press(XB1_RIGHT) && ratio_radius < start_value + 60) {
				ratio_radius += 2;
				on_the_fly_display(10, RADIUSP[0], ratio_radius);
							}
			if(event_press(XB1_LEFT) && ratio_radius > start_value) {
				ratio_radius -= 2;
				on_the_fly_display(10, RADIUSP[0], ratio_radius);
							}
			set_val(PS4_RIGHT, 0);
			set_val(PS4_LEFT, 0);
					}
			}
	// Entire Script ON/OFF 
	else {
		set_rgb(255,0,0);
			}
	}
function pass() {
	radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
	angle = get_polar(POLAR_LS, POLAR_ANGLE);
	if (radius < 2400) {
		set_polar(POLAR_LS, 0, 0);
		set_val(XB1_LB, 0);
			}
	else if (radius >= 2400 && radius < 8000) {
		set_val(XB1_LB, 100);
		if (!get_val(XB1_LS)) {
			// Only apply when XB1_LS is not active
			set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
					}
		else {
			set_polar2(POLAR_LS, angle, 8000);
					}
			}
	else if (radius >= 8000) {
		if (!get_val(XB1_LS)) {
			// Only apply when XB1_LS is not active
			set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
					}
		else {
			set_polar2(POLAR_LS, angle, 10000);
					}
			}
	if (!get_val(XB1_LS)) {
		// Only apply octagon mapping when XB1_LS is not active
		LX = get_val(XB1_LX);
		LY = get_val(XB1_LY);
		map_circular_octagon(LX, LY);
		set_val(XB1_LX, scaled_x);
		set_val(XB1_LY, scaled_y);
			}
	}
function handle_rb_related_button(int button) {
	if(get_ival(button)) {
		ptime = get_ptime(button);
		if(ptime < 250) {
			set_val(button, 100);
					}
		else if(ptime >= 250 && ptime <= 380) {
			set_val(button, 100);
			rb_val = 100;
			// Activate XB1_RB
			combo_run (stop_lb);
					}
		else {
			set_val(button, 0);
					}
			}
	else {
		set_val(button, 0);
			}
	}
combo stop_lb {
	wait(100);
	set_val(XB1_LB, 0);
	wait(100);
	}
function handle_lb_related_button(int button) {
	if(get_ival(button)) {
		ptime = get_ptime(button);
		if(ptime < 250) {
			set_val(button, 100);
			lb_val = 100;
					}
		else if(ptime >= 250 && ptime <= 380) {
			set_val(button, 100);
			lb_val = 100;
			// Activate XB1_LB
		}
		else {
			set_val(button, 0);
					}
			}
	else {
		set_val(button, 0);
			}
	}
combo PressB {
	set_val(XB1_B, 100);
	wait(100);
	}
combo TapB {
	set_val(XB1_B, 0);
	wait(250);
	}
int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;
combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
	}
combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
	}
combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
	}
int UltimatePower;
combo OutSideBox_Finishing_cmb {
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100);
	wait(UltimatePower);
	///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
	}
int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);
	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
	}
function getPolar(Stick, AngleOrRadius) {
	if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
	return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));
	}
//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================
function on_the_fly_display (f_string, f_print, f_val){
	cls_oled(0);
	line_oled(1,18,127,18,1,1);
	print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);
	NumberToString(f_val, FindDigits(f_val));
	time_to_clear_screen  = 2000;
	}
combo CLEAR_SCREEN {
	wait(20);
	cls_oled(0);
	}
/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
	return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
	}
int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
	set_rumble(Vibrate_type,100);
	wait(300);
	reset_rumble();
	wait(20);
	}
int data_indx;
/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
	bufferIndex = 1;
	digitIndex = 10000;
	if(f_val < 0) {
		//--neg numbers
		putc_oled(bufferIndex,45);
		//--add leading "-"
		bufferIndex += 1;
		f_val = abs(f_val);
			}
	for(charIndex = 5;
	charIndex >= 1;
	charIndex--) {
		if(f_digits >= charIndex) {
			putc_oled(bufferIndex,(f_val / digitIndex) + 48);
			f_val %= digitIndex;
			bufferIndex ++;
			if(charIndex == 4) {
				putc_oled(bufferIndex,44);
				//--add ","
				bufferIndex ++;
							}
					}
		digitIndex /= 10;
			}
	puts_oled(10,38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
	}
int logVal;
function FindDigits(num) {
	logVal = 0;
	do {
		num /= 10;
		logVal++;
			}
	while (num);
	return logVal;
	}
int stringLength;
function getStringLength(offset) {
	stringLength = 0;
	do {
		offset++;
		stringLength++;
			}
	while (duint8(offset));
	return stringLength;
	}
function set_ds4_led(colour) {
	set_led(LED_1, duint8 (colour * 4));
	set_led(LED_2, duint8 ((colour * 4) + 1));
	set_led(LED_3, duint8 ((colour * 4) + 2));
	set_led(LED_4, duint8 ((colour * 4) + 3));
	}
int KS_EntireScript = FALSE;
function f_set_notify (f_val){
	if(f_val)Vibrate_type = RUMBLE_A;
	else     Vibrate_type = RUMBLE_B;
	combo_run(NOTIFY_cmb);
	}
function LED_Color(color) {
	for( data_indx = 0;
	data_indx < 3;
	data_indx++ ) {
		set_led(data_indx,duint8 ((color * 3) + data_indx));
			}
	}
int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {
	return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
	}
const string OFF   = "OFF";
const string ON    = "ON";
function defend(){
	// Check for LT+RT combo
	if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
		combo_active = 1;
			}
	else {
		combo_active = 0;
		// Reset everything when combo is released
		set_val(XB1_LT, get_val(XB1_LT));
		set_val(XB1_RT, get_val(XB1_RT));
		set_val(XB1_LB, get_val(XB1_LB));
		lt_counter = 50;
		rt_timer = 0;
		rb_timer = 0;
			}
	if(combo_active) {
		// Handle LT oscillation (50-100)
		set_val(XB1_LT, lt_counter);
		if(going_up) {
			lt_counter = lt_counter + 2;
			// Much slower oscillation
			if(lt_counter >= 100) {
				going_up = 0;
							}
					}
		else {
			lt_counter = lt_counter - 2;
			// Much slower oscillation
			if(lt_counter <= 50) {
				going_up = 1;
							}
					}
		// Handle RT timing (1 second on, 500ms off)
		rt_timer = rt_timer + 1;
		if(rt_timer <= 60) {
			// 1 second on (100 ticks)
			set_val(XB1_RT, 100);
					}
		else if(rt_timer <= 80) {
			// 500ms off (50 ticks)
			set_val(XB1_RT, 0);
					}
		else {
			rt_timer = 0;
			// Reset cycle
		}
		// Handle RB timing (3 seconds on, 1 second off)
		rb_timer = rb_timer + 1;
		if(rb_timer <= 210) {
			// 3 seconds on (300 ticks)
			set_val(XB1_LB, 100);
					}
		else if(rb_timer <= 220) {
			// 1 second off (100 ticks)
			set_val(XB1_LB, 0);
					}
		else {
			rb_timer = 0;
			// Reset cycle
		}
			}
	}
/*
function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;
    } while (temp > result);
    
    return result;
}
*/
// Function to generate a pseudo-random number
function getRandomNumber() {
	randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
	return randomSeed;
	}
// Get random number between min and max
function getRandomRange(min, max) {
	return (getRandomNumber() % (max - min + 1)) + min;
	}
////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////
//int angle, radius;
////////////////////////////////////////////////////////////////////////////////
// COMBOS
////////////////////////////////////////////////////////////////////////////////
combo LeftRightCombo {
	// 1) Linken Stick nach links
	set_polar(POLAR_LS, 0, 32000);
	//set_val(XB1_LX, -100);
	wait(AngleInterval);
	// 2) Linken Stick nach rechts
	set_polar(POLAR_LS, 180, 32000);
	//set_val(XB1_LX, 100);
	wait(AngleInterval);
	}
combo FAKE_SHOT{
	set_val(XB1_B, 100);
	vm_tctrl(0);
	wait(40);
	set_val(XB1_B, 100);
	set_val(XB1_A, 100);
	vm_tctrl(0);
	wait(60);
	set_val(XB1_B, 0);
	set_val(XB1_A, 100);
	vm_tctrl(0);
	wait(60);
	//Get_LS_Output = TRUE;
}
////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
//   - Must not declare local int variables here, so we use the global ones
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
	iSqrtValue = value;
	iSqrtRes   = 0;
	iSqrtBit   = 1 << 14;
	// 2^14 = 16384 (enough for up to ~20000)
// Shift down until bit <= value
	while(iSqrtBit > iSqrtValue) {
		iSqrtBit = iSqrtBit >> 2;
			}
	while(iSqrtBit != 0) {
		iSqrtTemp = iSqrtRes + iSqrtBit;
		if(iSqrtValue >= iSqrtTemp) {
			iSqrtValue = iSqrtValue - iSqrtTemp;
			iSqrtRes   = iSqrtRes + (iSqrtBit << 1);
					}
		iSqrtRes = iSqrtRes >> 1;
		iSqrtBit = iSqrtBit >> 2;
			}
	return iSqrtRes;
	}
////////////////////////////////////////////////////////////////////////////////
// Function: apply_one_axis_deadzone(val)
//   - Applies a square deadzone on a single axis: [DEADZONE..100] -> [0..100]
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(int val) {
	// Vorzeichen
	if(val >= 0) {
		sign = 1;
			}
	else {
		sign = -1;
			}
	abs_val = abs(val);
	// Innerhalb der Deadzone -> 0
	if(abs_val <= DEADZONE) {
		return 0;
			}
	// Begrenzen, falls > MAX_INPUT
	if(abs_val > MAX_INPUT) {
		abs_val = MAX_INPUT;
			}
	// Linear von [DEADZONE..MAX_INPUT] nach [0..MAX_INPUT] skalieren
	output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
	return (sign * output);
	}
// Hauptfunktion zum Erzeugen des konvexen Oktagons
function map_convex_octagon(int x, int y) {
	// 1) Square Deadzone pro Achse
	scaled_x = apply_one_axis_deadzone(x);
	scaled_y = apply_one_axis_deadzone(y);
	// 2) Sum-of-abs Clamp -> |x| + |y| <= 200
	sum = abs(scaled_x) + abs(scaled_y);
	if(sum > MAX_SUM) {
		scale_factor = (MAX_SUM * 1000) / sum;
		scaled_x = (scaled_x * scale_factor) / 1000;
		scaled_y = (scaled_y * scale_factor) / 1000;
			}
	// 3) X/Y zusätzlich auf ±100 beschränken
	if(scaled_x >  MAX_INPUT) scaled_x =  MAX_INPUT;
	if(scaled_x < -MAX_INPUT) scaled_x = -MAX_INPUT;
	if(scaled_y >  MAX_INPUT) scaled_y =  MAX_INPUT;
	if(scaled_y < -MAX_INPUT) scaled_y = -MAX_INPUT;
	}
function apply_octagonal_boundary(_stickX, _stickY) {
	// Get current stick values
	x = get_val(_stickX);
	y = get_val(_stickY);
	if(abs(x) > 0 || abs(y) > 0) {
		// Get absolute values
		abs_x = abs(x);
		abs_y = abs(y);
		// Find the larger component
		if(abs_x > abs_y) {
			max_component = abs_x;
					}
		else {
			max_component = abs_y;
					}
		// Calculate scale based on max component
		scale = MAX_VAL;
		if(max_component > 0) {
			// If we're closer to cardinal direction, reduce scale
			if(abs_x > abs_y * 2 || abs_y > abs_x * 2) {
				scale = RATIO;
							}
					}
		// Apply scaling to maintain direction
		x = (x * scale) / MAX_VAL;
		y = (y * scale) / MAX_VAL;
		// Set modified values
		set_val(_stickX, x);
		set_val(_stickY, y);
		return TRUE;
			}
	return FALSE;
	}
function butterfly() {
	// Get current stick values
	x = get_val(stickX);
	y = get_val(stickY);
	if(abs(x) > 0 || abs(y) > 0) {
		// Get absolute values
		abs_x = abs(x);
		abs_y = abs(y);
		// Calculate blend factor based on how close we are to diagonal
// When abs_x == abs_y, we're at diagonal (45 degrees)
		if(abs_x > abs_y) {
			blend_factor = (abs_y * 100) / abs_x;
					}
		else {
			blend_factor = (abs_x * 100) / abs_y;
					}
		// Sharp transition between sides and corners
		if(blend_factor > 100 - BLEND_B) {
			// Near diagonal (corner)
			max_val_B = MAX_VAL_B;
					}
		else if(blend_factor < BLEND_B) {
			// Near cardinal (side)
			max_val_B = (MAX_VAL_B * RATIO_B) / 100;
					}
		else {
			// Transition area - sharp blend
			max_val_B = (MAX_VAL_B * RATIO_B) / 100 +                      ((MAX_VAL_B - (MAX_VAL_B * RATIO_B) / 100) *                       (blend_factor - BLEND_B) / (100 - 2 * BLEND_B));
					}
		// Apply scaling while maintaining direction
		x = (x * max_val_B) / MAX_VAL_B;
		y = (y * max_val_B) / MAX_VAL_B;
		// Set the modified values
		set_val(stickX, x);
		set_val(stickY, y);
			}
	}
////////////////////////////////////////////////////////////////////////////////
// PROCESSING PIPELINE
////////////////////////////////////////////////////////////////////////////////
function process_stick_input() {
	// 1. Determine signs and absolute values
	if(LX >= 0) sign_x = 1;
	else sign_x = -1;
	if(LY >= 0) sign_y = 1;
	else sign_y = -1;
	abs_x = abs(LX);
	abs_y = abs(LY);
	// 2. Apply octagonal mapping
	L_inf = max(abs_x, abs_y);
	L_1 = abs_x + abs_y;
	// Scale L_1 for octagonal shape (improved scaling)
	scaled_L1 = (L_1 * 1024) / (1024 + INV_SQRT2_NUM);
	octNorm = max(L_inf, scaled_L1);
	// 3. Apply deadzone
	if(octNorm <= A_DEADZONE) {
		scaled_x = 0;
		scaled_y = 0;
		return;
			}
	// 4. Clamp maximum input
	if(octNorm > A_MAX_INPUT) {
		octNorm = A_MAX_INPUT;
			}
	// 5. Scale from [A_DEADZONE..A_MAX_INPUT] -> [0..A_MAX_INPUT]
	scaledNorm = (octNorm - A_DEADZONE) * A_MAX_INPUT / (A_MAX_INPUT - A_DEADZONE);
	// 6. Calculate acceleration factor
	accel_factor = calc_acceleration(scaledNorm);
	// 7. Apply sensitivity and acceleration
	sensitivity_scaled = (A_SENSITIVITY * accel_factor) / 100;
	// 8. Compute properly scaled outputs maintaining the octagonal shape
	raw_scaled_x = (abs_x * scaledNorm / octNorm) * sensitivity_scaled / 100;
	raw_scaled_y = (abs_y * scaledNorm / octNorm) * sensitivity_scaled / 100;
	// 9. Apply smoothing
	smooth_x = apply_smoothing(raw_scaled_x, prev_x);
	smooth_y = apply_smoothing(raw_scaled_y, prev_y);
	// 10. Restore signs and clamp to A_MAX_INPUT
	scaled_x = clamp(smooth_x * sign_x, -A_MAX_INPUT, A_MAX_INPUT);
	scaled_y = clamp(smooth_y * sign_y, -A_MAX_INPUT, A_MAX_INPUT);
	}
////////////////////////////////////////////////////////////////////////////////
// HELPER FUNCTIONS
////////////////////////////////////////////////////////////////////////////////
function calc_acceleration(int speed) {
	if(speed <= A_ACCEL_THRESHOLD) {
		return 100;
		// No acceleration below threshold
	}
	// Progressive acceleration above threshold
	return 100 + ((speed - A_ACCEL_THRESHOLD) * 50) / (A_MAX_INPUT - A_ACCEL_THRESHOLD);
	}
function apply_smoothing(int current, int previous) {
	return (current * (100 - A_SMOOTH_FACTOR) + previous * A_SMOOTH_FACTOR) / 100;
	}


function o22() {

    // Get current stick values
    x = get_val(o22_stickX);
    y = get_val(o22_stickY);
    
    // Compute absolute values
    o22_abs_x = abs(x);
    o22_abs_y = abs(y);

    // Calculate L-infinity (max) norm and L1 (Manhattan) norm
    if (o22_abs_x > o22_abs_y) {
        o22_L_inf = o22_abs_x;
    } else {
        o22_L_inf = o22_abs_y;
    }
    o22_L_1 = o22_abs_x + o22_abs_y;
    
    // Scale L1 norm for octagonal shape
    o22_scaled_L1 = (o22_L_1 * o22_INV_SQRT2_NUM) / o22_INV_SQRT2_DEN;
    
    // Octagonal norm is the maximum of o22_L_inf and o22_scaled_L1
    if (o22_L_inf > o22_scaled_L1) {
        o22_octNorm = o22_L_inf;
    } else {
        o22_octNorm = o22_scaled_L1;
    }
    
    // If the stick is moved (nonzero input)
    if (o22_octNorm > 0) {
        // Scale outputs while maintaining direction and apply o22_MAX_VAL scaling
        o22_output_x = (x * o22_MAX_VAL) / o22_octNorm;
        o22_output_y = (y * o22_MAX_VAL) / o22_octNorm;
        
        // Clamp outputs to o22_MAX_VAL limits
        if (o22_output_x > o22_MAX_VAL) o22_output_x = o22_MAX_VAL;
        if (o22_output_x < -o22_MAX_VAL) o22_output_x = -o22_MAX_VAL;
        if (o22_output_y > o22_MAX_VAL) o22_output_y = o22_MAX_VAL;
        if (o22_output_y < -o22_MAX_VAL) o22_output_y = -o22_MAX_VAL;
    } else {
        o22_output_x = 0;
        o22_output_y = 0;
    }
    
    // Rotate the normalized output by 22.5° clockwise.
    // Rotation matrix for a clockwise rotation by θ is:
    //   x' = x*cosθ + y*sinθ
    //   y' = -x*sinθ + y*cosθ
    o22_rotated_x = ((o22_output_x * o22_COS22_NUM) + (o22_output_y * o22_SIN22_NUM)) / o22_ROT_DEN;
    o22_rotated_y = ((-o22_output_x * o22_SIN22_NUM) + (o22_output_y * o22_COS22_NUM)) / o22_ROT_DEN;
    
    // Set final rotated values to the stick outputs
    set_val(o22_stickX, o22_rotated_x);
    set_val(o22_stickY, o22_rotated_y);
}

// Constants
define star_MAX_INPUT = 100;
define star_DEADZONE = 5;
define star_ARRAY_SIZE = 23;

// Variables
int star_octagonMax[star_ARRAY_SIZE];  // Array declaration
int star_x, star_y, star_mag_sq, star_mag;
int star_angle, star_index, star_extension;
int star_scaled_x, star_scaled_y;
int star_sign, star_abs_val, star_output;
int star_i;  // Loop counter

// Variables for star_intSqrt
int star_iSqrtValue;
int star_iSqrtRes;
int star_iSqrtBit;
int star_iSqrtTemp;

////////////////////////////////////////////////////////////////////////////////
// Function: star_intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
////////////////////////////////////////////////////////////////////////////////
function star_intSqrt(value) {
    star_iSqrtValue = value;
    star_iSqrtRes   = 0;
    star_iSqrtBit   = 1 << 14; // 2^14 = 16384 (enough for up to ~20000)

    // Shift down until bit <= value
    while(star_iSqrtBit > star_iSqrtValue) {
        star_iSqrtBit = star_iSqrtBit >> 2;
    }

    while(star_iSqrtBit != 0) {
        star_iSqrtTemp = star_iSqrtRes + star_iSqrtBit;
        if(star_iSqrtValue >= star_iSqrtTemp) {
            star_iSqrtValue = star_iSqrtValue - star_iSqrtTemp;
            star_iSqrtRes   = star_iSqrtRes + (star_iSqrtBit << 1);
        }
        star_iSqrtRes = star_iSqrtRes >> 1;
        star_iSqrtBit = star_iSqrtBit >> 2;
    }
    return star_iSqrtRes;
}

// Initialize array values in main
function init_octagon_values() {
    star_octagonMax[0] = 100;   // 0°
    star_octagonMax[1] = 99;    // 2°
    star_octagonMax[2] = 98;    // 4°
    star_octagonMax[3] = 96;    // 6°
    star_octagonMax[4] = 94;    // 8°
    star_octagonMax[5] = 91;    // 10°
    star_octagonMax[6] = 88;    // 12°
    star_octagonMax[7] = 85;    // 14°
    star_octagonMax[8] = 82;    // 16°
    star_octagonMax[9] = 79;    // 18°
    star_octagonMax[10] = 76;   // 20°
    star_octagonMax[11] = 74;   // 22°
    star_octagonMax[12] = 72;   // 24°
    star_octagonMax[13] = 70;   // 26°
    star_octagonMax[14] = 69;   // 28°
    star_octagonMax[15] = 68;   // 30°
    star_octagonMax[16] = 67;   // 32°
    star_octagonMax[17] = 66;   // 34°
    star_octagonMax[18] = 66;   // 36°
    star_octagonMax[19] = 65;   // 38°
    star_octagonMax[20] = 65;   // 40°
    star_octagonMax[21] = 65;   // 42°
    star_octagonMax[22] = 65;   // 44°
}

function apply_deadzone(int val) {
    if(val >= 0) {
        star_sign = 1;
    } else {
        star_sign = -1;
    }
    star_abs_val = abs(val);
    
    if(star_abs_val <= star_DEADZONE)
        return 0;
    
    star_output = ((star_abs_val - star_DEADZONE) * star_MAX_INPUT) / (star_MAX_INPUT - star_DEADZONE);
    if(star_output > star_MAX_INPUT)
        star_output = star_MAX_INPUT;
        
    return star_sign * star_output;
}

function star_map_convex_octagon(int x, int y) {
    // Apply deadzone first
    star_scaled_x = apply_deadzone(x);
    star_scaled_y = apply_deadzone(y);
    
    // Calculate magnitude
    star_mag_sq = star_scaled_x * star_scaled_x + star_scaled_y * star_scaled_y;
    if(star_mag_sq == 0)
        return;
        
    star_mag = star_intSqrt(star_mag_sq);
    
    // Get current angle (0-360)
    star_angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Get index into extension table (normalize to 0-45 degrees)
    star_index = star_angle % 45;
    if(star_index > 22) {
        star_index = 45 - star_index;
    }
    
    // Apply convex octagonal boundary
    star_extension = star_octagonMax[star_index];
    star_scaled_x = (star_scaled_x * star_extension) / 100;
    star_scaled_y = (star_scaled_y * star_extension) / 100;
}

init {
    // Initialize the octagon values
    init_octagon_values();
}