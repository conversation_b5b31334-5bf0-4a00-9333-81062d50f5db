// GPC Script: WildAlien2025
// Activates XB1_RB or XB1_LB based on Left Stick direction.

// Definitions
// STICK_DEADZONE: How far the stick must be moved before registering input (0-100).
define STICK_DEADZONE = 10;

// Global Variables
// In GPC, all 'int' variables must be declared globally.
int current_angle;    // Stores the current angle of the left stick (0-359 degrees).
int current_radius;   // Stores the current radius/magnitude of the left stick (0-100).
int is_rb_zone;       // Flag: TRUE (1) if in XB1_RB zone, FALSE (0) otherwise.

// Main execution block, runs continuously
main {
    // Get current left stick angle and radius
    // PVAR_LS_ANGLE provides angle from -179 to 180 degrees.
    // PVAR_LS_TILT provides magnitude from 0 to 100.
    current_angle = get_val(PVAR_LS_ANGLE);
    current_radius = get_val(PVAR_LS_TILT);

    // Convert angle from (-179 to 180) range to (0 to 359) range
    if (current_angle < 0) {
        current_angle = current_angle + 360;
    }

    // Check if the stick is moved beyond the deadzone
    if (current_radius > STICK_DEADZONE) {
        // Stick is active
        is_rb_zone = FALSE; // Initialize to FALSE (0)

        // --- Check for XB1_RB activation zones ---
        // Zone 1 for RB: 315 degrees up to (but not including) 45 degrees.
        // This covers angles [315, 359] OR [0, 44].
        if (current_angle >= 315) { // Angles from 315 to 359
            is_rb_zone = TRUE;
        }
        if (current_angle < 45) {  // Angles from 0 to 44
            is_rb_zone = TRUE;
        }

        // Zone 2 for RB: 135 degrees up to (but not including) 225 degrees.
        // This covers angles [135, 224].
        // GPC requires breaking down complex conditions:
        if (current_angle >= 135) {
            if (current_angle < 225) { // Angle is 135 or more, AND less than 225
                is_rb_zone = TRUE;
            }
        }

        // --- Set button states based on the determined zone ---
        if (is_rb_zone) {
            set_val(XB1_RB, 100); // Activate Right Bumper
            set_val(XB1_LB, 0);   // Deactivate Left Bumper
        } else {
            // If not in an RB zone, it's an LB zone by elimination
            set_val(XB1_LB, 100); // Activate Left Bumper
            set_val(XB1_RB, 0);   // Deactivate Right Bumper
        }

    } else {
        // Stick is in the deadzone or centered: deactivate both bumpers
        set_val(XB1_LB, 0);
        set_val(XB1_RB, 0);
    }
}

// init block (optional, not strictly needed here as variables are set each loop)
// init {
//     // Initialize global variables if needed at script start
//     // current_angle = 0;
//     // current_radius = 0;
//     // is_rb_zone = FALSE;
// }
