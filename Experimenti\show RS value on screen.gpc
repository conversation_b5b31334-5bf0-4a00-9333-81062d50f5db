const string Axis_Strings[] = { "RX:", "RY:" }

int rx;
int ry;
int sleep_timer;
int digit_count;
int val_str;
int digits_val;
int digit_idx;

main {

  // Reset when out of deadzone
  if(get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1000)
    sleep_timer = 0;

  if(sleep_timer < 5000) {
    rx = get_ival(POLAR_LX);
    ry = get_ival(POLAR_LY);
    // Scale down for actual Playstation Controller stick range
    switch(get_controller()) {
      case PIO_PS3 {}
      case PIO_PS4 {}
      case PIO_PS5 {
        rx /= 128;
        ry /= 128;
      }
    }
    cls_oled(OLED_BLACK);
    // Display RX
    print(10, 10, OLED_FONT_MEDIUM, OLED_WHITE, Axis_Strings[0]);
    int_to_string(rx, find_digits(rx));
    puts_oled(50, 10, OLED_FONT_MEDIUM, val_str - 1, OLED_WHITE);
    // Display RY
    print(10, 38, OLED_FONT_MEDIUM, OLED_WHITE, Axis_Strings[1]);
    int_to_string(ry, find_digits(ry));
    puts_oled(50, 38, OLED_FONT_MEDIUM, val_str - 1, OLED_WHITE);
    // Sleep timer
    sleep_timer += get_rtime();
    if(sleep_timer >= 5000)
      cls_oled(OLED_BLACK);
  }

}

function find_digits(f_num) {
  digit_count = 0;
  do {
    digit_count ++;
    f_num /= 10;
  }
  while(f_num);
  return digit_count;
}

function center_text(f_chars,f_char_width) {
  return (OLED_WIDTH / 2) - ((f_char_width * f_chars) / 2);
}

function int_to_string(f_value, f_digits) {
  val_str = 1;
  digits_val = 10000;
  // -- Add "-"
  if(f_value < 0) {
    putc_oled(val_str, ASCII_MINUS);
    val_str ++;
    f_value = abs(f_value);
  }
  // -- Convert digits to ASCII
  for(digit_idx = 5; digit_idx; digit_idx --) {
    if(f_digits >= digit_idx) {
      putc_oled(val_str, ASCII_DIGIT0 + (f_value / digits_val));
      f_value %= digits_val;
      val_str ++;
    }
    digits_val /= 10;
  }
}