// oscilate_triggers.gpc
// Script that oscillates LT, pulses RT, and pulses LB when LT+RT are pressed together

// Variables
int lt_counter;    // For LT oscillation
int going_up;      // For LT direction
int combo_active;  // Track if LT+RT is pressed
int rt_timer;      // For RT spam
int rb_timer;      // For RB timing

init {
    // Initialize variables
    lt_counter = 50;  // Start at 50 for LT
    going_up = 1;
    combo_active = 0;
    rt_timer = 0;
    rb_timer = 0;
}

// Combo that activates when LT and RT are pressed together
combo XB1_LT && XB1_RT {
    combo_active = 1;

    // Handle LT oscillation (50-100)
    set_val(XB1_LT, lt_counter);
    if(going_up) {
        lt_counter = lt_counter + 2;  // Slower oscillation (using integer)
        if(lt_counter >= 100) {
            going_up = 0;
        }
    } else {
        lt_counter = lt_counter - 2;  // Slower oscillation (using integer)
        if(lt_counter <= 50) {
            going_up = 1;
        }
    }

    // Handle RT timing (1 second on, 500ms off)
    rt_timer = rt_timer + 1;
    if(rt_timer <= 60) {  // 1 second on (60 ticks)
        set_val(XB1_RT, 100);
    } else if(rt_timer <= 80) {  // 500ms off (20 ticks)
        set_val(XB1_RT, 0);
    } else {
        rt_timer = 0;  // Reset cycle
    }

    // Handle LB timing (3 seconds on, 1 second off)
    rb_timer = rb_timer + 1;
    if(rb_timer <= 210) {  // 3 seconds on (210 ticks)
        set_val(XB1_LB, 100);
    } else if(rb_timer <= 220) {  // 1 second off (10 ticks)
        set_val(XB1_LB, 0);
    } else {
        rb_timer = 0;  // Reset cycle
    }
}

// Reset combo when LT and RT are not both pressed
combo !(XB1_LT && XB1_RT) {
    // When combo is released
    combo_active = 0;
    // Reset everything
    set_val(XB1_LT, get_val(XB1_LT));
    set_val(XB1_RT, get_val(XB1_RT));
    set_val(XB1_LB, get_val(XB1_LB));
    lt_counter = 50;
    rt_timer = 0;
    rb_timer = 0;
}

main {
    // The combo handles all the logic when LT+RT are pressed
    // This main block just passes through controller inputs when the combo is not active
    if(!combo_active) {
        // Pass through all controller inputs normally
        // No need to do anything as GPC passes through inputs by default
    }
}
