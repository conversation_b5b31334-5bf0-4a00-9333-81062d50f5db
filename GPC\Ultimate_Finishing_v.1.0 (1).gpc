
// ============  Ultimate Finishing Script ============//
//HOW it WORKs  :
//1-  when you are inside the 18 yards box of your opponent press Shot button and direction where you aim ,  
//    while nothing else pressed , like  no sprnt or L2 or L1 ..etc .
//2-  it will generate fixed power for inside box finishing .
//3-  <PERSON><PERSON><PERSON> will modify your aiming and lock it to the corner where you aim to grant a perfect aim as possible .
//4-  Long Shots designed not to be too long , just activate it around the box so you can get perfect results
//    all you need to do is press FinesseShot Button + Shot Button .
//    it will perform Finesse Shot with perfect power and aim assist that you need to grant scoring as much as possible .
//5-  Please Note that this is not a hack that will help you score each time . ENJOY !!
  
  //==================================================// 
 //IMPORTANT // MAKE SURE NO Shooting SCRIPTS Generated From FIFA Generator // 
   
    
   //============ COPY THis just BEFORE the END MAIN SECTION ============//


	
  	 if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn)){ //inside box trigger
 
		if( get_val(ShotBtn ) ){
		    
			set_val(ShotBtn,0);
	
			
			combo_restart(Inside_Box_Finishing)
	
	
	}
	}   
	
	if(combo_running(Inside_Box_Finishing) && get_val(PassBtn) ) { // FAKE_SHOT Setiuation , need to force stop shooting combo
	combo_stop(Inside_Box_Finishing);
	combo_run(FAKE_SHOT);
	}

	if( get_val(FinesseShot) && event_press(ShotBtn) && !get_val(PaceCtrol)){ //out side box trigger
	 set_val(ShotBtn,0);
	 combo_run(OutSide_Box_Shoot);
	}
  // ============ END OF COPY TO MAIN SECTION ============ //
  
  
  
  
   // ============ COPY THis TO COMBOS SECTION ============ //
   
   combo Shooting_Setup {
  set_val(ShotBtn, 0);
  set_val(PlayerRun,100);
  RA_L_R () ; 
  wait(30);//
  set_val(ShotBtn, 0);
  set_val(PaceCtrol,100);
  set_val(SprintBtn,100);
  wait(30);//     
}   

  
  combo Inside_Box_Finishing {
     AUTO_EXIT();
call (Shooting_Setup );
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(220);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600); 

} 


combo OutSide_Box_Shoot {

    set_val(PS4_R3,100);
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(168);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, 100);
    wait(17);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    wait(15);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);            
}       


  combo FAKE_SHOT {        
	set_val(ShotBtn,100); 

	wait(40); 
	
	set_val(PassBtn,100); 
	wait(60);

	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}  


function AUTO_EXIT() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
	
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 

	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE

		  
		 
	}
	}
	


 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }
