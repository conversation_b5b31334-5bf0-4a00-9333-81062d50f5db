main {
// DOWN
    if(get_val(XB1_PL1)){
            set_polar(POLAR_RS, 180 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
        }
// UP        
            if(get_val(XB1_RS)){
            set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
        }
// LEFT      
      if(get_val(XB1_PL2)){
            set_polar(POLAR_RS, 270 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
        }
// RIGHT       
            if(get_val(XB1_PR2)){
            set_polar(POLAR_RS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
        }      
    
}