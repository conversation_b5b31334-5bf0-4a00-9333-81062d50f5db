#pragma METAINFO("set_polar", 1, 0, "DontAtMe")
fix32 _COSINE_(fix32 a, bool b) {/*^ */
  if ((a = mod(a + 6.28319, 6.28319)) < 0.0) a = mod(a += 6.28319, 6.28319);
  if (a > 0.785522 && a < 3.92761) return b ? sin(1.5708 - a) : cos(1.5708 - a);
  return b ? cos(a) : sin(a);
}
#define sin(a) (_COSINE_(a))
#define cos(a) (_COSINE_(a, 1))

const fix32 radius = 100.00;
const fix32 speed = 0.6;

//speed and radius when moving the stick.
const fix32 movement_speed = 0.400;
const fix32 movement_radius = 0.1;
fix32 angle;
main {
  
  fix32 x = get_actual(STICK_2_X);
  fix32 y = get_actual(STICK_2_Y);
  fix32 m = sqrt(sq(x)+sq(y));
  
  // Left Trigger to activate.

  if(is_active(PADDLE_1)){
	// set_val(BUTTON_4, 100); 
    angle = mod(angle += (m > radius ? movement_speed : speed), 360.00);
    set_polar(STICK_2_X, STICK_2_Y, angle, (m > radius ? movement_radius : radius));
  }

  if(is_active(PADDLE_3)){
	 set_val(BUTTON_7, 100); 
    angle = mod(angle += (m > radius ? movement_speed : speed), 360.00);
    set_polar(STICK_2_X, STICK_2_Y, angle, (m > radius ? movement_radius : radius));
  }
 
  if(is_active(PADDLE_2)){
	 set_val(BUTTON_8, 100); 
	  set_val(BUTTON_5, 100); 
    angle = mod(angle += (m > radius ? movement_speed : speed), 360.00);
    set_polar(STICK_2_X, STICK_2_Y, angle, (m > radius ? movement_radius : radius));
  }

  if(is_active(PADDLE_4)){
    angle = mod(angle += (m > radius ? movement_speed : speed), 360.00);
    set_polar(STICK_2_X, STICK_2_Y, angle, (m > radius ? movement_radius : radius));
  }  
  
} 

void set_polar(uint8 X, uint8 Y, fix32 ANGLE, fix32 RADIUS){
  fix32 angle = rad2deg(mod(deg2rad(ANGLE) + 6.28319, 6.28319));
  fix32  x_Out = cos(deg2rad(angle));
  fix32  y_Out = sin(deg2rad(angle));
  offset(X, clamp(x_Out * RADIUS, -100.00, 100.00));
  offset(Y, clamp(y_Out * RADIUS, -100.00, 100.00));
}
void offset(uint8 axis, fix32 offset_val) {
  set_val(axis, clamp(offset_val * (100f - abs(get_actual(axis))) / 100f + get_actual(axis), -100f, 100f));
  return;
}
