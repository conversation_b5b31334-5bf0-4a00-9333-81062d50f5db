define RIGHT_MAG_THRESHOLD = 17;
// Random seed will be updated each frame
int randomSeed = 0;

int rxVal;
int ryVal;
int lxVal;
int lyVal;
int rightMagnitude;
int leftMagnitude;

int scaledRX;
int scaledRY;
int scaledLX;
int scaledLY;
int result;
int temp;
int targetMagnitude;
int currentMagnitude = 95;  // Starting at middle value
int transitionSpeed = 1;    // How fast the magnitude changes (adjust this value to control speed)
int updateInterval = 30;    // How often to get a new target (in frames)
int frameCounter = 0;       // Counter for frames

// Function to generate a pseudo-random number
function getRandomNumber() {
    randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
    return randomSeed;
}

// Get random number between min and max
function getRandomRange(min, max) {
    return (getRandomNumber() % (max - min + 1)) + min;
}

main {
    // Update random seed each frame using stick values to add entropy
    randomSeed = randomSeed + rxVal + ryVal + lxVal + lyVal;
    
    rxVal = get_val(XB1_RX);
    ryVal = get_val(XB1_RY);
    lxVal = get_val(XB1_LX);
    lyVal = get_val(XB1_LY);

    // Calculate magnitudes for both sticks
    rightMagnitude = sqrt(rxVal * rxVal + ryVal * ryVal);
    leftMagnitude = sqrt(lxVal * lxVal + lyVal * lyVal);

    // Update target magnitude periodically
    frameCounter++;
    if(frameCounter >= updateInterval) {
        targetMagnitude = getRandomRange(90, 100);
        frameCounter = 0;
    }

    // Gradually move current magnitude towards target
    if(currentMagnitude < targetMagnitude) {
        currentMagnitude = currentMagnitude + transitionSpeed;
    } else if(currentMagnitude > targetMagnitude) {
        currentMagnitude = currentMagnitude - transitionSpeed;
    }

    // Handle right stick
    if(rightMagnitude >= RIGHT_MAG_THRESHOLD && rightMagnitude != 0) {
        scaledRX = (rxVal * currentMagnitude) / rightMagnitude;
        scaledRY = (ryVal * currentMagnitude) / rightMagnitude;
        set_val(XB1_RX, scaledRX);
        set_val(XB1_RY, scaledRY);
    }

    // Handle left stick
    if(leftMagnitude >= RIGHT_MAG_THRESHOLD && leftMagnitude != 0) {
        scaledLX = (lxVal * currentMagnitude) / leftMagnitude;
        scaledLY = (lyVal * currentMagnitude) / leftMagnitude;
        set_val(XB1_LX, scaledLX);
        set_val(XB1_LY, scaledLY);
    }
}

function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;
    } while (temp > result);
    
    return result;
}