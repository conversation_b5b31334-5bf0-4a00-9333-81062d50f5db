// Eight-point stick boundary with precise angles
// Creates 8 evenly spaced points at MAX_VAL=100

// Constants for stick identification
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis
define STICK = POLAR_LS; // Left stick polar identifier

// Constants for shape
define MAX_VAL = 100;    // Maximum stick value
define RATIO = 60;       // Reduced ratio value
define ANGLE_STEP = 45;  // 360/8 = 45 degrees between points

int angle;
int radius;
int target_radius;
int base_angle;

main {
    // Get current polar coordinates
    angle = get_ipolar(STICK, POLAR_ANGLE);
    radius = get_ipolar(STICK, POLAR_RADIUS);
    
    if(radius > 0) {
        // Find the nearest base angle (0, 45, 90, etc)
        base_angle = (angle / ANGLE_STEP) * ANGLE_STEP;
        
        // If we're exactly on a 45-degree increment
        if(angle == base_angle) {
            target_radius = MAX_VAL;
        }
        // If we're between points
        else {
            target_radius = RATIO;
        }
        
        // Keep the original angle but modify the radius
        set_polar(STICK, angle, target_radius * 327);
    }
}
