																/*
																
															  _____          _____  _  __           _   _  _____ ______ _        ______ _____ ___  _  _          ____   ___  
															 |  __ \   /\   |  __ \| |/ /     /\   | \ | |/ ____|  ____| |      |  ____/ ____|__ \| || |        |___ \ / _ \ 
															 | |  | | /  \  | |__) | ' /     /  \  |  \| | |  __| |__  | |      | |__ | |       ) | || |_  __   ____) | | | |
															 | |  | |/ /\ \ |  _  /|  <     / /\ \ | . ` | | |_ |  __| | |      |  __|| |      / /|__   _| \ \ / /__ <| | | |
															 | |__| / ____ \| | \ \| . \   / ____ \| |\  | |__| | |____| |____  | |   | |____ / /_   | |    \ V /___) | |_| |
															 |_____/_/    \_\_|  \_\_|\_\ /_/    \_\_| \_|\_____|______|______| |_|    \_____|____|  |_|     \_/|____/ \___/ 
															                                                                                                                 
															                                                                                                                 
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(Darke-Angel) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as Dark.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/





































 int D30v191[0]; init { 	D30v115(); 	combo_run(D30v1); 	combo_run(D30v2); 	combo_run(D30v3); 	combo_run(D30v4); 	combo_run(D30v5); 	combo_run(D30v6); 	combo_run(D30v7); 	combo_run(D30v8); 	combo_run(D30v9); 	combo_run(D30v10); 	combo_run(D30v11); 	combo_run(D30v12); 	combo_run(D30v13); 	combo_run(D30v14); 	combo_run(D30v15); 	combo_run(D30v16); 	combo_run(D30v17); 	combo_run(D30v18); 	combo_run(D30v19); 	combo_run(D30v20); 	combo_run(D30v21); 	combo_run(D30v22); 	combo_run(D30v23); 	combo_run(D30v24); 	combo_run(D30v25); 	combo_run(D30v26); 	combo_run(D30v27); 	combo_run(D30v28); 	combo_run(D30v29); 	combo_run(D30v30); 	combo_run(D30v31); 	combo_run(D30v32); 	combo_run(D30v33); 	combo_run(D30v34); 	combo_run(D30v35); 	combo_run(D30v36); 	combo_run(D30v37); 	combo_run(D30v38); 	combo_run(D30v39); 	combo_run(D30v40); 	combo_run(D30v41); 	combo_run(D30v42); 	combo_run(D30v43); 	combo_run(D30v44); 	combo_run(D30v45); 	combo_run(D30v46); 	combo_run(D30v47); 	combo_run(D30v48); 	combo_run(D30v49); 	combo_run(D30v50); 	combo_run(D30v51); 	combo_run(D30v52); 	combo_run(D30v53); 	combo_run(D30v54); 	combo_run(D30v55); 	combo_run(D30v56); 	combo_run(D30v57); 	combo_run(D30v58); 	combo_run(D30v59); 	combo_run(D30v60); 	combo_run(D30v61); 	combo_run(D30v62); 	combo_run(D30v63); 	combo_run(D30v64); 	combo_run(D30v65); 	combo_run(D30v66); 	combo_run(D30v67); 	combo_run(D30v68); 	combo_run(D30v69); 	combo_run(D30v70); 	combo_stop(D30v1); 	combo_stop(D30v2); 	combo_stop(D30v3); 	combo_stop(D30v4); 	combo_stop(D30v5); 	combo_stop(D30v6); 	combo_stop(D30v7); 	combo_stop(D30v8); 	combo_stop(D30v9); 	combo_stop(D30v10); 	combo_stop(D30v11); 	combo_stop(D30v12); 	combo_stop(D30v13); 	combo_stop(D30v14); 	combo_stop(D30v15); 	combo_stop(D30v16); 	combo_stop(D30v17); 	combo_stop(D30v18); 	combo_stop(D30v19); 	combo_stop(D30v20); 	combo_stop(D30v21); 	combo_stop(D30v22); 	combo_stop(D30v23); 	combo_stop(D30v24); 	combo_stop(D30v25); 	combo_stop(D30v26); 	combo_stop(D30v27); 	combo_stop(D30v28); 	combo_stop(D30v29); 	combo_stop(D30v30); 	combo_stop(D30v31); 	combo_stop(D30v32); 	combo_stop(D30v33); 	combo_stop(D30v34); 	combo_stop(D30v35); 	combo_stop(D30v36); 	combo_stop(D30v37); 	combo_stop(D30v38); 	combo_stop(D30v39); 	combo_stop(D30v40); 	combo_stop(D30v41); 	combo_stop(D30v42); 	combo_stop(D30v43); 	combo_stop(D30v44); 	combo_stop(D30v45); 	combo_stop(D30v46); 	combo_stop(D30v47); 	combo_stop(D30v48); 	combo_stop(D30v49); 	combo_stop(D30v50); 	combo_stop(D30v51); 	combo_stop(D30v52); 	combo_stop(D30v53); 	combo_stop(D30v54); 	combo_stop(D30v55); 	combo_stop(D30v56); 	combo_stop(D30v57); 	combo_stop(D30v58); 	combo_stop(D30v59); 	combo_stop(D30v60); 	combo_stop(D30v61); 	combo_stop(D30v62); 	combo_stop(D30v63); 	combo_stop(D30v64); 	combo_stop(D30v65); 	combo_stop(D30v66); 	combo_stop(D30v67); 	combo_stop(D30v68); 	combo_stop(D30v69); 	combo_stop(D30v70); 	combo_run(D30v107); } int D30v264 ; int D30v265; int D30v266; int D30v267; int D30v268; define D30v269 = 0; define D30v270 = 1; define D30v271 = 2; define D30v272 = 3; define D30v273 = 4; define D30v274 = 5; define D30v275 = 6; define D30v276 = 7; define D30v277 = 8; define D30v278 = 9; define D30v279 = 10; define D30v280 = 11; define D30v281 = 12; define D30v282 = 13; define D30v283 = 14; define D30v284 = 15; define D30v285 = 16; define D30v286 = 17; define D30v287 = 18; define D30v288 = 19; define D30v289 = 20; define D30v290 = 21; define D30v291 = 22; define D30v23 = 23; define D30v293 = 24; define D30v294 = 25; define D30v295 = 26; define D30v296 = 27; define D30v297 = 28; define D30v298 = 29; define D30v299 = 30; define D30v300 = 31; define D30v301 = 32; define D30v302 = 33; define D30v303 = 34; define D30v304 = 35; define D30v305 = 36; define D30v306 = 37; define D30v307 = 38; define D30v308 = 39; define D30v309 = 40; define D30v310 = 41; define D30v311 = 42; define D30v312 = 43; define D30v313 = 44; define D30v314 = 45; define D30v315 = 46; define D30v316 = 47; define D30v317 = 48; define D30v318 = 49; define D30v319 = 50; define D30v320 = 51; define D30v321 = 52; define D30v322 = 53; define D30v323 = 54; define D30v324 = 55; define D30v325 = 56; define D30v326 = 57; define D30v327 = 58; define D30v328 = 59; define D30v329 = 60; define D30v330 = 61; define D30v331 = 62; define D30v332 = 63; define D30v333 = 64; define D30v334 = 65; define D30v335 = 66; define D30v336 = 67; define D30v337 = 68; define D30v338 = 69; define D30v339 = 70; define D30v340 = 0; function D30v109(D30v110) { 	if (D30v110 == 0) vm_tctrl(-0); 	else if (D30v110 == 1) vm_tctrl(-1); 	else if (D30v110 == 2) vm_tctrl(-2); 	else if (D30v110 == 3) vm_tctrl(-3); 	else if (D30v110 == 4) vm_tctrl(-4); 	else if (D30v110 == 5) vm_tctrl(-5); 	else if (D30v110 == 6) vm_tctrl(-6); 	else if (D30v110 == 7) vm_tctrl(-7); 	else if (D30v110 == 8) vm_tctrl(-8); 	else if (D30v110 == 9) vm_tctrl(-9); } int D30v341, D30v342; int D30v343, D30v344; int D30v345 = FALSE, D30v346; int D30v347 = TRUE; int D30v348; const string D30v802[] = { 	"Off",  "On" } ; int D30v349; int D30v350; int D30v351; int D30v352; int D30v353; int D30v354; int D30v355; int D30v356; int D30v357; int D30v358; int D30v359; int D30v360; int D30v361; int D30v362; int D30v363; int D30v364; int D30v365; int D30v366; int D30v367; int D30v368; int D30v110; int D30v370; int D30v371 ; int D30v372 ; int D30v373 ; define D30v374 = 24; int D30v375; int D30v376; int D30v377; int D30v378; int D30v379; int D30v380; int D30v381; int D30v382; int D30v383; int D30v384 ; int D30v385 ; int D30v386 ; int D30v387 ; int D30v388 ; int D30v389 ; int D30v390 ; int D30v391 ; int D30v392 ; int D30v393 ; int D30v394 ; int D30v395 ; int D30v396; int D30v397; int D30v398; int D30v399; int D30v400; int D30v401; int D30v402; int D30v403; int D30v404; int D30v405; int D30v406; int D30v407; int D30v408; int D30v409; int D30v410; int D30v411; int D30v412; int D30v413; int D30v414; int D30v415; int D30v416; int D30v417; int D30v418; int D30v419; int D30v420; int D30v421; int D30v422; int D30v423; int D30v424; int D30v425; int D30v426; int D30v427; int D30v428; int D30v429; int D30v430 ; int D30v431 ; int D30v432 ; int D30v433; int D30v434 ; int D30v435 ; int D30v436 ; int D30v437; int D30v438 ; int D30v439 ; int D30v440 ; int D30v441; int D30v442 ; int D30v443 ; int D30v444 ; int D30v445; int D30v446; const int16 D30v808[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 5, 0 	} 	, { 		-150, 150, 10, 5, 0 	} 	, { 		-150, 150, 10, 5, 0 	} 	,      { 		-150, 150, 10, 5, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       	{ 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		3,60,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 D30v550[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		70,70,1 	} 	,       	    { 		71,72,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 56, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		73, 73, 1 	} 	,    { 		0, 0, 0 	} 	,    { 		69, 69, 1 	} 	,    { 		57, 60, 1 	} 	,   { 		61, 65, 1 	} 	,   { 		66, 68, 1 	} } ; const uint8 D30v780[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string D30v560[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string D30v559[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- Normal Finish ",  "+/- PowerShot",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed", "TR to PS" ,  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","First Touch","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","AI_Support","" } ; const string D30v784 [] = { 	"Classic","Alternative","Custom", ""  } ; const string D30v883 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string D30v800[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string D30v786[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string D30v853[] = { 	"Right",  "Left",  "" } ; const string D30v851[] = { 	"One Tap",  "Double Tap",  "" } ; const string D30v790[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string D30v792[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_PowerShot","FL_Nutmg_L_R" } ; const string D30v827[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; const uint8 D30v1317[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 D30v1318[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 D30v1319[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 D30v1320[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 D30v1321[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 D30v1322[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D30v1323[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D30v1324[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 D30v1325[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 D30v1326[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 D30v1327[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 D30v1328[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 D30v1329[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 D30v1330[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D30v1331[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D30v1332[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function D30v111(D30v112) { 	if (D30v112 == 9) { 		D30v447 = -1; 			} 	else if (D30v112 <= 0) { 		D30v447 = 1; 			} 	else if (D30v112 > 9 ) { 		D30v112 = 0; 			} 	D30v112 += D30v447; 	return D30v112; 	} function D30v113() { 	vm_tctrl(0); 	if(D30v29 && D30v352){ 		if(D30v526 < 1000){ 			D30v450 = 10; 			D30v476   = 10; 			D30v474  = 10; 					} 			} 	if(D30v453 && D30v353){ 		D30v451 = FALSE; 		if(D30v526 < 1000){ 			D30v450 = 11; 			D30v476   = 11; 			D30v474  = 11; 					} 			} 	if(D30v451 && D30v353){ 		D30v453 = FALSE; 		if(D30v526 < 1000){ 			D30v450 = 10; 			D30v476   = 10; 			D30v474  = 10; 					} 			} 			       if(D30v526 >= 1000){     D30v455 = D30v111(D30v455);     D30v473 = D30v111(D30v473);     D30v474 = D30v111(D30v474);     D30v450 = D30v111(D30v450);     D30v476 = D30v111(D30v476);     } 	if(D30v352){ 		if(D30v479 == D30v564){ 			D30v456 = !D30v456; 			if(D30v1317[D30v455]) D30v216(D30v1317[D30v455]); 					} 		if(D30v479 == D30v221 (D30v564 + 4)){ 			D30v456 = FALSE; 			if(D30v1324[D30v473]) D30v216(D30v1324[D30v473]); 					} 		if(D30v479 == D30v221 (D30v564 + 1) ){ 			D30v456 = TRUE; 			if(D30v1319[D30v450]) D30v216(D30v1319[D30v450]); 					} 		if(D30v479 == D30v221 (D30v564 - 1) ){ 			D30v456 = FALSE; 			if(D30v1318[D30v450]) D30v216(D30v1318[D30v450]); 					} 		if(D30v479 == D30v221 (D30v564 + 2) ){ 			D30v456 = TRUE; 			if(D30v1321[D30v476]) D30v216(D30v1321[D30v476]); 					} 		if(D30v479 == D30v221 (D30v564 - 2) ){ 			D30v456 = FALSE; 			if(D30v1320[D30v476]) D30v216(D30v1320[D30v476]); 					} 		if(D30v479 == D30v221 (D30v564 + 3) ){ 			D30v456 = TRUE; 			if(D30v1322[D30v474]) D30v216(D30v1322[D30v474]); 					} 		if(D30v479 == D30v221 (D30v564 - 3) ){ 			D30v456 = FALSE; 			if(D30v1323[D30v474]) D30v216(D30v1323[D30v474]); 					} 			} 	else if(D30v353){ 		if(D30v479 == D30v564){ 			D30v456 = !D30v456; 			if(D30v1325[D30v455]) D30v216(D30v1325[D30v455]); 					} 		if(D30v479 == D30v221 (D30v564 + 4)){ 			D30v456 = FALSE; 			if(D30v1332[D30v473]) D30v216(D30v1332[D30v473]); 					} 		if(D30v479 == D30v221 (D30v564 + 1) ){ 			D30v456 = TRUE; 			if(D30v1327[D30v450]) D30v216(D30v1327[D30v450]); 					} 		if(D30v479 == D30v221 (D30v564 - 1) ){ 			D30v456 = FALSE; 			if(D30v1326[D30v450]) D30v216(D30v1326[D30v450]); 					} 		if(D30v479 == D30v221 (D30v564 + 2) ){ 			D30v456 = TRUE; 			if(D30v1329[D30v476]) D30v216(D30v1329[D30v476]); 					} 		if(D30v479 == D30v221 (D30v564 - 2) ){ 			D30v456 = FALSE; 			if(D30v1328[D30v476]) D30v216(D30v1328[D30v476]); 					} 		if(D30v479 == D30v221 (D30v564 + 3) ){ 			D30v456 = TRUE; 			if(D30v1330[D30v474]) D30v216(D30v1330[D30v474]); 					} 		if(D30v479 == D30v221 (D30v564 - 3) ){ 			D30v456 = FALSE; 			if(D30v1331[D30v474]) D30v216(D30v1331[D30v474]); 					} 			} } int D30v455; int D30v473; int D30v474; int D30v450; int D30v476; function D30v114() { 	if(D30v1108){ 		D30v477 += get_rtime(); 			} 	if(D30v477 >= 3000){ 		D30v477 = 0; 		D30v1108 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(D30v442) && !get_ival(D30v443) && !get_ival(D30v441) && !get_ival(D30v440)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !D30v480 && !combo_running(D30v0)) { 			D30v479 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D30v480 = TRUE; 			D30v1108 = TRUE; 			D30v477 = 0; 			vm_tctrl(0); 			D30v113(); 					} 		set_val(D30v1089, 0); 		set_val(D30v1090, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		D30v480 = FALSE; 			} 	} function D30v115() { 	D30v161(); 	if (D30v375 == 0 && D30v376 == 0 && D30v377 == 0 && D30v378 == 0 && D30v379 == 0 && D30v380 == 0 && D30v381 == 0 && D30v382 == 0) { 		D30v375 = 4; 		D30v376 = 41; 		D30v377 = 41; 		D30v378 = 42; 		D30v379 = 42; 		D30v380 = 31; 		D30v381 = 31; 		D30v382 = 31; 			} 	D30v922 = get_slot(); 	} int D30v447 = 1; int D30v484; int D30v485; int D30v486 = TRUE; int D30v487[6]; int D30v488; int D30v489; int D30v490; int D30v491; int block = FALSE; int D30v492 = 1; combo D30v0{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{  D30v109(D30v110); 	if(!D30v485){ 		D30v485 = TRUE; 		D30v484 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,D30v485); 		set_pvar(SPVAR_3,D30v484); 		D30v486 = TRUE; 			} 	if(!D30v491){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			D30v490 = D30v118(D30v490 + 1 ,0,5)D30v486 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			D30v490 = D30v118(D30v490 - 1 ,0,5)D30v486 = TRUE 		} 		if(event_press(PS4_UP)){ 			D30v487[D30v490]  = D30v118(D30v487[D30v490] + 1 ,0,9)D30v486 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			D30v487[D30v490]  = D30v118(D30v487[D30v490] - 1 ,0,9)D30v486 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			D30v488 = 0; 			for(D30v489 = 5; 			D30v489 >= 0; 			D30v489--){ 				D30v488 += D30v487[D30v489] * pow(10,D30v489) 			} 			if(D30v488 == D30v116(D30v484)){ 				D30v491 = TRUE; 				set_pvar(SPVAR_2,D30v491)  			} 			D30v486 = TRUE; 					} 			} 	if(D30v486){ 		cls_oled(0)if(!D30v491){ 			D30v122(D30v484,D30v507,10,OLED_FONT_MEDIUM,OLED_WHITE,D30v508)for( D30v489 = 0; 			D30v489 < 6; 			D30v489++){ 				D30v122(D30v487[D30v489],85 - (D30v489 * 10),40,OLED_FONT_MEDIUM,!(D30v489 == D30v490),D30v508) 			} 					} 		D30v486 = FALSE; 			} 	if(D30v491){ 		if (get_ival(D30v438) || get_ival(D30v442) || get_ival(D30v440) || get_ival(D30v441) || D30v345 || combo_running(D30v72) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			D30v109(D30v110); 					} 		if(get_ival(D30v443) > 40 || (!get_ival(D30v440) && !get_ival(D30v441))){ 			if(get_ival(D30v438)){ 				vm_tctrl(0); 				if(get_ptime(D30v438) > D30v579){ 					set_val(D30v438,0); 									} 							} 					} 		if(!get_ival(D30v440)){ 			if(get_ival(D30v438)){ 				vm_tctrl(0); 				if(get_ptime(D30v438) > D30v579){ 					set_val(D30v438,0); 									} 							} 					} 		if (D30v345) { 			vm_tctrl(0); 			if(D30v346 < 8050){ 				D30v346 += get_rtime(); 							} 			if (D30v346 >= 8000) { 				cls_oled(OLED_BLACK); 				D30v346 = 0; 				D30v345 = FALSE; 							} 					} 		if (block) { 		if (D30v110 > 7)combo_run(D30v108); 			if (D30v492 < 310) { 				D30v492 += get_rtime(); 							} 			if (D30v492 <= 300 ) { 				D30v158(); 							} 			if (D30v492 > 300 ) { 				block = FALSE; 				D30v492 = 1; 				D30v688 = TRUE; 							} 			if (D30v492 < 0) { 				D30v492 = 1; 							} 			if (D30v492 <= 100) { 				combo_stop(D30v88); 				combo_stop(D30v97); 				combo_stop(D30v89); 				combo_stop(D30v98); 				combo_stop(D30v95); 				combo_stop(D30v96); 				combo_stop(D30v92); 				combo_stop(D30v94); 				combo_stop(D30v91); 				combo_stop(D30v87); 				combo_stop(D30v85); 				combo_stop(D30v90); 				combo_stop(D30v104); 				combo_stop(D30v106); 				combo_stop(D30v100); 				combo_stop(D30v105); 				combo_stop(D30v99); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(D30v428){ 			D30v429 = FALSE; 					} 		if(D30v429){ 			D30v428 = FALSE; 					} 		if(D30v350){ 			D30v351 = FALSE; 			D30v352 = FALSE; 			D30v353 = FALSE; 					} 		if(D30v351){ 			D30v350 = FALSE; 			D30v352 = FALSE; 			D30v353 = FALSE; 					} 		if(D30v352){ 			D30v350 = FALSE; 			D30v351 = FALSE; 			D30v353 = FALSE; 					} 		if(D30v353){ 			D30v350 = FALSE; 			D30v351 = FALSE; 			D30v352 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				D30v1143 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				D30v1143 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				D30v1031 = !D30v1031; 				D30v218(D30v1262); 				D30v192(D30v1031, sizeof(D30v523) - 1, D30v523[0]); 				D30v345 = TRUE; 							} 					} 		if (D30v1031) { 				if(D30v422 == TRUE){ 			if(get_ival(D30v443) && ( combo_running(D30v102) && !get_ival(D30v441)) || get_ipolar(POLAR_RS,POLAR_RADIUS) > 2800)  {set_val(D30v441,0);combo_run(D30v101);} 					} 					if(combo_running(D30v101)){ 						if(event_press(D30v441) || event_press(D30v438) || event_press(D30v445) || event_press(D30v444) || event_press(D30v443) ){combo_stop(D30v101);} 					} 			if(D30v370){ 				D30v255(); 			} 			if (D30v368) { 				D30v245(); 							} 			if (event_release(D30v443)) { 				D30v526 = 1; 							} 			if (D30v526 < 8000) { 				D30v526 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					D30v528 = !D30v528; 					D30v218(D30v528); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (D30v528) { 				if (D30v528) D30v211(D30v1063); 				if (D30v528) { 					D30v137(); 									} 							} 			else if (!get_ival(D30v443)) { 				D30v211(D30v1066); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						D30v341 = TRUE; 						D30v348 = TRUE; 						D30v347 = FALSE; 						if (!D30v341) { 							D30v347 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!D30v347) { 					if (D30v341 || D30v342) { 						vm_tctrl(0); 					} 					if (D30v341) { 						combo_stop(D30v72); 						vm_tctrl(0); 						D30v349= D30v138(D30v349,0  ); 						D30v350 = D30v138(D30v350, 1); 						D30v351  = D30v138(D30v351   ,2  ); 						D30v352  = D30v138(D30v352 , 3); 						D30v353  = D30v138(D30v353 , 4); 						D30v354 = D30v138(D30v354, 5); 						D30v355 = D30v138(D30v355, 6); 						D30v356 = D30v138(D30v356, 7); 						D30v357 = D30v138(D30v357, 8); 						D30v358 = D30v138(D30v358, 9); 						D30v359 = D30v138(D30v359, 10); 						D30v360 = D30v138(D30v360, 11); 						D30v361 = D30v138(D30v361, 12); 						D30v362 = D30v138(D30v362,13); 						D30v363 = D30v138(D30v363, 14); 						D30v364 = D30v138(D30v364, 15); 						D30v365 = D30v138(D30v365, 16); 						D30v366 = D30v138(D30v366, 17); 						D30v367 = D30v138(D30v367, 18); 						D30v368 = D30v138(D30v368, 19); 						D30v110 = D30v138(D30v110, 20); 						D30v370 = D30v138(D30v370, 21); 						D30v371              = D30v138(D30v371              ,22  ); 						D30v372              = D30v138(D30v372              ,23  ); 						D30v373               = D30v138(D30v373               ,24  ); 						if (event_press(PS4_DOWN)) { 							D30v343 = clamp(D30v343 + 1, 0, D30v374); 							D30v348 = TRUE; 													} 						if (event_press(PS4_UP)) { 							D30v343 = clamp(D30v343 - 1, 0, D30v374); 							D30v348 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							D30v341 = FALSE; 							D30v347 = FALSE; 							D30v348 = FALSE; 							vm_tctrl(0); 							combo_run(D30v75); 													} 						if (D30v550[D30v343][2] == 1) { 							if(D30v343 == 0 ){ 								if(D30v349 == 2 ){ 									if (event_press(PS4_CROSS)) { 										D30v344 = D30v550[D30v343][0]; 										D30v341 = FALSE; 										D30v342 = TRUE; 										D30v348 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									D30v344 = D30v550[D30v343][0]; 									D30v341 = FALSE; 									D30v342 = TRUE; 									D30v348 = TRUE; 																	} 															} 													} 						D30v158(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, D30v540[0]); 						D30v147(D30v343 + 1, D30v153(D30v343 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, D30v542[0]); 						D30v147(D30v922, D30v153(D30v922), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(D30v343 == 0 ){ 							if(D30v349 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, D30v544[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, D30v545[0]); 															} 													} 						else{ 							if (D30v550[D30v343][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, D30v545[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, D30v544[0]); 															} 													} 											} 					if (D30v342) { 						D30v430               = D30v141(D30v430, 0); 						D30v431               = D30v141(D30v431, 1); 						D30v432             = D30v141(D30v432, 2); 						D30v433           = D30v141(D30v433, 3); 						D30v434             = D30v141(D30v434, 4); 						D30v435             = D30v141(D30v435, 5); 						D30v436              = D30v141(D30v436, 6); 						D30v437           = D30v141(D30v437, 7); 						D30v375          = D30v141(D30v375, 8); 						D30v376   = D30v141(D30v376, 9); 						D30v377 = D30v141(D30v377, 10); 						D30v378      = D30v141(D30v378, 11); 						D30v379    = D30v141(D30v379, 12); 						D30v380    = D30v141(D30v380, 13); 						D30v381    = D30v141(D30v381, 14); 						D30v382      = D30v141(D30v382, 15); 						D30v383      = D30v141(D30v383, 16); 						D30v264              = D30v141(D30v264, 17); 						D30v265           = D30v141(D30v265, 18); 						D30v266            = D30v141(D30v266, 19); 						D30v267            = D30v141(D30v267, 20); 						D30v268= D30v141(D30v268, 21); 						D30v397               = D30v141(D30v397, 22); 						D30v398               = D30v141(D30v398, 23); 						D30v399                   = D30v141(D30v399, 24); 						D30v400                   = D30v141(D30v400, 25); 						D30v401                   = D30v141(D30v401, 26); 						D30v402   = D30v141(D30v402, 27); 						D30v403   = D30v141(D30v403, 28); 						D30v404 = D30v141(D30v404, 29); 						D30v405   = D30v141(D30v405, 30); 						D30v406   = D30v141(D30v406, 31); 						D30v407 = D30v141(D30v407, 32); 						D30v408   = D30v141(D30v408, 33); 						D30v409   = D30v141(D30v409, 34); 						D30v410 = D30v141(D30v410, 35); 						D30v411   = D30v141(D30v411, 36); 						D30v412   = D30v141(D30v412, 37); 						D30v413 = D30v141(D30v413, 38); 						D30v414   = D30v144(D30v414, 39); 						D30v415         = D30v144(D30v415, 40); 						D30v416   = D30v141(D30v416, 41); 						D30v417     = D30v141(D30v417, 42); 						D30v418                   = D30v144(D30v418, 43); 						D30v1219 = D30v141(D30v1219, 54); 						D30v1213 = D30v141(D30v1213, 55); 						D30v1220              = D30v141(D30v1220            ,56); 						D30v419               = D30v144(D30v419, 44); 						D30v420 = D30v144(D30v420, 45); 						D30v421                 = D30v144(D30v421, 46); 						D30v423               = D30v144(D30v423, 47); 						D30v424 = D30v141(D30v424, 48); 						D30v425 = D30v141(D30v425, 49); 						D30v426 = D30v141(D30v426, 50); 						D30v427 = D30v141(D30v427, 51); 						D30v428               = D30v141(D30v428, 52); 						D30v429                 = D30v141(D30v429, 53); 						D30v384       = D30v144(D30v384     ,57 ); 						D30v385       = D30v144(D30v385     ,58 ); 						D30v386      = D30v141(D30v386    ,59 ); 						D30v387   = D30v141(D30v387 ,60 ); 						D30v388       = D30v144(D30v388     ,61 ); 						D30v389       = D30v144(D30v389     ,62 ); 						D30v390   = D30v141(D30v390 ,63 ); 						D30v391   = D30v141(D30v391 ,64 ); 						D30v392      = D30v141(D30v392    ,65 ); 						D30v393          = D30v144(D30v393        ,66 ); 						D30v394          = D30v144(D30v394        ,67 ); 						D30v395         = D30v141(D30v395       ,68 ); 						D30v446             = D30v144(D30v446           ,69); 						D30v29             = D30v141(D30v29           ,70); 						D30v453           = D30v141(D30v453         ,71); 						D30v451         = D30v141(D30v451       ,72); 						D30v422		  = D30v141(D30v422      ,73); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								D30v344 = clamp(D30v344 + 1, D30v550[D30v343][0], D30v550[D30v343][1]); 								D30v348 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								D30v344 = clamp(D30v344 - 1, D30v550[D30v343][0], D30v550[D30v343][1]); 								D30v348 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							D30v341 = TRUE; 							D30v342 = FALSE; 							D30v348 = TRUE; 													} 						D30v158(); 						D30v924 = D30v808[D30v344][0]; 						D30v925 = D30v808[D30v344][1]; 						if (D30v808[D30v344][4] == 0) { 							D30v147(D30v924, D30v153(D30v924), 4, 20, OLED_FONT_SMALL); 							D30v147(D30v925, D30v153(D30v925), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (D30v348) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (D30v342) { 							print(D30v203(D30v156(D30v559[D30v344]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D30v559[D30v344]); 													} 						else { 							print(D30v203(D30v156(D30v560[D30v343]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D30v560[D30v343]); 													} 						D30v348 = FALSE; 					} 									} 				if (!D30v341 && !D30v342) { 					if (D30v347) { 						cls_oled(0); 						combo_run(D30v72); 						D30v347 = FALSE; 						D30v345 = TRUE; 						vm_tctrl(0); 					} 					if(D30v349 == 0){ 						D30v438      = PS4_CIRCLE; 						D30v439      = PS4_CROSS ; 						D30v440    = PS4_L1    ; 						D30v441  = PS4_R1; 						D30v442    = PS4_L2; 						D30v443    = PS4_R2; 						D30v444     = PS4_SQUARE; 						D30v445  = PS4_TRIANGLE; 					} 					else if(D30v349 == 1){ 						D30v438      = PS4_SQUARE; 						D30v439      = PS4_CROSS ; 						D30v440    = PS4_L1    ; 						D30v441  = PS4_R1; 						D30v442    = PS4_L2; 						D30v443    = PS4_R2; 						D30v444     = PS4_CIRCLE; 						D30v445  = PS4_TRIANGLE; 					} 					else if(D30v349 == 2){ 						D30v438      = D30v1345[D30v430]; 						D30v439      = D30v1345[D30v431] ; 						D30v440    = D30v1345[D30v432]  ; 						D30v441  = D30v1345[D30v433]; 						D30v442    = D30v1345[D30v434]; 						D30v443    = D30v1345[D30v435]; 						D30v444     = D30v1345[D30v436]; 						D30v445  = D30v1345[D30v437]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !D30v1167) { 						set_val(D30v439, 0); 						vm_tctrl(0); 						combo_run(D30v77); 											} 					if (D30v688) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							D30v564 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							D30v1021 = D30v1356[D30v564][0]; 							D30v639 = D30v1356[D30v564][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							D30v418 += 5; 							D30v199(D30v203(sizeof(D30v566) - 1, OLED_FONT_MEDIUM_WIDTH), D30v566[0], D30v418); 													} 						if (event_press(PS4_LEFT)) { 							D30v418 -= 5; 							D30v199(D30v203(sizeof(D30v566) - 1, OLED_FONT_MEDIUM_WIDTH), D30v566[0], D30v418); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !D30v586 ) { 						if (event_press(PS4_UP)) { 							D30v571 += 25; 							D30v199(D30v203(sizeof(D30v572) - 1, OLED_FONT_MEDIUM_WIDTH), D30v572[0], D30v571); 													} 						if (event_press(PS4_DOWN)) { 							D30v571 -= 25; 							D30v199(D30v203(sizeof(D30v572) - 1, OLED_FONT_MEDIUM_WIDTH), D30v572[0], D30v571); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (D30v363) { 						D30v237(); 											} 					if (D30v364) { 						D30v238(); 						D30v239(); 											} 					if (!D30v364) { 						if (get_ival(D30v438)) { 							if (event_press(PS4_RIGHT)) { 								D30v579 += 2; 								D30v199(D30v203(sizeof(D30v580) - 1, OLED_FONT_MEDIUM_WIDTH), D30v580[0], D30v579); 															} 							if (event_press(PS4_LEFT)) { 								D30v579 -= 2; 								D30v199(D30v203(sizeof(D30v580) - 1, OLED_FONT_MEDIUM_WIDTH), D30v580[0], D30v579); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(D30v440) ){ 							if(get_ival(D30v438) && get_ptime(D30v438) > D30v579){ 								set_val(D30v438,0); 															} 													} 											} 					if(D30v367){ 						D30v242(); 											} 					if (D30v359) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								D30v586 = !D30v586; 								D30v218(D30v586); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (D30v586 && D30v359) { 						vm_tctrl(0); 						combo_stop(D30v85); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								D30v414 += 10; 								D30v199(D30v203(sizeof(D30v588) - 1, OLED_FONT_MEDIUM_WIDTH), D30v588[0], D30v414); 															} 							if (event_press(PS4_DOWN)) { 								D30v414 -= 10; 								D30v199(D30v203(sizeof(D30v588) - 1, OLED_FONT_MEDIUM_WIDTH), D30v588[0], D30v414); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						D30v211(D30v1065); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								D30v593 = FALSE; 								vm_tctrl(0); 								combo_run(D30v78); 															} 							if (event_press(PS4_LEFT)) { 								D30v593 = TRUE; 								vm_tctrl(0); 								combo_run(D30v78); 															} 							set_val(PS4_L1,0); 													} 											} 					if (D30v360) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								D30v595 = !D30v595; 								D30v218(D30v595); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (D30v595 && D30v360) { 						vm_tctrl(0); 						D30v211(D30v1067); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								D30v596 = FALSE; 								vm_tctrl(0); 								combo_run(D30v79); 															} 							if (event_press(PS4_RIGHT)) { 								D30v596 = TRUE; 								vm_tctrl(0); 								combo_run(D30v79); 															} 													} 											} 					if(D30v352 || D30v353 ){ 						D30v114(); 											} 					if (D30v350) { 						if (D30v350 == D30v1071) D30v599 = TRUE; 						if (D30v350 == D30v1072) { 							if (event_press(D30v1355[-1 +D30v383]) && get_brtime(D30v1355[-1 +D30v383]) <= 200) { 								D30v599 = !D30v599; 								D30v218(D30v599); 															} 							set_val(D30v1355[-1 +D30v383], 0); 													} 						if (D30v350 > 0 && D30v350 < 3 && D30v599 == 1) { 							D30v215(); 													} 						else if (D30v350 == 3) { 							if (get_ival(D30v1355[-1 +D30v383])) { 								D30v215(); 															} 							set_val(D30v1355[-1 +D30v383], 0); 													} 											} 									if (D30v351) { 						if (D30v351 == D30v1071) D30v602 = TRUE; 						if (D30v351 == D30v1072) { 							if (event_press(D30v1355[-1 +D30v268]) && get_brtime(D30v1355[-1 +D30v268]) <= 200) { 								D30v602 = !D30v602; 								D30v218(D30v602); 															} 							set_val(D30v1355[-1 +D30v268], 0); 													} 						if (D30v351 > 0 && D30v351 < 3 && D30v602 == 1) { 							D30v213(); 													} 						else if (D30v351 == 3) { 							if (get_ival(D30v1355[-1 +D30v268])) { 								D30v213(); 															} 							set_val(D30v1355[-1 +D30v268], 0); 													} 											} 					if (D30v354) { 						if (D30v354 == 1) { 							D30v605 = PS4_R3; 							D30v602 = FALSE; 													} 						if (D30v354 == 2) { 							D30v605 = PS4_L3; 							D30v602 = FALSE; 													} 						if (D30v354 == 3) { 							D30v605 = XB1_PR1; 							D30v602 = FALSE; 													} 						if (D30v354 == 4) { 							D30v605 = XB1_PR2; 							D30v602 = FALSE; 													} 						if (D30v354 == 5) { 							D30v605 = XB1_PL1; 							D30v602 = FALSE; 													} 						if (D30v354 == 6) { 							D30v605 = XB1_PL2; 							D30v602 = FALSE; 													} 						if(get_ival(D30v605)){ 							if(D30v397 || D30v398){ 								if( D30v397 && event_press(PS4_L1)){ 									D30v456 = FALSE; 									D30v1020 = D30v397  ; 									D30v216( D30v397   ); 								} 								if( D30v398 && event_press(PS4_R1)){ 									D30v456 = TRUE; 									D30v1020 =  D30v398 ; 									D30v216( D30v398   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( D30v399 ){ 								if(event_press(PS4_SQUARE)){ 									D30v456 = FALSE; 									D30v1020 =  D30v399  ; 													combo_stop(D30v88); 				combo_stop(D30v97); 				combo_stop(D30v89); 				combo_stop(D30v98); 				combo_stop(D30v95); 				combo_stop(D30v96); 				combo_stop(D30v92); 				combo_stop(D30v94); 				combo_stop(D30v91); 				combo_stop(D30v87); 				combo_stop(D30v85); 				combo_stop(D30v90); 				combo_stop(D30v104); 				combo_stop(D30v106); 				combo_stop(D30v100); 				combo_stop(D30v105); 				combo_stop(D30v99); 									D30v216( D30v399   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									D30v456 = TRUE; 									D30v1020 =  D30v399  ; 									D30v216( D30v399   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( D30v400 ){ 								if(event_press(PS4_CROSS)){ 									D30v456 = FALSE; 									D30v1020 =  D30v400  ; 									D30v216( D30v400   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(D30v88); 				combo_stop(D30v97); 				combo_stop(D30v89); 				combo_stop(D30v98); 				combo_stop(D30v95); 				combo_stop(D30v96); 				combo_stop(D30v92); 				combo_stop(D30v94); 				combo_stop(D30v91); 				combo_stop(D30v87); 				combo_stop(D30v85); 				combo_stop(D30v90); 				combo_stop(D30v104); 				combo_stop(D30v106); 				combo_stop(D30v100); 				combo_stop(D30v105); 				combo_stop(D30v99); 									D30v456 = TRUE; 									D30v1020 =  D30v400  ; 									D30v216( D30v400   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( D30v401 ){ 								if(event_press(PS4_R3)){ 									D30v456 = FALSE; 									D30v1020 =  D30v401  ; 									D30v216( D30v401   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(D30v605,0); 											} 					if (D30v355) { 						if (D30v403 == 1) { 							if (event_press(D30v1355[-1 + D30v402]) && !D30v1118) { 								vm_tctrl(0); 								combo_run(D30v82); 															} 							else if (event_press(D30v1355[-1 + D30v402]) && D30v1118) { 								set_val(D30v1355[-1 + D30v402], 0); 								D30v456 = !D30v404; 								D30v1020 = D30v355; 								D30v216(D30v355); 															} 													} 						else { 							if (event_press(D30v1355[-1 + D30v402])) { 								D30v456 = !D30v404; 								set_val(D30v1355[-1 + D30v402], 0); 								D30v1020 = D30v355; 								D30v216(D30v355); 															} 													} 					} 					if (D30v357) { 						if (D30v409 == 1) { 							if (event_press(D30v1355[-1 +D30v408]) && !D30v1118) { 								vm_tctrl(0); 								combo_run(D30v82); 															} 							else if (event_press(D30v1355[-1 +D30v408]) && D30v1118) { 								set_val(D30v1355[-1 +D30v408], 0); 								D30v456 = !D30v410; 								D30v1020 = D30v357; 								D30v216(D30v357); 															} 													} 						else { 							if (event_press(D30v1355[-1 +D30v408])) { 								D30v456 = !D30v410; 								set_val(D30v1355[-1 +D30v408], 0); 								D30v1020 = D30v357; 								D30v216(D30v357); 															} 													} 					} 					if (D30v356) { 						if (D30v406 == 1) { 							if (event_press(D30v1355[-1 +D30v405]) && !D30v1118) { 								vm_tctrl(0); 								combo_run(D30v82); 															} 							else if (event_press(D30v1355[-1 +D30v405]) && D30v1118) { 								set_val(D30v1355[-1 +D30v405], 0); 								D30v456 = !D30v407; 								D30v1020 = D30v356; 								D30v216(D30v356); 															} 													} 						else { 							if (event_press(D30v1355[-1 +D30v405])) { 								D30v456 = !D30v407; 								set_val(D30v1355[-1 +D30v405], 0); 								D30v1020 = D30v356; 								D30v216(D30v356); 															} 													} 					} 					if (D30v358) { 						if (D30v412 == 1) { 							if (event_press(D30v1355[-1 +D30v411]) && !D30v1118) { 								vm_tctrl(0); 								combo_run(D30v82); 															} 							else if (event_press(D30v1355[-1 +D30v411]) && D30v1118) { 								set_val(D30v1355[-1 +D30v411], 0); 								D30v456 = !D30v413; 								D30v1020 = D30v358; 								D30v216(D30v358); 															} 													} 						else { 							if (event_press(D30v1355[-1 +D30v411])) { 								D30v456 = !D30v413; 								set_val(D30v1355[-1 +D30v411], 0); 								D30v1020 = D30v358; 								D30v216(D30v358); 															} 													} 					} 					if (D30v1020 == D30v299 && combo_running(D30v30)) set_val(D30v440, 100); 					if(D30v372){ 						if(!block){ 							if(!get_val(D30v442)){ 								if( !get_val(D30v443)){ 									if(get_val(D30v439)){ 										D30v621 += get_rtime(); 																			} 									if(D30v392){ 										if(get_ival(D30v439) && get_ptime(D30v439) > D30v389){ 											set_val(D30v439,0); 																					} 																			} 									if(event_release(D30v439)){ 										if( D30v621 < D30v388 ){ 											D30v622 = D30v388 - D30v621; 											combo_run(D30v104); 																					} 										else{ 											if(D30v390 || D30v391) combo_restart(D30v105); 																					} 										D30v621 = 0; 																			} 																	} 							} 						} 											} 					if(D30v371){ 						if(!block){ 							if(!get_ival(D30v442)){ 								if( !get_ival(D30v443)){ 									if(get_ival(D30v445)){ 										D30v623 += get_rtime(); 																			} 									if(event_release(D30v445)){ 										if(D30v623 < D30v384){ 											D30v624 = D30v384 - D30v623; 											combo_run(D30v106); 																					} 										else{ 											if(D30v387) combo_run(D30v99); 																					} 										D30v623 = 0; 																			} 																	} 							} 						} 											} 					if(D30v373){ 						if(!block){ 							if(get_ival(D30v444)){ 								D30v625 += get_rtime(); 															} 							if(D30v395){ 								if(get_ival(D30v444) && get_ptime(D30v444) > D30v394){ 									set_val(D30v444,0); 																	} 															} 							if(event_release(D30v444)){ 								if(D30v625 && (D30v625 < D30v393)){ 									D30v626 = D30v393 - D30v625; 									combo_run(D30v100); 																	} 								D30v625 = 0; 															} 													} 											} 					if (D30v361) { 						if (event_press(D30v1355[-1 +D30v416])) { 							vm_tctrl(0); 							combo_run(D30v77); 													} 						set_val(D30v1355[-1 +D30v416], 0); 											} 					if(!D30v365){ 						D30v419 = 0 ; 						D30v420 = 0; 						D30v423 = 0; 						D30v421 = 0; 											} 					if (D30v366) { 						D30v238(); 						if (D30v424 == 0) { 							D30v629 = FALSE; 													} 						else { 							D30v629 = TRUE; 													} 						if (D30v425 == 0) { 							D30v631 = FALSE; 													} 						else { 							D30v631 = TRUE; 													} 						if (D30v426 == 0) { 							D30v633 = FALSE; 													} 						else { 							D30v633 = TRUE; 													} 						if (D30v427 == 0) { 							D30v635 = FALSE; 													} 						else { 							D30v635 = TRUE; 													} 						if(D30v426 == 6 || D30v425 == 6 || D30v424 == 6){ 							if (get_ival(D30v1355[-1 + D30v426]) || get_ival(D30v1355[-1 + D30v425]) || get_ival(D30v1355[-1 + D30v424])){ 								combo_run(D30v0); 															} 													} 						if (D30v633) { 							if (get_val(D30v1355[-1 + D30v426])) { 								set_val(D30v1355[-1 + D30v426], 0); 								combo_run(D30v97); 								D30v1176 = 9000; 															} 													} 						if (D30v635) { 							if (get_val(D30v1355[-1 + D30v427])) { 								set_val(D30v1355[-1 + D30v427], 0); 								combo_run(D30v98); 								D30v1176 = 9000; 							} 							if (combo_running(D30v98)) { 								if(event_press(D30v438)){ 									combo_stop(D30v98); 									set_val(D30v438,100); 							} 								if (get_ival(D30v439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D30v443) > 30) { 									combo_stop(D30v98); 																	} 															} 													} 						if (D30v631) { 							if (get_val(D30v1355[-1 + D30v425])) { 								set_val(D30v1355[-1 + D30v425], 0); 								D30v244(); 								D30v1176 = 9000; 															} 													} 						if (D30v629) { 							if (get_val(D30v1355[-1 + D30v424])) { 								set_val(D30v1355[-1 + D30v424], 0); 								combo_run(D30v95); 								D30v1176 = 9000; 															} 													} 											} 					if (D30v368) { 						D30v245(); 											} 									} 				}else if(get_ival(D30v443)){ 				combo_stop(D30v97); 				combo_stop(D30v89); 				combo_stop(D30v98); 				combo_stop(D30v95); 				combo_stop(D30v96); 				combo_stop(D30v92); 				combo_stop(D30v94); 				combo_stop(D30v91); 				combo_stop(D30v87); 				combo_stop(D30v85); 				combo_stop(D30v90); 				combo_stop(D30v104); 				combo_stop(D30v106); 				combo_stop(D30v100); 				combo_stop(D30v105); 				combo_stop(D30v99); 				} 					} 		else { 			if (!get_ival(D30v443)) D30v211(D30v1064); 					} 			} 			D30v257(); 	} combo D30v1 { 	set_val(D30v442, 100); 	set_val(D30v440,100); 	D30v232(); 	wait(400); 	set_val(D30v439,100); 	wait(90); 	wait( 400); 	} combo D30v2 { 	set_val(D30v442, 100); 	set_val(D30v440,100); 	D30v232(); 	wait(400); 	set_val(D30v438,100); 	wait(220); 	wait( 400); 	} combo D30v3 { 	call(D30v28); 	wait( 100); 	call(D30v98); 	D30v228(D30v1021, D30v639); 	wait( 800); 	wait( 350); 	set_val(D30v441,100); 	set_val(D30v440,100); 	wait( 400); 	} combo D30v4 { 	D30v234(); 	wait(45); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v225(D30v1122,D30v641); 	wait(45); 	D30v232(); 	wait(45); 	vm_tctrl(0); 	wait(350); 	} combo D30v5 { 	D30v232(); 	wait( D30v1024 + random(1,5)); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v6 { 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	D30v228(D30v1122, D30v641); 	wait( D30v1024 + random(1,5)); 	D30v228(D30v1122, D30v641); 	wait( 1000); 	wait( 350); 	} combo D30v7 { 	D30v235(); 	D30v456 = FALSE; 	wait(D30v1024 + random(1,5)); 	D30v232(); 	wait(D30v1024 + random(1,5)); 	D30v235(); 	wait(D30v1024 + random(1,5)); 	D30v456 = TRUE; 	D30v232(); 	wait(D30v1024 + random(1,5)); 	wait(350); 	} combo D30v8 { 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v456 = TRUE; 	D30v232(); 	wait( D30v1024 + random(1,5)); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v456 = FALSE; 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v9 { 	D30v456 = TRUE; 	D30v232(); 	wait(D30v1024 + random(1,5)); 	D30v235(); 	wait(D30v1024 + random(1,5)); 	D30v456 = FALSE; 	D30v232(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v10 { 	D30v456 = FALSE; 	D30v232(); 	wait( D30v1024 + random(1,5)); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v456 = TRUE; 	D30v232(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v11 { 	D30v228(0,0); 	set_val(D30v440,100); 	set_val(D30v441,100); 	D30v234(); 	wait( 60); 	wait( 60); 	} combo D30v12 { 	set_val(D30v1087, inv(D30v1021)); 	set_val(D30v1088, inv(D30v639)); 	set_val(D30v441, 100); 	set_val(D30v440, 100); 	wait( 60); 	set_val(D30v1087, inv(D30v1021)); 	set_val(D30v1088, inv(D30v639)); 	set_val(D30v441, 100); 	set_val(D30v440, 100); 	wait( 500); 	wait( 350); 	} combo D30v13 { 	D30v228(0, 0); 	set_val(D30v442, 100); 	wait( 60); 	D30v228(0, 0); 	set_val(D30v442, 100); 	set_val(D30v438, 100); 	wait( 60); 	D30v228(0, 0); 	set_val(D30v442, 100); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 80); 	D30v228(0, 0); 	set_val(D30v442, 100); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	wait( 350); 	} combo D30v14 { 	set_val(D30v438, 100); 	wait( 60); 	D30v228(inv(D30v1021), inv(D30v639)); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 80); 	D30v228(inv(D30v1021), inv(D30v639)); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	wait( 350); 	} combo D30v15 { 	set_val(D30v440, 100); 	D30v232(); 	wait( 500); 	wait( 350); 	} combo D30v16 { 	D30v235(); 	wait( D30v1024 + random(1,5)); 	if(D30v456) D30v657 = D30v564 + 3; 	else  D30v657 = D30v564 - 3; 	D30v223(D30v657); 	D30v225(D30v1122,D30v641); 	wait(D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	if(D30v456) D30v657 = D30v564 + 1; 	else  D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v225(D30v1122,D30v641); 	wait(D30v1024 + random(1,5)); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v17 { 	set_val(D30v440,100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	set_val(D30v440,100); 	wait( D30v1024 + random(1,5)); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v18 { 	set_val(D30v442,100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442,100); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442,100); 	D30v232(); 	set_val(D30v442,100); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	set_val(D30v442,100); 	set_val(D30v443,100); 	wait(50); 	wait(350); 	} combo D30v19 { 	set_val(D30v442,100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442,100); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442,100); 	D30v232(); 	set_val(D30v442,100); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v20 { 	D30v233(); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	D30v232()    	wait( D30v1024 + random(1,5)); 	D30v456 = !D30v456; 	D30v231(); 	wait( 1000); 	wait( 350); 	} combo D30v21 { 	set_val(D30v440,100); 	D30v235(); 	wait(50); 	D30v228(0,0); 	set_val(D30v440,100); 	wait(50); 	set_val(D30v440,100); 	D30v235(); 	wait(50); 	wait( 350); 	} combo D30v22 { 	D30v228(0, 0); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v23 { 	D30v235(); 	wait(D30v1024 + random(1,5)); 	D30v236()wait(D30v1024 + random(1,5)); 	D30v235(); 	wait(D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v24 { 	set_val(D30v441, 100); 	set_val(D30v440, 100); 	wait( 20); 	set_val(D30v441, 100); 	set_val(D30v440, 100); 	if (D30v456) D30v657 = D30v564 + 4; 	else { 		D30v657 = D30v564 - 4; 			} 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	set_val(D30v443, 100); 	wait( 100); 	wait( 350); 	} combo D30v25 { 	set_val(D30v442, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442, 100); 	wait( 30); 	set_val(D30v442, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v26 { 	set_val(D30v442, 100); 	D30v234(); 	wait( 70); 	set_val(D30v442, 100); 	D30v236(); 	wait( 60); 	set_val(D30v442, 100); 	D30v235(); 	wait( 60); 	wait( 350); 	} combo D30v27 { 	set_val(D30v442, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442, 100); 	wait( 30); 	set_val(D30v442, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v228(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} combo D30v28 { 	D30v232(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo D30v29 { 	D30v232(); 	set_val(D30v443, 0); 	wait(350); 	wait( 350); 	} combo D30v30 { 	if (D30v1020 == D30v301) D30v1025 = 200; 	else D30v1025 = 1; 	wait( D30v1025); 	D30v234(); 	wait( 70); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( 70); 	wait( 350); 	} combo D30v31 { 	set_val(D30v440, 100)D30v234(); 	D30v228(D30v1021,D30v639); 	wait( 50); 	set_val(D30v440, 100)D30v236(); 	D30v228(D30v1021,D30v639); 	wait( 50); 	set_val(D30v440, 100)D30v232(); 	D30v228(D30v1021,D30v639); 	wait( 50); 	D30v228(D30v1021,D30v639); 	wait(465); 	D30v228(D30v1021,D30v639); 	set_val(D30v442, 100); 	set_val(D30v443, 100); 	wait(50); 	if (D30v456) D30v657 = D30v564 - 1; 	else D30v657 = D30v564 + 1; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 50); 	if (D30v456) D30v657 = D30v564 + 4; 	else D30v657 = D30v564 - 4; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 700); 	wait( 350); 	} combo D30v32 { 	if (D30v1020 == D30v301) D30v1025 = 200; 	else D30v1025 = 1; 	set_val(D30v442,100); 	wait( D30v1025); 	D30v234(); 	set_val(D30v442,100); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	set_val(D30v442,100); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	set_val(D30v442,100); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v33 { 	if (D30v456) D30v657 = D30v564 - 2; 	else D30v657 = D30v564 + 2; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	wait( 280); 	D30v236(); 	wait( 50); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 45); 	set_val(D30v438, 100); 	D30v228(D30v1122, D30v641); 	wait( 45); 	D30v228(D30v1122, D30v641); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 45); 	D30v228(D30v1122, D30v641); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 45); 	D30v228(D30v1122, D30v641); 	wait( 100); 	D30v228(D30v1122, D30v641); 	wait( 500); 	wait( 350); 	} combo D30v34 { 	D30v232(); 	wait( 280); 	D30v231()  set_val(D30v438, 100); 	set_val(D30v442, 100); 	wait( 60); 	D30v231()  set_val(D30v442, 100); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 60); 	D30v231()  set_val(D30v442, 100); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	wait( 250); 	D30v231()   	wait( 300); 	wait( 350); 	} combo D30v35 { 	D30v232(); 	wait( 300); 	D30v234(); 	wait( 60); 	wait( 350); 	} combo D30v36 { 	set_val(D30v440, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v440, 100); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v440, 100); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v37 { 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v38 { 	set_val(D30v440, 100); 	D30v233(); 	wait( 60); 	set_val(D30v440, 100); 	D30v236(); 	wait( 60); 	set_val(D30v440, 100); 	D30v232(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo D30v39 { 	D30v235(); 	set_val(D30v440,100); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	set_val(D30v440,100); 	wait( 70); 	D30v232(); 	set_val(D30v440,100); 	wait( 70); 	wait( 350); 	} combo D30v40 { 	if (D30v456) D30v657 = D30v564 + 3; 	else D30v657 = D30v564 - 3; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	set_val(D30v438, 100); 	set_val(D30v442,100); 	wait( 60); 	set_val(D30v442,100); 	D30v228(D30v1122, D30v641); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 80); 	set_val(D30v442,100); 	D30v228(D30v1122, D30v641); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	set_val(D30v442,100); 	D30v228(D30v1122, D30v641); 	wait( 300); 	wait( 350); 	} combo D30v41 { 	set_val(D30v440, 100); 	D30v234(); 	D30v228(0, 0); 	wait( D30v1024 + random(1,5)); 	set_val(D30v440, 100); 	D30v236(); 	D30v228(0, 0); 	wait( 65); 	set_val(D30v440, 100); 	D30v228(0, 0); 	D30v235(); 	wait( D30v1024 + random(1,5)); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	set_val(D30v443,0); 	D30v228(D30v1122, D30v641); 	wait( 200); 	set_val(D30v443,0); 	wait( 350); 	} combo D30v42 { 	if (D30v1020 == D30v301) D30v1025 = 200; 	else D30v1025 = 1; 	wait( D30v1025); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	wait( 350); 	} combo D30v43 { 	D30v235(); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} combo D30v44 { 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	if (D30v1020 == D30v314) D30v231(); 	set_val(D30v442, 100); 	set_val(D30v443, 100); 	wait( 200); 	if (D30v1020 == D30v314) D30v231(); 	wait( 300); 	wait( 350); 	} combo D30v45 { 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v236(); 	wait( D30v1024 + random(1,5)); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	if (D30v1020 == D30v314) D30v231(); 	set_val(D30v442, 100); 	set_val(D30v443, 100); 	wait( 200); 	if (D30v1020 == D30v314) D30v231(); 	wait( 300); 	wait( 350); 	} combo D30v46 { 	call(D30v33)call(D30v35); 	} combo D30v47 {    D30v688 = FALSE; 	D30v228(D30v1021, D30v639); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v228(D30v1021, D30v639); 	D30v236(); 	D30v688 = FALSE; 	wait( D30v1024 + random(1,5)); 	D30v228(D30v1021, D30v639); 	D30v232(); 	D30v688 = FALSE; 	wait( D30v1024 + random(1,5)); 	set_val(D30v442, 100); 	set_val(D30v443, 100); 	D30v228(inv(D30v1021), inv(D30v639)); 	D30v688 = FALSE; 	wait( 400); 	wait( 350); 	D30v688 = TRUE; 	} combo D30v48 { 	D30v228(D30v1021, D30v639); 	set_val(XB1_LS, 100); 	D30v234(); 	wait( D30v1024 + random(1,5)); 	D30v228(D30v1021, D30v639); 	D30v236(); 	set_val(XB1_LS, 100); 	wait( D30v1024 + random(1,5)); 	D30v228(D30v1021, D30v639); 	D30v232(); 	wait( D30v1024 + random(1,5)); 	set_val(D30v442, 100); 	set_val(D30v443, 100); 	if (D30v456) D30v657 = D30v564 + 4; 	else D30v657 = D30v564 - 4; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 220); 	if (D30v456) D30v657 = D30v564 + 4; 	else D30v657 = D30v564 - 4; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 60); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	wait( 600); 	wait( 350); 	} combo D30v49 { 	set_val(D30v439, 0); 	set_val(D30v438, 100); 	wait( 80); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 80); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo D30v50 { 	set_val(D30v438, 100); 	set_val(D30v443,100); 	wait( 60); 	set_val(D30v443,100); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	set_val(D30v443,100); 	wait( 60); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	set_val(D30v443,100); 	wait( 60); 	wait( 350); 	} combo D30v51 { 	set_val(D30v440,100); 	set_val(D30v441,100); 	D30v228(inv(D30v1021), inv(D30v639)); 	wait( 200); 	set_val(D30v440,100); 	set_val(D30v441,100); 	D30v456 = FALSE; 	D30v231(); 	wait( 50); 	set_val(D30v440,100); 	set_val(D30v441,100); 	D30v456 = !D30v456; 	D30v231(); 	set_val(D30v440,100); 	set_val(D30v441,100); 	wait( 540); 	wait( 350); 	} combo D30v52 { 	set_val(D30v438, 100); 	wait( 60); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 60); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} combo D30v53 { 	D30v228(inv(D30v1021), inv(D30v639)); 	set_val(D30v442, 100); 	set_val(D30v438, 100); 	wait( 60); 	D30v228(inv(D30v1021), inv(D30v639)); 	set_val(D30v442, 100); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 60); 	D30v228(inv(D30v1021), inv(D30v639)); 	set_val(D30v442, 100); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	D30v228(0, 0); 	wait( 300); 	wait( 350); 	} combo D30v54 { 	set_val(D30v440, 100); 	set_val(D30v444, 100); 	wait( 60); 	set_val(D30v440, 100); 	set_val(D30v444, 100); 	set_val(D30v439, 100); 	wait( 60); 	set_val(D30v440, 100); 	set_val(D30v444, 0); 	set_val(D30v439, 100); 	D30v231(); 	wait( 60); 	set_val(D30v440, 100); 	D30v231(); 	wait( 300); 	wait( 350); 	} combo D30v55 { 	set_val(D30v438, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo D30v56 { 	set_val(D30v438, 100); 	set_val(D30v442,100); 	wait( 60); 	set_val(D30v442,100); 	set_val(D30v438, 100); 	set_val(D30v439, 100); 	wait( 60); 	set_val(D30v442,100); 	set_val(D30v438, 0); 	set_val(D30v439, 100); 	wait( 60); 	wait( 350); 	} combo D30v57 { 	set_val(D30v440, 100); 	D30v234(); 	wait( 300); 	wait( 350); 	} combo D30v58 { 	D30v235(); 	wait( 70); 	D30v236(); 	wait( 70); 	D30v234(); 	wait( 70); 	wait( 350); 	} combo D30v59 { 	set_val(D30v440,100); 	D30v235(); 	wait( 70); 	set_val(D30v440,100); 	D30v236(); 	wait( 70); 	D30v234(); 	set_val(D30v440,100); 	wait(50); 	wait( 350); 	} combo D30v60 { 	D30v228(D30v1021, D30v639); 	D30v235(); 	wait( 100); 	D30v236(); 	D30v228(D30v1021, D30v639); 	wait( 60); 	D30v234(); 	D30v228(D30v1021, D30v639); 	wait( 320); 	D30v228(D30v1021, D30v639); 	D30v236(); 	wait( 220); 	D30v228(D30v1021, D30v639); 	D30v234(); 	D30v228(D30v1021, D30v639); 	wait( 100); 	wait( 350); 	} combo D30v61 { 	call(D30v83); 	D30v228(0, 0); 	call(D30v84); 	call(D30v84); 	call(D30v84); 	call(D30v84); 	call(D30v84); 	set_val(D30v442, 100); 	D30v235(); 	wait( 70); 	set_val(D30v442, 100); 	D30v236(); 	wait( 60); 	set_val(D30v442, 100); 	D30v234(); 	wait( 60); 	set_val(D30v442, 100); 	wait( 600); 	wait( 350); 	} combo D30v62 { 	set_val(D30v442,100); 	set_val(D30v441,100); 	if (D30v456) D30v657 = D30v564 - 2; 	else D30v657 = D30v564 + 2; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	wait(50); 	set_val(D30v441,100); 	set_val(D30v442,100); 	if (D30v456) D30v657 = D30v564 - 3; 	else D30v657 = D30v564 + 3; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	wait(50); 	set_val(D30v441,100); 	set_val(D30v442,100); 	if (D30v456) D30v657 = D30v564 - 4; 	else D30v657 = D30v564 + 4; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	wait(50); 	set_val(D30v441,100); 	set_val(D30v442,100); 	if (D30v456) D30v657 = D30v564 - 5; 	else D30v657 = D30v564 + 5; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	set_val(D30v442,100); 	set_val(D30v441,100); 	wait(50); 	set_val(D30v441,100); 	set_val(D30v442,100); 	if (D30v456) D30v657 = D30v564 - 6; 	else D30v657 = D30v564 + 6; 	D30v223(D30v657); 	D30v225(D30v1122, D30v641); 	wait(50); 	} combo D30v63 { 	wait( 100); 	D30v228(0, 0); 	D30v234(); 	wait( 70); 	D30v228(0, 0); 	D30v236()   	wait( 70); 	D30v228(0, 0); 	D30v234()   	wait( 70); 	D30v228(0, 0); 	D30v236()   	wait( 70); 	D30v228(0, 0); 	D30v235(); 	wait( 70); 	D30v228(0, 0); 	wait( 350); 	} combo D30v64 { 	set_val(PS4_R3,100); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	D30v228(D30v1122, D30v641); 	wait( 70); 	D30v228(D30v1122, D30v641); 	wait( 400); 	wait( 350); 	} combo D30v65 { 	call(D30v83); 	D30v228(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (D30v456) D30v657 = D30v564 + 1; 	else D30v657 = D30v564 - 1; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	D30v228(D30v1122, D30v641); 	wait( 70); 	D30v228(D30v1122, D30v641); 	wait( 400); 	wait( 350); 	} combo D30v66 { 	call(D30v83); 	D30v228(0,0); 	set_val(D30v442,100); 	set_val(D30v443,100); 	wait( 750); 	} combo D30v67 { 	set_val(PS4_R3,100); 	if (D30v456) D30v657 = D30v564 + 2; 	else D30v657 = D30v564 - 2; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	D30v228(D30v1122, D30v641); 	wait( 70); 	D30v228(D30v1122, D30v641); 	wait( 400); 	wait( 350); 	} combo D30v68 { 	set_val(D30v442,100); 	set_val(PS4_R3,100); 	if (D30v456) D30v657 = D30v564 ; 	else D30v657 = D30v564; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	D30v228(D30v1122, D30v641); 	wait( 70); 	set_val(D30v442,100); 	D30v228(D30v1122, D30v641); 	wait( 400); 	wait( 350); 	} combo D30v69 { 	set_val(D30v442,100); 	set_val(PS4_R3,100); 	if (D30v456) D30v657 = D30v564 ; 	else D30v657 = D30v564; 	D30v223(D30v657); 	D30v228(D30v1122, D30v641); 	D30v228(D30v1122, D30v641); 	wait( 70); 	set_val(D30v442,100); 	D30v228(D30v1122, D30v641); 	wait(800); 	call(D30v98); 	} combo D30v70 { 	D30v228(0,0); 	set_val(D30v441,100); 	set_val(D30v440,100); 	D30v232(); 	wait( 350); 	wait( 350); 	set_val(D30v441,100); 	set_val(D30v440,100); 	wait( 400); 	} int D30v130 ; int D30v733 ; int D30v734 ; int D30v735; int D30v736; function D30v116(D30v117){ 	D30v733 = 2; 	D30v734 = 987654; 	D30v130 = 54321; 	D30v735 = (D30v117 >> D30v733) | (D30v117 << (32 - D30v733)); 	D30v736 = (((D30v735 >> ((D30v735 & 0xF) % 13)) & 0x7FFFF) + D30v130) % D30v734 + 123456; 	return D30v736; 	} define D30v738 = -1; define D30v507 = -2; define D30v740 = -3; define D30v741 = 0; define D30v508 = 1; function D30v118(D30v117, D30v120, D30v121) { 	if(D30v117 > D30v121) return D30v120; 	if(D30v117 < D30v120) return D30v121; 	return D30v117; 	} int D30v745,D30v746; function D30v122(D30v123,D30v124,D30v125,D30v126,D30v127,D30v128){ 	if(!D30v128){ 		print(D30v131(D30v129(D30v123),D30v126,D30v124),D30v125,D30v126,D30v127,D30v123)     	} 	else{ 		if(D30v123 < 0){ 			putc_oled(1,45); 					} 		if(D30v123){ 			for(D30v745 = D30v135(D30v123) + D30v746 = (D30v123 < 0 ),D30v123 = abs(D30v123); 			D30v123 > 0; 			D30v745-- , D30v746++){ 				putc_oled(D30v745,D30v123%10 + 48); 				D30v123 = D30v123/10; 							} 					} 		else{ 			putc_oled(1,48); 			D30v746 = 1         		} 		puts_oled(D30v131(D30v746,D30v126,D30v124),D30v125,D30v126,D30v746 ,D30v127); 			} 	} int D30v767; function D30v129(D30v130) { 	D30v767 = 0; 	do { 		D30v130++; 		D30v767++; 			} 	while (duint8(D30v130)); 	return D30v767; 	} function D30v131(D30v132,D30v126,D30v124) { 	if(D30v124 == -3){ 		return 128 - ((D30v132 * (7 + (D30v126 > 1) + D30v126 * 4)) + 3 ); 			} 	if(D30v124 == -2){ 		return 64 - ((D30v132 * (7 + (D30v126 > 1) + D30v126 * 4)) / 2); 			} 	if(D30v124 == -1){ 		return 3 	} 	return D30v124; 	} function D30v135(D30v136) { 	for(D30v745 = 1; 	D30v745 < 11; 	D30v745++){ 		if(!(abs(D30v136) / pow(10,D30v745))){ 			return D30v745; 			break; 					} 			} 	return 1; 	} function D30v137() { 	if (get_ival(D30v438)) { 		set_val(D30v438, 0); 		if (get_ival(D30v440)) D30v774 = 50; 		if (!get_ival(D30v440)) D30v774 = 410; 		combo_run(D30v71); 			} 	if (D30v773 > 0) set_polar(POLAR_LS, D30v773 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) D30v773 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) D30v773 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) D30v773 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) D30v773 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D30v773 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D30v773 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D30v773 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D30v773 = 270; } int D30v774; int D30v528; int D30v773; combo D30v71 { 	set_val(D30v438, 100); 	vm_tctrl(0);wait( D30v774); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	D30v528 = !D30v528; } define D30v777 = 19; function D30v138(D30v139, D30v140) { 	if (D30v343 == D30v140) { 		if (event_press(PS4_RIGHT)) { 			D30v139 = clamp(D30v139 + 1, 0, D30v780[D30v343]); 			D30v348 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			D30v139 = clamp(D30v139 - 1, 0, D30v780[D30v343]); 			D30v348 = TRUE; 					} 		if (D30v343 == 0) { 			print(D30v203(D30v156(D30v784[D30v349]) ,OLED_FONT_SMALL_WIDTH),D30v777  ,OLED_FONT_SMALL , OLED_WHITE ,D30v784[D30v349]); 					} 		else if (D30v343 == 1) { 			print(D30v203(D30v156(D30v786[D30v350]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v786[D30v350]); 					} 		else if (D30v343 == 2) { 			print(D30v203(D30v156(D30v786[D30v351]) ,OLED_FONT_SMALL_WIDTH ),D30v777  ,OLED_FONT_SMALL , OLED_WHITE ,D30v786[D30v351]); 					} 		else if (D30v343 == 5) { 			print(D30v203(D30v156(D30v790[D30v354]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v790[D30v354]); 					} 		else if (D30v343 == 6) { 			print(D30v203(D30v156(D30v792[D30v355]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v355]); 					} 		else if (D30v343 == 7) { 			print(D30v203(D30v156(D30v792[D30v356]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v356]); 					} 		else if (D30v343 == 8) { 			print(D30v203(D30v156(D30v792[D30v357]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v357]); 					} 		else if (D30v343 == 9) { 			print(D30v203(D30v156(D30v792[D30v358]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v358]); 					} 		else if (D30v343 == 20) { 			print(D30v203(D30v156(D30v800[D30v110]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v800[D30v110]); 					} 		else { 			if (D30v139 == 1)        print(D30v203(D30v156(D30v802[1]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v802[1])      else        print(D30v203(D30v156(D30v802[0]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v802[0])     		} 			} 	return D30v139; 	} function D30v141(D30v139, D30v140) { 	if (D30v344 == D30v140) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D30v139 += D30v808[D30v344][2]  				        D30v348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D30v139 -= D30v808[D30v344][2]  				        D30v348 = TRUE; 							} 			D30v139 = clamp(D30v139, D30v808[D30v344][0], D30v808[D30v344][1]); 		} 		if (D30v344 == 8) { 			print(D30v203(D30v156(D30v792[D30v375]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v375])     		} 		else if (D30v344 == 9) { 			print(D30v203(D30v156(D30v792[D30v376]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v376])     		} 		else if (D30v344 == 10) { 			print(D30v203(D30v156(D30v792[D30v377]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v377])     		} 		else if (D30v344 == 11) { 			print(D30v203(D30v156(D30v792[D30v378]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v378])     		} 		else if (D30v344 == 12) { 			print(D30v203(D30v156(D30v792[D30v379]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v379])     		} 		else if (D30v344 == 13) { 			print(D30v203(D30v156(D30v792[D30v380]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v380])     		} 		else if (D30v344 == 14) { 			print(D30v203(D30v156(D30v792[D30v381]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v381])     		} 		else if (D30v344 == 15) { 			print(D30v203(D30v156(D30v792[D30v382]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v382])     		} 		else if (D30v344 == 16) { 			print(D30v203(D30v156(D30v827[D30v383]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v383])     		} 		else if (D30v344 == 17) { 			print(D30v203(D30v156(D30v792[D30v264]),OLED_FONT_SMALL_WIDTH ),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v792[D30v264])  		} 		else if(D30v344 == 18){ 			print(D30v203(D30v156(D30v792[D30v265]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v792[D30v265])  		} 		else if(D30v344 == 19){ 			print(D30v203(D30v156(D30v792[D30v266]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v792[D30v266])  		} 		else if(D30v344 == 20){ 			print(D30v203(D30v156(D30v792[D30v267]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v792[D30v267])  		} 		else if(D30v344 == 21){ 			print(D30v203(D30v156(D30v827[D30v268]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v827[D30v268])       		} 		else if(D30v344 == 22){ 			print(D30v203(D30v156(D30v792[D30v397]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v397])     		} 		else if (D30v344 == 23) { 			print(D30v203(D30v156(D30v792[D30v398]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v398])     		} 		else if (D30v344 == 24) { 			print(D30v203(D30v156(D30v792[D30v399]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v399])     		} 		else if (D30v344 == 25) { 			print(D30v203(D30v156(D30v792[D30v400]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v400])     		} 		else if (D30v344 == 26) { 			print(D30v203(D30v156(D30v792[D30v401]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v792[D30v401])     		} 		else if (D30v344 == 27) { 			print(D30v203(D30v156(D30v827[D30v402]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v402])     		} 		else if (D30v344 == 28) { 			print(D30v203(D30v156(D30v851[D30v403]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v851[D30v403])     		} 		else if (D30v344 == 29) { 			print(D30v203(D30v156(D30v853[D30v404]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v853[D30v404])     		} 		else if (D30v344 == 30) { 			print(D30v203(D30v156(D30v827[D30v405]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v405])     		} 		else if (D30v344 == 31) { 			print(D30v203(D30v156(D30v851[D30v406]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v851[D30v406])     		} 		else if (D30v344 == 32) { 			print(D30v203(D30v156(D30v853[D30v407]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v853[D30v407])     		} 		else if (D30v344 == 33) { 			print(D30v203(D30v156(D30v827[D30v408]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v408])     		} 		else if (D30v344 == 34) { 			print(D30v203(D30v156(D30v851[D30v409]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v851[D30v409])     		} 		else if (D30v344 == 35) { 			print(D30v203(D30v156(D30v853[D30v410]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v853[D30v410])     		} 		else if (D30v344 == 36) { 			print(D30v203(D30v156(D30v827[D30v411]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v411])     		} 		else if (D30v344 == 37) { 			print(D30v203(D30v156(D30v851[D30v412]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v851[D30v412])     		} 		else if (D30v344 == 38) { 			print(D30v203(D30v156(D30v853[D30v413]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v853[D30v413])     		} 		else if (D30v344 == 41) { 			print(D30v203(D30v156(D30v827[D30v416]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v416])     		} 		else if (D30v344 == 48) { 			print(D30v203(D30v156(D30v827[D30v424]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v424])     		} 		else if (D30v344 == 49) { 			print(D30v203(D30v156(D30v827[D30v425]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v425])     		} 		else if (D30v344 == 50) { 			print(D30v203(D30v156(D30v827[D30v426]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v426])     		} 		else if (D30v344 == 51) { 			print(D30v203(D30v156(D30v827[D30v427]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v827[D30v427])     		} 		else if(D30v344 == 0){ 			print(D30v203(D30v156(D30v883[D30v430]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v430])  		} 		else if(D30v344 == 1){ 			print(D30v203(D30v156(D30v883[D30v431]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v431])  		} 		else if(D30v344 == 2){ 			print(D30v203(D30v156(D30v883[D30v432]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v432])  		} 		else if(D30v344 == 3){ 			print(D30v203(D30v156(D30v883[D30v433]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v433])  		} 		else if(D30v344 == 4){ 			print(D30v203(D30v156(D30v883[D30v434]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v434])  		} 		else if(D30v344 == 5){ 			print(D30v203(D30v156(D30v883[D30v435]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v435])  		} 		else if(D30v344 == 6){ 			print(D30v203(D30v156(D30v883[D30v436]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v436])  		} 		else if(D30v344 == 7){ 			print(D30v203(D30v156(D30v883[D30v437]),OLED_FONT_SMALL_WIDTH),D30v777,OLED_FONT_SMALL,OLED_WHITE,D30v883[D30v437])  		} 		else{ 			if (D30v139 == 1)        print(D30v203(D30v156(D30v802[1]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v802[1])      else        print(D30v203(D30v156(D30v802[0]), OLED_FONT_SMALL_WIDTH), D30v777, OLED_FONT_SMALL, OLED_WHITE, D30v802[0])     		} 		D30v159(0); 			} 	return D30v139; 	} function D30v144(D30v139, D30v140) { 	if (D30v344 == D30v140) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D30v139 += D30v808[D30v344][2]  				        D30v348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D30v139 -= D30v808[D30v344][2]  				        D30v348 = TRUE; 							} 			if (event_press(PS4_UP)) { 				D30v139 += D30v808[D30v344][3]  				        D30v348 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				D30v139 -= D30v808[D30v344][3]  				        D30v348 = TRUE; 							} 			D30v139 = clamp(D30v139, D30v808[D30v344][0], D30v808[D30v344][1]); 		} 		D30v206(D30v139, D30v209(D30v139)); 	} 	return D30v139; 	} int D30v910, D30v911, D30v912; function D30v147(D30v117, D30v149, D30v150, D30v151, D30v126) { 	D30v911 = 1; 	D30v912 = 10000; 	if (D30v117 < 0)  	  { 		putc_oled(D30v911, 45); 		D30v911 += 1; 		D30v117 = abs(D30v117); 			} 	for (D30v910 = 5; 	D30v910 >= 1; 	D30v910--) { 		if (D30v149 >= D30v910) { 			putc_oled(D30v911, D30v918[D30v117 / D30v912]); 			D30v117 = D30v117 % D30v912; 			D30v911 += 1; 					} 		D30v912 /= 10; 			} 	puts_oled(D30v150, D30v151, D30v126, D30v911 - 1, OLED_WHITE); } const string D30v545 = " No Edit Variable"; const string D30v544 = " A/CROSS to Edit "; const string D30v540 = "MOD;"; const string D30v542 = "MSL;"; int D30v922; function D30v153(D30v136) { 	D30v136 = abs(D30v136); 	if (D30v136 / 10000 > 0) return 5; 	if (D30v136 / 1000 > 0) return 4; 	if (D30v136 / 100 > 0) return 3; 	if (D30v136 / 10 > 0) return 2; 	return 1; 	} const int8 D30v918[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int D30v924, D30v925; const image D30v927 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo D30v72 { 	call(D30v73); 	D30v155(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, D30v927[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, D30v927[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(D30v74); 	vm_tctrl(0);wait( 1000); 	D30v345 = TRUE; 	} combo D30v73 { 	cls_oled(OLED_BLACK); 	} enum { 	D30v929 = -2, D30v930, D30v931 = 5, D30v932 = -1, D30v933 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo D30v74 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 D30v1339[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 D30v1340[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 D30v1341[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 D30v1342[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int D30v934; int D30v935; int D30v936; int D30v937; int D30v938; int D30v939; int D30v940; function D30v155() { 	D30v940 = 3; 	D30v938 = D30v940 * D30v1342[D30v939]; 	D30v937 = D30v940 * D30v1339[D30v939]; 	D30v935 = ((D30v938 * D30v1341[D30v934]) / 328) - ((D30v937 * D30v1340[D30v934]) / 328); 	D30v936 = ((D30v938 * D30v1340[D30v934]) / 328) + ((D30v937 * D30v1341[D30v934]) / 328); 	D30v938 = D30v935; 	D30v937 = D30v936; 	D30v939 += 1; 	D30v934 += 45; 	if(D30v939 >= 360) { 		D30v939 %= 360; 			} 	if(D30v934 >= 360) { 		D30v934 %= 360; 			} 	pixel_oled(64 + (((D30v938 / D30v940) * 30) / 328), 32 + (((D30v937 / D30v940) * 30) / 328), OLED_WHITE); 	} int D30v944; function D30v156(D30v130) { 	D30v944 = 0; 	do { 		D30v130++; 		D30v944++; 			} 	while (duint8(D30v130)); 	return D30v944; 	} int D30v947; const uint8 D30v1343[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function D30v158() { 	for (D30v947 = 0; 	D30v947 < sizeof(D30v1343) / sizeof(D30v1343[0]); 	D30v947++) { 		if (get_ival(D30v1343[D30v947]) || event_press(D30v1343[D30v947])) { 			set_val(D30v1343[D30v947], 0); 		} 			} 	} define D30v948 = 131; define D30v949 = 132; define D30v950 = 133; define D30v951 = 134; define D30v952 = 130; define D30v953 = 89; define D30v954 = 127; define D30v955 = 65; int D30v956; int D30v957; int D30v958 = 1; define D30v959 = 36; const string D30v960 = "Hold LT/L2 +"; function D30v159(D30v160) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, D30v960[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, D30v958); 	putc_oled(1, D30v950); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, D30v956); 	putc_oled(1, D30v951); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, D30v957); 	if (D30v160) { 		putc_oled(1, D30v948); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, D30v956); 		putc_oled(1, D30v949); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, D30v957); 			} 	} const uint8 D30v1345 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function D30v161() { 	D30v970 = sizeof(data); 	D30v485 = get_pvar(SPVAR_1,0,1,0); 	D30v491 = get_pvar(SPVAR_2,0,1,0); 	D30v484 = get_pvar(SPVAR_3,0x2B67, 0x1869F,0x2B67); 	D30v163(); 	if (D30v188(0, 1, 0)) { 		D30v354 = D30v188(  0, 6, 0); 		D30v351 = D30v188(0, 3, 0); 		D30v352 = D30v188(0,1,0); 		D30v353 = D30v188(0,1,0); 		D30v264 = D30v188(0, 70, 0); 		D30v265 = D30v188(0, 70, 0); 		D30v266 = D30v188(0, 70, 0); 		D30v267 = D30v188(0, 70, 0); 		D30v268 = D30v188(0, 22, 8); 		D30v355 = D30v188(0, 70, 0); 		D30v356 = D30v188(0, 70, 0); 		D30v357 = D30v188(0, 70, 0); 		D30v358 = D30v188(0, 70, 0); 		D30v359 = D30v188(0, 1, 0); 		D30v360 = D30v188(0, 1, 0); 		D30v361 = D30v188(0, 1, 0); 		D30v362 = D30v188(0, 1, 0); 		D30v370 = D30v188(0, 1, 0); 		D30v397 = D30v188(0, 70, 0); 		D30v398 = D30v188(0, 70, 0); 		D30v399 = D30v188(0, 70, 0); 		D30v400 = D30v188(0, 70, 0); 		D30v401 = D30v188(0, 70, 0); 		D30v402 = D30v188(1, 25, 1); 		D30v403 = D30v188(0, 1, 0); 		D30v404 = D30v188(0, 1, 0); 		D30v405 = D30v188(1, 25, 5); 		D30v406 = D30v188(0, 1, 0); 		D30v407 = D30v188(0, 1, 0); 		D30v408 = D30v188(0, 25, 2); 		D30v409 = D30v188(0, 1, 0); 		D30v410 = D30v188(0, 1, 1); 		D30v411 = D30v188(1, 25, 8); 		D30v412 = D30v188(0, 1, 0); 		D30v413 = D30v188(0, 1, 1); 		D30v414 = D30v188(350, 600, 350); 		D30v415 = D30v188(350, 600, 445); 		D30v416 = D30v188(0, 22, 0); 		D30v417 = D30v188(0, 1, 0); 		D30v418 = D30v188(-100, 300, 0); 		D30v363 = D30v188(0, 1, 0); 		D30v364 = D30v188(0, 1, 0); 		D30v365 = D30v188(0, 1, 0); 		D30v366 = D30v188(0, 1, 0); 		D30v367 = D30v188(0, 1, 0); 		D30v419 = D30v188(-150, 150, 0); 		D30v420 = D30v188(-150, 150, 0); 		D30v421 = D30v188(-150, 150, 0); 		D30v422 = D30v188(0, 1, 0); 		D30v423 = D30v188(-150, 150, 0); 		D30v424 = D30v188(0, 22, 0); 		D30v425 = D30v188(0, 22, 0); 		D30v426 = D30v188(0, 22, 0); 		D30v427 = D30v188(0, 22, 0); 		D30v579 = D30v188(60, 400, 235); 		D30v429 = D30v188(0, 1, 0); 		D30v428 = D30v188(0, 1, 0); 		D30v350 = D30v188(0, 3, 0); 		D30v375 = D30v188(0, 70, 0); 		D30v376 = D30v188(0, 70, 0); 		D30v377 = D30v188(0, 70, 0); 		D30v380 = D30v188(0, 70, 0); 		D30v381 = D30v188(0, 70, 0); 		D30v382 = D30v188(0, 70, 0); 		D30v383 = D30v188(0, 22, 8); 		D30v368 = D30v188(0, 1, 0); 		D30v378 = D30v188(0, 70, 0); 		D30v379 = D30v188(0, 70, 0); 		D30v571 = D30v188(0, 2500, 400); 		D30v1219 = D30v188(0, 1, 0); 		D30v1220 = D30v188(0, 1,  0); 		D30v1213 = D30v188(0, 1, 0); 		D30v110 = D30v188(0, 10, 0); 		D30v396 = D30v188(0, 1, 0); 		D30v349 = D30v188(0, 2, 0); 		D30v430 = D30v188(0, 9, 9); 		D30v431 = D30v188(0, 9, 8); 		D30v432 = D30v188(0, 9, 3); 		D30v433 = D30v188(0, 9, 1); 		D30v434 = D30v188(0, 9, 4); 		D30v435 = D30v188(0, 9, 0); 		D30v436 = D30v188(0, 9, 7); 		D30v437 = D30v188(0, 9, 6); 		D30v371    = D30v188(0, 1, 0); 		D30v372    = D30v188(0, 1, 0); 		D30v373     = D30v188(0, 1, 0); 		D30v384     = D30v188(60, 500, 120); 		D30v385     = D30v188(60, 500, 350); 		D30v386    = D30v188(0, 1, 0); 		D30v387 = D30v188(0, 1, 0); 		D30v388     = D30v188(50, 250, 80); 		D30v389     = D30v188(100, 850, 180); 		D30v390 = D30v188(0, 1, 0); 		D30v391 = D30v188(0, 1, 0); 		D30v392    = D30v188(0, 1, 0); 		D30v393        = D30v188(80, 500, 120); 		D30v394        = D30v188(80, 500, 350); 		D30v395       = D30v188(0, 1, 0); 		D30v446           = D30v188(3, 60, 3); 		D30v29           = D30v188(0, 1, 0); 		D30v453         = D30v188(0, 1, 0); 		D30v451       = D30v188(0, 1, 0); 	} 	else{ 		D30v354 = 0; 		D30v351 = 0; 		D30v352 = 0; 		D30v353 = 0; 		D30v264 = 0; 		D30v265 = 0; 		D30v266 = 0; 		D30v267 = 0; 		D30v268 = 8; 		D30v355 = 0; 		D30v356 = 0; 		D30v357 = 0; 		D30v358 = 0; 		D30v359 = 0; 		D30v360 = 0; 		D30v361 = 0; 		D30v362 = 0; 		D30v370 = 0; 		D30v397 = 0; 		D30v398 = 0; 		D30v399 = 0; 		D30v400 = 0; 		D30v401 = 0; 		D30v402 = 1; 		D30v403 = 0; 		D30v404 = 0; 		D30v405 = 5; 		D30v406 = 0; 		D30v407 = 0; 		D30v408 = 2; 		D30v409 = 0; 		D30v410 = 1; 		D30v411 = 8; 		D30v412 = 0; 		D30v413 = 1; 		D30v414 = 350; 		D30v415 = 445; 		D30v416 = 0; 		D30v417 = 0; 		D30v418 = 0; 		D30v363 = 0; 		D30v364 = 0; 		D30v365 = 0; 		D30v366 = 0; 		D30v367 = 0; 		D30v419 = 0; 		D30v420 = 0; 		D30v421 = 0; 		D30v422 = 0; 		D30v423 = 0; 		D30v424 = 0; 		D30v425 = 0; 		D30v426 = 0; 		D30v427 = 0; 		D30v579 = 235; 		D30v429 = 0; 		D30v428 = 0; 		D30v350 = 0; 		D30v375 = 0; 		D30v376 = 0; 		D30v377 = 0; 		D30v380 = 0; 		D30v381 = 0; 		D30v382 = 0; 		D30v383 = 8; 		D30v368 = 0; 		D30v378 = 0; 		D30v379 = 0; 		D30v571 = 400; 		D30v1219 = 0; 		D30v1220 = 0; 		D30v1213 = 0; 		D30v110 = 0; 		D30v396 = 0; 		D30v349 = 0; 		D30v430 = 9; 		D30v431 = 8; 		D30v432 = 3; 		D30v433 = 1; 		D30v434 = 4; 		D30v435 = 0; 		D30v436 = 7; 		D30v437 = 6; 		D30v371 = 0; 		D30v372 = 0; 		D30v373 = 0; 		D30v384 = 120; 		D30v385 = 350; 		D30v386 = 0; 		D30v387 = 0; 		D30v388 = 80; 		D30v389 = 180; 		D30v390 = 0; 		D30v391 = 0; 		D30v392 = 0; 		D30v393 = 120; 		D30v394 = 360; 		D30v395 = 0; 		D30v446     = 3; 		D30v29     = 0; 		D30v453     = 0; 		D30v451     = 0; 			} 	if (D30v349 == 0) { 		D30v438 = PS4_CIRCLE; 		D30v439 = PS4_CROSS; 		D30v440 = PS4_L1; 		D30v441 = PS4_R1; 		D30v442 = PS4_L2; 		D30v443 = PS4_R2; 		D30v444 = PS4_SQUARE; 		D30v445 = PS4_TRIANGLE; 			} 	else if (D30v349 == 1) { 		D30v438      = PS4_SQUARE; 		D30v439      = PS4_CROSS ; 		D30v440    = PS4_L1    ; 		D30v441  = PS4_R1; 		D30v442    = PS4_L2; 		D30v443    = PS4_R2; 		D30v444     = PS4_CIRCLE; 		D30v445  = PS4_TRIANGLE; 	} 	else if (D30v349 == 2) { 		D30v438 = D30v1345[D30v430]; 		D30v439 = D30v1345[D30v431]; 		D30v440 = D30v1345[D30v432]; 		D30v441 = D30v1345[D30v433]; 		D30v442 = D30v1345[D30v434]; 		D30v443 = D30v1345[D30v435]; 		D30v444 = D30v1345[D30v436]; 		D30v445 = D30v1345[D30v437]; 			} 	} function D30v162() { 	D30v163(); 	D30v186(   1,0,     1); 	D30v186(D30v354, 0, 6); 	D30v186(D30v351, 0, 3); 	D30v186(D30v352, 0 , 1); 	D30v186(D30v353, 0 , 1); 	D30v186(D30v264, 0, 70); 	D30v186(D30v265, 0, 70); 	D30v186(D30v266, 0, 70); 	D30v186(D30v267, 0, 70); 	D30v186(D30v268, 0, 22); 	D30v186(D30v355, 0, 70); 	D30v186(D30v356, 0, 70); 	D30v186(D30v357, 0, 70); 	D30v186(D30v358, 0, 70); 	D30v186(D30v359, 0, 1); 	D30v186(D30v360, 0, 1); 	D30v186(D30v361, 0, 1); 	D30v186(D30v362, 0, 1); 	D30v186(D30v370, 0, 1); 	D30v186(D30v397, 0, 70); 	D30v186(D30v398, 0, 70); 	D30v186(D30v399, 0, 70); 	D30v186(D30v400, 0, 70); 	D30v186(D30v401, 0, 70); 	D30v186(D30v402, 1, 25); 	D30v186(D30v403, 0, 1); 	D30v186(D30v404, 0, 1); 	D30v186(D30v405, 1, 25); 	D30v186(D30v406, 0, 1); 	D30v186(D30v407, 0, 1); 	D30v186(D30v408, 0, 25); 	D30v186(D30v409, 0, 1); 	D30v186(D30v410, 0, 1); 	D30v186(D30v411, 1, 25); 	D30v186(D30v412, 0, 1); 	D30v186(D30v413, 0, 1); 	D30v186(D30v414, 350, 600); 	D30v186(D30v415, 350, 600); 	D30v186(D30v416, 0, 22); 	D30v186(D30v417, 0, 1); 	D30v186(D30v418, -100, 300); 	D30v186(D30v363, 0, 1); 	D30v186(D30v364, 0, 1); 	D30v186(D30v365, 0, 1); 	D30v186(D30v366, 0, 1); 	D30v186(D30v367, 0, 1); 	D30v186(D30v419, -150, 150); 	D30v186(D30v420, -150, 150); 	D30v186(D30v421, -150, 150); 	D30v186(D30v422, 0, 1); 	D30v186(D30v423, -150, 150); 	D30v186(D30v424, 0, 22); 	D30v186(D30v425, 0, 22); 	D30v186(D30v426, 0, 22); 	D30v186(D30v427, 0, 22); 	D30v186(D30v579, 60, 400); 	D30v186(D30v429, 0, 1); 	D30v186(D30v428, 0, 1); 	D30v186(D30v350, 0, 3); 	D30v186(D30v375, 0, 70); 	D30v186(D30v376, 0, 70); 	D30v186(D30v377, 0, 70); 	D30v186(D30v380, 0, 70); 	D30v186(D30v381, 0, 70); 	D30v186(D30v382, 0, 70); 	D30v186(D30v383, 0, 22); 	D30v186(D30v368, 0, 1); 	D30v186(D30v378, 0, 70); 	D30v186(D30v379, 0, 70); 	D30v186(D30v571, 0, 2500); 	D30v186(D30v1219, 0, 1); 	D30v186(D30v1220, 0, 1); 	D30v186(D30v1213, 0, 1); 	D30v186(D30v110, 0, 10); 	D30v186(D30v396, 0, 1); 	D30v186(D30v349, 0, 2); 	D30v186(D30v430, 0, 9); 	D30v186(D30v431, 0, 9); 	D30v186(D30v432, 0, 9); 	D30v186(D30v433, 0, 9); 	D30v186(D30v434, 0, 9); 	D30v186(D30v435, 0, 9); 	D30v186(D30v436, 0, 9); 	D30v186(D30v437, 0, 9); 	D30v186(D30v371,    0, 1); 	D30v186(D30v372,    0, 1); 	D30v186(D30v373,     0, 1); 	D30v186(D30v384,     60, 500); 	D30v186(D30v385,     60, 500); 	D30v186(D30v386,    0, 1); 	D30v186(D30v387, 0, 1); 	D30v186(D30v388,     50, 250); 	D30v186(D30v389,     100, 850); 	D30v186(D30v390, 0, 1); 	D30v186(D30v391, 0, 1); 	D30v186(D30v392,    0, 1); 	D30v186(D30v393,        80, 500); 	D30v186(D30v394,        80, 500); 	D30v186(D30v395,       0, 1); 	D30v186(D30v446 ,         3,60); 	D30v186(D30v29,           0,1); 	D30v186(D30v453,           0,1); 	D30v186(D30v451,           0,1); 	} function D30v163() { 	D30v977 = SPVAR_4; 	D30v978 = 0; 	D30v980 = 0; 	} int D30v978,  D30v977, D30v980, D30v981, D30v982; function D30v164(D30v165) { 	D30v981 = 0; 	while (D30v165) { 		D30v981++; 		D30v165 = abs(D30v165 >> 1); 	} 	return D30v981; 	} function D30v166(D30v167, D30v168) { 	D30v981 = max(D30v164(D30v167), D30v164(D30v168)); 	if (D30v169(D30v167, D30v168)) { 		D30v981++; 	} 	return D30v981; 	} function D30v169(D30v167, D30v168) { 	return D30v167 < 0 || D30v168 < 0; 	} function D30v172(D30v173) { 	return 1 << clamp(D30v173 - 1, 0, 31); 	} function D30v174(D30v173) { 	if (D30v173 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - D30v173); } function D30v176(D30v173) { 	return D30v174(D30v173 - 1); 	} function D30v178(D30v165, D30v173) { 	if (D30v165 < 0) { 		return (abs(D30v165) & D30v176(D30v173)) | D30v172(D30v173); 	} 	return D30v165 & D30v176(D30v173); } function D30v181(D30v165, D30v173) { 	if (D30v165 & D30v172(D30v173)) { 		return 0 - (D30v165 & D30v176(D30v173)); 	} 	return D30v165 & D30v176(D30v173); } function D30v184(D30v185) { 	return get_pvar(D30v185, 0x80000000, 0x7FFFFFFF, 0); 	} function D30v186(D30v165, min, max) { 	D30v982 = D30v166(min, max); 	D30v165 = clamp(D30v165, min, max); 	if (D30v169(min, max)) { 		D30v165 = D30v178(D30v165, D30v982); 	} 	D30v165 = D30v165 & D30v174(D30v982); 	if (D30v982 >= 32 - D30v978) { 		D30v980 = D30v980 | (D30v165 << D30v978); 		set_pvar(D30v977, D30v980); 		D30v977++; 		D30v982 -= (32 - D30v978); 		D30v165 = D30v165 >> (32 - D30v978); 		D30v978 = 0; 		D30v980 = 0; 	} 	D30v980 = D30v980 | (D30v165 << D30v978); 	D30v978 += D30v982; 	if (!D30v978) { 		D30v980 = 0; 	} 	set_pvar(D30v977, D30v980); } function D30v188(min, max, D30v189) { 	D30v982 = D30v166(min, max); 	D30v980 = (D30v184(D30v977) >> D30v978) & D30v174(D30v982); 	if (D30v982 >= 32 - D30v978) { 		D30v980 = (D30v980 & D30v174(32 - D30v978)) | ((D30v184(D30v977 + 1) & D30v174(D30v982 - (32 - D30v978))) << (32 - D30v978)); 	} 	D30v978 += D30v982; 	D30v980 = D30v980 & D30v174(D30v982); 	if (D30v978 >= 32) { 		D30v977++; 		D30v978 -= 32; 	} 	if (D30v169(min, max)) { 		D30v980 = D30v181(D30v980, D30v982); 	} 	if (D30v980 < min || D30v980 > max) { 		return D30v189; 	} 		if(D30v191[262] != 7925){       D30v188(min, max, D30v189); 	} 	return D30v980; 	} const string D30v1008 = "SETTINGS"; const string D30v1009 = "WAS SAVED"; combo D30v75 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	D30v162(); 	print(15, 2, OLED_FONT_MEDIUM, 1, D30v1008[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, D30v1009[0]); 	D30v1010 = 1500; 	combo_run(D30v76); 	} int D30v1010 = 1500; combo D30v76 { 	vm_tctrl(0);wait( D30v1010); 	cls_oled(0); 	D30v347 = FALSE; 	} define D30v1011 = 0; define D30v1012 = 1; define D30v1013 = 2; define D30v1014 = 3; define D30v1015 = 4; define D30v1016 = 5; define D30v1017 = 6; define D30v1018 = 7; int D30v657; int D30v1020; int D30v1021, D30v639; int D30v456; int D30v1024 = 49; int D30v1025 = 200; int D30v688 = TRUE; combo D30v77 { 	set_val(D30v439, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(D30v439, 0); 	vm_tctrl(0);wait( 120); 	if (D30v417) D30v234(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int D30v586; int D30v593; combo D30v78 { 	if (D30v593) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (D30v593) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (D30v593) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(D30v438, 100); 	vm_tctrl(0);wait( D30v414); 	if (D30v593) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	D30v586 = FALSE; 	D30v218(D30v586); 	} int D30v595; int D30v596; combo D30v79 { 	if (D30v596) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (D30v596) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(D30v444, 100); 	set_val(D30v441, 100); 	set_val(D30v442, 100); 	D30v1167 = 4000; 	vm_tctrl(0);wait( D30v415); 	vm_tctrl(0);wait( 50); 	set_val(D30v444, 100); 	vm_tctrl(0);wait( 50); 	D30v595 = FALSE; 	D30v218(D30v595); 	} int D30v1031 = TRUE; function D30v190(D30v191) { 	if (D30v191) { 		D30v1032 = D30v1064; 			} 	else { 		D30v1032 = D30v1063; 			} 	combo_run(D30v80); 	} int D30v1032; combo D30v80 { 	D30v211(D30v1032); 	vm_tctrl(0);wait( 300); 	D30v211(D30v1061); 	vm_tctrl(0);wait( 100); 	D30v211(D30v1032); 	vm_tctrl(0);wait( 300); 	D30v211(D30v1061); 	} define D30v1036 = 100; define D30v1037 = 130; const string D30v523 = "SCRIPT WAS"; function D30v192(D30v117, D30v194, D30v195) { 	if (!D30v341 && !D30v342) { 		cls_oled(0); 		print(D30v194, 3, OLED_FONT_MEDIUM, OLED_WHITE, D30v195); 		if (D30v117) { 			print(D30v196(sizeof(D30v1041) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D30v1041[0]); 		} 		else { 			print(D30v196(sizeof(D30v1042) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D30v1042[0]); 		} 		D30v190(D30v117); 			} 	} function D30v196(D30v132, D30v126) { 	return (OLED_WIDTH / 2) - ((D30v132 * D30v126) / 2); 	} const string D30v1042 = "OFF"; const string D30v1041 = "ON"; function D30v199(D30v123, D30v201, D30v117) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(D30v123, 0, OLED_FONT_MEDIUM, OLED_WHITE, D30v201); 	D30v206(D30v117, D30v209(D30v117)); 	D30v345 = TRUE; 	} const string D30v566 = "EA PING"; const string D30v588 = "FK_POWER"; const string D30v580 = "MaxFnshPwr"const string D30v572 = "JK_Agg"; int D30v571; int D30v579; function D30v203(D30v132, D30v126) { 	return (OLED_WIDTH / 2) - ((D30v132 * D30v126) / 2); 	} int D30v1051; int D30v1052, D30v1053; function D30v206(D30v117, D30v149) { 	D30v1051 = 1; 	D30v1053 = 10000; 	if (D30v117 < 0) { 		putc_oled(D30v1051, 45); 		D30v1051 += 1; 		D30v117 = abs(D30v117); 			} 	for (D30v1052 = 5; 	D30v1052 >= 1; 	D30v1052--) { 		if (D30v149 >= D30v1052) { 			putc_oled(D30v1051, (D30v117 / D30v1053) + 48); 			D30v117 %= D30v1053; 			D30v1051++; 			if (D30v1052 == 4) { 				putc_oled(D30v1051, 44); 				D30v1051++; 							} 					} 		D30v1053 /= 10; 			} 	puts_oled(D30v203(D30v1051 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, D30v1051 - 1, OLED_WHITE); 	} int D30v1057; function D30v209(D30v210) { 	D30v1057 = 0; 	do { 		D30v210 /= 10; 		D30v1057++; 			} 	while (D30v210); 	return D30v1057; 	} int D30v602; define D30v1061 = 0; define D30v1062 = 1; define D30v1063 = 2; define D30v1064 = 3; define D30v1065 = 4; define D30v1066 = 5; define D30v1067 = 6; define D30v1068 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int D30v1069; function D30v211(D30v212) { 	for (D30v1069 = 0; 	D30v1069 < 3; 	D30v1069++) { 		set_rgb(data[D30v212][0], data[D30v212][1], data[D30v212][2]); 			} 	} const int8 D30v1355[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int D30v605 = PS4_L3; define D30v1071 = 1; define D30v1072 = 2; define D30v1073 = 3; define D30v1074 = 2; define D30v1075 = 3; define D30v1076 = 4; define D30v1077 = 5; define D30v1078 = 6; define D30v1079 = 7; define D30v1080 = 8; define D30v1081 = 9; int D30v599 = FALSE; int D30v1083; int D30v1084; int D30v1085; int D30v1086; define D30v1087 = PS4_LX; define D30v1088 = PS4_LY; define D30v1089 = PS4_RX; define D30v1090 = PS4_RY; function D30v213 () { if(D30v351 == 3){ 		if( get_ival(PS4_RY) < -70  && !D30v1083 && !combo_running(D30v0) ) { 			D30v1083 = TRUE; 			D30v456 = FALSE; 			D30v1020 = D30v264; 			            D30v216(D30v264); 		} 		if( get_ival(PS4_RY) >  70  && !D30v1084 && !combo_running(D30v0)) { 			D30v1084 = TRUE; 			D30v456 = TRUE; 			D30v1020 = D30v266; 			           D30v216(D30v266); 		} 		if( get_ival(PS4_RX) < -70  && !D30v1085 && !combo_running(D30v0) ) { 			D30v1085 = TRUE; 			D30v456 = FALSE; 			D30v1020 = D30v267; 			              D30v216(D30v267); 		} 		if( get_ival(PS4_RX) >  70  && !D30v1086 && !combo_running(D30v0) ) { 			D30v1086 = TRUE; 			D30v456 = TRUE; 			D30v1020 = D30v265; 			            D30v216(D30v265); 		} 			set_val(D30v1089,0);              set_val(D30v1090,0);  			} 	else if(D30v351 < 3 && !get_ival(XB1_RS) &&  !get_ival(D30v442) && !get_ival(D30v443) && !get_ival(D30v441)) { 		if( get_ival(PS4_RY) < -70  && !D30v1083 && !combo_running(D30v0) ) { 			D30v1083 = TRUE; 			D30v456 = FALSE; 			D30v1020 = D30v264; 			            D30v216(D30v264); 		} 		if( get_ival(PS4_RY) >  70  && !D30v1084 && !combo_running(D30v0)) { 			D30v1084 = TRUE; 			D30v456 = TRUE; 			D30v1020 = D30v266; 			           D30v216(D30v266); 		} 		if( get_ival(PS4_RX) < -70  && !D30v1085 && !combo_running(D30v0) ) { 			D30v1085 = TRUE; 			D30v456 = FALSE; 			D30v1020 = D30v267; 			              D30v216(D30v267); 		} 		if( get_ival(PS4_RX) >  70  && !D30v1086 && !combo_running(D30v0) ) { 			D30v1086 = TRUE; 			D30v456 = TRUE; 			D30v1020 = D30v265; 			            D30v216(D30v265); 		} 			set_val(D30v1089,0);              set_val(D30v1090,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		D30v1083 = 0; 		D30v1084  = 0; 		D30v1085  = 0; 		D30v1086  = 0; 			} 	} function D30v214() { 	if (D30v479 == D30v564) { 		D30v456 = FALSE; 		if (D30v375) D30v216(D30v375); 			} 	if (D30v479 == D30v221(D30v564 + 4)) { 		D30v456 = FALSE; 		if (D30v382) D30v216(D30v382); 			} 	if (D30v479 == D30v221(D30v564 + 1)) { 		D30v456 = TRUE; 		if (D30v377) D30v216(D30v377); 			} 	if (D30v479 == D30v221(D30v564 - 1)) { 		D30v456 = FALSE; 		if (D30v376) D30v216(D30v376); 			} 	if (D30v479 == D30v221(D30v564 + 2)) { 		D30v456 = TRUE; 		if (D30v379) D30v216(D30v379); 			} 	if (D30v479 == D30v221(D30v564 - 2)) { 		D30v456 = FALSE; 		if (D30v378) D30v216(D30v378); 			} 	if (D30v479 == D30v221(D30v564 + 3)) { 		D30v456 = TRUE; 		if (D30v381) D30v216(D30v381); 			} 	if (D30v479 == D30v221(D30v564 - 3)) { 		D30v456 = FALSE; 		if (D30v380) D30v216(D30v380); 			} 	} int D30v1108; int D30v477 = 0; function D30v215() { 	if(D30v1108){ 		D30v477 += get_rtime(); 			} 	if(D30v477 >= 3000){ 		D30v477 = 0; 		D30v1108 = FALSE; 			} 	if(D30v350 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D30v480 && !combo_running(D30v0)) { 			D30v480 = TRUE; 			D30v1108 = TRUE; 			D30v477 = 0; 			D30v479 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D30v214(); 					} 		set_val(D30v1089, 0); 		set_val(D30v1090, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(D30v442) && !get_ival(D30v443) && !get_ival(D30v441) && !get_ival(D30v440)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D30v480 && !combo_running(D30v0)) { 			D30v480 = TRUE; 			D30v1108 = TRUE; 			D30v477 = 0; 			D30v479 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D30v214(); 					} 		set_val(D30v1089, 0); 		set_val(D30v1090, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		D30v480 = FALSE; 			} 	} function D30v216(D30v217) { 	D30v1020 = D30v217; 	D30v191[-327 + (D30v217 * 3)] = TRUE; 	D30v688 = FALSE; 	block = TRUE; 	if (D30v110 > 7)vm_tctrl(0); 	} int D30v1116; combo D30v81 { 	set_rumble(D30v1116, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function D30v218(D30v117) { 	if (D30v117) D30v1116 = RUMBLE_A; 	else D30v1116 = RUMBLE_B; 	combo_run(D30v81); 	} int D30v1117 = 300; int D30v1118 ; combo D30v82 { 	D30v1118 = TRUE; 	vm_tctrl(0);wait( D30v1117); 	D30v1118 = FALSE; 	} combo D30v83 { 	D30v220(); 	D30v228(0, 0); 	vm_tctrl(0);wait( 20); 	D30v228(0, 0); 	vm_tctrl(0);wait( 100); 	D30v228(0, 0); 	set_val(D30v443, 100); 	D30v228(0, 0); 	vm_tctrl(0);wait( 60); 	D30v228(0, 0); 	vm_tctrl(0);wait( 150); 	D30v688 = TRUE; 	vm_tctrl(0);wait( 350); 	} function D30v220() { 	D30v657 = D30v564  D30v223(D30v657); 	D30v1021 = D30v1122; 	D30v639 = D30v641; 	} combo D30v84 { 	set_val(D30v442, 100); 	set_val(D30v441, 100); 	vm_tctrl(0);wait( 100); 	set_val(D30v442, 100); 	vm_tctrl(0);wait( 100); 	D30v688 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 D30v1356[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int D30v1122, D30v641, D30v564; int D30v479; int D30v480; int D30v1127; function D30v221(D30v222) { 	D30v1127 = D30v222; 	if (D30v1127 < 0) D30v1127 = 8 - abs(D30v222); 	else if (D30v1127 >= 8) D30v1127 = D30v222 - 8  return D30v1127; 	} function D30v223(D30v224) { 	if (D30v224 < 0) D30v224 = 8 - abs(D30v224); 	else if (D30v224 >= 8) D30v224 = D30v224 - 8; 	D30v1122 = D30v1356[D30v224][0]; 	D30v641 = D30v1356[D30v224][1]; } function D30v225(D30v226, D30v227) { 	set_val(D30v1089, D30v226); 	set_val(D30v1090, D30v227); 	} function D30v228(D30v229, D30v230) { 	set_val(D30v1087, D30v229); 	set_val(D30v1088, D30v230); 	} function D30v231() { 	if (D30v456) { 		set_val(D30v1087, inv(D30v639)); 		set_val(D30v1088, D30v1021); 			} 	else { 		set_val(D30v1087, D30v639); 		set_val(D30v1088, inv(D30v1021)); 			} 	} function D30v232() { 	if (D30v456) { 		set_val(D30v1089, inv(D30v639)); 		set_val(D30v1090, D30v1021); 			} 	else { 		set_val(D30v1089, D30v639); 		set_val(D30v1090, inv(D30v1021)); 			} 	} function D30v233() { 	if (!D30v456) { 		set_val(D30v1089, inv(D30v639)); 		set_val(D30v1090, D30v1021); 			} 	else { 		set_val(D30v1089, D30v639); 		set_val(D30v1090, inv(D30v1021)); 			} 	} function D30v234() { 	set_val(D30v1089, D30v1021); 	set_val(D30v1090, D30v639); 	} function D30v235() { 	set_val(D30v1089, inv(D30v1021)); 	set_val(D30v1090, inv(D30v639)); 	} function D30v236() { 	set_val(D30v1089, 0); 	set_val(D30v1090, 0); 	} int D30v1143; function D30v237() { 	if ((event_press(D30v439)  ) && !combo_running(D30v85) && (D30v1167  <= 0 || (D30v1167 < 3000 && D30v1167 > 1  )) && !get_ival(D30v443) && D30v526 > 500 &&!get_ival(D30v442) &&!get_ival(D30v438) &&!get_ival(D30v441) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(D30v85) ) { 		combo_run(D30v85); 			} 	if (combo_running(D30v85) && (        get_ival(D30v443) ||        get_ival(D30v442) ||        get_ival(D30v438) ||        get_ival(D30v441) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(D30v85); 			} 	} combo D30v85 { vm_tctrl(0);wait(700); if( !block )set_val(D30v440,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(D30v1143 == 1 &&  !block  ){ D30v225(100,inv(D30v639));} else{ if(!block )D30v225(-100,inv(D30v639)); } vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo D30v86 { 	vm_tctrl(0);wait( 800); 	} int D30v1145 = 1000; int D30v1146 = 1600; int D30v1147 = 1600; int D30v1148 = TRUE; int D30v1149 = TRUE; int D30v635 = FALSE; int D30v1151 = TRUE; int D30v629 = FALSE; int D30v1153 = TRUE; int D30v631 = FALSE; int D30v1155 = TRUE; int D30v633 = FALSE; function D30v238(){ 	if (get_ival(D30v440)) { 		D30v1157 = 1000; 		D30v1176 = 0; 		D30v526 = 1; 		combo_stop(D30v95); 			} 	if (event_press(D30v442) || event_press(D30v425)) { 		D30v1157 = 4000; 		D30v1176 = 0; 		D30v1145 = 1600; 			} 	if (get_ival(D30v441) && !get_ival(D30v440) ) { 		D30v1157 = 0; 		D30v1176 = 0; 		D30v1145 = 1600; 			} 	else if (get_ival(D30v440) && get_ival(D30v441)){ 		D30v1157 = 1000; 			} 	if (D30v1157 > 0) { 		D30v1157 -= get_rtime(); 			} 	if (D30v1157 < 0) { 		D30v1157 = 0; 			} 	D30v1235 = D30v418; 	if (event_release(D30v439)) { 		D30v1163 = 1; 		D30v1176 = 0; 		D30v526 = 1; 	} 	if (event_release(D30v445)) { 		D30v1164 = 1; 		D30v1176 = 0; 		D30v526 = 1; 	} 	if (event_release(D30v440)) { 		D30v1146 = 1; 		D30v1176 = 0; 		D30v1145 = 1600; 			} 	if (event_release(D30v441)) { 		D30v1147 = 1; 		D30v1176 = 0; 		D30v1145 = 1600; 			} 	if (event_release(D30v444) || (get_ival(D30v445) && get_ival(D30v440))) { 		D30v1167 = 4000; 		D30v1176 = 0; 	} 	if (get_ival(D30v439) && D30v1167 < 4000 && D30v1167 > 3500) { 		D30v1167 = 0; 			} 	if (D30v1145 < 1510) { 		D30v1145 += get_rtime(); 			} 	if (D30v1146 < 1600) { 		D30v1146 += get_rtime(); 			} 	if (D30v1147 < 1600) { 		D30v1147 += get_rtime(); 			} 	if (D30v1167 > 0) { 		D30v1167 -= get_rtime(); 			} 	if (D30v1167 < 0) { 		D30v1167 = 0; 			} 	if (D30v1163 < 5100) { 		D30v1163 += get_rtime(); 			} 	if (D30v1164 < 4100) { 		D30v1164 += get_rtime(); 			} 	if (D30v1176 > 0) { 		D30v1176 -= get_rtime(); 			} 	if (D30v1176 < 0) { 		D30v1176 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		D30v1145 = 1; 		D30v1176 = 0; 			} 	if (combo_running(D30v92)) { 		set_val(D30v439, 0); 		if(get_ival(D30v439)){ 			D30v1179 = 0; 			combo_stop(D30v87); 			set_val(D30v439, 0); 			combo_stop(D30v92); 			combo_run(D30v49); 					} 			} 	if ((combo_running(D30v97) || combo_running(D30v88))) { 		set_val(D30v439, 0); 		if(get_ival(D30v439)){ 			D30v526 = 1; 			D30v1179 = 0; 			combo_stop(D30v87); 			set_val(D30v439, 0); 			combo_stop(D30v97); 			combo_stop(D30v88); 			combo_run(D30v49); 					} 			} 	if (event_press(D30v438)) { 		combo_run(D30v86); 			} 	if (D30v526 > 1500) { 		if (D30v1146 < 1500) { 			D30v1181 = 120; 					} 		if (D30v1147 < 1500) { 			D30v1181 = 228; 					} 		else { 			D30v1181 = 200; 					} 			} 	if (D30v526 < 1500) { 		D30v1181 = 450; 			} 	if (D30v526 > 2700) { 		D30v1185 = 920; 			} 	else if (D30v526 >= 0 && D30v526 < 2700) { 		D30v1185 = 725; 			} 	} function D30v239() { 	if (D30v1148) { 		if ((D30v526 <= 600 || (D30v1145 <= 1500 && D30v1145 > 1) || ( D30v1146 <= 150 || D30v1147 <= 150)) && event_press(D30v438) ) { 			if (!get_ival(D30v441) && !get_ival(D30v440) && !get_ival(D30v442) && !get_ival(D30v443)) { 				set_val(D30v438, 0); 				if (D30v1167 < 4000 && D30v1167 > 1) { 					set_val(D30v438, 0); 					combo_run(D30v90); 									} 				else { 					set_val(D30v438, 0); 					combo_run(D30v88); 					D30v1176 = 9000; 				} 							} 					} 			} 	if (D30v1155) { 		if (D30v526 > 1000 && !D30v1176 && (!get_ival(D30v441) && !get_ival(PS4_L3) && event_press(D30v438)) &&  D30v1146 > 150 &&  D30v1147 > 150) { 			if (!get_ival(D30v440) && !get_ival(D30v442)) { 				set_val(D30v438, 0); 				if (((D30v1164 > 1 && D30v1164 <= 2500) || (D30v1163 > 1 && D30v1163 <= 3000)) &&  D30v1145 != 1600) { 					set_val(D30v438, 0); 					combo_run(D30v89); 					D30v1176 = 9000; 									} 				else if (((D30v1164 > 2500 && D30v1164 <= 4000) || (D30v1163 > 3000 && D30v1163 <= 3500))  &&  D30v1145 != 1600) { 					set_val(D30v438, 0); 					combo_run(D30v88); 					D30v1176 = 9000; 									} 				else if ((D30v1167 < 4000 && D30v1167 > 1)) { 					set_val(D30v438, 0); 					combo_run(D30v90); 					D30v1176 = 9000; 									} 				else { 					set_val(D30v438, 0); 					D30v243(); 					D30v1176 = 9000; 									} 				D30v1176 = 9000; 							} 					} 			} 	if (D30v1149) { 		if (get_ival(D30v440) && get_ival(D30v441)) { 			if (!get_ival(D30v442) && !get_ival(D30v443) && (D30v1167 && D30v1163 > 1 && D30v1163 <= 1500) || (!D30v1167 && D30v1163 > 1 && D30v1163 <= 1500) || (D30v1163 > 1500 && !D30v1167) && !D30v1176) { 				if (event_press(D30v438)) { 					set_val(D30v438, 0); 					combo_run(D30v98); 					D30v1176 = 9000; 									} 							} 					} 			} 	if (D30v1153) { 		if (!get_ival(D30v443) && !get_ival(D30v440) && !get_ival(D30v441)) { 			if (get_ival(D30v442) && get_ival(D30v438)) { 				D30v244(); 				set_val(D30v438, 0); 				D30v1176 = 9000; 							} 					} 			} 	if (D30v1151) { 		if (get_ival(D30v441) && !get_ival(D30v440) && !D30v1157) { 			if (!get_ival(D30v442) && !get_ival(D30v443) && !D30v1176) { 				if (get_ival(D30v438) && D30v526 >= 1000) { 					set_val(D30v438, 0); 					combo_run(D30v95); 					D30v1176 = 9000; 									} 				if (get_ival(D30v438) && D30v526 < 1000 && !D30v1157  ) { 					combo_run(D30v96); 									} 							} 					} 			} 	if(combo_running(D30v90)){ 		D30v1179 = 0; 		combo_stop(D30v87)   	} 	if (get_ival(D30v440) || D30v1157 > 0) { 		combo_stop(D30v95); 		combo_stop(D30v97); 		combo_stop(D30v96); 			} 	if (combo_running(D30v88) || combo_running(D30v92) || combo_running(D30v97) || combo_running(D30v98) || combo_running(D30v95)) { 		if (get_ival(D30v439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D30v443) > 30) { 			combo_stop(D30v92); 			combo_stop(D30v97); 			combo_stop(D30v98); 			combo_stop(D30v95); 			combo_stop(D30v88); 			D30v1179 = 0; 			combo_stop(D30v87)     		} 			} 	if (combo_running(D30v88) || combo_running(D30v89)) { 		if (get_ival(D30v439) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D30v443)) { 			combo_stop(D30v90); 			combo_stop(D30v89); 			combo_stop(D30v88); 			D30v1179 = 0; 			combo_stop(D30v87)     		} 			} 	if (event_press(D30v438) && D30v1176 > 100 && D30v1176 < 8990) { 		set_val(D30v438, 0); 		combo_stop(D30v92); 		combo_stop(D30v97); 		combo_stop(D30v98); 		combo_stop(D30v95); 		combo_stop(D30v88); 		D30v1179 = 0; 		combo_stop(D30v87)    combo_run(D30v91); 			} 	if (!D30v635) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D30v98); 					} 			} 	if (!D30v629) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D30v95); 					} 			} 	if (!D30v631) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D30v92); 			D30v1179 = 0; 			combo_stop(D30v87)     		} 			} 	if (!D30v633) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D30v97); 					} 			} 	if ((get_ival(D30v443) || get_ival(D30v439)) && !D30v354) { 		combo_stop(D30v4); 		combo_stop(D30v47); 		combo_stop(D30v33); 			} 	} define D30v1189 = 15; define D30v1190 = 15; int D30v1191 = 0; define D30v1192 = 8000; define D30v1193 = 4; define D30v1194 = 2000; int D30v1179 = 0; const int16 D30v1357[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 D30v1358[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int D30v1196 = FALSE; int D30v1197; int D30v1198; int D30v1199; int D30v1200; function D30v240 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D30v1199 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D30v1196 = FALSE; 		for ( D30v947 = 0; 		D30v947 < sizeof(D30v1358) / sizeof(D30v1358[0]); 		D30v947++) { 			if (D30v1199 == D30v1358[D30v947]) { 				D30v1196 = TRUE; 				break; 							} 					} 		if (!D30v1196) { 			D30v1197 = D30v1358[0]; 			D30v1198 = abs(D30v1199 - D30v1358[0]); 			for ( D30v947 = 1; 			D30v947 < sizeof(D30v1358) / sizeof(D30v1358[0]); 			D30v947++) { 				D30v1200 = abs(D30v1199 - D30v1358[D30v947]); 				if (D30v1200 < D30v1198) { 					D30v1197 = D30v1358[D30v947]; 					D30v1198 = D30v1200; 									} 							} 			set_polar(POLAR_LS, D30v1197, 32767); 					} 			} } function D30v241 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D30v1199 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D30v1196 = FALSE; 		for ( D30v947 = 0; 		D30v947 < sizeof(D30v1357) / sizeof(D30v1357[0]); 		D30v947++) { 			if (D30v1199 == D30v1357[D30v947]) { 				D30v1196 = TRUE; 				break; 							} 					} 		if (!D30v1196) { 			D30v1197 = D30v1357[0]; 			D30v1198 = abs(D30v1199 - D30v1357[0]); 			for ( D30v947 = 1; 			D30v947 < sizeof(D30v1357) / sizeof(D30v1357[0]); 			D30v947++) { 				D30v1200 = abs(D30v1199 - D30v1357[D30v947]); 				if (D30v1200 < D30v1198) { 					D30v1197 = D30v1357[D30v947]; 					D30v1198 = D30v1200; 									} 							} 			set_polar(POLAR_LS, D30v1197, 32767); 					} 			} } int D30v1213; function D30v242() { 	if (combo_running(D30v87) && ( event_press(D30v438)    ||   get_ival(D30v443) ||         get_ival(D30v439) ||        get_ival(D30v444) ||        get_ival(D30v445) ||        get_ival(D30v440)      )) { 		combo_stop(D30v87); 		D30v1179 = 0; 			} 	if (D30v1191 == 0) { 		if ( ( D30v1167 == 0 && !combo_running(D30v90) && !combo_running(D30v98) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(D30v438) || (D30v1179 == 1 ||     combo_running(D30v96) || combo_running(D30v94)|| combo_running(D30v92) ||  combo_running(D30v95)     || combo_running(D30v88) || combo_running(D30v97) || combo_running(D30v89) ||  combo_running(D30v91)  ))     ) { 			if(D30v428)D30v241 (); 			else if (D30v429)D30v240 (); 			combo_stop(D30v103); 			combo_run(D30v87); 					} 			} 	else{ 		combo_stop(D30v87); 			} 	} combo D30v87 { 	if(D30v428)D30v241 (); 	else if (D30v429)D30v240 (); 	combo_stop(D30v103); 	vm_tctrl(0); 	wait(4000); 	D30v1179 = 0; 	} combo D30v88 { 	set_val(D30v440,0); 	set_val(D30v438, 100); 	vm_tctrl(0);wait( random(210, 215) + D30v420); 	set_val(D30v438, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function D30v243() { 	if (D30v526 > 600 && D30v526 <= 800) { 		D30v1214 = 240; 			} 	if (D30v526 > 800 && D30v526 <= 1000) { 		D30v1214 = 230; 			} 	if (D30v526 > 1000 && D30v526 <= 1500) { 		D30v1214 = 225; 			} 	if (D30v526 > 1500 && D30v526 <= 2000) { 		D30v1214 = 235; 			} 	if (D30v526 > 2000) { 		D30v1214 = 218; 			} 	combo_run(D30v97); 	} combo D30v89 { 	set_val(D30v438, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( 500); 	} combo D30v90 { 	set_val(D30v438, 100); 	vm_tctrl(0);wait( 205); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( 300); 	} combo D30v91 { 	set_val(D30v438, 100); 	vm_tctrl(0);wait( 190); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( 400); 	} int D30v1219; int D30v1220; int D30v29; int D30v453; int D30v451; int D30v1224; combo D30v92 { 	   set_val(D30v438, 0); 	if (D30v1219) { 		D30v232(); 		D30v228(0, 0); 		D30v1224 = 350; 			} 	else { 		D30v1224 = 0; 			} 	vm_tctrl(0); 	wait(D30v1224); 	if (D30v1219) { 		set_val(D30v441, 0); 		D30v1224 = 60; 			} 	else { 		set_val(D30v442, 0); 		D30v1224 = 60; 			} 	set_val(D30v438,0); 	vm_tctrl(0);wait(D30v1224); 	set_val(D30v442, 0); 	set_val(D30v441, 0); 	set_val(D30v438,0); 	vm_tctrl(0);wait(D30v1224); 	if (D30v1219) { 		D30v1224 = 0; 			} 	else { 		D30v1224 = 60; 			} 	set_val(D30v441, 0); 	set_val(D30v442, 0); 	set_val(D30v438,0); 	vm_tctrl(0);wait(D30v1224); 	if (!D30v1219) {set_val(D30v443,100);} 	set_val(D30v438, 100); 	set_val(D30v442, 100); 	vm_tctrl(0);wait(random(265, 268) +   D30v419 ); 	set_val(D30v442, 100); 	set_val(D30v438, 0); 	if (!D30v1219) {set_val(D30v443,100);} 	if (D30v1219) { 		D30v1224 = 15; 			} 	else { 		D30v1224 = 56; 			} 	vm_tctrl(0);wait(random(0,2) + D30v1236 + D30v1235 + D30v1224 ); 	set_val(D30v442, 100); 	if(D30v1213)set_val(D30v438, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D30v438, 0); 	set_val(D30v442, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(D30v442, 100); 	vm_tctrl(0);wait(2500); 	} int D30v1164; int D30v1167; int D30v526; int D30v1163; int D30v1235; int D30v1236 = 111; int D30v1157; int D30v1238; function D30v244() { if(D30v1219){ 	D30v1238 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((D30v1238 > 5 && D30v1238 < 40) ) {         D30v456 = FALSE; 			} 	if ((D30v1238 > 40 && D30v1238 < 85)){ 		D30v456 = TRUE; 		    } 	if ((D30v1238 > 95 && D30v1238 < 130) ) { 		D30v456 = FALSE; 		    } 	 if((D30v1238 > 130 && D30v1238 < 175)) { 		D30v456 = TRUE; 		} 			if ((D30v1238 > 185 && D30v1238 < 220) ) {        D30v456 = FALSE; 			} 	if ((D30v1238 > 220 && D30v1238 < 265)){        D30v456 = TRUE; 		    } 	if ((D30v1238 > 275 && D30v1238 < 310) ) {        D30v456 = FALSE; 		    } 	 if((D30v1238 > 310 && D30v1238 < 355)) { 		D30v456 = TRUE; 		} 		} 	if (D30v1167 == 0 && D30v526 >= 1000) { 			set_val(D30v438, 0); 			D30v1236 = 170; 		set_val(D30v438, 0); 		combo_stop(D30v97); 		combo_stop(D30v98); 		combo_stop(D30v95); 		combo_stop(D30v88); 		combo_stop(D30v94); 		combo_stop(D30v91); 		combo_stop(D30v90); 		combo_run(D30v92); 			} 	else { 		if (D30v1167) { 			set_val(D30v438, 0); 			combo_run(D30v93); 					} 		else { 			if (D30v526 < 1000) { 				set_val(D30v438, 0); 				combo_run(D30v94); 							} 					} 			} } combo D30v93 { 	set_val(D30v438, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(D30v438, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo D30v94 { if(D30v1220){set_val(D30v442,0);set_val(D30v440,100);set_val(D30v441,100);}else{set_val(D30v442,100);} 	set_val(D30v438, 100); 	vm_tctrl(0);wait( random(215, 225) )  set_val(D30v438, 0); 	vm_tctrl(0);wait( 700); 	} int D30v1248 = 246; int D30v1181 = 150; int D30v1250 = 0; combo D30v95 { 	set_val(D30v442, 100); 	set_val(D30v441, 0); 	set_val(D30v438,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D30v442, 0); 	set_val(D30v441, 0); 	set_val(D30v438,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	D30v1250 = D30v418; 	set_val(D30v441, 100); 	set_val(D30v438,0); 	vm_tctrl(0);wait( 60); 	set_val(D30v441, 100); 	set_val(D30v438, 100); 	vm_tctrl(0);wait( D30v1248 + 10 + random(-2, 2) +  D30v423); 	set_val(D30v441, 100); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( D30v1181 + D30v1250 - 58  ); 	set_val(D30v441, 100); 	if(D30v1213)set_val(D30v438, 100); 	vm_tctrl(0);wait( 60); 	set_val(D30v441, 100); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( 3000); 	} combo D30v96 { 	if(!combo_running(D30v95))set_val(D30v441, 100); 	if(!combo_running(D30v95))set_val(D30v438, 100); 	vm_tctrl(0);wait( 160 + D30v423 ); 	if(!combo_running(D30v95))set_val(D30v441, 100); 	if(!event_press(D30v438))set_val(D30v438, 0); 	vm_tctrl(0);wait( 3000); 	} int D30v1176; int D30v1252 = 220; int D30v1214; int D30v1254 = 0; combo D30v97 { 	D30v1254 = D30v418; 	set_val(D30v438, 100); 	vm_tctrl(0);wait( D30v1252 + D30v420); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( D30v1214 + (D30v1254) ); 	if(D30v1213)set_val(D30v438, 100); 	vm_tctrl(0);wait( 60); 	set_val(D30v438, 0); 	vm_tctrl(0);wait( 2000); 	} int D30v1256 = TRUE; int D30v1185; int D30v1258 = 270; int D30v1259 = 0; combo D30v98 { 	set_val(D30v440, 100); 	set_val(D30v441, 100); 	if (D30v1256) { 		D30v1259 = D30v418; 			} 	else { 		D30v1259 = 0   	} 	set_val(D30v438, 100); 	vm_tctrl(0);wait( D30v1258 + D30v421); 	set_val(D30v438,0); 	vm_tctrl(0);wait( D30v1185 + D30v1259 + 40)  	if(!event_press(D30v438))set_val(D30v438,0); 	vm_tctrl(0);wait(3000); } int D30v1262; int D30v622; int D30v621; int D30v624; int D30v623; combo D30v99 { 	set_val(D30v445, 0); 	vm_tctrl(0);wait( 30); 	set_val(D30v445, 100); 	vm_tctrl(0);wait( 60); 	} int D30v625; int D30v626; combo D30v100 { 	set_val(D30v444, 100); 	vm_tctrl(0);wait( D30v626); 	} define D30v1269 = TRUE; define D30v1270 = 95; define D30v1271 = 10; define D30v1272 = 70; define D30v1273 = FALSE; define D30v1274 = 50; define D30v1275 = 95; define D30v1276 = XB1_LT; define D30v1277 = XB1_RT; define D30v1278 = XB1_LX; define D30v1279 = XB1_LY; define D30v1280 = POLAR_LS; function D30v245() { 				if(D30v422 == TRUE){ 			if( combo_running(D30v102) && !get_ival(D30v441)|| get_ipolar(POLAR_RS,POLAR_RADIUS) > 2800 ){set_val(D30v441,0);combo_run(D30v101);} 					} 					if(combo_running(D30v101)){ 						if(event_press(D30v441) || event_press(D30v438) || event_press(D30v445) || event_press(D30v444) || event_press(D30v443) ){combo_stop(D30v101);} 					} 	if ( get_ival(D30v443) > 30 &&    (get_ival(D30v442) || get_ival(D30v439)) &&    (!get_ival(D30v444) || !get_ival(D30v438))  ) { set_val(D30v443, 0); 		if(!get_ival(D30v438)){ 			set_val(D30v442,100); 					} 		else{ 			set_val(D30v442,0); 			set_val(D30v443,100); 					} 		  combo_run(D30v102); 		  if(D30v422 == TRUE){ 		  if(!get_ival(D30v441)){combo_run(D30v101);}else{combo_stop(D30v101);} 		  } 			} 	else { 		combo_stop(D30v102); 		combo_stop(D30v101); 			} 	} combo D30v101 { set_val(D30v441, 100); vm_tctrl(0); wait(600); vm_tctrl(0); wait(100); 	} combo D30v102 { 	if(!get_ival(D30v438)){ 		set_val(D30v442,100); 			} 	else{ 		set_val(D30v442,0); 			} 	set_val(D30v443, 100); 	vm_tctrl(-2);wait(D30v571); 	if(!get_ival(D30v438)){ 		set_val(D30v442,100); 			} 	else{ 		set_val(D30v442,0); 			}     set_val(D30v443, 0); 	vm_tctrl(-2); 	wait(200); 	} function D30v246(D30v229) { return D30v248(D30v229 + 8192); } function D30v248(D30v229) {   D30v229 = (D30v229 % 32767) << 17;   if((D30v229 ^ (D30v229 * 2)) < 0) { D30v229 = (-2147483648) - D30v229; }   D30v229 = D30v229 >> 17;   return D30v229 * ((98304) - (D30v229 * D30v229) >> 11) >> 14; } int D30v1283, D30v1284; function D30v250(D30v251, D30v252, D30v253, D30v254){   D30v254 = (D30v254 * 32767) / 100;   D30v253 = (D30v253 * 32767) / 10000;   D30v252 = (360 - D30v252) * 91;   D30v1284 = D30v248(D30v252); D30v1283 = D30v246(D30v252);   D30v252 = 32767 - D30v246(abs(abs(D30v1284) - abs(D30v1283)));   D30v253 = D30v253 * (32767 - ((D30v252 * D30v254) >> 15)) >> 15;   set_val(42 + D30v251, clamp((D30v253 * D30v1283) >> 15, -32767, 32767));   set_val(43 + D30v251, clamp((D30v253 * D30v1284) >> 15, -32767, 32767));   return; } int D30v1289, D30v1290; function D30v255() {    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(D30v442) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2)      && !get_ival(D30v440) &&  !get_ival(D30v443) && !get_ival(D30v439) && !get_ival(D30v441) && !get_ival(D30v445) && !get_ival(D30v438)){        combo_run(D30v103);    }else{    combo_stop(D30v103);    } } function D30v256(){   stickize(POLAR_LX, POLAR_LY, 141);   D30v1289 = get_ipolar(POLAR_LS, POLAR_RADIUS);   D30v1290 = get_ipolar(POLAR_LS, POLAR_ANGLE);   D30v250(POLAR_LS,  D30v1290,  D30v1289, D30v446 + random(0,1));   } combo D30v103 {  vm_tctrl(0); wait(750); D30v256(); vm_tctrl(0); wait(200); 	} combo D30v104 { 	set_val(D30v439,100); 	vm_tctrl(0);wait( D30v622); 	set_val(D30v439,  0); 	vm_tctrl(0);wait( 30); 	if(D30v390){ 		set_val(D30v441,100); 			} 	vm_tctrl(0);wait( 60); 	} combo D30v105 { 	if(D30v390)set_val(D30v441,  100); 	vm_tctrl(0);wait( 60);     wait( 60);     vm_tctrl(0);wait(850);     if(D30v391 && !get_ival(D30v438) && !get_ival(D30v443) && !get_ival(D30v439) && !block && !get_ival(D30v441) && !get_ival(D30v442) )set_val(D30v442,  100);     wait(500); 	} combo D30v106 { 	set_val(D30v445,100); 	vm_tctrl(0);wait( D30v624); 	set_val(D30v445,  0); 	vm_tctrl(0);wait( 30); 	if(D30v387){ 		set_val(D30v445,100); 			} 	vm_tctrl(0);wait( 60); 	} int D30v970 int D30v1295 combo D30v107 { 	combo_suspend(D30v107) 	vm_tctrl(0);wait(361) } function D30v257 (){ 	if(D30v970[D30v1295] != 361){ 	    D30v1295-- 	} 	else{ 		if(inv(D30v1295) != 267){ 			D30v257(); 		} 	} } int D30v165; function D30v258(D30v259, D30v260, D30v261) {   D30v165 = get_ipolar(D30v259, POLAR_RADIUS);   if(D30v260) {     if(D30v165 <= D30v260) D30v165 = (D30v165 * 5000) / D30v260;     else D30v165 = ((5000 * (D30v165 - D30v260)) / (10000 - D30v260)) + 5000;   }   if (D30v261) D30v165 = (D30v165 * D30v261) / 10000;   set_polar2(D30v259, get_ipolar(D30v259, POLAR_ANGLE), min(D30v165, 14142));   if (D30v259 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 141);   else stickize(ANALOG_LX, ANALOG_LY, 141);   return; } combo D30v108{ vm_tctrl(-7); wait(1000); } 