<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Learning App</title>
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --correct-color: #38b000;
            --wrong-color: #d90429;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f1f5f9;
            color: var(--dark-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .app-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .app-description {
            font-size: 1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 2rem;
        }

        .start-screen {
            text-align: center;
        }

        .mode-title {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
        }

        .mode-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .mode-button {
            padding: 1rem 2rem;
            min-width: 200px;
        }

        .options-container {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 10px;
        }

        .options-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .option-group {
            margin-bottom: 1.5rem;
        }

        .option-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .word-count-input {
            width: 100%;
            max-width: 200px;
            padding: 0.5rem;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }

        .word-count-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .word-count-btn {
            padding: 0.5rem 1rem;
            background-color: #f1f3f5;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .word-count-btn:hover {
            background-color: #e9ecef;
        }

        .word-count-btn.active {
            background-color: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .letter-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
            justify-content: center;
        }

        .letter-btn {
            width: 40px;
            height: 40px;
            font-weight: bold;
            padding: 0;
            border-radius: 50%;
        }

        .letter-btn.active {
            background-color: var(--secondary-color);
        }

        .cancel-btn {
            background-color: var(--wrong-color);
            margin-top: 1rem;
        }

        .quiz-container {
            display: none;
            flex-direction: column;
            gap: 1.5rem;
        }

        .progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-bar {
            flex-grow: 1;
            height: 0.5rem;
            background-color: #e9ecef;
            border-radius: 1rem;
            margin: 0 1rem;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .word-display {
            font-size: 2.5rem;
            text-align: center;
            margin: 1.5rem 0;
            color: var(--primary-color);
            font-weight: bold;
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        label {
            font-size: 1.2rem;
            color: var(--dark-color);
        }

        .input-wrapper {
            display: flex;
            gap: 0.5rem;
        }

        input {
            flex-grow: 1;
            font-size: 1.2rem;
            padding: 0.75rem 1rem;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        button {
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-color);
            color: white;
            transition: background-color 0.3s ease, transform 0.1s ease;
        }

        button:hover {
            background-color: var(--secondary-color);
        }

        button:active {
            transform: scale(0.98);
        }

        .feedback {
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-top: 1rem;
            border-radius: 8px;
        }

        .feedback.correct {
            background-color: rgba(56, 176, 0, 0.1);
            color: var(--correct-color);
        }

        .feedback.wrong {
            background-color: rgba(217, 4, 41, 0.1);
            color: var(--wrong-color);
        }

        .result-container {
            display: none;
            text-align: center;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .result-title {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .score {
            font-size: 4rem;
            font-weight: bold;
            color: var(--secondary-color);
            margin: 1rem 0;
        }

        .result-details {
            margin: 2rem 0;
            text-align: left;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .word-pair {
            display: flex;
            gap: 0.5rem;
        }

        .result-item.correct span {
            color: var(--correct-color);
        }

        .result-item.wrong span {
            color: var(--wrong-color);
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .restart-btn, .home-btn {
            margin-top: 2rem;
        }

        .home-btn {
            background-color: #6c757d;
        }

        @media (max-width: 600px) {
            body {
                padding: 1rem;
            }

            .main-content {
                padding: 1.5rem;
            }

            .word-display {
                font-size: 2rem;
            }

            .mode-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 class="app-title">English Learning App</h1>
            <p class="app-description">Practice English-German translations</p>
        </header>

        <div class="main-content">
            <div class="start-screen" id="start-screen">
                <h2 class="mode-title">Choose Test Mode</h2>
                <div class="mode-buttons">
                    <button class="mode-button" id="en-to-de-btn">English → German</button>
                    <button class="mode-button" id="de-to-en-btn">German → English</button>
                </div>
                <div class="options-container">
                    <h3 class="options-title">Test Options</h3>
                    <div class="option-group">
                        <label class="option-label" for="word-count">Word Count:</label>
                        <input type="number" id="word-count" class="word-count-input" value="20" min="1">
                        <div class="word-count-options">
                            <button class="word-count-btn" data-count="10">10</button>
                            <button class="word-count-btn" data-count="20">20</button>
                            <button class="word-count-btn" data-count="30">30</button>
                            <button class="word-count-btn" data-count="all">All Words (<span id="total-word-count">0</span>)</button>
                        </div>
                    </div>
                    <div class="option-group">
                        <label class="option-label" for="letter-filter">First Letter Filter:</label>
                        <div class="letter-filter" id="letter-filter">
                            <button class="letter-btn" data-letter="">All</button>
                            <button class="letter-btn" data-letter="a">A</button>
                            <button class="letter-btn" data-letter="b">B</button>
                            <button class="letter-btn" data-letter="c">C</button>
                            <button class="letter-btn" data-letter="d">D</button>
                            <button class="letter-btn" data-letter="e">E</button>
                            <button class="letter-btn" data-letter="f">F</button>
                            <button class="letter-btn" data-letter="g">G</button>
                            <button class="letter-btn" data-letter="h">H</button>
                            <button class="letter-btn" data-letter="i">I</button>
                            <button class="letter-btn" data-letter="j">J</button>
                            <button class="letter-btn" data-letter="k">K</button>
                            <button class="letter-btn" data-letter="l">L</button>
                            <button class="letter-btn" data-letter="m">M</button>
                            <button class="letter-btn" data-letter="n">N</button>
                            <button class="letter-btn" data-letter="o">O</button>
                            <button class="letter-btn" data-letter="p">P</button>
                            <button class="letter-btn" data-letter="q">Q</button>
                            <button class="letter-btn" data-letter="r">R</button>
                            <button class="letter-btn" data-letter="s">S</button>
                            <button class="letter-btn" data-letter="t">T</button>
                            <button class="letter-btn" data-letter="u">U</button>
                            <button class="letter-btn" data-letter="v">V</button>
                            <button class="letter-btn" data-letter="w">W</button>
                            <button class="letter-btn" data-letter="x">X</button>
                            <button class="letter-btn" data-letter="y">Y</button>
                            <button class="letter-btn" data-letter="z">Z</button>
                        </div>
                    </div>
                    <button class="cancel-btn" id="cancel-btn">Cancel</button>
                </div>
            </div>

            <div class="quiz-container" id="quiz">
                <div class="progress">
                    <span id="progress-text">1/20</span>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" id="progress-fill"></div>
                    </div>
                    <span id="score-text">Score: 0</span>
                </div>

                <div class="word-display" id="word-display"></div>

                <div class="input-container">
                    <label id="translation-label">Translation:</label>
                    <div class="input-wrapper">
                        <input 
                            type="text" 
                            id="translation-input" 
                            placeholder="Type your answer..."
                            autocomplete="off"
                        >
                        <button id="submit-btn">Submit</button>
                    </div>
                </div>

                <div class="feedback" id="feedback"></div>
                
                <button class="cancel-btn" id="quiz-cancel-btn">Cancel Test</button>
            </div>

            <div class="result-container" id="results">
                <h2 class="result-title">Your Results</h2>
                <div class="score" id="final-score">0/20</div>
                <p>Here's how you did:</p>

                <div class="result-details" id="result-details">
                    <!-- Results will be populated here -->
                </div>

                <div class="action-buttons">
                    <button class="restart-btn" id="restart-btn">Try Again</button>
                    <button class="home-btn" id="home-btn">Back to Home</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Word list with English and German translations
        const wordsList = [
            { english: "to finish", german: "beenden" },
            { english: "come on", german: "komm schon" },
            { english: "dad", german: "papa" },
            { english: "how are you", german: "wie geht es dir" },
            { english: "listen", german: "zuhören" },
            { english: "to listen", german: "zuhören" },
            { english: "much", german: "viel" },
            { english: "job", german: "arbeit" },
            { english: "studio", german: "studio" },
            { english: "programme", german: "programm" },
            { english: "what programmes", german: "welche programme" },
            { english: "later", german: "später" },
            { english: "midnight", german: "mitternacht" },
            { english: "too late", german: "zu spät" },
            { english: "to get up", german: "aufstehen" },
            { english: "how do you like it", german: "wie gefällt es dir" },
            { english: "it's about a seagull", german: "es geht um eine möwe" },
            { english: "to send", german: "senden" },
            { english: "idea", german: "idee" },
            { english: "to hear", german: "hören" },
            { english: "partner", german: "partner" },
            { english: "to interview", german: "interviewen" },
            { english: "tired", german: "müde" },
            { english: "to be thirsty", german: "durstig sein" },
            { english: "chocolate", german: "schokolade" },
            { english: "usually", german: "normalerweise" },
            { english: "to turn on", german: "einschalten" },
            { english: "all of Plymouth", german: "ganz Plymouth" },
            { english: "behind", german: "hinter" },
            { english: "in front of", german: "vor" },
            { english: "to turn around", german: "umdrehen" },
            { english: "ghost", german: "geist" },
            { english: "age", german: "alter" },
            { english: "also", german: "auch" },
            { english: "metre", german: "meter" },
            { english: "on Saturday afternoon", german: "am Samstagnachmittag" },
            { english: "to ride a bike", german: "fahrrad fahren" },
            { english: "bike", german: "fahrrad" },
            { english: "to stop", german: "anhalten" },
            { english: "ready", german: "bereit" },
            { english: "loud", german: "laut" },
            { english: "scary", german: "gruselig" },
            { english: "traveller", german: "reisender" },
            { english: "to travel", german: "reisen" },
            { english: "part", german: "teil" },
            { english: "to be called", german: "genannt werden" },
            { english: "over 400 year", german: "über 400 jahre" },
            { english: "museum", german: "museum" },
            { english: "money", german: "geld" },
            { english: "child", german: "kind" },
            { english: "ticket", german: "ticket" },
            { english: "pound", german: "pfund" },
            { english: "to imagine", german: "sich vorstellen" },
            { english: "life", german: "leben" },
            { english: "in 1580", german: "im jahr 1580" },
            { english: "sound", german: "geräusch" },
            { english: "attic", german: "dachboden" },
            { english: "boring", german: "langweilig" },
            { english: "mouth", german: "mund" },
            { english: "diary", german: "tagebuch" },
            { english: "address", german: "adresse" },
            { english: "sick", german: "krank" },
            { english: "to hope", german: "hoffen" },
            { english: "bottled", german: "abgefüllt" },
            { english: "to smell", german: "riechen" },
            { english: "me too", german: "ich auch" },
            { english: "out of", german: "aus" },
            { english: "to call", german: "anrufen" },
            { english: "moon", german: "mond" },
            { english: "to walk", german: "gehen" },
            { english: "along the street", german: "entlang der straße" },
            { english: "along the river", german: "entlang des flusses" },
            { english: "oh, it's you", german: "oh, du bist es" },
            { english: "to bark", german: "bellen" },
            { english: "step", german: "schritt" },
            { english: "voice", german: "stimme" },
            { english: "thief", german: "dieb" },
            { english: "hand", german: "hand" },
            { english: "to fight", german: "kämpfen" },
            { english: "coin", german: "münze" },
            { english: "to taste", german: "schmecken" },
            { english: "end", german: "ende" },
            { english: "dream", german: "traum" },
            { english: "perfect", german: "perfekt" },
            { english: "to understand", german: "verstehen" }
        ];

        // DOM elements
        const startScreen = document.getElementById('start-screen');
        const quizContainer = document.getElementById('quiz');
        const resultsContainer = document.getElementById('results');
        const wordDisplay = document.getElementById('word-display');
        const translationLabel = document.getElementById('translation-label');
        const translationInput = document.getElementById('translation-input');
        const submitBtn = document.getElementById('submit-btn');
        const feedback = document.getElementById('feedback');
        const progressText = document.getElementById('progress-text');
        const progressFill = document.getElementById('progress-fill');
        const scoreText = document.getElementById('score-text');
        const finalScore = document.getElementById('final-score');
        const resultDetails = document.getElementById('result-details');
        const restartBtn = document.getElementById('restart-btn');
        const homeBtn = document.getElementById('home-btn');
        const enToDeBtnBtn = document.getElementById('en-to-de-btn');
        const deToEnBtnBtn = document.getElementById('de-to-en-btn');
        const wordCountInput = document.getElementById('word-count');
        const cancelBtn = document.getElementById('cancel-btn');
        const quizCancelBtn = document.getElementById('quiz-cancel-btn');
        const letterFilter = document.getElementById('letter-filter');
        const totalWordCount = document.getElementById('total-word-count');

        // Game state
        let currentWordIndex = 0;
        let score = 0;
        let userAnswers = [];
        let shuffledWords = [];
        let currentMode = ''; // 'en-to-de' or 'de-to-en'
        let wordCount = 20;
        let letterFilterActive = '';

        // Start game in English to German mode
        function startEnToDe() {
            currentMode = 'en-to-de';
            translationLabel.textContent = 'German Translation:';
            translationInput.placeholder = 'Type the German translation...';
            updateWordCount();
            startGame();
        }

        // Start game in German to English mode
        function startDeTiEn() {
            currentMode = 'de-to-en';
            translationLabel.textContent = 'English Translation:';
            translationInput.placeholder = 'Type the English translation...';
            updateWordCount();
            startGame();
        }

        // Update word count based on input or "All" selection
        function updateWordCount() {
            const inputValue = wordCountInput.value.trim();
            if (inputValue === '') {
                // "All Words" option selected
                wordCount = 0; // 0 means all words
            } else {
                wordCount = parseInt(inputValue) || 20;
            }
        }

        // Start the game
        function startGame() {
            startScreen.style.display = 'none';
            quizContainer.style.display = 'flex';
            initGame();
        }

        // Initialize game
        function initGame() {
            currentWordIndex = 0;
            score = 0;
            userAnswers = [];
            
            // Get all words that match our filter
            let filteredWords = [...wordsList];
            
            // Apply letter filter if active
            if (letterFilterActive) {
                if (currentMode === 'en-to-de') {
                    filteredWords = filteredWords.filter(word => 
                        word.english.toLowerCase().startsWith(letterFilterActive.toLowerCase()));
                } else {
                    filteredWords = filteredWords.filter(word => 
                        word.german.toLowerCase().startsWith(letterFilterActive.toLowerCase()));
                }
            }
            
            // Shuffle the filtered words
            shuffledWords = filteredWords.sort(() => Math.random() - 0.5);
            
            // Apply word count limit (if wordCount is 0, use all words)
            if (wordCount > 0 && wordCount < shuffledWords.length) {
                shuffledWords = shuffledWords.slice(0, wordCount);
            }
            
            // If no words match our criteria
            if (shuffledWords.length === 0) {
                alert("No words match your filter criteria. Please try different options.");
                goHome();
                return;
            }
            
            // Update display
            displayCurrentWord();
            updateProgress();
            
            // Clear feedback
            feedback.textContent = '';
            feedback.className = 'feedback';
            
            // Focus on input
            translationInput.focus();
        }

        // Display current word
        function displayCurrentWord() {
            if (currentWordIndex < shuffledWords.length) {
                if (currentMode === 'en-to-de') {
                    wordDisplay.textContent = shuffledWords[currentWordIndex].english;
                } else {
                    wordDisplay.textContent = shuffledWords[currentWordIndex].german;
                }
            }
        }

        // Update progress bar and text
        function updateProgress() {
            const progress = (currentWordIndex / shuffledWords.length) * 100;
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${currentWordIndex + 1}/${shuffledWords.length}`;
            scoreText.textContent = `Score: ${score}`;
        }

        // Check answer
        function checkAnswer() {
            const userAnswer = translationInput.value.trim().toLowerCase();
            let correctAnswer, questionWord;
            
            if (currentMode === 'en-to-de') {
                correctAnswer = shuffledWords[currentWordIndex].german.toLowerCase();
                questionWord = shuffledWords[currentWordIndex].english;
            } else {
                correctAnswer = shuffledWords[currentWordIndex].english.toLowerCase();
                questionWord = shuffledWords[currentWordIndex].german;
            }
            
            const isCorrect = userAnswer === correctAnswer;
            
            // Record answer
            userAnswers.push({
                question: questionWord,
                correctAnswer: correctAnswer,
                userAnswer: userAnswer,
                isCorrect: isCorrect,
                mode: currentMode
            });
            
            // Update score
            if (isCorrect) {
                score++;
                feedback.textContent = '✓ Correct!';
                feedback.className = 'feedback correct';
            } else {
                feedback.textContent = `✗ Wrong! Correct answer: ${correctAnswer}`;
                feedback.className = 'feedback wrong';
            }
            
            // Move to next word after a short delay
            setTimeout(() => {
                currentWordIndex++;
                
                if (currentWordIndex < shuffledWords.length) {
                    displayCurrentWord();
                    updateProgress();
                    translationInput.value = '';
                    feedback.textContent = '';
                    feedback.className = 'feedback';
                    translationInput.focus();
                } else {
                    // Show results when all words are answered
                    showResults();
                }
            }, 1500);
        }

        // Show results
        function showResults() {
            quizContainer.style.display = 'none';
            resultsContainer.style.display = 'block';
            
            finalScore.textContent = `${score}/${shuffledWords.length}`;
            
            // Clear previous results
            resultDetails.innerHTML = '';
            
            // Add result items
            userAnswers.forEach(answer => {
                const resultItem = document.createElement('div');
                resultItem.className = `result-item ${answer.isCorrect ? 'correct' : 'wrong'}`;
                
                let displayText;
                if (answer.mode === 'en-to-de') {
                    displayText = `<div class="word-pair">
                        <strong>${answer.question}</strong> = <span>${answer.correctAnswer}</span>
                    </div>`;
                } else {
                    displayText = `<div class="word-pair">
                        <strong>${answer.question}</strong> = <span>${answer.correctAnswer}</span>
                    </div>`;
                }
                
                resultItem.innerHTML = `
                    ${displayText}
                    <div>
                        Your answer: <span>${answer.userAnswer}</span>
                    </div>
                `;
                
                resultDetails.appendChild(resultItem);
            });
        }

        // Go back to home screen
        function goHome() {
            resultsContainer.style.display = 'none';
            startScreen.style.display = 'block';
        }

        // Cancel the current quiz
        function cancelQuiz() {
            if (confirm("Are you sure you want to cancel the test? Your progress will be lost.")) {
                goHome();
            }
        }

        // Event listeners
        enToDeBtnBtn.addEventListener('click', startEnToDe);
        deToEnBtnBtn.addEventListener('click', startDeTiEn);

        submitBtn.addEventListener('click', () => {
            if (translationInput.value.trim() !== '') {
                checkAnswer();
            }
        });

        translationInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && translationInput.value.trim() !== '') {
                checkAnswer();
            }
        });

        restartBtn.addEventListener('click', () => {
            resultsContainer.style.display = 'none';
            quizContainer.style.display = 'flex';
            initGame();
        });
        
        homeBtn.addEventListener('click', goHome);

        wordCountInput.addEventListener('input', () => {
            wordCount = parseInt(wordCountInput.value);
        });

        cancelBtn.addEventListener('click', () => {
            // This is for the cancel button on the start screen
            // No confirmation needed as no progress to lose
            goHome();
        });
        
        quizCancelBtn.addEventListener('click', cancelQuiz);

        letterFilter.addEventListener('click', (e) => {
            if (e.target.classList.contains('letter-btn')) {
                const letter = e.target.getAttribute('data-letter');
                letterFilterActive = letter;
                const buttons = letterFilter.querySelectorAll('.letter-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
            }
        });
        
        // Set "All" button as active by default
        document.querySelector('.letter-btn[data-letter=""]').classList.add('active');

        // Update total word count display
        totalWordCount.textContent = wordsList.length;

        // Add event listener for word count buttons
        const wordCountButtons = document.querySelectorAll('.word-count-btn');
        wordCountButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                wordCountButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                const count = button.getAttribute('data-count');
                if (count === 'all') {
                    wordCountInput.value = '';
                } else {
                    wordCountInput.value = count;
                }
            });
        });
        
        // Set default active button (20 words)
        document.querySelector('.word-count-btn[data-count="20"]').classList.add('active');
    </script>
</body>
</html>
