
/*
This script implements a toggle system for the Y button on an Xbox controller with the following functionality:
Toggle Mechanism:
Pressing Y button toggles the script on/off
Uses a 'run' variable to track state (0=off, 1=on)
Main Functionality:
When activated, it runs a predefined combo sequence
The combo performs a specific pattern of Y button presses:
Presses Y for 4 seconds
Releases for 140ms
Presses Y again for 3 seconds
Releases for 80ms
This pattern repeats while the script is active
Safety Features:
Includes button value resets to prevent unintended activations
Has threshold checks (>80% for activation, <80% for deactivation)
*/

// Variable to track if the script is running (0 = off, 1 = on)
int run;

main {
    // Toggle script on/off when Y button is pressed
    if(event_press(XB1_Y)) {
        if(run == 1) {
            run = 0;  // Turn off if currently running
        } else {
            run = 1;  // Turn on if currently off
        }
    }
    // Reset Y button value to prevent unintended activations
    set_val(XB1_Y, 0)

    // Set Y button value based on script state
    if(run == 1) {
        set_val(XB1_Y, 100);  // Fully press Y when script is on
    } else if(run == 0) {
        set_val(XB1_Y, 0);    // Release Y when script is off
    }

    // Control combo execution based on Y button value
    if(get_val(XB1_Y) > 80) combo_run(TypeComboName);   // Start combo when Y is pressed above 80%
    if(get_val(XB1_Y) < 80) combo_stop(TypeComboName);  // Stop combo when Y is pressed below 80%
}

// Combo sequence that executes a specific pattern of Y button presses and releases
combo TypeComboName {
    set_val(XB1_Y, 100);  // Press Y fully
    wait(4000);           // Hold for 4 seconds
    set_val(XB1_Y, 0);    // Release Y
    wait(140);            // Wait 140ms
    set_val(XB1_Y, 100);  // Press Y fully again
    wait(3000);           // Hold for 3 seconds
    set_val(XB1_Y, 0);    // Release Y
    wait(80);             // Wait 80ms
}
