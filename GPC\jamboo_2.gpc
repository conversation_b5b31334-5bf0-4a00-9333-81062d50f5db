unmap 24;  // Paddle Right 1
unmap 25;  // Paddle Right 2
unmap 26;  // Paddle Left  1
unmap 27;  // Paddle Left  2
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define DOnotUSE      =   1;
define DelayNormal   =  80;
define DelayRnbwHOLD = 160;
define DelayRest     = 200;
define DelayLeft_move= 500;
define time_to_dblclick     = 300; // Time to Double click     

define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_VIEW;  
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL        =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
//--------------------------------------------------------------   
define UP         = 0;
define UP_RIGHT   = 1;
define RIGHT      = 2;
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;

int shootpower=220;
int tempshotpower=220;
int finesseshootpower=230;
int limit=250;
int yachse;
int xachse;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;

int b_tap; 
int Finesse  = TRUE;
int onoff_penalty;

int LeftStickSens = 90;
int CrossDribbleSens = 120;
int SKILL_1KS = TRUE; 
int SKILL_2KS = TRUE; 
int SKILL_3KS = TRUE; 
int rs_move;

int switch;

int hold_RB_LB;
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
      //   double click : Sprint Button 
       if (event_press(SprintBtn) && !b_tap) {      
                   combo_run(ONE_TAP);                  
       // Second tap                                    
       }else if (event_press(SprintBtn) && b_tap ){ 
                  b_tap=FALSE;                         
                  right_on = 0;combo_run(BRIDGE_DRIBBLE);
       }                                               
	//-------------------------------------------------------------- 
	//--- DEFENSE ---
	if(defence_on) f_defence();                 
//--------------------------------------------------------------
// SPRINT
	if(get_val(XB1_RT) && event_press(XB1_LB)){GetLXY();combo_run(Sprint);} 
//--------------------------------------------------------------
	if(get_val(XB1_B) && get_ptime(XB1_B)>= shootpower){
		set_val(XB1_B,  0);
														  
	}
	
	if(get_val(XB1_LB) )  {
		shootpower = finesseshootpower;
	}else {shootpower = tempshotpower;}

	//--------------------------------------------------------------
	//                                                           
	if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){
		LX = get_val(MOVE_X);                                      
		LY = get_val(MOVE_Y);                                      
		calc_zone ();
	}
	//--------------------------------------------------------------
	// Start RS Skills

			if(get_val(XB1_PR1)){
			    if(abs(get_val(SKILL_STICK_X))> 60 || abs(get_val(SKILL_STICK_Y))> 60){                                     
					calc_zone_RS ();
					check_direction ();
					if(zone_RS == zone_p) combo_run(NUTMEG_SKILL_STRAIGHT);
					if(direction_ON) combo_run(NUTMEG_SKILL);
			    }
			}
	
if(get_val(XB1_PL1) || get_val(XB1_PL2) || get_val(XB1_PR1)){
		
		if(get_val(XB1_PL1)){
	    if(abs(get_ival(PS4_RX))> 60 || abs(get_ival(PS4_RY))> 60  ){                                       
			calc_zone_RS ();
			check_direction ();
			rs_move = TRUE;
			if(direction_ON) ACTIVE = FAKE_SHOT_SKILL; combo_run(BALL_ROLL_SCOOP_TURN);
	    }
	}
				if(get_val(XB1_PL2)){
		    if(abs(get_ival(PS4_RX))> 60 || abs(get_ival(PS4_RY))> 60  ){                                       
				calc_zone_RS ();
				check_direction ();
				rs_move = TRUE;
				if(direction_ON) ACTIVE = ACTIVE = FLAIR_ROULETTE_SKILL; combo_run(ROULETTE);
		    }
		}	
	
		if(abs(get_ival(PS4_RX))< 20 && abs(get_ival(PS4_RY))< 20){
			rs_move = FALSE;
		}
	
		set_val(TRACE_6,rs_move);
		

		if(ACTIVE == FLAIR_ROULETTE_SKILL && combo_running(ROULETTE)) set_val(PlayerRun,100);// L1		
		if(ACTIVE == BALL_ROLL_FAKE_TURN && combo_running(TURN_AND_SPIN)) set_val(PaceCtrol,100);//  Ball Roll Fake Turn L2
		//-----------------------------------------------------------                                          
		set_val(SKILL_STICK_X,0);   
		set_val(SKILL_STICK_Y,0);
	
	}
	// End of RS Skills
	//--------------------------------------------------------------
	//-------------------------------------------------------------
    
    if (event_press(XB1_RS)){ combo_run(NUTMEG_SKILL_STRAIGHT);   }
      set_val(XB1_RS,0); 
	//--------------------------------------------------------------

	//--------------------------------------------------------------
    // MIRZA
   //	if(get_val(XB1_PR1)) {set_val(XB1_RB,100);}	
	//if(event_release(XB1_PR1)){
	//	combo_run(mirza);
	//} 
	//--------------------------------------------------------------
	
/*	if(hold_RB_LB){
        if(get_val(XB1_PL2)){
            set_val(XB1_LB,100);
            set_val(XB1_RB,100);
        }
        if(event_release(XB1_PL2)) hold_RB_LB = FALSE;
    }
 */
 
 //	if (event_press(XB1_PL2)){combo_run(PlayerStop) } 
 //	if (event_press(XB1_PR1)){combo_run(FAKE_PASS_EXIT) } 


	if(!get_val(XB1_A)) {
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_Y,100);
		}
 // THREADED THROUGH PASS
	}


	//--------------------------------------------------------------
	if(get_val(ShotBtn)) set_val(SprintBtn,0);                

	////////////////////////////////  
	// LED color indication           
	if(onoff_penalty)   colorled(0,0,1,0); 
	else	colorled(0,1,0,0);
////////////////////////////////  
//--------------------------------------------------------------
//  turn ON Penalty  hold  L1 and press OPTIONS
	if(get_val(PS4_L1)){
		if(event_press(PS4_OPTIONS)){
		onoff_penalty = !onoff_penalty;
		}
		set_val(PS4_OPTIONS,0);
    }
    if(onoff_penalty){

		if (get_val(XB1_X))	{set_val(PS4_LX,0);set_val(PS4_LY,0);}			// Middle 
		if (get_val(PS4_LEFT))	{set_val(PS4_LX,-78);set_val(PS4_LY,-38);} 	// Left Up 
		if (get_val(PS4_RIGHT))	{set_val(PS4_LX,78);set_val(PS4_LY,-38);} 	// Right Up 
		if (get_val(PS4_UP))	{set_val(PS4_LX,-54);set_val(PS4_LY, 90);} 	// Left Down 
		if (get_val(PS4_DOWN))	{set_val(PS4_LX,54);set_val(PS4_LY,90);} 	// Right Down 
   }
//--------------------------------------------------------------
/*	if(get_val(PlayerRun)){
		sensitivity(PS4_LX,50,LeftStickSens);
		sensitivity(PS4_LY,50,LeftStickSens);
	}
	if(get_val(FinesseShot)){
		sensitivity(PS4_LX,50,CrossDribbleSens);
		sensitivity(PS4_LY,50,CrossDribbleSens);
	}
*/	
//--------------------------------------------------------------
	if(get_val(ShotBtn)){
		CORNER ();
	}   
//--------------------------------------------------------------
   
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////


combo BALL_ROLL_SCOOP_TURN {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PS4_L2,100);
    wait(40); 
    LA_L_R()
    set_val(PS4_L2,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PS4_L2,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
}

combo spring {  
	wait(40);
	set_val(XB1_LB,100);
	set_val(XB1_Y,100);          
} 

combo FAKE_PASS_EXIT { 
	set_val(SprintBtn,100);
	set_val(ShotBtn,100);  
	wait(40);
	set_val(SprintBtn,100);
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	wait(60);
	set_val(SprintBtn,100);
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}  

combo PlayerStop {

     set_val(XB1_LB, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 0);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 0);
    wait(50);
    set_val(XB1_LB, 0);

    hold_RB_LB = TRUE;
}



                      
                        
 

combo CANCEL_SHOOT {
     set_val(ShotBtn,100);
     wait(290);
     set_val(PS4_L2,100);
     set_val(PS4_R2,100);
     wait(300);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo SPEED_BOOST {  
    	        
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	
	wait(300);
	
	set_val(SprintBtn,100);
	
	wait(500);
	
	
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo SPEED_BOOST_2 {  
    
    set_val(PlayerRun,100);
    
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
	
	wait(40);
	
    LA (move_lx,move_ly);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	
	wait(200);
	
	
	set_val(SprintBtn,100);
	
	wait(500);
	
	
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo NUTMEG_SKILL {
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	wait(20);
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	wait(100);
    
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++						
combo CANCELED_TURN_AND_SPIN {  
    RA_UP ();      // up   
    wait(w_rstick);         
    RA_ZERO ();    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
        wait(200);
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);

    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    wait(300);
}						   
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++						   
combo TURN_AND_SPIN {  
  if(ACTIVE == BALL_ROLL_FAKE_TURN ) hold_btn = 200;//  Ball Roll Fake Turn L2 
	else hold_btn = 1;      
 wait(hold_btn);
	RA_UP ();      // up   
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);          
	RA_L_R () ;    // Left or Right 
	wait(w_rstick);    
}						   
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++						   
combo BALL_ROLL_CHOP {         
	RA_L_R () ;    // Left or Right 
	wait(300);                     
	RA_ZERO ();    // ZERO         
	wait(w_rstick);                
	RA_OPP () ;    // Left or Right
	wait(300);                    
}  						   
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++			   
combo ROULETTE1 {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}						  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FEINT_EXIT {             
	RA_OPP ();                    
	wait(w_rstick);               
	RA_DOWN (); // down           
	wait(w_rstick);               
	RA_L_R ();  //  <-/->         
	wait(w_rstick);               
} 			
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

combo NUTMEG_SKILL_STRAIGHT {
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	wait(20);
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	
    RA (LX,LY);
    set_val(SprintBtn,100);
	wait(100);
	
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo BALL_ROLL {               
	RA_L_R () ;    // Left or Right 
	wait(250);  
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo LATERAL_HEELtoHEEL {  
    set_val(PlayerRun,100);
    RA_OPP () ;            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_ZERO ();            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_L_R () ;            
    wait(60);//           
    wait(300);            
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo ADVANCED_CROQUETA { 
  set_val(PlayerRun,100); 
  RA_L_R ();      // <-/->
  wait(300);//            
    set_val(PS4_L2,100);     
    set_val(PS4_R2,100);     
  LA_L_R();               
  wait(800);// 800        
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FakeShotz {
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_LB, 100);
	set_val(XB1_X, 100);
	wait(50);
	set_val(XB1_LB, 100);
	set_val(XB1_A, 100);
	set_val(XB1_X, 100);
	wait(50);
	set_val(XB1_LB, 100);
	set_val(XB1_A, 100);
	set_val(XB1_X, 0);
	wait(50);
	set_val(XB1_LB, 100);
	set_val(XB1_A, 0);
	wait(50);
	set_val(XB1_LB, 0);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo mirza {
    wait(100); 
    set_val(XB1_RT, 100);
    wait(300); 
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FAKE_SHOT {  
wait(100);
	set_val(XB1_X,100);  
	wait(40);              
	set_val(XB1_X,100);  
	set_val(XB1_A,100); 
	wait(60);             
	set_val(XB1_X,0);  
	set_val(XB1_A,100);
	wait(60);           
} 
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo FAKE_SHOT_RT { 
wait(100);
	set_val(XB1_RT,100);
	set_val(XB1_X,100);  
	wait(40); 
	set_val(XB1_RT,100);
	set_val(XB1_X,100);  
	set_val(XB1_A,100); 
	wait(60); 
	set_val(XB1_RT,0);
	set_val(XB1_X,0);  
	set_val(XB1_A,100);
	wait(60);           
}  
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo ONE_TAP {                                    
    b_tap=TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    b_tap=FALSE;                                  
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo DRIVEN_SHOT  {     
   if( Finesse ) set_val(FinesseShot,100); 
   set_val(ShotBtn,100); 
   wait(100);
   set_val(ShotBtn,100); 
   if( Finesse ){ set_val(PlayerRun,100); set_val(FinesseShot,100); }
   wait(40);
   set_val(ShotBtn,100); 
   if( Finesse ) set_val(FinesseShot,100); 
   wait(390- 140);            
   if( Finesse ) set_val(FinesseShot,100); 
   set_val(ShotBtn,  0); 
   wait(60);             
   if( Finesse ) set_val(FinesseShot,100); 
   set_val(ShotBtn,100); 
   wait(100);            
   }
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo BRIDGE_DRIBBLE {
   set_val(FinesseShot,100);
   wait(40);
   set_val(FinesseShot,   0);
   wait(40);
   set_val(FinesseShot,100);
   wait(40);
}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
combo Sprint {
set_val(XB1_LB,0);
set_val(XB1_RX,(xachse));
set_val(XB1_RY,(yachse));
wait(300);}
//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(PS4_LX) >= 50) move_lx = 100;
    else if(get_val(PS4_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(PS4_LY) >= 50) move_ly = 100;
    else if(get_val( PS4_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
int RS_x, RS_y ;
int zone_RS;
function calc_zone_RS( ){

    if(get_val(PS4_RX) >= 50) RS_x = 100;
    else if(get_val(PS4_RX) <= -50) RS_x = -100;
    else RS_x = 0;
    if(get_val(PS4_RY) >= 50) RS_y = 100;
    else if(get_val( PS4_RY) <= -50) RS_y = -100;
    else RS_y = 0;
    
    
    if(RS_x != 0 || RS_y != 0) {
        zone_RS = 0; while(zone_RS < 8) {
            if(dchar(zone_RS) == RS_x && dchar(8 + zone_RS) == RS_y) {
                break;
            } zone_RS += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//============================================================================================

//--- Defense 
int high_press:
int defense_timer;
int team_timer;
int defence_on = TRUE;
int team_presure_cooldown;
function f_defence (){
    
    //--- JOCKEY
     if(get_val(PaceCtrol) && get_val(SprintBtn)){
         high_press = TRUE;         
     }
     //--- Hard Tackle ----------------
   //  if(get_val(PaceCtrol) && get_val(SprintBtn)){
   //      if(get_val(ShotBtn)) set_val(FinesseShot,100);         
   //  }
     //------------------------------
     if(high_press) {
        // set_val(XB1_VIEW,100);
         if(event_release(PaceCtrol)){
             high_press = FALSE;
         }
         //--- TEAM PRESURE
         //
         if(!team_presure_cooldown){
             if(event_press(PS4_UP)) combo_run(TEAM_PRESURE);
             set_val(PS4_UP,0);
         }
         //--- CONTAIN    
     
         if(get_val(PassBtn)){
             defense_timer += get_rtime();
            if(defense_timer > 500 && defense_timer < 535){    
                set_val(PassBtn,100);         
            }else{
                set_val(PassBtn,  0);// CONTAIN
            }
            if(defense_timer > 535) defense_timer = 0;
         }
         
         
           
     }//---------------------------
}
     


combo TEAM_PRESURE {
    set_val
(PS4_DOWN,100);
    wait(50);
    wait(50);
    set_val(PS4_LEFT,100);
    wait(50);
    wait(50);
}

//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int temp_zone;
int direction_ON;
//============================================================================================
function check_direction (){
	
	direction_ON = FALSE;
	
	if(zone_p == 0 ){
	
		if(zone_RS > 0 && zone_RS < 4){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 4 && zone_RS < 8){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 1 ){
	
		if(zone_RS > 1 && zone_RS < 5){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 5 && zone_RS < 8 || zone_RS == 0 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 2 ){
	
		if(zone_RS > 2 && zone_RS < 6){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS == 7 || zone_RS == 0 || zone_RS == 1 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 3 ){
	
		if(zone_RS > 3 && zone_RS < 7){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS >=0 && zone_RS < 3 ){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 4 ){
	
		if(zone_RS > 0 && zone_RS < 4){right_on = FALSE; direction_ON = TRUE; }  
		if(zone_RS > 4 && zone_RS < 8 ){right_on = TRUE; direction_ON = TRUE; }
	}
	
	else if(zone_p == 5 ){
	
		if(zone_RS > 5 && zone_RS < 8  || zone_RS == 0){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 1 && zone_RS < 5 || zone_RS == 0){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	else if(zone_p == 6 ){
	
		if(zone_RS == 7 || zone_RS == 0 || zone_RS == 1){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS > 2 && zone_RS < 6){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	
	else if(zone_p == 7 ){
	
		if(zone_RS >= 0 && zone_RS < 3){ right_on = TRUE; direction_ON = TRUE;}  
		if(zone_RS >  3 && zone_RS < 7){ right_on = FALSE; direction_ON = TRUE;}
	}
	
	
	set_val(TRACE_1,right_on);
	
	set_val(TRACE_2,zone_p);
	
	set_val(TRACE_3,zone_RS);
	
	
}
//============================================================================================
function RA (XX,YY){
	set_val(PS4_RX,XX);
	set_val(PS4_RY,YY);
}
//============================================================================================
//============================================================================================
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}
//============================================================================================
function GetLXY () {xachse = get_val(XB1_LX);yachse = get_val(XB1_LY);}
//============================================================================================
//============================================================================================
function CORNER () {    
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    { 
    	set_val(PS4_LX, 100); 
		set_val(PS4_LY, -50); 
    }
      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{ 
		set_val(PS4_LX, 100); 
		set_val(PS4_LY, 50); 
	}
  
 
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {
	    set_val(PS4_LY, -100); 
	    set_val(PS4_LX, -50); 
    }
    
   if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
   { 
		set_val(PS4_LY, 100);  
        set_val(PS4_LX, -50); 
   }
}
//============================================================================================
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             