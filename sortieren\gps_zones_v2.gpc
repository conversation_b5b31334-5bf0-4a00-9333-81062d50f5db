// GPS Zones Calculation v2

// Constants for sensor types and measurements
const int POLAR_LS = 1;    // Left sensor constant
const int POLAR_RS = 2;    // Right sensor constant
const int POLAR_ANGLE = 3; // Angle constant

// Define the order array
int order[8] = {0, 1, 2, 3, 4, 5, 6, 7};

// Global variable for polar_angle
int polar_angle;

// Function to get polar values from sensors
function int get_ipolar(int sensor_type, int measurement_type) {
    // Add your sensor reading logic here
    // This should interface with your GPS/sensor hardware
    if (sensor_type == POLAR_LS) {
        // Read from left sensor
        return get_sensor_value(1); // Replace with actual sensor reading function
    } else if (sensor_type == POLAR_RS) {
        // Read from right sensor
        return get_sensor_value(2); // Replace with actual sensor reading function
    }
    return 0; // Default return for invalid sensor type
}

// Function to calculate zone for POLAR_LS
function int calc_zone() {
    int polar_LS;
    int index;
    int zone_p;
    
    polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Validate sensor reading
    if (polar_LS < 0 || polar_LS >= 360) {
        return -1; // Error code for invalid reading
    }
    
    index = ((polar_LS + 23) % 360) / 45;  // Using 23 instead of 22.5 to avoid floating point
    zone_p = order[index];
    return zone_p;
}

// Function to calculate zone for POLAR_RS
function int calc_RS() {
    int index;
    int zone_RS;
    
    polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
    
    // Validate sensor reading
    if (polar_angle < 0 || polar_angle >= 360) {
        return -1; // Error code for invalid reading
    }
    
    index = ((polar_angle + 23) % 360) / 45;  // Using 23 instead of 22.5 to avoid floating point
    zone_RS = order[index];
    return zone_RS;
}

// Main function
function main() {
    int result_LS = calc_zone();
    int result_RS = calc_RS();
    
    // Error checking
    if (result_LS == -1 || result_RS == -1) {
        combo_write(-1);    // Signal error condition
        return;
    }
    
    // Output results
    combo_write(result_LS);    // Send left sensor zone
    combo_write(result_RS);    // Send right sensor zone
}