# GPC Functions / Actions

The GPC script language comes standard with many built-in functions and commands. You will have to use some of them to be able to create a working script.

All functions are listed in the GPC Language Reference.

GPC Language Reference Explained
If you lookup a function in the GPC Language Reference you will notice the Parameter and Return Value sections.

If a function has one or more Parameters or a Return Value they are of a specific data type.

Example get_val:

fix32 get_val(uint8 io);
Parameters: io Index of an I/O value.
Return Value: The current I/O value.

The Parameters io data type is uint8.
The Return Value data type in this case is fix32.

When you want to use this function you have to supply the argument (parameter value) in a matching data type. To make use of the return value you have to compare it with a value or store it in a variable of the matching data type too. The functions example code in the GPC Language Reference are very useful if you have trouble with a function.

If you insert the function name into the GPC Script IDE you will notice after typing the opening bracket it will show the same information:
The Parameters are listed inside the brackets.
The Return Value is shown directly after the colon if the function has one.

When your variable is of a different data type than the function requires or returns you can use type casting. Example:

int8 Value=100;
if (get_val(BUTTON_1) == (fix32)Value) { } // (fix32) , (int8) , ...
For a list of all available data types click here.

Functions Sorted By Actions
Here are some GPC functions often used in scripts with links to the GPC Language Reference. The linked GPC Language Reference code examples use the GPC Designators for input/output names like BUTTON_5 or STICK_1_X , column GPC.

Read Input
Read the value of the real current state of an input get_actual.
get_val returns the value of the current state of the input in the script, that may be different of what you are currently pressing on your controller.

Check if an input is outside is_active or inside is_release its resting range.

Catch a state change of an input when it changes from released to pressed state event_active or vice versa event_release

Check if an input is currently active check_active or released check_release for a time or longer (in milliseconds).

Read the time that has passed since the input last entered in active state time_active or in release state time_release
Be careful with those as a return value of time_active doesn't mean that the input is currently active. You may want to use check_active / check_release instead.

GPC Language Reference Links
Get Input: get_val get_actual - check_active check_release event_active event_release - check_active check_release - time_active time_release

Send Output
If you want to automatically press one button for a specific time or multiple buttons in a specific order you will use a combo most of the time.

GPC Language Reference Links
Set Output: combo_run combo_stop combo_restart combo_pause set_val wait call

LED Control
You can utilize the LED color of the Titan Two and the Controller for visual feedback via changing its color and or blinking. Some games are changing the color of the controller during some actions, like low health. You can monitor the led to perform script actions on color change too.

GPC Language Reference Links
Read State: led_get led_get_actual led_vmget
Set State: led_reset led_set led_vmset

Rumble Control
You can utilize the rumbles of the controller for some feedback. Like rumble twice when disabling a feature of your script. Some games will rumble the controller when you are hit or accelerating to fast. You can monitor the rumbles to perform script actions too.

GPC Language Reference Links
Read State: ffb_get ffb_get_actual
Set State: ffb_reset ffb_set

Segment Display Control
You can utilize the segment display of the Titan Two for visual feedback displaying numbers/symbols/letters. GPC Language Reference Links
Set State: display_overlay

Time Tracking
There are several time related functions available:

system_time gets the time in milli-seconds since the device has power-up, or last reset.
elapsed_time gets the elapsed time in milli-seconds since previous interaction of main.

For button activity time checks:
Read the time that has passed since the input last entered in active state time_active or in release state time_release
Be careful with those as a return value of time_active doesn't mean that the input is currently active. You may want to use check_active / check_release instead.

Persistent Memory
Store values to reuse them after power cycling the Titan Two or read settings of an Interactive Configuration.

Read: pmem_load pmem_read
Write: pmem_save pmem_write