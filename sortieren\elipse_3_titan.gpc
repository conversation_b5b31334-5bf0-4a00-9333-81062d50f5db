#include <titanone.gph>
#pragma METAINFO("Ellipse Movement", 1, 0, "Assistant")

// Constants for stick identification 
const int X_RATIO = 100;
const int Y_RATIO = 40;
const int ROTATION_SPEED = 25;
const int ROTATION_RADIUS = 100;

// Global variables
int x, y;
int last_x = 0;    
int last_y = 0;    
int last_step = 0; 
int rotation_step = 0;
int rotation_counter = 0;

// Lookup tables for rotation
const int cos_table[] = {100, 71, 0, -71, -100, -71, 0, 71};
const int sin_table[] = {0, 71, 100, 71, 0, -71, -100, -71};

// Function to determine zone (0-7) based on stick position
int get_zone(int x, int y) {
    if (abs(x) > abs(y) * 2) {
        if (x > 0) return 0;  // Right
        return 4;             // Left
    } 
    else if (abs(y) > abs(x) * 2) {
        if (y > 0) return 2;  // Down
        return 6;             // Up
    }
    else {
        if (x > 0 && y > 0) return 1;      // Down-Right
        if (x < 0 && y > 0) return 3;      // Down-Left
        if (x < 0 && y < 0) return 5;      // Up-Left
        if (x > 0 && y < 0) return 7;      // Up-Right
        return 0;
    }
    return 0;
}

init {
    // Initialize virtual machine control
    vm_tctrl(-2);
}

main {
    // Get current stick values
    x = get_val(STICK_1_X);
    y = get_val(STICK_1_Y);

    // Update last position when stick is moved without L3 pressed
    if ((abs(x) > 20 || abs(y) > 20) && !get_val(BUTTON_4)) {
        int zone = get_zone(x, y);
        last_step = zone;
        last_x = x;
        last_y = y;
        
        // Normal stick movement
        set_val(STICK_1_X, x);
        set_val(STICK_1_Y, y);
    }
    // Elliptical movement when L3 is pressed
    else if (get_val(BUTTON_4)) {
        if (x == 0 && y == 0) {
            // Auto-rotation using stored direction
            rotation_step = last_step;
            
            // Calculate position based on rotation step
            switch(rotation_step) {
                case 0: 
                    x = ROTATION_RADIUS; 
                    y = 0; 
                    break;
                case 1: 
                    x = ROTATION_RADIUS * 71 / 100;
                    y = ROTATION_RADIUS * 71 / 100; 
                    break;
                case 2: 
                    x = 0; 
                    y = ROTATION_RADIUS; 
                    break;
                case 3: 
                    x = -ROTATION_RADIUS * 71 / 100;
                    y = ROTATION_RADIUS * 71 / 100; 
                    break;
                case 4: 
                    x = -ROTATION_RADIUS; 
                    y = 0; 
                    break;
                case 5: 
                    x = -ROTATION_RADIUS * 71 / 100;
                    y = -ROTATION_RADIUS * 71 / 100; 
                    break;
                case 6: 
                    x = 0; 
                    y = -ROTATION_RADIUS; 
                    break;
                case 7: 
                    x = ROTATION_RADIUS * 71 / 100;
                    y = -ROTATION_RADIUS * 71 / 100; 
                    break;
            }

            // Update rotation
            rotation_counter += ROTATION_SPEED;
            if (rotation_counter >= 100) {
                rotation_counter -= 100;
                rotation_step = (rotation_step + 1) % 8;
            }
        }

        // Apply elliptical scaling when triggers are pressed
        if (get_val(BUTTON_7) && get_val(BUTTON_8)) {  // L2 & R2
            if (abs(x) > 0 || abs(y) > 0) {
                // Apply elliptical transformation
                x = (x * X_RATIO) / 100;
                y = (y * Y_RATIO) / 100;
                
                // Apply final values to stick
                set_val(STICK_1_X, x);
                set_val(STICK_1_Y, y);
            }
        }
    }
}