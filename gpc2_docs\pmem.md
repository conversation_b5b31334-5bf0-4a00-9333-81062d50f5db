# Persistent Memory Functions

Persistent Memory allows you to store values to reuse them at a later time when powering on the T2 again and read settings of an Interactive Configuration.

The available functions:
Init: pmem_load
Read: pmem_read
Write: pmem_save pmem_write

ATTENTION: None of the pmem_ functions should be called on every interaction of main.

To use the pmem_ functions you need to know the Storage Size of the Data Types of your variables.
Not caring about their sizes will result in getting wrong values back when trying to read them from pmem again.

Introduction
Each slot of the Titan Two has 128 bytes (0..127) of PMEM storage available.
How many variables/data you can store into this space depends on the data types you want to store.

A bit is the smallest unit of storage. A bit stores just a 0 or 1.
1 bytes is a group of 8 bits (0..7). 2 bytes == 16 bits, 4bytes == 32 bits.

You can think of the PMEM storage like a table consisting of 128 rows with 8 columns.

Byte Offset	Bit offsets
0	0	1	2	3	4	5	6	7
1	0	1	2	3	4	5	6	7
2	0	1	2	3	4	5	6	7
..	0	1	2	3	4	5	6	7
127	0	1	2	3	4	5	6	7
You can read and write this table only by byte_offset(rows) directly, not by bit_offset(columns).

Example:
The size of a variable of type fix32 is 4 bytes, 32bits : fix32 antirecoilX=13.5;
You can store it starting at any byte_offset.row you want, but because of its size it will write to the next 3 rows too. This limits the valid starting byte_offsets to 0..124, as writing it to 124 will write to 124,125,126,127 too.

A byte_offset.row can only be used by a single variable. The start byte_offset + the size of the data type can not overlap with the starting byte_offset.row of another one.
When storing a 4 byte size variable to byte_offset 0 (uses 0,1,2,3) the next available byte_offset.row is 4. To store a variable of type int16 (2 byte==16bits) you can't use 0,1,2,3 as its already in use, but you don't have to use 4, you can use any other starting byte_offset.row where all next 16bits are not already in use.

To store a boolean (0 or 1) you need 1 byte==8 bits. This sounds wrong at first. You have to remember we can write to the byte_offsets.rows only, not to a single bit_offset.
You can use a single variable to hold 8 bits(8bits==1byte) and write this variable to a byte_offset.row. The downside of this is you need a bit of code to handle this correctly.
If you have enough unused byte-offsets.rows available it is easier to just use one byte offset for one boolean (like an on/off toggle flag).

Usage Example
ATTENTION: None of the pmem_ functions should be called on every interaction of main.

To be able to access the persistent memory contents you have to use the function pmem_load first.
pmem_load() and pmem_read(…) should be called once at the init section of the script
and pmem_write(…) and pmem_save() only after a change of a values of a variable you want to update in pmem.

On pmem_read you prefix your variable with &	
pmem_read(1,&yourvariable);
On pmem_write you do NOT use &	
pmem_write(1,yourvariable);
Rapid Fire example with saving/loading speed adjustments and on/off toggle state

bool RapidFire;  // 1 byte == 8bits
uint16 RFPress;  // 2 bytes==16bits
uint16 RFRelease;// 2 bytes==16bits
 
init {
  pmem_load(); // this loads the memory correlated with the current memory slot
  pmem_read(0,&RapidFire); // rapid fire on/off state, byteoffset 0, 8bit (0)
  pmem_read(1,&RFPress);   // rapid fire press time, byteoffset 1, 16bit (1+2)
  pmem_read(3,&RFRelease); // rapid fire release time, byteoffset 3, 16bit (3+4)
 
  if (RFPress==0) RFPress=20;     // use maximum speed when no value
  if (RFRelease==0) RFRelease=20; // use maximum speed when no value
 
  // info output to GTuner IV Output Panel
  printf("Rapid Fire state (1=On,0=Off) : %d , Press Time: %d, Release Time: %d"
                                                 ,RapidFire,RFPress,RFRelease);
}
 
main {
   // hold L2/LT & press DPad_UP to toggle rapid fire
   if (get_actual(BUTTON_8) && event_active(BUTTON_10)) {
       RapidFire=!RapidFire;                        // invert current state
       printf("Rapid Fire is now: %d",RapidFire); 
       pmem_write(0,RapidFire); // write to pmem, is not saved yet
       pmem_save();             // save it now
   }
 
   // hold L2/LT & press DPad_Left to increase speed / decrease wait & press time
   if (get_actual(BUTTON_8) && event_active(BUTTON_12)) {
       RFRelease=RFPress=clamp(RFPress -=10,20,1000); // limit values to 20..1000
       printf("Rapid Speed is now: %d | %d",RFPress,RFRelease); 
       pmem_write(1,RFPress);   // write to pmem, is not saved yet
       pmem_write(3,RFRelease); // write to pmem, is not saved yet
       pmem_save();             // save it now
   }
 
   // hold L2/LZ & press DPad_Right to decrease speed/increase wait&press time
   if (get_actual(BUTTON_8) && event_active(BUTTON_13)) {
       RFRelease=RFPress=clamp(RFPress +=10,20,1000); // limit values to 20..1000
       printf("Rapid Speed is now: %d | %d",RFPress,RFRelease);
       pmem_write(1,RFPress);    // write to pmem, is not saved yet
       pmem_write(3,RFRelease); // write to pmem, is not saved yet
       pmem_save();             // save it now
   }
   // when enabled and fire button is active run rapid fire combo
   if (RapidFire && get_actual(BUTTON_5)) combo_run(cRapidFire);
}
 
combo cRapidFire {
   set_val(BUTTON_5,100); wait(RFPress); // press button
   set_val(BUTTON_5,0); wait(RFRelease); // release button
}


pmem_load
pmem_load - Load persistent memory

Description
void pmem_load();
void pmem_load(uint8 slot_no);
Load the persistent memory contents correlated with the current memory slot, or with slot_no, to a RAM structure, so it can be accessed by pmem_read() and pmem_write().

If used, pmem_load() should be called once at the init section of the script.
Optional Parameter
slot_no: A valid memory slot number, between 1 and 9.
Return Value
No value is returned.

Persistent Memory
Persistent Memory Flow

The persistent memory is an array of 128 bytes that can be used to store data structures such that they can continue to be accessed even after the end of the GPC script that created or last modified them.

For performance reasons the operations pmem_read() and pmem_write() are performed in RAM, therefore the persistent memory contents should first be loaded into RAM space by pmem_load() and, if modified, saved with pmem_save().

Examples
Example #1 pmem_load() example
init {
    pmem_load();
}

![alt text](image.png)

pmem_save
pmem_save - Save persistent memory

Description
void pmem_save();
void pmem_save(uint8 slot_no);
Save the contents from the RAM structure that mirrors the persistent memory to the actual persistent memory correlated with current memory slot, or with slot_no.

pmem_save() should be called at least once after modify the contents using pmem_write(), so the modified data can be accessible on subsequent executions of the GPC script.
Optional Parameter
slot_no: A valid memory slot number, between 1 and 9.
Return Value
No value is returned.

Persistent Memory
Persistent Memory Flow

The persistent memory is an array of 128 bytes that can be used to store data structures such that they can continue to be accessed even after the end of the GPC script that created or last modified them.

For performance reasons the operations pmem_read() and pmem_write() are performed in RAM, therefore the persistent memory contents should first be loaded into RAM space by pmem_load() and, if modified, saved with pmem_save().

Examples
Example #1 pmem_save() example
void set_pvar(uint8 idx, int16 val) {
    pmem_write(idx, val);
    pmem_save();
}

pmem_read
pmem_read — Read persistent memory

Description
Variation 1
void pmem_read(uint8 offset, <anytype> *variable);
Update the variable pointed by *variable with the value of type and size defined by <anytype>, located in the position offset from the persistent memory RAM array.

Variation 2
uint8 pmem_read(uint8 offset);
Read the uint8 value from position offset of the persistent memory RAM array.

Parameters
offset: Position of the persistent memory array, starting from 0, from which a value should be read. Note: on Variation 2 the offset is passed as direct parameter of the opcode, therefore stack values can't be used.
<anytype> *variable: Pointer to an variable of type <anytype>.
<anytype> can be: int8, uint8, int16, uint16, int32, uint32, fix32 or any of its aliases.
Byte Size of Variable Types
int8, uint8	1 byte
int16, uint16	2 bytes	big-endian
int32, uint32, fix32	4 bytes	big-endian
Return Value
Variation 1 does not returns any value.
Variation 2 returns an uint8 value from the position offset.
Persistent Memory
Persistent Memory Flow

The persistent memory is an array of 128 bytes that can be used to store data structures such that they can continue to be accessed even after the end of the GPC script that created or last modified them.

For performance reasons the operations pmem_read() and pmem_write() are performed in RAM, therefore the persistent memory contents should first be loaded into RAM space by pmem_load() and, if modified, saved with pmem_save().

Examples
Example #1 pmem_read() example
bool checkbox_value;
uint8 radiobox_value;
uint8 combobox_value;
int spinbox_value;
fix32 spinboxf_value;
int slider_value;
int dial_value;
 
init {
    pmem_load(); // Load permanent memory before use any pmem operation
 
    // Store the values from the permanent memory into variables.
    checkbox_value = (pmem_read(0) >> 7) & 0b1;
    radiobox_value = pmem_read(1);
    combobox_value = pmem_read(2);
    pmem_read(3, &spinbox_value);
    pmem_read(5, &spinboxf_value);
    pmem_read(9, &slider_value);
    pmem_read(11, &dial_value);
}

pmem_write
pmem_write — Write persistent memory

Description
void pmem_write(uint8 offset, <anytype> value);
Write on position offset, of the persistent memory RAM array, the value of type and size defined by <anytype>.

Parameters
offset: Position of the persistent memory array, starting from 0, to which the value should be write.
<anytype> value: A value of type <anytype>.
<anytype> can be: int8, uint8, int16, uint16, int32, uint32, fix32 or any of its aliases.
Byte Size of Variable Types
int8, uint8	1 byte
int16, uint16	2 bytes	big-endian
int32, uint32, fix32	4 bytes	big-endian
Return Value
No value is returned.

Persistent Memory
Persistent Memory Flow

The persistent memory is an array of 128 bytes that can be used to store data structures such that they can continue to be accessed even after the end of the GPC script that created or last modified them.

For performance reasons the operations pmem_read() and pmem_write() are performed in RAM, therefore the persistent memory contents should first be loaded into RAM space by pmem_load() and, if modified, saved with pmem_save().

Examples
Example #1 pmem_write() example
void set_pvar(uint8 idx, int16 val) {
    pmem_write(idx, val);
    pmem_save();
}