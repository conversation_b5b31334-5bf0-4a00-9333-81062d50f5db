const uint8 Input_Data[][] = {
// input, frames of delay
  { PS5_DOWN     , 55  },
  { PS5_RIGHT    , 52  }
}

define Frame_Time = 20;

int toggle;
int frame_timer;
int action_set_idx;

init {

  //vm_tctrl(2 - 10);

}

main {

  if(event_press(XB1_PL1)) {
    frame_timer = 0;
    action_set_idx = 0;
    toggle = !toggle;
  }

  if(toggle) {
    set_val(Input_Data[action_set_idx][0], 100);
    frame_timer += get_rtime();
    if(frame_timer / Frame_Time >= Input_Data[action_set_idx][1]) {
      action_set_idx ++;
      frame_timer = 0;
    }
    if(action_set_idx > (sizeof(Input_Data) / 2) - 1) {
      frame_timer = 0;
      action_set_idx = 0;
      toggle = FALSE;
    }
  }

  set_val(TRACE_1, toggle);
  set_val(TRACE_2, action_set_idx);

}