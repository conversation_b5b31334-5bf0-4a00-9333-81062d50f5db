// Define constants and variables
int DBL_BTN = PS4_LEFT;
int b_tap, btn_pressed;
int Double_Tap_Combo, Triple_Tap_Combo, Quatro_Tap_Combo; // Boolean flags for combos
int btnVALUE;
int wait_timer = 400;
int timer_wait = 1000;
int timer;
int VIBRATE_RUMBLE;
int time2delete;

main {
    // Update trace values for debugging
    set_val(TRACE_1, btnVALUE);
    set_val(TRACE_2, timer);
    set_val(TRACE_3, timer_wait);
    set_val(TRACE_4, b_tap);
    set_val(TRACE_5, time2delete);
    set_val(TRACE_6, btn_pressed);

    // Handle timer countdowns
    if (timer > 0 && !btn_pressed) {
        timer -= get_rtime();
        if (timer <= 0) {
            b_tap = 0;
        }
    }
    if (timer > 0 && btn_pressed > 0) {
        timer -= get_rtime();
        if (timer <= 0) {
            b_tap = 0;
            btn_pressed = 0;
            time2delete = 1;
        }
    }

    // Detect button press and handle value increment
    if (event_press(DBL_BTN)) {
        btnVALUE++;
    }

    // Reset btnVALUE if it exceeds 4 or button is released after reaching 4
    if (btnVALUE > 4 || (event_release(DBL_BTN) && btnVALUE == 4)) {
        btnVALUE = 0;
        btn_pressed = 0;
    }

    // Handle button press with long press
    if (btnVALUE > 0 && get_ival(DBL_BTN) && get_ptime(DBL_BTN) > 300) {
        time2delete = 1;
    }

    if (time2delete && event_release(DBL_BTN)) {
        btnVALUE = 0;
        btn_pressed = 0;
        time2delete = 0;
    }

    // Handle button tap sequences
    if (event_press(DBL_BTN) && !b_tap && !btn_pressed) {
        // First press
        timer = wait_timer;
        b_tap = 1;
    } else if (event_press(DBL_BTN) && b_tap == 1 && !btn_pressed) {
        // Double tap
        combo_run(vib2x);
        Double_Tap_Combo = TRUE;
        btn_pressed = 1;
        b_tap = 0;
    } else if (event_press(DBL_BTN) && !b_tap && btn_pressed == 1) {
        // Triple tap preparation
        timer = timer_wait;
        b_tap = 2;
    } else if (event_press(DBL_BTN) && b_tap == 2 && btn_pressed == 1) {
        // Triple tap
        combo_run(vib3x);
        Triple_Tap_Combo = TRUE;
        btn_pressed = 2;
        b_tap = 0;
    } else if (event_press(DBL_BTN) && !b_tap && btn_pressed == 2) {
        // Quadruple tap preparation
        timer = timer_wait;
        b_tap = 3;
    } else if (event_press(DBL_BTN) && b_tap == 3 && btn_pressed == 2) {
        // Quadruple tap
        combo_run(vib4x);
        Quatro_Tap_Combo = TRUE;
        btn_pressed = 3;
        b_tap = 0;
    }

    // Handle the combos
    if (Double_Tap_Combo) {
        combo_run(Double_Tap_Combo);
        set_val(DBL_BTN, 0);
    } else if (!Double_Tap_Combo && combo_running(Double_Tap_Combo)) {
        combo_stop(Double_Tap_Combo);
    }

    if (Triple_Tap_Combo) {
        combo_run(Triple_Tap_Combo);
        set_val(DBL_BTN, 0);
    } else if (!Triple_Tap_Combo && combo_running(Triple_Tap_Combo)) {
        combo_stop(Triple_Tap_Combo);
    }

    if (Quatro_Tap_Combo) {
        combo_run(Quatro_Tap_Combo);
        set_val(DBL_BTN, 0);
    } else if (!Quatro_Tap_Combo && combo_running(Quatro_Tap_Combo)) {
        combo_stop(Quatro_Tap_Combo);
    }

    // Handle combo release
    if (event_release(DBL_BTN)) {
        if (Double_Tap_Combo) {
            Double_Tap_Combo = FALSE;
            if (btn_pressed != 1) btn_pressed = FALSE;
        }
        if (Triple_Tap_Combo) {
            Triple_Tap_Combo = FALSE;
            if (btn_pressed != 2) btn_pressed = FALSE;
        }
        if (Quatro_Tap_Combo) {
            Quatro_Tap_Combo = FALSE;
            if (btn_pressed != 3) btn_pressed = FALSE;
        }
    }
}

// Define vibration combos
combo vib2x {
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
    wait(200);
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
}

combo vib3x {
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
    wait(200);
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
    wait(200);
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
}

combo vib4x {
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
    wait(200);
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
    wait(200);
    set_rumble(VIBRATE_RUMBLE, 25);
    wait(200);
    reset_rumble();
}

combo Double_Tap_Combo {
    set_val(PS4_CROSS, 100);
    wait(100);
    set_val(PS4_CROSS, 0);
    wait(10);
}

combo Triple_Tap_Combo {
    set_val(PS4_L3, 100);
    wait(100);
    set_val(PS4_L3, 0);
    wait(100);
}

combo Quatro_Tap_Combo {
    set_val(PS4_SQUARE, 100);
    wait(100);
    set_val(PS4_SQUARE, 0);
    wait(100);
}
 