
  // ============  HOW it WORKs ============// : 
//1 - the script triggered by pressing Shot button and direction where you aim ,  
//    while nothing else pressed , like when no sprnt or L2 or L1 ..etc .
//2-  it will generate fixed power for both setiuations , for inside box you just need to press short period on shot button
//    that suitable for inside 18 yards box finishing ,, for outside of box hold shot button long.
//3 - it will lock the corner of where you aim to grant a perfect aim as possible .
  
  //==================================================// 
   // MAKE SURE NO Shooting SCRIPTS Generated From FIFA Generator // 
   
      //============ COPY THis at the start of the script ============//
   
int Shot_wait ; 
int ShotBoxWait ;
   
  
   
   
   //============ COPY THis just before the END MAIN SECTION ============//

 if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn)){
 
		if( get_val(ShotBtn ) ){
			
			ShotBoxWait += get_rtime();
			if (ShotBoxWait > 200)  { Shot_wait =  196; combo_restart(Inside_Box_Finishing)}
		
		
			if (ShotBoxWait < 200 )  { Shot_wait =  176; combo_restart(Inside_Box_Finishing) }		
	}
	
	}
	
  

  // ============ END OF COPY TO MAIN SECTION ============ //
  
  
  
  
   // ============ COPY THis TO COMBOS SECTION ============ //
    
   
combo Inside_Box_Finishing {
    set_val(ShotBtn, 0);
    wait(20);
    CORNER_FIX_MOVE() 
    RA_L_R() ;
    wait(60);
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(Shot_wait);
    LA ((LX),inv(LY)); 
    set_val(ShotBtn, 100);
    wait(17);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    LA ((LX),0);
    wait(15);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(450);   
    ShotBoxWait = 0;
} 

 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }

function CORNER_FIX_MOVE() {
     
   
   
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
		set_val(PS4_LX,  100);
		set_val(PS4_LY,  -16);
		 
	}
	      
	      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
		set_val(PS4_LX, 100); 
		set_val(PS4_LY,  16);
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 
		set_val(PS4_LX, -100); 
		set_val(PS4_LY,  -16);
	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE
	set_val(PS4_LX, -100);
	set_val(PS4_LY,  16);
		  
		 
	}
	}
  
  
  
  