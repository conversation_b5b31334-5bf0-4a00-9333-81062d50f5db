main {

if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (1);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (3);
      }
      set_val(XB1_RIGHT,0);
	}

    LED_Color(Green);
    if (get_val(XB1_PL1)) {
      PN_Angle = 230;
      combo_run(p23);
    } //230 
    if (get_val(XB1_PR1)) {
      PN_Angle = 310;
      combo_run(p23);
    } //310
    if (get_val(XB1_PL2)) {
      PN_Angle = 150;
      combo_run(p23);
    } //210
    if (get_val(XB1_PR2)) {
      PN_Angle = 30;
      combo_run(p23);
    } //330
  }



int Penalties_ON_Off;
int PN_Angle;

combo p23 {
  set_polar(P<PERSON><PERSON>_LS, PN_Angle, 32767);
  set_val(XB1_B, 100);
  wait(420);
  set_polar(P<PERSON><PERSON>_LS, PN_Angle, 32767);
  wait(1500); //Aim_Lock
}

define ColorOFF = 0;
define Blue = 1;
define Red = 2;
define Green = 3;
define Pink = 4;
define SkyBlue = 5;
define Yellow = 6;
define White = 7;

data(
  0, 0, 0, //0. ColorOFF
  2, 0, 0, //1. Blue     
  0, 2, 0, //2. Red      
  0, 0, 2, //3. Green    
  2, 2, 0, //4. Pink     
  2, 0, 2, //5. SkyBlue 
  0, 2, 2, //6. Yellow   
  2, 2, 2 //7. White    
); // end of data segment-------------- 
// COLOR LED function        
//-------------------------------------------------------------- 

int data_indx;

function LED_Color(color) {
  for (data_indx = 0; data_indx < 3; data_indx++) {
    set_led(data_indx, duint8((color * 3) + data_indx));
  }
}
 