#include <titanone.gph>
#pragma METAINFO("Ellipse Movement V2", 1, 0, "Assistant")

// Constants
#define MAX_RADIUS 32767  // Maximum stick value
#define PI 3.14159
#define TWO_PI 6.28318

// Global variables
int angle = 0;
int radius = 0;
int x_out, y_out;
int ellipse_active = 0;

// Configuration
#define BASE_SPEED 5      // Base rotation speed
#define X_SCALE 100       // X axis scale (percentage)
#define Y_SCALE 40        // Y axis scale (percentage)

// Fast sine approximation for GPC2
// Input: angle in radians * 1000
// Output: sine * 1000
function fast_sin(angle) {
    // Normalize angle to 0-2π
    angle = angle % (TWO_PI * 1000);
    
    // Quarter wave polynomial approximation
    int x = angle % (PI * 1000);
    if (x > (PI * 500)) {
        x = PI * 1000 - x;
    }
    
    // Polynomial coefficients optimized for integer math
    int x2 = (x * x) / 1000;
    int x3 = (x2 * x) / 1000;
    int result = x - (x3 / 6000);
    
    // Adjust sign based on quadrant
    if (angle > PI * 1000) {
        result = -result;
    }
    
    return result;
}

// Fast cosine using sine
function fast_cos(angle) {
    return fast_sin(angle + (PI * 500));  // PI/2 phase shift
}

// Convert polar to cartesian coordinates
function polar_to_xy(angle_deg, radius, x_scale, y_scale) {
    // Convert angle to radians * 1000
    int angle_rad = (angle_deg * PI * 1000) / 180;
    
    // Calculate scaled x and y
    x_out = (radius * fast_cos(angle_rad) / 1000) * x_scale / 100;
    y_out = (radius * fast_sin(angle_rad) / 1000) * y_scale / 100;
}

init {
    // Initialize virtual machine
    vm_tctrl(-2);
}

main {
    // Get stick input
    int input_x = get_val(STICK_1_X);
    int input_y = get_val(STICK_1_Y);
    
    // Calculate current radius from input
    radius = isqrt((input_x * input_x) + (input_y * input_y));
    
    // Only process if stick is moved significantly
    if (radius > 3000) {
        // Calculate angle from input (in degrees)
        angle = (iatan2(input_y, input_x) * 180) / PI;
        if (angle < 0) angle += 360;
        
        // Check if L3 is pressed for ellipse mode
        if (get_val(BUTTON_9)) {
            ellipse_active = 1;
            
            // Increment angle for rotation
            angle = (angle + BASE_SPEED) % 360;
            
            // Convert to cartesian with elliptical scaling
            polar_to_xy(angle, radius, X_SCALE, Y_SCALE);
            
            // Apply calculated values
            set_val(STICK_1_X, clamp(x_out, -MAX_RADIUS, MAX_RADIUS));
            set_val(STICK_1_Y, clamp(y_out, -MAX_RADIUS, MAX_RADIUS));
        } else {
            ellipse_active = 0;
            // Pass through normal stick values when L3 is not pressed
            set_val(STICK_1_X, input_x);
            set_val(STICK_1_Y, input_y);
        }
    } else if (ellipse_active && get_val(BUTTON_9)) {
        // Continue rotation even when stick returns to center
        angle = (angle + BASE_SPEED) % 360;
        polar_to_xy(angle, MAX_RADIUS/2, X_SCALE, Y_SCALE);
        set_val(STICK_1_X, clamp(x_out, -MAX_RADIUS, MAX_RADIUS));
        set_val(STICK_1_Y, clamp(y_out, -MAX_RADIUS, MAX_RADIUS));
    } else {
        ellipse_active = 0;
        // Center stick when no input
        set_val(STICK_1_X, 0);
        set_val(STICK_1_Y, 0);
    }
}