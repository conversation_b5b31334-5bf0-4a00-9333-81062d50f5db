/////////////////////////
// ALPHA - TESTERS - T4// 
/////////////////////////

// Hi guys Today we will test new way for controlling your players movements .
// Follow steps and the out come should be as following : 
    // - enhanced responsive player movement .
    // - faster passes and throughballs than usual .
    // - more responsive jockey movements .
    

         
//////////////////
// 1st  step - .// 
//////////////////
   
               //-if you use Elite series controllers please turn off any (Left stick) sensitivity profiles only use the default .
               
               //-Your script must be WITHOUT LS Dribbling Sens / Also Sprint Sens OFF .
               
               //-Your script must be WITHOUT Defence Mod .
               
               //-Your script must be WITHOUT VM Control .
               
 
//////////////////
// 2nd  step - .// 
//////////////////

              // copy all the following and paste it first thing in your main loop . ( after main { ) 
                if ( !onoff_penalty && !onoff_FK &&  ( get_val(SprintBtn) || get_val(PassBtn) || get_val(ThroughBall)) ){ //caution movement in Y access (help defending)
             sensitivity(PS4_LX, 35, 107);
             sensitivity(PS4_LY, 35, 91);
        }
       
    
       if ( !onoff_penalty && !onoff_FK && !get_val(PaceCtrol) && !get_val(FinesseShot) &&  !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)  ){
            sensitivity(PS4_LX, 35, 91);
            sensitivity(PS4_LY, 35, 107); // faster dribbling in Y access
        }
         
              
// That's it guys ,, now test the script , and let me know how was your experiment. Thanks for your time . <3
