# Python equivalent of the AutoHotkey script with Tkinter overlay
# Requires the 'pynput' library: pip install pynput

from pynput import keyboard
import threading
import time
import sys
import tkinter as tk
import tkinter.font as tkFont

# --- Configuration ---
SPAM_KEY_TO_SEND = 'q'  # The key to spam (single character)

# Spam intervals: Map hotkey characters to their intervals in seconds
SPAM_INTERVALS = {
    'a': 1.0,   # 1 second interval
    's': 0.9,   # 0.9 second interval
    'd': 0.7,   # 0.7 second interval
    'f': 0.1    # 0.1 second interval
}
STOP_SPAM_KEY_CHAR = 'y'    # Character to stop any spamming
SEND_F5_KEY_CHAR = 'g'      # Character to trigger sending F5
PAUSE_RESUME_KEY_CHAR = '#' # Character to pause/resume the script
EXIT_SCRIPT_KEY = keyboard.Key.esc  # pynput Key object for Escape key

OVERLAY_DURATION_MS = 2000 # How long the overlay stays visible

# --- Global State ---
active_timer_key_char = ""    # Keeps track of which timer key char is currently running
active_timer_object = None    # Stores the active threading.Timer object
spam_active = False           # Flag to control the spamming loop
script_paused = False         # Flag to indicate if the script's hotkeys are paused

# --- Keyboard Controller (for sending keys) ---
kb_controller = keyboard.Controller()

# --- Tkinter Overlay Globals ---
tk_root = None
overlay_window = None
overlay_timer_id = None # To cancel existing timer for overlay dismissal

# --- Functions ---

def show_status_overlay(message):
    """Displays a message in a temporary, centered, on-top overlay window."""
    global tk_root, overlay_window, overlay_timer_id

    if not tk_root: # Tkinter not initialized
        print(f"[INFO] {message}") # Fallback to console
        return

    # If an old overlay dismissal timer is pending, cancel it
    if overlay_timer_id:
        tk_root.after_cancel(overlay_timer_id)
        overlay_timer_id = None

    # If an old overlay window exists, destroy it
    if overlay_window:
        try:
            overlay_window.destroy()
        except tk.TclError: # Window might already be destroyed
            pass
        overlay_window = None

    overlay_window = tk.Toplevel(tk_root)
    overlay_window.overrideredirect(True)  # No window decorations (border, title bar)
    overlay_window.wm_attributes("-topmost", True)  # Always on top
    overlay_window.wm_attributes("-alpha", 0.85) # Slight transparency

    # Style the label
    label_font = tkFont.Font(family="Arial", size=14, weight="bold")
    label = tk.Label(overlay_window, text=message, font=label_font, 
                      bg="black", fg="white", padx=20, pady=10)
    label.pack()

    # Calculate position to center the overlay
    overlay_window.update_idletasks() # Ensure window size is calculated
    screen_width = tk_root.winfo_screenwidth()
    screen_height = tk_root.winfo_screenheight()
    window_width = overlay_window.winfo_width()
    window_height = overlay_window.winfo_height()
    
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2) - 50 # Slightly above true center
    
    overlay_window.geometry(f"+{x}+{y}")

    # Schedule the overlay to disappear
    overlay_timer_id = tk_root.after(OVERLAY_DURATION_MS, destroy_current_overlay)

def destroy_current_overlay():
    """Destroys the current overlay window if it exists."""
    global overlay_window, overlay_timer_id
    if overlay_window:
        try:
            overlay_window.destroy()
        except tk.TclError:
            pass # Window might already be gone
        overlay_window = None
    overlay_timer_id = None


def display_status_message(message):
    """Schedules the overlay to be shown from any thread."""
    if tk_root:
        # tk.Misc.after_idle is a way to ensure GUI updates happen on the main Tkinter thread
        tk_root.after_idle(lambda: show_status_overlay(message))
    else:
        print(f"[INFO] {message}") # Fallback if Tkinter isn't ready


def spam_function_for_timer():
    global spam_active, active_timer_object, active_timer_key_char, script_paused

    if not spam_active or not active_timer_key_char or script_paused:
        active_timer_object = None
        return

    try:
        kb_controller.press(SPAM_KEY_TO_SEND)
        kb_controller.release(SPAM_KEY_TO_SEND)
    except Exception as e:
        print(f"[ERROR] Could not send key '{SPAM_KEY_TO_SEND}': {e}")

    current_interval_seconds = SPAM_INTERVALS.get(active_timer_key_char)
    if spam_active and current_interval_seconds is not None and not script_paused:
        active_timer_object = threading.Timer(current_interval_seconds, spam_function_for_timer)
        active_timer_object.daemon = True
        active_timer_object.start()
    else:
        active_timer_object = None

def stop_spam_timer():
    global active_timer_object, active_timer_key_char, spam_active
    
    if not spam_active and active_timer_object is None:
        return

    spam_active = False
    if active_timer_object:
        active_timer_object.cancel()
        active_timer_object = None
    
    display_status_message("Spam stopped")
    active_timer_key_char = ""

def start_spam_timer(key_char, interval_seconds):
    global active_timer_key_char, active_timer_object, spam_active, script_paused

    if script_paused:
        display_status_message("Cannot start spam, script is paused.")
        return

    if spam_active and active_timer_key_char != "" and active_timer_key_char != key_char:
        stop_spam_timer() # This will also call display_status_message

    spam_active = True
    active_timer_key_char = key_char

    if active_timer_object:
        active_timer_object.cancel()
        active_timer_object = None

    active_timer_object = threading.Timer(interval_seconds, spam_function_for_timer)
    active_timer_object.daemon = True
    active_timer_object.start()
    
    display_spam_key = SPAM_KEY_TO_SEND
    if isinstance(SPAM_KEY_TO_SEND, keyboard.Key):
        display_spam_key = SPAM_KEY_TO_SEND.name
        
    display_status_message(f"'{display_spam_key}' spam started (via '{key_char}', {interval_seconds*1000:.0f}ms)")

def handle_spam_toggle(key_char_pressed):
    global active_timer_key_char, spam_active, script_paused

    if script_paused:
        return

    if key_char_pressed not in SPAM_INTERVALS:
        print(f"[ERROR] Internal Script Error: Key '{key_char_pressed}' not found in SPAM_INTERVALS map!")
        return

    interval_seconds = SPAM_INTERVALS[key_char_pressed]

    if spam_active and active_timer_key_char == key_char_pressed:
        stop_spam_timer()
    else:
        start_spam_timer(key_char_pressed, interval_seconds)

def do_send_f5():
    global script_paused
    if script_paused:
        return
    display_status_message("Sending F5")
    try:
        kb_controller.press(keyboard.Key.f5)
        kb_controller.release(keyboard.Key.f5)
    except Exception as e:
        print(f"[ERROR] Could not send F5: {e}")

keep_listening = True

def do_exit_script():
    global keep_listening, tk_root
    display_status_message("Exiting script...")
    stop_spam_timer()
    keep_listening = False
    if tk_root:
        tk_root.quit() # Properly close Tkinter
    return False # Stop pynput listener

# --- Hotkey Listener Callback ---
def on_key_press(key):
    global script_paused, spam_active, keep_listening

    if not keep_listening:
        return False

    pressed_key_value = None
    try:
        pressed_key_value = key.char
    except AttributeError:
        pressed_key_value = key

    if pressed_key_value == EXIT_SCRIPT_KEY:
        return do_exit_script()

    if isinstance(pressed_key_value, str) and pressed_key_value == PAUSE_RESUME_KEY_CHAR:
        script_paused = not script_paused
        if script_paused:
            display_status_message(f"Script paused. Press '{PAUSE_RESUME_KEY_CHAR}' to resume.")
            if spam_active:
                stop_spam_timer() 
        else:
            display_status_message(f"Script resumed. Press '{PAUSE_RESUME_KEY_CHAR}' to pause.")
        return keep_listening

    if script_paused:
        return keep_listening

    if isinstance(pressed_key_value, str):
        if pressed_key_value in SPAM_INTERVALS:
            handle_spam_toggle(pressed_key_value)
        elif pressed_key_value == STOP_SPAM_KEY_CHAR:
            stop_spam_timer()
        elif pressed_key_value == SEND_F5_KEY_CHAR:
            do_send_f5()
            
    return keep_listening

def start_keyboard_listener_thread():
    """Runs the pynput listener in its own thread."""
    with keyboard.Listener(on_press=on_key_press) as listener:
        listener.join() # This blocks until the listener stops (e.g., on_press returns False)
    
    # Listener has stopped, if tk_root still exists, schedule its destruction
    # This helps ensure the program exits if Esc is pressed rapidly before Tkinter fully quits
    if tk_root and tk_root.winfo_exists():
        try:
            tk_root.after_idle(tk_root.destroy)
        except tk.TclError:
            pass # Main window might already be destroyed


# --- Main Execution ---
if __name__ == "__main__":
    print("Python Hotkey Script with Overlay Started.")
    print(f" - Spam '{SPAM_KEY_TO_SEND}' using keys: {', '.join(SPAM_INTERVALS.keys())}")
    print(f" - Press '{STOP_SPAM_KEY_CHAR}' to stop spam.")
    print(f" - Press '{SEND_F5_KEY_CHAR}' to send F5.")
    print(f" - Press '{PAUSE_RESUME_KEY_CHAR}' to pause/resume script.")
    print(f" - Press 'Esc' (Escape key) to exit.")
    print("Listening for hotkeys (GUI overlay will show status)...")

    # Initialize Tkinter root (but don't show it)
    tk_root = tk.Tk()
    tk_root.withdraw() # Hide the main Tkinter window

    # Start the pynput listener in a separate daemon thread
    # Daemon threads exit when the main program exits
    listener_thread = threading.Thread(target=start_keyboard_listener_thread, daemon=True)
    listener_thread.start()

    try:
        # Start the Tkinter event loop (this blocks the main thread)
        tk_root.mainloop()
    except KeyboardInterrupt:
        print("\nScript interrupted by user (Ctrl+C). Exiting.")
        # Ensure cleanup if interrupted
        if keep_listening: # if do_exit_script wasn't called
            stop_spam_timer()
            keep_listening = False
        if tk_root and tk_root.winfo_exists():
            tk_root.destroy()
    finally:
        # Ensure the listener thread is given a chance to join if it's still alive,
        # though as a daemon it should exit with the main thread.
        if listener_thread.is_alive():
            print("Waiting for listener thread to finish...")
            # Note: Forcing a join on a daemon thread that's blocked by pynput listener.join()
            # might not be immediate if the listener itself isn't stopping.
            # The `return False` in `do_exit_script` is key to stopping the listener.
            pass # listener_thread.join(timeout=1) # Optional: wait briefly

        print("Python Hotkey Script Exited.")
