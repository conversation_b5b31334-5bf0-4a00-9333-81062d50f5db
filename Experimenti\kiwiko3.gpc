// Constants
define Aim = PS4_L2;       // Left trigger for aiming
define Shoot = PS4_R2;     // Right trigger for shooting
define Radial_DZ = 15;     // Radial deadzone value

// Global Variables for Tracking and Adjustment
int x_input, y_input;       // Inputs from axes
int x_adjustment, y_adjustment; // Adjustments based on inputs
int Val;                    // Calculated value for adjustment
int mod_edit[20];           // Array to store modification values
int tracking_strength;     // Tracking strength parameter

// Smart Tracking Function
function SmartTracker(f_val, f_axis_X, f_axis_Y) {
    // Retrieve integer values from the specified axes
    x_input = get_ival(f_axis_X);
    y_input = get_ival(f_axis_Y);
    
    // If both Aim and Shoot triggers are pressed, get the actual axis values
    if (get_ival(Aim) && get_ival(Shoot)) {
        x_input = get_val(f_axis_X);
        y_input = get_val(f_axis_Y);
    }
    
    // Check if inputs exceed the radial deadzone
    if (abs(x_input) > mod_edit[Radial_DZ] || abs(y_input) > mod_edit[Radial_DZ]) {
        x_adjustment = 100 - abs(x_input);
        y_adjustment = 100 - abs(y_input);
    } else {
        // If within deadzone, no adjustment
        x_adjustment = 0;
        y_adjustment = 0;
    }
    
    // Calculate the adjustment value
    Val = f_val * (x_adjustment * y_adjustment) / 32767;
    
    // Optional: Trace inside the function for detailed debugging
    set_val(TRACE_4, x_input);
    set_val(TRACE_5, y_input);
    set_val(TRACE_6, Val);
    
    // Return the clamped adjusted Y input
    return clamp(y_input + Val, -32767, 32767);
}

// Initialization Block
init {
    // Initialize modification values
    mod_edit[Radial_DZ] = 15;  // Set default deadzone
    tracking_strength = 100;    // Set default tracking strength
}

// Main Execution Block
main {
    // Apply smart tracking to the left stick Y axis
    // Note: No local variable declarations; using global 'tracking_strength'
    set_val(PS4_LY, SmartTracker(tracking_strength, PS4_LX, PS4_LY));
    
    // Debug output to traces using global variables
    set_val(TRACE_1, x_adjustment); // Trace X Adjustment
    set_val(TRACE_2, y_adjustment); // Trace Y Adjustment
    set_val(TRACE_3, Val);           // Trace Val
    
    // Additional Tracing (Optional)
    // Example: Trace the tracking strength and adjusted Y value
    set_val(TRACE_4, tracking_strength);
    set_val(TRACE_5, y_input);
    set_val(TRACE_6, get_val(PS4_LY)); // Trace the final adjusted Y value
}