define STICK_IDLE_TIMEOUT = 200;
define CYCLE_TIME_MS = 10;
define IDLE_CYCLES_THRESHOLD = STICK_IDLE_TIMEOUT / CYCLE_TIME_MS;
define MOVE_SPEED = 10; // Degrees per cycle

int last_lx = 0;
int last_ly = 0;
int idle_counter = 0;
int current_lx;
int current_ly;
int target_radius;
int current_angle;
int target_angle;
int is_moving = 0;
int clockwise_dist;
int counter_dist;

main {
    current_lx = get_val(XB1_LX);
    current_ly = get_val(XB1_LY);
    
    if (current_lx != last_lx || current_ly != last_ly) {
        idle_counter = 0;
        last_lx = current_lx;
        last_ly = current_ly;
        
        // Update target angle when stick moves
        if (abs(current_lx) > 2000 || abs(current_ly) > 2000) {
            target_angle = get_polar(POLAR_LS, POLAR_ANGLE);
            is_moving = 1;
        }
    } else {
        if (idle_counter <= IDLE_CYCLES_THRESHOLD) {
             idle_counter = idle_counter + 1;
        }
    }
    
    if (get_val(XB1_LS)) {
        if (abs(current_lx) < 2000 && abs(current_ly) < 2000) {
            target_radius = 0;
            is_moving = 0;
        } else {
            target_radius = 9800;
            
            if (is_moving) {
                // Get current angle
                current_angle = get_polar(POLAR_LS, POLAR_ANGLE);
                
                // Calculate clockwise and counter-clockwise distances
                clockwise_dist = (target_angle - current_angle + 360) % 360;
                counter_dist = (current_angle - target_angle + 360) % 360;
                
                // If we're close enough to target, set exact angle
                if (clockwise_dist <= MOVE_SPEED || counter_dist <= MOVE_SPEED) {
                    set_polar(POLAR_LS, target_angle, target_radius);
                    is_moving = 0;
                } else {
                    // Move in the shorter direction
                    if (clockwise_dist <= counter_dist) {
                        current_angle = (current_angle + MOVE_SPEED) % 360;
                    } else {
                        current_angle = (current_angle - MOVE_SPEED + 360) % 360;
                    }
                    set_polar(POLAR_LS, current_angle, target_radius);
                }
            } else {
                set_polar2(POLAR_LS, get_polar(POLAR_LS, POLAR_ANGLE), target_radius);
            }
        }
    }
}
