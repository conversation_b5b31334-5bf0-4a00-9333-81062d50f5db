
// ============  HOW it WORKs ============// : 
//1 - the script triggered by pressing Pass button or Triangle "Through Ball button" , 
//     while nothing else pressed Sprint bottun or jockey button or Player run button .. etc . 
//  
//2-  short tap will generate fixed power that suitable for short passes and through balls if you need more you can hold as much as you want. 
// 
//   
//3 - logic behind this script is to let the game think that you attempt to make Heel to Heel mainly , 
//    and short step over sometiomes skill and then pass .
//    it's already proven that any kinda of skill before any action will let the upcoming action more accurate and successful.
//    that's why I created this . 
//    you will see the passes actually happening during the skill itself , trust me it will drive your opponents crazy ! 
  
  //==================================================// 
 // ============ COPY BEFORE MAIN SECTION ============ //
 int Pass_Or_Through ;
  //=====================================================//
  

  // ============ COPY BEFORE THE END OF MAIN SECTION ============ //
  
      if (event_press(PassBtn)){
    Pass_Or_Through = PassBtn ;
    } 
      if (event_press(PS4_TRIANGLE)){
    Pass_Or_Through = PS4_TRIANGLE ;
    } 
    
     if ( ( event_press(PassBtn) || event_press(PS4_TRIANGLE) )  
     && !get_val(SprintBtn) && !get_val(PlayerRun) 
     && ( abs(get_val(MOVE_X))> 40 || abs(get_val(MOVE_Y))> 40)){
     set_val(PassBtn,0);
     set_val(PS4_TRIANGLE,0);
     combo_run(lol_pass);
     }
  
   // ============ COPY THis TO COMBOS SECTION ============ //
   
   combo Heel_Pass {    
    set_val(PassBtn,0);
    set_val(PS4_TRIANGLE,0);
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO (); 
	set_val(PassBtn,0);
    set_val(PS4_TRIANGLE,0);// ZERO                   
	wait(w_rstick);                          
	RA_DOWN (); 
	set_val(PassBtn,0);
    set_val(PS4_TRIANGLE,0);// down                  
	wait(w_rstick);                         
}                   
    
combo lol_pass {
set_val(PS4_L3,100);
call (Heel_Pass);
set_val(PS4_L3,100);
set_val(Pass_Or_Through,100);
wait(91);
set_val(PS4_L3,100);
wait(120);
}
  
  
  