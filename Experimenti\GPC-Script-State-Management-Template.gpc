/**
version: <PERSON>ript Template with State Management
Author: Jo<PERSON>1337

Discord : https://discord.gg/7ZGANnFEUS ( Join Discord to get help)
************************************************************ */

// Define constants for timers
define loadingTimer = 3000; // Time in milliseconds before the loading state ends
define SCREEN_SAVER_TIMER = 10000; // Time in milliseconds before the screen saver activates
define STATE_TRANSITION_TIMER = 500; // Time in milliseconds for state transition

// Define variables to store timer values
int loadingTime = 0; // Timer for the loading state
int screenSaverTimer = 0; // Timer for the screen saver state
int stateTransitionTime = 0; // Timer for state transitions

// Define an enum for the different states of the script
enum {
    STATE_OFF, // 0: The script is turned off
    STATE_RESTART, // 1: The script is restarting
    STATE_LOADING, // 2: The script is loading
    STATE_PLAYING, // 3: The script is in the playing state (normal gameplay)
    STATE_SETUP_MENU, // 4: The script is in the setup menu state
    STATE_SETUP_SUBMENU, // 5: The script is in a submenu of the setup menu
    STATE_SCREEN_SAVER, // 6: The script is in the screen saver state
    STATE_SAVING, // 7: The script is saving data
    STATE_CLEAN_SCREEN, // 8: The script is cleaning the screen (preparing for a state transition)
    STATE_QUICK_TOGGLE // 9: The script is in a quick toggle state
}

// Define an array of strings to represent the names of the states (for debugging/display purposes)
const string TEXT[]  = {"STATE_OFF","STATE_RESTART",
"STATE_LOADING", "STATE_PLAYING", "STATE_SETUP_MENU","STATE_SETUP_SUBMENU", "STATE_SCREEN_SAVER"
, "STATE_SAVING", "STATE_CLEAN_SCREEN", "STATE_QUICK_TOGGLE"};

// Define global state variables
int currentState = STATE_LOADING;  // The current state of the script (starts in the loading state)
int previousState; // The previous state of the script
int tempState; // Temporary state variable (used for clean state transitions)
int quickToggleModId; // ID of the quick toggle mod (if you have more than one)

// Initialize the script
init {
	cls_oled(OLED_BLACK); // Clear the OLED screen to black
}

// Main loop of the script
main {

	// State machine: execute different code depending on the current state
	switch (currentState) {
        case STATE_OFF: {
            handleOffState(); // Handle the OFF state
            break;
        }
        case STATE_RESTART: {
            handleRestartState(); // Handle the RESTART state
            break;
        }
        case STATE_LOADING: {
            handleLoadingState(); // Handle the LOADING state
            break;
        }
        case STATE_PLAYING: {
            handlePlayingState(); // Handle the PLAYING state
            break;
        }
        case STATE_SETUP_MENU: {
            handleUserSetupMenuSateInput(); // Handle input in the SETUP_MENU state
            break;
        }
        case STATE_SETUP_SUBMENU: {
            handleSubMenuState(); // Handle the SETUP_SUBMENU state
            break;
        }
        case STATE_SCREEN_SAVER: {
            handleScreenSaverState(); // Handle the SCREEN_SAVER state
            break;
        }
        case STATE_SAVING: {
            handleSavingState(); // Handle the SAVING state
            break;
        }
        case STATE_CLEAN_SCREEN: {
            handleCleanScreenState(); // Handle the CLEAN_SCREEN state
            break;
        }
        case STATE_QUICK_TOGGLE: {
            handleQuickToggleState(); // Handle the QUICK_TOGGLE state
            break;
        }
        
    }
    print(0,20, OLED_FONT_SMALL, OLED_BLACK,TEXT[currentState]); // Print the current state to the OLED screen
    set_val(TRACE_1, currentState); // Set the value of TRACE_1 to the current state (for debugging)

}

/****
* State Management Fonction
********************/

// Handles the OFF state
function handleOffState() {
	cls_oled(OLED_BLACK); // Clear the OLED screen
    combo_stop_all(); // Stop all active combos
	handleUserOffSateInput(); // Handle user input in the OFF state
}

// Handles the RESTART state
function handleRestartState() {
    currentState = STATE_PLAYING; // Transition to the PLAYING state
}

// Handles the LOADING state
function handleLoadingState() {
    if (loadingTime >= loadingTimer) { // If the loading time is greater than or equal to the loading timer
       cls_oled(OLED_BLACK); // Clear the OLED screen
       currentState = STATE_PLAYING; // Transition to the PLAYING state
    } else {
        loadingTime += get_rtime(); // Increment the loading time
		// Do a combo here to display your image.
    }
}

// Handles the CLEAN_SCREEN state
function handleCleanScreenState() {
	// ADAPT DEPEND YOUR NEEDS (Could be for THEMING)
	if (previousState == STATE_SAVING) {
		cls_oled(OLED_BLACK);
	} else if (previousState == STATE_PLAYING && tempState == STATE_SCREEN_SAVER ) {
		cls_oled(OLED_BLACK);
	} else {
		cls_oled(OLED_BLACK);
	}
	
	if (stateTransitionTime > STATE_TRANSITION_TIMER) {
    		currentState = tempState; // Transition to the temporary state
    		previousState = currentState; // Update the previous state
    		stateTransitionTime = 0; // Reset the state transition timer
	} else {
			stateTransitionTime += get_rtime(); // Increment the state transition timer
	}
}

// Handles the PLAYING state (normal gameplay)
function handlePlayingState() {
	previousState = currentState; // Update the previous state
	// Put your stuff here to display main screen
    setScreenSaver(); // Start the screen saver timer
    
    // function to handle player input
    handleUserPlayingSateInput(); // Handle user input in the PLAYING state
}
function handleScreenSaverState() {
	previousState = currentState; // Update the previous state
	handleUserPlayingSateInput(); // Handle user input in the SCREEN_SAVER state
}

// Handles the MAIN Setup MENU state
function handleUserSetupMenuSateInput() {
	previousState = currentState; // Update the previous state
    combo_stop_all(); // Stop all active combos
    block_all_inputs(); // Block all inputs
    
    // Put your stuff here to display Setup main screen

    if (event_press(XB1_A)) {
        currentState = STATE_SETUP_SUBMENU;  // Open submenu
    }

    if (event_press(XB1_B)) { // Just return to playing states
    	tempState = STATE_PLAYING; // Set the temporary state to PLAYING
		currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
    }
    
    if (event_press(XB1_Y)) { // SAVE
    	currentState = STATE_SAVING; // Transition to the SAVING state
	}
}

// Handles the SUBMENU state
function handleSubMenuState() {
	previousState = currentState; // Update the previous state
   
   	// Put your stuff here to display Setup main screen
   	
   	// XB1_B will trigger a new state
    if (event_press(XB1_B)) {
        currentState = STATE_SETUP_MENU;  // Return to main menu
    }
}


function handleSavingState() {
	previousState = currentState; // Update the previous state
	tempState = STATE_PLAYING; // Set the temporary state to PLAYING
	currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
	screenSaverTimer = 0; // Reset the screen saver timer
}

function handleQuickToggleState() {
	previousState = currentState; // Update the previous state
	// Put your stuff here that you want display QuickToggle screen
	
	if (event_press(XB1_B)) {
    	tempState = STATE_PLAYING; // Set the temporary state to PLAYING
		currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
    }
    
    if (event_press(XB1_Y)) { // SAVE
    	currentState = STATE_SAVING; // Transition to the SAVING state
	}
}


function handleUserOffSateInput() {
	if (get_val(XB1_LT) && event_release(XB1_VIEW)) {
	 	currentState = STATE_RESTART; // Transition to the RESTART state
	}
}

function setScreenSaver() {
    if (screenSaverTimer >= SCREEN_SAVER_TIMER) { // If the screen saver timer is greater than or equal to the screen saver timer
    	tempState = STATE_SCREEN_SAVER; // Set the temporary state to SCREEN_SAVER
    	currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
    } else {
    	screenSaverTimer += get_rtime(); // Increment the screen saver timer
    }
}

function handleUserPlayingSateInput() {

	if (get_val(XB1_LT) && event_release(XB1_VIEW)) {
	 	currentState = STATE_OFF; // Transition to the OFF state
	}

    
	if (get_val(XB1_LT) && event_release(XB1_MENU)) {
        currentState = STATE_SETUP_MENU; // Open Setup menu
    }
    
   	if (get_val(XB1_LT) && event_release(XB1_RIGHT)) {
   		quickToggleModId = 0; // Set the quick toggle mod ID to 0
   		tempState = STATE_QUICK_TOGGLE; // Set the temporary state to QUICK_TOGGLE
    	currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
    }

   	if (get_val(XB1_LT) && event_release(XB1_LEFT)) {
   		quickToggleModId = 1; // Set the quick toggle mod ID to 1
   		tempState = STATE_QUICK_TOGGLE; // Set the temporary state to QUICK_TOGGLE
    	currentState = STATE_CLEAN_SCREEN; // Transition to the CLEAN_SCREEN state
    }
    
	if (get_val(XB1_VIEW)) {
		screenSaverTimer = 0; // Reset the screen saver timer
	 	currentState = STATE_PLAYING; // Transition to the PLAYING state
	}
}
