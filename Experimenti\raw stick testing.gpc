define STICK_MAG_THRESHOLD = 17;    // Minimum magnitude to trigger stick scaling
define STICK_MAG_TARGET   = 100;   // Target magnitude for scaling

int magnitude;
int rx;
int ry;
int lx;
int ly;
int scaled_x;
int scaled_y;

// Function to handle stick scaling
function scaleStick(x, y, threshold, target, stick) {
    magnitude = isqrt(x * x + y * y);
    
    if(magnitude >= threshold && magnitude != 0) {
        scaled_x = (x * target) / magnitude;
        scaled_y = (y * target) / magnitude;
        
        if(stick == 1) {  // Right stick
            set_val(XB1_RX, scaled_x);
            set_val(XB1_RY, scaled_y);
        } else {          // Left stick
            set_val(XB1_LX, scaled_x);
            set_val(XB1_LY, scaled_y);
        }
    }
}

main {
    // Get stick values
    rx = get_val(XB1_RX);
    ry = get_val(XB1_RY);
    lx = get_val(XB1_LX);
    ly = get_val(XB1_LY);
    
    // Scale both sticks
    scaleStick(rx, ry, STICK_MAG_THRESHOLD, STICK_MAG_TARGET, 1);  // Right stick
    scaleStick(lx, ly, STICK_MAG_THRESHOLD, STICK_MAG_TARGET, 0);  // Left stick
}