// Script was generated with < FIFA Series Skills Generator > ver. 15.7 Date :10/07/21 Time: 01:58:55
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
$$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
$$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
$$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
$$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
$$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
$$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
$$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
\__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
$$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
$$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
$$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
$$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
$$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
$$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
$$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
\__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_R1; // Finesse Shot
define PlayerRun     = PS4_L1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MODIFIER      = PS4_L1;     
define CrossBtn      = PS4_SQUARE; // Cross Btn 
define ThroughBall   = PS4_TRIANGLE; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
define SCOOP_TURN_FAKE_SKILL          =49; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
                                               
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                
                                                                
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot)){ 
	      if(event_press(ShotBtn) ){ 
		        combo_run(Timed_Finesse_Finish ); 
	      } 
         set_val(ShotBtn,0);
    } 
                                                  
                                                  
    //--------------------------------------------------------------
    //  turn ON Penalty  hold  L1 and press OPTIONS 
    if(get_val(PS4_L1)){                      
        if(event_press(PS4_OPTIONS)){             
            onoff_penalty = !onoff_penalty;   
        }                                         
       set_val(PS4_OPTIONS,0);                    
    }                                              
    if(onoff_penalty){ fPenalties ();  }  
                                                   
	//-----------------------------------------  
	// ON / OFF FREE KICK MODE                   
	// hold L1/LB and press SHARE/VIEW           
	if(get_val(PS4_L1)){                          
        if(event_press(PS4_SHARE)){             
            onoff_FK = !onoff_FK;               
        }                                       
       set_val(PS4_SHARE,0);                    
    }                                           
    if(onoff_FK){ f_FREE_KICK ();  }             
    //----------------------------------------- 
    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
	       LX = get_val(MOVE_X);                                      
	       LY = get_val(MOVE_Y);                                      
     calc_zone ();                                              
    }                                                           
    //----------------------------------------------------------- 
                                      
   if(!get_val(PS4_R3) &&  !get_val(PaceCtrol) && !get_val(SprintBtn)){ // all Skills mode                
                                                     
	      //  Right Stick -->  UP                          
	      if( get_val(PS4_RY) < -70  && !flick_up ) {   
	      		flick_up = TRUE;                          
	      		right_on = FALSE;          
	      		ACTIVE = HEEL_TO_HEEL_FLICK_SKILL; combo_run(HEELtoHEEL);   //2. Heel to Heel
	      }                                              
	      //  Right Stick -->  DOWN                               
	      if( get_val(PS4_RY) >  70  && !flick_d ) {     
	      		flick_d = TRUE;                            
	      		right_on = TRUE;              
	      		 ACTIVE = SCOOP_TURN_FAKE_SKILL; combo_run(SCOOP_TURN_FAKE);
	      }                                               
                                                        
	      //  Right Stick --> LEFT                                
	      if( get_val(PS4_RX) < -70  && !flick_l ) {     
	      		flick_l = TRUE;                             
	      		right_on = FALSE;               
	      		ACTIVE = REVERSE_ELASTICO_SKILL; combo_run(REVERSE_ELASTICO);  // 13. REVERSE_ELASTICO_SKILL
	      }                                               
                                                        
	      // Right Stick --> RIGHT                                   
	      if( get_val(PS4_RX) >  70  && !flick_r ) {      
	      		flick_r = TRUE;                             
	      		right_on = TRUE;            
	      		ACTIVE = ELASTICO_SKILL; combo_run(ELASTICO);  // 12. ELASTICO_SKILL
	      }                                                
                                                         
                                                          
        if(abs(get_val(PS4_RY))<20  && abs(get_val(PS4_RX))<20){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }                                              
        set_val(SKILL_STICK_X,0); 
        set_val(SKILL_STICK_Y,0); 
    }// end of ALWAYS ON  
      if(event_press(PS4_R3)){    
	      		right_on = TRUE;      
	      		ACTIVE = ROULETTE_SKILL; combo_run(ROULETTE); // 17. ROULETTE_SKILL
      }                     
    if(event_press(PS4_L2) && !tap){  
        combo_run(ONE_TAP);              
	                    
    }else if( event_press(PS4_L2) &&  tap){ 
    	                                 
        right_on = TRUE;ACTIVE = FOUR_TOUCH_TURN_SKILLS ;  combo_run(FOUR_TOUCH_TURN_cmb);
    }                           
                                                
    
    //========================================
    // *** ULTIMATE FINISHING ***
    //========================================
    if(event_release(CrossBtn)){
        after_cross_timer = 4000;
    }
    
    if(after_cross_timer){
        after_cross_timer -=get_rtime();
    }
                  
    if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
      if(after_cross_timer <= 0 ){
        if(Ultimate_Finishing){
          if( event_press(ShotBtn)){
            combo_restart(Inside_Box_Finishing); 
          }
        }else{
          if(get_val(ShotBtn) && get_ptime(ShotBtn) > 150){
            set_val(ShotBtn,0);
          }
        }
      }
    }   
    
    if(Ultimate_Finishing){
      if( get_val(FinesseShot) && event_press(ShotBtn) && !get_val(PaceCtrol)){
         set_val(ShotBtn,0);
         combo_run(OutSide_Box_Shoot);
      }
    }else{
        if(get_val(FinesseShot)){
            if(get_val(ShotBtn) && get_ptime(ShotBtn) > 200 ){
                set_val(ShotBtn,0);
            }
        }
    }

    //===============================================
    //    GRROUND PASSES  MIN / MAX
    //===============================================
    if(get_val(PassBtn)){
        ground_pass_timer += get_rtime();
    }
    if(get_val(PassBtn) && get_ptime(PassBtn) > Ground_Pass_MAX){
        set_val(PassBtn,0);
    }
    if(event_release(PassBtn)){
        if(ground_pass_timer && (ground_pass_timer < Ground_Pass_MIN)){
            GP_difference = Ground_Pass_MIN - ground_pass_timer;
            
            combo_run(Ground_Pass_MIN_cmb);
        }
        ground_pass_timer = 0;
    }
    //===============================================
    //    TROUGH PASSES  MIN / MAX
    //===============================================
    if(get_val(ThroughBall)){
    	trough_pass_timer += get_rtime();
    }
    if(get_val(ThroughBall) && get_ptime(ThroughBall) > Trough_Pass_MAX){
    	set_val(ThroughBall,0);
    	if(DoubleTapTroughPass && !trough_start) combo_run(DOUBLE_TAP_TROUGH_cmb);
    	trough_start = TRUE;
    }
    if(event_release(ThroughBall)){
    	if(!trough_start){
	    	if(trough_pass_timer && (trough_pass_timer < Trough_Pass_MIN)){
	    		TroughP_difference = Trough_Pass_MIN - trough_pass_timer;
	    		
	    		combo_run(Trough_Pass_MIN_cmb);
	    	}else{
	    		if(DoubleTapTroughPass) combo_run(DOUBLE_TAP_TROUGH_cmb);
	    	}
    	}
    	trough_pass_timer = 0;
    	trough_start      = FALSE;
    }
     
	//===============================================
    //    LOB PASSES/CROSES  MIN / MAX
    //===============================================
    if(get_val(CrossBtn)){
    	Lob_pass_timer += get_rtime();
    }
    if(get_val(CrossBtn) && get_ptime(CrossBtn) > Lob_Pass_MAX){
    	set_val(CrossBtn,0);
    	if(DoubleTapTroughPass)combo_run(DOUBLE_TAP_TROUGH_cmb);
    }
    if(event_release(CrossBtn)){
    	if(Lob_pass_timer && (Lob_pass_timer < Lob_Pass_MIN)){
    		LobP_difference = Lob_Pass_MIN - Lob_pass_timer;
    		
    		combo_run(Lob_Pass_MIN_cmb);
    	}
    	Lob_pass_timer = 0;
    }

    // all Skills mode                
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int after_cross_timer;  
int Ultimate_Finishing = 0;
 combo Inside_Box_Finishing {
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(200);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600); 
} 
combo OutSide_Box_Shoot {
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(150);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, 100);
    wait(17);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    wait(15);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);            
}       

     
function INSIDE_BOX_AIM() { 
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 }
int ground_pass_timer; 
int Ground_Pass_MIN = 80;
int Ground_Pass_MAX = 250;
int GP_difference;

combo Ground_Pass_MIN_cmb {
	set_val(PassBtn,100);
	wait(GP_difference);
}
//=================================
int trough_pass_timer; 
int Trough_Pass_MIN = 80;
int Trough_Pass_MAX = 300;
int TroughP_difference;
int DoubleTapTroughPass = TRUE;
int trough_start;

combo Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	wait(TroughP_difference);
	if(DoubleTapTroughPass){
		set_val(ThroughBall,100);
	}
	wait(60);
}
combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}

//=================================
int Lob_pass_timer; 
int Lob_Pass_MIN = 80;
int Lob_Pass_MAX = 350;
int LobP_difference;

combo Lob_Pass_MIN_cmb {
	set_val(CrossBtn,100);
	wait(LobP_difference);
}
//=================================
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
////////////////////////////////////////////////////////////////
// VARIABLES  for FREE KICK MOD 
//-------------------------------------------- 
define TOP_SPIN       = 1;                     
define SIDE_SPIN      = 2;                     
define KNUCKLEBALL_FK = 3;                     
define spin_time      = 80;                    
//-------------------------------------------- 
int onoff_FK;                                  
int shot_power = 355;                          
int side_dir   = 100;                          
int FK_mode = 1;

function f_FREE_KICK (){
/////////////////////////////////////////// 
    // AIMING FIX :                                 
    // AIM and then press and hold R2/RT to Lock Aim 
    if(!get_val(PS4_R2)){                             
        LX = get_val(PS4_LX);                           
        LY = get_val(PS4_LY);                           
        if(get_val(PS4_L1)){                     
            ///////////////////////////////////////////   
            // SET THE POWER of the SHOOT                 
            if(event_press(PS4_UP))    shot_power = 330;//
            if(event_press(PS4_RIGHT)) shot_power = 370;//
            if(event_press(PS4_DOWN))  shot_power = 390;//
            if(event_press(PS4_LEFT))  shot_power = 410;//
        }                                               
                                 
    }else if(get_val(PS4_R2)){                       
                               
        ///////////////////////////////////////////    
        // SET THE POWER of the SHOOT                  
        ///////////////////////////////////////////
        // Fine tune Aim                         
        if(press_hold(PS4_LEFT)) {               
            LX = LX - 1;                                  
        }                                               
        if(press_hold(PS4_RIGHT)){                      
            LX = LX + 1;                                  
        }                                               
        if(press_hold(PS4_UP)){                         
            LY = LY - 1;                                  
        }                                               
        if(press_hold(PS4_DOWN)){                       
            LY = LY + 1;                                  
        }                                               
        set_val(PS4_LX, LX );                           
        set_val(PS4_LY, LY );   
    }
    set_val(PS4_UP,0);                              
    set_val(PS4_RIGHT,0);                           
    set_val(PS4_LEFT,0);                            
    set_val(PS4_DOWN,0);                                                   
    ///////////////////////////////////////////      
    // TOP SPIN FK                                   
    if(get_val(XB1_Y)){                              
        FK_mode = TOP_SPIN ;                          
        //shot_power = 450;// 380 / 400               
        combo_run(SHOT_POWER);                        
        set_val(XB1_Y,0);                             
    }                                               
    ///////////////////////////////////////////     
    // SIDE SPIN FK                                 
    // left side                                    
    if(get_val(XB1_X)){                             
        FK_mode = SIDE_SPIN;                          
        side_dir = -100;                              
        //shot_power = 300;                           
        combo_run(SHOT_POWER);                       
        set_val(XB1_X,0);                            
    }                                              
    ///////////////////////////////////////////    
    // SIDE SPIN FK                                 
    // right side                                   
    if(get_val(XB1_B)){                             
        FK_mode = SIDE_SPIN;                          
        side_dir =  100;                              
        //shot_power = 300;                           
        combo_run(SHOT_POWER);                        
        set_val(XB1_B,0);                             
    }                                               
    ///////////////////////////////////////////     
    // KNUCKLEBALL_FK                               
    //                                              
    if(get_val(XB1_A)){                             
        FK_mode = KNUCKLEBALL_FK;                     
        shot_power = 550;                             
        combo_run(SHOT_POWER);                        
        set_val(XB1_A,0);                             
    }                                               
    if(event_press(PS4_L3)){                        
        FK_mode = 0;                                  
        combo_run(SHOT_POWER);                        
    }                                               
     set_val(PS4_R2,0);             
}

combo SHOT_POWER {                                    
	set_val(ShotBtn,100);                               
	wait(shot_power);                                   
	wait(300);                                          
	/////////////////////////////////////////////////   
	//  FREE KICK MODE                                  
	if(FK_mode == TOP_SPIN )  combo_run(TOP_SPIN_FK);    
	if(FK_mode == SIDE_SPIN ) combo_run(SIDE_SPIN_FK);    
	if(FK_mode == KNUCKLEBALL_FK ) combo_run(KNUCKLEBALL);
}                                                       
//--------------------------------------------------- 
combo TOP_SPIN_FK  {                                  
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY, 100);                               
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100);                               
	wait(spin_time);                                           
}                                                     
//--------------------------------------------------- 
combo SIDE_SPIN_FK  {                                 
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,100);// DOWN                         
	wait(spin_time);                                           
	set_val(PS4_RX,side_dir);// LEFT or RIGHT           
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100);// UP                          
	wait(spin_time);                                           
}                                                     
//--------------------------------------------------- 
combo KNUCKLEBALL {                                   
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,100);// DOWN                         
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,-100); // UP                         
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY,0);                                  
	wait(spin_time);                                           
	set_val(PS4_RX,0);                                  
	set_val(PS4_RY, 100);// DOWN                        
	wait(spin_time);                                           
}                                                     
function press_hold(f_btn) {   
    return event_press(f_btn) || get_val(f_btn) && get_ptime(f_btn) > 250
           && get_ptime(f_btn) % (get_rtime() * 8) == 0;  
} 
combo InstantTimedFinish {
    CORNER()
    set_val(ShotBtn, 100);             
    wait(200);
    set_val(ShotBtn, 0);
    CORNER()
    wait(150)
    set_val(ShotBtn, 100);             
    wait(200);  
} 

int long_finesse;
function CORNER() { 

     if (combo_running(InstantTimedFinish)){
         FINESSE_OR_NORMAL = 100;
     }else{
         FINESSE_OR_NORMAL =  25;
     }   
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {  
        right_on = FALSE;
        LA (FINESSE_OR_NORMAL, -90);
    }
          
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    { 
        right_on = TRUE;
        LA (FINESSE_OR_NORMAL, 90);
    }
    
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    { 
        right_on = TRUE;
        LA (inv(FINESSE_OR_NORMAL), -90);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {
        right_on = FALSE;
        LA (inv(FINESSE_OR_NORMAL),  90);
    }
          
}

function CORNER_FIX_MOVE() {
        
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {   right_on = FALSE;
        LA (100,-16);
    }
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    {   right_on = TRUE;
        LA (100, 16);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {   right_on = TRUE; 
        LA (-100,-16); 
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {   right_on = FALSE;
        LA (-100, 16);
    }
}

int ping = 70;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 200;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    CORNER ();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause );
    
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn)            
           
} 
combo FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);     
}

combo SCOOP_TURN_FAKE {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PaceCtrol,100);
    wait(40); 
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
    wait(250);
    LA_L_R()
    wait(300); 
}        
                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}                                        
                                         
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   
combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
}                  
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}                        
///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//=========================================================== 
// --- PENALTIES 
//*************************************************   
int onoff_penalty;                                   
int dir;                                      
int LeftRight,UpDown; 
int Pen_X,Pen_Y;
int correct_X,correct_Y;
const int PenaltiX_Y [] []= {
{  87,   0},//0. 1 Right
{ -86,   0},//1. 2 Left
{   0, 100},//2. 3 Down
{   0, -70},//3. 4 Up
{  56,  90},//4. 8 Right Down
{  78, -38},//5. 9 Right Up
{ -56,  90},//6.11 Left Down  
{ -78, -38} //7.13 Left Up 
};
//---------------------------   
function fPenalties () {                              
    
    if(!get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )LeftRight = 1;// Right
		                                              
		if(event_press(PS4_LEFT) ) LeftRight = 2;// Left
		                                              
		if(event_press(PS4_DOWN))  UpDown    = 3;// Down
		                                              
		if(event_press(PS4_UP))    UpDown    = 4;// Up  
		                                              
		if(LeftRight && !UpDown){                       
		if(LeftRight == 1) dir = 1; // Right          
		else   dir = 2;             // Left           
		                                              
		if(dir == 1 ){                                
		   Pen_X = PenaltiX_Y [0][0] ;  //0.          
		   Pen_Y = PenaltiX_Y [0][1] ;                        
		}                                             
		                                              
		if(dir == 2 ){                                
		   Pen_X = PenaltiX_Y [1][0] ;  //1.           
		   Pen_Y = PenaltiX_Y [1][1] ;                     
		}                                             
		}                                               
		else if(!LeftRight && UpDown){                  
		if(UpDown == 3) dir = 3; // Down              
		else   dir = 4;          // Up                
		if(dir == 3 ){                                
		   Pen_X = PenaltiX_Y [2][0] ;  //2.           
		   Pen_Y = PenaltiX_Y [2][1] ;                   
		}                                             
		                                              
		if(dir == 4 ){                                
		   Pen_X = PenaltiX_Y [3][0] ;  //3.           
		   Pen_Y = PenaltiX_Y [3][1] ;                    
		}                                             
		                                              
		}                                               
		else if(LeftRight && UpDown){                   
		//---------------------------------------       
		  dir = (LeftRight * UpDown) + 5 ;            
		  // Right Down                               
		  if(dir == 8 ){                              
		      Pen_X = PenaltiX_Y [4][0] ;  //4.           
		      Pen_Y = PenaltiX_Y [4][1] ;                      
		  }                                           
		  //Right Up                                  
		  if(dir == 9 ){                              
		      Pen_X = PenaltiX_Y [5][0] ;  //5.           
		      Pen_Y = PenaltiX_Y [5][1] ;                     
		  }
		  // Left Down                                
		  if(dir == 11 ){                             
		      Pen_X = PenaltiX_Y [6][0] ;  //6.           
		      Pen_Y = PenaltiX_Y [6][1] ;                      
		  }           
		  // Left Up                                  
		  if(dir == 13 ){                             
		      Pen_X = PenaltiX_Y [7][0] ;  //7.           
		      Pen_Y = PenaltiX_Y [7][1] ;                        
		  }                                           
		                                  
		}                                               
     }else if(get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )correct_X += 1;// Right
		                                          
		if(event_press(PS4_LEFT) ) correct_X -= 1;// Left
		                                          
		if(event_press(PS4_DOWN))  correct_Y += 1;// Down
		                                          
		if(event_press(PS4_UP))    correct_Y -= 1;// Up  
     }
     
     	set_val(PS4_LX, Pen_X + correct_X);
     	set_val(PS4_LY, Pen_Y + correct_Y);
     	
      set_val(PS4_UP,   0);                       
      set_val(PS4_DOWN, 0);                       
      set_val(PS4_LEFT, 0);                      
      set_val(PS4_RIGHT,0);                      
      //----  reset the aiming direction  
      if(event_press(XB1_RS)){           
      	LeftRight = 0;                   
      	UpDown    = 0;                  
      	dir       = 0;
      	Pen_X     = 0;
      	Pen_Y     = 0;
      	correct_X = 0;
      	correct_Y = 0;
      }                              
      set_val(XB1_RS,0);           
}                         
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             