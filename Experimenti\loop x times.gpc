// Variable to track the number of loop iterations
int loops;

main {
    // Reset loop counter when left button is pressed
    if(event_press(XB1_LEFT))
        loops = 0;

    // Run the loop combo if iterations are less than 10
    // This prevents infinite looping
    if(loops < 10)
        combo_run(Loop);
}

// Combo to perform a repeated button press action
combo Loop {
    // Fully press the A button (100% intensity)
    set_val(XB1_A,100);
    
    // Wait for 40 time units
    wait(40);
    
    // Synchronize timing to ensure consistent loop execution
    // Adjusts wait time based on remaining runtime
    wait(40 - get_rtime());
    
    // Increment loop counter
    loops ++;
}