// Star of Lakshmi Movement Script
// Eight-pointed star with equal points

// Configuration
define MAX_VAL = 100          // Maximum stick value
define DEADZONE = 10          // Stick deadzone
define ANGLE_STEP = 3         // Movement speed
define POINT_COUNT = 8        // Number of points
define INNER_RADIUS = 60      // Inner radius (valley between points)
define OUTER_RADIUS = 100     // Outer radius (point tips)

// Global variables
int stick_x;
int stick_y;
int out_x;
int out_y;
int current_angle;
int magnitude;
int stick_angle;
int angle;
int base_angle;
int segment;
int segment_angle;
int segment_progress;
int radius;
int angle_rad;
int x;
int y;

// Cosine function using sine with phase shift
function icos(x) { return isin(x + 8192); }

// Fixed-point sine function
function isin(x) {
    x = (x % 32767) << 17;
    if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
    x = x >> 17;
    return x * ((98304) - (x * x) >> 11) >> 14;
}

// Function to get angle from stick position (returns 0-359 degrees)
function atan2(y, x) {
    if(x == 0 && y == 0) return 0;
    
    if(x >= 0) {
        if(y >= 0) {
            // First quadrant
            if(x >= y) angle = (y * 45) / x;
            else angle = 90 - (x * 45) / y;
        } else {
            // Fourth quadrant
            y = -y;
            if(x >= y) angle = 360 - (y * 45) / x;
            else angle = 270 + (x * 45) / y;
        }
    } else {
        x = -x;
        if(y >= 0) {
            // Second quadrant
            if(x >= y) angle = 180 - (y * 45) / x;
            else angle = 90 + (x * 45) / y;
        } else {
            // Third quadrant
            y = -y;
            if(x >= y) angle = 180 + (y * 45) / x;
            else angle = 270 - (x * 45) / y;
        }
    }
    return angle;
}

function calc_star_position(angle, mag) {
    // Calculate base angle within 360 degrees
    base_angle = angle % 360;
    if(base_angle < 0) base_angle += 360;
    
    // Calculate segment (0-7) and progress within segment
    segment = (base_angle * POINT_COUNT) / 360;
    segment_angle = base_angle - (segment * 360 / POINT_COUNT);
    
    // Calculate radius based on angle within segment
    segment_progress = (segment_angle * 100) / (360 / POINT_COUNT);
    
    if(segment_progress <= 50) {
        // First half - going from outer to inner
        radius = OUTER_RADIUS - ((OUTER_RADIUS - INNER_RADIUS) * segment_progress * 2) / 100;
    } else {
        // Second half - going from inner to outer
        segment_progress = segment_progress - 50;
        radius = INNER_RADIUS + ((OUTER_RADIUS - INNER_RADIUS) * segment_progress * 2) / 100;
    }
    
    // Scale radius by input magnitude
    radius = (radius * mag) / 100;
    
    // Convert angle to GPC's angle format (0-32767 represents 0-2π)
    angle_rad = (base_angle * 32767) / 360;
    
    // Convert polar coordinates to cartesian using fixed-point trig
    out_x = (radius * icos(angle_rad)) / 32767;
    out_y = (radius * isin(angle_rad)) / 32767;
}

main {
    // Get stick input
    stick_x = get_val(XB1_LX);
    stick_y = get_val(XB1_LY);
    
    // Calculate magnitude
    magnitude = isqrt(stick_x * stick_x + stick_y * stick_y);
    
    if(magnitude > DEADZONE) {
        // Normalize magnitude to 0-100 range
        magnitude = (magnitude * 100) / MAX_VAL;
        if(magnitude > 100) magnitude = 100;
        
        // Get the angle from stick position
        stick_angle = atan2(stick_y, stick_x);
        current_angle = stick_angle;
        
        // Calculate position on star
        calc_star_position(current_angle, magnitude);
        
        // Apply calculated position
        set_val(XB1_LX, out_x);
        set_val(XB1_LY, out_y);
    } else {
        // Center stick when in deadzone
        set_val(XB1_LX, 0);
        set_val(XB1_LY, 0);
    }
}