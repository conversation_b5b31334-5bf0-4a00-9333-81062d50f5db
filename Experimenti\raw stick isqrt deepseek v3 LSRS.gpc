// Define threshold and target values for right stick
define RIGHT_MAG_THRESHOLD = 17 * 32767 / 100;
define RIGHT_MAG_TARGET    = 32767;

// Define threshold and target values for left stick
define LEFT_MAG_THRESHOLD = 17 * 32767 / 100;
define LEFT_MAG_TARGET    = 32767;

// Declare variables for right stick
int currentRX;
int currentRY;
int vectorMagnitudeR;
int scaledRX;
int scaledRY;

// Declare variables for left stick
int currentLX;
int currentLY;
int vectorMagnitudeL;
int scaledLX;
int scaledLY;

main {
    // Handle right stick
    currentRX = get_val(POLAR_RX);
    currentRY = get_val(POLAR_RY);
    
    vectorMagnitudeR = isqrt(currentRX * currentRX + currentRY * currentRY);
    
    if(vectorMagnitudeR >= RIGHT_MAG_THRESHOLD) {
        scaledRX = (currentRX * RIGHT_MAG_TARGET) / vectorMagnitudeR;
        scaledRY = (currentRY * RIGHT_MAG_TARGET) / vectorMagnitudeR;
        
        set_val(POLAR_RX, scaledRX);
        set_val(POLAR_RY, scaledRY);
    }
    
    // Handle left stick
    currentLX = get_val(POLAR_LX);
    currentLY = get_val(POLAR_LY);
    
    vectorMagnitudeL = isqrt(currentLX * currentLX + currentLY * currentLY);
    
    if(vectorMagnitudeL >= LEFT_MAG_THRESHOLD) {
        scaledLX = (currentLX * LEFT_MAG_TARGET) / vectorMagnitudeL;
        scaledLY = (currentLY * LEFT_MAG_TARGET) / vectorMagnitudeL;
        
        set_val(POLAR_LX, scaledLX);
        set_val(POLAR_LY, scaledLY);
    }
}