    define GEN_SENS = 100;  // General sensitivity - Default: 100 - Range [0 ~ 327]
                            // set a number below 100 for less sens - above 100 for more sens
 
    define ADS_SENS = 50;  // Sensitivity while aiming - Default: 100 - Range [0 ~ 327]
                            // set a number below 100 for less sens - above 100 for more sens
 
    define RUN_SENS = 100;     // Sensitivity while running - Default: 100 - Range [0 ~ 327]
                               // set a number below 100 for less sens - above 100 for more sens 
 
    define MIDPOINT = 50;   /** MIDPOINT - Default: 50 - Range [0 ~ 100]
    If you set a number below 50 you are defining a zone of high sensitivity range when close to rest position 
    (fast movements) and a low sensitivity range when far from rest position (better accuracy).
    if you set a number above 50 you are defining a zone of low sensitivity when close to the rest position
    (better accuracy), and a zone of high sensitivity when far from rest position (fast movements).
    
    Wenn Sie eine Zahl unter 50 einstellen, definieren Sie eine Zone mit hohem Empfindlichkeitsbereich in der Nähe der Ruheposition 
    (schnelle Bewegungen) und einen niedrigen Empfindlichkeitsbereich, wenn er weit von der Ruheposition entfernt ist (bessere Genauigkeit).
    Wenn Sie eine Zahl über 50 einstellen, definieren Sie eine Zone mit niedriger Empfindlichkeit, wenn Sie sich nahe der Ruheposition befinden
    (bessere Genauigkeit) und einen Bereich mit hoher Empfindlichkeit, wenn er weit von der Ruheposition entfernt ist (schnelle Bewegungen)
    
    **/

main {

    // ADS SENS
    if (get_val(PS4_L2) ) {        
        sensitivity(PS4_LX, MIDPOINT, ADS_SENS);        
        sensitivity(PS4_LY, MIDPOINT, ADS_SENS); 
    }
    else 
    {
    // GENERAL SENS
        sensitivity(PS4_LX, MIDPOINT, GEN_SENS);
        sensitivity(PS4_LY, MIDPOINT, GEN_SENS);
    }
    // RUNNING SENS
    if(get_val(PS4_RY) < -90) 
    {
        sensitivity(PS4_LX, MIDPOINT, RUN_SENS);
        sensitivity(PS4_LY, MIDPOINT, RUN_SENS);
    } 
}