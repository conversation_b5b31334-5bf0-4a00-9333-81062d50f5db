// Script was generated with < FIFA Series Skills Generator > ver. 15.1 Date :10/01/21 Time: 17:21:20
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
$$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
$$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
$$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
$$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
$$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
$$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
$$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
\__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
$$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
$$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
$$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
$$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
$$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
$$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
$$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
\__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_LB;     
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
int onoff_penalty;                                               
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                
   //--- DEFENSE                               
   if(defence_on) f_defence();                 
                                                
    //--- Use Open Up MOD                                                         
    if(event_press(ShotBtn) || event_press(PassBtn) || event_press(FinesseShot)){  
        combo_run(OPEN_UP_cmb);                                                   
    }                                                                            
                                                                                 
                                                                
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot)){ 
	      if(event_press(ShotBtn) ){ 
		        combo_run(Timed_Finesse_Finish ); 
	      } 
         set_val(ShotBtn,0);
    } 
    ///////////////////////////////////////////////////////////// 
    //                                                           
    if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
	       LX = get_val(MOVE_X);                                      
	       LY = get_val(MOVE_Y);                                      
     calc_zone ();                                              
    }                                                           
    //----------------------------------------------------------- 
                                      
   if(!get_val(PS4_R3) &&  !get_val(PaceCtrol) && !get_val(SprintBtn)){ // all Skills mode                
                                                     
	      //  Right Stick -->  UP                          
	      if( get_val(PS4_RY) < -70  && !flick_up ) {   
	      		flick_up = TRUE;                          
	      		right_on = FALSE;          
	      		ACTIVE = HEEL_TO_HEEL_FLICK_SKILL; combo_run(HEELtoHEEL);   //2. Heel to Heel
	      }                                              
	      //  Right Stick -->  DOWN                               
	      if( get_val(PS4_RY) >  70  && !flick_d ) {     
	      		flick_d = TRUE;                            
	      		right_on = FALSE;              
	      		ACTIVE = RAINBOW_SKILL; combo_run(RAINBOW);
	      }                                               
                                                        
	      //  Right Stick --> LEFT                                
	      if( get_val(PS4_RX) < -70  && !flick_l ) {     
	      		flick_l = TRUE;                             
	      		right_on = FALSE;               
	      		ACTIVE = ROULETTE_SKILL; combo_run(ROULETTE); // 17. ROULETTE_SKILL
	      }                                               
                                                        
	      // Right Stick --> RIGHT                                   
	      if( get_val(PS4_RX) >  70  && !flick_r ) {      
	      		flick_r = TRUE;                             
	      		right_on = TRUE;            
	      		ACTIVE = ROULETTE_SKILL; combo_run(ROULETTE); // 17. ROULETTE_SKILL
	      }                                                
                                                         
                                                          
        if(abs(get_val(PS4_RY))<20  && abs(get_val(PS4_RX))<20){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }                                              
        set_val(SKILL_STICK_X,0); 
        set_val(SKILL_STICK_Y,0); 
    }// end of ALWAYS ON  
                              
 		// SPRINT
	if(get_val(XB1_RT) && event_press(XB1_LB)){combo_run(SPRINT_cmb);}                                  
                                  
     if (event_press(XB1_RS)){ combo_run(FakeRabona);   }
    set_val(XB1_RS,0);                    
     
     //  turn ON Penalty  hold  L1 and press OPTIONS
	if(get_val(PS4_L1)){
		if(event_press(PS4_OPTIONS)){
			onoff_penalty = !onoff_penalty;
		}
		set_val(PS4_OPTIONS,0);
    }
    if(onoff_penalty){

		if (get_val(XB1_X))	    {set_val(PS4_LX,0);set_val(PS4_LY,0);}		// Middle 
		if (get_val(PS4_LEFT))	{set_val(PS4_LX,-78);set_val(PS4_LY,-38);} 	// Left Up 
		if (get_val(PS4_RIGHT))	{set_val(PS4_LX,78);set_val(PS4_LY,-38);} 	// Right Up 
		if (get_val(PS4_UP))	{set_val(PS4_LX,-54);set_val(PS4_LY, 90);} 	// Left Down 
		if (get_val(PS4_DOWN))	{set_val(PS4_LX,54);set_val(PS4_LY,90);} 	// Right Down 
   }
    
    if(get_val(XB1_PL1)){combo_run(OutSide_Box_Shoot);}  
    
        // RB PASS
   	if(get_val(XB1_PR1)) {set_val(XB1_A,100);}	
	if(event_release(XB1_PR1)){
		combo_run(RB_PASS);
	} 
 	
	// THREADED THROUGH PASS
	if(!get_val(XB1_A)) {
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_Y,100);
		}
	}
    
    //========================================
    // *** ULTIMATE FINISHING ***
    //========================================
    if(event_release(CrossBtn)){
        after_cross_timer = 4000;
    }
    
    if(after_cross_timer){
        after_cross_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && after_cross_timer <= 0 ){
            combo_restart(Inside_Box_Finishing); 
        }
    }   
    
        if( get_val(FinesseShot) && event_press(ShotBtn) && !get_val(PaceCtrol)){
       set_val(ShotBtn,0);
       combo_run(OutSide_Box_Shoot);
    }
    // all Skills mode                
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
combo RAINBOW {          
	RA_DOWN ();    // down 
	wait(w_rstick);        
	RA_UP();       // up   
	wait(w_rstick);        
}

combo RB_PASS {
	wait(100);  // 100 ms
	set_val(XB1_RB,100);
	wait(100);
}

combo SPRINT_cmb { 
	RA_UP();   
	wait(40);  
	wait(40);  
	RA_UP();   
	wait(40);  
	wait(40);  
} 

combo FakeRabona {
set_val(PaceCtrol,100);
    set_val(MOVE_X,inv(LX)   );
    set_val(MOVE_Y,inv(LY)   );
    set_val(ShotBtn,100);
    wait(40); 
    set_val(MOVE_X,inv(LX)   );
    set_val(MOVE_Y,inv(LY)   );
    set_val(PaceCtrol,100); 
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    set_val(MOVE_X,inv(LX)   );
    set_val(MOVE_Y,inv(LY)   );
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
    }


 int after_cross_timer;  
 combo Inside_Box_Finishing {
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(211);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600); 
} 
combo OutSide_Box_Shoot {
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    INSIDE_BOX_AIM();
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(170);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, 100);
    wait(17);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    wait(15);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    wait(80)
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);            
}       

     
 function INSIDE_BOX_AIM() { 
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 }
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo OPEN_UP_cmb {      
    set_val(PS4_L3,100); 
    wait(100);           
}                       
int Teammate_Contain_on = FALSE;
//--- Defense V 2.0
// Credits to Dark.Angle 
//=======================================
int defence_on = TRUE;
function f_defence (){
    
   //============ COPY THis TO MAIN SECTION ============//
    
	if( get_val(SprintBtn) && get_val(PassBtn)){	
		set_val(PS4_L3,100);
		if( abs(get_val(PS4_LX))>20  || abs(get_val(PS4_LY))>20  ) {
			set_val(PassBtn,0);
			if(Teammate_Contain_on) set_val(XB1_X,100);// auto team mate contain
			combo_run(JoCKEY);
		}else{  
			set_val(PassBtn,100); 
		}
	
	}
  
  //==============NEW SENSitivity MODE ==============//
  //This will gives you good left stick dribbling and better control on ball ,, also faster jockey reactions while defending
  //with new defence mode
    if (!get_val(SprintBtn) && !get_val(PassBtn) && !get_val(PS4_TRIANGLE) && !get_val(ShotBtn) && !combo_running(JoCKEY) ){
		sensitivity(PS4_LX, NOT_USE, 91);
		sensitivity(PS4_LY, NOT_USE, 91);
	}else if ( get_val(SprintBtn) && !combo_running(JoCKEY) ) {
		sensitivity(PS4_LX, NOT_USE, 110);
		sensitivity(PS4_LY, NOT_USE, 110);
	} else if ( get_val(SprintBtn) && combo_running(JoCKEY) ) { 
		sensitivity(PS4_LX, NOT_USE, 108);
		sensitivity(PS4_LY, NOT_USE, 108);
	}
}

combo JoCKEY {
  set_val(PaceCtrol,100);
  wait(333);
  set_val(PaceCtrol,0);
  wait(30);
}
combo InstantTimedFinish {
    CORNER()
    set_val(ShotBtn, 100);             
    wait(220);
    set_val(ShotBtn, 0);
    CORNER()
    wait(160)
    set_val(ShotBtn, 100);             
    wait(220);  
} 

int long_finesse;
function CORNER() { 

     if (combo_running(InstantTimedFinish)){
         FINESSE_OR_NORMAL = 100;
     }else{
         FINESSE_OR_NORMAL =  25;
     }   
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {  
        right_on = FALSE;
        LA (FINESSE_OR_NORMAL, -90);
    }
          
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    { 
        right_on = TRUE;
        LA (FINESSE_OR_NORMAL, 90);
    }
    
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    { 
        right_on = TRUE;
        LA (inv(FINESSE_OR_NORMAL), -90);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {
        right_on = FALSE;
        LA (inv(FINESSE_OR_NORMAL),  90);
    }
          
}

function CORNER_FIX_MOVE() {
        
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {   right_on = FALSE;
        LA (100,-16);
    }
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    {   right_on = TRUE;
        LA (100, 16);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {   right_on = TRUE; 
        LA (-100,-16); 
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {   right_on = FALSE;
        LA (-100, 16);
    }
}

int ping = 70;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 220;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    CORNER ();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause );
    
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn)            
           
} 
combo FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);     
}

                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}                                        
                                         
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}                        
combo ADVANCED_CROQUETA { 
  set_val(PlayerRun,100); 
  RA_L_R ();      // <-/->
  wait(300);//            
    set_val(PS4_L2,100);     
    set_val(PS4_R2,100);     
  LA_L_R();               
  wait(800);// 800        
}                         
combo CANCELED_TURN_AND_SPIN {  
    RA_UP ();      // up   
    wait(w_rstick);         
    RA_ZERO ();    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
    wait(200);
    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    wait(300);
}           
///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             