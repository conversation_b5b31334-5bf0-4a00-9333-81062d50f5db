// Global Variables
int lsAngle;
int rsAngle;
int relativeAngle;
int rightZoneStart;
int upZoneStart;
int leftZoneStart;
int downZoneStart;

// Function to handle angle wrap-around
function angleInRange(angle, start, end) {
    if (start <= end) {
        if (angle >= start && angle < end) {
            return TRUE;
        }
    } else {
        if (angle >= start || angle < end) {
            return TRUE;
        }
    }
    return FALSE;
}

// Main function for handling right stick skills
function handleRightStickSkills() {
    // Check if right stick is being used
    if(get_val(POLAR_RS, POLAR_RADIUS) > 2000) {
        set_val(TRACE_1, 1);  // Debugging: Right stick is being used
        
        // Get the angle of the left stick
        lsAngle = (360 - get_val(POLAR_LS, POLAR_ANGLE)) % 360;
        
        // Get the angle of the right stick
        rsAngle = (360 - get_val(POLAR_RS, POLAR_ANGLE)) % 360;
        
        // Calculate the relative angle between RS and LS
        relativeAngle = (rsAngle - lsAngle + 360) % 360;
        
        // Calculate zone boundaries relative to left stick angle
        rightZoneStart = lsAngle;
        upZoneStart = (lsAngle + 90) % 360;
        leftZoneStart = (lsAngle + 180) % 360;
        downZoneStart = (lsAngle + 270) % 360;

        // Determine which zone the right stick is in relative to the left stick
        if (angleInRange(rsAngle, rightZoneStart, upZoneStart)) {
            move_right();
            
        } else if (angleInRange(rsAngle, upZoneStart, leftZoneStart)) {
            move_up();
            
        } else if (angleInRange(rsAngle, leftZoneStart, downZoneStart)) {
            move_left();
            
        } else {
            move_down();
            
        }
    } else {
        set_val(TRACE_1, 0);  // Debugging: Right stick is not being used
        set_val(TRACE_2, 0);  // Debugging: No movement
    }
}

function move_down() {
    set_polar(POLAR_RS, (lsAngle + 180) % 360, 32767);
}

function move_up() {
    set_polar(POLAR_RS, lsAngle, 32767);
}

function move_left() {
    set_polar(POLAR_RS, (lsAngle + 270) % 360, 32767);
}

function move_right() {
    set_polar(POLAR_RS, (lsAngle + 90) % 360, 32767);
}



main 
{
set_val(TRACE_1, downZoneStart);  // Debugging: Moving down
set_val(TRACE_2, rightZoneStart);  // Debugging: Moving right
set_val(TRACE_3, upZoneStart);  // Debugging: Moving up
set_val(TRACE_4, leftZoneStart);  // Debugging: Moving left
set_val(TRACE_5, downZoneStart);  // Debugging: Moving down
}