 
    //========================================
    // *** DYNAMIC FINISHING ***
    //========================================
    if(event_release(CrossBtn)){
        after_cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1000;
    }
    
 
    
    if(after_cross_timer){
        after_cross_timer -=get_rtime();
    }
    
      if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && after_cross_timer <= 0 ){
            vm_tctrl(0);
         set_val(ShotBtn,0);
         Dynamic_Aim();     
            
                  if( after_sprint_timer > 225  ) {
            UltimatePower = 182.7 + ( 0.0323*(after_sprint_timer) ) ; // since the ms the sprint is released >> 190 up to 215 ms shooting power generated. 
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Dynamic_Shoot); 
            }
            
               
            
                            if( after_sprint_timer <= 0  ) {
            UltimatePower = 230 ;
            DrivenShot = 15 ;
            drval=100;
            combo_restart(Dynamic_Shoot); 
        
            }
            
        }
    } 
    
      if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  /// FakeShot Support avoid Conflictions
    combo_stop(Dynamic_Shoot);
    }
  

                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int after_cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;


 combo Dynamic_Shoot {
    set_val(ShotBtn, 100);
    Dynamic_Aim();
    wait(UltimatePower);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    Dynamic_Aim();
    wait(DrivenShot);  
    set_val(ShotBtn, 0);
    Dynamic_Aim();
    wait(150);
    set_val(ShotBtn, 0);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    Dynamic_Aim();
    wait(50);
    set_val(ShotBtn, 0);
    set_val(PS4_R3,0);
    set_val(SprintBtn,0);
    set_val(PaceCtrol,100);
    Dynamic_Aim() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
} 
  
  
  
 function Dynamic_Aim() { 
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 }