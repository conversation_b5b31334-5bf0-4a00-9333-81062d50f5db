// Constants for better readability and maintainability
define MAX_BUTTON_VALUE = 100;
define MAX_POLAR_VALUE = 31000;
define FULL_ROTATION = 360;

int LS_Angle;

main {
    // Set button values
    set_val(XB1_LB, MAX_BUTTON_VALUE);
    set_val(XB1_LT, MAX_BUTTON_VALUE);

    // Calculate and set LS_Angle
    LS_Angle = (FULL_ROTATION - get_polar(POLAR_LS, POLAR_ANGLE)) % FULL_ROTATION;

    // Set polar coordinates for left stick
    set_polar(POLAR_LS, LS_Angle, MAX_POLAR_VALUE);

    // Set trace value
    set_val(TRACE_1, LS_Angle);
}