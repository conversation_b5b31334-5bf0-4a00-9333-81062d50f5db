// Elliptical boundary for analog stick
// Creates smooth elliptical movement with configurable axes

// Constants for stick identification
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Ellipse axis ratios (percentage of maximum)
define X_RATIO = 100;    // X-axis scale (100 = full range)
define Y_RATIO = 10;     // Y-axis scale (60% of full range)

// Rotation settings
define ANGLE_STEP = 8;   // Angle increment per frame
define BASE_RADIUS = 30000;  // Base radius for rotation

int x, y;
int current_angle;       // Track rotation angle

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Check if both triggers are pressed
    if (get_val(XB1_LT) && get_val(XB1_RT)) {
        // Update rotation angle
        current_angle = (current_angle + ANGLE_STEP) % 360;
        
        // Calculate rotated position on ellipse using set_polar
        set_polar(POLAR_LS, current_angle, BASE_RADIUS);
        
        // Get the polar coordinates and apply elliptical scaling
        x = get_val(stickX) * X_RATIO / 100;
        y = get_val(stickY) * Y_RATIO / 100;
        
        // Set modified values
        set_val(stickX, x);
        set_val(stickY, y);
    } else if (abs(x) > 0 || abs(y) > 0) {
        // Original elliptical scaling when triggers not pressed
        x = (x * X_RATIO) / 100;
        y = (y * Y_RATIO) / 100;
        
        set_val(stickX, x);
        set_val(stickY, y);
    }
}