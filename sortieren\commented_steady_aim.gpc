// Custom implementation of cosine function for fixed-point arithmetic
fix32 _COS_(fix32 a) { /*^ cos fix */
  // Normalize the angle to the range [0, 2π]
  a = mod(a + 6.28319, 6.28319);
  // Handle negative angles
  if (a < 0.0) a = mod(a += 6.28319, 6.28319);
  // For angles between π/4 and 5π/4, use the relation cos(x) = sin(π/2 - x)
  if (a > 0.785522 && a < 3.92761) return sin(1.5708 - a);
  // For other angles, use the standard cosine function
  return cos(a);
}

// Custom implementation of sine function for fixed-point arithmetic
fix32 _SIN_(fix32 a) { /*^ sin fix */
  // Normalize the angle to the range [0, 2π]
  a = mod(a + 6.28319, 6.28319);
  // Handle negative angles
  if (a < 0.0) a = mod(a += 6.28319, 6.28319);
  // For angles between π/4 and 5π/4, use the relation sin(x) = cos(π/2 - x)
  if (a > 0.785522 && a < 3.92761) return cos(1.5708 - a);
  // For other angles, use the standard sine function
  return sin(a);
}

// Replace the standard trigonometric functions with our custom implementations
#define sin _SIN_
#define cos _COS_
 
// Define the steady aim angle (in degrees)
#define STEADY_AIM 45.0 

// Function to process left stick input
void processLeftStick(fix32 stickX, fix32 stickY, fix32 *newX, fix32 *newY) {
    // Calculate the polar radius (distance from origin) of the stick input
    fix32 polarRadius = sqrt(stickX * stickX + stickY * stickY);
    fix32 targetRadius;
    fix32 randomVariation;
    
    // If the stick input is within the deadzone, set output to 0
    if (polarRadius < DEADZONE) {
        *newX = 0.0;
        *newY = 0.0;
    }
    else {
        // Calculate the angle of the stick input
        fix32 angle = atan2(stickY, stickX);
        // Normalize the radius to account for the deadzone
        fix32 normalizedRadius = (polarRadius - DEADZONE) / (FULL_RADIUS - DEADZONE);
        
        // Check if LB button is pressed (aiming mode)
        if (get_val(XB1_LB)) {  // XB1_LB
            // Generate random variation for aiming
            randomVariation = 3.0 + (rand() * 9.0);
            targetRadius = BASE_RADIUS + (rand() > 0.5 ? randomVariation : -randomVariation);
            targetRadius = clamp(targetRadius, MIN_RADIUS, MAX_RADIUS);
            
            // If RT is also pressed, set it to full value (likely for firing)
            if (get_val(XB1_RT)) {  // XB1_RT
                set_val(XB1_RT, 100.0);
            }
        }
        // If only RT is pressed, use full radius (likely for running)
        else if (get_val(XB1_RT)) {  // XB1_RT
            targetRadius = FULL_RADIUS;
        }
        // Normal movement
        else {
            randomVariation = 2.0 + (rand() * 12.0);
            targetRadius = FULL_RADIUS - randomVariation;
        }
        
        // Apply a quadratic curve to make small movements more precise
        normalizedRadius = normalizedRadius * normalizedRadius;
        
        // Calculate new stick position
        fix32 newRadius = normalizedRadius * targetRadius;
        *newX = cos(angle) * newRadius;
        *newY = sin(angle) * newRadius;
        
        // Ensure the new values are within the valid range
        *newX = clamp(*newX, -FULL_RADIUS, FULL_RADIUS);
        *newY = clamp(*newY, -FULL_RADIUS, FULL_RADIUS);
    }
}

void process_stick() {
    // Get stick input values
    g_x = get_val(STICK_2_X);
    g_y = get_val(STICK_2_Y);

    // Calculate polar coordinates
    g_radius = sqrt(g_x * g_x + g_y * g_y);
    g_angle = rad2deg(atan2(g_y, g_x));

    // Apply different processing based on the stick radius
    if (g_radius < RADIUS_THRESHOLD_1) {
        // Deactivate XB1_LB and activate XB1_LT for very small movements
        set_val(XB1_LB, 100);
        set_val(XB1_LT, 100);
        g_angle = 0.0;
        g_radius = 0.0;
    } else if (g_radius >= RADIUS_THRESHOLD_1 && g_radius < RADIUS_THRESHOLD_2) {
        // Activate XB1_LB for medium movements
        set_val(XB1_LB, 100);
        // Quantize angle for more precise control
        g_angle = round(g_angle / ANGLE_INTERVAL) * ANGLE_INTERVAL;
        // Limit the radius to MAX_RADIUS
        g_radius = min(g_radius, MAX_RADIUS);
    } else {
        // Deactivate XB1_LB for large movements
        set_val(XB1_LB, 0);
        // Quantize angle with a different interval for large movements
        g_angle = round(g_angle / ANGLE_INTERVAL_2) * ANGLE_INTERVAL_2;
        // Limit the radius to MAX_RADIUS
        g_radius = min(g_radius, MAX_RADIUS);
    }

    // Convert back to Cartesian coordinates
    fix32 rad = deg2rad(g_angle);
    g_x = g_radius * cos(rad);
    g_y = g_radius * sin(rad);

    // Update stick output
    set_val(STICK_2_X, g_x);
    set_val(STICK_2_Y, g_y);
}

main {
  // Calculate the adjusted angle based on the stick input and STEADY_AIM value
  fix32 a = deg2rad(lerp(0.0, 360.0, (round(((rad2deg(mod(atan2(get_actual(STICK_1_Y), get_actual(STICK_1_X)) + 6.28319, 6.28319)) * (360.0 / (fix32)STEADY_AIM)) / 360.0))) / (360.0 / (fix32)STEADY_AIM)));
  
  // Set the magnitude to the maximum value (100.0) for maximum radius
  fix32 m = 100.0;
  
  // Set the X-axis value of the stick to the maximum radius
  set_val(STICK_1_X, cos(a) * m);
  
  // Set the Y-axis value of the stick to the maximum radius
  set_val(STICK_1_Y, sin(a) * m);
}