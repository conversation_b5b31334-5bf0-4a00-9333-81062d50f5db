
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = XB1_LT;     // Pace Control
define FinesseShot   = PS4_L1;     // Finesse Shot
define PlayerRun     = PS4_R1;     // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2;     // Sprint Btn 
define PassBtn       = PS4_CROSS;  // Pass Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
                                                                 
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL          =40;  
define CANCEL_SHOOT_SKILL             =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int Temp; 
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
int ping = 0 ;                                               
int timer;
int print;
int hold_RB_LB;
int onoff_penalty;
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                
	///////////////////////////////////// 
	// LONG TIMED FINESSE FINISH                
//	if(get_val(XB1_PR2)){ 
//		timer += get_rtime();  
//	}
	/// - will start combo if you hold btn for more than 250 ms
//	if(get_val(XB1_PR2) && get_ptime(XB1_PR2)> 250 ){ 
//		combo_run(Timed_Finesse_Finish_Long);
//	}
	///////////////////////////////////// 
	// LONG TIMED FINESSE FINISH                
//	if(event_release(XB1_PR2) ){
		//--- will start combo if you tap btn 9hold it below 250 ms )
//		if(timer < 250) combo_run(Timed_Finesse_Finish_Before_Penalty); 
//		timer = 0;
//	}
	 
	 	 // THREADED THROUGH PASS
	if(!get_val(XB1_A)) {
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_Y,100);
		}
	}
	 
	set_val(XB1_PR2,0); 
    
    if(event_press(XB1_PR1)) combo_run(FULL_POWER_cmb);
    if(event_press(XB1_PL1)) combo_run(CHIP_SHOT);
    if(event_press(XB1_PL2)) combo_run(PlayerStop);
    
    //  turn ON Penalty  hold  L1 and press OPTIONS
	if(get_val(PS4_L1)){
		if(event_press(PS4_OPTIONS)){
			onoff_penalty = !onoff_penalty;
		}
		set_val(PS4_OPTIONS,0);
    }
    if(onoff_penalty){

		if (get_val(XB1_X))	    {set_val(PS4_LX,0);set_val(PS4_LY,0);}		// Middle 
		if (get_val(PS4_LEFT))	{set_val(PS4_LX,-78);set_val(PS4_LY,-38);} 	// Left Up 
		if (get_val(PS4_RIGHT))	{set_val(PS4_LX,78);set_val(PS4_LY,-38);} 	// Right Up 
		if (get_val(PS4_UP))	{set_val(PS4_LX,-54);set_val(PS4_LY, 90);} 	// Left Down 
		if (get_val(PS4_DOWN))	{set_val(PS4_LX,54);set_val(PS4_LY,90);} 	// Right Down 
   }
      
    //========================================== 
    // SPRINT FASTER                             
    if(event_press(SprintBtn) && !tap ) combo_run(ONE_TAP); 
                                                 
    if(event_press(SprintBtn) &&  tap ){     
    	combo_run(SPRINT_cmb);       
    }                  
         
    //--- Use Open Up MOD                                                         
    if(event_press(ShotBtn) || event_press(PassBtn) || event_press(FinesseShot)){  
        combo_run(OPEN_UP_cmb);                                                   
    }                                                                            
                                                                                 
                                                          
    if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
		LX = get_val(MOVE_X);                                      
		LY = get_val(MOVE_Y);                                      
		calc_zone ();                                              
    }                                                           
    //----------------------------------------------------------- 
                                      
   if( !get_val(XB1_LT)&& !get_val(XB1_RS) &&  !get_val(PaceCtrol) && !get_val(SprintBtn)) { // all Skills mode 
                                                     
	      //  Right Stick -->  UP                          
	      if( get_val(PS4_RY) < -70  && !flick_up ) {   
	      		flick_up = TRUE;                          
	      		if(get_val(PS4_LX) < -20) right_on = TRUE; 
	      		else right_on = FALSE; 
	      		ACTIVE = DRIBBLING_SKILL ; start = TRUE; combo_run(DRIBBLING_SKILL_cmb);
	      }                                              
	      //  Right Stick -->  DOWN                               
	      if( get_val(PS4_RY) >  70  && !flick_d ) {     
	      		flick_d = TRUE;                            
	      		right_on = TRUE; 
	      		if(get_val(PS4_LX) >  20) right_on = TRUE; 
	      		else right_on = FALSE; 
	      		ACTIVE = DRIBBLING_SKILL ; start = TRUE; combo_run(DRIBBLING_SKILL_cmb);
	      }                                               
                                                        
	                                   
                                                        
	                                     
                                                          
        if(abs(get_val(PS4_RY))<20  && abs(get_val(PS4_RX))<20){  
	     		  flick_up = 0;                                 
	     		  flick_d  = 0;                                 
	     		  flick_l  = 0;                                 
	     		  flick_r  = 0;                                 
        }                                              
        set_val(SKILL_STICK_X,0); 
        set_val(SKILL_STICK_Y,0); 
    }// end of ALWAYS ON  
      if(start){                         
      		sensitivity(PS4_LX,NOT_USE,90); 
      		sensitivity(PS4_LY,NOT_USE,90); 
      }                                  
                                                
      if(ACTIVE == BOLASIE_FLICK_SKILL && combo_running(TURN_AND_SPIN)) set_val(FinesseShot,100);// + R1
    // all Skills mode                
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int start;
combo CANCEL_SHOOT {
     set_val(ShotBtn,100);
     wait(290);
     set_val(PS4_L2,100);
     set_val(PS4_R2,100);
     wait(300);
} 

combo DRIBBLING_SKILL_cmb {               
    set_val(FinesseShot,100); 
    wait(20);
    set_val(FinesseShot,100);       
    LA_L_R();
    wait(375);
    wait(20);
    set_val(FinesseShot, 0);
    set_val(SprintBtn,100);  
    wait(800);
    start = FALSE;
}           
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo CHIP_SHOT {
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    set_val(PS4_L3,100);
    wait( 80);
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    wait(90);
}
combo FULL_POWER_cmb {
	set_val(XB1_LB,100);
    set_val(ShotBtn,100);
    set_val(PS4_L3 ,100);
    wait(80);
    set_val(ShotBtn,100);
    wait(150);
}
combo ROTATE_L {
      set_val(PS4_LY,0 ); 
      set_val(PS4_LX,   -60 ); 
      wait(120);

      set_val(PS4_LY,   60); 
      set_val(PS4_LX,  -60); 
      wait(120);

      set_val(PS4_LY,  60); 
      set_val(PS4_LX,   0); 
      wait(120);

      set_val(PS4_LY,   60); 
      set_val(PS4_LX, 60); 
      wait(120);


      set_val(PS4_LY,0 ); 
      set_val(PS4_LX,  60 ); 
      wait(120);

      set_val(PS4_LY,   -60); 
      set_val(PS4_LX,  60); 
      wait(120);

      set_val(PS4_LY,  -60); 
      set_val(PS4_LX,   0);
      wait(120);

      set_val(PS4_LY,   -60); 
      set_val(PS4_LX, -60); 
      wait(120);

      }

combo ROTATE_R {

      set_val(PS4_LY,0 ); 
      set_val(PS4_LX,   60 ); 
      wait(120);

      set_val(PS4_LY,  60); 
      set_val(PS4_LX,  60); 
      wait(120);

      set_val(PS4_LY,  60); 
      set_val(PS4_LX,   0); 
      wait(120);

      set_val(PS4_LY,   60); 
      set_val(PS4_LX, -60); 
      wait(120);

      set_val(PS4_LY,0 ); 
      set_val(PS4_LX,  -60 ); 
      wait(120);

      set_val(PS4_LY,   -60); 
      set_val(PS4_LX,  -60); 
      wait(120);

      set_val(PS4_LY,  -60); 
      set_val(PS4_LX,   0);
      wait(120);

      set_val(PS4_LY,   -60); 
      set_val(PS4_LX, 60); 
      wait(120);

      }

combo PlayerStop {
    set_val(XB1_LB, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 100);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 100);
    set_val(XB1_X, 0);
    wait(50);
    set_val(XB1_LB, 100);
    set_val(XB1_A, 0);
    wait(50);
    set_val(XB1_LB, 0);
    hold_RB_LB = TRUE;
}
combo SPRINT_cmb { 
	RA_UP();   
	wait(40);  
	wait(40);  
	RA_UP();   
	wait(40);  
	wait(40);  
}           
combo OPEN_UP_cmb {      
    set_val(PS4_L3,100); 
    wait(100);           
}    

combo LATERAL_HEELtoHEEL {  
    set_val(PlayerRun,100);
    RA_OPP () ;            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_ZERO ();            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_L_R () ;            
    wait(60);//           
    wait(300);            
}
//------------------------------------- 

combo FAKE_SHOT {        
	set_val(ShotBtn,100); 

	wait(40); 
	set_val(ShotBtn,100);
	set_val(PassBtn,100); 
	wait(60);

	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
}     


combo Timed_Finesse_Finish_Before_Penalty {
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    combo_run(LATERAL_HEELtoHEEL)
    wait(160)
    
    set_val(PlayerRun,100); // this button combined with CORNER() will give a little more accurate 
                              //control for the player to force face the opponent goal 
    CORNER ();
    wait(200)
    
    CORNER ();             // Releasing PlayerRun button and force to aim slightly vertically 
    wait(200) 
    // time separation needed before shot
    
    set_val(FinesseShot, 100); // initiate FinesseSHOT
    CORNER ();
    set_val(ShotBtn, 100);             
    wait(220); 
    set_val(FinesseShot, 100);
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(140+ping); // Ping is variable set to 0 for squad battle , still need more testing in online to entre a suitable value to it .
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(500)            
           
}             
combo Timed_Finesse_Finish_Long { 
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    combo_run(LATERAL_HEELtoHEEL)
    wait(160)
    set_val(PlayerRun,100); // this button combined with CORNER() will give a little more accurate 
                            //control for the player to force face the opponent goal 
    CORNER ();
    wait(400);
    // time separation needed before shot
    
    set_val(FinesseShot, 100);// initiate FinesseSHOT
    
    CORNER (); 
    set_val(ShotBtn, 100);             
    wait(240); 
    set_val(FinesseShot, 100);
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(120+ping); // Ping is variable set to 0 for squad battle , still need more testing in online to entre a suitable value to it .
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(500)            
        
}   

  function CORNER_FIX_MOVE() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
		set_val(PS4_LX,  100);
		set_val(PS4_LY,  -16);
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
		set_val(PS4_LX, 100); 
		set_val(PS4_LY,  16);
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 
		set_val(PS4_LX, -100); 
		set_val(PS4_LY,  -16);
	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE
	set_val(PS4_LX, -100);
	set_val(PS4_LY,  16);
		  
		 
	}
	}

 
function CORNER() { 
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	
	{ right_on = FALSE
		set_val(PS4_LY,  -100); 
		set_val(PS4_LX,  16); 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	
	{ 
	right_on = TRUE
		set_val(PS4_LY, 100); 
		set_val(PS4_LX, 16); 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	
	{ right_on = TRUE
		set_val(PS4_LX,  -16); 
		set_val(PS4_LY, -100); 
	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{right_on = FALSE
		set_val(PS4_LX, -16);  
		set_val(PS4_LY, 100); 
	}

}

   
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}                                        
                                         
//////////////////////////////////////////////////////   
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin 
combo TURN_AND_SPIN {  
  if(ACTIVE == BALL_ROLL_FAKE_TURN ) hold_btn = 200;//  Ball Roll Fake Turn L2 
	else hold_btn = 1;      
 wait(hold_btn);
	RA_UP ();      // up   
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);          
	RA_L_R () ;    // Left or Right 
	wait(w_rstick);    
}                   

///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             