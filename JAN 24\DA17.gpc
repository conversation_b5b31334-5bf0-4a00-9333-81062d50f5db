/*

░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─
░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄
░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
*/

/*| This Script was made and intended for Dark-Angel vip discord members    .                       |
| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            |
| All rights reserved. This material may not be reproduced, displayed,                              |
| modified or distributed without the express prior written permission of the                       |
| copyright holder.

// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.

/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
- тαуℓσ∂ιƒт21
- Jblaze122
- DoGzTheFiGhTeR
- .Me
- Swizzy
- Fadexz
Your contributions have been invaluable, and I am truly grateful for your support."
*/










































































int DA24216[0]; init { 	DA24115(); 	combo_run(DA241); 	combo_run(DA242); 	combo_run(DA243); 	combo_run(DA244); 	combo_run(DA245); 	combo_run(DA246); 	combo_run(DA247); 	combo_run(DA248); 	combo_run(DA249); 	combo_run(DA2410); 	combo_run(DA2411); 	combo_run(DA2412); 	combo_run(DA2413); 	combo_run(DA2414); 	combo_run(DA2415); 	combo_run(DA2416); 	combo_run(DA2417); 	combo_run(DA2418); 	combo_run(DA2419); 	combo_run(DA2420); 	combo_run(DA2421); 	combo_run(DA2422); 	combo_run(DA2423); 	combo_run(DA2424); 	combo_run(DA2425); 	combo_run(DA2426); 	combo_run(DA2427); 	combo_run(DA2428); 	combo_run(DA2429); 	combo_run(DA2430); 	combo_run(DA2431); 	combo_run(DA2432); 	combo_run(DA2433); 	combo_run(DA2434); 	combo_run(DA2435); 	combo_run(DA2436); 	combo_run(DA2437); 	combo_run(DA2438); 	combo_run(DA2439); 	combo_run(DA2440); 	combo_run(DA2441); 	combo_run(DA2442); 	combo_run(DA2443); 	combo_run(DA2444); 	combo_run(DA2445); 	combo_run(DA2446); 	combo_run(DA2447); 	combo_run(DA2448); 	combo_run(DA2449); 	combo_run(DA2450); 	combo_run(DA2451); 	combo_run(DA2452); 	combo_run(DA2453); 	combo_run(DA2454); 	combo_run(DA2455); 	combo_run(DA2456); 	combo_run(DA2457); 	combo_run(DA2458); 	combo_run(DA2459); 	combo_run(DA2460); 	combo_run(DA2461); 	combo_run(DA2462); 	combo_run(DA2463); 	combo_run(DA2464); 	combo_run(DA2465); 	combo_run(DA2466); 	combo_run(DA2467); 	combo_run(DA2468); 	combo_run(DA2469); 	combo_run(DA2470); 	combo_stop(DA241); 	combo_stop(DA242); 	combo_stop(DA243); 	combo_stop(DA244); 	combo_stop(DA245); 	combo_stop(DA246); 	combo_stop(DA247); 	combo_stop(DA248); 	combo_stop(DA249); 	combo_stop(DA2410); 	combo_stop(DA2411); 	combo_stop(DA2412); 	combo_stop(DA2413); 	combo_stop(DA2414); 	combo_stop(DA2415); 	combo_stop(DA2416); 	combo_stop(DA2417); 	combo_stop(DA2418); 	combo_stop(DA2419); 	combo_stop(DA2420); 	combo_stop(DA2421); 	combo_stop(DA2422); 	combo_stop(DA2423); 	combo_stop(DA2424); 	combo_stop(DA2425); 	combo_stop(DA2426); 	combo_stop(DA2427); 	combo_stop(DA2428); 	combo_stop(DA2429); 	combo_stop(DA2430); 	combo_stop(DA2431); 	combo_stop(DA2432); 	combo_stop(DA2433); 	combo_stop(DA2434); 	combo_stop(DA2435); 	combo_stop(DA2436); 	combo_stop(DA2437); 	combo_stop(DA2438); 	combo_stop(DA2439); 	combo_stop(DA2440); 	combo_stop(DA2441); 	combo_stop(DA2442); 	combo_stop(DA2443); 	combo_stop(DA2444); 	combo_stop(DA2445); 	combo_stop(DA2446); 	combo_stop(DA2447); 	combo_stop(DA2448); 	combo_stop(DA2449); 	combo_stop(DA2450); 	combo_stop(DA2451); 	combo_stop(DA2452); 	combo_stop(DA2453); 	combo_stop(DA2454); 	combo_stop(DA2455); 	combo_stop(DA2456); 	combo_stop(DA2457); 	combo_stop(DA2458); 	combo_stop(DA2459); 	combo_stop(DA2460); 	combo_stop(DA2461); 	combo_stop(DA2462); 	combo_stop(DA2463); 	combo_stop(DA2464); 	combo_stop(DA2465); 	combo_stop(DA2466); 	combo_stop(DA2467); 	combo_stop(DA2468); 	combo_stop(DA2469); 	combo_stop(DA2470); 	combo_run(DA24108); } int DA24276 ; int DA24277; int DA24278; int DA24279; int DA24280; define DA24281 = 0; define DA24282 = 1; define DA24283 = 2; define DA24284 = 3; define DA24285 = 4; define DA24286 = 5; define DA24287 = 6; define DA24288 = 7; define DA24289 = 8; define DA24290 = 9; define DA24291 = 10; define DA24292 = 11; define DA24293 = 12; define DA24294 = 13; define DA24295 = 14; define DA24296 = 15; define DA24297 = 16; define DA24298 = 17; define DA24299 = 18; define DA24300 = 19; define DA24301 = 20; define DA24302 = 21; define DA24303 = 22; define DA2423 = 23; define DA24305 = 24; define DA24306 = 25; define DA24307 = 26; define DA24308 = 27; define DA24309 = 28; define DA24310 = 29; define DA24311 = 30; define DA24312 = 31; define DA24313 = 32; define DA24314 = 33; define DA24315 = 34; define DA24316 = 35; define DA24317 = 36; define DA24318 = 37; define DA24319 = 38; define DA24320 = 39; define DA24321 = 40; define DA24322 = 41; define DA24323 = 42; define DA24324 = 43; define DA24325 = 44; define DA24326 = 45; define DA24327 = 46; define DA24328 = 47; define DA24329 = 48; define DA24330 = 49; define DA24331 = 50; define DA24332 = 51; define DA24333 = 52; define DA24334 = 53; define DA24335 = 54; define DA24336 = 55; define DA24337 = 56; define DA24338 = 57; define DA24339 = 58; define DA24340 = 59; define DA24341 = 60; define DA24342 = 61; define DA24343 = 62; define DA24344 = 63; define DA24345 = 64; define DA24346 = 65; define DA24347 = 66; define DA24348 = 67; define DA24349 = 68; define DA24350 = 69; define DA24351 = 70; define DA24352 = 0; function DA24109(DA24110) { 	if (DA24110 == 0) vm_tctrl(-0); 	else if (DA24110 == 1) vm_tctrl(2); 	else if (DA24110 == 2) vm_tctrl(-2); 	else if (DA24110 == 3) vm_tctrl(-4); 	else if (DA24110 == 4) vm_tctrl(-6); 	else if (DA24110 == 5) vm_tctrl(-8); 	else if (DA24110 == 6) vm_tctrl(-9); } int DA24353, DA24354; int DA24355, DA24356; int DA24357 = FALSE, DA24358; int DA24359 = TRUE; int DA24360; const string DA24827[] = { 	"Off",  "On" } ; int DA24361; int DA24362; int DA24363; int DA24364; int DA24365; int DA24366; int DA24367; int DA24368; int DA24369; int DA24370; int DA24371; int DA24372; int DA24373; int DA24374; int DA24375; int DA24376; int DA24377; int DA24378; int DA24379; int DA24380; int DA24110; int DA24382; int DA24383 ; int DA24384 ; int DA24385 ; define DA24386 = 24; int DA24387; int DA24388; int DA24389; int DA24390; int DA24391; int DA24392; int DA24393; int DA24394; int DA24395; int DA24396 ; int DA24397 ; int DA24398 ; int DA24399 ; int DA24400 ; int DA24401 ; int DA24402 ; int DA24403 ; int DA24404 ; int DA24405 ; int DA24406 ; int DA24407; int DA24408; int DA24409; int DA24410; int DA24411; int DA24412; int DA24413; int DA24414; int DA24415; int DA24416; int DA24417; int DA24418; int DA24419; int DA24420; int DA24421; int DA24422; int DA24423; int DA24424; int DA24425; int DA24426; int DA24427; int DA24428; int DA24429; int DA24430; int DA24431; int DA24432; int DA24433; int DA24434; int DA24435; int DA24436; int DA24437; int DA24438; int DA24439; int DA24440 ; int DA24441 ; int DA24442 ; int DA24443; int DA24444 ; int DA24445 ; int DA24446 ; int DA24447; int DA24448 ; int DA24449 ; int DA24450 ; int DA24451; int DA24452 ; int DA24453 ; int DA24454 ; int DA24455; int DA24456; int DA24457; int DA24458; int DA24459; int DA24460; const int16 DA24833[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		0,2500,25,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 DA24581[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 DA24805[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1,   	    1,  	1,  	1   } ; const string DA24591[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string DA24590[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","Double Tap GrounP","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string DA24809 [] = { 	"Classic","Alternative","Custom", ""  } ; const string DA24908 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string DA24825[] = { 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" } ; const string DA24811[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string DA24878[] = { 	"Right",  "Left",  "" } ; const string DA24876[] = { 	"One Tap",  "Double Tap",  "" } ; const string DA24815[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string DA24817[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"Feint && Exit",  	"Feint & Exit",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Ball Roll Chop",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel to Ball",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"Spin Move L/R",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Drag Back Som.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string DA24852[] = { 	"OFF",  "PS5_PS",  "PS5_SHARE",  "PS5_OPTIONS",  "PS5_R1",  "PS5_R2",  "PS5_R3",  "PS5_L1",  "PS5_L2",  "PS5_L3",  "PS5_UP",  "PS5_DOWN",  "PS5_LEFT",  "PS5_RIGHT",  "PS5_TRIANGLE",  "PS5_CIRCLE",  "PS5_CROSS",  "PS5_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS5_TOUCH",  "" } ; int DA24461 = -1; int DA24462 = -1; int DA24463 = -1; int DA24464 = -1; int DA24465 = -1; int DA24466; int DA24467; int DA24468; int DA24469; int DA24470; const uint8 DA241358[] = { 	4,4,4, 4,4,4, 4,4,4,4 } ; const uint8 DA241359[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 DA241360[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 DA241361[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 DA241362[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 DA241363[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA241364[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA241365[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21 } ; const uint8 DA241366[] = { 	4,4,4, 4,4,4, 4,4,4,4 } ; const uint8 DA241367[] = { 	9, 42, 41, 43, 9, 4, 9, 34, 70, 41, 33,29 } ; const uint8 DA241368[] = { 	7, 10, 7, 41, 10, 70, 41, 42, 43, 7, 33,29  } ; const uint8 DA241369[] = { 	41, 9, 41, 9, 41, 42, 43, 41, 42, 41, 33,29  } ; const uint8 DA241370[] = { 	7, 41, 10, 7, 10, 43, 41, 7, 41, 7, 33 ,29 } ; const uint8 DA241371[] = { 	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29 } ; const uint8 DA241372[] = { 	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29 } ; const uint8 DA241373[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function DA24111(DA24112) { 	if (DA24112 >= 9) { 		DA24471 = -1; 			} 	else if (DA24112 <= 0) { 		DA24471 = 1; 			} 	DA24112 += DA24471; 	return DA24112; 	} function DA24113() { 	vm_tctrl(0); 	if(DA2429 && DA24364){ 		if(DA24557 < 1000){ 			DA24473 = 10; 			DA24500   = 10; 			DA24498  = 10; 					} 			} 	if(DA24476 && DA24365){ 		DA24474 = FALSE; 		if(DA24557 < 1000){ 			DA24473 = 11; 			DA24500   = 11; 			DA24498  = 11; 					} 			} 	if(DA24474 && DA24365){ 		DA24476 = FALSE; 		if(DA24557 < 1000){ 			DA24473 = 10; 			DA24500   = 10; 			DA24498  = 10; 					} 			} 			       if(DA24557 >= 1000){     DA24478 = DA24111(DA24478);     DA24497 = DA24111(DA24497);     DA24498 = DA24111(DA24498);     DA24473 = DA24111(DA24473);     DA24500 = DA24111(DA24500);         while (DA24466 == DA24478 || DA24467 == DA24497 || DA24468 == DA24498 ||            DA24469 == DA24473 || DA24470 == DA24500) {         DA24478 = DA24111(DA24478);         DA24497 = DA24111(DA24497);         DA24498 = DA24111(DA24498);         DA24473 = DA24111(DA24473);         DA24500 = DA24111(DA24500);     }     } 	if(DA24364){ 		if(DA24503 == DA24597){ 			DA24480 = !DA24480; 			if(DA241358[DA24478]) DA24241(DA241358[DA24478]); 					} 		if(DA24503 == DA24246 (DA24597 + 4)){ 			DA24480 = FALSE; 			if(DA241365[DA24497]) DA24241(DA241365[DA24497]); 					} 		if(DA24503 == DA24246 (DA24597 + 1) ){ 			DA24480 = TRUE; 			if(DA241360[DA24473]) DA24241(DA241360[DA24473]); 					} 		if(DA24503 == DA24246 (DA24597 - 1) ){ 			DA24480 = FALSE; 			if(DA241359[DA24473]) DA24241(DA241359[DA24473]); 					} 		if(DA24503 == DA24246 (DA24597 + 2) ){ 			DA24480 = TRUE; 			if(DA241362[DA24500]) DA24241(DA241362[DA24500]); 					} 		if(DA24503 == DA24246 (DA24597 - 2) ){ 			DA24480 = FALSE; 			if(DA241361[DA24500]) DA24241(DA241361[DA24500]); 					} 		if(DA24503 == DA24246 (DA24597 + 3) ){ 			DA24480 = TRUE; 			if(DA241363[DA24498]) DA24241(DA241363[DA24498]); 					} 		if(DA24503 == DA24246 (DA24597 - 3) ){ 			DA24480 = FALSE; 			if(DA241364[DA24498]) DA24241(DA241364[DA24498]); 					} 			} 	if(DA24365){ 		if(DA24503 == DA24597){ 			DA24480 = !DA24480; 			if(DA241366[DA24478]) DA24241(DA241366[DA24478]); 					} 		if(DA24503 == DA24246 (DA24597 + 4)){ 			DA24480 = FALSE; 			if(DA241373[DA24497]) DA24241(DA241373[DA24497]); 					} 		if(DA24503 == DA24246 (DA24597 + 1) ){ 			DA24480 = TRUE; 			if(DA241368[DA24473]) DA24241(DA241368[DA24473]); 					} 		if(DA24503 == DA24246 (DA24597 - 1) ){ 			DA24480 = FALSE; 			if(DA241367[DA24473]) DA24241(DA241367[DA24473]); 					} 		if(DA24503 == DA24246 (DA24597 + 2) ){ 			DA24480 = TRUE; 			if(DA241370[DA24500]) DA24241(DA241370[DA24500]); 					} 		if(DA24503 == DA24246 (DA24597 - 2) ){ 			DA24480 = FALSE; 			if(DA241369[DA24500]) DA24241(DA241369[DA24500]); 					} 		if(DA24503 == DA24246 (DA24597 + 3) ){ 			DA24480 = TRUE; 			if(DA241371[DA24498]) DA24241(DA241371[DA24498]); 					} 		if(DA24503 == DA24246 (DA24597 - 3) ){ 			DA24480 = FALSE; 			if(DA241372[DA24498]) DA24241(DA241372[DA24498]); 					} 			} 			DA24466 = DA24478; DA24467 = DA24497; DA24468 = DA24498; DA24469 = DA24473; DA24470 = DA24500; } int DA24478; int DA24497; int DA24498; int DA24473; int DA24500; function DA24114() { 	if(DA241153){ 		DA24501 += get_rtime(); 			} 	if(DA24501 >= 3000){ 		DA24501 = 0; 		DA241153 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA24452) && !get_ival(DA24453) && !get_ival(DA24451) && !get_ival(DA24450)) { 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA24504 && !combo_running(DA240)) { 			DA24503 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA24504 = TRUE; 			DA241153 = TRUE; 			DA24501 = 0; 			vm_tctrl(0); 			DA24113(); 					} 		set_val(DA241138, 0); 		set_val(DA241139, 0); 			} 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) { 		DA24504 = FALSE; 			} 	} function DA24115() { 	DA24186(); 	if (DA24387 == 0 && DA24388 == 0 && DA24389 == 0 && DA24390 == 0 && DA24391 == 0 && DA24392 == 0 && DA24393 == 0 && DA24394 == 0) { 		DA24387 = 4; 		DA24388 = 41; 		DA24389 = 41; 		DA24390 = 42; 		DA24391 = 42; 		DA24392 = 29; 		DA24393 = 29; 		DA24394 = 48; 			} 	DA24947 = get_slot(); 	} int DA24471 = 1; int DA24508; int DA24509; int DA24510 = TRUE; int DA24511[6]; int DA24512; int DA24513; int DA24514; int DA24515; function DA24116(DA24117, DA24118, DA24119) { 	DA24119 = (DA24119 * 14142) / 46340; 	if (DA24118 <= 0) { 		set_polar2(DA24117, (DA24118 = (abs(DA24118) + 360) % 360), min(DA24119, DA24519[DA24118 % 90])); 		return; 			} 	set_polar2(DA24117, inv(DA24118 % 360), min(DA24119, DA24519[DA24118 % 90])); 	} function DA24120(DA24117, DA24122) { 	if (DA24122) return (360 - get_polar(DA24117, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_val(42 + DA24117), 2) + pow(get_val(43 + DA24117), 2))) + 1; 	} function DA24123(DA24117,DA24122) { 	if (DA24122) return (360 - get_ipolar(DA24117, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + DA24117), 2) + pow(get_ival(43 + DA24117), 2))) + 1; 	} const int16 DA24519[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int DA24523 = 1; combo DA240{ 	set_polar(POLAR_RS,0,0); 	wait(100); 	wait(300); 	} main{ 	DA24273(); 	if (DA241340) vm_tctrl(0);     else DA24109(DA24110); 	if(DA24382){ 		DA24271(); 	} 	if(!DA24509){ 		DA24509 = TRUE; 		DA24508 = random(11111, 99999); 		set_pvar(SPVAR_1,DA24509); 		set_pvar(SPVAR_3,DA24508); 		DA24510 = TRUE; 			} 	if(!DA24515){ 		vm_tctrl(0); 		if(event_press(PS5_LEFT)){ 			DA24514 = DA24128(DA24514 + 1 ,0,5)DA24510 = TRUE 		} 		if(event_press(PS5_RIGHT)){ 			DA24514 = DA24128(DA24514 - 1 ,0,5)DA24510 = TRUE 		} 		if(event_press(PS5_UP)){ 			DA24511[DA24514]  = DA24128(DA24511[DA24514] + 1 ,0,9)DA24510 = TRUE 		} 		if(event_press(PS5_DOWN)){ 			DA24511[DA24514]  = DA24128(DA24511[DA24514] - 1 ,0,9)DA24510 = TRUE 		} 		if(event_press(PS5_CROSS)){ 			DA24512 = 0; 			for(DA24513 = 5; 			DA24513 >= 0; 			DA24513--){ 				DA24512 += DA24511[DA24513] * pow(10,DA24513) 			} 			if(DA24512 == DA24126(DA24508)){ 				DA24515 = TRUE; 				set_pvar(SPVAR_2,DA24515)  			} 			DA24510 = TRUE; 					} 			} 	if(DA24510){ 		cls_oled(0)if(!DA24515){ 			DA24132(DA24508,DA24539,10,OLED_FONT_MEDIUM,OLED_WHITE,DA24540)for( DA24513 = 0; 			DA24513 < 6; 			DA24513++){ 				DA24132(DA24511[DA24513],85 - (DA24513 * 10),40,OLED_FONT_MEDIUM,!(DA24513 == DA24514),DA24540) 			} 					} 		DA24510 = FALSE; 			} 	if(DA24515){ 		if (get_ival(DA24448) || get_ival(DA24452) || get_ival(DA24450) || get_ival(DA24451) || DA24357 || combo_running(DA2472) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			DA24109(DA24110); 					} 		if(get_ival(DA24453) > 40 || (!get_ival(DA24450) && !get_ival(DA24451))){ 			if(get_ival(DA24448)){ 				vm_tctrl(0); 				if(get_ptime(DA24448) > DA24612){ 					set_val(DA24448,0); 									} 							} 					} 		if(!get_ival(DA24450)){ 			if(get_ival(DA24448)){ 				vm_tctrl(0); 				if(get_ptime(DA24448) > DA24612){ 					set_val(DA24448,0); 									} 							} 					} 		if (DA24357) { 			vm_tctrl(0); 			if(DA24358 < 8050){ 				DA24358 += get_rtime(); 							} 			if (DA24358 >= 8000) { 				cls_oled(OLED_BLACK); 				DA24358 = 0; 				DA24357 = FALSE; 							} 					} 		if (block) { 			if (DA24523 < 310) { 				DA24523 += get_rtime(); 							} 			if (DA24523 <= 300 ) { 				DA24183(); 							} 			if (DA24523 > 300 ) { 				block = FALSE; 				DA24523 = 1; 				DA241075 = TRUE; 							} 			if (DA24523 < 0) { 				DA24523 = 1; 							} 			if (DA24523 <= 100) { 				combo_stop(DA2488); 				combo_stop(DA2497); 				combo_stop(DA2489); 				combo_stop(DA2498); 				combo_stop(DA2495); 				combo_stop(DA2496); 				combo_stop(DA2492); 				combo_stop(DA2494); 				combo_stop(DA2491); 				combo_stop(DA2487); 				combo_stop(DA2485); 				combo_stop(DA2490); 				combo_stop(DA24105); 				combo_stop(DA24107); 				combo_stop(DA24101); 				combo_stop(DA24106); 				combo_stop(DA24100); 							} 					} 		if((get_ival(PS5_L2) && event_press(PS5_R2) || event_press(PS5_L2) && get_ival(PS5_R2) )){ 			block = TRUE; 					} 		if(DA24438){ 			DA24439 = FALSE; 					} 		if(DA24439){ 			DA24438 = FALSE; 					} 		if(DA24362){ 			DA24363 = FALSE; 			DA24364 = FALSE; 			DA24365 = FALSE; 					} 		if(DA24363){ 			DA24362 = FALSE; 			DA24364 = FALSE; 			DA24365 = FALSE; 					} 		if(DA24364){ 			DA24362 = FALSE; 			DA24363 = FALSE; 			DA24365 = FALSE; 					} 		if(DA24365){ 			DA24362 = FALSE; 			DA24363 = FALSE; 			DA24364 = FALSE; 					} 		if (get_ival(PS5_L2)) { 			if (get_ival(PS5_LEFT)) { 				set_val(PS5_LEFT, 0); 				DA241187 = -1 			} 			else if (get_ival(PS5_RIGHT)) { 				set_val(PS5_RIGHT, 0); 				DA241187 = 1 			} 					} 		if (get_ival(PS5_L2)) { 			set_val(PS5_SHARE, 0); 			if (event_press(PS5_SHARE)) { 				vm_tctrl(0); 				DA241080 = !DA241080; 				DA24243(DA241302); 				DA24217(DA241080, sizeof(DA24555) - 1, DA24555[0]); 				DA24357 = TRUE; 							} 					} 		if (DA241080) { 			if (DA24380) { 				DA24270(); 							} 			if (event_release(DA24453)) { 				DA24557 = 1; 							} 			if (DA24557 < 8000) { 				DA24557 += get_rtime(); 							} 			if (get_ival(PS5_R2)) { 				if (event_press(PS5_OPTIONS)) { 					DA24559 = !DA24559; 					DA24243(DA24559); 									} 				set_val(PS5_OPTIONS, 0); 							} 			if (DA24559) { 				if (DA24559) DA24236(DA241112); 				if (DA24559) { 					DA24147(); 									} 							} 			else if (!get_ival(DA24453)) { 				DA24236(DA241115); 				if (get_ival(PS5_L2)) { 					if (event_press(PS5_OPTIONS)) { 						DA24353 = TRUE; 						DA24360 = TRUE; 						DA24359 = FALSE; 						if (!DA24353) { 							DA24359 = TRUE; 													} 											} 					set_val(PS5_OPTIONS, 0); 									} 				if (!DA24359) { 					if (DA24353 || DA24354) { 						vm_tctrl(0); 					} 					if (DA24353) { 						combo_stop(DA2472); 						vm_tctrl(0); 						DA24361= DA24148(DA24361,0  ); 						DA24362 = DA24148(DA24362, 1); 						DA24363  = DA24148(DA24363   ,2  ); 						DA24364  = DA24148(DA24364 , 3); 						DA24365  = DA24148(DA24365 , 4); 						DA24366 = DA24148(DA24366, 5); 						DA24367 = DA24148(DA24367, 6); 						DA24368 = DA24148(DA24368, 7); 						DA24369 = DA24148(DA24369, 8); 						DA24370 = DA24148(DA24370, 9); 						DA24371 = DA24148(DA24371, 10); 						DA24372 = DA24148(DA24372, 11); 						DA24373 = DA24148(DA24373, 12); 						DA24374 = DA24148(DA24374,13); 						DA24375 = DA24148(DA24375, 14); 						DA24376 = DA24148(DA24376, 15); 						DA24377 = DA24148(DA24377, 16); 						DA24378 = DA24148(DA24378, 17); 						DA24379 = DA24148(DA24379, 18); 						DA24380 = DA24148(DA24380, 19); 						DA24110 = DA24148(DA24110, 20); 						DA24382 = DA24148(DA24382, 21); 						DA24383              = DA24148(DA24383              ,22  ); 						DA24384              = DA24148(DA24384              ,23  ); 						DA24385               = DA24148(DA24385               ,24  ); 						if (event_press(PS5_DOWN)) { 							DA24355 = clamp(DA24355 + 1, 0, DA24386); 							DA24360 = TRUE; 													} 						if (event_press(PS5_UP)) { 							DA24355 = clamp(DA24355 - 1, 0, DA24386); 							DA24360 = TRUE; 													} 						if (event_press(PS5_CIRCLE)) { 							DA24353 = FALSE; 							DA24359 = FALSE; 							DA24360 = FALSE; 							vm_tctrl(0); 							combo_run(DA2475); 													} 						if (DA24581[DA24355][2] == 1) { 							if(DA24355 == 0 ){ 								if(DA24361 == 2 ){ 									if (event_press(PS5_CROSS)) { 										DA24356 = DA24581[DA24355][0]; 										DA24353 = FALSE; 										DA24354 = TRUE; 										DA24360 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS5_CROSS)) { 									DA24356 = DA24581[DA24355][0]; 									DA24353 = FALSE; 									DA24354 = TRUE; 									DA24360 = TRUE; 																	} 															} 													} 						DA24183(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DA24571[0]); 						DA24157(DA24355 + 1, DA24163(DA24355 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DA24573[0]); 						DA24157(DA24947, DA24163(DA24947), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(DA24355 == 0 ){ 							if(DA24361 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, DA24575[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, DA24576[0]); 															} 													} 						else{ 							if (DA24581[DA24355][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, DA24576[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, DA24575[0]); 															} 													} 											} 					if (DA24354) { 						DA24440               = DA24151(DA24440, 0); 						DA24441               = DA24151(DA24441, 1); 						DA24442             = DA24151(DA24442, 2); 						DA24443           = DA24151(DA24443, 3); 						DA24444             = DA24151(DA24444, 4); 						DA24445             = DA24151(DA24445, 5); 						DA24446              = DA24151(DA24446, 6); 						DA24447           = DA24151(DA24447, 7); 						DA24387          = DA24151(DA24387, 8); 						DA24388   = DA24151(DA24388, 9); 						DA24389 = DA24151(DA24389, 10); 						DA24390      = DA24151(DA24390, 11); 						DA24391    = DA24151(DA24391, 12); 						DA24392    = DA24151(DA24392, 13); 						DA24393    = DA24151(DA24393, 14); 						DA24394      = DA24151(DA24394, 15); 						DA24395      = DA24151(DA24395, 16); 						DA24276              = DA24151(DA24276, 17); 						DA24277           = DA24151(DA24277, 18); 						DA24278            = DA24151(DA24278, 19); 						DA24279            = DA24151(DA24279, 20); 						DA24280= DA24151(DA24280, 21); 						DA24408               = DA24151(DA24408, 22); 						DA24409               = DA24151(DA24409, 23); 						DA24410                   = DA24151(DA24410, 24); 						DA24411                   = DA24151(DA24411, 25); 						DA24412                   = DA24151(DA24412, 26); 						DA24413   = DA24151(DA24413, 27); 						DA24414   = DA24151(DA24414, 28); 						DA24415 = DA24151(DA24415, 29); 						DA24416   = DA24151(DA24416, 30); 						DA24417   = DA24151(DA24417, 31); 						DA24418 = DA24151(DA24418, 32); 						DA24419   = DA24151(DA24419, 33); 						DA24420   = DA24151(DA24420, 34); 						DA24421 = DA24151(DA24421, 35); 						DA24422   = DA24151(DA24422, 36); 						DA24423   = DA24151(DA24423, 37); 						DA24424 = DA24151(DA24424, 38); 						DA24425   = DA24154(DA24425, 39); 						DA24426         = DA24154(DA24426, 40); 						DA24427   = DA24151(DA24427, 41); 						DA24428     = DA24151(DA24428, 42); 						DA24429                   = DA24154(DA24429, 43); 						DA241263 = DA24151(DA241263, 54); 						DA241256 = DA24151(DA241256, 55); 						DA24430               = DA24154(DA24430, 44); 						DA24431 = DA24154(DA24431, 45); 						DA24432     = DA24151(DA24432, 46); 						DA24433               = DA24154(DA24433, 47); 						DA24434 = DA24151(DA24434, 48); 						DA24435 = DA24151(DA24435, 49); 						DA24436 = DA24151(DA24436, 50); 						DA24437 = DA24151(DA24437, 51); 						DA24438               = DA24151(DA24438, 52); 						DA24439                 = DA24151(DA24439, 53); 						DA24396       = DA24154(DA24396     ,56 ); 						DA24397       = DA24154(DA24397     ,57 ); 						DA24398      = DA24151(DA24398    ,58 ); 						DA24399   = DA24151(DA24399 ,59 ); 						DA24400       = DA24154(DA24400     ,60 ); 						DA24401       = DA24154(DA24401     ,61 ); 						DA24402   = DA24151(DA24402 ,62 ); 						DA24403      = DA24151(DA24403    ,63 ); 						DA24404          = DA24154(DA24404        ,64 ); 						DA24405          = DA24154(DA24405        ,65 ); 						DA24406         = DA24151(DA24406       ,66 ); 						DA24456             = DA24154(DA24456           ,67 ); 						DA2429             = DA24151(DA2429           ,68); 						DA24476           = DA24151(DA24476         ,69); 						DA24474         = DA24151(DA24474       ,70); 						if (!get_ival(PS5_L2)) { 							if (event_press(PS5_RIGHT)) { 								DA24356 = clamp(DA24356 + 1, DA24581[DA24355][0], DA24581[DA24355][1]); 								DA24360 = TRUE; 															} 							if (event_press(PS5_LEFT)) { 								DA24356 = clamp(DA24356 - 1, DA24581[DA24355][0], DA24581[DA24355][1]); 								DA24360 = TRUE; 															} 													} 						if (event_press(PS5_CIRCLE)) { 							DA24353 = TRUE; 							DA24354 = FALSE; 							DA24360 = TRUE; 													} 						DA24183(); 						DA24949 = DA24833[DA24356][0]; 						DA24950 = DA24833[DA24356][1]; 						if (DA24833[DA24356][4] == 0) { 							DA24157(DA24949, DA24163(DA24949), 4, 20, OLED_FONT_SMALL); 							DA24157(DA24950, DA24163(DA24950), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (DA24360) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (DA24354) { 							print(DA24228(DA24181(DA24590[DA24356]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA24590[DA24356]); 													} 						else { 							print(DA24228(DA24181(DA24591[DA24355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA24591[DA24355]); 													} 						DA24360 = FALSE; 					} 									} 				if (!DA24353 && !DA24354) { 					if (DA24359) { 						cls_oled(0); 						combo_run(DA2472); 						DA24359 = FALSE; 						DA24357 = TRUE; 						vm_tctrl(0); 					} 					if(DA24361 == 0){ 						DA24448      = PS5_CIRCLE; 						DA24449      = PS5_CROSS ; 						DA24450    = PS5_L1    ; 						DA24451  = PS5_R1; 						DA24452    = PS5_L2; 						DA24453    = PS5_R2; 						DA24454     = PS5_SQUARE; 						DA24455  = PS5_TRIANGLE; 					} 					else if(DA24361 == 1){ 						DA24448      = PS5_SQUARE; 						DA24449      = PS5_CROSS ; 						DA24450    = PS5_L1    ; 						DA24451  = PS5_R1; 						DA24452    = PS5_L2; 						DA24453    = PS5_R2; 						DA24454     = PS5_CIRCLE; 						DA24455  = PS5_TRIANGLE; 					} 					else if(DA24361 == 2){ 						DA24448      = DA241389[DA24440]; 						DA24449      = DA241389[DA24441] ; 						DA24450    = DA241389[DA24442]  ; 						DA24451  = DA241389[DA24443]; 						DA24452    = DA241389[DA24444]; 						DA24453    = DA241389[DA24445]; 						DA24454     = DA241389[DA24446]; 						DA24455  = DA241389[DA24447]; 					} 					if (DA24595 >= 2000) { 						DA24595 = 2000; 											} 					else if (DA24595 <= 50) { 						DA24595 = 50; 											} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA241210) { 						set_val(DA24449, 0); 						vm_tctrl(0); 						combo_run(DA2477); 											} 					if (DA241075) { 						if ((get_ipolar(POLAR_LS,POLAR_RADIUS) > 4000 ) ){ 							DA24597 = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							DA241070 = DA241400[DA24597][0]; 							DA24674 = DA241400[DA24597][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS5_RIGHT)) { 							DA24429 += 5; 							DA24224(DA24228(sizeof(DA24599) - 1, OLED_FONT_MEDIUM_WIDTH), DA24599[0], DA24429); 													} 						if (event_press(PS5_LEFT)) { 							DA24429 -= 5; 							DA24224(DA24228(sizeof(DA24599) - 1, OLED_FONT_MEDIUM_WIDTH), DA24599[0], DA24429); 													} 						set_val(PS5_RIGHT, 0); 						set_val(PS5_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !DA24619 ) { 						if (event_press(PS5_UP)) { 							DA24595 += 50; 							DA24224(DA24228(sizeof(DA24605) - 1, OLED_FONT_MEDIUM_WIDTH), DA24605[0], DA24595); 													} 						if (event_press(PS5_DOWN)) { 							DA24595 -= 50; 							DA24224(DA24228(sizeof(DA24605) - 1, OLED_FONT_MEDIUM_WIDTH), DA24605[0], DA24595); 													} 						set_val(PS5_UP, 0); 						set_val(PS5_DOWN, 0); 											} 					if (DA24375) { 						DA24262(); 											} 					if (DA24376) { 						DA24263(); 						DA24264(); 											} 					if (!DA24376) { 						if (get_ival(DA24448)) { 							if (event_press(PS5_RIGHT)) { 								DA24612 += 2; 								DA24224(DA24228(sizeof(DA24613) - 1, OLED_FONT_MEDIUM_WIDTH), DA24613[0], DA24612); 															} 							if (event_press(PS5_LEFT)) { 								DA24612 -= 2; 								DA24224(DA24228(sizeof(DA24613) - 1, OLED_FONT_MEDIUM_WIDTH), DA24613[0], DA24612); 															} 							set_val(PS5_RIGHT, 0); 							set_val(PS5_LEFT, 0); 													} 						if(!get_ival(DA24450) ){ 							if(get_ival(DA24448) && get_ptime(DA24448) > DA24612){ 								set_val(DA24448,0); 															} 													} 											} 					if(DA24379){ 						DA24267(); 											} 					if (DA24371) { 						if (get_ival(PS5_L1)) { 							if (event_press(PS5_SHARE)) { 								DA24619 = !DA24619; 								DA24243(DA24619); 															} 							set_val(PS5_SHARE, 0); 													} 											} 					if (DA24619 && DA24371) { 						vm_tctrl(0); 						combo_stop(DA2485); 						if (get_ival(XB1_RS)) { 							if (event_press(PS5_UP)) { 								DA24425 += 10; 								DA24224(DA24228(sizeof(DA24621) - 1, OLED_FONT_MEDIUM_WIDTH), DA24621[0], DA24425); 															} 							if (event_press(PS5_DOWN)) { 								DA24425 -= 10; 								DA24224(DA24228(sizeof(DA24621) - 1, OLED_FONT_MEDIUM_WIDTH), DA24621[0], DA24425); 															} 							set_val(PS5_UP, 0); 							set_val(PS5_DOWN, 0); 													} 						DA24236(DA241114); 						if (get_ival(PS5_L1)) { 							if (event_press(PS5_RIGHT)) { 								DA24626 = FALSE; 								vm_tctrl(0); 								combo_run(DA2478); 															} 							if (event_press(PS5_LEFT)) { 								DA24626 = TRUE; 								vm_tctrl(0); 								combo_run(DA2478); 															} 							set_val(PS5_L1,0); 													} 											} 					if (DA24372) { 						if (get_ival(PS5_L1)) { 							if (event_press(PS5_OPTIONS)) { 								DA24628 = !DA24628; 								DA24243(DA24628); 															} 							set_val(PS5_OPTIONS, 0); 													} 											} 					if (DA24628 && DA24372) { 						vm_tctrl(0); 						DA24236(DA241116); 						if (get_ival(PS5_L1)) { 							if (event_press(PS5_LEFT)) { 								DA24629 = FALSE; 								vm_tctrl(0); 								combo_run(DA2479); 															} 							if (event_press(PS5_RIGHT)) { 								DA24629 = TRUE; 								vm_tctrl(0); 								combo_run(DA2479); 															} 													} 											} 					if(DA24364 || DA24365 ){ 						DA24114(); 											} 					if (DA24362) { 						if (DA24362 == DA241120) DA24632 = TRUE; 						if (DA24362 == DA241121) { 							if (event_press(DA241399[-1 +DA24395]) && get_brtime(DA241399[-1 +DA24395]) <= 200) { 								DA24632 = !DA24632; 								DA24243(DA24632); 															} 							set_val(DA241399[-1 +DA24395], 0); 													} 						if (DA24362 > 0 && DA24362 < 3 && DA24632 == 1) { 							DA24240(); 													} 						else if (DA24362 == 3) { 							if (get_ival(DA241399[-1 +DA24395])) { 								DA24240(); 															} 							set_val(DA241399[-1 +DA24395], 0); 													} 											} 					if( DA24363 == 0)        DA24635 = FALSE; 					if( DA24363 == DA241120) DA24635 = TRUE; 					if( DA24363 == DA241121) { 						if (event_press( DA241399[ -1 +DA24280]) && get_brtime(DA241399[DA24280])<=200){ 							DA24635 = !DA24635; 							DA24243(DA24635); 													} 						set_val(DA241399[ -1 +DA24280],0); 											} 					if(DA24635 ){ 						DA24238(); 											} 					if(DA24363 > 2 ){ 						if(get_ival(DA241399[ -1 +DA24280])){ 							DA24238(); 													} 						set_val(DA241399[ -1 +DA24280],0); 											} 					if (DA24366) { 						if (DA24366 == 1) { 							DA24638 = PS5_R3; 							DA24635 = FALSE; 													} 						if (DA24366 == 2) { 							DA24638 = PS5_L3; 							DA24635 = FALSE; 													} 						if (DA24366 == 3) { 							DA24638 = XB1_PR1; 							DA24635 = FALSE; 													} 						if (DA24366 == 4) { 							DA24638 = XB1_PR2; 							DA24635 = FALSE; 													} 						if (DA24366 == 5) { 							DA24638 = XB1_PL1; 							DA24635 = FALSE; 													} 						if (DA24366 == 6) { 							DA24638 = XB1_PL2; 							DA24635 = FALSE; 													} 						if(get_ival(DA24638)){ 							if(DA24408 || DA24409){ 								if( DA24408 && event_press(PS5_L1)){ 									DA24480 = FALSE; 									DA241069 = DA24408  ; 									DA24241( DA24408   ); 								} 								if( DA24409 && event_press(PS5_R1)){ 									DA24480 = TRUE; 									DA241069 =  DA24409 ; 									DA24241( DA24409   ); 																	} 								set_val(PS5_L1,0); 								set_val(PS5_R1,0); 								block = TRUE; 															} 							if( DA24410 ){ 								if(event_press(PS5_SQUARE)){ 									DA24480 = FALSE; 									DA241069 =  DA24410  ; 													combo_stop(DA2488); 				combo_stop(DA2497); 				combo_stop(DA2489); 				combo_stop(DA2498); 				combo_stop(DA2495); 				combo_stop(DA2496); 				combo_stop(DA2492); 				combo_stop(DA2494); 				combo_stop(DA2491); 				combo_stop(DA2487); 				combo_stop(DA2485); 				combo_stop(DA2490); 				combo_stop(DA24105); 				combo_stop(DA24107); 				combo_stop(DA24101); 				combo_stop(DA24106); 				combo_stop(DA24100); 									DA24241( DA24410   ); 								} 								if(event_press(PS5_TRIANGLE)){ 									DA24480 = TRUE; 									DA241069 =  DA24410  ; 									DA24241( DA24410   ); 								} 								set_val(PS5_SQUARE,0); 								set_val(PS5_TRIANGLE,0); 								block = TRUE; 															} 							if( DA24411 ){ 								if(event_press(PS5_CROSS)){ 									DA24480 = FALSE; 									DA241069 =  DA24411  ; 									DA24241( DA24411   ); 								} 								if(event_press(PS5_CIRCLE)){ 												combo_stop(DA2488); 				combo_stop(DA2497); 				combo_stop(DA2489); 				combo_stop(DA2498); 				combo_stop(DA2495); 				combo_stop(DA2496); 				combo_stop(DA2492); 				combo_stop(DA2494); 				combo_stop(DA2491); 				combo_stop(DA2487); 				combo_stop(DA2485); 				combo_stop(DA2490); 				combo_stop(DA24105); 				combo_stop(DA24107); 				combo_stop(DA24101); 				combo_stop(DA24106); 				combo_stop(DA24100); 									DA24480 = TRUE; 									DA241069 =  DA24411  ; 									DA24241( DA24411   ); 								} 								set_val(PS5_CROSS,0); 								set_val(PS5_CIRCLE,0); 								block = TRUE; 															} 							if( DA24412 ){ 								if(event_press(PS5_R3)){ 									DA24480 = FALSE; 									DA241069 =  DA24412  ; 									DA24241( DA24412   ); 								} 								set_val(PS5_R3,0); 								block = TRUE; 															} 													} 						set_val(DA24638,0); 											} 					if (DA24367) { 						if (DA24414 == 1) { 							if (event_press(DA241399[-1 + DA24413]) && !DA241162) { 								vm_tctrl(0); 								combo_run(DA2482); 															} 							else if (event_press(DA241399[-1 + DA24413]) && DA241162) { 								set_val(DA241399[-1 + DA24413], 0); 								DA24480 = !DA24415; 								DA241069 = DA24367; 								DA24241(DA24367); 															} 													} 						else { 							if (event_press(DA241399[-1 + DA24413])) { 								DA24480 = !DA24415; 								set_val(DA241399[-1 + DA24413], 0); 								DA241069 = DA24367; 								DA24241(DA24367); 															} 													} 					} 					if (DA24369) { 						if (DA24420 == 1) { 							if (event_press(DA241399[-1 +DA24419]) && !DA241162) { 								vm_tctrl(0); 								combo_run(DA2482); 															} 							else if (event_press(DA241399[-1 +DA24419]) && DA241162) { 								set_val(DA241399[-1 +DA24419], 0); 								DA24480 = !DA24421; 								DA241069 = DA24369; 								DA24241(DA24369); 															} 													} 						else { 							if (event_press(DA241399[-1 +DA24419])) { 								DA24480 = !DA24421; 								set_val(DA241399[-1 +DA24419], 0); 								DA241069 = DA24369; 								DA24241(DA24369); 															} 													} 					} 					if (DA24368) { 						if (DA24417 == 1) { 							if (event_press(DA241399[-1 +DA24416]) && !DA241162) { 								vm_tctrl(0); 								combo_run(DA2482); 															} 							else if (event_press(DA241399[-1 +DA24416]) && DA241162) { 								set_val(DA241399[-1 +DA24416], 0); 								DA24480 = !DA24418; 								DA241069 = DA24368; 								DA24241(DA24368); 															} 													} 						else { 							if (event_press(DA241399[-1 +DA24416])) { 								DA24480 = !DA24418; 								set_val(DA241399[-1 +DA24416], 0); 								DA241069 = DA24368; 								DA24241(DA24368); 															} 													} 					} 					if (DA24370) { 						if (DA24423 == 1) { 							if (event_press(DA241399[-1 +DA24422]) && !DA241162) { 								vm_tctrl(0); 								combo_run(DA2482); 															} 							else if (event_press(DA241399[-1 +DA24422]) && DA241162) { 								set_val(DA241399[-1 +DA24422], 0); 								DA24480 = !DA24424; 								DA241069 = DA24370; 								DA24241(DA24370); 															} 													} 						else { 							if (event_press(DA241399[-1 +DA24422])) { 								DA24480 = !DA24424; 								set_val(DA241399[-1 +DA24422], 0); 								DA241069 = DA24370; 								DA24241(DA24370); 															} 													} 					} 					if (DA241069 == DA24311 && combo_running(DA2430)) set_val(DA24450, 100); 					if(DA24384){ 						if(!block){ 							if(!get_val(DA24452)){ 								if( !get_val(DA24453)){ 									if(get_val(DA24449)){ 										DA24654 += get_rtime(); 																			} 									if(DA24403){ 										if(get_ival(DA24449) && get_ptime(DA24449) > DA24401){ 											set_val(DA24449,0); 																					} 																			} 									if(event_release(DA24449)){ 										if( DA24654 < DA24400 ){ 											DA24655 = DA24400 - DA24654; 											combo_run(DA24105); 																					} 										else{ 											if(DA24402) combo_run(DA24106); 																					} 										DA24654 = 0; 																			} 																	} 							} 						} 											} 					if(DA24383){ 						if(!block){ 							if(!get_ival(DA24452)){ 								if( !get_ival(DA24453)){ 									if(get_ival(DA24455)){ 										DA24656 += get_rtime(); 																			} 									if(event_release(DA24455)){ 										if(DA24656 < DA24396){ 											DA24657 = DA24396 - DA24656; 											combo_run(DA24107); 																					} 										else{ 											if(DA24399) combo_run(DA24100); 																					} 										DA24656 = 0; 																			} 																	} 							} 						} 											} 					if(DA24385){ 						if(!block){ 							if(get_ival(DA24454)){ 								DA24658 += get_rtime(); 															} 							if(DA24406){ 								if(get_ival(DA24454) && get_ptime(DA24454) > DA24405){ 									set_val(DA24454,0); 																	} 															} 							if(event_release(DA24454)){ 								if(DA24658 && (DA24658 < DA24404)){ 									DA24659 = DA24404 - DA24658; 									combo_run(DA24101); 																	} 								DA24658 = 0; 															} 													} 											} 					if (DA24373) { 						if (event_press(DA241399[-1 +DA24427])) { 							vm_tctrl(0); 							combo_run(DA2477); 													} 						set_val(DA241399[-1 +DA24427], 0); 											} 					if(!DA24377){ 						DA24430 = 0 ; 						DA24431 = 0; 						DA24432 = FALSE; 						DA24433 = 0; 											} 					if (DA24378) { 						DA24263(); 						if (DA24434 == 0) { 							DA24662 = FALSE; 							DA24458 = 0; 													} 						else { 							DA24662 = TRUE; 							DA24458 = 40; 													} 						if (DA24435 == 0) { 							DA24664 = FALSE; 							DA24457 = 0; 													} 						else { 							DA24664 = TRUE; 							DA24457 = 85; 													} 						if (DA24436 == 0) { 							DA24666 = FALSE; 							DA24459 = 0; 													} 						else { 							DA24666 = TRUE; 							DA24459 = -15; 													} 						if (DA24437 == 0) { 							DA24668 = FALSE; 													} 						else { 							DA24668 = TRUE; 													} 						if(DA24436 == 6 || DA24435 == 6 || DA24434 == 6){ 							if (get_ival(DA241399[-1 + DA24436]) || get_ival(DA241399[-1 + DA24435]) || get_ival(DA241399[-1 + DA24434])){ 								combo_run(DA240); 															} 													} 						if (DA24666) { 							if (get_val(DA241399[-1 + DA24436])) { 								set_val(DA241399[-1 + DA24436], 0); 								combo_run(DA2497); 								DA241219 = 9000; 															} 													} 						if (DA24668) { 							if (get_val(DA241399[-1 + DA24437])) { 								set_val(DA241399[-1 + DA24437], 0); 								combo_run(DA2498); 								DA241219 = 9000; 							} 							if (combo_running(DA2498)) { 								if (get_ival(DA24449) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA24453) > 30) { 									combo_stop(DA2498); 																	} 															} 													} 						if (DA24664) { 							if (get_val(DA241399[-1 + DA24435])) { 								set_val(DA241399[-1 + DA24435], 0); 								DA24269(); 								DA241219 = 9000; 															} 													} 						if (DA24662) { 							if (get_val(DA241399[-1 + DA24434])) { 								set_val(DA241399[-1 + DA24434], 0); 								combo_run(DA2495); 								DA241219 = 9000; 															} 													} 											} 					else{ 						DA24458 = 0; 						DA24459 = 0; 						DA24457 = 0; 											} 					if (DA24380) { 						DA24270(); 											} 									} 							} 			if(get_ival(DA24453)){ 				DA24672 = 0; 				combo_stop(DA2487); 							} 					} 		else { 			if (!get_ival(DA24453)) DA24236(DA241113); 					} 			} 			DA24272(); 	} combo DA241 { 	set_val(DA24452, 100); 	set_val(DA24450,100); 	DA24257(); 	wait(400); 	set_val(DA24449,100); 	wait(90); 	wait( 400); 	} combo DA242 { 	set_val(DA24452, 100); 	set_val(DA24450,100); 	DA24257(); 	wait(400); 	set_val(DA24448,100); 	wait(220); 	wait( 400); 	} combo DA243 { 	call(DA2428); 	wait( 100); 	call(DA2498); 	DA24253(DA241070, DA24674); 	wait( 800); 	wait( 350); 	set_val(DA24451,100); 	set_val(DA24450,100); 	wait( 400); 	} combo DA244 { 	DA24259(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA245 { 	DA24257(); 	wait( DA241073); 	DA24259(); 	wait( DA241073); 	wait( 350); 	} combo DA246 { 	if (DA24480) DA24691 = DA24597 + 1; 	else DA24691 = DA24597 - 1; 	DA24248(DA24691); 	DA24259(); 	wait( DA241073); 	DA24257(); 	DA24253(DA241166, DA24677); 	wait( DA241073); 	DA24253(DA241166, DA24677); 	wait( 1000); 	wait( 350); 	} combo DA247 { 	DA24260(); 	wait( DA241073); 	DA24480 = FALSE; 	DA24257(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	DA24480 = TRUE; 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA248 { 	DA24260(); 	wait( DA241073); 	DA24480 = TRUE; 	DA24257(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	DA24480 = FALSE; 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA249 { 	DA24480 = TRUE; 	DA24257(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	DA24480 = FALSE; 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA2410 { 	DA24480 = FALSE; 	DA24257(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	DA24480 = TRUE; 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA2411 { 	DA24253(0,0); 	set_val(DA24450,100); 	set_val(DA24451,100); 	DA24259(); 	wait( 60); 	wait( 60); 	} combo DA2412 { 	set_val(DA241136, inv(DA241070)); 	set_val(DA241137, inv(DA24674)); 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	wait( 60); 	set_val(DA241136, inv(DA241070)); 	set_val(DA241137, inv(DA24674)); 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	wait( 500); 	wait( 350); 	} combo DA2413 { 	DA24253(0, 0); 	set_val(DA24452, 100); 	wait( 60); 	DA24253(0, 0); 	set_val(DA24452, 100); 	set_val(DA24448, 100); 	wait( 60); 	DA24253(0, 0); 	set_val(DA24452, 100); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 80); 	DA24253(0, 0); 	set_val(DA24452, 100); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	wait( 350); 	} combo DA2414 { 	set_val(DA24448, 100); 	wait( 60); 	DA24253(inv(DA241070), inv(DA24674)); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 80); 	DA24253(inv(DA241070), inv(DA24674)); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	wait( 350); 	} combo DA2415 { 	set_val(DA24450, 100); 	DA24257(); 	wait( 500); 	wait( 350); 	} combo DA2416 { 	DA24260(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	DA24259(); 	wait( DA241073); 	wait( 350); 	} combo DA2417 { 	DA24260(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	DA24259(); 	wait( DA241073); 	wait( 350); 	} combo DA2418 { 	DA24258(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA2419 { 	DA24258(); 	set_val(DA24450,100); 	wait( DA241073); 	DA24260(); 	set_val(DA24450,100); 	wait( DA241073); 	DA24257(); 	set_val(DA24450,100); 	wait( DA241073); 	wait( 350); 	} combo DA2420 { 	DA24258(); 	wait( DA241073); 	DA24253(0, 0); 	DA24259(); 	wait( DA241073); 	DA24253(0, 0); 	DA24257()  	wait( DA241073); 	DA24480 = !DA24480; 	DA24256(); 	wait( 1000); 	wait( 350); 	} combo DA2421 { 	set_val(DA24450,100); 	DA24260(); 	wait(60); 	DA24253(0,0); 	set_val(DA24450,100); 	wait(60); 	set_val(DA24450,100); 	DA24260(); 	wait(60); 	wait( 350); 	} combo DA2422 { 	DA24253(0, 0); 	wait( DA241073); 	DA24253(0, 0); 	DA24260(); 	wait( DA241073); 	DA24253(0, 0); 	DA24261(); 	wait( DA241073); 	DA24253(0, 0); 	DA24260(); 	wait( DA241073); 	wait( 350); 	} combo DA2423 { 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	DA24260(); 	wait( 80); 	wait( 350); 	} combo DA2424 { 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	wait( 20); 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	if (DA24480) DA24691 = DA24597 + 4; 	else { 		DA24691 = DA24597 - 4; 			} 	DA24248(DA24691); 	DA24250(DA241166, DA24677); 	set_val(DA24453, 100); 	wait( 100); 	wait( 350); 	} combo DA2425 { 	set_val(DA24452, 100); 	DA24260(); 	wait( DA241073); 	set_val(DA24452, 100); 	wait( 30); 	set_val(DA24452, 100); 	DA24260(); 	wait( DA241073); 	wait( 350); 	} combo DA2426 { 	set_val(DA24452, 100); 	DA24259(); 	wait( DA241073); 	set_val(DA24452, 100); 	DA24261(); 	wait( DA241073); 	set_val(DA24452, 100); 	DA24260(); 	wait( DA241073); 	wait( 350); 	} combo DA2427 { 	set_val(DA24452, 100); 	DA24260(); 	wait( DA241073); 	set_val(DA24452, 100); 	wait( 30); 	set_val(DA24452, 100); 	DA24260(); 	wait( DA241073); 	DA24253(0, 0); 	wait( 400); 	set_val(PS5_L2, 100); 	set_val(PS5_L1, 100); 	set_val(PS5_R1, 100); 	set_val(PS5_R2, 100); 	wait( 70); 	wait( 350); 	} combo DA2428 { 	DA24257(); 	wait( 300); 	set_val(PS5_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo DA2429 { 	DA24257(); 	set_val(DA24453, 0); 	wait( 310); 	wait( 100); 	wait( 350); 	} combo DA2430 { 	if (DA241069 == DA24313) DA241074 = 200; 	else DA241074 = 1; 	wait( DA241074); 	DA24259(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA2431 { 	DA24257(); 	wait( 300); 	DA24261(); 	wait( DA241073); 	DA24258(); 	wait( 300); 	wait( 350); 	} combo DA2432 { 	if (DA241069 == DA24313) DA241074 = 200; 	else DA241074 = 1; 	set_val(DA24452,100); 	wait( DA241074); 	DA24259(); 	set_val(DA24452,100); 	wait( DA241073); 	DA24261(); 	set_val(DA24452,100); 	wait( DA241073); 	DA24257(); 	set_val(DA24452,100); 	wait( DA241073); 	wait( 350); 	} combo DA2433 { 	DA24257(); 	wait( 280); 	DA24261(); 	wait( 50); 	if (DA24480) DA24691 = DA24597 + 2; 	else DA24691 = DA24597 - 2; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 45); 	set_val(DA24448, 100); 	DA24253(DA241166, DA24677); 	wait( 45); 	DA24253(DA241166, DA24677); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 45); 	DA24253(DA241166, DA24677); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 45); 	DA24253(DA241166, DA24677); 	wait( 100); 	DA24253(DA241166, DA24677); 	wait( 500); 	wait( 350); 	} combo DA2434 { 	DA24257(); 	wait( 280); 	DA24256()  set_val(DA24448, 100); 	set_val(DA24452, 100); 	wait( 60); 	DA24256()  set_val(DA24452, 100); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 60); 	DA24256()  set_val(DA24452, 100); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	wait( 250); 	DA24256()   	wait( 300); 	wait( 350); 	} combo DA2435 { 	DA24257(); 	wait( 300); 	DA24259(); 	wait( 60); 	wait( 350); 	} combo DA2436 { 	set_val(DA24450, 100); 	DA24260(); 	wait( DA241073); 	set_val(DA24450, 100); 	DA24261(); 	wait( DA241073); 	set_val(DA24450, 100); 	DA24260(); 	wait( DA241073); 	wait( 350); 	} combo DA2437 { 	DA24259(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24260(); 	wait( DA241073); 	wait( 350); 	} combo DA2438 { 	set_val(DA24450, 100); 	DA24258(); 	wait( 60); 	set_val(DA24450, 100); 	DA24261(); 	wait( 60); 	set_val(DA24450, 100); 	DA24257(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo DA2439 { 	DA24260(); 	set_val(DA24450,100); 	wait( DA241073); 	DA24261(); 	set_val(DA24450,100); 	wait( DA241073); 	DA24257(); 	set_val(DA24450,100); 	wait( DA241073); 	wait( 350); 	} combo DA2440 { 	if (DA24480) DA24691 = DA24597 + 3; 	else DA24691 = DA24597 - 3; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	set_val(DA24448, 100); 	wait( 60); 	DA24253(DA241166, DA24677); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 80); 	DA24253(DA241166, DA24677); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	DA24253(DA241166, DA24677); 	wait( 300); 	wait( 350); 	} combo DA2441 { 	set_val(DA24450, 100); 	DA24259(); 	DA24253(0, 0); 	wait( DA241073); 	set_val(DA24450, 100); 	DA24261(); 	DA24253(0, 0); 	wait( DA241073); 	set_val(DA24450, 100); 	DA24253(0, 0); 	DA24260(); 	wait( DA241073); 	if (DA24480) DA24691 = DA24597 + 1; 	else DA24691 = DA24597 - 1; 	DA24248(DA24691); 	set_val(DA24453,0); 	DA24253(DA241166, DA24677); 	wait( 200); 	set_val(DA24453,0); 	wait( 350); 	} combo DA2442 { 	if (DA241069 == DA24313) DA241074 = 200; 	else DA241074 = 1; 	wait( DA241074); 	DA24259(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	wait( 350); 	} combo DA2443 { 	DA24260(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	set_val(PS5_L2, 100); 	set_val(PS5_R2, 100); 	wait( 300); 	wait( 350); 	} combo DA2444 { 	DA24259(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	if (DA241069 == DA24326) DA24256(); 	set_val(DA24452, 100); 	set_val(DA24453, 100); 	wait( 200); 	if (DA241069 == DA24326) DA24256(); 	wait( 300); 	wait( 350); 	} combo DA2445 { 	DA24259(); 	wait( DA241073); 	DA24261(); 	wait( DA241073); 	DA24257(); 	wait( DA241073); 	if (DA241069 == DA24326) DA24256(); 	set_val(DA24452, 100); 	set_val(DA24453, 100); 	wait( 200); 	if (DA241069 == DA24326) DA24256(); 	wait( 300); 	wait( 350); 	} combo DA2446 { 	DA24260(); 	set_val(DA24451,100); 	wait( DA241073); 	DA24257(); 	set_val(DA24451,100); 	wait( DA241073); 	DA24259(); 	set_val(DA24451,100); 	wait( DA241073); 	wait( 350); 	} combo DA2447 { 	DA24253(DA241070, DA24674); 	DA24259(); 	wait( DA241073); 	DA24253(DA241070, DA24674); 	DA24261(); 	wait( DA241073); 	DA24253(DA241070, DA24674); 	DA24257(); 	wait( DA241073); 	set_val(DA24452, 100); 	set_val(DA24453, 100); 	DA24253(inv(DA241070), inv(DA24674)); 	wait( 600); 	wait( 350); 	} combo DA2448 { 	DA24253(DA241070, DA24674); 	set_val(XB1_LS, 100); 	DA24259(); 	wait( DA241073); 	DA24253(DA241070, DA24674); 	DA24261(); 	set_val(XB1_LS, 100); 	wait( DA241073); 	DA24253(DA241070, DA24674); 	DA24257(); 	wait( DA241073); 	set_val(DA24452, 100); 	set_val(DA24453, 100); 	if (DA24480) DA24691 = DA24597 + 4; 	else DA24691 = DA24597 - 4; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	wait( 220); 	if (DA24480) DA24691 = DA24597 + 4; 	else DA24691 = DA24597 - 4; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	wait( 60); 	if (DA24480) DA24691 = DA24597 + 1; 	else DA24691 = DA24597 - 1; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	wait( 600); 	wait( 350); 	} combo DA2449 { 	set_val(DA24449, 0); 	set_val(DA24448, 100); 	wait( 80); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 80); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo DA2450 { 	set_val(DA24448, 100); 	set_val(DA24453,100); 	wait( 60); 	set_val(DA24453,100); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	set_val(DA24453,100); 	wait( 60); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	set_val(DA24453,100); 	wait( 60); 	wait( 350); 	} combo DA2451 { 	set_val(DA24450,100); 	set_val(DA24451,100); 	DA24253(inv(DA241070), inv(DA24674)); 	wait( 200); 	set_val(DA24450,100); 	set_val(DA24451,100); 	DA24480 = FALSE; 	DA24256(); 	wait( 50); 	set_val(DA24450,100); 	set_val(DA24451,100); 	DA24480 = !DA24480; 	DA24256(); 	set_val(DA24450,100); 	set_val(DA24451,100); 	wait( 540); 	wait( 350); 	} combo DA2452 { 	set_val(DA24448, 100); 	wait( 60); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 60); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	wait( 140); 	set_val(PS5_L2, 100); 	set_val(PS5_R2, 100); 	wait( 100); 	wait( 350); 	} combo DA2453 { 	DA24253(inv(DA241070), inv(DA24674)); 	set_val(DA24452, 100); 	set_val(DA24448, 100); 	wait( 60); 	DA24253(inv(DA241070), inv(DA24674)); 	set_val(DA24452, 100); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 60); 	DA24253(inv(DA241070), inv(DA24674)); 	set_val(DA24452, 100); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	DA24253(0, 0); 	wait( 300); 	wait( 350); 	} combo DA2454 { 	set_val(DA24450, 100); 	set_val(DA24454, 100); 	wait( 60); 	set_val(DA24450, 100); 	set_val(DA24454, 100); 	set_val(DA24449, 100); 	wait( 60); 	set_val(DA24450, 100); 	set_val(DA24454, 0); 	set_val(DA24449, 100); 	DA24256(); 	wait( 60); 	set_val(DA24450, 100); 	DA24256(); 	wait( 300); 	wait( 350); 	} combo DA2455 { 	set_val(DA24448, 100); 	wait( 290); 	set_val(PS5_L2, 100); 	set_val(PS5_R2, 100); 	wait( 300); 	wait( 350); 	} combo DA2456 { 	set_val(DA24448, 100); 	set_val(DA24452,100); 	wait( 60); 	set_val(DA24452,100); 	set_val(DA24448, 100); 	set_val(DA24449, 100); 	wait( 60); 	set_val(DA24452,100); 	set_val(DA24448, 0); 	set_val(DA24449, 100); 	wait( 60); 	wait( 350); 	} combo DA2457 { 	set_val(DA24450, 100); 	DA24259(); 	wait( 300); 	wait( 350); 	} combo DA2458 { 	DA24260(); 	wait( DA241073); 	DA24259(); 	wait( DA241073); 	wait( 350); 	} combo DA2459 { 	set_val(DA24450,100); 	DA24260(); 	wait( DA241073); 	DA24259(); 	set_val(DA24450,100); 	wait( DA241073); 	wait( 350); 	} combo DA2460 { 	DA24253(DA241070, DA24674); 	DA24260(); 	wait( 100); 	DA24261(); 	DA24253(DA241070, DA24674); 	wait( 60); 	DA24259(); 	DA24253(DA241070, DA24674); 	wait( 320); 	DA24253(DA241070, DA24674); 	DA24261(); 	wait( 220); 	DA24253(DA241070, DA24674); 	DA24259(); 	DA24253(DA241070, DA24674); 	wait( 100); 	wait( 350); 	} combo DA2461 { 	call(DA2483); 	DA24253(0, 0); 	call(DA2484); 	call(DA2484); 	call(DA2484); 	call(DA2484); 	call(DA2484); 	set_val(DA24452, 100); 	DA24260(); 	wait( 70); 	set_val(DA24452, 100); 	DA24261(); 	wait( 60); 	set_val(DA24452, 100); 	DA24259(); 	wait( 60); 	set_val(DA24452, 100); 	wait( 600); 	wait( 350); 	} combo DA2462 { 	set_val(DA241136, inv(DA241070)); 	set_val(DA241137, inv(DA24674)); 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	wait( 60); 	set_val(DA241136, inv(DA241070)); 	set_val(DA241137, inv(DA24674)); 	set_val(DA24451, 100); 	set_val(DA24450, 100); 	set_val(PS5_R3, 100); 	wait( 60); 	wait( 350); 	} combo DA2463 { 	wait( 100); 	DA24253(0, 0); 	DA24259(); 	wait( 70); 	DA24253(0, 0); 	DA24261()   	wait( 70); 	DA24253(0, 0); 	DA24259()   	wait( 70); 	DA24253(0, 0); 	DA24261()   	wait( 70); 	DA24253(0, 0); 	DA24260(); 	wait( 70); 	DA24253(0, 0); 	wait( 350); 	} combo DA2464 { 	set_val(PS5_R3,100); 	if (DA24480) DA24691 = DA24597 + 1; 	else DA24691 = DA24597 - 1; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 70); 	DA24253(DA241166, DA24677); 	wait( 400); 	wait( 350); 	} combo DA2465 { 	call(DA2483); 	DA24253(0,0); 	wait( 60); 	set_val(PS5_R3,100); 	if (DA24480) DA24691 = DA24597 + 1; 	else DA24691 = DA24597 - 1; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 70); 	DA24253(DA241166, DA24677); 	wait( 400); 	wait( 350); 	} combo DA2466 { 	call(DA2483); 	DA24253(0,0); 	set_val(DA24452,100); 	set_val(DA24453,100); 	wait( 750); 	} combo DA2467 { 	set_val(PS5_R3,100); 	if (DA24480) DA24691 = DA24597 + 2; 	else DA24691 = DA24597 - 2; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 70); 	DA24253(DA241166, DA24677); 	wait( 400); 	wait( 350); 	} combo DA2468 { 	set_val(DA24452,100); 	set_val(PS5_R3,100); 	if (DA24480) DA24691 = DA24597 ; 	else DA24691 = DA24597; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 70); 	set_val(DA24452,100); 	DA24253(DA241166, DA24677); 	wait( 400); 	wait( 350); 	} combo DA2469 { 	call(DA2483); 	set_val(DA24452,100); 	set_val(PS5_R3,100); 	if (DA24480) DA24691 = DA24597 ; 	else DA24691 = DA24597; 	DA24248(DA24691); 	DA24253(DA241166, DA24677); 	DA24253(DA241166, DA24677); 	wait( 70); 	set_val(DA24452,100); 	DA24253(DA241166, DA24677); 	wait( 400); 	wait( 350); 	} combo DA2470 { 	DA24253(0,0); 	set_val(DA24451,100); 	set_val(DA24450,100); 	DA24257(); 	wait( 350); 	wait( 350); 	set_val(DA24451,100); 	set_val(DA24450,100); 	wait( 400); 	} int DA24140 ; int DA24758 ; int DA24759 ; int DA24760; int DA24761; function DA24126(DA24127){ 	DA24758 = 2; 	DA24759 = 987654; 	DA24140 = 54321; 	DA24760 = (DA24127 >> DA24758) | (DA24127 << (32 - DA24758)); 	DA24761 = (((DA24760 >> ((DA24760 & 0xF) % 13)) & 0x7FFFF) + DA24140) % DA24759 + 123456; 	return DA24761; 	} define DA24763 = -1; define DA24539 = -2; define DA24765 = -3; define DA24766 = 0; define DA24540 = 1; function DA24128(DA24127, DA24130, DA24131) { 	if(DA24127 > DA24131) return DA24130; 	if(DA24127 < DA24130) return DA24131; 	return DA24127; 	} int DA24770,DA24771; function DA24132(DA24133,DA24134,DA24135,DA24136,DA24137,DA24138){ 	if(!DA24138){ 		print(DA24141(DA24139(DA24133),DA24136,DA24134),DA24135,DA24136,DA24137,DA24133)     	} 	else{ 		if(DA24133 < 0){ 			putc_oled(1,45); 					} 		if(DA24133){ 			for(DA24770 = DA24145(DA24133) + DA24771 = (DA24133 < 0 ),DA24133 = abs(DA24133); 			DA24133 > 0; 			DA24770-- , DA24771++){ 				putc_oled(DA24770,DA24133%10 + 48); 				DA24133 = DA24133/10; 							} 					} 		else{ 			putc_oled(1,48); 			DA24771 = 1         		} 		puts_oled(DA24141(DA24771,DA24136,DA24134),DA24135,DA24136,DA24771 ,DA24137); 			} 	} int DA24792; function DA24139(DA24140) { 	DA24792 = 0; 	do { 		DA24140++; 		DA24792++; 			} 	while (duint8(DA24140)); 	return DA24792; 	} function DA24141(DA24142,DA24136,DA24134) { 	if(DA24134 == -3){ 		return 128 - ((DA24142 * (7 + (DA24136 > 1) + DA24136 * 4)) + 3 ); 			} 	if(DA24134 == -2){ 		return 64 - ((DA24142 * (7 + (DA24136 > 1) + DA24136 * 4)) / 2); 			} 	if(DA24134 == -1){ 		return 3 	} 	return DA24134; 	} function DA24145(DA24146) { 	for(DA24770 = 1; 	DA24770 < 11; 	DA24770++){ 		if(!(abs(DA24146) / pow(10,DA24770))){ 			return DA24770; 			break; 					} 			} 	return 1; 	} function DA24147() { 	if (get_ival(DA24448)) { 		set_val(DA24448, 0); 		if (get_ival(DA24450)) DA24799 = 50; 		if (!get_ival(DA24450)) DA24799 = 410; 		combo_run(DA2471); 			} 	if (DA24798 > 0) set_polar(POLAR_LS, DA24798 * -1, 32767); 	if (get_ival(PS5_RIGHT) && get_ival(PS5_DOWN)) DA24798 = 345; 	if (get_ival(PS5_RIGHT) && get_ival(PS5_UP)) DA24798 = 45; 	if (get_ival(PS5_LEFT) && get_ival(PS5_UP)) DA24798 = 135; 	if (get_ival(PS5_LEFT) && get_ival(PS5_DOWN)) DA24798 = 225; 	if (event_press(PS5_LEFT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA24798 = 180; 	if (event_press(PS5_RIGHT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA24798 = 1; 	if (event_press(PS5_UP) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA24798 = 90; 	if (event_press(PS5_DOWN) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA24798 = 270; } int DA24799; int DA24559; int DA24798; combo DA2471 { 	set_val(DA24448, 100); 	wait( DA24799); 	wait( 50); 	wait( 3800); 	DA24559 = !DA24559; } define DA24802 = 19; function DA24148(DA24149, DA24150) { 	if (DA24355 == DA24150) { 		if (event_press(PS5_RIGHT)) { 			DA24149 = clamp(DA24149 + 1, 0, DA24805[DA24355]); 			DA24360 = TRUE; 					} 		if (event_press(PS5_LEFT)) { 			DA24149 = clamp(DA24149 - 1, 0, DA24805[DA24355]); 			DA24360 = TRUE; 					} 		if (DA24355 == 0) { 			print(DA24228(DA24181(DA24809[DA24361]) ,OLED_FONT_SMALL_WIDTH),DA24802  ,OLED_FONT_SMALL , OLED_WHITE ,DA24809[DA24361]); 					} 		else if (DA24355 == 1) { 			print(DA24228(DA24181(DA24811[DA24362]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24811[DA24362]); 					} 		else if (DA24355 == 2) { 			print(DA24228(DA24181(DA24811[DA24363]) ,OLED_FONT_SMALL_WIDTH ),DA24802  ,OLED_FONT_SMALL , OLED_WHITE ,DA24811[DA24363]); 					} 		else if (DA24355 == 5) { 			print(DA24228(DA24181(DA24815[DA24366]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24815[DA24366]); 					} 		else if (DA24355 == 6) { 			print(DA24228(DA24181(DA24817[DA24367]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24367]); 					} 		else if (DA24355 == 7) { 			print(DA24228(DA24181(DA24817[DA24368]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24368]); 					} 		else if (DA24355 == 8) { 			print(DA24228(DA24181(DA24817[DA24369]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24369]); 					} 		else if (DA24355 == 9) { 			print(DA24228(DA24181(DA24817[DA24370]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24370]); 					} 		else if (DA24355 == 20) { 			print(DA24228(DA24181(DA24825[DA24110]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24825[DA24110]); 					} 		else { 			if (DA24149 == 1)        print(DA24228(DA24181(DA24827[1]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24827[1])      else        print(DA24228(DA24181(DA24827[0]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24827[0])     		} 			} 	return DA24149; 	} function DA24151(DA24149, DA24150) { 	if (DA24356 == DA24150) { 		if (get_ival(PS5_L2)) { 			if (event_press(PS5_RIGHT)) { 				DA24149 += DA24833[DA24356][2]  				        DA24360 = TRUE; 							} 			if (event_press(PS5_LEFT)) { 				DA24149 -= DA24833[DA24356][2]  				        DA24360 = TRUE; 							} 			DA24149 = clamp(DA24149, DA24833[DA24356][0], DA24833[DA24356][1]); 		} 		if (DA24356 == 8) { 			print(DA24228(DA24181(DA24817[DA24387]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24387])     		} 		else if (DA24356 == 9) { 			print(DA24228(DA24181(DA24817[DA24388]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24388])     		} 		else if (DA24356 == 10) { 			print(DA24228(DA24181(DA24817[DA24389]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24389])     		} 		else if (DA24356 == 11) { 			print(DA24228(DA24181(DA24817[DA24390]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24390])     		} 		else if (DA24356 == 12) { 			print(DA24228(DA24181(DA24817[DA24391]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24391])     		} 		else if (DA24356 == 13) { 			print(DA24228(DA24181(DA24817[DA24392]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24392])     		} 		else if (DA24356 == 14) { 			print(DA24228(DA24181(DA24817[DA24393]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24393])     		} 		else if (DA24356 == 15) { 			print(DA24228(DA24181(DA24817[DA24394]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24394])     		} 		else if (DA24356 == 16) { 			print(DA24228(DA24181(DA24852[DA24395]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24395])     		} 		else if (DA24356 == 17) { 			print(DA24228(DA24181(DA24817[DA24276]),OLED_FONT_SMALL_WIDTH ),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24817[DA24276])  		} 		else if(DA24356 == 18){ 			print(DA24228(DA24181(DA24817[DA24277]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24817[DA24277])  		} 		else if(DA24356 == 19){ 			print(DA24228(DA24181(DA24817[DA24278]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24817[DA24278])  		} 		else if(DA24356 == 20){ 			print(DA24228(DA24181(DA24817[DA24279]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24817[DA24279])  		} 		else if(DA24356 == 21){ 			print(DA24228(DA24181(DA24852[DA24280]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24852[DA24280])       		} 		else if(DA24356 == 22){ 			print(DA24228(DA24181(DA24817[DA24408]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24408])     		} 		else if (DA24356 == 23) { 			print(DA24228(DA24181(DA24817[DA24409]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24409])     		} 		else if (DA24356 == 24) { 			print(DA24228(DA24181(DA24817[DA24410]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24410])     		} 		else if (DA24356 == 25) { 			print(DA24228(DA24181(DA24817[DA24411]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24411])     		} 		else if (DA24356 == 26) { 			print(DA24228(DA24181(DA24817[DA24412]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24817[DA24412])     		} 		else if (DA24356 == 27) { 			print(DA24228(DA24181(DA24852[DA24413]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24413])     		} 		else if (DA24356 == 28) { 			print(DA24228(DA24181(DA24876[DA24414]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24876[DA24414])     		} 		else if (DA24356 == 29) { 			print(DA24228(DA24181(DA24878[DA24415]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24878[DA24415])     		} 		else if (DA24356 == 30) { 			print(DA24228(DA24181(DA24852[DA24416]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24416])     		} 		else if (DA24356 == 31) { 			print(DA24228(DA24181(DA24876[DA24417]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24876[DA24417])     		} 		else if (DA24356 == 32) { 			print(DA24228(DA24181(DA24878[DA24418]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24878[DA24418])     		} 		else if (DA24356 == 33) { 			print(DA24228(DA24181(DA24852[DA24419]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24419])     		} 		else if (DA24356 == 34) { 			print(DA24228(DA24181(DA24876[DA24420]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24876[DA24420])     		} 		else if (DA24356 == 35) { 			print(DA24228(DA24181(DA24878[DA24421]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24878[DA24421])     		} 		else if (DA24356 == 36) { 			print(DA24228(DA24181(DA24852[DA24422]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24422])     		} 		else if (DA24356 == 37) { 			print(DA24228(DA24181(DA24876[DA24423]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24876[DA24423])     		} 		else if (DA24356 == 38) { 			print(DA24228(DA24181(DA24878[DA24424]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24878[DA24424])     		} 		else if (DA24356 == 41) { 			print(DA24228(DA24181(DA24852[DA24427]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24427])     		} 		else if (DA24356 == 48) { 			print(DA24228(DA24181(DA24852[DA24434]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24434])     		} 		else if (DA24356 == 49) { 			print(DA24228(DA24181(DA24852[DA24435]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24435])     		} 		else if (DA24356 == 50) { 			print(DA24228(DA24181(DA24852[DA24436]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24436])     		} 		else if (DA24356 == 51) { 			print(DA24228(DA24181(DA24852[DA24437]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24852[DA24437])     		} 		else if(DA24356 == 0){ 			print(DA24228(DA24181(DA24908[DA24440]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24440])  		} 		else if(DA24356 == 1){ 			print(DA24228(DA24181(DA24908[DA24441]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24441])  		} 		else if(DA24356 == 2){ 			print(DA24228(DA24181(DA24908[DA24442]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24442])  		} 		else if(DA24356 == 3){ 			print(DA24228(DA24181(DA24908[DA24443]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24443])  		} 		else if(DA24356 == 4){ 			print(DA24228(DA24181(DA24908[DA24444]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24444])  		} 		else if(DA24356 == 5){ 			print(DA24228(DA24181(DA24908[DA24445]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24445])  		} 		else if(DA24356 == 6){ 			print(DA24228(DA24181(DA24908[DA24446]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24446])  		} 		else if(DA24356 == 7){ 			print(DA24228(DA24181(DA24908[DA24447]),OLED_FONT_SMALL_WIDTH),DA24802,OLED_FONT_SMALL,OLED_WHITE,DA24908[DA24447])  		} 		else{ 			if (DA24149 == 1)        print(DA24228(DA24181(DA24827[1]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24827[1])      else        print(DA24228(DA24181(DA24827[0]), OLED_FONT_SMALL_WIDTH), DA24802, OLED_FONT_SMALL, OLED_WHITE, DA24827[0])     		} 		DA24184(0); 			} 	return DA24149; 	} function DA24154(DA24149, DA24150) { 	if (DA24356 == DA24150) { 		if (get_ival(PS5_L2)) { 			if (event_press(PS5_RIGHT)) { 				DA24149 += DA24833[DA24356][2]  				        DA24360 = TRUE; 							} 			if (event_press(PS5_LEFT)) { 				DA24149 -= DA24833[DA24356][2]  				        DA24360 = TRUE; 							} 			if (event_press(PS5_UP)) { 				DA24149 += DA24833[DA24356][3]  				        DA24360 = TRUE; 							} 			if (event_press(PS5_DOWN)) { 				DA24149 -= DA24833[DA24356][3]  				        DA24360 = TRUE; 							} 			DA24149 = clamp(DA24149, DA24833[DA24356][0], DA24833[DA24356][1]); 		} 		DA24231(DA24149, DA24234(DA24149)); 	} 	return DA24149; 	} int DA24935, DA24936, DA24937; function DA24157(DA24127, DA24159, DA24160, DA24161, DA24136) { 	DA24936 = 1; 	DA24937 = 10000; 	if (DA24127 < 0)  	  { 		putc_oled(DA24936, 45); 		DA24936 += 1; 		DA24127 = abs(DA24127); 			} 	for (DA24935 = 5; 	DA24935 >= 1; 	DA24935--) { 		if (DA24159 >= DA24935) { 			putc_oled(DA24936, DA24943[DA24127 / DA24937]); 			DA24127 = DA24127 % DA24937; 			DA24936 += 1; 					} 		DA24937 /= 10; 			} 	puts_oled(DA24160, DA24161, DA24136, DA24936 - 1, OLED_WHITE); } const string DA24576 = " No Edit Variable"; const string DA24575 = " A/CROSS to Edit "; const string DA24571 = "MOD;"; const string DA24573 = "MSL;"; int DA24947; function DA24163(DA24146) { 	DA24146 = abs(DA24146); 	if (DA24146 / 10000 > 0) return 5; 	if (DA24146 / 1000 > 0) return 4; 	if (DA24146 / 100 > 0) return 3; 	if (DA24146 / 10 > 0) return 2; 	return 1; 	} const int8 DA24943[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int DA24949, DA24950; const image DA24952 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DA2472 { 	call(DA2473); 	DA24180(); 	wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, DA24952[0]); 	wait( get_rtime()); 	wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DA24952[0]); 	wait( get_rtime()); 	wait( 1000)call(DA2474); 	wait( 1000); 	DA24357 = TRUE; 	} combo DA2473 { 	cls_oled(OLED_BLACK); 	} const int8 DA241381[] = { 	OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT  } const int8 DA241382[] = { 	OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH  } int DA24954; function DA24165(DA24166, DA24167, DA24168, DA24169) { 	DA24954--; 	switch(DA24166) { 		case DA24968 { 			DA24166 = OLED_WIDTH - (DA24954 * DA241382[DA24168]) - 4; 			break; 					} 		case DA24967 { 			DA24166 = (OLED_WIDTH >> 1) - ((DA24954 * DA241382[DA24168]) >> 1); 			break; 					} 	} 	switch(DA24167) { 		case DA24967 { 			DA24167 = (OLED_HEIGHT >> 1) - (DA241381[DA24168] >> 1); 			break; 					} 		case DA24970 { 			DA24167 = OLED_HEIGHT - DA241381[DA24168] - 4; 			break; 					} 	} 	puts_oled(DA24166, DA24167, DA24168, DA24954, DA24169); 	DA24954 = 1; } enum { 	DA24967 = -2, DA24968, DA24969 = 5, DA24970 = -1, DA24971 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo DA2474 { 	wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	wait( 720); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	wait( 720); 	reset_rumble(); 	wait( 1560); 	} function DA24170(DA24166, DA24167, DA24173, DA24168, DA24169) { 	DA24178(DA24173); 	DA24165(DA24166, DA24167, DA24168, DA24169); 	} function DA24176(DA24177) { 	putc_oled(DA24954, DA24177); 	DA24954++; 	} function DA24178(DA24179) { 	do { 		DA24176(dint8(DA24179)); 		DA24179++; 	} 	while(dint8(DA24179))  } const int16 DA241383[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DA241384[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DA241385[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 DA241386[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int DA24983; int DA24984; int DA24985; int DA24986; int DA24987; int DA24988; int DA24989; function DA24180() { 	DA24989 = 3; 	DA24987 = DA24989 * DA241386[DA24988]; 	DA24986 = DA24989 * DA241383[DA24988]; 	DA24984 = ((DA24987 * DA241385[DA24983]) / 328) - ((DA24986 * DA241384[DA24983]) / 328); 	DA24985 = ((DA24987 * DA241384[DA24983]) / 328) + ((DA24986 * DA241385[DA24983]) / 328); 	DA24987 = DA24984; 	DA24986 = DA24985; 	DA24988 += 1; 	DA24983 += 45; 	if(DA24988 >= 360) { 		DA24988 %= 360; 			} 	if(DA24983 >= 360) { 		DA24983 %= 360; 			} 	pixel_oled(64 + (((DA24987 / DA24989) * 30) / 328), 32 + (((DA24986 / DA24989) * 30) / 328), OLED_WHITE); 	} int DA24993; function DA24181(DA24140) { 	DA24993 = 0; 	do { 		DA24140++; 		DA24993++; 			} 	while (duint8(DA24140)); 	return DA24993; 	} int DA24996; const uint8 DA241387[] = { 	PS5_OPTIONS,  PS5_LEFT,  PS5_RIGHT,  PS5_UP,  PS5_DOWN,  PS5_CROSS,  PS5_CIRCLE,  PS5_SQUARE,  PS5_TRIANGLE,  PS5_R3,  PS5_L3,  PS5_RX,  PS5_RY,  PS5_PS,  PS5_TOUCH,  PS5_SHARE } ; function DA24183() { 	for (DA24996 = 0; 	DA24996 < sizeof(DA241387) / sizeof(DA241387[0]); 	DA24996++) { 		if (get_ival(DA241387[DA24996]) || event_press(DA241387[DA24996])) { 			set_val(DA241387[DA24996], 0); 		} 			} 	} define DA24997 = 131; define DA24998 = 132; define DA24999 = 133; define DA241000 = 134; define DA241001 = 130; define DA241002 = 89; define DA241003 = 127; define DA241004 = 65; int DA241005; int DA241006; int DA241007 = 1; define DA241008 = 36; const string DA241009 = "Hold LT/L2 +"; function DA24184(DA24185) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, DA241009[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, DA241007); 	putc_oled(1, DA24999); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DA241005); 	putc_oled(1, DA241000); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DA241006); 	if (DA24185) { 		putc_oled(1, DA24997); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DA241005); 		putc_oled(1, DA24998); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DA241006); 			} 	} const uint8 DA241389 [] = { 	  PS5_R1,        	  PS5_R2,        	  PS5_R3,        	  PS5_L1,        	  PS5_L2,        	  PS5_L3,        	  PS5_TRIANGLE,  	  PS5_CIRCLE,    	  PS5_CROSS,     	  PS5_SQUARE     } ; function DA24186() { 	DA241019 = sizeof(data); 	DA24509 = get_pvar(SPVAR_1,0,1,0); 	DA24515 = get_pvar(SPVAR_2,0,1,0); 	DA24508 = get_pvar(SPVAR_3,11111, 99999,11111); 	DA24188(); 	if (DA24213(0, 1, 0)) { 		DA24366 = DA24213(  0, 6, 0); 		DA24363 = DA24213(0, 3, 0); 		DA24364 = DA24213(0,1,0); 		DA24365 = DA24213(0,1,0); 		DA24276 = DA24213(0, 70, 0); 		DA24277 = DA24213(0, 70, 0); 		DA24278 = DA24213(0, 70, 0); 		DA24279 = DA24213(0, 70, 0); 		DA24280 = DA24213(0, 22, 8); 		DA24367 = DA24213(0, 70, 0); 		DA24368 = DA24213(0, 70, 0); 		DA24369 = DA24213(0, 70, 0); 		DA24370 = DA24213(0, 70, 0); 		DA24371 = DA24213(0, 1, 0); 		DA24372 = DA24213(0, 1, 0); 		DA24373 = DA24213(0, 1, 0); 		DA24374 = DA24213(0, 1, 0); 		DA24382 = DA24213(0, 1, 0); 		DA24408 = DA24213(0, 70, 0); 		DA24409 = DA24213(0, 70, 0); 		DA24410 = DA24213(0, 70, 0); 		DA24411 = DA24213(0, 70, 0); 		DA24412 = DA24213(0, 70, 0); 		DA24413 = DA24213(1, 25, 1); 		DA24414 = DA24213(0, 1, 0); 		DA24415 = DA24213(0, 1, 0); 		DA24416 = DA24213(1, 25, 5); 		DA24417 = DA24213(0, 1, 0); 		DA24418 = DA24213(0, 1, 0); 		DA24419 = DA24213(0, 25, 2); 		DA24420 = DA24213(0, 1, 0); 		DA24421 = DA24213(0, 1, 1); 		DA24422 = DA24213(1, 25, 8); 		DA24423 = DA24213(0, 1, 0); 		DA24424 = DA24213(0, 1, 1); 		DA24425 = DA24213(350, 600, 350); 		DA24426 = DA24213(350, 600, 445); 		DA24427 = DA24213(0, 22, 0); 		DA24428 = DA24213(0, 1, 0); 		DA24429 = DA24213(-100, 300, 0); 		DA24375 = DA24213(0, 1, 0); 		DA24376 = DA24213(0, 1, 0); 		DA24377 = DA24213(0, 1, 0); 		DA24378 = DA24213(0, 1, 0); 		DA24379 = DA24213(0, 1, 0); 		DA24430 = DA24213(-150, 150, 0); 		DA24431 = DA24213(-150, 150, 0); 		DA24432 = DA24213(0, 1, 0); 		DA24433 = DA24213(-150, 150, 0); 		DA24434 = DA24213(0, 22, 0); 		DA24435 = DA24213(0, 22, 0); 		DA24436 = DA24213(0, 22, 0); 		DA24437 = DA24213(0, 22, 0); 		DA24612 = DA24213(60, 400, 235); 		DA24439 = DA24213(0, 1, 0); 		DA24438 = DA24213(0, 1, 0); 		DA24362 = DA24213(0, 3, 0); 		DA24387 = DA24213(0, 70, 0); 		DA24388 = DA24213(0, 70, 0); 		DA24389 = DA24213(0, 70, 0); 		DA24392 = DA24213(0, 70, 0); 		DA24393 = DA24213(0, 70, 0); 		DA24394 = DA24213(0, 70, 0); 		DA24395 = DA24213(0, 22, 8); 		DA24380 = DA24213(0, 1, 0); 		DA24390 = DA24213(0, 70, 0); 		DA24391 = DA24213(0, 70, 0); 		DA24595 = DA24213(50, 2000, 1100); 		DA241263 = DA24213(0, 1, 0); 		DA241256 = DA24213(0, 1, 0); 		DA24110 = DA24213(0, 6, 0); 		DA24407 = DA24213(0, 1, 0); 		DA24361 = DA24213(0, 2, 0); 		DA24440 = DA24213(0, 9, 9); 		DA24441 = DA24213(0, 9, 8); 		DA24442 = DA24213(0, 9, 3); 		DA24443 = DA24213(0, 9, 1); 		DA24444 = DA24213(0, 9, 4); 		DA24445 = DA24213(0, 9, 0); 		DA24446 = DA24213(0, 9, 7); 		DA24447 = DA24213(0, 9, 6); 		DA24383    = DA24213(0, 1, 0); 		DA24384    = DA24213(0, 1, 0); 		DA24385     = DA24213(0, 1, 0); 		DA24396     = DA24213(60, 500, 120); 		DA24397     = DA24213(60, 500, 350); 		DA24398    = DA24213(0, 1, 0); 		DA24399 = DA24213(0, 1, 0); 		DA24400     = DA24213(50, 250, 80); 		DA24401     = DA24213(100, 850, 180); 		DA24402 = DA24213(0, 1, 0); 		DA24403    = DA24213(0, 1, 0); 		DA24404        = DA24213(80, 500, 120); 		DA24405        = DA24213(80, 500, 350); 		DA24406       = DA24213(0, 1, 0); 		DA24456           = DA24213(0, 2500, 750); 		DA2429           = DA24213(0, 1, 0); 		DA24476         = DA24213(0, 1, 0); 		DA24474       = DA24213(0, 1, 0); 	} 	else{ 		DA24366 = 0; 		DA24363 = 0; 		DA24364 = 0; 		DA24365 = 0; 		DA24276 = 0; 		DA24277 = 0; 		DA24278 = 0; 		DA24279 = 0; 		DA24280 = 8; 		DA24367 = 0; 		DA24368 = 0; 		DA24369 = 0; 		DA24370 = 0; 		DA24371 = 0; 		DA24372 = 0; 		DA24373 = 0; 		DA24374 = 0; 		DA24382 = 0; 		DA24408 = 0; 		DA24409 = 0; 		DA24410 = 0; 		DA24411 = 0; 		DA24412 = 0; 		DA24413 = 1; 		DA24414 = 0; 		DA24415 = 0; 		DA24416 = 5; 		DA24417 = 0; 		DA24418 = 0; 		DA24419 = 2; 		DA24420 = 0; 		DA24421 = 1; 		DA24422 = 8; 		DA24423 = 0; 		DA24424 = 1; 		DA24425 = 350; 		DA24426 = 445; 		DA24427 = 0; 		DA24428 = 0; 		DA24429 = 0; 		DA24375 = 0; 		DA24376 = 0; 		DA24377 = 0; 		DA24378 = 0; 		DA24379 = 0; 		DA24430 = 0; 		DA24431 = 0; 		DA24432 = 0; 		DA24433 = 0; 		DA24434 = 0; 		DA24435 = 0; 		DA24436 = 0; 		DA24437 = 0; 		DA24612 = 235; 		DA24439 = 0; 		DA24438 = 0; 		DA24362 = 0; 		DA24387 = 0; 		DA24388 = 0; 		DA24389 = 0; 		DA24392 = 0; 		DA24393 = 0; 		DA24394 = 0; 		DA24395 = 8; 		DA24380 = 0; 		DA24390 = 0; 		DA24391 = 0; 		DA24595 = 1100; 		DA241263 = 0; 		DA241256 = 0; 		DA24110 = 0; 		DA24407 = 0; 		DA24361 = 0; 		DA24440 = 9; 		DA24441 = 8; 		DA24442 = 3; 		DA24443 = 1; 		DA24444 = 4; 		DA24445 = 0; 		DA24446 = 7; 		DA24447 = 6; 		DA24383 = 0; 		DA24384 = 0; 		DA24385 = 0; 		DA24396 = 120; 		DA24397 = 350; 		DA24398 = 0; 		DA24399 = 0; 		DA24400 = 80; 		DA24401 = 180; 		DA24402 = 0; 		DA24403 = 0; 		DA24404 = 120; 		DA24405 = 360; 		DA24406 = 0; 		DA24456     = 750; 		DA2429     = 0; 		DA24476     = 0; 		DA24474     = 0; 			} 	if (DA24361 == 0) { 		DA24448 = PS5_CIRCLE; 		DA24449 = PS5_CROSS; 		DA24450 = PS5_L1; 		DA24451 = PS5_R1; 		DA24452 = PS5_L2; 		DA24453 = PS5_R2; 		DA24454 = PS5_SQUARE; 		DA24455 = PS5_TRIANGLE; 			} 	else if (DA24361 == 1) { 		DA24448      = PS5_SQUARE; 		DA24449      = PS5_CROSS ; 		DA24450    = PS5_L1    ; 		DA24451  = PS5_R1; 		DA24452    = PS5_L2; 		DA24453    = PS5_R2; 		DA24454     = PS5_CIRCLE; 		DA24455  = PS5_TRIANGLE; 	} 	else if (DA24361 == 2) { 		DA24448 = DA241389[DA24440]; 		DA24449 = DA241389[DA24441]; 		DA24450 = DA241389[DA24442]; 		DA24451 = DA241389[DA24443]; 		DA24452 = DA241389[DA24444]; 		DA24453 = DA241389[DA24445]; 		DA24454 = DA241389[DA24446]; 		DA24455 = DA241389[DA24447]; 			} 	} function DA24187() { 	DA24188(); 	DA24211(   1,0,     1); 	DA24211(DA24366, 0, 6); 	DA24211(DA24363, 0, 3); 	DA24211(DA24364, 0 , 1); 	DA24211(DA24365, 0 , 1); 	DA24211(DA24276, 0, 70); 	DA24211(DA24277, 0, 70); 	DA24211(DA24278, 0, 70); 	DA24211(DA24279, 0, 70); 	DA24211(DA24280, 0, 22); 	DA24211(DA24367, 0, 70); 	DA24211(DA24368, 0, 70); 	DA24211(DA24369, 0, 70); 	DA24211(DA24370, 0, 70); 	DA24211(DA24371, 0, 1); 	DA24211(DA24372, 0, 1); 	DA24211(DA24373, 0, 1); 	DA24211(DA24374, 0, 1); 	DA24211(DA24382, 0, 1); 	DA24211(DA24408, 0, 70); 	DA24211(DA24409, 0, 70); 	DA24211(DA24410, 0, 70); 	DA24211(DA24411, 0, 70); 	DA24211(DA24412, 0, 70); 	DA24211(DA24413, 1, 25); 	DA24211(DA24414, 0, 1); 	DA24211(DA24415, 0, 1); 	DA24211(DA24416, 1, 25); 	DA24211(DA24417, 0, 1); 	DA24211(DA24418, 0, 1); 	DA24211(DA24419, 0, 25); 	DA24211(DA24420, 0, 1); 	DA24211(DA24421, 0, 1); 	DA24211(DA24422, 1, 25); 	DA24211(DA24423, 0, 1); 	DA24211(DA24424, 0, 1); 	DA24211(DA24425, 350, 600); 	DA24211(DA24426, 350, 600); 	DA24211(DA24427, 0, 22); 	DA24211(DA24428, 0, 1); 	DA24211(DA24429, -100, 300); 	DA24211(DA24375, 0, 1); 	DA24211(DA24376, 0, 1); 	DA24211(DA24377, 0, 1); 	DA24211(DA24378, 0, 1); 	DA24211(DA24379, 0, 1); 	DA24211(DA24430, -150, 150); 	DA24211(DA24431, -150, 150); 	DA24211(DA24432, 0, 1); 	DA24211(DA24433, -150, 150); 	DA24211(DA24434, 0, 22); 	DA24211(DA24435, 0, 22); 	DA24211(DA24436, 0, 22); 	DA24211(DA24437, 0, 22); 	DA24211(DA24612, 60, 400); 	DA24211(DA24439, 0, 1); 	DA24211(DA24438, 0, 1); 	DA24211(DA24362, 0, 3); 	DA24211(DA24387, 0, 70); 	DA24211(DA24388, 0, 70); 	DA24211(DA24389, 0, 70); 	DA24211(DA24392, 0, 70); 	DA24211(DA24393, 0, 70); 	DA24211(DA24394, 0, 70); 	DA24211(DA24395, 0, 22); 	DA24211(DA24380, 0, 1); 	DA24211(DA24390, 0, 70); 	DA24211(DA24391, 0, 70); 	DA24211(DA24595, 50, 2000); 	DA24211(DA241263, 0, 1); 	DA24211(DA241256, 0, 1); 	DA24211(DA24110, 0, 6); 	DA24211(DA24407, 0, 1); 	DA24211(DA24361, 0, 2); 	DA24211(DA24440, 0, 9); 	DA24211(DA24441, 0, 9); 	DA24211(DA24442, 0, 9); 	DA24211(DA24443, 0, 9); 	DA24211(DA24444, 0, 9); 	DA24211(DA24445, 0, 9); 	DA24211(DA24446, 0, 9); 	DA24211(DA24447, 0, 9); 	DA24211(DA24383,    0, 1); 	DA24211(DA24384,    0, 1); 	DA24211(DA24385,     0, 1); 	DA24211(DA24396,     60, 500); 	DA24211(DA24397,     60, 500); 	DA24211(DA24398,    0, 1); 	DA24211(DA24399, 0, 1); 	DA24211(DA24400,     50, 250); 	DA24211(DA24401,     100, 850); 	DA24211(DA24402, 0, 1); 	DA24211(DA24403,    0, 1); 	DA24211(DA24404,        80, 500); 	DA24211(DA24405,        80, 500); 	DA24211(DA24406,       0, 1); 	DA24211(DA24456 ,         0,2500); 	DA24211(DA2429,           0,1); 	DA24211(DA24476,           0,1); 	DA24211(DA24474,           0,1); 	} function DA24188() { 	DA241026 = SPVAR_4; 	DA241027 = 0; 	DA241029 = 0; 	} int DA241027,  DA241026, DA241029, DA241030, DA241031; function DA24189(DA24190) { 	DA241030 = 0; 	while (DA24190) { 		DA241030++; 		DA24190 = abs(DA24190 >> 1); 	} 	return DA241030; 	} function DA24191(DA24192, DA24193) { 	DA241030 = max(DA24189(DA24192), DA24189(DA24193)); 	if (DA24194(DA24192, DA24193)) { 		DA241030++; 	} 	return DA241030; 	} function DA24194(DA24192, DA24193) { 	return DA24192 < 0 || DA24193 < 0; 	} function DA24197(DA24198) { 	return 1 << clamp(DA24198 - 1, 0, 31); 	} function DA24199(DA24198) { 	if (DA24198 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - DA24198); } function DA24201(DA24198) { 	return DA24199(DA24198 - 1); 	} function DA24203(DA24190, DA24198) { 	if (DA24190 < 0) { 		return (abs(DA24190) & DA24201(DA24198)) | DA24197(DA24198); 	} 	return DA24190 & DA24201(DA24198); } function DA24206(DA24190, DA24198) { 	if (DA24190 & DA24197(DA24198)) { 		return 0 - (DA24190 & DA24201(DA24198)); 	} 	return DA24190 & DA24201(DA24198); } function DA24209(DA24210) { 	return get_pvar(DA24210, 0x80000000, 0x7FFFFFFF, 0); 	} function DA24211(DA24190, min, max) { 	DA241031 = DA24191(min, max); 	DA24190 = clamp(DA24190, min, max); 	if (DA24194(min, max)) { 		DA24190 = DA24203(DA24190, DA241031); 	} 	DA24190 = DA24190 & DA24199(DA241031); 	if (DA241031 >= 32 - DA241027) { 		DA241029 = DA241029 | (DA24190 << DA241027); 		set_pvar(DA241026, DA241029); 		DA241026++; 		DA241031 -= (32 - DA241027); 		DA24190 = DA24190 >> (32 - DA241027); 		DA241027 = 0; 		DA241029 = 0; 	} 	DA241029 = DA241029 | (DA24190 << DA241027); 	DA241027 += DA241031; 	if (!DA241027) { 		DA241029 = 0; 	} 	set_pvar(DA241026, DA241029); } function DA24213(min, max, DA24214) { 	DA241031 = DA24191(min, max); 	DA241029 = (DA24209(DA241026) >> DA241027) & DA24199(DA241031); 	if (DA241031 >= 32 - DA241027) { 		DA241029 = (DA241029 & DA24199(32 - DA241027)) | ((DA24209(DA241026 + 1) & DA24199(DA241031 - (32 - DA241027))) << (32 - DA241027)); 	} 	DA241027 += DA241031; 	DA241029 = DA241029 & DA24199(DA241031); 	if (DA241027 >= 32) { 		DA241026++; 		DA241027 -= 32; 	} 	if (DA24194(min, max)) { 		DA241029 = DA24206(DA241029, DA241031); 	} 	if (DA241029 < min || DA241029 > max) { 		return DA24214; 	} 		if(DA24216[283] != 7590){ 		DA24213(min, max, DA24214); 	} 	return DA241029; 	} const string DA241057 = "SETTINGS"; const string DA241058 = "WAS SAVED"; combo DA2475 { 	wait( 20); 	cls_oled(0); 	DA24187(); 	print(15, 2, OLED_FONT_MEDIUM, 1, DA241057[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, DA241058[0]); 	DA241059 = 1500; 	combo_run(DA2476); 	} int DA241059 = 1500; combo DA2476 { 	wait( DA241059); 	cls_oled(0); 	DA24359 = FALSE; 	} define DA241060 = 0; define DA241061 = 1; define DA241062 = 2; define DA241063 = 3; define DA241064 = 4; define DA241065 = 5; define DA241066 = 6; define DA241067 = 7; int DA24691; int DA241069; int DA241070, DA24674; int DA24480; int DA241073 = 50; int DA241074 = 200; int DA241075 = TRUE; combo DA2477 { 	set_val(DA24449, 0); 	set_val(PS5_L3, 100); 	set_val(PS5_R3, 100); 	wait( 60); 	set_val(DA24449, 0); 	wait( 120); 	if (DA24428) DA24259(); 	wait( 50); 	wait( 50); 	} int DA24619; int DA24626; combo DA2478 { 	if (DA24626) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	wait( 70); 	if (DA24626) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	wait( 2000); 	if (DA24626) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	wait( 50); 	wait( 200); 	set_val(DA24448, 100); 	wait( DA24425); 	if (DA24626) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	wait( 50); 	wait( 1200); 	DA24619 = FALSE; 	DA24243(DA24619); 	} int DA24628; int DA24629; combo DA2479 { 	if (DA24629) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	wait( 320); 	wait( 50); 	set_val(XB1_RY, -60); 	wait( 1100); 	wait( 50); 	if (DA24629) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	wait( 120); 	wait( 50); 	set_val(XB1_LY, -100); 	set_val(DA24454, 100); 	set_val(DA24451, 100); 	set_val(DA24452, 100); 	DA241210 = 4000; 	wait( DA24426); 	wait( 50); 	set_val(DA24454, 100); 	wait( 50); 	DA24628 = FALSE; 	DA24243(DA24628); 	} int DA241080 = TRUE; function DA24215(DA24216) { 	if (DA24216) { 		DA241081 = DA241113; 			} 	else { 		DA241081 = DA241112; 			} 	combo_run(DA2480); 	} int DA241081; combo DA2480 { 	DA24236(DA241081); 	wait( 300); 	DA24236(DA241110); 	wait( 100); 	DA24236(DA241081); 	wait( 300); 	DA24236(DA241110); 	} define DA241085 = 100; define DA241086 = 130; const string DA24555 = "SCRIPT WAS"; function DA24217(DA24127, DA24219, DA24220) { 	if (!DA24353 && !DA24354) { 		cls_oled(0); 		print(DA24219, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA24220); 		if (DA24127) { 			print(DA24221(sizeof(DA241090) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA241090[0]); 		} 		else { 			print(DA24221(sizeof(DA241091) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA241091[0]); 		} 		DA24215(DA24127); 			} 	} function DA24221(DA24142, DA24136) { 	return (OLED_WIDTH / 2) - ((DA24142 * DA24136) / 2); 	} const string DA241091 = "OFF"; const string DA241090 = "ON"; function DA24224(DA24133, DA24226, DA24127) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(DA24133, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA24226); 	DA24231(DA24127, DA24234(DA24127)); 	DA24357 = TRUE; 	} const string DA24599 = "EA PING"; const string DA24621 = "FK_POWER"; const string DA24613 = "MaxFnshPwr"const string DA24605 = "JK_Agg"; int DA24595; int DA24612; function DA24228(DA24142, DA24136) { 	return (OLED_WIDTH / 2) - ((DA24142 * DA24136) / 2); 	} int DA241100; int DA241101, DA241102; function DA24231(DA24127, DA24159) { 	DA241100 = 1; 	DA241102 = 10000; 	if (DA24127 < 0) { 		putc_oled(DA241100, 45); 		DA241100 += 1; 		DA24127 = abs(DA24127); 			} 	for (DA241101 = 5; 	DA241101 >= 1; 	DA241101--) { 		if (DA24159 >= DA241101) { 			putc_oled(DA241100, (DA24127 / DA241102) + 48); 			DA24127 %= DA241102; 			DA241100++; 			if (DA241101 == 4) { 				putc_oled(DA241100, 44); 				DA241100++; 							} 					} 		DA241102 /= 10; 			} 	puts_oled(DA24228(DA241100 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA241100 - 1, OLED_WHITE); 	} int DA241106; function DA24234(DA24235) { 	DA241106 = 0; 	do { 		DA24235 /= 10; 		DA241106++; 			} 	while (DA24235); 	return DA241106; 	} int DA24635; define DA241110 = 0; define DA241111 = 1; define DA241112 = 2; define DA241113 = 3; define DA241114 = 4; define DA241115 = 5; define DA241116 = 6; define DA241117 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int DA241118; function DA24236(DA24169) { 	for (DA241118 = 0; 	DA241118 < 3; 	DA241118++) { 		set_rgb(data[DA24169][0], data[DA24169][1], data[DA24169][2]); 			} 	} const int8 DA241399[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS5_R1,  	  PS5_R2,  	  XB1_RS,  	  PS5_L1,  	  PS5_L2,  	  XB1_LS,  	  PS5_UP,  	  PS5_DOWN,  	  PS5_LEFT,  	  PS5_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS5_TOUCH  } int DA24638 = PS5_L3; define DA241120 = 1; define DA241121 = 2; define DA241122 = 3; define DA241123 = 2; define DA241124 = 3; define DA241125 = 4; define DA241126 = 5; define DA241127 = 6; define DA241128 = 7; define DA241129 = 8; define DA241130 = 9; int DA24632 = FALSE; int DA241132; int DA241133; int DA241134; int DA241135; define DA241136 = PS5_LX; define DA241137 = PS5_LY; define DA241138 = PS5_RX; define DA241139 = PS5_RY; function DA24238 () { 	if(!get_ival(XB1_RS) &&  !get_ival(DA24452) && !get_ival(DA24453) && !get_ival(DA24451)) { 		if( get_ival(PS5_RY) < -70  && !DA241132 && !combo_running(DA240) ) { 			DA241132 = TRUE; 			DA24480 = FALSE; 			DA241069 = DA24276; 			            DA24241(DA24276); 		} 		if( get_ival(PS5_RY) >  70  && !DA241133 && !combo_running(DA240)) { 			DA241133 = TRUE; 			DA24480 = TRUE; 			DA241069 = DA24278; 			           DA24241(DA24278); 		} 		if( get_ival(PS5_RX) < -70  && !DA241134 && !combo_running(DA240) ) { 			DA241134 = TRUE; 			DA24480 = FALSE; 			DA241069 = DA24279; 			              DA24241(DA24279); 		} 		if( get_ival(PS5_RX) >  70  && !DA241135 && !combo_running(DA240) ) { 			DA241135 = TRUE; 			DA24480 = TRUE; 			DA241069 = DA24277; 			            DA24241(DA24277); 		} 			} 	if(abs(get_ival(PS5_RY))<20  && abs(get_ival(PS5_RX))<20){ 		DA241132 = 0; 		DA241133  = 0; 		DA241134  = 0; 		DA241135  = 0; 			} 	} function DA24239() { 	if (DA24503 == DA24597) { 		DA24480 = FALSE; 		if (DA24387) DA24241(DA24387); 			} 	if (DA24503 == DA24246(DA24597 + 4)) { 		DA24480 = FALSE; 		if (DA24394) DA24241(DA24394); 			} 	if (DA24503 == DA24246(DA24597 + 1)) { 		DA24480 = TRUE; 		if (DA24389) DA24241(DA24389); 			} 	if (DA24503 == DA24246(DA24597 - 1)) { 		DA24480 = FALSE; 		if (DA24388) DA24241(DA24388); 			} 	if (DA24503 == DA24246(DA24597 + 2)) { 		DA24480 = TRUE; 		if (DA24391) DA24241(DA24391); 			} 	if (DA24503 == DA24246(DA24597 - 2)) { 		DA24480 = FALSE; 		if (DA24390) DA24241(DA24390); 			} 	if (DA24503 == DA24246(DA24597 + 3)) { 		DA24480 = TRUE; 		if (DA24393) DA24241(DA24393); 			} 	if (DA24503 == DA24246(DA24597 - 3)) { 		DA24480 = FALSE; 		if (DA24392) DA24241(DA24392); 			} 	} int DA241153; int DA24501 = 0; function DA24240() { 	if(DA241153){ 		DA24501 += get_rtime(); 			} 	if(DA24501 >= 3000){ 		DA24501 = 0; 		DA241153 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA24452) && !get_ival(DA24453) && !get_ival(DA24451) && !get_ival(DA24450)) { 		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA24504 && !combo_running(DA240)) { 			DA24504 = TRUE; 			DA241153 = TRUE; 			DA24501 = 0; 			DA24503 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA24239(); 					} 		set_val(DA241138, 0); 		set_val(DA241139, 0); 			} 	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 4000) { 		DA24504 = FALSE; 			} 	} function DA24241(DA24242) { 	DA241069 = DA24242; 	DA24216[-327 + (DA24242 * 3)] = TRUE; 	DA241075 = FALSE; 	block = TRUE; 	} int DA241160; combo DA2481 { 	set_rumble(DA241160, 100); 	wait( 300); 	reset_rumble(); 	wait( 20); 	} function DA24243(DA24127) { 	if (DA24127) DA241160 = RUMBLE_A; 	else DA241160 = RUMBLE_B; 	combo_run(DA2481); 	} int DA241161 = 300; int DA241162 ; combo DA2482 { 	DA241162 = TRUE; 	wait( DA241161); 	DA241162 = FALSE; 	} combo DA2483 { 	DA24245(); 	DA24253(0, 0); 	wait( 20); 	DA24253(0, 0); 	wait( 100); 	DA24253(0, 0); 	set_val(DA24453, 100); 	DA24253(0, 0); 	wait( 60); 	DA24253(0, 0); 	wait( 150); 	DA241075 = TRUE; 	wait( 350); 	} function DA24245() { 	DA24691 = DA24597  DA24248(DA24691); 	DA241070 = DA241166; 	DA24674 = DA24677; 	} combo DA2484 { 	set_val(DA24452, 100); 	set_val(DA24451, 100); 	wait( 100); 	set_val(DA24452, 100); 	wait( 100); 	DA241075 = TRUE; 	wait( 350); 	} const int8 DA241400[][] = { { 		0,    -99   	} 	,  	  { 		98,    -100   	} 	,  	  { 		97,    0   	} 	,  	  { 		96,    99   	} 	,  	  { 		0,    99   	} 	,  	  { 		-96,    98   	} 	,  	  { 		-95,    0   	} 	,  	  { 		-94,    -96   	} } ; int DA241166, DA24677, DA24597; int DA24503; int DA24504; int DA241171; function DA24246(DA24247) { 	DA241171 = DA24247; 	if (DA241171 < 0) DA241171 = 8 - abs(DA24247); 	else if (DA241171 >= 8) DA241171 = DA24247 - 8  return DA241171; 	} function DA24248(DA24249) { 	if (DA24249 < 0) DA24249 = 8 - abs(DA24249); 	else if (DA24249 >= 8) DA24249 = DA24249 - 8; 	DA241166 = DA241400[DA24249][0]; 	DA24677 = DA241400[DA24249][1]; } function DA24250(DA24251, DA24252) { 	set_val(DA241138, DA24251); 	set_val(DA241139, DA24252); 	} function DA24253(DA24166, DA24167) { 	set_val(DA241136, DA24166); 	set_val(DA241137, DA24167); 	} function DA24256() { 	if (DA24480) { 		set_val(DA241136, inv(DA24674)); 		set_val(DA241137, DA241070); 			} 	else { 		set_val(DA241136, DA24674); 		set_val(DA241137, inv(DA241070)); 			} 	} function DA24257() { 	if (DA24480) { 		set_val(DA241138, inv(DA24674)); 		set_val(DA241139, DA241070); 			} 	else { 		set_val(DA241138, DA24674); 		set_val(DA241139, inv(DA241070)); 			} 	} function DA24258() { 	if (!DA24480) { 		set_val(DA241138, inv(DA24674)); 		set_val(DA241139, DA241070); 			} 	else { 		set_val(DA241138, DA24674); 		set_val(DA241139, inv(DA241070)); 			} 	} function DA24259() { 	set_val(DA241138, DA241070); 	set_val(DA241139, DA24674); 	} function DA24260() { 	set_val(DA241138, inv(DA241070)); 	set_val(DA241139, inv(DA24674)); 	} function DA24261() { 	set_val(DA241138, 0); 	set_val(DA241139, 0); 	} int DA241187; function DA24262() { 	if ((event_press(DA24449)  ) && !combo_running(DA2485) && (DA241210  <= 0 || (DA241210 < 3000 && DA241210 > 1  )) && !get_ival(DA24453) && DA24557 > 500 &&!get_ival(DA24452) &&!get_ival(DA24448) &&!get_ival(DA24451) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA2485) ) { 		combo_run(DA2485); 			} 	if (combo_running(DA2485) && (        get_ival(DA24453) ||        get_ival(DA24452) ||        get_ival(DA24448) ||        get_ival(DA24451) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_ipolar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA2485); 		DA241327 = TRUE; 			} 	} combo DA2485 { wait(750); set_val(DA24450,100); wait(60); wait(60); if(DA241187 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} wait(60); wait(60); 	} combo DA2486 { 	wait( 800); 	DA241211 = 0; 	} int DA241188 = 1600; int DA241189 = 1600; int DA241190 = 1600; int DA241191 = TRUE; int DA241192 = TRUE; int DA24668 = FALSE; int DA241194 = TRUE; int DA24662 = FALSE; int DA241196 = TRUE; int DA24664 = FALSE; int DA241198 = TRUE; int DA24666 = FALSE; function DA24263(){ 	if (get_ival(DA24450)) { 		DA241200 = 1000; 		DA241219 = 0; 		DA24557 = 1; 		combo_stop(DA2495); 			} 	if (event_press(DA24452) || event_press(DA24435)) { 		DA241200 = 4000; 		DA241219 = 0; 		DA241188 = 1600; 			} 	if (get_ival(DA24451) && !get_ival(DA24450) ) { 		DA241200 = 0; 		DA241219 = 0; 		DA241188 = 1600; 			} 	else if (get_ival(DA24450)){ 		DA241200 = 1000; 			} 	if (DA241200 > 0) { 		DA241200 -= get_rtime(); 			} 	if (DA241200 < 0) { 		DA241200 = 0; 			} 	DA241280 = DA24429; 	if (event_release(DA24449)) { 		DA241206 = 1; 		DA241219 = 0; 		DA24557 = 1; 	} 	if (event_release(DA24455)) { 		DA241207 = 1; 		DA241219 = 0; 		DA24557 = 1; 	} 	if (event_release(DA24450)) { 		DA241189 = 1; 		DA241219 = 0; 		DA241188 = 1600; 			} 	if (event_release(DA24451)) { 		DA241190 = 1; 		DA241219 = 0; 		DA241188 = 1600; 			} 	if (event_release(DA24454) || (get_ival(DA24455) && get_ival(DA24450))) { 		DA241210 = 4000; 		DA241219 = 0; 	} 	if (get_ival(DA24449) && DA241210 < 4000 && DA241210 > 3500) { 		DA241211 = DA241210; 		DA241210 = 0; 			} 	if (DA241188 < 1510) { 		DA241188 += get_rtime(); 			} 	if (DA241189 < 1600) { 		DA241189 += get_rtime(); 			} 	if (DA241190 < 1600) { 		DA241190 += get_rtime(); 			} 	if (DA241210 > 0) { 		DA241210 -= get_rtime(); 			} 	if (DA241210 < 0) { 		DA241210 = 0; 			} 	if (DA241206 < 5100) { 		DA241206 += get_rtime(); 			} 	if (DA241207 < 4100) { 		DA241207 += get_rtime(); 			} 	if (DA241219 > 0) { 		DA241219 -= get_rtime(); 			} 	if (DA241219 < 0) { 		DA241219 = 0; 			} 	if (abs(get_ival(PS5_RX)) > 30 || abs(get_ival(PS5_RY)) > 30) { 		DA241188 = 1; 		DA241219 = 0; 			} 	if (combo_running(DA2492)) { 		set_val(DA24449, 0); 		if(get_ival(DA24449)){ 			DA24672 = 0; 			combo_stop(DA2487); 			set_val(DA24449, 0); 			combo_stop(DA2492); 			combo_run(DA2449); 					} 			} 	if ((combo_running(DA2497) || combo_running(DA2488))) { 		set_val(DA24449, 0); 		if(get_ival(DA24449)){ 			DA24557 = 1; 			DA24672 = 0; 			combo_stop(DA2487); 			set_val(DA24449, 0); 			combo_stop(DA2497); 			combo_stop(DA2488); 			combo_run(DA2449); 					} 			} 	if (event_press(DA24448)) { 		combo_run(DA2486); 			} 	if (DA24557 > 1500) { 		if (DA241189 < 1500) { 			DA241224 = 120; 					} 		if (DA241190 < 1500) { 			DA241224 = 228; 					} 		else { 			DA241224 = 200; 					} 			} 	if (DA24557 < 1500) { 		DA241224 = 450; 			} 	if (DA24557 > 2700) { 		DA241228 = 920; 			} 	else if (DA24557 >= 0 && DA24557 < 2700) { 		DA241228 = 725; 			} 	} function DA24264() { 	if (DA241191) { 		if ((DA24557 <= 600 || (DA241188 <= 1500 && DA241188 > 1) || ( DA241189 <= 150 || DA241190 <= 150)) && event_press(DA24448) ) { 			if (!get_ival(DA24451) && !get_ival(DA24450) && !get_ival(DA24452) && !get_ival(DA24453)) { 				set_val(DA24448, 0); 				if (DA241210 < 4000 && DA241210 > 1) { 					set_val(DA24448, 0); 					combo_run(DA2490); 									} 				else { 					set_val(DA24448, 0); 					combo_run(DA2488); 					DA241219 = 9000; 				} 							} 					} 			} 	if (DA241198) { 		if (DA24557 > 1000 && !DA241219 && (!get_ival(DA24451) && !get_ival(PS5_L3) && event_press(DA24448)) &&  DA241189 > 150 &&  DA241190 > 150) { 			if (!get_ival(DA24450) && !get_ival(DA24452)) { 				set_val(DA24448, 0); 				if (((DA241207 > 1 && DA241207 <= 2500) || (DA241206 > 1 && DA241206 <= 3000)) &&  DA241188 != 1600) { 					set_val(DA24448, 0); 					combo_run(DA2489); 					DA241219 = 9000; 									} 				else if (((DA241207 > 2500 && DA241207 <= 4000) || (DA241206 > 3000 && DA241206 <= 3500))  &&  DA241188 != 1600) { 					set_val(DA24448, 0); 					combo_run(DA2488); 					DA241219 = 9000; 									} 				else if ((DA241210 < 4000 && DA241210 > 1)) { 					set_val(DA24448, 0); 					combo_run(DA2490); 					DA241219 = 9000; 									} 				else { 					set_val(DA24448, 0); 					DA24268(); 					DA241219 = 9000; 									} 				DA241219 = 9000; 							} 					} 			} 	if (DA241192) { 		if (get_ival(DA24450) && get_ival(DA24451)) { 			if (!get_ival(DA24452) && !get_ival(DA24453) && (DA241210 && DA241206 > 1 && DA241206 <= 1500) || (!DA241210 && DA241206 > 1 && DA241206 <= 1500) || (DA241206 > 1500 && !DA241210) && !DA241219) { 				if (event_press(DA24448)) { 					set_val(DA24448, 0); 					combo_run(DA2498); 					DA241219 = 9000; 									} 							} 					} 			} 	if (DA241196) { 		if (!get_ival(DA24453) && !get_ival(DA24450) && !get_ival(DA24451)) { 			if (get_ival(DA24452) && get_ival(DA24448)) { 				DA24269(); 				set_val(DA24448, 0); 				DA241219 = 9000; 							} 					} 			} 	if (DA241194) { 		if (get_ival(DA24451) && !get_ival(DA24450) && !DA241200) { 			if (!get_ival(DA24452) && !get_ival(DA24453) && !DA241219) { 				if (get_ival(DA24448) && DA24557 >= 1000) { 					set_val(DA24448, 0); 					combo_run(DA2495); 					DA241219 = 9000; 									} 				if (get_ival(DA24448) && DA24557 < 1000 && !DA241200) { 					set_val(DA24448, 0); 					combo_run(DA2496); 									} 							} 					} 			} 	if(combo_running(DA2490)){ 		DA24672 = 0; 		combo_stop(DA2487)   	} 	if (get_ival(DA24450) || DA241200 > 0) { 		combo_stop(DA2495); 		combo_stop(DA2497); 		combo_stop(DA2496); 			} 	if (combo_running(DA2488) || combo_running(DA2492) || combo_running(DA2497) || combo_running(DA2498) || combo_running(DA2495)) { 		if (get_ival(DA24449) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA24453) > 30) { 			combo_stop(DA2492); 			combo_stop(DA2497); 			combo_stop(DA2498); 			combo_stop(DA2495); 			combo_stop(DA2488); 			DA24672 = 0; 			combo_stop(DA2487)     		} 			} 	if (combo_running(DA2488) || combo_running(DA2489)) { 		if (get_ival(DA24449) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA24453)) { 			combo_stop(DA2490); 			combo_stop(DA2489); 			combo_stop(DA2488); 			DA24672 = 0; 			combo_stop(DA2487)     		} 			} 	if (event_press(DA24448) && DA241219 > 100 && DA241219 < 8990) { 		set_val(DA24448, 0); 		combo_stop(DA2492); 		combo_stop(DA2497); 		combo_stop(DA2498); 		combo_stop(DA2495); 		combo_stop(DA2488); 		DA24672 = 0; 		combo_stop(DA2487)    combo_run(DA2491); 			} 	if (!DA24668) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2498); 					} 			} 	if (!DA24662) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2495); 					} 			} 	if (!DA24664) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2492); 			DA24672 = 0; 			combo_stop(DA2487)     		} 			} 	if (!DA24666) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA2497); 					} 			} 	if ((get_ival(DA24453) || get_ival(DA24449)) && !DA24366) { 		combo_stop(DA244); 		combo_stop(DA2447); 		combo_stop(DA2433); 			} 	} define DA241232 = 15; define DA241233 = 15; int DA241234 = 0; define DA241235 = 8000; define DA241236 = 4; define DA241237 = 2000; int DA24672 = 0; const int16 DA241401[] = { 	15, 20, 25 ,30,35    ,145,150 , 155, 160,165 ,    195, 200,205, 210,215,325  ,330, 335, 340,345 } ; const int16 DA241402[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int DA241239 = FALSE; int DA241240; int DA241241; int DA241242; int DA241243; function DA24265 () { 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA241242 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA241239 = FALSE; 		for ( DA24996 = 0; 		DA24996 < sizeof(DA241402) / sizeof(DA241402[0]); 		DA24996++) { 			if (DA241242 == DA241402[DA24996]) { 				DA241239 = TRUE; 				break; 							} 					} 		if (!DA241239) { 			DA241240 = DA241402[0]; 			DA241241 = abs(DA241242 - DA241402[0]); 			for ( DA24996 = 1; 			DA24996 < sizeof(DA241402) / sizeof(DA241402[0]); 			DA24996++) { 				DA241243 = abs(DA241242 - DA241402[DA24996]); 				if (DA241243 < DA241241) { 					DA241240 = DA241402[DA24996]; 					DA241241 = DA241243; 									} 							} 			set_polar(POLAR_LS, DA241240, 32767); 					} 			} } function DA24266 () { 	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA241242 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA241239 = FALSE; 		for ( DA24996 = 0; 		DA24996 < sizeof(DA241401) / sizeof(DA241401[0]); 		DA24996++) { 			if (DA241242 == DA241401[DA24996]) { 				DA241239 = TRUE; 				break; 							} 					} 		if (!DA241239) { 			DA241240 = DA241401[0]; 			DA241241 = abs(DA241242 - DA241401[0]); 			for ( DA24996 = 1; 			DA24996 < sizeof(DA241401) / sizeof(DA241401[0]); 			DA24996++) { 				DA241243 = abs(DA241242 - DA241401[DA24996]); 				if (DA241243 < DA241241) { 					DA241240 = DA241401[DA24996]; 					DA241241 = DA241243; 									} 							} 			set_polar(POLAR_LS, DA241240, 32767); 					} 			} } int DA241256; function DA24267() { 	if (combo_running(DA2487) && (        get_ival(DA24453) ||        get_ival(DA24448) ||        get_ival(DA24449) ||        get_ival(DA24454) ||        get_ival(DA24455) ||        get_ival(DA24450)      )) { 		combo_stop(DA2487); 		DA24672 = 0; 			} 	if (DA241234 == 0) { 		if ( ( DA241210 == 0 && !combo_running(DA2490) && !combo_running(DA2498) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( event_press(DA24448) || (DA24672 == 1 ||     combo_running(DA2496) || combo_running(DA2494)|| combo_running(DA2492) ||  combo_running(DA2495)     || combo_running(DA2488) || combo_running(DA2497) || combo_running(DA2489) ||  combo_running(DA2491)  ))     ) { 			if(DA24438)DA24266 (); 			else if (DA24439)DA24265 (); 			combo_restart(DA2487); 					} 			} 	else{ 		combo_stop(DA2487); 			} 	} combo DA2487 { 	if(DA24438)DA24266 (); 	else if (DA24439)DA24265 (); 	wait( 4000); 	DA24672 = 0; 	} combo DA2488 { 	set_val(DA24450,0); 	set_val(DA24448, 100); 	wait( random(210, 215) + DA24431); 	set_val(DA24448, 0); 	wait(600); 	wait( 2000); 	} function DA24268() { 	if (DA24557 > 600 && DA24557 <= 800) { 		DA241257 = 240; 			} 	if (DA24557 > 800 && DA24557 <= 1000) { 		DA241257 = 230; 			} 	if (DA24557 > 1000 && DA24557 <= 1500) { 		DA241257 = 225; 			} 	if (DA24557 > 1500 && DA24557 <= 2000) { 		DA241257 = 235; 			} 	if (DA24557 > 2000) { 		DA241257 = 218; 			} 	combo_run(DA2497); 	} combo DA2489 { 	set_val(DA24448, 100); 	wait( random(170, 190)); 	set_val(DA24448, 0); 	wait( 500); 	} combo DA2490 { 	set_val(DA24448, 100); 	wait( 205); 	set_val(DA24448, 0); 	wait( 300); 	} combo DA2491 { 	set_val(DA24448, 100); 	wait( 190); 	set_val(DA24448, 0); 	wait( 400); 	} int DA241262; int DA241263; int DA2429; int DA24476; int DA24474; int DA241267; int DA241268; combo DA2492 { 	if (DA241263) { 		set_val(DA24448, 0); 		DA241267 = 400; 			} 	else { 		DA241267 = 0; 			} 	if (DA241263) { 		DA24250(0, DA241268); 		DA24253(0, 0); 			} 	wait(DA241267); 	if (DA241263) { 		set_val(DA24452, 100); 		set_val(DA24451, 0); 		DA241267 = 60; 			} 	else { 		set_val(DA24451, 100); 		DA241267 = 60; 			} 	set_val(DA24448,0); 	wait(DA241267); 	set_val(DA24452, 0); 	set_val(DA24451, 0); 	set_val(DA24448,0); 	wait(DA241267); 	if (DA241263) { 		DA241267 = 0; 			} 	else { 		DA241267 = 60; 			} 	set_val(DA24451, 0); 	set_val(DA24452, 0); 	set_val(DA24448,0); 	wait(DA241267); 	set_val(DA24448, 100); 	set_val(DA24452, 100); 	wait(random(265, 268) +   DA24430 ); 	set_val(DA24452, 100); 	set_val(DA24448, 0); 	if (DA241263) { 		DA241267 = 25; 			} 	else { 		DA241267 = 30; 			} 	wait(random(0,2) + DA241281 + DA241280 + DA241267 ); 	set_val(DA24452, 100); 	set_val(DA24448, 100); 	wait(random(0,2) + 60); 	set_val(DA24448, 0); 	set_val(DA24452, 100); 	wait(random(0,2) + 80); 	set_val(DA24452, 100); 	wait(2500); 	} int DA241207; int DA241210; int DA24557; int DA241206; int DA241280; int DA241281 = 111; int DA241200; int DA241211; int DA241284; function DA24269() { 	DA241284 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DA241284 > 10 && DA241284 < 80) || (DA241284 > 110 && DA241284 < 170)) { 		DA241268 = 100; 			} 	if ((DA241284 < 350 && DA241284 > 280) || (DA241284 < 260 && DA241284 > 190)) { 		DA241268 = -100; 			} 	if (DA241210 == 0 && (DA24557 >= 750 || ((DA241211 > 3000 && DA241206 > 1 && DA241206 < 5000)))) { 		if (DA24557 <= 2000 && DA241206 > 1500) { 			set_val(DA24448, 0); 			DA241281 = 170; 		} 		if (DA24557 <= 2000 && DA241206 > 1 && DA241206 <= 1500) { 			set_val(DA24448, 0); 			DA241281 = 202; 					} 		if (DA24557 > 2000 || (DA241206 > 1 && DA241206 <= 1500)) { 			set_val(DA24448, 0); 			DA241281 = 151; 					} 		if ((DA24557 > 2000 && DA241206 > 1500) || DA241211 > 1 && DA241206 > 1) { 			set_val(DA24448, 0); 			DA241281 = 152; 					} 		if ((DA24557 < 2000 && DA241206 > 1500) || DA241210 > 1 && DA241206 > 1) { 			set_val(DA24448, 0); 			DA241281 = 149; 					} 		if (DA241206 > 1500) { 			set_val(DA24448, 0); 			DA241281 = 148; 					} 		if (!DA24557 > 2000 && DA241211 > 1 && DA241206 > 1 && DA241206 <= 1500) { 			set_val(DA24448, 0); 			DA241281 = 147; 					} 		set_val(DA24448, 0); 		combo_stop(DA2497); 		combo_stop(DA2498); 		combo_stop(DA2495); 		combo_stop(DA2488); 		combo_stop(DA2494); 		combo_stop(DA2491); 		combo_stop(DA2490); 		combo_run(DA2492); 			} 	else { 		if (DA241210) { 			set_val(DA24448, 0); 			combo_run(DA2493); 					} 		else { 			if (DA24557 < 750) { 				set_val(DA24448, 0); 				combo_run(DA2494); 							} 					} 			} } combo DA2493 { 	set_val(DA24448, 100); 	wait(random(0, 6) + random(200, 205)); 	set_val(DA24448, 0); 	wait(random(0, 6) + 700); 	} combo DA2494 { 	set_val(DA24448, 100); 	wait( random(200, 205) + DA24430 )  set_val(DA24448, 0); 	wait( 700); 	} int DA241288 = 246; int DA241224 = 150; int DA241290 = 0; combo DA2495 { 	set_val(DA24452, 100); 	set_val(DA24451, 0); 	set_val(DA24448,0); 	wait(random(0,2) + 60); 	set_val(DA24452, 0); 	set_val(DA24451, 0); 	set_val(DA24448,0); 	wait(random(0,2) + 60); 	DA241290 = DA24429; 	set_val(DA24451, 100); 	set_val(DA24448,0); 	wait( 60); 	set_val(DA24451, 100); 	set_val(DA24448, 100); 	wait( DA241288 + 10 + random(-2, 2) +  DA24433); 	set_val(DA24451, 100); 	set_val(DA24448, 0); 	DA241262 = DA241224; 	wait( DA241224 + DA241290 - 58 + DA24458 ); 	set_val(DA24451, 100); 	if(DA241256)set_val(DA24448, 100); 	wait( 60); 	set_val(DA24451, 100); 	set_val(DA24448, 0); 	wait( 3000); 	} combo DA2496 { 	set_val(DA24451, 100); 	set_val(DA24448, 100); 	wait( 160 + DA24433 ); 	set_val(DA24451, 100); 	set_val(DA24448, 0); 	wait( 3000); 	} int DA241219; int DA241292 = 220; int DA241257; int DA241294 = 0; combo DA2497 { 	DA241294 = DA24429; 	set_val(DA24448, 100); 	wait( DA241292); 	set_val(DA24448, 0); 	wait( DA241257 + (DA241294) + 22 + DA24459); 	if(DA241256)set_val(DA24448, 100); 	wait( DA241292); 	set_val(DA24448, 0); 	wait( 2000); 	} int DA241296 = TRUE; int DA241228; int DA241298 = 260; int DA241299 = 0; combo DA2498 { 	set_val(DA24450, 100); 	set_val(DA24451, 100); 	if (DA241296) { 		DA241299 = DA24429; 			} 	else { 		DA241299 = 0   	} 	set_val(DA24448, 100); 	wait( DA241298); 	wait( DA241228 + DA241299 + 40)  } int DA241302; int DA241303 = 145; combo DA2499 { 	set_val(DA24448, 100); 	set_val(DA24450, 100); 	wait( DA241303); 	wait( 500); 	} int DA24655; int DA24654; int DA24657; int DA24656; combo DA24100 { 	set_val(DA24455, 0); 	wait( 30); 	set_val(DA24455, 100); 	wait( 60); 	} int DA24658; int DA24659; combo DA24101 { 	set_val(DA24454, 100); 	wait( DA24659); 	} define DA241310 = TRUE; define DA241311 = 95; define DA241312 = 10; define DA241313 = 70; define DA241314 = FALSE; define DA241315 = 50; define DA241316 = 95; define DA241317 = XB1_LT; define DA241318 = XB1_RT; define DA241319 = XB1_LX; define DA241320 = XB1_LY; define DA241321 = POLAR_LS; int DA241322; combo DA24102 { 	set_polar(POLAR_LS,0,0); 	wait(60); 	wait(60); 	} function DA24270() { 	if (    get_ival(DA24453) > 30 &&    (get_ival(DA24452) || get_ival(DA24449)) &&    (!get_ival(DA24454) || !get_ival(DA24448))  ) { set_val(DA24453, 0); 		if(!get_ival(DA24448)){ 			set_val(DA24452,100); 					} 		else{ 			set_val(DA24452,0); 					} 		  combo_run(DA24104); 		if(DA24432 == TRUE){ 			combo_run(DA24103); 					} 			} 	else { 		combo_stop(DA24104); 		combo_stop(DA24103); 			} 	} combo DA24103 { 	if (DA24432 == TRUE) { 		set_val(DA24451, 100); 		DA241323 = 60; 			} 	else { 		DA241323 = 0; 			} 	set_val(DA24452, 0); 	wait( DA241323); 	if (DA24432 == TRUE) { 		set_val(DA24451, 0); 		DA241323 = 60; 			} 	else { 		DA241323 = 0; 			} 	set_val(DA24452, 0); 	wait( DA241323); 	if (DA24432 == TRUE) { 		set_val(DA24451, 100); 			} 	wait( 750); 	wait( 750); 	} combo DA24104 { 	if(!get_ival(DA24448)){ 		set_val(DA24452,100); 			} 	else{ 		set_val(DA24452,0); 			} 	set_val(DA24453, 100); 	wait(DA24595); 	if(!get_ival(DA24448)){ 		set_val(DA24452,100); 			} 	else{ 		set_val(DA24452,0); 			}     set_val(DA24453, 0); 	wait(500); 	} int DA241325; int DA241323 ; int DA241327 = TRUE; int DA241328; int DA241329; int DA241330; int DA241331; int DA241332; function DA24271() { 			if((DA241330 >= DA24456) || get_ival(XB1_LS) || get_ival(XB1_RS) || get_ival(DA24451) || get_ival(DA24450)  || get_ival(DA24452) ||       get_ival(DA24449) || get_ival(DA24455) || get_ival(DA24448) || get_ival(DA24454)   || get_ival(XB1_PR1) ||      get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(DA241138))> 45 || abs(get_ival(DA241139))> 45))){ 				if(!get_ival(DA24453))DA24116(POLAR_LS, DA24123(POLAR_LS,POLAR_ANGLE), DA24123(POLAR_LS, POLAR_RADIUS)); 							} 	if( !get_ival(DA24452) && !get_ival(DA24453) && !get_ival(DA24450) && !combo_running(DA24104) ){ 		if (DA24123(POLAR_LS, POLAR_RADIUS) > 1800) { 			DA241330 += get_rtime(); 			if( (DA241330 > 2500) ) DA241330 = 0; 			if((DA241330 <  DA24456) && DA24557 > 2000){ 			       sensitivity(PS4_LX, 60, 90); 			       sensitivity(PS4_LY, 60, 90); 				} 					} 			} } combo DA24105 { 	set_val(DA24449,100); 	wait( DA24655); 	set_val(DA24449,  0); 	wait( 30); 	if(DA24402){ 		set_val(DA24449,100); 			} 	wait( 60); 	} combo DA24106 { 	set_val(DA24449,  0); 	wait( 30); 	set_val(DA24449,100); 	wait( 60); 	} combo DA24107 { 	set_val(DA24455,100); 	wait( DA24657); 	set_val(DA24455,  0); 	wait( 30); 	if(DA24399){ 		set_val(DA24455,100); 			} 	wait( 60); 	} int DA241019 int DA241337 combo DA24108 { 	combo_suspend(DA24108) 	wait(361) } function DA24272 (){ 	if(DA241019[DA241337] != 361){ 	    DA241337-- 	} 	else{ 		if(inv(DA241337) != 285){ 			DA24272(); 		} 	} } int DA241340;  function DA24273() {     if (combo_running(DA240) || combo_running(DA241) || combo_running(DA242) || combo_running(DA243) || combo_running(DA244) ||      	combo_running(DA245) || combo_running(DA246) || combo_running(DA247) || combo_running(DA248) ||      	combo_running(DA249) || combo_running(DA2410) || combo_running(DA2411) || combo_running(DA2412) ||      	combo_running(DA2413) || combo_running(DA2414) || combo_running(DA2415) || combo_running(DA2416) ||      	combo_running(DA2417) || combo_running(DA2418) || combo_running(DA2419) || combo_running(DA2420) ||      	combo_running(DA2421) || combo_running(DA2422) || combo_running(DA2423) || combo_running(DA2424) ||      	combo_running(DA2425) || combo_running(DA2426) || combo_running(DA2427) ||      	combo_running(DA2428) || combo_running(DA2429) || combo_running(DA2430) || combo_running(DA2431) ||      	combo_running(DA2432) || combo_running(DA2433) || combo_running(DA2434) ||      	combo_running(DA2435) || combo_running(DA2436) || combo_running(DA2437) ||      	combo_running(DA2438) || combo_running(DA2439) || combo_running(DA2440) ||      	combo_running(DA2441) || combo_running(DA2442) || combo_running(DA2443) ||      	combo_running(DA2444) || combo_running(DA2445) || combo_running(DA2446) ||      	combo_running(DA2447) || combo_running(DA2448) || combo_running(DA2449) ||      	combo_running(DA2450) || combo_running(DA2451) || combo_running(DA2452) || combo_running(DA2453) ||      	combo_running(DA2454) || combo_running(DA2455) || combo_running(DA2456) || combo_running(DA2457) ||      	combo_running(DA2458) || combo_running(DA2459) || combo_running(DA2460) || combo_running(DA2461) ||      	combo_running(DA2462) || combo_running(DA2463) || combo_running(DA2464) || combo_running(DA2465) ||      	combo_running(DA2466) || combo_running(DA2467) || combo_running(DA2468) || combo_running(DA2469) || combo_running(DA2470) ||      	combo_running(DA2471) || combo_running(DA2472) || combo_running(DA2473) || combo_running(DA2474) || combo_running(DA2475) ||      	combo_running(DA2476) || combo_running(DA2477) || combo_running(DA2478) || combo_running(DA2479) ||     	combo_running(DA2480) || combo_running(DA2481) || combo_running(DA2482) ||       	combo_running(DA2483) || combo_running(DA2484) || combo_running(DA2485) || combo_running(DA2486) ||      	combo_running(DA2487) || combo_running(DA2488) || combo_running(DA2489) || combo_running(DA2490) || combo_running(DA2491) ||      	combo_running(DA2492) || combo_running(DA2493) || combo_running(DA2494) || combo_running(DA2495) ||      	combo_running(DA2496) || combo_running(DA2497) || combo_running(DA2498) || combo_running(DA24100) ||      	combo_running(DA24101) || combo_running(DA24103) || combo_running(DA24104) || combo_running(DA24105) ||      	combo_running(DA24106) || combo_running(DA24107) || combo_running(DA24108)) {      	DA241340 = 1;     } else {     	DA241340 = 0;     } } 