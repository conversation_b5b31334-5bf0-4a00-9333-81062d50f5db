////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////
int LX, LY;                   // Rohe Stick-Eingaben
int scaled_x, scaled_y;       // Ausgabewerte nach Deadzone & Oktagon

int DEADZONE   = 30;          // Quadratische Deadzone

// --- <PERSON><PERSON><PERSON> Set ---
int MAX_INPUT1 = 70;  
int MAX_SUM1   = 140;  

// --- Zweites Set ---
int MAX_INPUT2 = 100;   
int MAX_SUM2   = 200; 

// Hilfsvariablen
int sign, abs_val, output, sum, scale_factor;


////////////////////////////////////////////////////////////////////////////////
// FUNKTIONEN
////////////////////////////////////////////////////////////////////////////////

// Apply Square Deadzone auf EINER Achse, 
// jetzt mit "maxInput" als Parameter!
function apply_one_axis_deadzone(int val, int deadzone, int maxInput) {
    // Vorzeichen
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);

    // Innerhalb der Deadzone -> 0
    if(abs_val <= deadzone) {
        return 0;
    }

    // Begrenzen, falls > maxInput
    if(abs_val > maxInput) {
        abs_val = maxInput;
    }

    // Linear von [deadzone..maxInput] nach [0..maxInput] skalieren
    output = ((abs_val - deadzone) * maxInput) / (maxInput - deadzone);

    return (sign * output);
}

function map_convex_octagon(int x, int y, int deadzone, int maxInput, int maxSum) {
    // 1) Square Deadzone pro Achse
    scaled_x = apply_one_axis_deadzone(x, deadzone, maxInput);
    scaled_y = apply_one_axis_deadzone(y, deadzone, maxInput);

    // 2) Sum-of-abs Clamp -> |x| + |y| <= maxSum
    sum = abs(scaled_x) + abs(scaled_y);
    if(sum > maxSum) {
        scale_factor = (maxSum * 1000) / sum;
        scaled_x = (scaled_x * scale_factor) / 1000;
        scaled_y = (scaled_y * scale_factor) / 1000;
    }

    // 3) X/Y zusätzlich auf ±maxInput beschränken
    if(scaled_x >  maxInput) scaled_x =  maxInput;
    if(scaled_x < -maxInput) scaled_x = -maxInput;
    if(scaled_y >  maxInput) scaled_y =  maxInput;
    if(scaled_y < -maxInput) scaled_y = -maxInput;
}


////////////////////////////////////////////////////////////////////////////////
// MAIN
////////////////////////////////////////////////////////////////////////////////
main {
    // 1) Rohe Eingaben holen
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);

    // 2) Beispiel-Logik:
    //    - Oktagon mit Set1 (70/140), wenn KEIN Trigger oder beide Trigger
    //    - Oktagon mit Set2 (100/200), sonst (z.B. nur ein Trigger)
    //
    //    Du kannst die Bedingungen natürlich beliebig anpassen.
    
    if( (get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0)
     || (get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) ) 
    {
        // => Erstes Set: 70/140
        map_convex_octagon(LX, LY, DEADZONE, MAX_INPUT1, MAX_SUM1);
    } 
    else 
    {
        // => Zweites Set: 100/200
        map_convex_octagon(LX, LY, DEADZONE, MAX_INPUT2, MAX_SUM2);
    }

    // 3) Fertige Werte setzen
    set_val(XB1_LX, scaled_x);
    set_val(XB1_LY, scaled_y);
}
