// Constants
define STICK_THRESHOLD = 2000;
define FULL_CIRCLE = 360;
define QUARTER_CIRCLE = 90;
define HALF_CIRCLE = 180;
define THREE_QUARTER_CIRCLE = 270;
define MAX_STICK_VALUE = 32767;

// Variables
int lsAngle;
int rsAngle;
int relativeAngle;
int angleInRange;
int angle;
int start;
int end;
int rightZoneStart;
int upZoneStart;
int leftZoneStart;
int downZoneStart;
int cosTable[36];
int sinTable[36];
int temp_index;  // Added for use in cos_deg and sin_deg functions

// Initialize lookup tables for sine and cosine
function init_lookup_tables() {
    // Only initialize once
    if (cosTable[0] != 0) return;
    
    // Fill tables with values for every 10 degrees (0 to 350)
    cosTable[0] = 100;    sinTable[0] = 0;     // 0°
    cosTable[1] = 98;     sinTable[1] = 17;    // 10°
    cosTable[2] = 94;     sinTable[2] = 34;    // 20°
    cosTable[3] = 87;     sinTable[3] = 50;    // 30°
    cosTable[4] = 77;     sinTable[4] = 64;    // 40°
    cosTable[5] = 64;     sinTable[5] = 77;    // 50°
    cosTable[6] = 50;     sinTable[6] = 87;    // 60°
    cosTable[7] = 34;     sinTable[7] = 94;    // 70°
    cosTable[8] = 17;     sinTable[8] = 98;    // 80°
    cosTable[9] = 0;      sinTable[9] = 100;   // 90°
    cosTable[10] = -17;   sinTable[10] = 98;   // 100°
    cosTable[11] = -34;   sinTable[11] = 94;   // 110°
    cosTable[12] = -50;   sinTable[12] = 87;   // 120°
    cosTable[13] = -64;   sinTable[13] = 77;   // 130°
    cosTable[14] = -77;   sinTable[14] = 64;   // 140°
    cosTable[15] = -87;   sinTable[15] = 50;   // 150°
    cosTable[16] = -94;   sinTable[16] = 34;   // 160°
    cosTable[17] = -98;   sinTable[17] = 17;   // 170°
    cosTable[18] = -100;  sinTable[18] = 0;    // 180°
    cosTable[19] = -98;   sinTable[19] = -17;  // 190°
    cosTable[20] = -94;   sinTable[20] = -34;  // 200°
    cosTable[21] = -87;   sinTable[21] = -50;  // 210°
    cosTable[22] = -77;   sinTable[22] = -64;  // 220°
    cosTable[23] = -64;   sinTable[23] = -77;  // 230°
    cosTable[24] = -50;   sinTable[24] = -87;  // 240°
    cosTable[25] = -34;   sinTable[25] = -94;  // 250°
    cosTable[26] = -17;   sinTable[26] = -98;  // 260°
    cosTable[27] = 0;     sinTable[27] = -100; // 270°
    cosTable[28] = 17;    sinTable[28] = -98;  // 280°
    cosTable[29] = 34;    sinTable[29] = -94;  // 290°
    cosTable[30] = 50;    sinTable[30] = -87;  // 300°
    cosTable[31] = 64;    sinTable[31] = -77;  // 310°
    cosTable[32] = 77;    sinTable[32] = -64;  // 320°
    cosTable[33] = 87;    sinTable[33] = -50;  // 330°
    cosTable[34] = 94;    sinTable[34] = -34;  // 340°
    cosTable[35] = 98;    sinTable[35] = -17;  // 350°
}

// Function to return cosine for an angle in degrees using the lookup table
function cos_deg(int ang) {
    init_lookup_tables();  // Ensure the table is filled
    while (ang < 0) { ang += 360; }
    ang = ang % 360;
    temp_index = (ang + 5) / 10;  // Round to nearest multiple of 10
    temp_index = temp_index % 36;
    return cosTable[temp_index];
}

// Function to return sine for an angle in degrees using the lookup table
function sin_deg(int ang) {
    init_lookup_tables();  // Ensure the table is filled
    while (ang < 0) { ang += 360; }
    ang = ang % 360;
    temp_index = (ang + 5) / 10;  // Round to nearest multiple of 10
    temp_index = temp_index % 36;
    return sinTable[temp_index];
}

// Function to handle angle wrap-around
function angleInRange(angle, start, end) {
    if (start <= end) {
        return (angle >= start && angle < end);
    } else {
        return (angle >= start || angle < end);
    }
    // Default return statement to ensure all code paths return a value
    return FALSE;
}

// Main function for handling right stick skills
function handleRightStickSkills() {
    // Check if right stick is being used without other inputs
    if (!get_val(XB1_RS) && !get_val(XB1_LT) &&
        !get_val(XB1_RT) && !get_val(XB1_RB) &&
        !get_val(XB1_LB)) {
       
        // Check if right stick is pushed beyond threshold
        if(get_val(POLAR_RS, POLAR_RADIUS) > STICK_THRESHOLD) {
           
            // Get the angle of the left stick (inverted and normalized to 0-359)
            lsAngle = (FULL_CIRCLE - get_val(POLAR_LS, POLAR_ANGLE)) % FULL_CIRCLE;
           
            // Get the angle of the right stick (inverted and normalized to 0-359)
            rsAngle = (FULL_CIRCLE - get_val(POLAR_RS, POLAR_ANGLE)) % FULL_CIRCLE;
           
            // Calculate the relative angle between RS and LS
            relativeAngle = (rsAngle - lsAngle + FULL_CIRCLE) % FULL_CIRCLE;
           
            // Calculate zone boundaries relative to left stick angle
            rightZoneStart = lsAngle;
            upZoneStart = (lsAngle + QUARTER_CIRCLE) % FULL_CIRCLE;
            leftZoneStart = (lsAngle + HALF_CIRCLE) % FULL_CIRCLE;
            downZoneStart = (lsAngle + THREE_QUARTER_CIRCLE) % FULL_CIRCLE;

            // Determine which zone the right stick is in relative to the left stick
            if (angleInRange(rsAngle, rightZoneStart, upZoneStart)) {
                move_right();
            } else if (angleInRange(rsAngle, upZoneStart, leftZoneStart)) {
                move_up();
            } else if (angleInRange(rsAngle, leftZoneStart, downZoneStart)) {
                move_left();
            } else {
                move_down();
            }
        }
    }
}

// Movement functions
// These functions set the right stick to the appropriate angle relative to the left stick
function move_down() {
    set_val(XB1_RX, cos_deg((lsAngle + HALF_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
    set_val(XB1_RY, sin_deg((lsAngle + HALF_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
}

function move_up() {
    set_val(XB1_RX, cos_deg(lsAngle) * MAX_STICK_VALUE / 100);
    set_val(XB1_RY, sin_deg(lsAngle) * MAX_STICK_VALUE / 100);
}

function move_left() {
    set_val(XB1_RX, cos_deg((lsAngle + THREE_QUARTER_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
    set_val(XB1_RY, sin_deg((lsAngle + THREE_QUARTER_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
}

function move_right() {
    set_val(XB1_RX, cos_deg((lsAngle + QUARTER_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
    set_val(XB1_RY, sin_deg((lsAngle + QUARTER_CIRCLE) % FULL_CIRCLE) * MAX_STICK_VALUE / 100);
}

// Main loop
main {
    handleRightStickSkills();
    // Add other main loop functions here if needed
}
