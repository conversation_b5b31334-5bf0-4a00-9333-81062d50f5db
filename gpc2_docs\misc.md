# Miscellaneous Functions

#import
#import — Import GIT file

Description
#import "<filename>.git"
Import the Input Translator file named <filename>.git to the script source code.

The Input Translator sections are declared according the <filename>, a suffix is added according the GIT type, as the following:

Table 1
Suffix	GIT Type
_cmap	Controller Remapper
_kmap	Keyboard Mapping
_mmap	Mouse Mapping
_mxyc	Mouse XY Converter
If <filename> contains non-alphanumeric characters, those are replaced with '_'.
Examples
Example code for #import
#import "example-file.git"
 
init {
    remapper(example_file_cmap);
    keymapping(example_file_kmap);
    mousemapping(example_file_mmap);
    mxyconverter(example_file_mxyc);
}

device_poweroff
device_poweroff - Power off the device

Description
void device_poweroff();
Programmatically power off the device.

Return Value
No value is returned.

Examples
Example #1 device_poweroff() example
init {
    device_poweroff();
}

display_overlay
display_overlay — Control the 7-segment display

Description
void display_overlay(uint8 mask, uint16 timeout);
Set, for an certain amount of time, the state on/off of each segment of the Titan Two's display, based on the bitmask mask.



Bit to Segment Map
Bit Position	Bit Mask	Hex	Segment	Header
0	00000001	01	A	SEG_A
1	00000010	02	B	SEG_B
2	00000100	04	C	SEG_C
3	00001000	08	D	SEG_D
4	00010000	10	E	SEG_E
5	00100000	20	F	SEG_F
6	01000000	40	G	SEG_G
7	10000000	80	DOT	BOTTOM_DOT
Parameters
mask: Each bit correlates to a segment of the display, accordingly above table. Setting a bit makes the correlated segment to be lit.
timeout: How long, in milli-seconds, the display should show mask. After the timeout, the display goes back to the state it had before call display_overlay().
Subsequent calls of display_overlay() does not creates an stack of states, instead mask and timeout are always updated with the values from the last call of display_overlay().
Return Value
No value is returned.

Examples
Example #1 display_overlay() example
#include <display.gph>
 
main {
    combo_run(CountDown);
}
 
combo CountDown {
    const uint8 disp[] = { 0, _9_, _8_, _7_, _6_, _5_, _4_, _3_, _2_, _1_, _0_ };
    static uint8 i;
 
    display_overlay(disp[i], 1000);
    if(++i >= sizeof(disp)) i = 0;
    wait(0);
 
    wait(980);
}

![alt text](image-1.png)

printf
printf — Output a formatted string

Description
void printf(char *fmt, ...);
Print a formatted string to Gtuner IV output.

Parameters
fmt: The string that contains the text to be printed. It can optionally contain embedded format tags that are replaced by the values specified in subsequent additional arguments and formatted as requested.
args: Depending on the fmt string, the function may expect a sequence of additional arguments, each containing one value to be inserted in place of each %-tag specified in the format parameter. There should be the same number of these arguments as the number of %-tags that expect a value.
Return Value
No value is returned.

Format Tag Syntax
The format tag prototype is: %[flags][width][.precision][length]specifier

flags
-	Left-justify within the given field width. Right justification is the default.
+	Forces to precede the result with a plus or minus sign even for positive numbers.
#	Used with o, x or X specifiers the value is preceded with 0, 0x or 0X respectively for values different than zero. Used with e, E and f, it forces the written output to contain a decimal point even if no digits would follow.
0	Left-pads the number with zeroes instead of spaces, where padding is specified.
width
number	Minimum number of characters to be printed.
*	The width is not specified in the format string, but as an additional integer value argument preceding the argument that has to be formatted.
.precision
.number	For integer specifiers (d, i, o, u, x, X), precision specifies the minimum number of digits to be written. For e, E and f specifiers, this is the number of digits to be printed after the decimal point.
.*	The precision is not specified in the format string, but as an additional integer value argument preceding the argument that has to be formatted.
length
h	The argument is interpreted as a short int or unsigned short int.
l	The argument is interpreted as a long int or unsigned long int.
specifier
c or C	Character.
d or i	Signed decimal integer.
e	Scientific notation using e character.
E	Scientific notation using E character.
f	Decimal point.
g	Uses the shorter of %e or %f.
G	Uses the shorter of %E or %f.
o	Signed octal.
s or S	Pointer address of the string.
Note: This specifier does not have the conventional expected behavior.
u	Unsigned decimal integer.
x	Unsigned hexadecimal integer.
X	Unsigned hexadecimal integer with capital letters.
p	Pointer address.
n	Nothing printed.
%	The % character.
Examples
Example #1 printf() example
init {
    int ch;
    for(ch=85; ch<=100; ++ch) {
        printf("ASCII: %d, Character: %c", ch, ch);
    }
}

power_source
power_source - Get the power source

Description
uint8 power_source();
Retrieve the source of power for the Titan Two device.

Return Value
Returns a number that represents the power source as the following table.

Return Value	Power Source
0	OUTPUT Port
1	PROG Port
2	DC Power Jack
Examples
Example #1 power_source() example
const char *ps_text[] = {
    "Power Source: OUTPUT Port",
    "Power Source: PROG Port",
    "Power Source: DC Power Jack"
};
 
init {
    printf(ps_text[power_source()]);
}

push_to_talk
push_to_talk - Enable or disable the headset MIC

Description
void push_to_talk(bool talk);
Enable or disable the controller headset MIC according the talk argument.

Parameters
talk: TRUE for enable the headset MIC or FALSE to disable.
Return Value
No value is returned.

Examples
Example #1 push_to_talk() example
init {
    // Disable the headset MIC when the script is loaded
    push_to_talk(FALSE);
}
 
main {
    if(event_active(BUTTON_2)) {
        // Enable the headset MIC when the button is pressed
        push_to_talk(TRUE);
    } else if(event_release(BUTTON_2)) {
        // Disable the headset MIC when the button is released
        push_to_talk(FALSE);
    }
}

script_load
script_load - Load a script file

Description
bool script_load("<filename>.gbc"); 
Load the script bytecode file named <filename> stored in the SD-Card at /TITAN TWO/MEMSLOT/.

Parameters
<filename>: Filename of the bytecode to load. The <filename> must have 8 characters or less.
Return Value
Returns TRUE if the bytecode file was successfully loaded, FALSE otherwise.

Examples
Example #1 script_load() example
main {
    // If BUTTON_2 is pressed ...
    if(event_active(BUTTON_2)) {
        // ... load the bytecode file "Example".
        if(script_load("Example.gbc") == FALSE) {
            printf("Error loading Example.gbc");
        }
    }
}

memset
memset - Fill memory with a constant byte

Description
void memset(void *dst, uint8 val, int16 size);
The memset() function fills the first size bytes of the memory area pointed to by dst with the constant byte val.

Parameters
dst: Pointer to the block of memory to fill.
val: Value to be set.
size: Number of bytes to be set to the value.
Return Value
No value is returned.

Examples
Example #1 memset() example
uint8 arr[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9 };
 
init {
    memset(&arr, 0, sizeof(arr)); // All arr[] elements are now 0
}

memcpy
memcpy - Copy memory area

Description
void memcpy(void *dst, void *src, int16 size);
The memcpy() function copies size bytes from memory area src to memory area dst. The memory areas must not overlap.

Parameters
dst: Pointer to the destination array where the content is to be copied.
src: Pointer to the source of data to be copied.
size: Number of bytes to copy.
Return Value
No value is returned.

Examples
Example #1 memset() example
const uint8 src_arr[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9 };
 
init {
    uint8 dst_arr[9];
 
    memcpy(&dst_arr, &src_arr, sizeof(dst_arr));
    // dst_arr[] now have the same contents of src_arr[]
}
