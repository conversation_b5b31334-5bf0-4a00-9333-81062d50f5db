# LED

led_set
led_set — Set LED intensity and duty length

Description
void led_set(<LED_IDENTIFIER>, fix32 intensity, uint32 dutylength);
Set the intensity and duty length values of the LED identified by <LED_IDENTIFIER>.

ATTENTION: The led_set() holds the last state set and should NOT be called on every interaction of main.
Depending on the “Device Lightbar” option in device settings, the led_set() also have effect on the Titan Two lightbar.
DualShock 4 and Titan Two Lightbar RGB Map
LED_1	Blue
LED_2	Red
LED_3	Green
Always set LED_4 to 0.0 when configuring colors using the RGB spectrum.
Parameters
<LED_IDENTIFIER>: An valid LED identifier LED_1, LED_2, LED_3 or LED_4. Note: the LED identifier is passed as direct parameter of the led_set() opcode, therefore stack values can't be used.
intensity: The LED intensity level in percentage, from 0.00 to 100.00 (%). Pass a negative value to use the “Default LED Intensity” configured by the user in the device settings.
dutylength: The total length of the LED blinking duty cycle, expressed in milli-seconds. The duty cycle is 50% on and 50% off, for instance, a dutylenght of 800 will result in blink the LED for 400ms on and 400ms off. Set this parameter to 0 to not blink the LED.
The intensity value is only fully supported by DualShock 4 controllers and for the others controllers this parameter is interpreted as binary value to turn the LED on or off.
The dutylength value is only fully supported by DualShock 3 controllers, for the others controllers this parameter is ignored.
Return Value
No value is returned.

Examples
Example #1 led_set() example
init {
    uint8 protocol;
    port_status(PORT_USB_A, &protocol, NULL);
 
    // If there is an PS3 controller on INPUT-A ...
    if(protocol == PROTOCOL_PS3) {
        // ... blink the player LED_2.
        led_set(LED_1, 0.0, 0);
        led_set(LED_2, 100.0, 400);
        led_set(LED_3, 0.0, 0);
        led_set(LED_4, 0.0, 0);
    }
}
Example #2 led_set() example
void set_rgb(fix32 r, fix32 g, fix32 b) {
    led_set(LED_1, b, 0);
    led_set(LED_2, r, 0);
    led_set(LED_3, g, 0);
    led_set(LED_4, 0.0, 0);
    printf("rgb(%0.2f, %0.2f, %0.2f)", r, g, b);
    return;
}

led_get
led_get — Get LED intensity and duty length

Description
fix32 led_get(<LED_IDENTIFIER>, uint32 *dutylength);
Retrieve the intensity and duty length values of the LED identified by <LED_IDENTIFIER>.

If the intensity and duty length were programmatically set by led_set(), led_get() will return the values programmatically set.
Parameters
<LED_IDENTIFIER>: An valid LED identifier LED_1, LED_2, LED_3 or LED_4. Note: the LED identifier is passed as direct parameter of the led_get() opcode, therefore stack values can't be used.
dutylength: Address of an uint32 variable to receive the duty length value. This parameter can be NULL.
Return Value
The LED intensity in percentage, from 0.00 to 100.00 (%).

Examples
Example #1 led_get() example
main {
    if(led_get(LED_1, NULL) < 50.0) {
        led_set(LED_1, 50.0, 0);
    }
}

led_get_actual
led_get_actual — Get actual LED intensity and duty length

Description
fix32 led_get_actual(<LED_IDENTIFIER>, uint32 *dutylength);
Retrieve the original intensity and duty length values of the LED identified by <LED_IDENTIFIER>.

led_get_actual() will always return the original values, even if led_set() is used to set an new intensity and duty length.
Parameters
<LED_IDENTIFIER>: An valid LED identifier LED_1, LED_2, LED_3 or LED_4. Note: the LED identifier is passed as direct parameter of the led_get_actual() opcode, therefore stack values can't be used.
dutylength: Address of an uint32 variable to receive the duty length value. This parameter can be NULL.
Return Value
The LED intensity in percentage, from 0.00 to 100.00 (%).

Examples
Example #1 led_get_actual() example
init {
    uint32 dutylength;
 
    if(led_get_actual(LED_1, &dutylength) < 85.0 || dutylength != 0) {
        led_set(LED_1, 90.0, 0);
    }
}

led_vmset
led_vmset — Blink LED

Description
void led_vmset(<LED_IDENTIFIER>, uint16 on, uint16 off, int16 count);
Blink the LED identified by <LED_IDENTIFIER> according the on, off and count parameters.

led_vmset() controls the LED on/off state internally and does not depend on the controller capabilities to blink the player LEDs.
ATTENTION: The led_vmset() should NOT be called on every interaction of main.
Depending on the “Device Lightbar” option in device settings, the led_vmset() also have effect on the Titan Two lightbar.
Parameters
<LED_IDENTIFIER>: An valid LED identifier LED_1, LED_2, LED_3 or LED_4. Note: the LED identifier is passed as direct parameter of the led_vmset() opcode, therefore stack values can't be used.
on: How long, in milli-seconds, the LED should be kept in the state “on” during the duty cycle period.
off: How long, in milli-seconds, the LED should be kept in the state “off” during the duty cycle period.
count: How many duty cycle periods should be executed. Pass an negative value to repeat the duty period indefinitely.
Return Value
No value is returned.

Examples
Example #1 led_vmset() example
init {
    // Blink LED_1 indefinitely
    led_vmset(LED_1, 250, 250, -1);
}

led_vmget
led_vmget — Get current count value

Description
int16 led_vmget(<LED_IDENTIFIER>);
Check how many duty cycle periods remains to be executed for the LED identified by <LED_IDENTIFIER>.

Parameters
<LED_IDENTIFIER>: An valid LED identifier LED_1, LED_2, LED_3 or LED_4. Note: the LED identifier is passed as direct parameter of the led_vmget() opcode, therefore stack values can't be used.
Return Value
Returns the current count value. The count value is initially set by led_vmset() and decremented by one for each executed duty cycle period.

A negative returning value indicates the duty period is configured to repeat indefinitely.
Examples
Example #1 led_vmget() example
int state = 0;
 
main {
    switch(state) {
        case 0: {
            if(led_vmget(LED_2) == 0) {
                led_vmset(LED_3, 200, 500, 3);
                state = 1;
            }
        } break;
        case 1: {
            if(led_vmget(LED_3) == 0) {
                led_vmset(LED_2, 500, 200, 2);
                state = 0;
            }
        } break;
    }
}

led_reset
led_reset — Reset all LEDs intensity and duty length

Description
void led_reset();
Reset all LEDs intensity and duty length to the original values.

led_reset() revert the changes applied by led_set() or led_vmset().
ATTENTION: The led_reset() should NOT be called on every interaction of main.
Return Value
No value is returned.

Examples
Example #1 led_reset() example
main {
    if(
        led_get(LED_1, NULL) != led_get_actual(LED_1, NULL) || 
        led_get(LED_2, NULL) != led_get_actual(LED_2, NULL) || 
        led_get(LED_3, NULL) != led_get_actual(LED_3, NULL) || 
        led_get(LED_4, NULL) != led_get_actual(LED_4, NULL)
    ) {
        led_reset();
    }
}