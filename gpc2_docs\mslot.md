# Memory Slot Functions

mslot_get
mslot_get - Get memory slot number

Description
uint8 mslot_get();
Get the current memory slot number.

The memory slot number can go from 1 to 9. The 255 value means the script is running via Test and Debug command and is not associated with any memory slot.
Return Value
Returns the current memory slot number.

Examples
Example #1 mslot_get() example
init {
    printf("Current memory slot number: %d", mslot_get());
}

mslot_load
mslot_load - Load memory slot

Description
void mslot_load(uint8 slot_no);
Load the memory slot of number slot_no.

Parameters
slot_no: The memory slot number to be loaded, from 1 to 9. Passing 0 to this parameter results in unload the current loaded memory slot.
Return Value
No value is returned.

Examples
Example #1 mslot_load() example
init {
    if(mslot_get() > 9) {
        mslot_load(0);
    }
}

mslot_check
mslot_check - Check memory slot

Description
uint8 mslot_check(uint8 slot_no);
Check if the memory slot of number slot_no can be loaded.

Parameters
slot_no: The memory slot number to be checked, from 1 to 9.
Return Value
Returns 0 if the memory slot is empty, or an non-zero number indicating a bytecode and/or an input translator is stored in the memory slot.

Bitmask	Hex	Type
00000001	01	Bytecode (Gamepack or Script)
00000010	02	Input Translator
Examples
Example #1 mslot_check() example
init {
    uint8 i;
 
    for(i=0; i<=9; ++i) {
        uint8 c = mslot_check(i);
        printf("Memory Slot: %d - Bytecode: %d, Input Translator: %d", i, c & 0x01, (c & 0x02) >> 1);
    }
}