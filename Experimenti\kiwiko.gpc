// Input variables to store X and Y axis values
int x_input, y_input;
// Adjustment variables for X and Y axis calculations
int x_adjustment, y_adjustment;
// Final calculated value
int Val;

// Smart tracking function that takes:
// f_val: tracking strength value
// f_axis_X: X axis input identifier (e.g. PS4_RX)
// f_axis_Y: Y axis input identifier (e.g. PS4_RY)
function SmartTracker(f_val, f_axis_X, f_axis_Y) {
    // Get raw input values from controller
    x_input = get_ival(f_axis_X);  // Get unmodified X axis input
    y_input = get_ival(f_axis_Y);  // Get unmodified Y axis input
    
    // If player is both aiming and shooting
    if (get_ival(Aim) && get_ival(Shoot)) {
        // Switch to using potentially modified input values
        x_input = get_val(f_axis_X);  // Get potentially modified X axis value
        y_input = get_val(f_axis_Y);  // Get potentially modified Y axis value
    }
    
    // If stick movement exceeds deadzone threshold in either direction
    if (abs((x_input)) > mod_edit[Radial_DZ] || abs((y_input)) > mod_edit[Radial_DZ]) {
        // Calculate adjustments based on how far from center the stick is
        // The further from center, the smaller the adjustment
        x_adjustment = 100 - abs(x_input);    // X axis adjustment (inverse of distance from center)
        y_adjustment = 100 - abs(y_input);    // Y axis adjustment (inverse of distance from center)
    }
    
    // Calculate final tracking value:
    // - Multiply tracking strength (f_val) by both adjustments
    // - Divide by 32767 to normalize to proper stick range
    Val = f_val * (x_adjustment * y_adjustment) / 32767;
    
    // Return the final Y value:
    // - Add tracking value to original Y input
    // - Clamp to valid stick range (-32767 to 32767)
    return clamp(y_input + Val, -32767, 32767);
}