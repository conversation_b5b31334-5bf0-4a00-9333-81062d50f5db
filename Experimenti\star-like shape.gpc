// Constants
define MAX_INPUT = 99;
define DEADZONE = 1;
define ARRAY_SIZE = 23;

// Variables
int octagonMax[ARRAY_SIZE];  // Array declaration
int x, y, mag_sq, mag;
int angle, index, extension;
int scaled_x, scaled_y;
int sign, abs_val, output;
int i;  // Loop counter

// Variables for intSqrt
int iSqrtValue;
int iSqrtRes;
int iSqrtBit;
int iSqrtTemp;

////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes   = 0;
    iSqrtBit   = 1 << 14; // 2^14 = 16384 (enough for up to ~20000)

    // Shift down until bit <= value
    while(iSqrtBit > iSqrtValue) {
        iSqrtBit = iSqrtBit >> 2;
    }

    while(iSqrtBit != 0) {
        iSqrtTemp = iSqrtRes + iSqrtBit;
        if(iSqrtValue >= iSqrtTemp) {
            iSqrtValue = iSqrtValue - iSqrtTemp;
            iSqrtRes   = iSqrtRes + (iSqrtBit << 1);
        }
        iSqrtRes = iSqrtRes >> 1;
        iSqrtBit = iSqrtBit >> 2;
    }
    return iSqrtRes;
}

// Initialize array values in main
function init_octagon_values() {
    octagonMax[0] = 100;   // 0°
    octagonMax[1] = 99;    // 2°
    octagonMax[2] = 98;    // 4°
    octagonMax[3] = 96;    // 6°
    octagonMax[4] = 94;    // 8°
    octagonMax[5] = 91;    // 10°
    octagonMax[6] = 88;    // 12°
    octagonMax[7] = 85;    // 14°
    octagonMax[8] = 82;    // 16°
    octagonMax[9] = 79;    // 18°
    octagonMax[10] = 76;   // 20°
    octagonMax[11] = 74;   // 22°
    octagonMax[12] = 72;   // 24°
    octagonMax[13] = 70;   // 26°
    octagonMax[14] = 69;   // 28°
    octagonMax[15] = 68;   // 30°
    octagonMax[16] = 67;   // 32°
    octagonMax[17] = 66;   // 34°
    octagonMax[18] = 66;   // 36°
    octagonMax[19] = 65;   // 38°
    octagonMax[20] = 65;   // 40°
    octagonMax[21] = 65;   // 42°
    octagonMax[22] = 65;   // 44°
}

function apply_deadzone(int val) {
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);
    
    if(abs_val <= DEADZONE)
        return 0;
    
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    if(output > MAX_INPUT)
        output = MAX_INPUT;
        
    return sign * output;
}

function map_convex_octagon(int x, int y) {
    // Apply deadzone first
    scaled_x = apply_deadzone(x);
    scaled_y = apply_deadzone(y);
    
    // Calculate magnitude
    mag_sq = scaled_x * scaled_x + scaled_y * scaled_y;
    if(mag_sq == 0)
        return;
        
    mag = intSqrt(mag_sq);
    
    // Get current angle (0-360)
    angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Get index into extension table (normalize to 0-45 degrees)
    index = angle % 45;
    if(index > 22) {
        index = 45 - index;
    }
    
    // Apply convex octagonal boundary
    extension = octagonMax[index];
    scaled_x = (scaled_x * extension) / 100;
    scaled_y = (scaled_y * extension) / 100;
}

init {
    // Initialize the octagon values
    init_octagon_values();
}

main {
    // Get stick values
    x = get_val(XB1_LX);
    y = get_val(XB1_LY);
    
    // Apply convex octagonal mapping
    map_convex_octagon(x, y);
    
    // Set final values
    set_val(XB1_LX, scaled_x);
    set_val(XB1_LY, scaled_y);
} 