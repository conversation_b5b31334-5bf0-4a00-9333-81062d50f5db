// Define stick constants
define stick_B = POLAR_LS;  // Left stick in polar mode
define stickX  = XB1_LX;     // Left stick X axis
define stickY  = XB1_LY;     // Left stick Y axis

// Octagon parameters:
define MAX_VAL_B = 100;    // Maximum stick value
define RATIO_B   = 80;     // When at “diagonal” the output is reduced to 70%
define BLEND_B   = 40;     // Blend range (percentage)

// Global integer variables
int x, y;
int abs_x, abs_y;
int max_val_B;
int blend_factor;

main {
    butterfly();
}

function butterfly() {

    // Get the current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Only process if the stick is moved away from center
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Calculate a “blend factor” which is 0 when the input is perfectly cardinal
        // and 100 when the input is perfectly diagonal.
        if(abs_x > abs_y) {
            blend_factor = (abs_y * 100) / abs_x;
        } else {
            blend_factor = (abs_x * 100) / abs_y;
        }
        
        /* 
         * In the original script the full (100) value was assigned on diagonals (blend_factor near 100)
         * and reduced (70%) on cardinals (blend_factor near 0). 
         * To “turn” the stick so that full radius is on 0°, 90°, 180°, 270° (cardinal),
         * we simply invert the mapping:
         *
         *   - When blend_factor is low (i.e. near cardinal) we allow full value.
         *   - When blend_factor is high (i.e. near diagonal) we reduce the value.
         */
        if(blend_factor < BLEND_B) {
            // Near cardinal: full range!
            max_val_B = MAX_VAL_B;
        }
        else if(blend_factor > (100 - BLEND_B)) {
            // Near diagonal: limit to 70%
            max_val_B = (MAX_VAL_B * RATIO_B) / 100;
        }
        else {
            // In the transition zone, blend linearly between the two extremes.
            max_val_B = MAX_VAL_B - (((MAX_VAL_B - (MAX_VAL_B * RATIO_B) / 100) * (blend_factor - BLEND_B)) / (100 - 2 * BLEND_B));
        }
        
        // Scale the original stick input by the calculated maximum value,
        // preserving the original direction.
        x = (x * max_val_B) / MAX_VAL_B;
        y = (y * max_val_B) / MAX_VAL_B;
        
        // Write the new (adjusted) values back to the left stick
        set_val(stickX, x);
        set_val(stickY, y);
    }
}
 