// Constants
define STICK_THRESHOLD = 2000;
define FULL_CIRCLE = 360;
define QUARTER_CIRCLE = 90;
define HALF_CIRCLE = 180;
define THREE_QUARTER_CIRCLE = 270;
define MAX_STICK_VALUE = 32767;

// Variables
int lsAngle;
int rsAngle;
int relativeAngle;
int angleInRange;
int angle;
int start;
int end;
int rightZoneStart; int upZoneStart; int leftZoneStart; int downZoneStart;

// Function to handle angle wrap-around
function angleInRange(angle, start, end) {
    if (start <= end) {
        return (angle >= start && angle < end);
    } else {
        return (angle >= start || angle < end);
    }
    // Default return statement to ensure all code paths return a value
    return FALSE;
}

// Main function for handling right stick skills
function handleRightStickSkills() {
    // Check if right stick is being used without other inputs
    if (!get_val(XB1_RS) && !get_val(XB1_LT) &&
        !get_val(XB1_RT) && !get_val(XB1_RB) &&
        !get_val(XB1_LB)) {
       
        // Check if right stick is pushed beyond threshold
        if(get_val(POLAR_RS, POLAR_RADIUS) > STICK_THRESHOLD) {
           
            // Get the angle of the left stick (inverted and normalized to 0-359)
            lsAngle = (FULL_CIRCLE - get_val(POLAR_LS, POLAR_ANGLE)) % FULL_CIRCLE;
           
            // Get the angle of the right stick (inverted and normalized to 0-359)
            rsAngle = (FULL_CIRCLE - get_val(POLAR_RS, POLAR_ANGLE)) % FULL_CIRCLE;
           
            // Calculate the relative angle between RS and LS
            relativeAngle = (rsAngle - lsAngle + FULL_CIRCLE) % FULL_CIRCLE;
           
            // Calculate zone boundaries relative to left stick angle
            rightZoneStart = lsAngle;
            upZoneStart = (lsAngle + QUARTER_CIRCLE) % FULL_CIRCLE;
            leftZoneStart = (lsAngle + HALF_CIRCLE) % FULL_CIRCLE;
            downZoneStart = (lsAngle + THREE_QUARTER_CIRCLE) % FULL_CIRCLE;

            // Determine which zone the right stick is in relative to the left stick
            if (angleInRange(rsAngle, rightZoneStart, upZoneStart)) {
                move_right();
            } else if (angleInRange(rsAngle, upZoneStart, leftZoneStart)) {
                move_up();
            } else if (angleInRange(rsAngle, leftZoneStart, downZoneStart)) {
                move_left();
            } else {
                move_down();
            }
        }
    }
}

// Movement functions
// These functions set the right stick to the appropriate angle relative to the left stick
function move_down() {
    set_polar(POLAR_RS, (lsAngle + HALF_CIRCLE) % FULL_CIRCLE, MAX_STICK_VALUE);
}

function move_up() {
    set_polar(POLAR_RS, lsAngle, MAX_STICK_VALUE);
}

function move_left() {
    set_polar(POLAR_RS, (lsAngle + THREE_QUARTER_CIRCLE) % FULL_CIRCLE, MAX_STICK_VALUE);
}

function move_right() {
    set_polar(POLAR_RS, (lsAngle + QUARTER_CIRCLE) % FULL_CIRCLE, MAX_STICK_VALUE);
}

// Main loop
main {
    handleRightStickSkills();
    // Add other main loop functions here if needed
}
 