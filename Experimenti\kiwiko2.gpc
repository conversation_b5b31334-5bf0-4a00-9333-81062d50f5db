// Constants
define Aim = PS4_L2;      // Left trigger for aiming
define Shoot = PS4_R2;    // Right trigger for shooting
define Radial_DZ = 15;    // Radial deadzone value

// Variables for tracking and adjustment
int x_input, y_input, x_adjustment, y_adjustment;
int Val;
int mod_edit[20];         // Array to store modification values

// Smart tracking function
function SmartTracker(f_val, f_axis_X, f_axis_Y) {
    x_input = get_ival(f_axis_X);
    y_input = get_ival(f_axis_Y);
    
    if (get_ival(Aim) && get_ival(Shoot)) {
        x_input = get_val(f_axis_X);
        y_input = get_val(f_axis_Y);
    }
    
    if (abs((x_input)) > mod_edit[Radial_DZ] || abs((y_input)) > mod_edit[Radial_DZ]) {
        x_adjustment = 100 - abs(x_input);
        y_adjustment = 100 - abs(y_input);
    }
    
    Val = f_val * (x_adjustment * y_adjustment) / 32767;
    return clamp(y_input + Val, -32767, 32767);
}

init {
    // Initialize modification values
    mod_edit[Radial_DZ] = 15;  // Set default deadzone
}
int tracking_strength;
main {
    // Apply smart tracking to right stick
    tracking_strength = 20;  // Adjust tracking strength
    
    // Apply tracking adjustment to right stick Y axis
    set_val(PS4_LY, SmartTracker(tracking_strength, PS4_LX, PS4_LY));
    
    // Debug output to traces
    set_val(TRACE_1, x_adjustment);
    set_val(TRACE_2, y_adjustment);
    set_val(TRACE_3, Val);
} 