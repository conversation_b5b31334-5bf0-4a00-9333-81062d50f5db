// ------------------------------
// Stick Sensitivity Adjustment
// ------------------------------

// Define base sensitivity value as a percentage (e.g., 100 = 100% sensitivity)
// Set this value between 100 and 400
int sensitivityValue = 300;

// Variables for sensitivity calculations (Global Scope)
int val_s;    // Stores sign of input value (+1 or -1)
int _val;     // Stores the modified input value
int baseSen;  // Calculated sensitivity scaling factor
int midVal;   // Midpoint value for response curve

// Main event loop
main {
    // Calculate 'baseSen' to prevent overflow
    // Ensures that 'baseSen' scales correctly based on 'sensitivityValue'
    baseSen = (sensitivityValue * 16384) / 100;

    // Midpoint value for linear response (half of max stick value 32767)
    midVal = 16384;

    // Apply custom sensitivity to all stick axes (Left X, Left Y, Right X, Right Y)
    _sensitivity(POLAR_LX, midVal, baseSen);
    _sensitivity(POLAR_LY, midVal, baseSen);
    _sensitivity(POLAR_RX, midVal, baseSen);
    _sensitivity(POLAR_RY, midVal, baseSen);
}

// ------------------------------
// Custom Sensitivity Function
// ------------------------------
function _sensitivity(int id, int mid, int sen) {
    // Retrieve current stick value for the specified axis
    _val = get_val(id);

    // Clamp the stick value to the safe range to prevent unexpected behavior
    if(_val > 32767) {
        _val = 32767;
    }
    else if(_val < -32768) {
        _val = -32768;
    }

    // Check if a midpoint is specified
    if(mid != NOT_USE) {
        // Determine the sign of the input value (+1 for positive, -1 for negative)
        if(_val >= 0) {
            val_s = 1;
        }
        else {
            val_s = -1;
        }

        // Work with the absolute value for scaling
        _val = _val * val_s;

        // Apply two-part response curve based on the midpoint
        if(_val <= mid) {
            // Below midpoint: Scale input linearly from 0 to midpoint
            _val = (_val * 16384) / mid;
        }
        else {
            // Above midpoint: Scale input linearly from midpoint to max
            _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
        }

        // Restore the original sign after scaling
        _val = _val * val_s;
    }

    // Apply the sensitivity multiplier if specified
    if(sen != NOT_USE) {
        // Multiply by 'sen' and divide to normalize
        _val = (_val * sen) / 16384;
    }

    // Clamp the modified stick value to ensure it stays within valid range
    if(_val > 32767) {
        _val = 32767;
    }
    else if(_val < -32768) {
        _val = -32768;
    }

    // Set the modified value back to the stick axis
    set_val(id, _val);
}