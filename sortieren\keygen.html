<!DOCTYPE html>
<!-- saved from url=(0059)file:///C:/Users/<USER>/Downloads/offline-key-generator.html -->
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Key Generator</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        body {
            font-family: 'Roboto', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f7fa;
            color: #333;
            overflow: hidden;
            position: relative;
        }

        .soccer-ball {
            position: absolute;
            width: 60vmin;
            height: 60vmin;
            opacity: 0.1;
            z-index: -1;
        }

        .container {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(245,245,245,0.95) 100%);
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 450px;
            z-index: 1;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .container:hover {
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: 700;
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        input {
            width: 100%;
            padding: 0.75rem;
            font-size: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            transition: border-color 0.3s ease;
        }

        input:focus {
            outline: none;
            border-color: #3498db;
        }

        button {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 500;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        button:active {
            transform: translateY(0);
        }

        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        button:hover::after {
            opacity: 1;
        }

        #result {
            margin-top: 1.5rem;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
            font-size: 1.2rem;
            color: #2c3e50;
            opacity: 0;
            transition: all 0.4s ease;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            box-shadow: inset 0 0 0 1px rgba(0,0,0,0.05);
        }

        #result.show {
            opacity: 1;
            transform: translateY(0);
        }

        .copy-btn {
            display: inline-block;
            margin-left: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: #2c3e50;
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .copy-btn:hover {
            opacity: 1;
            transform: scale(1.05);
        }
    </style>
</head>

<body>
    <svg class="soccer-ball" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="50" fill="#ffffff"></circle>
        <path d="M50 0 L97 25 L79 79 L21 79 L3 25 Z" fill="#000000"></path>
        <path d="M50 0 L3 25 L21 79 M50 0 L97 25 L79 79 M3 25 L97 25 M21 79 L79 79" stroke="#ffffff" stroke-width="2"
            fill="none"></path>
        <circle cx="50" cy="50" r="10" fill="#ffffff"></circle>
    </svg>

    <div class="container">
        <h1>Modern Key Generator</h1>
        <div class="input-group">
            <label for="inputNumber">Enter a 5-digit number:</label>
            <input type="number" id="inputNumber" placeholder="e.g., 12345" min="0" max="99999">
        </div>
        <button onclick="createKey()">Generate Key ⚽</button>
        <div id="result">
            <span id="keyOutput">Generated Key: 178131</span>
            <span class="copy-btn" onclick="copyToClipboard()">Copy</span>
        </div>
    </div>

    <script>
        // Add copy functionality
        function copyToClipboard() {
            const keyText = document.getElementById('keyOutput').textContent;
            navigator.clipboard.writeText(keyText.split(': ')[1])
                .then(() => {
                    const btn = document.querySelector('.copy-btn');
                    btn.textContent = 'Copied!';
                    setTimeout(() => {
                        btn.textContent = 'Copy';
                    }, 2000);
                });
        }

        function DA127(DA128) {
            let DA758 = 2;
            let DA759 = 984838;  // Decimal equivalent of 0xF1206
            let DA141 = 54321;   // Decimal equivalent of 0xD431
            let DA760 = (DA128 >>> DA758) | (DA128 << (32 - DA758));
            let shift_amount = (DA760 & 15) % 13;
            let intermediate = ((DA760 >>> shift_amount) & 0x7FFFF) + DA141;
            let DA761 = (intermediate % DA759) + 123456;  // Decimal equivalent of 0x1E240
            return DA761;
        }

        function createKey() {
            let input = document.getElementById('inputNumber').value;
            let inputValue = parseInt(input);
            let resultElement = document.getElementById('result');

            resultElement.classList.remove('show');

            if (isNaN(inputValue) || inputValue < 0 || inputValue > 99999) {
                resultElement.innerHTML = "<span style='color:#e74c3c'>Please enter a valid 5-digit number.</span>";
            } else {
                let result = DA127(inputValue);
                resultElement.innerHTML = `<span id="keyOutput">Generated Key: ${result}</span><span class="copy-btn" onclick="copyToClipboard()">Copy</span>`;
            }

            setTimeout(() => {
                resultElement.classList.add('show');
            }, 50);
        }
    </script>


</body>

</html>
