/////////////////////////
// ALPHA - TESTERS - T5// 
/////////////////////////

// Hi guys Today we will test new way for controlling your players movements .
// Follow steps and the out come should be as following : 
    // - Annoying player movement to your opponents coz your player will spam agile dribbling and protecting ball.
    //   your opponent will struggle to get ball back from you . 
    // - faster passes and throughballs than usual .
    // - more responsive jockey movements .
    // - you will get speed boost whenever you start sprinting .
    // - Finesse Shots success rate will highly increase if you trigger it when you see ball is very near to your player foot .
    

         
//////////////////
// 1st  step - .// 
//////////////////
   
               //-if you use Elite series controllers please turn off any (Left stick) sensitivity profiles only use the default .
               
               //-Your script must be WITHOUT LS Dribbling Sens / Also Sprint Sens OFF .
               
               //-Your script must be WITHOUT Defence Mod .
               
               //-Your script must be WITHOUT VM Control .
               
 
//////////////////
// 2nd  step - .// 
//////////////////
               // search  in your script for ( all other code ) without () .
              // copy all the following and paste it first thing after under the line you searched  .
  if ( !onoff_penalty && !onoff_FK &&  (get_val(PaceCtrol) ||  get_val(SprintBtn) || get_val(PassBtn) || get_val(ThroughBall)) ){
            	 sensitivity(PS4_LX, 50, 110);
                 sensitivity(PS4_LY, 50, 96);
                 vm_tctrl(-6);
	        
        }
       
    
       if ( !onoff_penalty && !onoff_FK && !get_val(PaceCtrol) && !get_val(FinesseShot) &&  !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)){
           	sensitivity(PS4_LX, 80, 142);
	        sensitivity(PS4_LY, 80, 142);
        }
        
        	        if( event_press(FinesseShot)  || event_press(CrossBtn) || event_press(PassBtn) || event_press(ShotBtn) || event_press(ThroughBall) || event_press(PaceCtrol) || event_press(PlayerRun) ){
        combo_stop(ANNOYING_RUNNER) ;set_val(FinesseShot,0);  PTSC = 2500;
    }
    
    	        if(combo_running(ANNOYING_RUNNER)  &&
    (  event_press(SprintBtn) || event_press(FinesseShot) ||  event_press(CrossBtn) || event_press(PassBtn) || event_press(ShotBtn) || event_press(ThroughBall) || event_press(PaceCtrol) || event_press(PlayerRun) ) ){
        combo_stop(ANNOYING_RUNNER) ;
    }
    
    	        if( event_press(SprintBtn) ){
        combo_stop(ANNOYING_RUNNER) ; PTSC = 1000;
    }
 
    
       if(PTSC){
        PTSC -=get_rtime();
    }
    
   if( flick_rs ) {  combo_stop(ANNOYING_RUNNER);  PTSC = 1200 }
   if (PTSC <= 0 && !get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(FinesseShot)  ){ combo_run(ANNOYING_RUNNER) }
   
//////////////////
// 3rd  step - .// 
//////////////////

// copy the following and paste it any where after main loop ends ,, for example you can put it in last line of your script 

int PTSC ;
combo ANNOYING_RUNNER {
sensitivity(PS4_LX, 40, 145);
sensitivity(PS4_LY, 40, 145);
set_val(FinesseShot,100);
wait(200);
sensitivity(PS4_LX, 120, 245);
sensitivity(PS4_LY, 120, 245);
wait(60);
set_val(PaceCtrol,100);
sensitivity(PS4_LX, 85, 245);
sensitivity(PS4_LY, 85, 245);
wait(300);
wait(500);
}
         
              
// That's it guys ,, now test the script , and let me know how was your experiment. Thanks for your time . <3
