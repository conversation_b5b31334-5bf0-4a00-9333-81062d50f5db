
/*

This script is an example of how to use OOP in GPC scripting.

CONTROLS:
	- CROSS: Call object constructors
	- CIRCLE: Call object deconstructors
	- UP/DOWN: Cylce between the different objects that are currently in memory

*/





// Object names
const string string1 = "Object 1:";
const string string2 = "Object 2:";

// Index of current object displayed
int current;

// Array for object pointers
int #objects[3];


// INIT
init {

	// Construct objects
	CallConstructors();

	// Print first object
	cls_oled(0);
	Class_print(0, 0, objects[current]);
	
}


// MAIN
main {
	
	// Press UP/DOWN to look at different objects
	if(event_press(PS4_UP  )) {
		current -= (current > 0);
		
		cls_oled(0);
		Class_print(0, 0, objects[current]);
	}
	if(event_press(PS4_DOWN)) {
		current += (current < 2);
		
		cls_oled(0);
		Class_print(0, 0, objects[current]);
	}
	
	// Press CROSS to reconstruct objects
	if(event_press(PS4_CROSS)) {
		_Class(objects[0]);
		_Class(objects[1]);
		_Class(objects[2]);
		CallConstructors();
		
		cls_oled(0);
		Class_print(0, 0, objects[current]);
	}
	
	// Press CIRCLE to deconstruct objects
	if(event_press(PS4_CIRCLE)) {
		_Class(objects[0]);
		_Class(objects[1]);
		_Class(objects[2]);
		objects[0] = 0;
		objects[1] = 0;
		objects[2] = 0;
		current = 0;
		
		cls_oled(0);
		Class_print(0, 0, objects[current]);
	}
	
	// Inspect memory addresses
	set_val(TRACE_1, objects[0]);
	set_val(TRACE_2, objects[1]);
	set_val(TRACE_3, objects[2]);

}


function CallConstructors() {
	
	// CALL OBJECT CONSTRUCTORS:
	
	// Object0 
	objects[0] = Class(-10000, 70, addr(string1));
	
	// Object1
	objects[1] = Class(-5000, 25, addr(string2));
	
	// Object2 = Object0 + Object1
	objects[2] = Class_add(objects[0], objects[1]);
	
}














//|==================|//
//| GPC Class Example|//
//|==================|//


// CONSTRUCTOR:
int #Class_pt;						// Object pointer
int Class_a, Class_b, Class_c;		// Object attribute IDs
function Class(int #a, int #b, int #c) {
	// Inherit root object (you can inherit from any class here)
	Class_pt = Object();
	
	// Create attributes and get their IDs
	Class_pt = Attribute(Var(a, sizeof(int32), SIGNED), Class_pt);
	Class_a  = Attribute_getID(Class_pt);
	
	Class_pt = Attribute(Var(b, sizeof(uint8), UNSIGNED), Class_pt);
	Class_b  = Attribute_getID(Class_pt);
	
	Class_pt = Attribute(String(c), Class_pt);
	Class_c  = Attribute_getID(Class_pt);
	
	// return the object pointer
	return Class_pt;
}

// DESTRUCTOR:
function _Class(int #self) {
	Object_free(self);
}

// Setters and Getters for Vars
function Class_setA(int value, int #self) {
	Var_set(value, Attribute_get(Class_a, self));
}
function Class_getA(int #self) {
	return Var_get(Attribute_get(Class_a, self));
}

function Class_setB(int value, int #self) {
	Var_set(value, Attribute_get(Class_b, self));
}
function Class_getB(int #self) {
	return Var_get(Attribute_get(Class_b, self));
}

// Setter and Getter for a String
function Class_setC(int constAddress, int #self) {
	Attribute_set(String(constAddress), Class_c, self);
}
function Class_getC(int #self) {
	return Attribute_get(Class_c, self);
}

// Other methods...

// Add two objects together
int Class_add_pt;
function Class_add(int #obj1, int #obj2) {
	if(obj1 > 0 && obj2 > 0) {
	
		// Create fresh object
		Class_add_pt = Class(-1, -1, -1);
		// Free the string it creates to avoid memory leak
		Memory_free(Attribute_get(Class_c, Class_add_pt));
		// Add values / concatenate strings. use class setters for values, and Attribute_set for pointers
		Class_setA(Class_getA(obj1) + Class_getA(obj2), Class_add_pt);
		Class_setB(Class_getB(obj1) + Class_getB(obj2), Class_add_pt);
		Attribute_set(String_cat(Class_getC(obj1), Class_getC(obj2)), Class_c, Class_add_pt);
		
		return Class_add_pt;
	}
	return 0;
}

// Print out an object's attributes
function Class_print(int x, int y, int #obj) {
	if(obj > 0) {
		String_print(x,      y, 0, 1, Class_getC(obj));
		String_print(x, y + 10, 0, 1, String_fromInt(Class_getA(obj)));
		Memory_freeLast();
		String_print(x, y + 20, 0, 1, String_fromInt(Class_getB(obj)));
		Memory_freeLast();
	}
}





















// You can ignore the rest of the code from this point on, its just library code that you need to copy paste



/*

<Object.gpc>
============

Description:
	Offers a limited OOP experience in GPC

Dependencies:
	<Var.gpc>
	<String.gpc>
	<List.gpc>
	<Memory.gpc>

Interface:
	int# Object()														// Instantiate a root Object to build a new object on
	void Object_free()													// Free an Object's memory
	int# Attribute(int attribute, int #object)							// Assign a new attribute to an Object. Use in class definition only
	void Attribute_set(int value, int attribute_id, int #attribute)		// Set an Attribute's memory address
	int# Attribute_get(int attribute_id, int #attribute)				// Get an Attribute's memory address

*/


int #Object_pt;
function Object() {
	Object_pt = List(0, sizeof(int16#), UNSIGNED);
	if(Object_pt > 0) return Object_pt;
	return -1;
}


function Object_free(int #address) {
	Memory_free2D(address);
}


function Attribute(int attribute, int #address) {
	return List_append(attribute, address);
}

function Attribute_getID(int #address) {
	return List_len(address) - 1;
}


function Attribute_set(int value, int attribute, int #address) {
	List_set(value, attribute, address);
}


function Attribute_get(int attribute, int #address) {
	return List_get(attribute, address);
}




/*
	
<Var.gpc>
=========

Description:
	Stores a value by pointer

Dependencies:
	<List.gpc>
	<Memory.gpc>

Interface:
	int# Var(int value, int size, int sign)		// Instantiate a Var object to hold a value
	int  set(int value, int #var)				// Set value of a Var
	int  get(int #var)							// Get value of a Var
	
*/


int #Var_pt;
int	Var_sign;
function Var(int value, int size, int sign) {
	Var_pt = List(1, size, sign);
	List_set(value, 0, Var_pt);
	return Var_pt;
}


function Var_set(int value, int #address) {
	List_set(value, 0, address);
}


function Var_get(int #address) {
	return List_get(0, address);
}




/*

<Map.gpc>
=========

Description:
	Hashmap to store key-value pairs. String key gets hashed to 16-bit key.
	
Dependencies:
	<Var.gpc>
	<String.gpc>
	<List.gpc>
	<Memory.gpc>

Interface:
	int# Map(int data_type, int sign)				// Instantiates an empty Map object
	int# append(int #key, int value, int #map)		// Appends a key-value pair to a Map
	int# remove(int #key, int #map)					// Removes a key-value pair from a Map
	void set(int value, int #key, int #map)			// Sets a value, by key in a key-value pair in a Map
	int  get(int #key, int #map)					// Gets a value, by key, from a key-value pair in a Map

*/


int hashString_hash,
	hashString_len,
	hashString_i;
function Map_hashString(int #address) {
    hashString_hash = 0;
    hashString_len = String_len(address);
    for (hashString_i = 0; hashString_i < hashString_len; hashString_i++) {
        hashString_hash = hashString_hash + ((hashString_hash) << 5) + List_get(hashString_i, address) + ((List_get(hashString_i, address)) << 7);
    }
    return ((hashString_hash) ^ (hashString_hash >> 16)) & 0xffff;
}


int #Map_pt;
function Map(int type, int sign) {
	Map_pt = List(2, sizeof(int16#), UNSIGNED);
	List_set(List(0, sizeof(int16 ), UNSIGNED), 0, Map_pt);
	List_set(List(0, type          , sign    ), 1, Map_pt);
	return Map_pt;
}


int insert_hashed;
function Map_append(int #key, int value, int #address) {
	insert_hashed = Map_hashString(key);
	List_set(List_append(insert_hashed, List_get(0, address)), 0, address);
	List_set(List_append(value        , List_get(1, address)), 1, address);
}


int erase_hashed,
	erase_len,
	erase_i;
function Map_remove(int #key, int #address) {
	erase_hashed = Map_hashString(key);
	erase_len = String_len(key);
	for(erase_i=0; erase_i<erase_len; erase_i++) {
		if(List_get(erase_i, List_get(0, address)) == erase_hashed) {
			List_set(List_remove(erase_i, List_get(0, address)), 0, address);
			List_set(List_remove(erase_i, List_get(1, address)), 1, address);
		}
	}
}


int set_hashed,
	set_len,
	set_i;
function Map_set(int value, int #key, int #address) {
	set_hashed = Map_hashString(key);
	set_len = String_len(key);
	for(set_i=0; set_i<set_len; set_i++) {
		if(List_get(set_i, List_get(0, address)) == set_hashed) {
			List_set(value, set_i, List_get(1, address));
			return;
		}
	}
}


int get_hashed,
	get_len,
	get_i;
function Map_get(int #key, int #address) {
	get_len = String_len(key);
	get_hashed = Map_hashString(key);
	for(get_i=0; get_i<get_len; get_i++) {
		if(List_get(get_i, List_get(0, address)) == get_hashed) {
			return List_get(get_i, List_get(1, address));
		}
	}
	return 0;
}




/*

<String.gpc>
============

Description:
	A dynamic string system

Dependencies:
	<List.gpc>
	<Memory.gpc>

Interface:
	int# String(int string_addr)															// Instantiates a dynamic String object from a const string
	String_print(int x, int y, int font, int colour, int #string)							// Prints a String at a position on OLED screen
	String_printn(int start, int end, int x, int y, int font, int colour, int #string)		// Prints a range of chars from a String on OLED screen
	String_dataStringLen(int address, int size)												// Gets length of a data section string
	void set(int value, int index, int #list)        										// Set a String index to a char value
	int  get(int index, int #list)                   										// Get a char value from a String index
	int# append(int value, int #list)                										// Append a char to a String
	int# remove(int index, int #list)                										// Remove a char from a String by index
	int# insert(int value, int index, int #list)     										// Insert a char into a String at an index
	int# len(int #string)                                      								// Gets number of chars in a String
	int# cat(int #string1, int #string2)													// Concatenates two Strings together
	int# rev(int #string)																	// Reverses the order of a String
	int# cpy(int #string)																	// Makes a copy of a String
	int  cmp(int #string1, int #string2)													// Compares two strings (0 same, 1 diff)
	int# fromInt(int value)																	// Converts integer to a String
	int# toInt(int #string)																	// Converts a String to an integer
	int# fromHex(int value)																	// Converts a hex value to a String
	int# toHex(int #string)																	// Converts a hex String to a hex value
	
*/


int #String_pt;
int String_i,
	String_len;
function String(int address) {
	String_len = String_dataStringLen(address);
	String_pt = List(String_len, sizeof(char), UNSIGNED);
	if(String_pt >= 0) {
		String_i = 0;
		while(duint8(address + String_i)) {
			List_set(duint8(address + String_i), String_i, String_pt);
			String_i++;
		}	
		return String_pt;	
	}
	return -1;
}


int print_i,
	print_len;
function String_print(int x, int y, int font, int color, int #address) {
	print_len = List_len(address);
	for(print_i=0; print_i<print_len; print_i++) {
		putc_oled(print_i + 1, List_get(print_i, address));
	}
	puts_oled(x, y, font, print_len, color);
}


int printn_i,
	printn_len;
function String_printn(int start, int end, int x, int y, int font, int color, int #address) {
	printn_len = List_len(address);
	if(start >= printn_len) return;
	for(printn_i=start; (printn_i<end) && (printn_i<printn_len); printn_i++) {
		putc_oled(printn_i - start + 1, List_get(printn_i, address));
	}
	puts_oled(x, y, font, min(printn_len, end) - start, color);
}


int dataStringLen_i;
function String_dataStringLen(int address) {
	dataStringLen_i = 0;
	while(duint8(address + dataStringLen_i)) {
		dataStringLen_i++;
	}
	return dataStringLen_i;
}


int dataStringArrayLen_sCount;
int dataStringArrayLen_bytes;
function String_dataStringArrayLen(int stringAddr, int arraySize)
{
	dataStringArrayLen_sCount = 0;
	dataStringArrayLen_bytes = 0;
	while(dataStringArrayLen_bytes < arraySize)
	{
		if(!duint8(stringAddr + dataStringArrayLen_bytes)) dataStringArrayLen_sCount ++;
		dataStringArrayLen_bytes++;
	}	
	return dataStringArrayLen_sCount;
}


function String_len(int #address) {
	return List_len(address);
}


function String_set(int value, int index, int #address) {
	List_set(value, index, address);
}


function String_get(int index, int #address) {
	return List_get(index, address);
}


function String_append(int value, int #address) {
	return List_append(value, address);
}


function String_insert(int value, int index, int #address) {
	return List_insert(value, index, address);
}


function String_remove(int index, int #address) {
	return List_remove(index, address)
}


int #cat_pt;
int cat_size1,
	cat_size2,
	cat_size,
	cat_i;
function String_cat(int #address1, int #address2) {
	cat_size1 = String_len(address1);
	cat_size2 = String_len(address2);
	cat_size = cat_size1 + cat_size2;
	cat_pt = List(cat_size, sizeof(char), UNSIGNED);
	if(cat_pt >= 0) {
		cat_i = 0;
		while(cat_i < cat_size) {
			if(cat_i < cat_size1) {
				List_set(List_get(cat_i, address1), cat_i, cat_pt);
			}
			else {
				List_set(List_get(cat_i - cat_size1, address2), cat_i, cat_pt);
			}
			cat_i++;
		}
		return cat_pt;
	}
	return -1;
}


int #rev_pt;
int rev_i,
	rev_len;
function String_rev(int #address) {
	rev_len = String_len(address);
	rev_pt = List(rev_len, sizeof(char), UNSIGNED);
	if(rev_pt >= 0) {
		for(rev_i=0; rev_i < rev_len; rev_i++) {
			List_set(List_get(rev_i, address), rev_len - rev_i - 1, rev_pt);
		}
		return rev_pt;
	}
	return -1;
}


int #cpy_pt;
int cpy_i,
	cpy_len;
function String_cpy(int #address) {
	cpy_len = String_len(address);
	cpy_pt = List(cpy_len, sizeof(char), UNSIGNED);
	if(cpy_pt >= 0) {
		for(cpy_i=0; cpy_i < cpy_len; cpy_i++) {
			List_set(List_get(cpy_i, address), cpy_i, cpy_pt);
		}
		return cpy_pt;
	}
	return -1;
}


int cmp_i,
	cmp_len1,
	cmp_len2;
function String_cmp(int #address1, int #address2) {
	cmp_len1 = String_len(address1);
	cmp_len2 = String_len(address2);
	if(cmp_len1 != cmp_len2) return 1;
	for(cmp_i=0; cmp_i<cmp_len1; cmp_i++) {
		if(List_get(cmp_i, address1) != List_get(cmp_i, address2)) return 1;
	}
	return 0;
}


int #fromInt_pt;
int fromInt_int1,
	fromInt_i,
	fromInt_digit,
	fromInt_digits,
	fromInt_len;
function String_fromInt(int integer) {
	fromInt_int1 = integer;
	fromInt_digits = 0;
	do {
    	fromInt_digits ++;
    	fromInt_int1 /= 10;
	}
	while(fromInt_int1);
	fromInt_len = fromInt_digits;
	if(integer < 0) {
		fromInt_len++;
	}
	fromInt_pt = List(fromInt_len, sizeof(char), UNSIGNED);
	if(fromInt_pt >= 0) {
		if(integer < 0) {
			integer *= -1;
			List_set(ASCII_MINUS, 0, fromInt_pt);
		}
		for(fromInt_i=0; fromInt_i<fromInt_digits; fromInt_i++) {
			fromInt_digit = integer / pow(10,fromInt_i) % 10;
			List_set(ASCII_DIGIT0 + fromInt_digit, fromInt_len - fromInt_i - 1, fromInt_pt);
		}
		return fromInt_pt;
	}
	return -1;
}


int toInt_len,
	toInt_i,
	toInt_digit,
	toInt_val,
	toInt_sign,
	toInt_start;
function String_toInt(int #address) {
	toInt_start = 0;
	toInt_sign = 1;
	if(List_get(0, address) == ASCII_MINUS) {
		toInt_sign = -1;
		toInt_start = 1;
	}
	toInt_len = List_len(address);
	toInt_val = 0;
	for(toInt_i=toInt_start; toInt_i<toInt_len; toInt_i++) {
		toInt_digit = List_get(toInt_i, address) - ASCII_DIGIT0;
		if(toInt_digit >= 0 && toInt_digit <= 9) {
			toInt_val += toInt_digit * pow(10, toInt_len - toInt_i - 1);
		}
		else return -1
	}
	return toInt_val * toInt_sign;
}


int #fromHex_pt;
int fromHex_i,
	fromHex_temp,
	fromHex_zeros;
const string hex = "0123456789abcdef";
function String_fromHex(int integer) {
	fromHex_zeros = TRUE;
	fromHex_pt = List(0, sizeof(char), UNSIGNED);
	for(fromHex_i=0; fromHex_i<INT_BITS/4; fromHex_i++) {
		fromHex_temp = ((integer & (0x0000000F << (4 * (7 - fromHex_i)))) >> (4 * (7 - fromHex_i))) & 0x0000000F;
		if(fromHex_temp && fromHex_zeros) fromHex_zeros = FALSE;
		if(!fromHex_zeros) fromHex_pt = List_append(duint8(hex[fromHex_temp]), fromHex_pt);
	}
	return fromHex_pt;
}


int toHex_i,
	toHex_len,
	toHex_temp,
	toHex_value;
function String_toHex(int #address) {
	toHex_len = List_len(address);
	if(toHex_len > 8 || toHex_len < 1) return 0;
	toHex_value = 0;
	for(toHex_i=0; toHex_i<toHex_len; toHex_i++) {
		toHex_temp = List_get(toHex_i, address);
		if(toHex_temp >= ASCII_LOWER_A && toHex_temp <= ASCII_LOWER_F) {
			toHex_temp = toHex_temp - ASCII_LOWER_A + 10;
		}
		else if(toHex_temp >= ASCII_UPPER_A && toHex_temp <= ASCII_UPPER_F) {
			toHex_temp = toHex_temp - ASCII_UPPER_A + 10;
		}
		else if(toHex_temp >= ASCII_DIGIT0 && toHex_temp <= ASCII_DIGIT9) {
			toHex_temp = toHex_temp - ASCII_DIGIT0;
		}
		else return 0;
		toHex_value = (toHex_value << 4) | toHex_temp;
	}
	return toHex_value
}




/*

<List.gpc>
==========

Description:
	A dynamic array system. Stores: char, uint8, int8, uint16, int16, int32

Dependencies:
	<Memory.gpc>

Interface:
	int# List(int length, int size, int sign)        // Instantiate a List object
	void set(int value, int index, int #list)        // Set a List index to a value
	int  get(int index, int #list)                   // Get a value from a List index
	void load(int data_address, int #list)           // Load an array from the data section directly into a List (TYPES MUST MATCH)
	int# append(int value, int #list)                // Append a value to a List
	int# remove(int index, int #list)                // Remove a value from a List by index
	int# insert(int value, int index, int #list)     // Insert a value into a List at an index
	int  len(#list)                                  // Get number of elements in a List

*/


define TYPE_BITS = 2;
define SIGN_BITS = 1;
define META_BITS = TYPE_BITS + SIGN_BITS;
define UNSIGNED = 0;
define SIGNED = 1;


int #List_pt;
int List_bits,
	List_i;
function List(int length, int size, int sign) {
	List_bits = size * 8 * length + META_BITS;
	List_pt = Memory_malloc(List_bits);
	if(List_pt > 0) {
		List_setType(size, List_pt);
		List_setSign(sign, List_pt);
		for(List_i=0; List_i<length; List_i++) {
			Memory_setBits(0, List_pt + META_BITS + List_i * size * 8, size * 8);
		}
		return List_pt;
	}
	return -1;
}


int set_bits,
	set_size;
function List_set(int value, int index, int #address) {
	set_size = List_getType(address);
	set_bits = set_size * 8;
	Memory_setBits(value, address + META_BITS + index * set_bits, set_bits);
}


int get_size,
	get_sign,
	get_bits;
function List_get(int index, int #address) {
	get_size = List_getType(address);
	get_sign = List_getSign(address);
	get_bits = get_size * 8;
	return List_unpack(Memory_getBits(address + META_BITS + index * get_bits, get_bits), get_bits, get_sign);
}


function List_unpack(int value, int bits, int sign) {
	if(sign) return (value << (32 - bits)) >> (32 - bits);
	return value;
}


int import_i,
	import_len;
function List_load(int address1, int #address2) {
	import_len = List_len(address2);
	if(List_getType(address2) == 1) {
		for(import_i=0; import_i<import_len; import_i++) {
			List_set(duint8(address1 + import_i), import_i, address2);
		}
	}
	else if(List_getType(address2) == 2) {
		for(import_i=0; import_i<import_len; import_i++) {
			List_set(duint16(address1 + import_i * 2), import_i, address2);
		}
	}
	else if(List_getType(address2) == 4) {
		for(import_i=0; import_i<import_len; import_i++) {
			List_set(dint32(address1 + import_i * 4), import_i, address2);
		}
	}
	return address2;
}


int #append_pt;
int append_len,
	append_size;
function List_append(int value, int #address) {
	append_len = List_len(address) + 1;
	append_size = List_getType(address);
	append_pt = Memory_realloc(address, META_BITS + append_len * append_size * 8);
	Memory_setBits(value, append_pt + META_BITS + (append_len - 1) * append_size * 8, append_size * 8);
	return append_pt;
}


int #insert_pt;
int insert_len,
	insert_size,
	insert_i,
	insert_val,
	insert_tmp;
function List_insert(int value, int index, int #address) {
	insert_len = List_len(address) + 1;
	insert_size = List_getType(address);
	insert_pt = Memory_realloc(address, META_BITS + insert_len * insert_size * 8);
	insert_val = value;
	for(insert_i=index; insert_i<insert_len; insert_i++) {
		insert_tmp = Memory_getBits(insert_pt + META_BITS + insert_i * insert_size * 8, insert_size * 8);
		Memory_setBits(insert_val, insert_pt + META_BITS + insert_i * insert_size * 8, insert_size * 8);
		insert_val = insert_tmp;
	}
	return insert_pt;
}


int remove_len,
	remove_size,
	remove_i,
	remove_j, 
	remove_val;
function List_remove(int index, int #address) {
	remove_len = List_len(address);
	remove_size = List_getType(address);
	remove_j = 0;
	for(remove_i=0; remove_i<remove_len; remove_i++) {
		if(remove_i != index) {
			remove_val = Memory_getBits(address + META_BITS + remove_i * remove_size * 8, remove_size * 8)
			Memory_setBits(remove_val, address + META_BITS + remove_j * remove_size * 8, remove_size * 8);
			remove_j++;
		}
	}
	Memory_free(address + META_BITS + remove_size * remove_j * 8);
	return address;
}


int getListLen_i,
	getListLen_size,
	getListLen_len;
function List_len(int #address) {
	getListLen_size = List_getType(address);
	getListLen_i = address + META_BITS;
	getListLen_len = 0;
	while(Memory_getBit(getListLen_i, INIT) == 1) {
		getListLen_i += getListLen_size * 8;
		getListLen_len++;
	}
	return getListLen_len;
}


function List_setType(int size, int #address) {
	Memory_setBits(size - 1, address, TYPE_BITS);
}


function List_getType(int #address) {
	return Memory_getBits(address, TYPE_BITS) + 1;
}


function List_setSign(int sign, int #address) {
	Memory_setBits(sign, address + TYPE_BITS, SIGN_BITS);
}


function List_getSign(int #address) {
	return Memory_getBits(address + TYPE_BITS, SIGN_BITS);
}




/*

<Memory.gpc>
============

Description:
	Enables a preallocated section of the Zen's stack memory to function as heap memory.
	Functionally Adds pointers to GPC (denoted by #).

Dependencies:
	none

Interface:
	int# malloc(int bits)                                 // Allocate heap bits to a pointer
	int# realloc(int #address, int bits)                  // Reallocate heap bits to a pointer
	void free(int #address)                               // Free bits from a pointer
	void freeAll()                                        // Free bits from all pointers
	void free2D(int #address)                             // Free a 2D pointer
	void setBits(int value, int #address, int bits)       // Write a value to heap memory
	int  getBits(int #address, bits)                      // Read a value from heap memory
	void setBit(int value, int #address, int mode)        // Set a bit's DATA or INIT value in heap memory
	int  getBit(int #address, int mode)                   // Get a bit's DATA or INIT value from heap memory
	int  bitsAllocated()                                  // Get total number of allocated bits in heap memory

*/


define INT_BITS = 32;
define HEAP_VARS = 400;
define TOTAL_ADDRESSABLE_BITS =  HEAP_VARS * INT_BITS / 2;
define DATA = 0;
define INIT = 1;


int Memory_start,
	Memory_lastPt,
	int32;


int HEAP[HEAP_VARS];


int malloc_i,
	malloc_j;
function Memory_malloc(int bits) {
	malloc_i = 0;
	while(malloc_i < TOTAL_ADDRESSABLE_BITS) {
		if(!Memory_getBit(malloc_i, INIT) && !Memory_getBit(malloc_i + 1, INIT)) {
			malloc_i++;
			malloc_j = 0;
			while(!Memory_getBit(malloc_i + malloc_j, INIT) && malloc_j <= bits && malloc_i + malloc_j < TOTAL_ADDRESSABLE_BITS) {
				if(malloc_j == bits) {
				malloc_j = 0;
					while(!Memory_getBit(malloc_i + malloc_j, INIT) && malloc_j <= bits) {
						Memory_setBit(1, malloc_i + malloc_j, INIT);
						if(malloc_j == bits - 1) {
							Memory_lastPt = malloc_i;
							return malloc_i;	
						}
						malloc_j++;
					}	
				}
				malloc_j++;
			}
			malloc_i += malloc_j;
		}
		malloc_i++;
	}

	return -1;	
}


int realloc_i,
	realloc_j,
	realloc_new,
	alloc_bits;
function Memory_realloc(int #address, int bits) {
	realloc_i = 0;
	alloc_bits = bits;
	while(Memory_getBit(address + realloc_i, INIT) == 1) {
		realloc_i++;
	}	
	bits -= realloc_i;
	while(realloc_i < TOTAL_ADDRESSABLE_BITS) {
		if(!Memory_getBit(address + realloc_i, INIT) && !Memory_getBit(address + realloc_i + 1, INIT)) {
			realloc_j = 0;
			while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits && realloc_i + realloc_j < TOTAL_ADDRESSABLE_BITS) {
				if(realloc_j == bits) {
				realloc_j = 0;
					while(!Memory_getBit(address + realloc_i + realloc_j, INIT) && realloc_j <= bits) {
						Memory_setBit(1, address + realloc_i + realloc_j, INIT);
						if(realloc_j == bits - 1) {
							Memory_lastPt = address;
							return address;	
						}
						realloc_j++;
					}	
				}
				realloc_j++;
			}
			realloc_i += realloc_j;
		}
		else break;
		realloc_i++;
	}
	
	realloc_new = Memory_malloc(alloc_bits);
	if(realloc_new >= 0) {
		realloc_i = 0;
		while(Memory_getBit(address + realloc_i, INIT) == 1) {
			Memory_setBit(Memory_getBit(address + realloc_i, DATA), realloc_new + realloc_i, DATA);
			realloc_i++;
		}
		Memory_free(address);
		return realloc_new;
	}
	
	return -1;	
}


int free_i;
function Memory_free(int #address) {
	free_i = address;
	while(Memory_getBit(free_i, INIT) == 1) {
		Memory_setBit(0, free_i, DATA);
		Memory_setBit(0, free_i, INIT);
		free_i++;
	}
}


function Memory_freeLast() {
	Memory_free(Memory_lastPt);
}


int freeAll_i;
function Memory_freeAll() {
	for(freeAll_i=0; freeAll_i<HEAP_VARS; freeAll_i++) {
		HEAP[freeAll_i] = 0;
	}
}


int free2D_i,
	free2D_len;
function Memory_free2D(int #address) {
	free2D_len = List_len(address);
	for(free2D_i=0; free2D_i<free2D_len; free2D_i++) {
		Memory_free(List_get(free2D_i, address));
	}
	Memory_free(address);
}


int setBits_i,
	setBits_bitVal;
function Memory_setBits(int value, int #address, int bits) {
	setBits_bitVal = 0;
	for(setBits_i=0; setBits_i<bits; setBits_i++) {
		if(Memory_getBit(address + setBits_i, INIT)) {
			setBits_bitVal = ((value & Memory_bitMask(setBits_i)) >> (setBits_i)) & 0x01;
			Memory_setBit(setBits_bitVal, address + setBits_i, DATA);
		}
	}
}


int getBits_i,
	getBits_bitVal,
	getBits_value;
function Memory_getBits(int #address, int bits) {
	getBits_bitVal = 0;
	getBits_value = 0;
	for(getBits_i=0; getBits_i<bits; getBits_i++) {
		if(Memory_getBit(address + getBits_i, INIT)) {
			getBits_bitVal = Memory_getBit(address + getBits_i, DATA);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << getBits_i);
		}
	}
	return getBits_value;
}


int getBits_c;
function Memory__getBits(int #address, int bits) {
	getBits_bitVal = 0;
	getBits_value = 0;
	getBits_c = 0;
	for(getBits_i=0; getBits_i<bits; getBits_i++) {
		if(!((address + getBits_i) % 2)) {
			getBits_bitVal = Memory_getBit((address / 2) + getBits_c, DATA);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << getBits_i);
		}
		else {
			getBits_bitVal = Memory_getBit((address / 2) + getBits_c, INIT);
			getBits_value = (getBits_value & ~Memory_bitMask(getBits_i)) | (getBits_bitVal << (getBits_i));
			getBits_c++;
		}
	}
	return getBits_value;
}


int bitsAllocated_i,
	bitsAllocated_count,
	bitsAllocated_finalBit;
function Memory_bitsAllocated() {
	bitsAllocated_count = 0;
	for(bitsAllocated_i=0; bitsAllocated_i<TOTAL_ADDRESSABLE_BITS; bitsAllocated_i++) {
		if(Memory_getBit(bitsAllocated_i, INIT)) {
			bitsAllocated_count++; 
			bitsAllocated_finalBit = bitsAllocated_i;
		}
	}
	return bitsAllocated_count;
}


int setBit_int,
	setBit_bit;
function Memory_setBit(int value, int #address, int mode) {
	if(abs(value) > 1) return;
	address *= 2;
	address += mode;
	setBit_int = address / INT_BITS;
	setBit_bit = address % INT_BITS;
	HEAP[setBit_int] = (HEAP[setBit_int] & ~Memory_bitMask(setBit_bit)) | (value << setBit_bit);
}


int getBit_int,
	getBit_bit;
function Memory_getBit(int #address, int mode) {
	address *= 2;
	address += mode;
	getBit_int = address / INT_BITS;
	getBit_bit = address % INT_BITS;
	return (HEAP[getBit_int] & Memory_bitMask(getBit_bit) >> (getBit_bit)) & Memory_bitMask(0);
}


function Memory_bitMask(int bit) {
	return 1 << bit;
}




/*

<EEPROM.gpc>
============

Description:
	Allows for precise control over a slots allocated ROM (bitpacking). Adapted from Swizzy's bitpacking,
	this version allows for read/write access to specific bit addresses at any time.

Dependencies:
	none
	
Interface:
	void setBits(int value, int address, int bits)		// Write a value of a specific bit-size to a bit-address in slot EEPROM
	int  getBits(int address, int bits)					// Read a value of a specific bit-size from a bit-address in slot EEPROM

*/


int EEPROM_currentBit,
	EEPROM_currentSlot,	
	EEPROM_currentValue,
	EEPROM_temp;
	

function EEPROM_setBits(int val, int address, int bits) {
	EEPROM_currentSlot = SPVAR_1 + address / 32;
	EEPROM_currentBit = address % 32;
	EEPROM_currentValue = EEPROM_getSlot(EEPROM_currentSlot) & EEPROM_makeFullMask(EEPROM_currentBit);

	val = val & EEPROM_makeFullMask(bits); 
	if (bits > 32 - EEPROM_currentBit) {
		EEPROM_currentValue = EEPROM_currentValue | (val << EEPROM_currentBit);
		set_pvar(EEPROM_currentSlot, EEPROM_currentValue); 
		EEPROM_currentSlot++;
		bits -= (32 - EEPROM_currentBit); 
		val = val >> (32 - EEPROM_currentBit); 
		EEPROM_currentBit = 0; 
		EEPROM_currentValue = 0; 
	}

	EEPROM_currentValue = EEPROM_getSlot(EEPROM_currentSlot) & EEPROM_makeSaveMask(EEPROM_currentBit, bits);
	EEPROM_currentValue = EEPROM_currentValue | (val << EEPROM_currentBit); 
	set_pvar(EEPROM_currentSlot, EEPROM_currentValue); 
}


function EEPROM_getBits(int address, int bits) {
	EEPROM_currentSlot = SPVAR_1 + address / 32;
	EEPROM_currentBit = address % 32;
	EEPROM_currentValue = (EEPROM_getSlot(EEPROM_currentSlot) >> EEPROM_currentBit) & EEPROM_makeFullMask(bits); 

	if (bits > 32 - EEPROM_currentBit) {
		EEPROM_temp = EEPROM_getSlot(EEPROM_currentSlot + 1);
		EEPROM_temp = EEPROM_temp & EEPROM_makeFullMask(bits - (32 - EEPROM_currentBit));
		EEPROM_temp = EEPROM_temp << (32 - EEPROM_currentBit);
		EEPROM_currentValue = (EEPROM_currentValue & EEPROM_makeFullMask(32 - EEPROM_currentBit)) | EEPROM_temp;
	}
	
	EEPROM_currentBit += bits; 
	EEPROM_currentValue = EEPROM_currentValue & EEPROM_makeFullMask(bits); 	
	
	return EEPROM_currentValue;
}


function EEPROM_makeFullMask(bits) {	
	if (bits == 32) {
		return -1;
	}
	return 0x7FFFFFFF >> (31 - bits); 
}


function EEPROM_makeSignMask(bits) {
	return EEPROM_makeFullMask(bits - 1);
}


function EEPROM_getSlot(slot) { 
	return get_pvar(slot, 0x80000000, 0x7FFFFFFF, 0);
}


function EEPROM_makeSaveMask(int EEPROM_currentBit, int bits) {
	return ~(EEPROM_makeFullMask(bits) << (EEPROM_currentBit));
}