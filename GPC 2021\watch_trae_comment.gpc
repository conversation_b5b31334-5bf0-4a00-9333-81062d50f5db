// Constants for text alignment
define Left = -1  
define Center = -2 
define Right = -3  
define String = 0  
define Value = 1 

// Variables for timers and display control
int ScreenSaverTimer, Display = TRUE, h, m, s, AfkTime;

// Width of fonts used for OLED display (Small, Medium, Large)
const uint8 width [] = {7,11,16};

// Separator for clock display
const string clock = ":";

// Main loop - runs continuously while the script is active
main {
    // Increment screen saver timer with the time since last execution
    ScreenSaverTimer += get_rtime();
    // Activate display after 28 seconds of inactivity
    if(ScreenSaverTimer >= 28000){
        Display = TRUE;
    }
    // Reset screen saver timer after 30 seconds
    if(ScreenSaverTimer >= 30000){
        ScreenSaverTimer = 0;
        Display = TRUE;
    }   
    
    // Increment AFK (Away From Keyboard) timer
    AfkTime += get_rtime();
    // Calculate time components from AFK timer (milliseconds)
    s = (AfkTime / 1000) % 60;       // Seconds (0-59)
    m = (AfkTime / 60000) % 60;      // Minutes (0-59)
    h = AfkTime / 3600000;           // Hours (unlimited)
    // Update display every second (when milliseconds roll over)
    if(!(AfkTime % 1000)){
        Display = TRUE;
    }
    // Display clock if the display flag is set
    if(Display){
        // Clear the OLED screen with black background
        cls_oled(OLED_BLACK);
        
        // Only show clock if screen saver isn't fully activated
        if(ScreenSaverTimer <= 28000){
            // Display hours in large font
            print_clock(h,11,33,OLED_FONT_LARGE,OLED_WHITE);
            
            // Display colon separator
            Print(clock[0],43,33,OLED_FONT_LARGE,OLED_WHITE,String);
            
            // Display minutes in large font
            print_clock(m,59,33,OLED_FONT_LARGE,OLED_WHITE);
            
            // Display seconds in medium font (slightly lower position)
            print_clock(s,95,39,OLED_FONT_MEDIUM,OLED_WHITE);
            
            // Draw a border around the display area
            rect_oled(0, 0, 128, 64, 0, 1);
        }
    }
    // Reset display flag to prevent continuous redrawing
    Display = FALSE;    
}

// Function to print clock digits with leading zero if needed
// Parameters:
//   f_string: The number to display (hours, minutes, or seconds)
//   f_x: X-coordinate position
//   f_y: Y-coordinate position
//   f_font: Font size to use
//   f_color: Color of the text
function print_clock (f_string, f_x, f_y, f_font, f_color){
    if(find_digits(f_string) == 1){
        // If single digit, print leading zero first
        Print (0, f_x, f_y, f_font, f_color, Value);
        // Then print the actual digit offset by the width of the first digit
        Print (f_string, f_x + width[f_font], f_y, f_font, f_color, Value);
    }
    else{
        // If two or more digits, print the number directly
        Print (f_string, f_x, f_y, f_font, f_color, Value);
    }
}

// General print function for OLED display
// Handles both string and numeric values with alignment options
int n, i;
function Print (f_string, f_x, f_y, f_font, f_color, f_type){
    if(!f_type){
        // String type - calculate position based on alignment and print
        print(x_location(get_string_length(f_string), f_font, f_x), f_y, f_font, f_color, f_string);
    }
    else{
        // Value type - convert number to digits and print
        if(f_string < 0){putc_oled(1,45);} // Print negative sign if needed
        
        if(f_string){
            // Convert number to individual digits and store in buffer
            for(n = find_digits(f_string) + i = (f_string < 0), f_string = abs(f_string); f_string > 0; n--, i++){  
                putc_oled(n, f_string % 10 + 48); // Convert digit to ASCII and store
                f_string = f_string / 10;         // Move to next digit
            }
        }
        else{
            // Special case for zero
            putc_oled(1,48); i = 1; // Print zero if value is zero
        }
        // Output the buffer to OLED at calculated position
        puts_oled(x_location(i, f_font, f_x), f_y, f_font, i, f_color);
    }
}

// Function to calculate string length (by Swizzy)
// Returns the number of characters in a null-terminated string
int stringLength;
function get_string_length(offset) {
    stringLength = 0;
    while (duint8(offset++)) { stringLength++; }
    return stringLength + 1;
}

// Function to calculate x location based on alignment (by Jbaze122)
// Handles left, center, right alignment or absolute positioning
function x_location(f_chars, f_font, f_x) {
    if(f_x == -3){return 128 - ((f_chars * (7 + (f_font > 1) + f_font * 4)) + 3 );} // Right alignment
    if(f_x == -2){return 64 - ((f_chars * (7 + (f_font > 1) + f_font * 4)) / 2);}  // Center alignment  
    if(f_x == -1){return 3;} // Left alignment
    return f_x;     // Absolute position
}

// Function to find number of digits in a number
// Returns the count of digits (1 for 0-9, 2 for 10-99, etc.)
function find_digits(f_num) {
    for(n = 1; n < 11; n++){
        if(!(abs(f_num) / pow(10,n))){
            return n; 
            break;
        }
    }
    return 1; // Default to 1 digit if something goes wrong
}