int DA_FC370;
// 10  Free Kick

/* Adjustable Variables */
int DA_FC424;
// 26

int DA_FC447 ;
// 42

const string DA_FC611 = "FK_POWER";

main {
if (DA_FC370) {
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_SHARE)) {
			DA_FC609 = !DA_FC609;
			DA_FC243(DA_FC609);
										}
		set_val(PS5_SHARE, 0);
								}
						}
if (DA_FC609 && DA_FC370) {
	vm_tctrl(0);
	combo_stop(DA_FC86);
	if (get_ival(XB1_RS)) {
		if (event_press(PS5_UP)) {
			DA_FC424 += 10;
										}
		if (event_press(PS5_DOWN)) {
			DA_FC424 -= 10;
										}
		set_val(PS5_UP, 0);
		set_val(PS5_DOWN, 0);
								}
	DA_FC236(DA_FC1052);
	if (get_ival(PS5_L1)) {
		if (event_press(PS5_RIGHT)) {
			DA_FC616 = FALSE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		if (event_press(PS5_LEFT)) {
			DA_FC616 = TRUE;
			vm_tctrl(0);
			combo_run(DA_FC78);
										}
		set_val(PS5_L1,0);
								}
						}
}

combo DA_FC86 {
vm_tctrl(0);
wait(750);
set_val(DA_FC449,100);
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);
if(DA_FC1123 == 1 ){
set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)}
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);

	}
	
combo DA_FC87 {
	vm_tctrl(0);
	wait( 800);
	DA_FC1147 = 0;
	}
	
///////////FK Combos//////////
int DA_FC609;
int DA_FC616;
combo DA_FC78 {
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, -100);
	vm_tctrl(0);
	wait( 70);
	if (DA_FC616) set_val(XB1_RX, 100);
	else set_val(XB1_RX, -100);
	set_val(XB1_RY, 100);
	vm_tctrl(0);
	wait( 2000);
	if (DA_FC616) set_val(XB1_RX, -100);
	else set_val(XB1_RX, 100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 200);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC424);
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, 100);
	set_val(XB1_LY,100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 1200);
	DA_FC609 = FALSE;
	DA_FC243(DA_FC609);
	}	
	
function DA_FC243(DA_FC127) {
	combo_run(DA_FC81);
	}
function DA_FC224(DA_FC133, DA_FC226, DA_FC127) {
	DA_FC231(DA_FC127, DA_FC234(DA_FC127));
	DA_FC356 = TRUE;
	}
	
//=================================================================
//Center X Function (Made By Batts) 
//=================================================================

function DA_FC228(DA_FC142, DA_FC136) {
	}
	
int DA_FC1056;
function DA_FC236(DA_FC169) {
	for (DA_FC1056 = 0;
	DA_FC1056 < 3;
	DA_FC1056++) {
			}
	}
	
	
	int DA_FC625;
define DA_FC1048 = 0;
define DA_FC1049 = 1;
define DA_FC1050 = 2;
define DA_FC1051 = 3;
define DA_FC1052 = 4;
define DA_FC1053 = 5;
define DA_FC1054 = 6;
define DA_FC1055 = 7;

int DA_FC1096;
combo DA_FC81 {
	vm_tctrl(0);
	wait( 300);
	vm_tctrl(0);
	wait( 20);
	}
	
function DA_FC231(DA_FC127, DA_FC159) {
	DA_FC1038 = 1;
	DA_FC1040 = 10000;
	if (DA_FC127 < 0) {
		//--neg numbers
		//--add leading "-"
		DA_FC1038 += 1;
		DA_FC127 = abs(DA_FC127);
			}
	for (DA_FC1039 = 5;
	DA_FC1039 >= 1;
	DA_FC1039--) {
		if (DA_FC159 >= DA_FC1039) {
			DA_FC127 %= DA_FC1040;
			DA_FC1038++;
			if (DA_FC1039 == 4) {
				//--add ","
				DA_FC1038++;
							}
					}
		DA_FC1040 /= 10;
			}
	}

int DA_FC1044;
function DA_FC234(DA_FC235) {
	DA_FC1044 = 0;
	do {
		DA_FC235 /= 10;
		DA_FC1044++;
			}
	while (DA_FC235);
	return DA_FC1044;
	}
int DA_FC356 = FALSE, DA_FC357;