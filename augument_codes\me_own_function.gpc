// This script maps the Right Stick's polar coordinates to the Left Stick
// with adjustments for angle inversion and radius scaling

// Main execution block - continuously copies Right Stick values to Left Stick
main {
  _set_polar(POLAR_LS, _get_polar(POLAR_RS, POLAR_ANGLE), _get_ipolar(POLAR_RS, POLAR_RADIUS));
}

// Sets polar coordinates for a given stick with radius normalization and angle correction
// @param stick: The stick to set values for (POLAR_LS or POLAR_RS)
// @param angle: The angle in degrees (0-359)
// @param radius: The radius magnitude
function _set_polar(stick, angle, radius) {
  // Normalize radius using a scaling factor (14142/46340 ≈ 0.3053)
  // This converts the input radius to a normalized range
  radius = (radius * 14142) / 46340;

  // Handle negative angles by converting to positive equivalent
  if (angle <= 0) {
    angle = (abs(angle) + 360) % 360;
    // Limit radius based on the angle's position in the lookup table
    set_polar2(stick, angle, min(radius, polarMax[angle % 90]));
    return;
  }

  // For positive angles, invert the angle and apply the same radius limiting
  set_polar2(stick, inv(angle % 360), min(radius, polarMax[angle % 90]));
}

// Gets polar coordinates from a stick
// @param stick: The stick to read from (POLAR_LS or POLAR_RS)
// @param angleOrRadius: POLAR_ANGLE for angle, POLAR_RADIUS for radius
// @return: Angle (0-359) or radius value
function _get_polar(stick, angleOrRadius) {
  if (angleOrRadius) 
    // For angle: Invert and normalize to 0-359 range
    return (360 - get_polar(stick, POLAR_ANGLE)) % 360;
  // For radius: Calculate magnitude using Pythagorean theorem
  return isqrt(~(pow(get_val(42 + stick), 2) + pow(get_val(43 + stick), 2))) + 1;
}

// Similar to _get_polar but uses integer values
// Useful for more precise/stable readings
function _get_ipolar(stick,angleOrRadius) {
  if (angleOrRadius)
    return (360 - get_ipolar(stick, POLAR_ANGLE)) % 360;
  return isqrt(~(pow(get_ival(42 + stick), 2) + pow(get_ival(43 + stick), 2))) + 1;
}

// Lookup table for maximum allowed radius values at different angles
// Values are optimized for smooth circular movement
// The table covers 0° to 90° and is mirrored for other quadrants
const int16 polarMax[] = { 
    10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 
    10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 
    10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 
    11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 
    13054, 13250, 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 
    13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 
    11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 
    10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 
    10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001 
};
