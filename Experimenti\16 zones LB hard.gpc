define MAX_RADIUS = 32767;
define REDUCED_RADIUS = 30000;
define STICK_DEADZONE = 3000;  // Deadzone for left stick movement
define LB_DEACTIVATION_TIME = 300;  // Time in ms to deactivate LB after direction change

int radius;
int max_allowed_radius;
int previous_angle;  // Store previous angle to detect changes
int lb_deactivation_timer;  // Timer for LB deactivation

init {
    previous_angle = -1;  // Initialize to an impossible angle value
    lb_deactivation_timer = 0;
}
int current_angle;
main {

    pass();

}

function pass() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2);
    
    // Check if either LT or RT is pressed
    if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
        max_allowed_radius = MAX_RADIUS;
    } else {
        max_allowed_radius = REDUCED_RADIUS;
    }
    
    set_polar(POLAR_LS, 
        current_angle,
        min(calculate_radius(), max_allowed_radius) // Ensure radius is within allowed limit
    );
    
    // Check if direction has changed while stick is being moved
    if(radius > STICK_DEADZONE) {
        if(previous_angle != -1 && previous_angle != current_angle) {
            // Direction changed, start deactivation timer
            lb_deactivation_timer = LB_DEACTIVATION_TIME;
        }
        
        // Update previous angle only when stick is moved
        previous_angle = current_angle;
    } else {
        // Reset previous angle when stick is not moved
        previous_angle = -1;
    }
    
    // Decrement timer if active
    if(lb_deactivation_timer > 0) {
        lb_deactivation_timer -= get_rtime();
    }
    
    // Set LB based on stick movement, deactivation timer, and trigger states
    if(radius > STICK_DEADZONE && lb_deactivation_timer <= 0 && !get_ival(XB1_LT) && !get_ival(XB1_RT) && !get_ival(XB1_RB)) {
        set_val(XB1_LB, 100);
    } else {
        set_val(XB1_LB, 0);
    }
}

define AngleInterval_2 = 8;    // Changed from 12 to 30 for 16 zones
// This defines the number of zones.  A value of 16 creates 16 zones.

function quantize_angle(angle, interval) {
    // This function quantizes the angle into discrete zones.
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)); // Calculate radius using Pythagorean theorem
}
