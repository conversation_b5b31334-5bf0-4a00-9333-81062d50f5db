// PS5-Controller-Tasten

define L2 = 7;           // L2 Taste
define R2 = 8;           // R2 Taste
define RX = 9;           // Rechte Stick X-Achse
define RY = 10;          // Rechte Stick Y-Achse

// Variablen für Funktionen
define RECOIL_Y = -6;    // Vertikaler Rückstoßausgleich (angepasst)
define RECOIL_X = 1;     // Horizontaler Rückstoßausgleich (angepasst)
define AIM_STRENGTH = 13; // Stärke des Aim Assist (reduziert)
define AIM_SPEED = 12;   // Geschwindigkeit des Aim Assist (langsamer, für Stabilität)
define AUTO_AIM_PULL = 11; // Auto-Aim-Zug (reduziert)
define HITBOX_SHIFT = 9;  // Hitbox-Anpassung (reduziert)

int recoilVariation = 0; // Dynamische Rückstoßvariation
int reconnectFlag = 0;   // Flag für Reconnect-Erkennung
int initialized = 0;     // Initialisierungs-Flag
int disconnectCounter = 0; // Zähler für Disconnect-Erkennung
int lastRX = 0;         // Letzte RX Position
int lastRY = 0;         // Letzte RY Position
int stabilityCounter = 0; // Zähler für Stabilisierung

main {
    // Initialisierung beim Start
    if (initialized == 0) {
        recoilVariation = 0;
        reconnectFlag = 0;
        disconnectCounter = 0;
        stabilityCounter = 0;
        lastRX = get_val(RX);
        lastRY = get_val(RY);
        initialized = 1;
    }

    // Verbesserte Disconnect-Erkennung
    if (get_val(RX) == 0 && get_val(RY) == 0 && get_val(L2) == 0 && get_val(R2) == 0) {
        disconnectCounter++;
        if (disconnectCounter > 10) { // Warte mehrere Frames zur Bestätigung
            reconnectFlag = 1;
            // Setze Werte auf die letzten bekannten stabilen Positionen
            set_val(RX, lastRX);
            set_val(RY, lastRY);
        }
    } else {
        disconnectCounter = 0;
        // Speichere aktuelle Positionen als letzte bekannte stabile Positionen
        if (abs(get_val(RX)) < 100 && abs(get_val(RY)) < 100) { // Nur realistische Werte speichern
            lastRX = get_val(RX);
            lastRY = get_val(RY);
        }
    }

    // Reset-Logik bei Reconnect
    if (reconnectFlag == 1) {
        if (stabilityCounter < 50) {
            set_val(RX, 0);
            set_val(RY, 0);
            stabilityCounter++;
        } else {
            reconnectFlag = 0;
            disconnectCounter = 0;
            stabilityCounter = 0;
        }
    }

    // Standardwerte setzen, wenn der Zen ausgeschaltet wird
    if (get_val(L2) == 0 && get_val(R2) == 0) {
        set_val(RX, 0); // RX auf Standard setzen
        set_val(RY, 0); // RY auf Standard setzen
    }

    // Rückstoßvariation aktualisieren
    recoilVariation = (recoilVariation + 1) % 2;

    // Automatisches Zielen auf den Gegner (Auto-Aim)
    if (get_val(L2) > 20) { // Wenn anvisiert wird
        combo_run(AutoAimCombo);
    }

    // No Recoil-Logik
    if (get_val(L2) > 0 && get_val(R2) > 0) { // Wenn geschossen wird
        combo_run(RecoilCompensation);
    }

    // Aim Assist Logik
    if (get_val(L2) > 20) { // Wenn anvisiert wird
        combo_run(AimAssistCombo);
    }

    // Hitbox-Shift Logik
    if (get_val(L2) > 20) { // Wenn anvisiert wird
        combo_run(HitboxAdjustment);
    }
}

// Rückstoßkompensation
combo RecoilCompensation {
    set_val(RY, get_val(RY) + RECOIL_Y + recoilVariation); // Vertikal mit minimaler Variation
    set_val(RX, get_val(RX) + RECOIL_X); // Horizontal angepasst
    wait(8); // Etwas längere Wartezeit für mehr Stabilität
}

// Verbesserte Aim Assist Bewegung
combo AimAssistCombo {
    set_val(RX, AIM_STRENGTH);  // Bewegung nach rechts
    wait(AIM_SPEED);
    set_val(RY, AIM_STRENGTH);  // Bewegung nach unten
    wait(AIM_SPEED);
    set_val(RX, -AIM_STRENGTH); // Bewegung nach links
    wait(AIM_SPEED);
    set_val(RY, -AIM_STRENGTH); // Bewegung nach oben
    wait(AIM_SPEED);
}

// Präziser Auto-Aim (mit Anpassung für klebenden Effekt)
combo AutoAimCombo {
    set_val(RX, get_val(RX) - AUTO_AIM_PULL); // Horizontaler Zug
    set_val(RY, get_val(RY) - AUTO_AIM_PULL); // Vertikaler Zug
    wait(10); // Längere Wartezeit für mehr Präzision
}

// Verstärkte Hitbox-Anpassung
combo HitboxAdjustment {
    set_val(RX, get_val(RX) + HITBOX_SHIFT); // Verschiebt den Zielpunkt horizontal
    wait(6); // Kürzer für schnelle Anpassung
    set_val(RY, get_val(RY) + HITBOX_SHIFT); // Verschiebt den Zielpunkt vertikal
    wait(6); 
    set_val(RX, get_val(RX) - HITBOX_SHIFT); // Zurück zur ursprünglichen Position (horizontal)
    wait(6); 
    set_val(RY, get_val(RY) - HITBOX_SHIFT); // Zurück zur ursprünglichen Position (vertikal)
    wait(6);
}