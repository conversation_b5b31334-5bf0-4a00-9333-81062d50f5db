define ShotBtn = XB1_B;      // Shot Btn         (default B/CIRCLE
define PassBtn = XB1_A;      // Short Pass Btn   (default A/CROSS)
define PlayerRun = XB1_RB;   // Player Run       (default L1/LB)
define FinesseShot = XB1_LB; // Finesse Shot     (default R1/RB)
define PaceCtrol = XB1_LT;   // Protect Ball     (default L2/LT)
define SprintBtn = XB1_RT;   // Sprint Btn       (default R2/RT)
define CrossBtn = XB1_X;     // Cross Btn        (default X/SQUARE)
define ThroughBall = XB1_Y;  // Through Ball Btn (default Y/TRIANGLE)

define None = 0;
define FAKE_SHOT_SKILL = 1;
define HEEL_TO_HEEL_FLICK_SKILL = 2;
define HEEL_FLICK_TURN_SKILL = 3;
define RAINBOW_SKILL = 4;
define DRAG_BACK_SOMBRERO_SKILL = 5;
define FAKE_PASS_SKILL = 6;
define DRAG_BACK_UNIVERSAL_SKILL = 7;
define STEP_OVER_FEINT_SKILL = 8;
define DRAG_TO_DRAG_SKILL = 9;
define HOCUS_POCUS_SKILL = 10;
define TRIPLE_ELASTICO_SKILL = 11;
define ELASTICO_SKILL = 12;
define REVERSE_ELASTICO_SKILL = 13;
define CRUYFF_TURN_SKILL = 14;
define LA_CROQUETA_SKILL = 15;
define RONALDO_CHOP_SKILL = 16;
define ROULETTE_SKILL = 17;
define FLAIR_ROULETTE_SKILL = 18;
define BALL_ROLL_SKILL = 19;
define BERBA_MCGEADY_SPIN_SKILL = 20;
define BOLASIE_FLICK_SKILL = 21;
define TORNADO_SKILL = 22;
define THREE_TOUCH_ROULETTE_SKILL = 23;
define ALTERNATIVE_ELASTICO_CHOP_SKILL = 24;
define BALL_ROLL_CHOP_SKILL = 25;
define FEINT_AND_EXIT_SKILL = 26;
define FEINT_L_EXIT_R_SKILL = 27;
define LATERAL_HEEL_TO_HEEL_SKILL = 28;
define WAKA_WAKA_SKILL = 29;
define BODY_FEINT_SKILL = 30;
define DRAG_TO_HEEL = 31;
define BALL_ROLL_FAKE_TURN = 32;
define FEINT_FORWARD_AND_TURN = 33;
define TURN_BACK = 34;
define ADVANCED_CROQUETA = 35;
define CANCELED_THREE_TOUCH_ROULETTE_SKILL = 36;
define REVERSE_STEP_OVER_SKILL = 37;
define FAKE_DRAG_BACK_SKILL = 38;
define RAINBOW_TO_SCORPION_KICK_SKILL = 39;
define STEP_OVER_BOOST_SKILL = 40;
define CANCEL_SHOOT_SKILL = 41;
define DIRECTIONAL_NUTMEG_SKILL = 42;
define CANCELED_BERBA_SPIN_SKILL = 43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION = 44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL = 45;
define DRIBBLING_SKILL = 46;
define FOUR_TOUCH_TURN_SKILLS = 47;
define SKILLED_BRIDGE_SKILL = 48;
define SCOOP_TURN_FAKE_SKILL = 49;
define BALL_ROLL_STEP_OVER_SKILL = 50;
define CANCELED_4_TOUCH_TURN_SKILL = 51;
define FAKE_SHOT_CANCEL_SKILL = 52;
define OKKOSHA_FLICK_SKILL = 53;
define ADVANCED_RAINBOW_SKILL = 54;
define STOP_LA_CROQUETA_SKILL = 55;
define JUGGLING_RAINBOW_SKILL = 56;
define STOP_NEYMAR_ROLL_SKILL = 57;
define STOP_V_DRAG_SKILL = 58;
define REV_OR_ELASTICO_SKILL = 59;
define STOP_REV_OR_ELASTICO_SKILL = 60;
define DRAG_REV_OR_ELASTICO_SKILL = 61;
define FAKE_RABONA_SKILL = 62;
define RABONA_TO_REV_ELASTICO_SKILL = 63;
define RABONA_TO_ELASTICO_SKILL = 64;
define SOMBRERO_FLICK_SKILL = 65;
define JUGGLE_BACK_SOMBRERO_SKILL = 66;
define FAKE_BERBA_OPP_EXIT_SKILL = 67;
define DIAGONAL_HEEL_CHOP_SKILL = 68;
define FAKE_BERBA_FAKE_DRAG_SKILL = 69;
define ELASTICO_CHOP_SKILL = 70;
define BALL_ROLL_CUT_180_SKILL = 71;
define HEEL_TO_BALL_ROLL_SKILL = 72;
define STUTTER_FEINT_SKILL = 73;
define JOG_OPENUP_FAKE_SHOT = 74;
define SPIN_MOVE_LEFT_RIGHT_SKILL = 75;

define MOVE_X = PS4_LX;
define MOVE_Y = PS4_LY;
define SKILL_STICK_X = PS4_RX;
define SKILL_STICK_Y = PS4_RY;


const int order[] = {2, 1, 0, 7, 6, 5, 4, 3};
int index;
int sector;


function calc_zone()
{
  polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
  index = ((polar_LS + 22.5) % 360) / 45;
  zone_p = order[index];
  return zone_p;
}

/*
int zoneRange = 60; // Customizable range for zones 0, 2, 4, and 6

function calc_zone()
{
  polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
  // Adjust the index calculation using zoneRange
  index = ((polar_LS + (zoneRange / 2)) % 360) / zoneRange;
  zone_p = order[index];
  return zone_p;
}
*/

int polar_angle;
function calc_RS()
{
  polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
  index = ((polar_angle + 22.5) % 360) / 45;
  zone_RS = order[index];
  return zone_RS;
}

int flick_rs;
int temp_zone;
function calc_temp_zone(user_zone)
{
  temp_zone = user_zone;
  if (temp_zone < 0)
    temp_zone = 8 - abs(user_zone);
  else if (temp_zone >= 8)
    temp_zone = user_zone - 8 return temp_zone;
}

function calc_relative_xy(d)
{
  if (d < 0)
    d = 8 - abs(d);
  else if (d >= 8)
    d = d - 8;
  move_lx = ZONE_P[d][0]; // X
  move_ly = ZONE_P[d][1]; // Y
}

int Get_LS_Output = TRUE;
int LX, LY; // Direction of Left Stick
int right_on;

int RS_Skills_Up;      // 0--
int RS_Skills_UpLeft;  // 1
int RS_Skills_UpRight; // 2--
int RS_Skills_LEFT;    // 3
int RS_Skills_RIGHT;   // 4--
int RS_Skills_DownL;   // 5
int RS_Skills_DownR;   // 6--
int RS_Skills_Down;    // 7
int rs_new_way_btn;    // 8

int dEnd;
int w_rstick = 50;
int ACTIVE;
int Sombrero;
int hold_btn = 200;
int LS_BlockOutput;


main
{

if (Get_LS_Output)
{
    if (abs(get_ival(PS4_LX)) > 50 || abs(get_ival(PS4_LY)) > 50)
        {
        calc_zone();
        LX = ZONE_P[zone_p][0];
        LY = ZONE_P[zone_p][1];
        }
          //======================================================
}

set_val(TRACE_2,zone_p);
set_val(TRACE_1,zone_RS);

f_RS_New_Way();

  if (RS_Skills_Up == 0 && RS_Skills_UpLeft == 0 && RS_Skills_UpRight == 0 && RS_Skills_LEFT == 0 && RS_Skills_RIGHT == 0 && RS_Skills_DownL == 0 && RS_Skills_DownR == 0 && RS_Skills_Down == 0)
  {
    // Variables have not been loaded, initialize them with specific values
    RS_Skills_Up = 8;
    RS_Skills_UpLeft = 17;
    RS_Skills_UpRight = 17;
    RS_Skills_LEFT = 17;
    RS_Skills_RIGHT = 17;
    RS_Skills_DownL = 19;
    RS_Skills_DownR = 19;
    RS_Skills_Down = 67;
  }

}

//--------------------------------------------------------------
//      Analog Functions
//--------------------------------------------------------------
int LS_Sens_Corect;
function RA(xx, yy)
{
  set_val(SKILL_STICK_X, xx);
  set_val(SKILL_STICK_Y, yy);
}
function LA(x, y)
{
  set_val(MOVE_X, x);
  set_val(MOVE_Y, y);
}
function LA_L_R()
{
  if (right_on)
  { // right
    set_val(MOVE_X, inv(LY));
    set_val(MOVE_Y, LX);
  }
  else
  { //  left
    set_val(MOVE_X, LY);
    set_val(MOVE_Y, inv(LX));
  }
}
function RA_L_R()
{
  if (right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_OPP()
{
  if (!right_on)
  { // right
    set_val(SKILL_STICK_X, inv(LY));
    set_val(SKILL_STICK_Y, LX);
  }
  else
  { //  left
    set_val(SKILL_STICK_X, LY);
    set_val(SKILL_STICK_Y, inv(LX));
  }
}
function RA_UP()
{
  set_val(SKILL_STICK_X, LX);
  set_val(SKILL_STICK_Y, LY);
}
function RA_DOWN()
{
  set_val(SKILL_STICK_X, inv(LX));
  set_val(SKILL_STICK_Y, inv(LY));
}
function RA_ZERO()
{
  set_val(SKILL_STICK_X, 0);
  set_val(SKILL_STICK_Y, 0);
}

function set_right_or_left()
{
  right_on = FALSE;
  if (zone_p == 4 || zone_p == 3 || zone_p == 7)
  {
    right_on = TRUE;
  } ///
}

function f_auto_skill_menu()
{
  // 1.1. RS = LS zone
  if (zone_RS == zone_p)
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Up)
      run_skill_combo(RS_Skills_Up);
  }

  // 1.4. RS = opposite of LS zone
  if (zone_RS == calc_temp_zone(zone_p + 4))
  { // right_on does not matter here
    // 1.1.0. if LS --> UP (zone 0)
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_Down)
      run_skill_combo(RS_Skills_Down);
  }
  //-------------------
  // 1.2. RS = LS zone +1/-1
  if (zone_RS == calc_temp_zone(zone_p + 1))
  {
    right_on = TRUE;
    if (RS_Skills_UpRight)
      run_skill_combo(RS_Skills_UpRight);
  }
  if (zone_RS == calc_temp_zone(zone_p - 1))
  {
    right_on = FALSE;
    if (RS_Skills_UpLeft)
      run_skill_combo(RS_Skills_UpLeft);
  }

  // 1.3. RS = LS zone +2/-2
  if (zone_RS == calc_temp_zone(zone_p + 2))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_RIGHT)
      run_skill_combo(RS_Skills_RIGHT);
  }
  if (zone_RS == calc_temp_zone(zone_p - 2))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_LEFT)
      run_skill_combo(RS_Skills_LEFT);
  }
  if (zone_RS == calc_temp_zone(zone_p + 3))
  {
    right_on = TRUE; // use One Way Skills
    if (RS_Skills_DownR)
      run_skill_combo(RS_Skills_DownR);
  }
  if (zone_RS == calc_temp_zone(zone_p - 3))
  {
    right_on = FALSE; // use One Way Skills
    if (RS_Skills_DownL)
      run_skill_combo(RS_Skills_DownL);
  }
}

function f_RS_New_Way()
{

  if (!get_ival(XB1_RS) && !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot))
  { // all Skills mode ){
    if ((abs(get_ival(SKILL_STICK_X)) > 45 || abs(get_ival(SKILL_STICK_Y)) > 45) && !flick_rs)
    { // getting RS zones
      flick_rs = TRUE;
      calc_RS();
      RS_X = ZONE_P[zone_RS][0];
      RS_Y = ZONE_P[zone_RS][1];
      f_auto_skill_menu();
    }
    set_val(SKILL_STICK_X, 0);
    set_val(SKILL_STICK_Y, 0);
  }
  //--- reset when RS is release
  if (abs(get_ival(SKILL_STICK_X)) < 20 && abs(get_ival(SKILL_STICK_Y)) < 20)
  {
    flick_rs = FALSE;
  }
}

function zone_saver()
{
  dEnd = zone_p
      calc_relative_xy(dEnd);
  LX = move_lx;
  LY = move_ly;
}

const int ZONE_P[][] = {
    //  X,    Y
    {0, -100},   // 0 UP
    {70, -70},   // 1 Up-Right
    {100, 0},    // 2 Right
    {70, 70},    // 3 Down right
    {0, 100},    // 4 Down
    {-100, 100}, // 5 Down Left
    {-100, 0},   // 6 Left
    {-70, -70}   // 7 Left Up
};

int move_lx, move_ly, zone_p;
int RS_X, RS_Y, zone_RS;
int rs_val = 35;
int polar_LS;




int skill_sens;
function run_skill_combo(f_skill)
{
  //-----------------------------------------------------------------------
  skill_sens = 1;
  if (f_skill == FAKE_SHOT_SKILL)
  {
    ACTIVE = FAKE_SHOT_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_HEEL_FLICK_SKILL)
  {
    ACTIVE = HEEL_TO_HEEL_FLICK_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_FLICK_TURN_SKILL)
  {
    ACTIVE = HEEL_FLICK_TURN_SKILL;
    combo_run(HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_SKILL)
  {
    ACTIVE = RAINBOW_SKILL;
    combo_run(RAINBOW);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_SOMBRERO_SKILL)
  {
    Sombrero = TRUE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_PASS_SKILL)
  {
    ACTIVE = FAKE_PASS_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_BACK_UNIVERSAL_SKILL)
  {
    Sombrero = FALSE;
    combo_run(DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_FEINT_SKILL)
  {
    ACTIVE = STEP_OVER_FEINT_SKILL;
    combo_run(STEP_OVER_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_DRAG_SKILL)
  {
    ACTIVE = DRAG_TO_DRAG_SKILL;
    combo_run(DRAG_TO_DRAG);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HOCUS_POCUS_SKILL)
  {
    ACTIVE = HOCUS_POCUS_SKILL;
    combo_run(HOCUS_POCUS);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TRIPLE_ELASTICO_SKILL)
  {
    ACTIVE = TRIPLE_ELASTICO_SKILL;
    combo_run(TRIPLE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_SKILL)
  {
    ACTIVE = ELASTICO_SKILL;
    combo_run(ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_ELASTICO_SKILL)
  {
    ACTIVE = REVERSE_ELASTICO_SKILL;
    combo_run(REVERSE_ELASTICO);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CRUYFF_TURN_SKILL)
  {
    ACTIVE = CRUYFF_TURN_SKILL;
    combo_run(CRUYFF_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LA_CROQUETA_SKILL)
  {
    ACTIVE = LA_CROQUETA_SKILL;
    combo_run(LA_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RONALDO_CHOP_SKILL)
  {
    ACTIVE = RONALDO_CHOP_SKILL;
    combo_run(FAKE_SHOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ROULETTE_SKILL)
  {
    ACTIVE = ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FLAIR_ROULETTE_SKILL)
  {
    ACTIVE = FLAIR_ROULETTE_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_SKILL)
  {
    ACTIVE = BALL_ROLL_SKILL;
    combo_run(BALL_ROLL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BERBA_MCGEADY_SPIN_SKILL)
  {
    ACTIVE = BERBA_MCGEADY_SPIN_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BOLASIE_FLICK_SKILL)
  {
    ACTIVE = BOLASIE_FLICK_SKILL;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TORNADO_SKILL)
  {
    ACTIVE = TORNADO_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = THREE_TOUCH_ROULETTE_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ALTERNATIVE_ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ALTERNATIVE_ELASTICO_CHOP_SKILL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CHOP_SKILL)
  {
    ACTIVE = BALL_ROLL_CHOP_SKILL;
    combo_run(BALL_ROLL_CHOP);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_AND_EXIT_SKILL)
  {
    ACTIVE = FEINT_AND_EXIT_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_L_EXIT_R_SKILL)
  {
    ACTIVE = FEINT_L_EXIT_R_SKILL;
    combo_run(FEINT_EXIT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == LATERAL_HEEL_TO_HEEL_SKILL)
  {
    ACTIVE = LATERAL_HEEL_TO_HEEL_SKILL;
    combo_run(LATERAL_HEELtoHEEL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == WAKA_WAKA_SKILL)
  {
    ACTIVE = WAKA_WAKA_SKILL;
    combo_run(WAKA_WAKA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BODY_FEINT_SKILL)
  {
    ACTIVE = BODY_FEINT_SKILL;
    combo_run(BODY_FEINT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_TO_HEEL)
  {
    ACTIVE = DRAG_TO_HEEL;
    combo_run(TORNADO_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_FAKE_TURN)
  {
    ACTIVE = BALL_ROLL_FAKE_TURN;
    combo_run(TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FEINT_FORWARD_AND_TURN)
  {
    ACTIVE = FEINT_FORWARD_AND_TURN;
    combo_run(FEINT_FORWARD);
    Get_LS_Output = FALSE;
  }
  if (f_skill == TURN_BACK)
  {
    ACTIVE = TURN_BACK;
    combo_run(TURN_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_CROQUETA)
  {
    ACTIVE = ADVANCED_CROQUETA;
    combo_run(ADVANCED_CROQUETA);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_THREE_TOUCH_ROULETTE_SKILL)
  {
    ACTIVE = CANCELED_THREE_TOUCH_ROULETTE_SKILL;
    combo_run(CANCELED_THREE_TOUCH_ROULETTE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REVERSE_STEP_OVER_SKILL)
  {
    ACTIVE = REVERSE_STEP_OVER_SKILL;
    combo_run(REVERSE_STEP_OVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_DRAG_BACK_SKILL)
  {
    ACTIVE = FAKE_DRAG_BACK_SKILL;
    combo_run(FAKE_DRAG_BACK);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RAINBOW_TO_SCORPION_KICK_SKILL)
  {
    ACTIVE = RAINBOW_TO_SCORPION_KICK_SKILL;
    combo_run(RAINBOW_TO_SCORPION);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STEP_OVER_BOOST_SKILL)
  {
    ACTIVE = STEP_OVER_BOOST_SKILL;
    combo_run(BOOSTED_STEPOVER);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCEL_SHOOT_SKILL)
  {
    ACTIVE = CANCEL_SHOOT_SKILL;
    combo_run(CANCEL_SHOOT);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIRECTIONAL_NUTMEG_SKILL)
  {
    ACTIVE = DIRECTIONAL_NUTMEG_SKILL;
    combo_run(NUTMEG_SKILL);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_SKILL)
  {
    ACTIVE = CANCELED_BERBA_SPIN_SKILL;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_BERBA_SPIN_WITH_DIRECTION)
  {
    ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION;
    combo_run(CANCELED_TURN_AND_SPIN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_TO_SCOOP_TURN_SKILL)
  {
    ACTIVE = BALL_ROLL_TO_SCOOP_TURN_SKILL;
    combo_run(BALL_ROLL_SCOOP_TURN);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRIBBLING_SKILL)
  {
    ACTIVE = DRIBBLING_SKILL;
    start = TRUE;
    combo_run(DRIBBLING_SKILL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FOUR_TOUCH_TURN_SKILLS)
  {
    ACTIVE = FOUR_TOUCH_TURN_SKILLS;
    combo_run(FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SKILLED_BRIDGE_SKILL)
  {
    ACTIVE = SKILLED_BRIDGE_SKILL;
    combo_run(SKILLED_BRIDGE_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SCOOP_TURN_FAKE_SKILL)
  {
    ACTIVE = SCOOP_TURN_FAKE_SKILL;
    combo_run(SCOOP_TURN_FAKE);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_STEP_OVER_SKILL)
  {
    ACTIVE = BALL_ROLL_STEP_OVER_SKILL;
    combo_run(BALL_ROLL_STEP_OVER_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == CANCELED_4_TOUCH_TURN_SKILL)
  {
    ACTIVE = CANCELED_4_TOUCH_TURN_SKILL;
    combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_SHOT_CANCEL_SKILL)
  {
    ACTIVE = FAKE_SHOT_CANCEL_SKILL;
    combo_run(FAKE_SHOT_CANCEL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == OKKOSHA_FLICK_SKILL)
  {
    ACTIVE = OKKOSHA_FLICK_SKILL;
    combo_run(OKKOSHA_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ADVANCED_RAINBOW_SKILL)
  {
    ACTIVE = ADVANCED_RAINBOW_SKILL;
    combo_run(ADVANCED_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_LA_CROQUETA_SKILL)
  {
    ACTIVE = STOP_LA_CROQUETA_SKILL;
    combo_run(STOP_LA_CROQUETA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLING_RAINBOW_SKILL)
  {
    ACTIVE = JUGGLING_RAINBOW_SKILL;
    combo_run(JUGGLING_RAINBOW_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_NEYMAR_ROLL_SKILL)
  {
    ACTIVE = STOP_NEYMAR_ROLL_SKILL;
    combo_run(STOP_NEYMAR_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_V_DRAG_SKILL)
  {
    ACTIVE = STOP_V_DRAG_SKILL;
    combo_run(STOP_V_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = REV_OR_ELASTICO_SKILL;
    combo_run(REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STOP_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = STOP_REV_OR_ELASTICO_SKILL;
    combo_run(STOP_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DRAG_REV_OR_ELASTICO_SKILL)
  {
    ACTIVE = DRAG_REV_OR_ELASTICO_SKILL;
    combo_run(DRAG_REV_OR_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_RABONA_SKILL)
  {
    ACTIVE = FAKE_RABONA_SKILL;
    combo_run(FAKE_RABONA_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_REV_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_REV_ELASTICO_SKILL;
    combo_run(RABONA_TO_REV_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == RABONA_TO_ELASTICO_SKILL)
  {
    ACTIVE = RABONA_TO_ELASTICO_SKILL;
    combo_run(RABONA_TO_ELASTICO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SOMBRERO_FLICK_SKILL)
  {
    ACTIVE = SOMBRERO_FLICK_SKILL;
    combo_run(SOMBRERO_FLICK_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JUGGLE_BACK_SOMBRERO_SKILL)
  {
    ACTIVE = JUGGLE_BACK_SOMBRERO_SKILL;
    combo_run(JUGGLE_BACK_SOMBRERO_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_OPP_EXIT_SKILL)
  {
    ACTIVE = FAKE_BERBA_OPP_EXIT_SKILL;
    combo_run(FAKE_BARBA_OPP_EXIT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == DIAGONAL_HEEL_CHOP_SKILL)
  {
    ACTIVE = DIAGONAL_HEEL_CHOP_SKILL;
    combo_run(DIAGONAL_HEEL_CHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == FAKE_BERBA_FAKE_DRAG_SKILL)
  {
    ACTIVE = FAKE_BERBA_FAKE_DRAG_SKILL;
    combo_run(FAKE_BARBA_TO_FAKE_DRAG_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == ELASTICO_CHOP_SKILL)
  {
    ACTIVE = ELASTICO_CHOP_SKILL;
    combo_run(ELASTICO_SHOP_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == BALL_ROLL_CUT_180_SKILL)
  {
    ACTIVE = BALL_ROLL_CUT_180_SKILL;
    combo_run(BAL_ROLL_CUT_180_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == HEEL_TO_BALL_ROLL_SKILL)
  {
    ACTIVE = HEEL_TO_BALL_ROLL_SKILL;
    combo_run(HEEL_to_BALL_ROLL_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == STUTTER_FEINT_SKILL)
  {
    ACTIVE = STUTTER_FEINT_SKILL;
    combo_run(STUTTER_FEINT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == JOG_OPENUP_FAKE_SHOT)
  {
    ACTIVE = JOG_OPENUP_FAKE_SHOT;
    combo_run(JOG_OPENUP_FAKE_SHOT_cmb);
    Get_LS_Output = FALSE;
  }
  if (f_skill == SPIN_MOVE_LEFT_RIGHT_SKILL)
  {
    ACTIVE = SPIN_MOVE_LEFT_RIGHT_SKILL;
    combo_run(ROULETTE);
    Get_LS_Output = FALSE;
  }
}

///////////////////////////////////////////////////////////////////
// 1. Fake Shot           ////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo FAKE_SHOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

///////////////////////////////////////////////////////////////////
// 3. RAINBOW   //////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo RAINBOW
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo DRAG_BACK
{
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(60);
  set_val(MOVE_X, inv(LX));
  set_val(MOVE_Y, inv(LY));
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (Sombrero)
    set_val(PS4_R3, 100);
  vm_tctrl(0);
  wait(40);
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 2. STEP OVER  /////////////////////////////////////////////
//////////////////////////////////////////////////////////////
combo STEP_OVER_FEINT
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  // vm_tctrl(0);wait(300);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Drag to Drag
combo DRAG_TO_DRAG
{
  LA(0, 0);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(0, 0);
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo HOCUS_POCUS
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TRIPLE_ELASTICO
{
  RA_DOWN(); // Down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO
{
  right_on = TRUE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = FALSE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo REVERSE_ELASTICO
{
  right_on = FALSE;
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = TRUE;
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo ELASTICO_SHOP_cmb
{
  set_val(FinesseShot, 100);
  RA_L_R(); // R
  vm_tctrl(0);
  wait(w_rstick);
  set_val(FinesseShot, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(FinesseShot, 100);
  RA_L_R(); // L
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo BAL_ROLL_CUT_180_cmb
{
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // zero
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 1. Cruyff Turn
combo CRUYFF_TURN
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(inv(LX), inv(LY));
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo LA_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(500); //
  Get_LS_Output = TRUE;
}
combo ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// Ball Roll
function RA_L_UR()
{
  // If right_on is true, rotate right

  if (right_on == TRUE)
  {
    // angle_rotation = 90
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle + angle_rotation), 32767)
  }
  // If right_on is false, rotate left
  else if (right_on == FALSE)
  {
    if (angle_rotation > 0)
    {
      angle_rotation--
    }

    set_polar(POLAR_RS, (LS_Angle - angle_rotation), 32767)
  }
}

combo BALL_ROLL
{
  RA_L_R(); // Left or Right
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  wait(300);
  sensitivity(PS4_LX, 50, 150);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(FinesseShot, 100);
  wait(160);
  wait(160);
  if (!get_ival(ShotBtn) && !get_ival(PassBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(ThroughBall))
    set_val(PlayerRun, 100);
  wait(160);
  wait(160);

  Get_LS_Output = TRUE;
}

combo BALL_ROLL_orig
{
  RA_L_R(); // Left or Right
  set_val(SprintBtn, 0);
  sensitivity(PS4_LX, 50, 40);
  sensitivity(PS4_LY, 60, 40);
  vm_tctrl(0);
  wait(310);
  sensitivity(PS4_LX, 50, 150);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin
combo TURN_AND_SPIN
{
  if (ACTIVE == BALL_ROLL_FAKE_TURN)
    hold_btn = 200; //  Ball Roll Fake Turn L2
  else
    hold_btn = 1;
  vm_tctrl(0);
  wait(hold_btn);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
////////////////////////////////////
//  Tornado Spin + L1
combo TORNADO_SPIN
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
///////////////////////////////
// 25.  Ball Roll Chop
combo BALL_ROLL_CHOP
{
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(300);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_OPP(); // Left or Right
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FEINT_EXIT
{
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
//////////////////////////////////////////////////////////////
// 28. LATERAL HEEL to HEEL ///////////////////////////////////
//////////////////////////////////////////////////////////////
// + L1   PlayerRun
combo LATERAL_HEELtoHEEL
{
  set_val(PlayerRun, 100);
  RA_OPP();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(60); //
  set_val(PlayerRun, 100);
  RA_L_R();
  vm_tctrl(0);
  wait(60); //
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo WAKA_WAKA
{
  RA_OPP(); // L
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);

  LA(0, 0);
  RA_L_R() // L
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo BODY_FEINT
{
  RA_L_R(); // R
  vm_tctrl(0);
  wait(100);
  RA_ZERO();
  vm_tctrl(0);
  wait(80);
  LA_L_R();
  vm_tctrl(0);
  wait(600);
  vm_tctrl(0);
  wait(600);
  Get_LS_Output = TRUE;
}
combo FEINT_FORWARD
{
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo TURN_BACK
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(80);
  Get_LS_Output = TRUE;
}
combo ADVANCED_CROQUETA
{
  set_val(PlayerRun, 100);
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(300); //
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(800); // 800
  Get_LS_Output = TRUE;
}
combo CANCELED_THREE_TOUCH_ROULETTE
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  // LA_L_R();
  vm_tctrl(0);
  wait(300); // 800
  Get_LS_Output = TRUE;
}

//////////////////////////////////////////////////////////////
// 37. REVERSE STEP OVER  ///////////////////////////
//////////////////////////////////////////////////////////////
combo REVERSE_STEP_OVER
{
  RA_L_R(); // <-/->
  vm_tctrl(0);
  wait(w_rstick);
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}
combo FAKE_DRAG_BACK
{
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(200); // 350
  right_on = FALSE;
  LA_L_R();
  vm_tctrl(0);
  wait(50); // 120
  right_on = !right_on;
  LA_L_R();
  vm_tctrl(0);
  wait(540);
  Get_LS_Output = TRUE;
}

combo RAINBOW_TO_SCORPION
{
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(40);
  RA_UP(); // up
  vm_tctrl(0);
  wait(190);               // 200
  set_val(PaceCtrol, 100); // L2
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100); // L2
  set_val(ShotBtn, 100);   // Shoot
  vm_tctrl(0);
  wait(180);
  Get_LS_Output = TRUE;
}
combo CANCEL_SHOOT
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(290);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo NUTMEG_SKILL
{
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  set_val(PlayerRun, 100);
  if (right_on)
    dEnd = zone_p + 1;
  else
  {
    dEnd = zone_p - 1;
    if (dEnd < 0)
      dEnd = 7;
  }
  calc_relative_xy(dEnd);
  RA(move_lx, move_ly);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}
combo CANCELED_TURN_AND_SPIN
{
  RA_UP(); // up
  vm_tctrl(0);
  wait(w_rstick);
  RA_ZERO(); // ZERO
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); // Left or Right
  vm_tctrl(0);
  wait(w_rstick);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(200);
  if (ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION)
    LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_SCOOP_TURN
{
  LS_BlockOutput = TRUE;
  RA_L_R();
  vm_tctrl(0);
  wait(250);
  RA_ZERO();
  vm_tctrl(0);
  wait(50) : right_on = !right_on;
  if (right_on)
    dEnd = zone_p - 2;
  else
    dEnd = zone_p + 2;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  LS_BlockOutput = TRUE;
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  set_val(ShotBtn, 100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(45);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(100);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(500);
  Get_LS_Output = TRUE;
}
combo BALL_ROLL_STEP_OVER_cmb
{
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(300);
  RA_UP();
  vm_tctrl(0);
  wait(60);
  Get_LS_Output = TRUE;
}
combo CANCEL_FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  LA(0, 0);
  vm_tctrl(0);
  wait(400);
  set_val(PS4_L2, 100);
  set_val(PS4_L1, 100);
  set_val(PS4_R1, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(70);
  Get_LS_Output = TRUE;
}
combo FAKE_SHOT_CANCEL_cmb
{
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(140);
  set_val(PS4_L2, 100);
  set_val(PS4_R2, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo OKKOSHA_FLICK_cmb
{
  set_val(PlayerRun, 100);
  RA_UP(); // <-/->
  vm_tctrl(0);
  wait(300); //
  Get_LS_Output = TRUE;
}
combo ADVANCED_RAINBOW_cmb
{
  LA(LX, LY);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(100);
  RA_ZERO(); // Zero
  LA(LX, LY);
  vm_tctrl(0);
  wait(40);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(320);
  LA(LX, LY);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(220);
  LA(LX, LY);
  RA_UP();
  LA(LX, LY); // up
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo STOP_LA_CROQUETA_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(LA_CROQUETA);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo JUGGLING_cmb
{
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(100);
  Get_LS_Output = TRUE;
}

combo JUGGLING_RAINBOW_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(60);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  RA_ZERO(); // Zero
  vm_tctrl(0);
  wait(40);
  set_val(PaceCtrol, 100);
  RA_UP(); // up
  vm_tctrl(0);
  wait(70);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(800);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_NEYMAR_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  RA_L_R();
  vm_tctrl(0);
  wait(200);
  RA_L_R();
  LA(LX, LY);
  vm_tctrl(0);
  wait(125);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_V_DRAG_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(125);
  set_val(ShotBtn, 100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  set_val(SprintBtn, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(30);
  LA_L_R();
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  RA_OPP(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STOP_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(FEINT_EXIT);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo DRAG_REV_OR_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(DRAG_BACK);
  vm_tctrl(0);
  wait(280);
  RA_OPP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R(); //  <-/->
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(300);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_RABONA_cmb
{
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(inv(LX), inv(LY));
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(0, 0);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}

combo RABONA_TO_REV_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_UP(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_DOWN() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  // RA functions rotation done
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo RABONA_TO_ELASTICO_cmb
{
  LS_BlockOutput = TRUE;
  zone_saver();
  call(FAKE_RABONA_cmb);
  vm_tctrl(0);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  // 1//
  RA_DOWN(); // original elastico is (RA_OPP())
  vm_tctrl(0);
  wait(w_rstick);
  // 2//
  right_on = FALSE; // ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  // 3//
  RA_UP() // original elastico is (RA_L_R())
  vm_tctrl(0);
  wait(w_rstick);
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo SOMBRERO_FLICK_cmb
{
  vm_tctrl(0);
  wait(100);
  LA(0, 0);
  RA_UP();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_UP()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_ZERO()
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  RA_DOWN();
  vm_tctrl(0);
  wait(70);
  LA(0, 0);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo JUGGLE_BACK_SOMBRERO_cmb
{
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  vm_tctrl(0);
  wait(100);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol, 100);
  set_val(FinesseShot, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(400);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo FAKE_BARBA_OPP_EXIT_cmb
{
  LS_BlockOutput = TRUE;
  LA(LX, LY);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  LA(inv(LX), inv(LY));
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo DIAGONAL_HEEL_CHOP_cmb
{

  if (right_on)
    dEnd = zone_p + 3;
  else
    dEnd = zone_p - 3;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  vm_tctrl(0);
  wait(40);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(80);
  LA(move_lx, move_ly);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo FAKE_BARBA_TO_FAKE_DRAG_cmb
{
  LA(LX, LY);
  LS_BlockOutput = TRUE;
  set_val(XB1_LS, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_ZERO();
  set_val(XB1_LS, 100);
  vm_tctrl(0);
  wait(w_rstick);
  LA(LX, LY);
  RA_L_R();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  set_val(SprintBtn, 100);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(220);
  if (right_on)
    dEnd = zone_p + 4;
  else
    dEnd = zone_p - 4;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(40);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(600);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}

combo HEEL_to_BALL_ROLL_cmb
{
  LS_BlockOutput = TRUE;
  set_val(PlayerRun, 100);
  RA_UP();
  LA(0, 0); // up
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  RA_ZERO(); // ZERO
  LA(0, 0);
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PlayerRun, 100);
  LA(0, 0);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(200);
  LS_BlockOutput = FALSE;
  Get_LS_Output = TRUE;
}
combo STUTTER_FEINT_cmb
{
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  right_on = !right_on;
  set_val(PaceCtrol, 100); // hold L2
  RA_L_R();                // lef/right
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo RS_SWITCH
{
  vm_tctrl(0);
  wait(45);
  set_val(PS4_RX, 0);
  set_val(PS4_RY, 0);
  vm_tctrl(0);
  wait(160);
}

combo JOG_OPENUP_FAKE_SHOT_cmb
{
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  vm_tctrl(0);
  wait(40);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  set_val(CrossBtn, 0);
  set_val(PassBtn, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(60);
  set_val(PlayerRun, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
}
combo STOP_PLAYER_cmb
{
  zone_saver();
  vm_tctrl(0);
  wait(20);
  vm_tctrl(0);
  wait(100);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(40);
  vm_tctrl(0);
  wait(160);
  Get_LS_Output = TRUE;
}



int time_to_dblclick = 300 int tap;
combo ONE_TAP
{
  tap = TRUE;
  vm_tctrl(0);
  wait(time_to_dblclick); // wait for second tap
  tap = FALSE;
}
int start;
combo DRIBBLING_SKILL_cmb
{
  set_val(FinesseShot, 100);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 100);
  LA_L_R();
  vm_tctrl(0);
  wait(375);
  vm_tctrl(0);
  wait(20);
  set_val(FinesseShot, 0);
  set_val(SprintBtn, 100);
  vm_tctrl(0);
  wait(800);
  start = FALSE;
  Get_LS_Output = TRUE;
}

combo DRIBBLING_SKILL_cmb_sideexit
{
  // set_val(FinesseShot,100);
  LA_L_R();
  // sensitivity(PS4_LX, 50, 90);
  // sensitivity(PS4_LY, 50, 90);
  vm_tctrl(0);
  wait(360);
  // wait(20);
  // LA_DOWN();
  // set_val(PlayerRun,100);
  vm_tctrl(0);
  wait(20)
      // wait(20);
      start = FALSE;
  Get_LS_Output = TRUE;
}

combo BOOSTED_STEPOVER
{
  if (right_on)
    dEnd = zone_p + 1;
  else
    dEnd = zone_p - 1;
  calc_relative_xy(dEnd);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  RA_L_R();
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(w_rstick);
  LA(move_lx, move_ly);
  vm_tctrl(0);
  wait(1000);
  Get_LS_Output = TRUE;
}

combo FOUR_TOUCH_TURN_cmb
{
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(30);
  set_val(PaceCtrol, 100);
  RA_DOWN(); // down
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SKILLED_BRIDGE_cmb
{
  set_val(PaceCtrol, 100);
  RA_UP();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_ZERO();
  vm_tctrl(0);
  wait(w_rstick);
  set_val(PaceCtrol, 100);
  RA_DOWN();
  vm_tctrl(0);
  wait(w_rstick);
  Get_LS_Output = TRUE;
}

combo SCOOP_TURN_FAKE
{
  RA_L_R();
  vm_tctrl(0);
  wait(280);
  LA_L_R()
  set_val(ShotBtn, 100);
  set_val(PaceCtrol, 100);
  vm_tctrl(0);
  wait(40);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 100);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  LA_L_R()
  set_val(PaceCtrol, 100);
  set_val(ShotBtn, 0);
  set_val(PassBtn, 100);
  vm_tctrl(0);
  wait(60);
  vm_tctrl(0);
  wait(250);
  LA_L_R()
  vm_tctrl(0);
  wait(300);
  Get_LS_Output = TRUE;
} 