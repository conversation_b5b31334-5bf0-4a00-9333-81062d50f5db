
define Stick_Press_LB_Threshold = 95;

int stick_swap_toggle;

main {

    if(get_ival(XB1_LT)) {
        set_val(XB1_DOWN,0);
        if(event_press(XB1_DOWN))
            stick_swap_toggle = !stick_swap_toggle;
    }

    if(stick_swap_toggle) {
        if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
            set_val(POLAR_LX, get_val(POLAR_RX));
            set_val(POLAR_LY, get_val(POLAR_RY));
        }
        if(isqrt(pow(get_ival(XB1_RX),2) + pow(get_ival(XB1_RY),2)) >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX),2) + pow(get_lval(XB1_RY),2)) < Stick_Press_LB_Threshold)
            combo_run(Press_LB);
        set_val(POLAR_RX, 0);
        set_val(POLAR_RY, 0);
    }

}

combo Press_LB {
    set_val(XB1_LB,100);
    wait(100);
    wait(20);
}


function icos(x) { return isin(x + 8192); }
function isin(x) {
    x = (x % 32767) << 17;
    if((x ^ (x * 2)) < 0) { x = (-2147483648) - x; }
    x = x >> 17;
    return x * ((98304) - (x * x) >> 11) >> 14;
}
int cos_angle, sin_angle;

function set_polar_dd(stick, angle, radius, dd_factor){
    ...
}

int r, a, dd;

main {

    if(get_ival(XB1_LT)) {
        set_val(XB1_DOWN,0);
        if(event_press(XB1_DOWN))
            stick_swap_toggle = !stick_swap_toggle;
    }

    if(stick_swap_toggle) {
        if(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500) {
            set_val(POLAR_LX, get_val(POLAR_RX));
            set_val(POLAR_LY, get_val(POLAR_RY));
        }
        if(isqrt(pow(get_ival(XB1_RX),2) + pow(get_ival(XB1_RY),2)) >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX),2) + pow(get_lval(XB1_RY),2)) < Stick_Press_LB_Threshold)
            combo_run(Press_LB);
        set_val(POLAR_RX, 0);
        set_val(POLAR_RY, 0);
    }

}

combo Press_LB {
    set_val(XB1_LB,100);
    wait(100);
    wait(20);
}

    // New script logic ends

main {
    stickize(POLAR_LX, POLAR_LY, 32767);

    r = get_polar(POLAR_LS, POLAR_RADIUS);
    a = get_polar(POLAR_LS, POLAR_ANGLE);
    dd = 9; // Range -50 < - > 100 

    set_polar_dd(POLAR_LS, a, r, dd);

    // Existing main logic continues...
