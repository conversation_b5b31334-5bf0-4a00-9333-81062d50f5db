// OOPOctaFull.gpc
// Description: Octagon mapping for analog stick using an OOP-like approach.

// --- Attribute Keys for OctaMapper Object (1-based for simplicity in example)
// These will be used as direct offsets in a conceptual memory block for the object.
// ATTR_KEY_... are 0-indexed for direct use in array-like access
define ATTR_KEY_ANGLE = 0;
define ATTR_KEY_COS_VAL = 1;
define ATTR_KEY_SIN_VAL = 2;
define ATTR_KEY_ABS_COS = 3;
define ATTR_KEY_ABS_SIN = 4;
define ATTR_KEY_L_INF = 5;
define ATTR_KEY_L1 = 6;
define ATTR_KEY_SCALED_L1 = 7;
define ATTR_KEY_NORM = 8;
define ATTR_KEY_OUT_X = 9;
define ATTR_KEY_OUT_Y = 10;
define ATTR_KEY_MAGNITUDE = 11; // Added attribute key for magnitude
define NUM_OCTA_ATTRS = 12; // Total number of attributes

// --- Simplified Memory Allocation for "Objects" ---
// We'll simulate a small heap. Each "object" is just a block of integers.
// For this example, we only need one OctaMapper object.
int octa_mapper_data[NUM_OCTA_ATTRS]; // Static allocation for one object's data
int octa_mapper_ptr = 0; // Represents the "pointer" to our single object's data array

// --- Global Variables for calculations and temporary storage ---
int i_attr; // For OctaMapper_constructor loop
int current_angle, current_cos_val, current_sin_val; // For OctaMapper_process
int current_abs_cos, current_abs_sin, current_L_inf, current_L1; // For OctaMapper_process
int current_scaled_L1, current_norm; // For OctaMapper_process
int temp_calc_x, temp_calc_y; // For OctaMapper_process
int raw_ls_x, raw_ls_y; // For main
int stick_angle, stick_magnitude; // For main
int mapped_x, mapped_y; // For main
int temp_idx_calc; // Temporary variable for calculations within functions
int temp_self_ptr; // Temporary holder for self_ptr argument
int temp_attr_key; // Temporary holder for attribute keys

// --- Global Lookup Tables & Init ---
int cosTable[36];
int sinTable[36];
int lookup_tables_initialized = 0;

function init_lookup_tables() {
    if (lookup_tables_initialized == 1) { return; }

    cosTable[0] = 1000; cosTable[1] = 984; cosTable[2] = 940; cosTable[3] = 866;
    cosTable[4] = 766; cosTable[5] = 642; cosTable[6] = 500; cosTable[7] = 342;
    cosTable[8] = 173; cosTable[9] = 0; cosTable[10] = -173; cosTable[11] = -342;
    cosTable[12] = -500; cosTable[13] = -642; cosTable[14] = -766; cosTable[15] = -866;
    cosTable[16] = -940; cosTable[17] = -984; cosTable[18] = -1000; cosTable[19] = -984;
    cosTable[20] = -940; cosTable[21] = -866; cosTable[22] = -766; cosTable[23] = -642;
    cosTable[24] = -500; cosTable[25] = -342; cosTable[26] = -173; cosTable[27] = 0;
    cosTable[28] = 173; cosTable[29] = 342; cosTable[30] = 500; cosTable[31] = 642;
    cosTable[32] = 766; cosTable[33] = 866; cosTable[34] = 940; cosTable[35] = 984;

    sinTable[0] = 0; sinTable[1] = 173; sinTable[2] = 342; sinTable[3] = 500;
    sinTable[4] = 642; sinTable[5] = 766; sinTable[6] = 866; sinTable[7] = 940;
    sinTable[8] = 984; sinTable[9] = 1000; sinTable[10] = 984; sinTable[11] = 940;
    sinTable[12] = 866; sinTable[13] = 766; sinTable[14] = 642; sinTable[15] = 500;
    sinTable[16] = 342; sinTable[17] = 173; sinTable[18] = 0; sinTable[19] = -173;
    sinTable[20] = -342; sinTable[21] = -500; sinTable[22] = -642; sinTable[23] = -766;
    sinTable[24] = -866; sinTable[25] = -940; sinTable[26] = -984; sinTable[27] = -1000;
    sinTable[28] = -984; sinTable[29] = -940; sinTable[30] = -866; sinTable[31] = -766;
    sinTable[32] = -642; sinTable[33] = -500; sinTable[34] = -342; sinTable[35] = -173;

    lookup_tables_initialized = 1;
}

function cos_deg_lookup(ang_val) {
    temp_idx_calc = init_lookup_tables
    while (ang_val < 0) { ang_val = ang_val + 360; }
    while (ang_val >= 360) { ang_val = ang_val - 360; }
    temp_idx_calc = ang_val + 5;
    temp_idx_calc = temp_idx_calc / 10;
    while (temp_idx_calc >= 36) { temp_idx_calc = temp_idx_calc - 36; }
    return cosTable[temp_idx_calc];
}

function sin_deg_lookup(ang_val) {
    temp_idx_calc = init_lookup_tables
    while (ang_val < 0) { ang_val = ang_val + 360; }
    while (ang_val >= 360) { ang_val = ang_val - 360; }
    temp_idx_calc = ang_val + 5;
    temp_idx_calc = temp_idx_calc / 10;
    while (temp_idx_calc >= 36) { temp_idx_calc = temp_idx_calc - 36; }
    return sinTable[temp_idx_calc];
}

// --- OctaMapper "Class" ---
// Constructor: Initializes the object's data. For simplicity, uses the global octa_mapper_data.
// No actual memory allocation needed here as we're using a global static array.
function OctaMapper_constructor() {
    // octa_mapper_ptr is already 0, pointing to the start of octa_mapper_data
    // Initialize attributes if needed (they are global, so default to 0)
    for (i_attr = 0; i_attr < NUM_OCTA_ATTRS; i_attr = i_attr + 1) {
        octa_mapper_data[i_attr] = 0;
    }
    return octa_mapper_ptr; // Return the "pointer" (which is just 0)
}

// Get an attribute value from the object
function OctaMapper_get_attr() {
    // arg0 is obj_ptr (unused), arg1 is attr_key
    return octa_mapper_data[arg1]; // Assuming arg0 (obj_ptr) is effectively 0 and keys are direct indices
}

// Main processing method for OctaMapper
function OctaMapper_process() {
    // arg0 is self_ptr (unused for global mapper)
    // arg1 is input_angle
    // arg2 is input_magnitude

    // Assign args to global vars before use in other func calls
    temp_self_ptr = arg0;
    current_angle = arg1; 
    // stick_magnitude is already global, arg2 will be assigned to it if needed for clarity
    // or directly use arg2 in calculations if it's only for that. Let's use arg2 directly for now.

    // Store input angle
    octa_mapper_data[ATTR_KEY_ANGLE] = current_angle
    // current_angle is already set from arg1

    // Retrieve cosine and sine
    current_cos_val = cos_deg_lookup(current_angle)
    current_sin_val = sin_deg_lookup(current_angle)
    octa_mapper_data[ATTR_KEY_COS_VAL] = current_cos_val
    octa_mapper_data[ATTR_KEY_SIN_VAL] = current_sin_val

    // Compute absolute values
    if (current_cos_val < 0) {
        current_abs_cos = 0 - current_cos_val
    } else {
        current_abs_cos = current_cos_val
    }
    octa_mapper_data[ATTR_KEY_ABS_COS] = current_abs_cos

    if (current_sin_val < 0) {
        current_abs_sin = 0 - current_sin_val
    } else {
        current_abs_sin = current_sin_val
    }
    octa_mapper_data[ATTR_KEY_ABS_SIN] = current_abs_sin

    // Compute L-infinity norm
    if (current_abs_cos > current_abs_sin) {
        current_L_inf = current_abs_cos
    } else {
        current_L_inf = current_abs_sin
    }
    octa_mapper_data[ATTR_KEY_L_INF] = current_L_inf

    // Compute L1 norm
    current_L1 = current_abs_cos + current_abs_sin
    octa_mapper_data[ATTR_KEY_L1] = current_L1

    // Scale L1 (approx 1/sqrt(2) = 707/1000)
    current_scaled_L1 = current_L1 * 707
    current_scaled_L1 = current_scaled_L1 / 1000
    octa_mapper_data[ATTR_KEY_SCALED_L1] = current_scaled_L1

    // Octagon norm
    if (current_L_inf > current_scaled_L1) {
        current_norm = current_L_inf
    } else {
        current_norm = current_scaled_L1
    }
    octa_mapper_data[ATTR_KEY_NORM] = current_norm

    // Avoid division by zero if norm is 0
    if (current_norm == 0) {
        octa_mapper_data[ATTR_KEY_OUT_X] = 0
        octa_mapper_data[ATTR_KEY_OUT_Y] = 0
    } else {
        // Scale output coordinates
        temp_calc_x = arg2 * current_cos_val // Use arg2 for input_magnitude
        temp_calc_x = temp_calc_x / current_norm
        octa_mapper_data[ATTR_KEY_OUT_X] = temp_calc_x

        temp_calc_y = arg2 * current_sin_val // Use arg2 for input_magnitude
        temp_calc_y = temp_calc_y / current_norm
        octa_mapper_data[ATTR_KEY_OUT_Y] = temp_calc_y
    }
}

// Getter for output X
function OctaMapper_get_out_x() { 
    return octa_mapper_data[ATTR_KEY_OUT_X];
}

// Getter for output Y
function OctaMapper_get_out_y() { 
    return octa_mapper_data[ATTR_KEY_OUT_Y];
}
/*
// Getter for any attribute (conceptual)
//{{ ... }}
    if (stick_magnitude > 10) { // Basic deadzone
        // Process using the OctaMapper object
        OctaMapper_process ls_mapper_instance stick_angle stick_magnitude

        // Retrieve the mapped output
        mapped_x = OctaMapper_get_out_x() 
        mapped_y = OctaMapper_get_out_y() 

        // Apply the mapped values (assuming XB1_LX/LY expect -100 to 100)
        set_val(XB1_LX, mapped_x);
{{ ... }}
*/
// --- Script Entry Points ---
int ls_mapper_instance; // Will hold the "pointer" to our left stick mapper

init {
    init_lookup_tables
    ls_mapper_instance = OctaMapper_constructor
}

main {
    // Get raw stick values (ensure they are in -100 to 100 range as expected by get_polar)
    raw_ls_x = get_val(XB1_LX);
    raw_ls_y = get_val(XB1_LY);

    // Get polar coordinates from raw XY
    // GPC get_polar arguments: (stick_id, type)
    // type 0 for magnitude (0-100), type 1 for angle (0-359)
    stick_magnitude = get_polar(POLAR_LS, 0); // Get magnitude (0-100)
    stick_angle = get_polar(POLAR_LS, 1);     // Get angle (0-359 degrees)

    // If stick is moved (magnitude > some deadzone, e.g., 10)
    if (stick_magnitude > 10) { // Basic deadzone
        // Process using the OctaMapper object
        OctaMapper_process ls_mapper_instance stick_angle stick_magnitude

        // Retrieve the mapped output
        mapped_x = OctaMapper_get_out_x() 
        mapped_y = OctaMapper_get_out_y() 

        // Apply the mapped values (assuming XB1_LX/LY expect -100 to 100)
        set_val(XB1_LX, mapped_x);
        set_val(XB1_LY, mapped_y);
    } else {
        // If in deadzone, output 0
        set_val(XB1_LX, 0);
        set_val(XB1_LY, 0);
    }

    // Example: if right stick is moved, could have another OctaMapper instance for it
    // if(get_val(XB1_RS)) { /* similar logic with rs_mapper_instance */ }
}