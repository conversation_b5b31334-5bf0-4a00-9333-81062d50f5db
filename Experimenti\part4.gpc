////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////

// Roh-Eingaben
int LX, LY;

// Deadzone
int DEADZONE = 30;

// --- <PERSON><PERSON><PERSON> Set ---
int MAX_INPUT1 = 70;
int MAX_SUM1   = 140;

// --- <PERSON><PERSON><PERSON> Set ---
int MAX_INPUT2 = 100;
int MAX_SUM2   = 200;

// Ergebnis-Variablen für Set1
int scaled_x_70_140;
int scaled_y_70_140;

// Ergebnis-Variablen für Set2
int scaled_x_100_200;
int scaled_y_100_200;

// Hilfsvariablen (global, um auch in Funktionen nutzbar zu sein)
int sign, abs_val, output, sum, scale_factor;


////////////////////////////////////////////////////////////////////////////////
// FUNKTION: Apply Square Deadzone auf EINER Achse
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(int val, int deadzone, int maxInput) {
    // Vorzeichen
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);

    // Innerhalb der Deadzone -> 0
    if(abs_val <= deadzone) {
        return 0;
    }

    // Begrenzen, falls > maxInput
    if(abs_val > maxInput) {
        abs_val = maxInput;
    }

    // Linear von [deadzone..maxInput] auf [0..maxInput] skalieren
    output = ((abs_val - deadzone) * maxInput) / (maxInput - deadzone);

    return (sign * output);
}


////////////////////////////////////////////////////////////////////////////////
// FUNKTION: Oktagon-Transformation für Set1 (70/140)
////////////////////////////////////////////////////////////////////////////////
function map_convex_octagon_set1(int x, int y) {
    // 1) Square Deadzone pro Achse
    scaled_x_70_140 = apply_one_axis_deadzone(x, DEADZONE, MAX_INPUT1);
    scaled_y_70_140 = apply_one_axis_deadzone(y, DEADZONE, MAX_INPUT1);

    // 2) Sum-of-abs Clamp -> |x| + |y| <= MAX_SUM1
    sum = abs(scaled_x_70_140) + abs(scaled_y_70_140);
    if(sum > MAX_SUM1) {
        scale_factor = (MAX_SUM1 * 1000) / sum;
        scaled_x_70_140 = (scaled_x_70_140 * scale_factor) / 1000;
        scaled_y_70_140 = (scaled_y_70_140 * scale_factor) / 1000;
    }

    // 3) X/Y auf ±MAX_INPUT1 beschränken
    if(scaled_x_70_140 >  MAX_INPUT1) scaled_x_70_140 =  MAX_INPUT1;
    if(scaled_x_70_140 < -MAX_INPUT1) scaled_x_70_140 = -MAX_INPUT1;
    if(scaled_y_70_140 >  MAX_INPUT1) scaled_y_70_140 =  MAX_INPUT1;
    if(scaled_y_70_140 < -MAX_INPUT1) scaled_y_70_140 = -MAX_INPUT1;
}


////////////////////////////////////////////////////////////////////////////////
// FUNKTION: Oktagon-Transformation für Set2 (100/200)
////////////////////////////////////////////////////////////////////////////////
function map_convex_octagon_set2(int x, int y) {
    // 1) Square Deadzone
    scaled_x_100_200 = apply_one_axis_deadzone(x, DEADZONE, MAX_INPUT2);
    scaled_y_100_200 = apply_one_axis_deadzone(y, DEADZONE, MAX_INPUT2);

    // 2) Sum-of-abs Clamp -> |x| + |y| <= MAX_SUM2
    sum = abs(scaled_x_100_200) + abs(scaled_y_100_200);
    if(sum > MAX_SUM2) {
        scale_factor = (MAX_SUM2 * 1000) / sum;
        scaled_x_100_200 = (scaled_x_100_200 * scale_factor) / 1000;
        scaled_y_100_200 = (scaled_y_100_200 * scale_factor) / 1000;
    }

    // 3) X/Y auf ±MAX_INPUT2 begrenzen
    if(scaled_x_100_200 >  MAX_INPUT2) scaled_x_100_200 =  MAX_INPUT2;
    if(scaled_x_100_200 < -MAX_INPUT2) scaled_x_100_200 = -MAX_INPUT2;
    if(scaled_y_100_200 >  MAX_INPUT2) scaled_y_100_200 =  MAX_INPUT2;
    if(scaled_y_100_200 < -MAX_INPUT2) scaled_y_100_200 = -MAX_INPUT2;
}


////////////////////////////////////////////////////////////////////////////////
// MAIN
////////////////////////////////////////////////////////////////////////////////
main {
    // 1) Rohe Eingaben holen
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);

    // 2) Beide Varianten PARALLEL berechnen
    map_convex_octagon_set1(LX, LY);  // => schreibt in scaled_x_70_140 / scaled_y_70_140
    map_convex_octagon_set2(LX, LY);  // => schreibt in scaled_x_100_200 / scaled_y_100_200

    // 3) Jetzt kannst Du entscheiden, welche Du auf welchen Stick legst
    //    oder nur intern benutzt. Beispiel:
    set_val(XB1_LX, scaled_x_70_140);
    //set_val(XB1_LY, scaled_y_70_140);

    set_val(XB1_LX, scaled_x_100_200);
    //set_val(XB1_LY, scaled_y_100_200);

    // -> So hast Du „gleichzeitig“ beide Oktagon-Varianten:
    //    Links-Stick = Set1 (70/140)
    //    Rechts-Stick = Set2 (100/200)
}
