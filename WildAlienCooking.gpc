// WildAlienCooking.gpc
// Activates XB1_RB or XB1_LB based on left stick angle zones

// Constants
define STICK_DEADZONE = 5000;  // Deadzone to prevent unwanted activation

// Variables
int currentAngle;
int stickMagnitude;

main {
    // Get the current angle of the left stick (0-359 degrees)
    currentAngle = get_polar(POLAR_LS, POLAR_ANGLE);
    
    // Get the magnitude of the left stick movement
    stickMagnitude = get_polar(POLAR_LS, POLAR_RADIUS);
    
    // Only activate buttons if stick is moved beyond deadzone
    if(stickMagnitude > STICK_DEADZONE) {
        // XB1_RB zones: 315-45 and 135-225 degrees
        if((currentAngle >= 315 || currentAngle <= 45) || 
           (currentAngle >= 135 && currentAngle <= 225)) {
            set_val(XB1_RB, 100);
            set_val(XB1_LB, 0);
        }
        // XB1_LB zones: 45-135 and 225-315 degrees
        else if((currentAngle > 45 && currentAngle < 135) || 
                (currentAngle > 225 && currentAngle < 315)) {
            set_val(XB1_LB, 100);
            set_val(XB1_RB, 0);
        }
    } else {
        // Reset both buttons when stick is in deadzone
        set_val(XB1_RB, 0);
        set_val(XB1_LB, 0);
    }
}