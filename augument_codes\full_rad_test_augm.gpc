// Using integers for better precision
int idle_start;
int x;
int y;
int last_radius;
int current_radius;
int current_angle;

// Constants for better readability and configuration
define DEADZONE = 100;
define IDLE_TIMEOUT = 2000;

init {
    idle_start = 0;
    last_radius = 0;
}

main {
    // Using get_ipolar for unmodified input values
    current_radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
    current_angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
    
    // Get current stick values using GHOST virtual stick for calculations
    x = get_val(ANALOG_GHOSTX);
    y = get_val(ANALOG_GHOSTY);
    
    // Using comparison for deadzone detection
    if(abs(x) < DEADZONE && abs(y) < DEADZONE) {
        if(idle_start == 0) {
            idle_start = get_rtime();
            // Store the last active radius using get_polar for modified value
            last_radius = get_polar(POLAR_LS, POLAR_RADIUS);
        }
        
        // Using time comparison
        if(get_rtime() - idle_start > IDLE_TIMEOUT) {
            // Gradually reduce radius instead of immediate zero
            last_radius = max(0, last_radius - 1000);
        }
        
        // Apply the calculated radius
        set_polar2(POLAR_LS, current_angle, last_radius);
        
        // Debug output using traces
        set_val(TRACE_1, last_radius);
        set_val(TRACE_2, current_angle);
    } else {
        idle_start = 0;
        last_radius = current_radius;
        set_polar2(POLAR_LS, current_angle, last_radius);
        
        // Debug output
        set_val(TRACE_3, last_radius);
    }
}
