int aimCorrection = TRUE;
define Ads = XB1_LT;
define Fire = XB1_RT;
define axisX = XB1_RX;
define axisY = XB1_RY;
define aimStick = POLAR_RS;

// Polar Aim Assist
define PolarAssist = TRUE;
define Radius = 100;
define Speed = 16;
define Release = 20;
// Polar Recoil
define PolarRecoil = TRUE;
define Strength = 25;
define Angle2 = 90;

int AimAssistAngle;
int GenStr;            // Do not touch this
int AdsSens     =  90; // ADS Only
int AdsFireSens = 100; // ADS & FIRE
int FireSens    =  80; // FIRE Only
int GeneralSens = 100; // NO ADS or FIRE 

main {
    if(PolarAssist) {
        if (get_ival(Ads) && !get_ival(Fire)) {
            if (abs(get_ival(axisX)) < Release && abs(get_ival(axisY)) < Release) {
                set_polar(aimStick, AimAssistAngle = (AimAssistAngle + Speed) % 360 ,Radius * 327);
            }
        }
    }
    if (PolarRecoil) {
          if (get_ival(Fire)) {
            if (abs(get_ival(axisY)) < Strength + 1 && abs(get_ival(axisX)) < Strength / 2) {
                PolarRecoil(Strength);
            }
        }
    }
    if(aimCorrection){ 
        if(get_ival(Ads)){ 
            if(!get_ival(Fire)){ 
                GenStr = AdsSens;
            }else{ 
                GenStr = AdsFireSens;
            } 
        }
        else if(get_ival(Fire)){ 
            GenStr = FireSens; 
        }else{ 
            GenStr = GeneralSens;
        }
        customPolarSens(POLAR_RX,8192,GenStr * 32767/100);
        customPolarSens(POLAR_RY,32767,GenStr * 32767/100);    
    }    
}
int fValue,fValueSens;
function customPolarSens(fId,fMid,fIndex) {
    fValue = get_val(fId);
    if(fMid != NOT_USE) {
        fValueSens = -1;
        if(fValue >= 0) fValueSens = 1;
            fValue *= fValueSens;
        if(fValue <= fMid) 
            fValue = (fValue * 16384) / fMid;
        else 
            fValue = ((16384 * (fValue - fMid)) / (32767 - fMid)) + 16384;
            fValue *= fValueSens;
    }
    if(fIndex != NOT_USE) {
        fValue = (fValue * fIndex) / 32767;
    }
    set_val(fId,clamp(fValue,-32768,32767));
    return;
}
function PolarRecoil(Output) {
    set_polar(aimStick,Angle2,Output * 328);
} 