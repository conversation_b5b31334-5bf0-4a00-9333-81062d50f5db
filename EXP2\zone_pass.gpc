define zone_MAX_RADIUS = 32767;
define zone_REDUCED_RADIUS = 10000;

int zone_radius;
int zone_max_allowed_radius;

main {
    zone_pass();
}

function zone_pass() {
    // Only apply zone pass if XB1_A, XB1_Y, or XB1_X are active
    if(get_ival(XB1_A) || get_ival(XB1_Y) || get_ival(XB1_X)) {
        // Store the zone_radius value to avoid repeated function calls
        zone_radius = get_polar(POLAR_LS, POLAR_RADIUS);

        // Check if either LT or RT is pressed
        if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
            zone_max_allowed_radius = zone_MAX_RADIUS;
        } else {
            zone_max_allowed_radius = zone_REDUCED_RADIUS;
        }

        set_polar(POLAR_LS,
            zone_quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), zone_AngleInterval_2),
            min(zone_calculate_radius(), zone_max_allowed_radius) // Ensure zone_radius is within allowed limit
        );
    }
    // If none of the specified buttons are pressed, do nothing (pass through original stick values)
}

define zone_AngleInterval_2 = 16;    // Changed from 12 to 30 for 16 zones
// This defines the number of zones.  A value of 16 creates 16 zones.

function zone_quantize_angle(angle, interval) {
    // This function quantizes the angle into discrete zones.
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

function zone_calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)); // Calculate zone_radius using Pythagorean theorem
}
