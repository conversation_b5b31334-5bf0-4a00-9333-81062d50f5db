// Define color constants
define OFF = 0;
define DIM_BLUE = 1;
define DIM_RED = 2;
define DIM_GREEN = 3;
define DIM_PINK = 4;
define DIM_SKYBLUE = 5;
define DIM_YELLOW = 6;
define DIM_WHITE = 7;
define BLUE = 8;
define RED = 9;
define GREEN = 10;
define PINK = 11;
define SKYBLUE = 12;
define YELLOW = 13;
define WHITE = 14;
define BRIGHT_BLUE = 15;
define BRIGHT_RED = 16;
define BRIGHT_GREEN = 17;
define BRIGHT_PINK = 18;
define BRIGHT_SKYBLUE = 19;
define BRIGHT_YELLOW = 20;
define BRIGHT_WHITE = 21;

// LED color data
data (
    0,0,0,0, // Off
    1,0,0,0, // Dim Blue
    0,1,0,0, // Dim Red
    0,0,1,0, // Dim Green
    0,0,0,1, // Dim Pink
    1,0,1,0, // Dim SkyBlue
    0,1,1,0, // Dim Yellow
    1,1,1,1, // <PERSON>m White
    2,0,0,0, // Blue
    0,2,0,0, // Red
    0,0,2,0, // Green
    0,0,0,2, // Pink
    2,0,2,0, // SkyBlue
    0,2,2,0, // Yellow
    2,2,2,2, // White
    3,0,0,0, // Bright Blue
    0,3,0,0, // Bright Red
    0,0,3,0, // Bright Green
    0,0,0,3, // Bright Pink
    3,0,3,0, // Bright SkyBlue
    0,3,3,0, // Bright Yellow
    3,3,3,3  // Bright White
);

// Threshold for stick press
define STICK_PRESS_LB_THRESHOLD = 95;

int stickSwapToggle;
int rightStickMagnitude;
int ultimatePower;
int dynamicAcceleration;

int ddValue = 35;

function icos(x) { 
    return isin(x + 8192); 
}

function isin(x) {
    x = (x % 32767) << 17;
    if ((x ^ (x * 2)) < 0) { 
        x = (-2147483648) - x;
    }
    x = x >> 17;
    return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggleActive;
int cosAngle, sinAngle;

// Increase the frequency of peaks and dips
int multiplier = 24;

function setPolarDD(stick, angle, radius, ddFactor) {
    ddFactor = (ddFactor * 32767) / 80;
    radius = (radius * 32767) / 10000;
    angle = (360 - angle) * 91;

    sinAngle = isin(angle); 
    cosAngle = icos(angle);

    angle = (angle * multiplier) % 32767;

    // Adjusted radius calculation for more peaks and dips
    stick.x = ((radius * cosAngle) >> 16) + ((ddFactor * cosAngle) >> 16);
    stick.y = ((radius * sinAngle) >> 16) + ((ddFactor * sinAngle) >> 16);
}

int timeToClearScreen = 2000;

combo clearScreen {     
    wait(20);     
    cls_oled(0); 
}

// Center X Function (Made By Batts)
function centerPosition(fChars, fFont) {
    return (OLED_WIDTH / 2) - ((fChars * fFont) / 2);
}

int rumblePower = 100;
int vibrateType;

combo notifyCombo {
    set_rumble(vibrateType, 100);
    wait(300);
    reset_rumble();
    wait(20);
}

int dataIndex;

// NumberToString (Made By Batts)
int bufferIndex;
int charIndex, digitIndex;

function numberToString(fVal, fDigits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if (fVal < 0) { // Handle negative numbers
        putc_oled(bufferIndex, 45); // Add leading "-"
        bufferIndex += 1;
        fVal = abs(fVal);
    } 
    for (charIndex = 5; charIndex >= 1; charIndex--) {
        if (fDigits >= charIndex) {
            putc_oled(bufferIndex, (fVal / digitIndex) + 48);
            fVal %= digitIndex;
            bufferIndex++; 
            if (charIndex == 4) {
                putc_oled(bufferIndex, 44); // Add ","
                bufferIndex++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, bufferIndex - 1, OLED_WHITE);
} 

int logVal;

function findDigits(num) {
    logVal = 0;
    do {
        num /= 10;
        logVal++;
    } while (num);
    return logVal;
}
