// GPS Zones Calculation

// Define the order array
int order[8] = {0, 1, 2, 3, 4, 5, 6, 7};

// Global variable for polar_angle
int polar_angle;

// Function to calculate zone for POLAR_LS
function int calc_zone() {
    int polar_LS;
    int index;
    int zone_p;
    
    polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
    index = ((polar_LS + 23) % 360) / 45;  // Using 23 instead of 22.5 to avoid floating point
    zone_p = order[index];
    return zone_p;
}

// Function to calculate zone for POLAR_RS
function int calc_RS() {
    int index;
    int zone_RS;
    
    polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
    index = ((polar_angle + 23) % 360) / 45;  // Using 23 instead of 22.5 to avoid floating point
    zone_RS = order[index];
    return zone_RS;
}

// Main function (if needed)
function main() {
    // Add any necessary initialization or main logic here
    int result_LS = calc_zone();
    int result_RS = calc_RS();
    
    // You can add print statements or use the results as needed
}