// An array that holds maximum allowed scaling factors for angles in the first 45°.
// There are 45 elements (from index 0 to 44), each corresponding to a specific angle
// within a 45-degree segment. (For example, index 0 is for 0°, index 1 for 1°, etc.)
// These values are used to determine how far out (in radius) the polar coordinate
// is allowed to go for a given angle.
const int8 anglesMax[] = {
  100, 98, 97, 96, 94, 94, 92, 92, 91, 90,
  90, 91, 92, 93, 94, 95, 96, 97, 98, 98,
  99, 100, 100, 98, 97, 96, 94, 94, 92, 92,
  91, 90, 90, 91, 92, 93, 94, 95, 96, 96,
  97, 98, 98, 99, 100
};

int radius, angle;

main {
  // Retrieve the current angle from the left-side polar sensor/device.
  // POLAR_LS is likely a constant indicating which polar device or side we are reading from.
  angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
  
  // Retrieve the current radius (or magnitude) from the same polar sensor/device.
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);

  // The next line limits (clips) the radius so that it does not exceed a maximum value
  // that is specific to the current angle.
  // Explanation:
  // - anglesMax[angle % 45]: We use the modulus operator (%) to get the remainder when the
  //   angle is divided by 45. This gives us an index between 0 and 44. The idea is that the
  //   maximum allowed radius is defined only for a 45° segment and then reused (by symmetry)
  //   for all other angles.
  // - 100 * anglesMax[angle % 45]: The value from the anglesMax array is multiplied by 100.
  //   This defines the maximum allowed radius for this angle.
  // - min(radius, 100 * anglesMax[angle % 45]): The min() function returns the smaller of the
  //   current radius and the computed maximum. This effectively “clips” the radius if it’s too high.
  set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
}