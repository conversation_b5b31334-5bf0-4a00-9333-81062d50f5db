int start_time;
int isBPressed = 0;
int blockBInput = 0;

const int8 anglesMax[] = {100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 97, 98, 98, 99, 100, 100, 98, 97, 96, 94, 94, 92, 92, 91, 90, 90, 91, 92, 93, 94, 95, 96, 96, 97, 98, 98, 99, 100};

int radius, angle;
main {

	    if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(MyCombo);
    } else if(combo_running(MyCombo)) {
        combo_stop(MyCombo);
    }

  angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
  radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
  set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle%45]));
  
  
    // Check if XB1_B button is pressed and not blocked
    if (event_press(XB1_B) && !blockBInput) {
        start_time = get_rtime(); // Record the time when the button was pressed
        isBPressed = 1;
        combo_run(PressB);
    }

    // Check if the button is released
    if (event_release(XB1_B)) {
        isBPressed = 0;
        blockBInput = 0; // Re-enable the B button input on release
        combo_stop(PressB);
    }

    // Check if 300 ms have passed since the button was pressed
    if (isBPressed && get_rtime() - start_time >= 300) {
        blockBInput = 1;
        set_val(XB1_B, 0); // Programmatically release the button
    }  
  
}

combo MyCombo {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LB, 100); //750
	wait(440);
    set_val(XB1_LB, 100); // Press XB1_LB
    wait(80);             // Wait for 80ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(40);             // Wait for 40ms pause
    set_val(XB1_LB, 100); // Press XB1_LB again
    wait(250);            // Wait for 150ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(250);  
}

combo PressB {
    set_val(XB1_B, 100); // Press the button
    wait(300);           // Wait for 300 milliseconds
    set_val(XB1_B, 0);   // Release the button
    wait(6000);           // Wait for 300 milliseconds
} 