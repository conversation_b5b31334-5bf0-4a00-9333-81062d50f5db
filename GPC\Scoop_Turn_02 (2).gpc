// Script was generated with < FIFA Series Skills Generator > ver. 11.8 Date :02.03.21 Time: 3:50:04
//------------------------------------------------------------------------
/********************************************************************************************** 
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
***********************************************************************************************/ 
                                                                       
                                                                       
/***********************************************************************
                                                                       
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\    $$\           
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$$$ |          
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\_$$ |          
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  |  $$ |          
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/   $$ |         
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |        $$ |         
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$\        
  \__|      \______|\__|      \__|  \__|      \________|\______|       
                                                                       
*************************************************************************/
                                                                         
//*************************************************************************/
                                                                         
//***************  XBOX ONE Elite Controller   **************************\\
//==\\//==\\//==        ONLY PADDLES           ==//==\\//==\\//==
//-----------------------------------------------------------------------
                                                                         
///\ Paddle Right 1 : 19. Ball Roll//\
///\ Paddle Right 2 : 0. None.//\
///\ Paddle Left  1 : 19. Ball Roll//\
///\ Paddle Left  2 : 0. None.//\
//-------------------------------------------------------------- 
// UNMAPING                                                 
//-------------------------------------------------------------- 
unmap 24;  // Paddle Right 1
unmap 25;  // Paddle Right 2
unmap 26;  // Paddle Left  1
unmap 27;  // Paddle Left  2
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define DOnotUSE      =   1;
define DelayNormal   =  80;
define DelayRnbwHOLD = 160;
define DelayRest     = 200;
define DelayLeft_move= 500;
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = PS4_L2; // Pace Control
define FinesseShot   = PS4_R1; // Finesse Shot
define PlayerRun     = PS4_L1; // Player Run  
define ShotBtn       = PS4_CIRCLE; // Shot Btn  
define SprintBtn     = PS4_R2; // Sprint Btn 
define PassBtn       = PS4_CROSS; // Pass Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
//--------------------------------------------------------------   
define UP         = 0; 
define UP_RIGHT   = 1; 
define RIGHT      = 2; 
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int b_tap; 
                                               
int block_time;                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                
                                                                
                                                             
      // ON / OFF All Skills   hold XB Start and press A / CROSS                            
      if(get_val(1)){                                  
         if(event_press(19))OnOffMods=!OnOffMods; 
         set_val(19,0);                        
      }                                        
                                                
     if(OnOffMods) { // All mods ---------------------------------------------
                                                
        ///////////////////////////////////////////////////////////// 
        //                                                           
        if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
	            LX = get_val(MOVE_X);                                      
	            LY = get_val(MOVE_Y);
	            calc_zone();
	            set_val(TRACE_1,zone_p); 
        }                                                           
        //----------------------------------------------------------- 
       
	if(block_time) {
	    block_time -= get_rtime();
		set_val(PS4_LX, 0);
		set_val(PS4_LY, 0);
              
   }
/////////////////////////////////////////////////////////////////////////
      // Paddle Right 1                
      //--------------------------------------------------------------
      if (event_press(XB1_RS)){  
      
	      right_on = 1;
	      block_time = 1000;
	      combo_run(SCOOP_TURN); 
      
      } //  
                                                
      set_val(XB1_RS,0);               
      //--------------------------------------------------------------
                                                
      // Paddle Left 1                
      //--------------------------------------------------------------
      if (event_press(XB1_LS)){ 
		  right_on = 0;
      	  block_time = 1000;
          combo_run(SCOOP_TURN); 
      } // 
                                                
      // Paddle Left 2                
      //--------------------------------------------------------------
                                                
                                                                     
        set_val(XB1_PR1,0);                     
        set_val(XB1_PR2,0);                     
        set_val(XB1_PL1,0);                     
        set_val(XB1_PL2,0);                     
                                                                     
                                                
                                              
     }// ON / OFF mods                          
     //--------------------------------------------------------------
                                                                     
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
                                                                
combo ONE_TAP {                                    
    b_tap=TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    b_tap=FALSE;                                  
}                                              
///////////////////////////////
// Ball Roll                   
combo BALL_ROLL {               
	RA_L_R () ;    // Left or Right 
	wait(250);
	wait(250);
	
	combo_run(SCOOP_TURN);
}



combo SCOOP_TURN {
    
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
    set_val(TRACE_2,dEnd);
    
    calc_relative_xy(dEnd);
    
    set_val(TRACE_5,move_lx);
    set_val(TRACE_6,move_ly);
    
	LA(move_lx,move_ly);
	wait(40);
	
     
	set_val(ShotBtn,100);
	LA(move_lx,move_ly);
	wait(40);
	
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	LA(move_lx,move_ly);
	wait(60); 
   
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	LA(move_lx,move_ly);
	wait(160);
	 
    LA(move_lx,move_ly);
    
    wait(800);
}             
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------

///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;
function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 8 - zone_p;
        else if(d >= 8) d = zone_p - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             