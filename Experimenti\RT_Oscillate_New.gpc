int oscillate_value;
int oscillation_speed = 10; // Adjust this to change oscillation speed

main {
    // Check if RT is being pressed
    if(get_val(XB1_RT)) {
        if(get_val(XB1_RT) < 100) {
            // Create oscillation between 50 and 100
            oscillate_value = 75 + (sin(combo_run_time() * oscillation_speed) * 25);
            set_val(XB1_RT, oscillate_value);
        } else {
            // Keep it at 100 if fully pressed
            set_val(XB1_RT, 100);
        }
    }
}
 