int DA_FC216[0];
init {
	DA_FC115();
	combo_run(DA_FC1);
	// F_Rainbow_PS_Skill = 1
	combo_run(DA_FC2);
	// Rainbow_PS_Skill = 2
	combo_run(DA_FC3);
	// Roll_Sombrero_PS_SKILL = 3
	combo_run(DA_FC4);
	// STEP_OVER_FEINT_SKILL = 4
	combo_run(DA_FC5);
	// REVERSE_STEP_OVER_SKILL = 5
	combo_run(DA_FC6);
	// STEP_OVER_BOOST_SKILL = 6
	combo_run(DA_FC7);
	// HOCUS_POCUS_SKILL = 7
	combo_run(DA_FC8);
	// TRIPLE_ELASTICO_SKILL = 8
	combo_run(DA_FC9);
	// ELASTICO_SKILL = 9
	combo_run(DA_FC10);
	// REVERSE_ELASTICO_SKILL = 10
	combo_run(DA_FC11);
	// FLAIR_Nutmeg_SKILL = 11
	combo_run(DA_FC12);
	// DRAG_BACK_UNIVERSAL_SKILL = 12
	combo_run(DA_FC13);
	// DRAG_TO_DRAG_SKILL = 13
	combo_run(DA_FC14);
	// CRUYFF_TURN_SKILL = 14
	combo_run(DA_FC15);
	// LA_CROQUETA_SKILL = 15
	combo_run(DA_FC16);
	// ROULETTE_SKILL = 16
	combo_run(DA_FC17);
	// FLAIR_ROULETTE_SKILL = 17
	combo_run(DA_FC18);
	// FEINT_AND_EXIT_SKILL = 18
	combo_run(DA_FC19);
	// FEINT_L_EXIT_R_SKILL = 19
	combo_run(DA_FC20);
	// WAKA_WAKA_SKILL = 20
	combo_run(DA_FC21);
	// BODY_FEINT_SKILL = 21
	combo_run(DA_FC22);
	// FEINT_FORWARD_AND_TURN = 22
	combo_run(DA_FC23);
	// TURN_BACK = 23
	combo_run(DA_FC24);
	// DIRECTIONAL_NUTMEG_SKILL = 24
	combo_run(DA_FC25);
	// FOUR_TOUCH_TURN_SKILLS = 25
	combo_run(DA_FC26);
	// SKILLED_BRIDGE_SKILL = 26
	combo_run(DA_FC27);
	// CANCELED_4_TOUCH_TURN_SKILL = 27
	combo_run(DA_FC28);
	// Roll_Sombrero_SKILL = 28
	combo_run(DA_FC29);
	// BALL_ROLL_SKILL = 29
	combo_run(DA_FC30);
	// Ball_Roll_drag_SKILL = 30
	combo_run(DA_FC31);
	// BALL_ROLL_CHOP_SKILL = 31
	combo_run(DA_FC32);
	// BALL_ROLL_FAKE_TURN = 32
	combo_run(DA_FC33);
	// BALL_ROLL_TO_SCOOP_TURN_SKILL = 33
	combo_run(DA_FC34);
	// SCOOP_TURN_FAKE_SKILL = 34
	combo_run(DA_FC35);
	// BALL_ROLL_STEP_OVER_SKILL = 35
	combo_run(DA_FC36);
	// BALL_ROLL_CUT_180_SKILL = 36
	combo_run(DA_FC37);
	// HEEL_TO_HEEL_FLICK_SKILL = 37
	combo_run(DA_FC38);
	// LATERAL_HEEL_TO_HEEL_SKILL = 38
	combo_run(DA_FC39);
	// DRAG_TO_HEEL = 39
	combo_run(DA_FC40);
	// DIAGONAL_HEEL_CHOP_SKILL = 40
	combo_run(DA_FC41);
	// HEEL_TO_BALL_ROLL_SKILL = 41
	combo_run(DA_FC42);
	// BERBA_MCGEADY_SPIN_SKILL = 42
	combo_run(DA_FC43);
	// ONE_FOOT_SPIN_SKILL = 43
	combo_run(DA_FC44);
	// CANCELED_BERBA_SPIN_SKILL = 44
	combo_run(DA_FC45);
	// CANCELED_BERBA_SPIN_WITH_DIRECTION = 45
	combo_run(DA_FC46);
	// SPIN_MOVE_LEFT_RIGHT_SKILL = 46
	combo_run(DA_FC47);
	// FAKE_BERBA_OPP_EXIT_SKILL = 47
	combo_run(DA_FC48);
	// FAKE_BERBA_FAKE_DRAG_SKILL = 48
	combo_run(DA_FC49);
	// FAKE_SHOT_SKILL = 49
	combo_run(DA_FC50);
	// FAKE_PASS_SKILL = 50
	combo_run(DA_FC51);
	// FAKE_DRAG_BACK_SKILL = 51
	combo_run(DA_FC52);
	// FAKE_SHOT_CANCEL_SKILL = 52
	combo_run(DA_FC53);
	// FAKE_RABONA_SKILL = 53
	combo_run(DA_FC54);
	// JOG_OPENUP_FAKE_SHOT = 54
	combo_run(DA_FC55);
	// CANCEL_SHOOT_SKILL = 55
	combo_run(DA_FC56);
	// RONALDO_CHOP_SKILL = 56
	combo_run(DA_FC57);
	// OKKOSHA_FLICK_SKILL = 57
	combo_run(DA_FC58);
	// RAINBOW_SKILL = 58
	combo_run(DA_FC59);
	// Flair_Rainbow_SKILL = 59
	combo_run(DA_FC60);
	// ADVANCED_RAINBOW_SKILL = 60
	combo_run(DA_FC61);
	// JUGGLING_RAINBOW_SKILL = 61
	combo_run(DA_FC62);
	// DRAG_BACK_SOMBRERO_SKILL = 62
	combo_run(DA_FC63);
	// SOMBRERO_FLICK_SKILL = 63
	combo_run(DA_FC64);
	// TR_BALL_HOP_SKILL = 64
	combo_run(DA_FC65);
	// TR_Flick_Up_Heel = 65
	combo_run(DA_FC66);
	// TR_Quick_Skill = 66
	combo_run(DA_FC67);
	// TR_Flicks = 67
	combo_run(DA_FC68);
	// TR_180_Flick = 68
	combo_run(DA_FC69);
	// TR_CALF_Rainbow = 69
	combo_run(DA_FC70);
	// FL_Nutmg_L_R = 70
	combo_stop(DA_FC1);
	// F_Rainbow_PS_Skill = 1
	combo_stop(DA_FC2);
	// Rainbow_PS_Skill = 2
	combo_stop(DA_FC3);
	// Roll_Sombrero_PS_SKILL = 3
	combo_stop(DA_FC4);
	// STEP_OVER_FEINT_SKILL = 4
	combo_stop(DA_FC5);
	// REVERSE_STEP_OVER_SKILL = 5
	combo_stop(DA_FC6);
	// STEP_OVER_BOOST_SKILL = 6
	combo_stop(DA_FC7);
	// HOCUS_POCUS_SKILL = 7
	combo_stop(DA_FC8);
	// TRIPLE_ELASTICO_SKILL = 8
	combo_stop(DA_FC9);
	// ELASTICO_SKILL = 9
	combo_stop(DA_FC10);
	// REVERSE_ELASTICO_SKILL = 10
	combo_stop(DA_FC11);
	// FLAIR_Nutmeg_SKILL = 11
	combo_stop(DA_FC12);
	// DRAG_BACK_UNIVERSAL_SKILL = 12
	combo_stop(DA_FC13);
	// DRAG_TO_DRAG_SKILL = 13
	combo_stop(DA_FC14);
	// CRUYFF_TURN_SKILL = 14
	combo_stop(DA_FC15);
	// LA_CROQUETA_SKILL = 15
	combo_stop(DA_FC16);
	// ROULETTE_SKILL = 16
	combo_stop(DA_FC17);
	// FLAIR_ROULETTE_SKILL = 17
	combo_stop(DA_FC18);
	// FEINT_AND_EXIT_SKILL = 18
	combo_stop(DA_FC19);
	// FEINT_L_EXIT_R_SKILL = 19
	combo_stop(DA_FC20);
	// WAKA_WAKA_SKILL = 20
	combo_stop(DA_FC21);
	// BODY_FEINT_SKILL = 21
	combo_stop(DA_FC22);
	// FEINT_FORWARD_AND_TURN = 22
	combo_stop(DA_FC23);
	// TURN_BACK = 23
	combo_stop(DA_FC24);
	// DIRECTIONAL_NUTMEG_SKILL = 24
	combo_stop(DA_FC25);
	// FOUR_TOUCH_TURN_SKILLS = 25
	combo_stop(DA_FC26);
	// SKILLED_BRIDGE_SKILL = 26
	combo_stop(DA_FC27);
	// CANCELED_4_TOUCH_TURN_SKILL = 27
	combo_stop(DA_FC28);
	// Roll_Sombrero_SKILL = 28
	combo_stop(DA_FC29);
	// BALL_ROLL_SKILL = 29
	combo_stop(DA_FC30);
	// Ball_Roll_drag_SKILL = 30
	combo_stop(DA_FC31);
	// BALL_ROLL_CHOP_SKILL = 31
	combo_stop(DA_FC32);
	// BALL_ROLL_FAKE_TURN = 32
	combo_stop(DA_FC33);
	// BALL_ROLL_TO_SCOOP_TURN_SKILL = 33
	combo_stop(DA_FC34);
	// SCOOP_TURN_FAKE_SKILL = 34
	combo_stop(DA_FC35);
	// BALL_ROLL_STEP_OVER_SKILL = 35
	combo_stop(DA_FC36);
	// BALL_ROLL_CUT_180_SKILL = 36
	combo_stop(DA_FC37);
	// HEEL_TO_HEEL_FLICK_SKILL = 37
	combo_stop(DA_FC38);
	// LATERAL_HEEL_TO_HEEL_SKILL = 38
	combo_stop(DA_FC39);
	// DRAG_TO_HEEL = 39
	combo_stop(DA_FC40);
	// DIAGONAL_HEEL_CHOP_SKILL = 40
	combo_stop(DA_FC41);
	// HEEL_TO_BALL_ROLL_SKILL = 41
	combo_stop(DA_FC42);
	// BERBA_MCGEADY_SPIN_SKILL = 42
	combo_stop(DA_FC43);
	// ONE_FOOT_SPIN_SKILL = 43
	combo_stop(DA_FC44);
	// CANCELED_BERBA_SPIN_SKILL = 44
	combo_stop(DA_FC45);
	// CANCELED_BERBA_SPIN_WITH_DIRECTION = 45
	combo_stop(DA_FC46);
	// SPIN_MOVE_LEFT_RIGHT_SKILL = 46
	combo_stop(DA_FC47);
	// FAKE_BERBA_OPP_EXIT_SKILL = 47
	combo_stop(DA_FC48);
	// FAKE_BERBA_FAKE_DRAG_SKILL = 48
	combo_stop(DA_FC49);
	// FAKE_SHOT_SKILL = 49
	combo_stop(DA_FC50);
	// FAKE_PASS_SKILL = 50
	combo_stop(DA_FC51);
	// FAKE_DRAG_BACK_SKILL = 51
	combo_stop(DA_FC52);
	// FAKE_SHOT_CANCEL_SKILL = 52
	combo_stop(DA_FC53);
	// FAKE_RABONA_SKILL = 53
	combo_stop(DA_FC54);
	// JOG_OPENUP_FAKE_SHOT = 54
	combo_stop(DA_FC55);
	// CANCEL_SHOOT_SKILL = 55
	combo_stop(DA_FC56);
	// RONALDO_CHOP_SKILL = 56
	combo_stop(DA_FC57);
	// OKKOSHA_FLICK_SKILL = 57
	combo_stop(DA_FC58);
	// RAINBOW_SKILL = 58
	combo_stop(DA_FC59);
	// Flair_Rainbow_SKILL = 59
	combo_stop(DA_FC60);
	// ADVANCED_RAINBOW_SKILL = 60
	combo_stop(DA_FC61);
	// JUGGLING_RAINBOW_SKILL = 61
	combo_stop(DA_FC62);
	// DRAG_BACK_SOMBRERO_SKILL = 62
	combo_stop(DA_FC63);
	// SOMBRERO_FLICK_SKILL = 63
	combo_stop(DA_FC64);
	// TR_BALL_HOP_SKILL = 64
	combo_stop(DA_FC65);
	// TR_Flick_Up_Heel = 65
	combo_stop(DA_FC66);
	// TR_Quick_Skill = 66
	combo_stop(DA_FC67);
	// TR_Flicks = 67
	combo_stop(DA_FC68);
	// TR_180_Flick = 68
	combo_stop(DA_FC69);
	// TR_CALF_Rainbow = 69
	combo_stop(DA_FC70);
	// FL_Nutmg_L_R = 70
}
int DA_FC275 ;
int DA_FC276;
int DA_FC277;
int DA_FC278;
int DA_FC279;
//====================================================
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
// Reordered Definitions
define DA_FC280 = 0;
define DA_FC281 = 1;
define DA_FC282 = 2;
define DA_FC283 = 3;
define DA_FC284 = 4;
define DA_FC285 = 5;
define DA_FC286 = 6;
define DA_FC287 = 7;
define DA_FC288 = 8;
define DA_FC289 = 9;
define DA_FC290 = 10;
define DA_FC291 = 11;
define DA_FC292 = 12;
define DA_FC293 = 13;
define DA_FC294 = 14;
define DA_FC295 = 15;
define DA_FC296 = 16;
define DA_FC297 = 17;
define DA_FC298 = 18;
define DA_FC299 = 19;
define DA_FC300 = 20;
define DA_FC301 = 21;
define DA_FC302 = 22;
define DA_FC23 = 23;
define DA_FC304 = 24;
define DA_FC305 = 25;
define DA_FC306 = 26;
define DA_FC307 = 27;
define DA_FC308 = 28;
define DA_FC309 = 29;
define DA_FC310 = 30;
define DA_FC311 = 31;
define DA_FC312 = 32;
define DA_FC313 = 33;
define DA_FC314 = 34;
define DA_FC315 = 35;
define DA_FC316 = 36;
define DA_FC317 = 37;
define DA_FC318 = 38;
define DA_FC319 = 39;
define DA_FC320 = 40;
define DA_FC321 = 41;
define DA_FC322 = 42;
define DA_FC323 = 43;
define DA_FC324 = 44;
define DA_FC325 = 45;
define DA_FC326 = 46;
define DA_FC327 = 47;
define DA_FC328 = 48;
define DA_FC329 = 49;
define DA_FC330 = 50;
define DA_FC331 = 51;
define DA_FC332 = 52;
define DA_FC333 = 53;
define DA_FC334 = 54;
define DA_FC335 = 55;
define DA_FC336 = 56;
define DA_FC337 = 57;
define DA_FC338 = 58;
define DA_FC339 = 59;
define DA_FC340 = 60;
define DA_FC341 = 61;
define DA_FC342 = 62;
define DA_FC343 = 63;
/*
define JUGGLE_BACK_SOMBRERO_SKILL = 64;
define STOP_V_DRAG_SKILL = 65;
define REV_OR_ELASTICO_SKILL = 66;
define STOP_REV_OR_ELASTICO_SKILL = 67;
define DRAG_REV_OR_ELASTICO_SKILL = 68;
define RABONA_TO_REV_ELASTICO_SKILL = 69;
define RABONA_TO_ELASTICO_SKILL = 70;
define STOP_LA_CROQUETA_SKILL = 71;
*/
//TR UPDATE//
define DA_FC344 = 64;
// sttand > RS LS 45 forward (stand)
define DA_FC345 = 65;
// sttand > RS LS  forward (stand)
define DA_FC346 = 66;
// hold LT + RT ( stand)
define DA_FC347 = 67;
// (stand)
define DA_FC348 = 68;
// move > RS + L2 + LS  forward 
define DA_FC349 = 69;
// Stand > RS + L2 + LS  forward 
define DA_FC350 = 70;
// Stand > RS + L2 + LS  forward 
//-- VM Speed
define DA_FC351 = 0;
function DA_FC109(DA_FC110) {
	if (DA_FC110 == 0) vm_tctrl(-0);
	//10 ms Default
	else if (DA_FC110 == 1) vm_tctrl(2);
	// 8 ms XB ONE
	else if (DA_FC110 == 2) vm_tctrl(-2);
	// 4 ms PS5/PS5
	else if (DA_FC110 == 3) vm_tctrl(-4);
	// 2 ms XB1 X/S
	else if (DA_FC110 == 4) vm_tctrl(-6);
	// 1 ms PC 
	else if (DA_FC110 == 5) vm_tctrl(-8);
	// 1 ms PC 
	else if (DA_FC110 == 6) vm_tctrl(-9);
	// 1 ms PC 
}
/* Menu Variables */
int DA_FC352, DA_FC353;
int DA_FC354, DA_FC355;
/* Display Variables / ScreenSaver / Strings/Text  */
int DA_FC356 = FALSE, DA_FC357;
int DA_FC358 = TRUE;
int DA_FC359;
const string DA_FC780[] = {
	"Off",  "On"
}
;
/* Mod Menu Variables */
int DA_FC360;
// 0
int DA_FC361;
// 1  RS New Way 
int DA_FC362;
// 2  OnOffRightStick (oldschool)
int DA_FC363;
// 3  Random Skills
int DA_FC364;
// 4  Random Skills
int DA_FC365;
// 5  Instant Skills
int DA_FC366;
// 6  Additin Skill 1
int DA_FC367;
// 7  Additin Skill 3
int DA_FC368;
// 8  Additin Skill 2
int DA_FC369;
// 9  Additin Skill 4
int DA_FC370;
// 10  Free Kick
int DA_FC371;
// 11  Corner Goal
int DA_FC372;
// 12  Player Lock
int DA_FC373;
// 13 Universal EA Ping
int DA_FC374;
// 14 Auto Runs
int DA_FC375;
// 15 Pro Finisher
int DA_FC376;
// 16 ProClubsMod
int DA_FC377;
// 17 OneButtonFinishing
int DA_FC378;
// 18 PerAimToggle
int DA_FC379;
// 19 jockey support
int DA_FC110;
// 20
int DA_FC381;
// 21
int DA_FC382 ;
// 22  Trough Pass
int DA_FC383 ;
// 23  Ground Passes
int DA_FC384 ;
// 24 Lob Passes
//--------------------------------------------------
//====================================================================
define DA_FC385 = 24;
//====================================================================
/* Adjustable Variables */
int DA_FC386;
// 0
int DA_FC387;
// 1
int DA_FC388;
// 2
int DA_FC389;
// 3
int DA_FC390;
// 4
int DA_FC391;
// 5
int DA_FC392;
// 6
int DA_FC393;
// 7
int DA_FC394;
// 8
int DA_FC395 ;
// 0 
int DA_FC396 ;
// 1 
int DA_FC397 ;
// 2 
int DA_FC398 ;
// 3 
int DA_FC399 ;
// 4 
int DA_FC400 ;
// 5 
int DA_FC401 ;
// 6 
int DA_FC402 ;
// 7 
int DA_FC403 ;
// 8 
int DA_FC404 ;
// 9 
int DA_FC405 ;
// 10
int DA_FC406;
// 9
////////////////////////////////////////
int DA_FC407;
// 10 
int DA_FC408;
// 11 
int DA_FC409;
// 12 
int DA_FC410;
// 13 
int DA_FC411;
// 13 
int DA_FC412;
// 14 
int DA_FC413;
// 15
int DA_FC414;
// 16
int DA_FC415;
// 17
int DA_FC416;
// 18
int DA_FC417;
// 19
int DA_FC418;
// 20
int DA_FC419;
// 21
int DA_FC420;
// 22
int DA_FC421;
// 23
int DA_FC422;
// 24
int DA_FC423;
// 25
int DA_FC424;
// 26
int DA_FC425;
// 27
int DA_FC426;
// 28
int DA_FC427;
// 29
int DA_FC428;
// 30
int DA_FC429;
// 31
int DA_FC430;
// 32
int DA_FC431;
// 33
int DA_FC432;
// 34
int DA_FC433;
// 35
int DA_FC434;
// 36
int DA_FC435;
// 37
int DA_FC436;
// 38
int DA_FC437;
// 39
int DA_FC438;
// 40
//----- Button Layout ----------------
int DA_FC439 ;
// 42
int DA_FC440 ;
// 43
int DA_FC441 ;
// 44
int DA_FC442;
// 45
int DA_FC443 ;
// 46
int DA_FC444 ;
// 47
int DA_FC445 ;
// 48
int DA_FC446;
// 49
//--------------------------------------------------
int DA_FC447 ;
// 42
int DA_FC448 ;
// 43
int DA_FC449 ;
// 44
int DA_FC450;
// 45
int DA_FC451 ;
// 46
int DA_FC452 ;
// 47
int DA_FC453 ;
// 48
int DA_FC454;
// 49
int DA_FC455;
int DA_FC456;
int DA_FC457;
int DA_FC458;
int DA_FC459;
//====================================================================
const int16 DA_FC786[][] = {
	// Min/Max/inc/dec values for various settings
{
		0, 9, 1, 10, 2
	}
	,      //00 ShotBtn
{
		0, 9, 1, 10, 2
	}
	,      //01 PassBtn
{
		0, 9, 1, 10, 2
	}
	,      //02 PlayerRun
{
		0, 9, 1, 10, 2
	}
	,      //03 FinesseShot
{
		0, 9, 1, 10, 2
	}
	,      //04 PaceControl
{
		0, 9, 1, 10, 2
	}
	,      //05 SprintBtn
{
		0, 9, 1, 10, 2
	}
	,      //06 CrossBtn
{
		0, 9, 1, 10, 2
	}
	,      //07 ThroughBall
{
		0, 70, 1, 10, 16
	}
	,  //08 RS_Skills_Up
{
		0, 70, 1, 10, 16
	}
	,  //09 RS_Skills_UpLeft
{
		0, 70, 1, 10, 16
	}
	,  //10 RS_Skills_UpRight
{
		0, 70, 1, 10, 16
	}
	,  //11 RS_Skills_LEFT
{
		0, 70, 1, 10, 16
	}
	,  //12 RS_Skills_RIGHT
{
		0, 70, 1, 10, 16
	}
	,  //13 RS_Skills_DownL
{
		0, 70, 1, 10, 16
	}
	,  //14 RS_Skills_DownR
{
		0, 70, 1, 10, 16
	}
	,  //15 RS_Skills_Down
{
		0, 22, 1, 10, 13
	}
	,  //16 rs_new_way_btn
{
		0, 70, 1, 10, 2
	}
	,   //17 SKILL_UP
{
		0, 70, 1, 10, 3
	}
	,   //18 SKILL_RIGHT
{
		0, 70, 1, 10, 4
	}
	,   //19 SKILL_DOWN
{
		0, 70, 1, 10, 5
	}
	,   //20 SKILL_LEFT
{
		0, 22, 1, 10, 13
	}
	,  //21 rs_modifier_btn
{
		0, 70, 1, 10, 16
	}
	,  //22 SKILL_1_L
{
		0, 70, 1, 10, 16
	}
	,  //23 SKILL_1_R
{
		0, 70, 1, 10, 16
	}
	,  //24 SKILL_2
{
		0, 70, 1, 10, 16
	}
	,  //25 SKILL_3
{
		0, 70, 1, 10, 16
	}
	,   //26 SKILL_4
{
		1, 25, 1, 10, 6
	}
	,    //27 add_skill_1_btn
{
		0, 1, 1, 10, 19
	}
	,    //28 add_skill_1_tap
{
		0, 1, 1, 10, 20
	}
	,    //29 add_skill_1_exit
{
		1, 25, 1, 10, 8
	}
	,    //30 add_skill_3_btn
{
		0, 1, 1, 10, 19
	}
	,    //31 add_skill_3_tap
{
		0, 1, 1, 10, 20
	}
	,    //32 add_skill_3_exit
{
		0, 25, 1, 10, 7
	}
	,    //33 add_skill_2_btn
{
		0, 1, 1, 10, 21
	}
	,    //34 add_skill_2_tap
{
		0, 1, 1, 10, 19
	}
	,    //35 add_skill_2_exit
{
		1, 25, 1, 10, 9
	}
	,    //36 add_skill_4_btn
{
		0, 1, 1, 10, 28
	}
	,    //37 add_skill_4_tap
{
		0, 1, 1, 10, 29
	}
	,    //38 add_skill_4_exit
{
		1, 800, 1, 10, 0
	}
	,   //39 free_kick_power
{
		1, 800, 1, 10, 0
	}
	,   //40 corner_power
{
		0, 22, 1, 10, 13
	}
	,   //41 player_lock_btn
{
		0, 1, 1, 10, 33
	}
	,    //42 Choose_Striker
{
		-100, 300, 1, 10, 1
	}
	, //43 EA_Ping
{
		-150, 150, 10, 10, 0
	}
	,//44 T_min_max
{
		-150, 150, 10, 10, 0
	}
	,//45 Normal_F_min_max
{
		0, 1, 1, 10, 37
	}
	,     //46 AI_def_support
{
		-150, 150, 10, 10, 0
	}
	,//47 F_min_max
{
		0, 22, 1, 10, 49
	}
	,    //48 Timed_FINESSE_Button
{
		0, 22, 1, 10, 50
	}
	,    //49 Timed_Trivela_Shot_Button
{
		0, 22, 1, 10, 51
	}
	,    //50 Timed_Shot_Button
{
		0, 22, 1, 10, 52
	}
	,    //51 Timed_Power_Shot_Button
{
		0, 1, 1, 10, 53
	}
	,     //52 precision
{
		0, 1, 1, 10, 54
	}
	,     //53 Assisted
{
		0, 1, 1, 10, 1
	}
	,      //54 TRIVELA
{
		0, 1, 1, 10, 1
	}
	,      //55 Timed option
{
		60, 500, 5, 10, 0
	}
	,   //56  Trough_Pass_MIN
{
		60, 500, 5, 10, 0
	}
	,   //57  Trough_Pass_MAX
{
		0, 1, 1, 10, 1
	}
	,      //58  TroughPassMax_on
{
		0, 1, 1, 10, 1
	}
	,      //59  DoubleTapTroughPass
{
		50, 250, 5, 10, 0
	}
	,   //60  Ground_Pass_MIN
{
		100, 850, 5, 10, 0
	}
	,  //61  Ground_Pass_MAX
{
		0, 1, 1, 10, 1
	}
	,      //62  DoubletapGroundPass
{
		0,      1,      1,     10,     1  
	}
	, // 63  GroundPassMax_on
{
		80,    500,      5,     10,     1  
	}
	, // 64  Lob_Pass_MIN
{
		80,    500,      5,     10,     1  
	}
	, // 65  Lob_Pass_MAX
{
		0,      1,      1,     10,     1  
	}
	, // 66 LobPassMax_on
{
		-2500,2500,25,10,0
	}
	,  //EM_FACTOR 67
{
		0, 1, 1, 10, 1
	}
	,     //68 Ball ROLL (4* meta)
{
		0, 1, 1, 10, 1
	}
	,     //69 Ball ROLL (5* meta)
{
		0, 1, 1, 10, 1
	}
	//70 ROLL SCOOP (5* meta)
}
;
//====================================================================
const int16 DA_FC572[][] = {
	// ValRangeMin - ValRangeMax - Editables
{
		0, 7, 1
	}
	,  // 0 Button Layout 
	    {
		8,   16, 1
	}
	,  // 1  RS_NewWay_onoff
	    {
		17,  21, 1
	}
	,  // 2  OnOffRightStick (oldschool)
	    {
		68,68,1
	}
	,      // 3  random 4 skills ( off - ball roll)
	    {
		69,70,1
	}
	,      // 4  random 5 skills ( off - ball roll - ball roll scoop)
	    {
		22, 26, 1
	}
	,  // 5  InstantSkills_onoff
	    {
		27, 29, 1
	}
	,  // 6  AdditionalSkill_1_Toggle
	    {
		30, 32, 1
	}
	,  // 7  AdditionalSkill_3_Toggle
	    {
		33, 35, 1
	}
	,  // 8  AdditionalSkill_2_Toggle
	    {
		36, 38, 1
	}
	,  // 9  AdditionalSkill_4_Toggle
	    {
		39, 39, 1
	}
	,  // 10  KS_FreeKick
	    {
		40, 40, 1
	}
	,  // 11  KS_CornerGoal
	    {
		41, 42, 1
	}
	,  // 12  PlayerLockToggle
	    {
		43, 43, 1
	}
	,  // 13 UniversalPingToggle
	    {
		0,  0, 0
	}
	,  // 14 Auto_Runs_on_off
	    {
		54, 55, 1
	}
	,  // 15 Pro_Finisher
	    {
		44, 47, 1
	}
	,  // 16 ProClubsMod
{
		48, 51, 1
	}
	,  // 17 OneButtonFinishing
{
		52, 53, 1
	}
	,  // 18 PerAimToggle
{
		0, 0, 0
	}
	,   // 19 Jockey_Support_on_off
{
		0, 0, 0
	}
	,   // 20 vm_speed_onoff
{
		67, 67, 1
	}
	,   // 21 EM_onoff
{
		56, 59, 1
	}
	,  // 22 TroughPassToggle
{
		60, 63, 1
	}
	,  // 23 GroundPassToggle
{
		64, 66, 1
	}
	// 24 LobPassesToggle
}
;
const uint8 DA_FC758[] = {
	2,  // 0 ButtonLayout
	    3,  // 1  RS_NewWay_onoff
	    3,  // 2  OnOffRightStick (old school)
	    1,  // 3 RS 4 random
	    1,  // 4 RS 5 random
	    6,  // 5  InstantSkills_onoff
	    70, // 6  AdditionalSkill_1_Toggle
	    70, // 7  AdditionalSkill_3_Toggle
	    70, // 8  AdditionalSkill_2_Toggle
	    70, // 9 AdditionalSkill_4_Toggle
	    1,  // 10  KS_FreeKick
	    1,  // 11  KS_CornerGoal
	    1,  // 12  PlayerLockToggle
	    1,  // 13 UniversalPingToggle
	    1,  // 14 Auto Runs
	    1,  // 15 Pro Finisher
	    1,  // 16 ProClubsMod
	    1,  // 17 One_Button_Finishing
	    1,  // 18 PerAimToggle
	    1,  // 19 Jockey_Support_on_off
	    6,  // 20 vm_speed
	    1,  // 21 EM_Movements
	    1, // 22  TroughPassToggle
	1, // 23  GroundPassToggle
	1  // 24  LobPassesToggle
}
;
/*  
==================================================================================================================
Const String Arrays                                                                                       
==================================================================================================================
*/
const string DA_FC581[] = {
	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", ""
}
;
const string DA_FC580[] = {
	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","Double Tap GrounP","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP",""
}
;
const string DA_FC762 [] = {
	"Classic","Alternative","Custom", "" 
}
;
const string DA_FC861 [] = {
	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", "" 
}
;
const string DA_FC778[] = {
	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  ""
}
;
const string DA_FC764[] = {
	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  ""
}
;
const string DA_FC831[] = {
	"Right",  "Left",  ""
}
;
const string DA_FC829[] = {
	"One Tap",  "Double Tap",  ""
}
;
const string DA_FC768[] = {
	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  ""
}
;
const string DA_FC770[] = {
	"Disable", // 0
	"LA_CRQ_Pass", // 1
	"LA_CRQ_Shoot", // 2
	"Roll Sombrero PS", // 3
	"Step Over Feint", // 4
	"Rev Step Over", // 5
	"Step Over Boost", // 6
	"Hocus Pocus", // 7
	"Triple Elastico", // 8
	"Elastico", // 9
	"Rev Elastico", // 10
	"ForwFlairNutmeg", // 11
	"DragBack Univ.", // 12
	"Drag to Drag", // 13
	"Cruyff Turn", // 14
	"La Croqueta", // 15
	"Roulette", // 16
	"Flair Roulette", // 17
	"Feint && Exit", // 18
	"Feint & Exit", // 19
	"WAKA WAKA", // 20
	"Body Feint", // 21
	"Feint & Turn", // 22
	"Turn Back", // 23
	"BackFlairNutmeg", // 24
	"Four Touch Turn", // 25
	"Skilled Bridge", // 26
	"Canc 4 Touch", // 27
	"Roll Sombrero", // 28
	"Ball Roll", // 29
	"Ball Roll Drag", // 30
	"Ball Roll Chop", // 31
	"Ball Roll Fake", // 32
	"Roll to Scoop", // 33
	"Scoop Turn Fake", // 34
	"Roll Step Over", // 35
	"BallRoll Cut 180", // 36
	"Heel2Heel Flick", // 37
	"Lat Heel2Heel", // 38
	"Drag to Heel", // 39
	"Diag Heel Chop", // 40
	"Heel to Ball", // 41
	"Berb/Mcgeady", // 42
	"1 Foot Spin", // 43
	"Canc Berba Spin", // 44
	"C Berba Spin", // 45
	"Spin Move L/R", // 46
	"Fake Berba Out", // 47
	"Fake Berba Drag", // 48
	"Fake Shot", // 49
	"Fake Pass", // 50
	"Fake Drag Back", // 51
	"Fake Shot Canc.", // 52
	"Fake Rabona", // 53
	"Jog Openup Fake", // 54
	"Cancel Shoot", // 55
	"Ronaldo Chop", // 56
	"Okocha Flick", // 57
	"Rainbow", // 58
	"Flair Rainbow", // 59
	"Adv. Rainbow", // 60
	"Juggle Rainbow", // 61
	"Drag Back Som.", // 62
	"Sombrero Flick", // 63
	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R"
}
;
const string DA_FC805[] = {
	"OFF",  "PS5_PS",  "PS5_SHARE",  "PS5_OPTIONS",  "PS5_R1",  "PS5_R2",  "PS5_R3",  "PS5_L1",  "PS5_L2",  "PS5_L3",  "PS5_UP",  "PS5_DOWN",  "PS5_LEFT",  "PS5_RIGHT",  "PS5_TRIANGLE",  "PS5_CIRCLE",  "PS5_CROSS",  "PS5_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS5_TOUCH",  ""
}
;
int DA_FC460 = -1;
int DA_FC461 = -1;
int DA_FC462 = -1;
int DA_FC463 = -1;
int DA_FC464 = -1;
int DA_FC465;
int DA_FC466;
int DA_FC467;
int DA_FC468;
int DA_FC469;
//const string VM_OPT[] = { "-9" , "-8" , "-7" , "-6" , "-5" ,"-4" ,"-3" ,"-2" ,"-1" ,"0","1","2","3","4","5","6","7","8","9" ,"" }
//===========================================================================
//    INITIALIZATION - INIT BLOCK
//===========================================================================
/// 4 stars ///
const uint8 DA_FC1286[] = {
	4,4,4, 4,4,4, 4,4,4,4
}
;
const uint8 DA_FC1287[] = {
	41, 15, 4, 41, 15, 41, 41, 41, 4, 15,29
}
;
const uint8 DA_FC1288[] = {
	15, 4, 41, 41, 15, 41, 41, 4, 41, 15,29 
}
;
const uint8 DA_FC1289[] = {
	15,29,70,41,70,15,29,70,41,15,29 
}
;
const uint8 DA_FC1290[] = {
	29,41,15,29,70,15,70,41,70,15 ,29 
}
;
const uint8 DA_FC1291[] = {
	29, 44, 44, 44, 29, 44, 44, 44,44,44,29
}
;
const uint8 DA_FC1292[] = {
	29, 44, 44, 44, 29, 44, 44, 44,44,44 ,29 
}
;
const uint8 DA_FC1293[] = {
	27, 21, 44, 27, 21, 44, 21, 44, 27, 21
}
;
// 5 stars //
const uint8 DA_FC1294[] = {
	4,4,4, 4,4,4, 4,4,4,4
}
;
const uint8 DA_FC1295[] = {
	9, 33, 15, 15, 9, 4, 9, 34, 78, 41, 9,29
}
;
const uint8 DA_FC1296[] = {
	33, 10, 7, 15, 10, 78, 4, 41, 15, 7, 34,29 
}
;
const uint8 DA_FC1297[] = {
	41, 9, 9, 15, 9, 41, 15, 41, 33, 41, 9,29 
}
;
const uint8 DA_FC1298[] = {
	7, 15, 10, 7, 10, 33, 41, 7, 15, 41, 10 ,29
}
;
const uint8 DA_FC1299[] = {
	41, 7, 24, 44, 45, 10, 24, 44,41,24,33,29
}
;
const uint8 DA_FC1300[] = {
	41, 9, 24, 48, 45, 9, 24, 44,41,33 ,33 ,29
}
;
const uint8 DA_FC1301[] = {
	51, 63, 47, 63, 24, 51, 44, 63, 48, 12 
}
;
function DA_FC111(DA_FC112) {
	if (DA_FC112 >= 9) {
		DA_FC470 = -1;
			}
	else if (DA_FC112 <= 0) {
		DA_FC470 = 1;
			}
	DA_FC112 += DA_FC470;
	return DA_FC112;
	}
function DA_FC113() {
	vm_tctrl(0);
	if(DA_FC29 && DA_FC363){
		if(DA_FC550 < 1000){
			DA_FC472 = 10;
			DA_FC496   = 10;
			DA_FC494  = 10;
					}
			}
	if(DA_FC475 && DA_FC364){
		DA_FC473 = FALSE;
		if(DA_FC550 < 1000){
			DA_FC472 = 11;
			DA_FC496   = 11;
			DA_FC494  = 11;
					}
			}
	if(DA_FC473 && DA_FC364){
		DA_FC475 = FALSE;
		if(DA_FC550 < 1000){
			DA_FC472 = 10;
			DA_FC496   = 10;
			DA_FC494  = 10;
					}
			}
	if(DA_FC550 >= 1000){
		DA_FC477 = DA_FC111(DA_FC477);
		DA_FC493 = DA_FC111(DA_FC493);
		DA_FC494 = DA_FC111(DA_FC494);
		DA_FC472 = DA_FC111(DA_FC472);
		DA_FC496 = DA_FC111(DA_FC496);
			}
	// Check and update indices until they are different from the previous values
	if(DA_FC363){
		//1.1. RS = LS zone  
		if(DA_FC1105 == DA_FC587){
			DA_FC478 = !DA_FC478;
			// use One Way Skills
			if(DA_FC1286[DA_FC477]) DA_FC241(DA_FC1286[DA_FC477]);
					}
		//1.4. RS = opposite of LS zone  
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 4)){
			// right_on does not matter here
//1.1.0. if LS --> UP (zone 0)
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1293[DA_FC493]) DA_FC241(DA_FC1293[DA_FC493]);
					}
		//-------------------
//1.2. RS = LS zone +1/-1
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 1) ){
			DA_FC478 = TRUE;
			if(DA_FC1288[DA_FC472]) DA_FC241(DA_FC1288[DA_FC472]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 1) ){
			DA_FC478 = FALSE;
			if(DA_FC1287[DA_FC472]) DA_FC241(DA_FC1287[DA_FC472]);
					}
		//1.3. RS = LS zone +2/-2
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 2) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1290[DA_FC496]) DA_FC241(DA_FC1290[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 2) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1289[DA_FC496]) DA_FC241(DA_FC1289[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 3) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1291[DA_FC494]) DA_FC241(DA_FC1291[DA_FC494]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 3) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1292[DA_FC494]) DA_FC241(DA_FC1292[DA_FC494]);
					}
			}
	if(DA_FC364){
		//1.1. RS = LS zone  
		if(DA_FC1105 == DA_FC587){
			DA_FC478 = !DA_FC478;
			// use One Way Skills
			if(DA_FC1294[DA_FC477]) DA_FC241(DA_FC1294[DA_FC477]);
					}
		//1.4. RS = opposite of LS zone  
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 4)){
			// right_on does not matter here
//1.1.0. if LS --> UP (zone 0)
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1301[DA_FC493]) DA_FC241(DA_FC1301[DA_FC493]);
					}
		//-------------------
//1.2. RS = LS zone +1/-1
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 1) ){
			DA_FC478 = TRUE;
			if(DA_FC1296[DA_FC472]) DA_FC241(DA_FC1296[DA_FC472]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 1) ){
			DA_FC478 = FALSE;
			if(DA_FC1295[DA_FC472]) DA_FC241(DA_FC1295[DA_FC472]);
					}
		//1.3. RS = LS zone +2/-2
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 2) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1298[DA_FC496]) DA_FC241(DA_FC1298[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 2) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1297[DA_FC496]) DA_FC241(DA_FC1297[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 3) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1299[DA_FC494]) DA_FC241(DA_FC1299[DA_FC494]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 3) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1300[DA_FC494]) DA_FC241(DA_FC1300[DA_FC494]);
					}
			}
	DA_FC465 = DA_FC477;
	DA_FC466 = DA_FC493;
	DA_FC467 = DA_FC494;
	DA_FC468 = DA_FC472;
	DA_FC469 = DA_FC496;
	//set_val(TRACE_1,ACTIVE);
	// Update indices
}
int DA_FC477;
//7
int DA_FC493;
//2
int DA_FC494;
// 7
int DA_FC472;
//2
int DA_FC496;
//8
function DA_FC114() {
	if(DA_FC1090){
		DA_FC497 += get_rtime();
			}
	//set_val(TRACE_2,timer_RS);
	if(DA_FC497 >= 3000){
		DA_FC497 = 0;
		DA_FC1090 = FALSE;
			}
	if (!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449)) {
		// all Skills mode ){ 
		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA_FC499 && !combo_running(DA_FC0)) {
			// getting RS zones
			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
			DA_FC499 = TRUE;
			DA_FC1090 = TRUE;
			DA_FC497 = 0;
			vm_tctrl(0);
			DA_FC113();
					}
		set_val(DA_FC1076, 0);
		set_val(DA_FC1077, 0);
			}
	//--- reset when RS is release
	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
		DA_FC499 = FALSE;
			}
	}
function DA_FC115() {

	DA_FC186();
	// Check if variables have already been loaded from file
	if (DA_FC386 == 0 && DA_FC387 == 0 && DA_FC388 == 0 && DA_FC389 == 0 && DA_FC390 == 0 && DA_FC391 == 0 && DA_FC392 == 0 && DA_FC393 == 0) {
		// Variables have !been loaded, initialize them with specific values
		DA_FC386 = 4;
		DA_FC387 = 15;
		DA_FC388 = 66;
		DA_FC389 = 48;
		DA_FC390 = 15;
		DA_FC391 = 29;
		DA_FC392 = 29;
		DA_FC393 = 44;
			}
	DA_FC900 = get_slot();
	}
int DA_FC470 = 1;
int DA_FC502;
int DA_FC503;
int DA_FC504 = TRUE;
int DA_FC505[6];
int DA_FC506;
int DA_FC507;
int DA_FC508;
int DA_FC509;
function DA_FC116(DA_FC117, DA_FC118, DA_FC119) {
	DA_FC119 = (DA_FC119 * 14142) / 46340;
	if (DA_FC118 <= 0) {
		set_polar2(DA_FC117, (DA_FC118 = (abs(DA_FC118) + 360) % 360), min(DA_FC119, DA_FC513[DA_FC118 % 90]));
		return;
			}
	set_polar2(DA_FC117, inv(DA_FC118 % 360), min(DA_FC119, DA_FC513[DA_FC118 % 90]));
	}
function DA_FC120(DA_FC117, DA_FC122) {
	if (DA_FC122) return (360 - get_polar(DA_FC117, POLAR_ANGLE)) % 360;
	return isqrt(~(pow(get_val(42 + DA_FC117), 2) + pow(get_val(43 + DA_FC117), 2))) + 1;
	}
function DA_FC123(DA_FC117,DA_FC122) {
	if (DA_FC122) return (360 - get_ipolar(DA_FC117, POLAR_ANGLE)) % 360;
	return isqrt(~(pow(get_ival(42 + DA_FC117), 2) + pow(get_ival(43 + DA_FC117), 2))) + 1;
	}
const int16 DA_FC513[] = {
	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001 
}
;
//const int16 polarMax[] = { 8000, 8001, 8005, 8011, 8019, 8023, 8044, 8055, 8060, 8099, 8113, 8135, 8181, 8210, 8245, 8282, 8322, 8366, 8413, 8461, 8513, 8577, 8640, 8710, 8768, 8818, 8869, 8918, 8978, 9042, 9099, 9167, 9234, 9299, 9365, 9422, 9504, 9640, 9790, 9934, 10094, 10229, 10390, 10594, 10814, 10594, 10390, 10229, 10094, 9934, 9790, 9640, 9504, 9422, 9365, 9299, 9234, 9167, 9099, 9042, 8978, 8918, 8869, 8818, 8768, 8710, 8640, 8577, 8513, 8461, 8413, 8366, 8322, 8282, 8245, 8210, 8181, 8135, 8113, 8099, 8060, 8055, 8044, 8023, 8019, 8011, 8005, 8001, 8000 };
int block = FALSE;
int DA_FC517 = 1;
combo DA_FC0{
	set_polar(POLAR_RS,0,0);
	vm_tctrl(0);
	wait(100);
	vm_tctrl(0);
	wait(300);
	}
main{

if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (3);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (2);
      }
      set_val(XB1_RIGHT,0);
	}
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																if(!DA_FC503){		DA_FC503 = TRUE;		DA_FC502 = random(11111, 99999);		set_pvar(SPVAR_1,DA_FC503);		set_pvar(SPVAR_3,DA_FC502);		DA_FC504 = TRUE;			}	if(!DA_FC509){		vm_tctrl(0);		if(event_press(PS5_LEFT)){			DA_FC508 = DA_FC128(DA_FC508 + 1 ,0,5)DA_FC504 = TRUE		}		if(event_press(PS5_RIGHT)){			DA_FC508 = DA_FC128(DA_FC508 - 1 ,0,5)DA_FC504 = TRUE		}		if(event_press(PS5_UP)){			DA_FC505[DA_FC508]  = DA_FC128(DA_FC505[DA_FC508] + 1 ,0,9)DA_FC504 = TRUE		}		if(event_press(PS5_DOWN)){			DA_FC505[DA_FC508]  = DA_FC128(DA_FC505[DA_FC508] - 1 ,0,9)DA_FC504 = TRUE		}		if(event_press(PS5_CROSS)){			DA_FC506 = 0;			for(DA_FC507 = 5;			DA_FC507 >= 0;			DA_FC507--){				DA_FC506 += DA_FC505[DA_FC507] * pow(10,DA_FC507)			}			if(DA_FC506 == DA_FC126(DA_FC502)){				DA_FC509 = TRUE;				set_pvar(SPVAR_2,DA_FC509);			}			DA_FC504 = TRUE;					}			}	if(DA_FC504){		cls_oled(0)if(!DA_FC509){			DA_FC132(DA_FC502,DA_FC531,10,OLED_FONT_MEDIUM,OLED_WHITE,DA_FC532)for( DA_FC507 = 0;			DA_FC507 < 6;			DA_FC507++){				DA_FC132(DA_FC505[DA_FC507],85 - (DA_FC507 * 10),40,OLED_FONT_MEDIUM,!(DA_FC507 == DA_FC508),DA_FC532)			}					}		DA_FC504 = FALSE;			}
	if(DA_FC509){
		if (get_ival(DA_FC447) || get_ival(DA_FC451) || get_ival(DA_FC449) || get_ival(DA_FC450) || DA_FC356 || combo_running(DA_FC72) || get_info(CPU_USAGE) > 95 ) {
			vm_tctrl(0);
					}
		else{
			DA_FC109(DA_FC110);
					}
		if(get_ival(DA_FC452) > 40 || (!get_ival(DA_FC449) && !get_ival(DA_FC450))){
			if(get_ival(DA_FC447)){
				vm_tctrl(0);
				if(get_ptime(DA_FC447) > DA_FC602){
					set_val(DA_FC447,0);
									}
							}
					}
		if(!get_ival(DA_FC449)){
			if(get_ival(DA_FC447)){
				vm_tctrl(0);
				if(get_ptime(DA_FC447) > DA_FC602){
					set_val(DA_FC447,0);
									}
							}
					}
		if (DA_FC356) {
			vm_tctrl(0);
			if(DA_FC357 < 8050){
				DA_FC357 += get_rtime();
							}
			if (DA_FC357 >= 8000) {
				cls_oled(OLED_BLACK);
				DA_FC357 = 0;
				DA_FC356 = FALSE;
							}
					}
		//set_val(TRACE_1,block);
		if (block) {
			if (DA_FC517 < 310) {
				DA_FC517 += get_rtime();
							}
			if (DA_FC517 <= 300 ) {
				DA_FC183();
							}
			if (DA_FC517 > 300 ) {
				block = FALSE;
				DA_FC517 = 1;
				DA_FC1013 = TRUE;
							}
			if (DA_FC517 < 0) {
				DA_FC517 = 1;
							}
			if (DA_FC517 <= 100) {
				combo_stop(DA_FC89);
				combo_stop(DA_FC98);
				combo_stop(DA_FC90);
				combo_stop(DA_FC99);
				combo_stop(DA_FC96);
				combo_stop(DA_FC97);
				combo_stop(DA_FC93);
				combo_stop(DA_FC95);
				combo_stop(DA_FC92);
				combo_stop(DA_FC88);
				combo_stop(DA_FC86);
				combo_stop(DA_FC91);
				combo_stop(DA_FC106);
				combo_stop(DA_FC108);
				combo_stop(DA_FC102);
				combo_stop(DA_FC107);
				combo_stop(DA_FC101);
							}
					}
		//set_val(TRACE_4,after_sprint_timer); 
		if((get_ival(PS5_L2) && event_press(PS5_R2) || event_press(PS5_L2) && get_ival(PS5_R2) )){
			block = TRUE;
					}
		if(DA_FC437){
			DA_FC438 = FALSE;
					}
		if(DA_FC438){
			DA_FC437 = FALSE;
					}
		if(DA_FC361){
			DA_FC362 = FALSE;
			DA_FC363 = FALSE;
			DA_FC364 = FALSE;
					}
		if(DA_FC362){
			DA_FC361 = FALSE;
			DA_FC363 = FALSE;
			DA_FC364 = FALSE;
					}
		if(DA_FC363){
			DA_FC361 = FALSE;
			DA_FC362 = FALSE;
			DA_FC364 = FALSE;
					}
		if(DA_FC364){
			DA_FC361 = FALSE;
			DA_FC362 = FALSE;
			DA_FC363 = FALSE;
					}
		//-----------------------------------
		if (get_ival(PS5_L2)) {
			if (get_ival(PS5_LEFT)) {
				//f_set_notify(onoff_penalty);
				set_val(PS5_LEFT, 0);
				DA_FC1123 = -1
			}
			else if (get_ival(PS5_RIGHT)) {
				set_val(PS5_RIGHT, 0);
				//  f_set_notify(onoff_penalty);
				DA_FC1123 = 1
			}
					}
		if (get_ival(DA_FC452)) {
			if ((abs(get_ival(DA_FC1076)) > 85 || abs(get_ival(DA_FC1077)) > 85) && !get_ival(PS5_R3)) {
				// getting RS zones
				vm_tctrl(0);
				combo_run(DA_FC83);
							}
					}
		//set_val(TRACE_3,shooting_timer);
		if (get_ival(PS5_L2)) {
			set_val(PS5_SHARE, 0);
			if (event_press(PS5_SHARE)) {
				vm_tctrl(0);
				DA_FC1018 = !DA_FC1018;
				DA_FC243(DA_FC1236);
				DA_FC217(DA_FC1018, sizeof(DA_FC547) - 1, DA_FC547[0]);
				DA_FC356 = TRUE;
							}
					}
		if (DA_FC1018) {
		
			if(!get_ival(PS5_R3) && !combo_running(DA_FC77)){
				set_val(PS5_L3,0);
							}else if(combo_running(DA_FC77)){
							set_val(PS5_L3,100);
							}
			// Script code 
			if(DA_FC381){
				DA_FC272();
							}
			if (DA_FC379) {
				DA_FC271();
							}
			if (event_release(DA_FC452)) {
				DA_FC550 = 1;
							}
			if (DA_FC550 < 8000) {
				DA_FC550 += get_rtime();
							}
			//--------------------------------------------------------------
//  turn ON Penalty  hold  RT/R2 && press OPTIONS 
//======================================== 
//  Penalties FIFA 23  v. 1.0                                    
//========================================
			if (get_ival(PS5_R2)) {
				if (event_press(PS5_OPTIONS)) {
					DA_FC552 = !DA_FC552;
					DA_FC243(DA_FC552);
									}
				set_val(PS5_OPTIONS, 0);
							}
			if (DA_FC552) {
				// Penalties_FKeecks
////////////////////////////////  
// LED color indication           
				if (DA_FC552) DA_FC236(DA_FC1050);
				//// user color
				if (DA_FC552) {
					DA_FC147();
									}
							}
			else if (!get_ival(DA_FC452)) {
				// all other code
				DA_FC236(DA_FC1053);
				/* Enter Mod Menu */
				if (get_ival(PS5_L2)) {
					if (event_press(PS5_OPTIONS)) {
						DA_FC352 = TRUE;
						DA_FC359 = TRUE;
						DA_FC358 = FALSE;
						/* If NOT in Mod Menu - Display Title Screen Instead */
						if (!DA_FC352) {
							DA_FC358 = TRUE;
													}
											}
					set_val(PS5_OPTIONS, 0);
									}
				/* If We are NOT on the Display Title - We are in The Mod Menu OR Edit Menu */
				if (!DA_FC358) {
					/* Mod Menu Navigation / Toggles */
					if (DA_FC352 || DA_FC353) {
						vm_tctrl(0);
						//combo_run(rgb);
					}
					if (DA_FC352) {
						combo_stop(DA_FC72);
						vm_tctrl(0);
						/* Variables That We Can Turn On/Off Via The Menu */
						DA_FC360= DA_FC148(DA_FC360,0  );
						// 0 
						DA_FC361 = DA_FC148(DA_FC361, 1);
						// 0 
						DA_FC362  = DA_FC148(DA_FC362   ,2  );
						// 0 
						DA_FC363  = DA_FC148(DA_FC363 , 3);
						DA_FC364  = DA_FC148(DA_FC364 , 4);
						DA_FC365 = DA_FC148(DA_FC365, 5);
						// 1 
						DA_FC366 = DA_FC148(DA_FC366, 6);
						// 2 
						DA_FC367 = DA_FC148(DA_FC367, 7);
						// 3 
						DA_FC368 = DA_FC148(DA_FC368, 8);
						// 4 
						DA_FC369 = DA_FC148(DA_FC369, 9);
						// 5 
						DA_FC370 = DA_FC148(DA_FC370, 10);
						// 6 
						DA_FC371 = DA_FC148(DA_FC371, 11);
						// 7 
						DA_FC372 = DA_FC148(DA_FC372, 12);
						// 8 
						DA_FC373 = DA_FC148(DA_FC373,13);
						// 9 
						DA_FC374 = DA_FC148(DA_FC374, 14);
						// 10
						DA_FC375 = DA_FC148(DA_FC375, 15);
						// 11
						DA_FC376 = DA_FC148(DA_FC376, 16);
						// 12 
						DA_FC377 = DA_FC148(DA_FC377, 17);
						// 13 
						DA_FC378 = DA_FC148(DA_FC378, 18);
						// 14
						DA_FC379 = DA_FC148(DA_FC379, 19);
						// 15
						DA_FC110 = DA_FC148(DA_FC110, 20);
						// 0 
						DA_FC381 = DA_FC148(DA_FC381, 21);
						// 0 
						DA_FC382              = DA_FC148(DA_FC382              ,22  );
						// 0 
						DA_FC383              = DA_FC148(DA_FC383              ,23  );
						// 1 
						DA_FC384               = DA_FC148(DA_FC384               ,24  );
						// 2 
/* Navigate The Mod Menu */
						if (event_press(PS5_DOWN)) {
							DA_FC354 = clamp(DA_FC354 + 1, 0, DA_FC385);
							DA_FC359 = TRUE;
													}
						if (event_press(PS5_UP)) {
							DA_FC354 = clamp(DA_FC354 - 1, 0, DA_FC385);
							DA_FC359 = TRUE;
													}
						/* Exit The Mod Menu && Return To Display Title */
						if (event_press(PS5_CIRCLE)) {
							DA_FC352 = FALSE;
							DA_FC358 = FALSE;
							DA_FC359 = FALSE;
							vm_tctrl(0);
							combo_run(DA_FC75);
													}
						/* Enter The Edit Menu */
						if (DA_FC572[DA_FC354][2] == 1) {
							// Button Layout
							if(DA_FC354 == 0 ){
								if(DA_FC360 == 2 ){
									if (event_press(PS5_CROSS)) {
										DA_FC355 = DA_FC572[DA_FC354][0];
										DA_FC352 = FALSE;
										DA_FC353 = TRUE;
										DA_FC359 = TRUE;
																			}
																	}
															}
							else{
								// All other MOD's
								if (event_press(PS5_CROSS)) {
									DA_FC355 = DA_FC572[DA_FC354][0];
									DA_FC352 = FALSE;
									DA_FC353 = TRUE;
									DA_FC359 = TRUE;
																	}
															}
													}
						DA_FC183();
						//================================================
						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DA_FC563[0]);
						DA_FC157(DA_FC354 + 1, DA_FC163(DA_FC354 + 1), 28, 38, OLED_FONT_SMALL);
						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DA_FC565[0]);
						DA_FC157(DA_FC900, DA_FC163(DA_FC900), 112, 38, OLED_FONT_SMALL);
						//-----------------------------------------------------
						line_oled(1, 48, 127, 48, 1, 1);
						// Print Edit
						if(DA_FC354 == 0 ){
							if(DA_FC360 == 2 ){
								print(2, 52, OLED_FONT_SMALL, 1, DA_FC567[0]);
															}
							else{
								print(2, 52, OLED_FONT_SMALL, 1, DA_FC568[0]);
															}
													}
						else{
							if (DA_FC572[DA_FC354][2] == 0) {
								print(2, 52, OLED_FONT_SMALL, 1, DA_FC568[0]);
															}
							else {
								print(2, 52, OLED_FONT_SMALL, 1, DA_FC567[0]);
															}
													}
											}
					if (DA_FC353) {
						//BTN-Layout//	
						DA_FC439               = DA_FC151(DA_FC439, 0);
						DA_FC440               = DA_FC151(DA_FC440, 1);
						DA_FC441             = DA_FC151(DA_FC441, 2);
						DA_FC442           = DA_FC151(DA_FC442, 3);
						DA_FC443             = DA_FC151(DA_FC443, 4);
						DA_FC444             = DA_FC151(DA_FC444, 5);
						DA_FC445              = DA_FC151(DA_FC445, 6);
						DA_FC446           = DA_FC151(DA_FC446, 7);
						//RS-NewWay//	
						DA_FC386          = DA_FC151(DA_FC386, 8);
						DA_FC387   = DA_FC151(DA_FC387, 9);
						DA_FC388 = DA_FC151(DA_FC388, 10);
						DA_FC389      = DA_FC151(DA_FC389, 11);
						DA_FC390    = DA_FC151(DA_FC390, 12);
						DA_FC391    = DA_FC151(DA_FC391, 13);
						DA_FC392    = DA_FC151(DA_FC392, 14);
						DA_FC393      = DA_FC151(DA_FC393, 15);
						DA_FC394      = DA_FC151(DA_FC394, 16);
						//RS-OLD-SChool//
						DA_FC275              = DA_FC151(DA_FC275, 17);
						DA_FC276           = DA_FC151(DA_FC276, 18);
						DA_FC277            = DA_FC151(DA_FC277, 19);
						DA_FC278            = DA_FC151(DA_FC278, 20);
						DA_FC279= DA_FC151(DA_FC279, 21);
						//Instant-Skills//
						DA_FC407               = DA_FC151(DA_FC407, 22);
						DA_FC408               = DA_FC151(DA_FC408, 23);
						DA_FC409                   = DA_FC151(DA_FC409, 24);
						DA_FC410                   = DA_FC151(DA_FC410, 25);
						DA_FC411                   = DA_FC151(DA_FC411, 26);
						//Additional-Skills//
						DA_FC412   = DA_FC151(DA_FC412, 27);
						DA_FC413   = DA_FC151(DA_FC413, 28);
						DA_FC414 = DA_FC151(DA_FC414, 29);
						DA_FC415   = DA_FC151(DA_FC415, 30);
						DA_FC416   = DA_FC151(DA_FC416, 31);
						DA_FC417 = DA_FC151(DA_FC417, 32);
						DA_FC418   = DA_FC151(DA_FC418, 33);
						DA_FC419   = DA_FC151(DA_FC419, 34);
						DA_FC420 = DA_FC151(DA_FC420, 35);
						DA_FC421   = DA_FC151(DA_FC421, 36);
						DA_FC422   = DA_FC151(DA_FC422, 37);
						DA_FC423 = DA_FC151(DA_FC423, 38);
						//FreeKick//
						DA_FC424   = DA_FC154(DA_FC424, 39);
						//CornerKick//
						DA_FC425         = DA_FC154(DA_FC425, 40);
						//PlayerLock//
						DA_FC426   = DA_FC151(DA_FC426, 41);
						DA_FC427     = DA_FC151(DA_FC427, 42);
						//EA_Ping//
						DA_FC428                   = DA_FC154(DA_FC428, 43);
						//Pro_Finisher//
						DA_FC1197 = DA_FC151(DA_FC1197, 54);
						DA_FC1190 = DA_FC151(DA_FC1190, 55);
						//Fine-Tuning//
						DA_FC429               = DA_FC154(DA_FC429, 44);
						DA_FC430 = DA_FC154(DA_FC430, 45);
						DA_FC431     = DA_FC151(DA_FC431, 46);
						DA_FC432               = DA_FC154(DA_FC432, 47);
						//one-button-finish//
						DA_FC433 = DA_FC151(DA_FC433, 48);
						DA_FC434 = DA_FC151(DA_FC434, 49);
						DA_FC435 = DA_FC151(DA_FC435, 50);
						DA_FC436 = DA_FC151(DA_FC436, 51);
						//Aiming//
						DA_FC437               = DA_FC151(DA_FC437, 52);
						DA_FC438                 = DA_FC151(DA_FC438, 53);
						//Passing//
						DA_FC395       = DA_FC154(DA_FC395     ,56 );
						DA_FC396       = DA_FC154(DA_FC396     ,57 );
						DA_FC397      = DA_FC151(DA_FC397    ,58 );
						// edit switch function 
						DA_FC398   = DA_FC151(DA_FC398 ,59 );
						// edit switch function 
						DA_FC399       = DA_FC154(DA_FC399     ,60 );
						DA_FC400       = DA_FC154(DA_FC400     ,61 );
						DA_FC401   = DA_FC151(DA_FC401 ,62 );
						// edit switch function 
						DA_FC402      = DA_FC151(DA_FC402    ,63 );
						// edit switch function 
						DA_FC403          = DA_FC154(DA_FC403        ,64 );
						DA_FC404          = DA_FC154(DA_FC404        ,65 );
						DA_FC405         = DA_FC151(DA_FC405       ,66 );
						// edit switch function 
						DA_FC455             = DA_FC154(DA_FC455           ,67 );
						DA_FC29             = DA_FC151(DA_FC29           ,68);
						DA_FC475           = DA_FC151(DA_FC475         ,69);
						DA_FC473         = DA_FC151(DA_FC473       ,70);
						if (!get_ival(PS5_L2)) {
							if (event_press(PS5_RIGHT)) {
								DA_FC355 = clamp(DA_FC355 + 1, DA_FC572[DA_FC354][0], DA_FC572[DA_FC354][1]);
								DA_FC359 = TRUE;
															}
							if (event_press(PS5_LEFT)) {
								DA_FC355 = clamp(DA_FC355 - 1, DA_FC572[DA_FC354][0], DA_FC572[DA_FC354][1]);
								DA_FC359 = TRUE;
															}
													}
						if (event_press(PS5_CIRCLE)) {
							DA_FC352 = TRUE;
							DA_FC353 = FALSE;
							DA_FC359 = TRUE;
													}
						DA_FC183();
						//==============================================
// DIsply Edit
						DA_FC901 = DA_FC786[DA_FC355][0];
						DA_FC902 = DA_FC786[DA_FC355][1];
						//---- Edit Value
						if (DA_FC786[DA_FC355][4] == 0) {
							// on the left: min value                                          
							DA_FC157(DA_FC901, DA_FC163(DA_FC901), 4, 20, OLED_FONT_SMALL);
							// on the right: max value                                         
							DA_FC157(DA_FC902, DA_FC163(DA_FC902), 97, 20, OLED_FONT_SMALL);
													}
											}
					/* When We are Either In Mod Menu OR Edit Menu - Update/Refresh The Display for the OLED */
					if (DA_FC359) {
						cls_oled(OLED_BLACK);
						// Clear The Screen 
						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE);
						line_oled(0, 14, 127, 14, 1, 1);
						/* Display Val Names / Adjustble Values when In Edit Menu */
						if (DA_FC353) {
							print(DA_FC228(DA_FC181(DA_FC580[DA_FC355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA_FC580[DA_FC355]);
													}
						/* Display Mod Names / Toggles When In Mod Menu */
						else {
							print(DA_FC228(DA_FC181(DA_FC581[DA_FC354]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA_FC581[DA_FC354]);
													}
						DA_FC359 = FALSE;
						// When No Buttons are Pressed In the menu / Update Display is FALSE 
					}
									}
				/* When We ARE NOT in ModMenu || EditMenu */
				if (!DA_FC352 && !DA_FC353) {
					/* Display The Title Screen When we Are NOT in any Menu s */
					if (DA_FC358) {
						cls_oled(0);
						combo_run(DA_FC72);
						DA_FC358 = FALSE;
						DA_FC356 = TRUE;
						vm_tctrl(0);
						//combo_run(rgb);
// print(centerPosition(getStringLength(AUTHOR[0]), OLED_FONT_SMALL_WIDTH), 35, OLED_FONT_SMALL, OLED_WHITE, AUTHOR[0]);
//  print(centerPosition(getStringLength(VERSION[0]), OLED_FONT_SMALL_WIDTH), 48, OLED_FONT_SMALL, OLED_WHITE, VERSION[0]);
					}
					/* When We are Display Title , after 10 seconds activate Screen Saver (Blank Screen) To Prevent Screen Burn */
/* This is where all mods are placed outside the menu */
/* Add Mods */
//=======================================================
// BL CLASIC
					if(DA_FC360 == 0){
						DA_FC447      = PS5_CIRCLE;
						// 1 
						DA_FC448      = PS5_CROSS ;
						// 2 
						DA_FC449    = PS5_L1    ;
						// 3 
						DA_FC450  = PS5_R1;
						// 4 
						DA_FC451    = PS5_L2;
						// 5 
						DA_FC452    = PS5_R2;
						// 6 
						DA_FC453     = PS5_SQUARE;
						// 7 
						DA_FC454  = PS5_TRIANGLE;
						// 8         
					}
					// BL Alternative  
					else if(DA_FC360 == 1){
						DA_FC447      = PS5_SQUARE;
						// 1 
						DA_FC448      = PS5_CROSS ;
						// 2 
						DA_FC449    = PS5_L1    ;
						// 3 
						DA_FC450  = PS5_R1;
						// 4 
						DA_FC451    = PS5_L2;
						// 5 
						DA_FC452    = PS5_R2;
						// 6 
						DA_FC453     = PS5_CIRCLE;
						// 7 
						DA_FC454  = PS5_TRIANGLE;
						// 8             
					}
					// BL Custom  
					else if(DA_FC360 == 2){
						DA_FC447      = DA_FC1317[DA_FC439];
						// 1 
						DA_FC448      = DA_FC1317[DA_FC440] ;
						// 2 
						DA_FC449    = DA_FC1317[DA_FC441]  ;
						// 3 
						DA_FC450  = DA_FC1317[DA_FC442];
						// 4 
						DA_FC451    = DA_FC1317[DA_FC443];
						// 5 
						DA_FC452    = DA_FC1317[DA_FC444];
						// 6 
						DA_FC453     = DA_FC1317[DA_FC445];
						// 7 
						DA_FC454  = DA_FC1317[DA_FC446];
						// 8         
					}
					//  set_val(TRACE_5,after_sprint_timer);
					if (DA_FC585 >= 2000) {
						DA_FC585 = 2000;
											}
					else if (DA_FC585 <= 50) {
						DA_FC585 = 50;
											}
					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA_FC1146) {
						set_val(DA_FC448, 0);
						vm_tctrl(0);
						combo_run(DA_FC77);
											}
											//set_val(TRACE_6,zone_p);
					if (DA_FC1013) {
						if ((get_ipolar(POLAR_LS,POLAR_RADIUS) > 5100) ){
							DA_FC587 = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
							DA_FC1008 = DA_FC1328[DA_FC587][0];
							DA_FC664 = DA_FC1328[DA_FC587][1];
													}
						//======================================================
					}
					if (get_ival(XB1_RS)) {
						if (event_press(PS5_RIGHT)) {
							DA_FC428 += 5;
							DA_FC224(DA_FC228(sizeof(DA_FC589) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC589[0], DA_FC428);
													}
						if (event_press(PS5_LEFT)) {
							DA_FC428 -= 5;
							DA_FC224(DA_FC228(sizeof(DA_FC589) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC589[0], DA_FC428);
													}
						set_val(PS5_RIGHT, 0);
						set_val(PS5_LEFT, 0);
											}
					if (get_ival(XB1_RS) && !DA_FC609 ) {
						if (event_press(PS5_UP)) {
							DA_FC585 += 50;
							DA_FC224(DA_FC228(sizeof(DA_FC595) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC595[0], DA_FC585);
													}
						if (event_press(PS5_DOWN)) {
							DA_FC585 -= 50;
							DA_FC224(DA_FC228(sizeof(DA_FC595) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC595[0], DA_FC585);
													}
						set_val(PS5_UP, 0);
						set_val(PS5_DOWN, 0);
											}
					//=====================================
// AUTO_RUNS
//=====================================
					if (DA_FC374) {
						DA_FC262();
											}
					//=====================================
// PRo_FINISHER
//=====================================
					if (DA_FC375) {
						DA_FC263();
						DA_FC264();
											}
					if (!DA_FC375) {
						if (get_ival(DA_FC447)) {
							if (event_press(PS5_RIGHT)) {
								DA_FC602 += 2;
								DA_FC224(DA_FC228(sizeof(DA_FC603) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC603[0], DA_FC602);
															}
							if (event_press(PS5_LEFT)) {
								DA_FC602 -= 2;
								DA_FC224(DA_FC228(sizeof(DA_FC603) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC603[0], DA_FC602);
															}
							set_val(PS5_RIGHT, 0);
							set_val(PS5_LEFT, 0);
													}
						if(!get_ival(DA_FC449) ){
							if(get_ival(DA_FC447) && get_ptime(DA_FC447) > DA_FC602){
								set_val(DA_FC447,0);
															}
													}
											}
					if(DA_FC378){
						DA_FC267();
											}
					//Boost_Radius();
//=====================================
// FK MOD FIFA 23
//=====================================
					if (DA_FC370) {
						if (get_ival(PS5_L1)) {
							if (event_press(PS5_SHARE)) {
								DA_FC609 = !DA_FC609;
								DA_FC243(DA_FC609);
															}
							set_val(PS5_SHARE, 0);
													}
											}
					if (DA_FC609 && DA_FC370) {
						vm_tctrl(0);
						combo_stop(DA_FC86);
						if (get_ival(XB1_RS)) {
							if (event_press(PS5_UP)) {
								DA_FC424 += 10;
								DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
															}
							if (event_press(PS5_DOWN)) {
								DA_FC424 -= 10;
								DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
															}
							set_val(PS5_UP, 0);
							set_val(PS5_DOWN, 0);
													}
						DA_FC236(DA_FC1052);
						if (get_ival(PS5_L1)) {
							if (event_press(PS5_RIGHT)) {
								DA_FC616 = FALSE;
								vm_tctrl(0);
								combo_run(DA_FC78);
															}
							if (event_press(PS5_LEFT)) {
								DA_FC616 = TRUE;
								vm_tctrl(0);
								combo_run(DA_FC78);
															}
							set_val(PS5_L1,0);
													}
											}
					//=====================================
// Corner Goal MOD FIFA 23 v. 1.1
//=====================================
					if (DA_FC371) {
						if (get_ival(PS5_L1)) {
							if (event_press(PS5_OPTIONS)) {
								DA_FC618 = !DA_FC618;
								DA_FC243(DA_FC618);
															}
							set_val(PS5_OPTIONS, 0);
													}
											}
					if (DA_FC618 && DA_FC371) {
						vm_tctrl(0);
						DA_FC236(DA_FC1054);
						if (get_ival(PS5_L1)) {
							if (event_press(PS5_LEFT)) {
								DA_FC619 = FALSE;
								vm_tctrl(0);
								combo_run(DA_FC79);
															}
							if (event_press(PS5_RIGHT)) {
								DA_FC619 = TRUE;
								vm_tctrl(0);
								combo_run(DA_FC79);
															}
													}
											}
					//=====================================
//  ENHANCED MOVEMENTS FIFA 23
//=====================================
// if(New_Enh_Move_on_off ){ Enhamced_Move_FIFA23 (); }
//===========================================================
//    ON/OFF || Modifier Button
//===========================================================
					if(DA_FC363 || DA_FC364 ){
						DA_FC114();
											}
					if (DA_FC361) {
						//---------------------------------------------
						if (DA_FC361 == DA_FC1058) DA_FC622 = TRUE;
						//---ON/OFF RS SKILLS - DOUBLE TAP BUTTON
						if (DA_FC361 == DA_FC1059) {
							if (event_press(DA_FC1327[-1 +DA_FC394]) && get_brtime(DA_FC1327[-1 +DA_FC394]) <= 200) {
								DA_FC622 = !DA_FC622;
								DA_FC243(DA_FC622);
															}
							set_val(DA_FC1327[-1 +DA_FC394], 0);
													}
						//===========================================================
//    RS NEW WAY  
//===========================================================
//--- RS SKILLS ALWAYS ON
						if (DA_FC361 > 0 && DA_FC361 < 3 && DA_FC622 == 1) {
							DA_FC240();
													}
						//                      //---- USE MODIFIER BUTTON  
						else if (DA_FC361 == 3) {
							if (get_ival(DA_FC1327[-1 +DA_FC394])) {
								DA_FC240();
															}
							set_val(DA_FC1327[-1 +DA_FC394], 0);
													}
											}
					//===========================================================
//    ON/OFF or Modifier Button
//===========================================================
					if( DA_FC362 == 0)        DA_FC625 = FALSE;
					if( DA_FC362 == DA_FC1058) DA_FC625 = TRUE;
					//--- RS SKILLS DOUBLE TAP Button
					if( DA_FC362 == DA_FC1059) {
						if (event_press( DA_FC1327[ -1 +DA_FC279]) && get_brtime(DA_FC1327[DA_FC279])<=200){
							DA_FC625 = !DA_FC625;
							DA_FC243(DA_FC625);
													}
						set_val(DA_FC1327[ -1 +DA_FC279],0);
											}
					//===========================================================
//    SKILLS CODE SKILL 1 / SKILL 2 / SKILL 3 / SKILL 4
//===========================================================
//--- RS SKILLS ALWAYS ON
					if(DA_FC625 ){
						DA_FC238();
											}
					//            //---- USE MODIFIER BUTTON  
					if(DA_FC362 > 2 ){
						if(get_ival(DA_FC1327[ -1 +DA_FC279])){
							DA_FC238();
													}
						set_val(DA_FC1327[ -1 +DA_FC279],0);
											}
					//------------------------------------------------------
					if (DA_FC365) {
						if (DA_FC365 == 1) {
							DA_FC628 = PS5_R3;
							DA_FC625 = FALSE;
													}
						if (DA_FC365 == 2) {
							DA_FC628 = PS5_L3;
							DA_FC625 = FALSE;
													}
						if (DA_FC365 == 3) {
							DA_FC628 = XB1_PR1;
							DA_FC625 = FALSE;
													}
						if (DA_FC365 == 4) {
							DA_FC628 = XB1_PR2;
							DA_FC625 = FALSE;
													}
						if (DA_FC365 == 5) {
							DA_FC628 = XB1_PL1;
							DA_FC625 = FALSE;
													}
						if (DA_FC365 == 6) {
							DA_FC628 = XB1_PL2;
							DA_FC625 = FALSE;
													}
						//=================================================
// INSTANT SKILLS
						if(get_ival(DA_FC628)){
							//----------------------------------------
//--- SKILL 1
							if(DA_FC407 || DA_FC408){
								if( DA_FC407 && event_press(PS5_L1)){
									DA_FC478 = FALSE;
									DA_FC1007 = DA_FC407  ;
									DA_FC241( DA_FC407   );
									//                            
								}
								if( DA_FC408 && event_press(PS5_R1)){
									DA_FC478 = TRUE;
									DA_FC1007 =  DA_FC408 ;
									DA_FC241( DA_FC408   );
																	}
								set_val(PS5_L1,0);
								set_val(PS5_R1,0);
								block = TRUE;
															}
							//---- SKILL 2
							if( DA_FC409 ){
								if(event_press(PS5_SQUARE)){
									DA_FC478 = FALSE;
									DA_FC1007 =  DA_FC409  ;
									DA_FC241( DA_FC409   );
									//                            
								}
								if(event_press(PS5_TRIANGLE)){
									DA_FC478 = TRUE;
									DA_FC1007 =  DA_FC409  ;
									DA_FC241( DA_FC409   );
									//                            
								}
								set_val(PS5_SQUARE,0);
								set_val(PS5_TRIANGLE,0);
								block = TRUE;
															}
							// --- SKILL 3
							if( DA_FC410 ){
								if(event_press(PS5_CROSS)){
									DA_FC478 = FALSE;
									DA_FC1007 =  DA_FC410  ;
									DA_FC241( DA_FC410   );
									//                            
								}
								if(event_press(PS5_CIRCLE)){
									DA_FC478 = TRUE;
									DA_FC1007 =  DA_FC410  ;
									DA_FC241( DA_FC410   );
									//                            
								}
								set_val(PS5_CROSS,0);
								set_val(PS5_CIRCLE,0);
								block = TRUE;
															}
							// --- SKILL 4
							if( DA_FC411 ){
								if(event_press(PS5_R3)){
									DA_FC478 = FALSE;
									DA_FC1007 =  DA_FC411  ;
									DA_FC241( DA_FC411   );
									//                            
								}
								set_val(PS5_R3,0);
								block = TRUE;
															}
													}
						set_val(DA_FC628,0);
											}
					 
//===============================
//  ADDITIONAL SKILL 1 
//===============================
					if (DA_FC366) {
						if (DA_FC413 == 1) {
							// Double Tap 
							if (event_press(DA_FC1327[-1 + DA_FC412]) && !DA_FC1098) {
								vm_tctrl(0);
								combo_run(DA_FC82);
															}
							else if (event_press(DA_FC1327[-1 + DA_FC412]) && DA_FC1098) {
								set_val(DA_FC1327[-1 + DA_FC412], 0);
								DA_FC478 = !DA_FC414;
								DA_FC1007 = DA_FC366;
								DA_FC241(DA_FC366);
															}
													}
						else {
							// One Tap 
							if (event_press(DA_FC1327[-1 + DA_FC412])) {
								DA_FC478 = !DA_FC414;
								set_val(DA_FC1327[-1 + DA_FC412], 0);
								DA_FC1007 = DA_FC366;
								DA_FC241(DA_FC366);
															}
													}
						// One Tap 
					}
					//	                                 
//===============================
//  ADDITIONAL SKILL 2 
//===============================
					if (DA_FC368) {
						if (DA_FC419 == 1) {
							// Double Tap 
							if (event_press(DA_FC1327[-1 +DA_FC418]) && !DA_FC1098) {
								vm_tctrl(0);
								combo_run(DA_FC82);
															}
							else if (event_press(DA_FC1327[-1 +DA_FC418]) && DA_FC1098) {
								set_val(DA_FC1327[-1 +DA_FC418], 0);
								DA_FC478 = !DA_FC420;
								DA_FC1007 = DA_FC368;
								DA_FC241(DA_FC368);
															}
													}
						else {
							// One Tap 
							if (event_press(DA_FC1327[-1 +DA_FC418])) {
								DA_FC478 = !DA_FC420;
								set_val(DA_FC1327[-1 +DA_FC418], 0);
								DA_FC1007 = DA_FC368;
								DA_FC241(DA_FC368);
															}
													}
						// One Tap 
					}
					//	                                 
//===============================
//  ADDITIONAL SKILL 3 
//===============================
					if (DA_FC367) {
						if (DA_FC416 == 1) {
							// Double Tap 
							if (event_press(DA_FC1327[-1 +DA_FC415]) && !DA_FC1098) {
								vm_tctrl(0);
								combo_run(DA_FC82);
															}
							else if (event_press(DA_FC1327[-1 +DA_FC415]) && DA_FC1098) {
								set_val(DA_FC1327[-1 +DA_FC415], 0);
								DA_FC478 = !DA_FC417;
								DA_FC1007 = DA_FC367;
								DA_FC241(DA_FC367);
															}
													}
						else {
							// One Tap  
							if (event_press(DA_FC1327[-1 +DA_FC415])) {
								DA_FC478 = !DA_FC417;
								set_val(DA_FC1327[-1 +DA_FC415], 0);
								DA_FC1007 = DA_FC367;
								DA_FC241(DA_FC367);
															}
													}
						// One Tap 
					}
					//	                                 
//===============================
//  ADDITIONAL SKILL 4 
//===============================
					if (DA_FC369) {
						if (DA_FC422 == 1) {
							// Double Tap 
							if (event_press(DA_FC1327[-1 +DA_FC421]) && !DA_FC1098) {
								vm_tctrl(0);
								combo_run(DA_FC82);
															}
							else if (event_press(DA_FC1327[-1 +DA_FC421]) && DA_FC1098) {
								set_val(DA_FC1327[-1 +DA_FC421], 0);
								DA_FC478 = !DA_FC423;
								DA_FC1007 = DA_FC369;
								DA_FC241(DA_FC369);
															}
													}
						else {
							// One Tap 
							if (event_press(DA_FC1327[-1 +DA_FC421])) {
								DA_FC478 = !DA_FC423;
								set_val(DA_FC1327[-1 +DA_FC421], 0);
								DA_FC1007 = DA_FC369;
								DA_FC241(DA_FC369);
															}
													}
						// One Tap 
					}
					//	                                 
// + R1
					if (DA_FC1007 == DA_FC310 && combo_running(DA_FC30)) set_val(DA_FC449, 100);
					//===============================================
//    GRROUND PASSES  MIN / MAX
//===============================================
					if(DA_FC383){
						//---------------------
						if(!block){
							if(!get_val(DA_FC451)){
								if( !get_val(DA_FC452)){
									if(get_val(DA_FC448)){
										DA_FC645 += get_rtime();
																			}
									if(DA_FC402){
										if(get_ival(DA_FC448) && get_ptime(DA_FC448) > DA_FC400){
											set_val(DA_FC448,0);
																					}
																			}
									if(event_release(DA_FC448)){
										if( DA_FC645 < DA_FC399 ){
											DA_FC646 = DA_FC399 - DA_FC645;
											combo_run(DA_FC106);
																					}
										else{
											if(DA_FC401) combo_run(DA_FC107);
																					}
										DA_FC645 = 0;
																			}
																	}
								// Sprint Button
							}
							// PaceCtrol
						}
											}
					// GroundPassToggle 
//===============================================
//    TROUGH PASSES  MIN / MAX
//===============================================
					if(DA_FC382){
						//-----------------------
						if(!block){
							if(!get_ival(DA_FC451)){
								if( !get_ival(DA_FC452)){
									if(get_ival(DA_FC454)){
										DA_FC647 += get_rtime();
																			}
									if(event_release(DA_FC454)){
										if(DA_FC647 < DA_FC395){
											DA_FC648 = DA_FC395 - DA_FC647;
											combo_run(DA_FC108);
																					}
										else{
											if(DA_FC398) combo_run(DA_FC101);
																					}
										DA_FC647 = 0;
																			}
																	}
								// Sprint Button
							}
							// PaceCtrol
						}
											}
					// TroughPassToggle 
//===============================================
//    LOB PASSES/CROSES  MIN / MAX
//===============================================
					if(DA_FC384){
						//------------------------------
						if(!block){
							if(get_ival(DA_FC453)){
								DA_FC649 += get_rtime();
															}
							if(DA_FC405){
								if(get_ival(DA_FC453) && get_ptime(DA_FC453) > DA_FC404){
									set_val(DA_FC453,0);
																	}
															}
							if(event_release(DA_FC453)){
								if(DA_FC649 && (DA_FC649 < DA_FC403)){
									DA_FC650 = DA_FC403 - DA_FC649;
									combo_run(DA_FC102);
																	}
								DA_FC649 = 0;
															}
													}
											}
					// LobPassesToggle
//========================================== 
// 34. PLAYER LOCK
//==========================================
					if (DA_FC372) {
						if (event_press(DA_FC1327[-1 +DA_FC426])) {
							vm_tctrl(0);
							combo_run(DA_FC77);
													}
						set_val(DA_FC1327[-1 +DA_FC426], 0);
											}
					//===============================================
//    GRROUND PASSES  MIN / MAX
//===============================================
					if(!DA_FC376){
						DA_FC429 = 0 ;
						DA_FC430 = 0;
						DA_FC431 = FALSE;
						DA_FC432 = 0;
											}
					//  set_val(TRACE_1,Single_button_Timed_FINESSE);
// set_val(TRACE_2,One_Button_Finishing);
					if (DA_FC377) {
						DA_FC263();
						if (DA_FC433 == 0) {
							DA_FC653 = FALSE;
							DA_FC457 = 0;
													}
						else {
							DA_FC653 = TRUE;
							DA_FC457 = 40;
													}
						if (DA_FC434 == 0) {
							DA_FC655 = FALSE;
							DA_FC456 = 0;
													}
						else {
							DA_FC655 = TRUE;
							DA_FC456 = 85;
													}
						if (DA_FC435 == 0) {
							DA_FC657 = FALSE;
							DA_FC458 = 0;
													}
						else {
							DA_FC657 = TRUE;
							DA_FC458 = -15;
													}
						if (DA_FC436 == 0) {
							DA_FC659 = FALSE;
													}
						else {
							DA_FC659 = TRUE;
													}
						if(DA_FC435 == 6 || DA_FC434 == 6 || DA_FC433 == 6){
							if (get_ival(DA_FC1327[-1 + DA_FC435]) || get_ival(DA_FC1327[-1 + DA_FC434]) || get_ival(DA_FC1327[-1 + DA_FC433])){
								combo_run(DA_FC0);
															}
													}
						if (DA_FC657) {
							if (get_val(DA_FC1327[-1 + DA_FC435])) {
								set_val(DA_FC1327[-1 + DA_FC435], 0);
								combo_run(DA_FC98);
								DA_FC1155 = 9000;
															}
													}
						if (DA_FC659) {
							if (get_val(DA_FC1327[-1 + DA_FC436])) {
								set_val(DA_FC1327[-1 + DA_FC436], 0);
								combo_run(DA_FC99);
								DA_FC1155 = 9000;
								//combo_run(SOMBRERO_FLICK_cmb);
							}
							if (combo_running(DA_FC99)) {
								if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452) > 30) {
									combo_stop(DA_FC99);
																	}
															}
													}
						if (DA_FC655) {
							if (get_val(DA_FC1327[-1 + DA_FC434])) {
								set_val(DA_FC1327[-1 + DA_FC434], 0);
								DA_FC270();
								DA_FC1155 = 9000;
															}
													}
						if (DA_FC653) {
							if (get_val(DA_FC1327[-1 + DA_FC433])) {
								set_val(DA_FC1327[-1 + DA_FC433], 0);
								combo_run(DA_FC96);
								DA_FC1155 = 9000;
															}
													}
											}
					else{
						DA_FC457 = 0;
						DA_FC458 = 0;
						DA_FC456 = 0;
											}
					if (DA_FC379) {
						DA_FC271();
											}
									}
							}
			// penalties ON/OFF
			if(get_ival(DA_FC452)){
				DA_FC663 = 0;
				combo_stop(DA_FC88);
							}
					}
		// Entire Script ON/OFF 
		else {
			if (!get_ival(DA_FC452)) DA_FC236(DA_FC1051);
					}
			}
	}
// main end
////ALL-SKillssss////
////////////////////
combo DA_FC1 {
	// 01
	set_val(DA_FC451, 100);
	set_val(DA_FC449,100);
	DA_FC257();
	wait(400);
	set_val(DA_FC448,100);
	wait(90);
	  
	vm_tctrl(0);
	wait( 400);
	
	}
combo DA_FC2 {
	// 01
	set_val(DA_FC451, 100);
	set_val(DA_FC449,100);
	DA_FC257();
	wait(400);
	set_val(DA_FC447,100);
	wait(220);
	  
	vm_tctrl(0);
	wait( 400);
	}
combo DA_FC3 {
	// 03
	call(DA_FC28);
	vm_tctrl(0);
	wait( 100);
	call(DA_FC99);
	DA_FC253(DA_FC1008, DA_FC664);
	vm_tctrl(0);
	wait( 800);
	vm_tctrl(0);
	wait( 350);
	  
	set_val(DA_FC450,100);
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( 400);
	}
combo DA_FC4 {
	//04
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// <-/->   
	vm_tctrl(0);
	wait( DA_FC1011);
	//vm_tctrl(0);wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC5 {
	//05
	DA_FC257();
	// <-/->   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC6 {
	//06
	if (DA_FC478) DA_FC667 = DA_FC587 + 1;
	else DA_FC667 = DA_FC587 - 1;
	DA_FC248(DA_FC667);
	DA_FC259();
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 1000);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC7 {
	//07
	DA_FC260();
	// Down    
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = FALSE;
	DA_FC257();
	// L    
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = TRUE;
	DA_FC257();
	// R    
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC8 {
	//08
	DA_FC260();
	// Down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = TRUE;
	DA_FC257();
	// R  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = FALSE;
	DA_FC257();
	// L   
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC9 {
	//09
	DA_FC478 = TRUE;
	DA_FC257();
	// R 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = FALSE;
	DA_FC257();
	// L 
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC10 {
	// 10
	DA_FC478 = FALSE;
	DA_FC257();
	// R  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = TRUE;
	DA_FC257();
	// L 
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC11 {
	//11
	DA_FC253(0,0);
	set_val(DA_FC449,100);
	set_val(DA_FC450,100);
	DA_FC259();
	vm_tctrl(0);
	wait( 60);
	vm_tctrl(0);
	wait( 60);
	}
combo DA_FC12 {
	//12
	set_val(DA_FC1074, inv(DA_FC1008));
	set_val(DA_FC1075, inv(DA_FC664));
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC1074, inv(DA_FC1008));
	set_val(DA_FC1075, inv(DA_FC664));
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	vm_tctrl(0);
	wait( 500);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC13 {
	//13
	DA_FC253(0, 0);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(0, 0);
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(0, 0);
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 80);
	DA_FC253(0, 0);
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC14 {
	//10
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 80);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC15 {
	//15
	set_val(DA_FC449, 100);
	DA_FC257();
	// <-/-> 
	vm_tctrl(0);
	wait( 500);
	//            
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC16 {
	//16
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// <-/->
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC17 {
	//17
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// <-/->
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC18 {
	// 18
	DA_FC258();
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down           
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	//  <-/->         
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC19 {
	//19
	DA_FC258();
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down           
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	//  <-/->         
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC20 {
	//20
	DA_FC258();
	// L       
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	DA_FC259();
	// up  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	DA_FC257() // L     
	  vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC478 = !DA_FC478;
	DA_FC256();
	vm_tctrl(0);
	wait( 1000);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC21 {
	//21
	set_val(DA_FC449,100);
	DA_FC260();
	vm_tctrl(0);
	wait(60);
	DA_FC253(0,0);
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait(60);
	set_val(DA_FC449,100);
	DA_FC260();
	vm_tctrl(0);
	wait(60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC22 {
	//22
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	DA_FC261();
	// ZERO 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC23 {
	//23
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	DA_FC260();
	vm_tctrl(0);
	wait( 80);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC24 {
	// 24
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	vm_tctrl(0);
	wait( 20);
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	if (DA_FC478) DA_FC667 = DA_FC587 + 4;
	else {
		DA_FC667 = DA_FC587 - 4;
			}
	DA_FC248(DA_FC667);
	DA_FC250(DA_FC1102, DA_FC665);
	set_val(DA_FC452, 100);
	vm_tctrl(0);
	wait( 100);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC25 {
	//25
	set_val(DA_FC451, 100);
	DA_FC260();
	// down                  
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 30);
	set_val(DA_FC451, 100);
	DA_FC260();
	// down                  
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC26 {
	// 26
	set_val(DA_FC451, 100);
	DA_FC259();
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	DA_FC261();
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	DA_FC260();
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC27 {
	//27
	set_val(DA_FC451, 100);
	DA_FC260();
	// down  
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 30);
	set_val(DA_FC451, 100);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 400);
	set_val(PS5_L2, 100);
	set_val(PS5_L1, 100);
	set_val(PS5_R1, 100);
	set_val(PS5_R2, 100);
	vm_tctrl(0);
	wait( 70);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC28 {
	//28
	DA_FC257();
	// down                  
	vm_tctrl(0);
	wait( 300);
	set_val(PS5_R3,100);
	vm_tctrl(0);
	wait( 60);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	//call(HEELtoHEEL);
}
combo DA_FC29 {
	// 29
	DA_FC257();
	// Left || Right 
	set_val(DA_FC452, 0);
	vm_tctrl(0);
	wait( 310);
	vm_tctrl(0);
	wait( 100);
	  
	vm_tctrl(0);
	wait( 350);
	}
//////////////////////////////////////////////////////   
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin 
combo DA_FC30 {
	// 30
	if (DA_FC1007 == DA_FC312) DA_FC1012 = 200;
	//  Ball Roll Fake Turn L2 
	else DA_FC1012 = 1;
	vm_tctrl(0);
	wait( DA_FC1012);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// Left || Right 
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC31 {
	// 31
	DA_FC257();
	// Left || Right 
	vm_tctrl(0);
	wait( 300);
	DA_FC261();
	// ZERO         
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC258();
	// Left || Right
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC32 {
	// 32
	if (DA_FC1007 == DA_FC312) DA_FC1012 = 200;
	//  Ball Roll Fake Turn L2 
	else DA_FC1012 = 1;
	set_val(DA_FC451,100);
	vm_tctrl(0);
	wait( DA_FC1012);
	DA_FC259();
	// up   
	set_val(DA_FC451,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	set_val(DA_FC451,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// Left || Right 
	set_val(DA_FC451,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC33 {
	// 33
	DA_FC257();
	vm_tctrl(0);
	wait( 250);
	DA_FC261();
	vm_tctrl(0);
	wait( 50);
	DA_FC478 = !DA_FC478;
	if (DA_FC478) DA_FC667 = DA_FC587 - 2;
	else DA_FC667 = DA_FC587 + 2;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 45);
	set_val(DA_FC447, 100);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 45);
	DA_FC253(DA_FC1102, DA_FC665);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 45);
	DA_FC253(DA_FC1102, DA_FC665);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 45);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 100);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 500);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC34 {
	// 34
	DA_FC257();
	vm_tctrl(0);
	wait( 280);
	DA_FC256()  set_val(DA_FC447, 100);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC256()  set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC256()  set_val(DA_FC451, 100);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	vm_tctrl(0);
	wait( 250);
	DA_FC256()  vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC35 {
	// 35
	DA_FC257();
	//  <-/->  
	vm_tctrl(0);
	wait( 300);
	DA_FC259();
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC36 {
	// 36
	set_val(DA_FC449, 100);
	DA_FC260();
	// down
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC449, 100);
	DA_FC261();
	// zero
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC449, 100);
	DA_FC260();
	// down
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC37 {
	// 37
	DA_FC259();
	// up                     
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO                   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC260();
	// down                  
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC38 {
	// 38
	set_val(DA_FC449, 100);
	DA_FC258();
	vm_tctrl(0);
	wait( 60);
	//            
	set_val(DA_FC449, 100);
	DA_FC261();
	vm_tctrl(0);
	wait( 60);
	//            
	set_val(DA_FC449, 100);
	DA_FC257();
	vm_tctrl(0);
	wait( 60);
	//           
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC39 {
	// 39 // drag to heel
	DA_FC260();
	// down  
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	//  <-/-> 
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC40 {
	// 40
	if (DA_FC478) DA_FC667 = DA_FC587 + 3;
	else DA_FC667 = DA_FC587 - 3;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(DA_FC1102, DA_FC665);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 80);
	DA_FC253(DA_FC1102, DA_FC665);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC41 {
	//41
	set_val(DA_FC449, 100);
	DA_FC259();
	DA_FC253(0, 0);
	// up     
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC449, 100);
	DA_FC261();
	// ZERO 
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC449, 100);
	DA_FC253(0, 0);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	if (DA_FC478) DA_FC667 = DA_FC587 + 1;
	else DA_FC667 = DA_FC587 - 1;
	DA_FC248(DA_FC667);
	set_val(DA_FC452,0);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 200);
	set_val(DA_FC452,0);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC42 {
	// 42
	if (DA_FC1007 == DA_FC312) DA_FC1012 = 200;
	//  Ball Roll Fake Turn L2 
	else DA_FC1012 = 1;
	vm_tctrl(0);
	wait( DA_FC1012);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// Left || Right 
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC43 {
	// 43
	DA_FC260();
	// down       
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO      
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	//  <-/->    
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(PS5_L2, 100);
	set_val(PS5_R2, 100);
	//LA_L_R();                 
	vm_tctrl(0);
	wait( 300);
	// 800            
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC44 {
	//44
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// Left || Right 
	vm_tctrl(0);
	wait( DA_FC1011);
	if (DA_FC1007 == DA_FC325) DA_FC256();
	set_val(DA_FC451, 100);
	set_val(DA_FC452, 100);
	vm_tctrl(0);
	wait( 200);
	if (DA_FC1007 == DA_FC325) DA_FC256();
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC45 {
	// 45
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC261();
	// ZERO  
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// Left || Right 
	vm_tctrl(0);
	wait( DA_FC1011);
	if (DA_FC1007 == DA_FC325) DA_FC256();
	set_val(DA_FC451, 100);
	set_val(DA_FC452, 100);
	vm_tctrl(0);
	wait( 200);
	if (DA_FC1007 == DA_FC325) DA_FC256();
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC46 {
	//46
	DA_FC260();
	// down 
	set_val(DA_FC450,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC257();
	// <-/->
	set_val(DA_FC450,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	set_val(DA_FC450,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC47 {
	//47
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC259();
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC261();
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC257();
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	set_val(DA_FC452, 100);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	vm_tctrl(0);
	wait( 600);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC48 {
	//48
	DA_FC253(DA_FC1008, DA_FC664);
	set_val(XB1_LS, 100);
	DA_FC259();
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC261();
	set_val(XB1_LS, 100);
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC257();
	vm_tctrl(0);
	wait( DA_FC1011);
	set_val(DA_FC451, 100);
	set_val(DA_FC452, 100);
	if (DA_FC478) DA_FC667 = DA_FC587 + 4;
	else DA_FC667 = DA_FC587 - 4;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 220);
	if (DA_FC478) DA_FC667 = DA_FC587 + 4;
	else DA_FC667 = DA_FC587 - 4;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 60);
	if (DA_FC478) DA_FC667 = DA_FC587 + 1;
	else DA_FC667 = DA_FC587 - 1;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 600);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC49 {
	// 49
	set_val(DA_FC448, 0);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 80);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 80);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 80);
	vm_tctrl(0);
	wait( 500);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC50 {
	//50
	set_val(DA_FC447, 100);
	set_val(DA_FC452,100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC452,100);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	set_val(DA_FC452,100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	set_val(DA_FC452,100);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC51 {
	// 51
	set_val(DA_FC449,100);
	set_val(DA_FC450,100);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	vm_tctrl(0);
	wait( 200);
	//350  
	set_val(DA_FC449,100);
	set_val(DA_FC450,100);
	DA_FC478 = FALSE;
	DA_FC256();
	vm_tctrl(0);
	wait( 50);
	//120  
	set_val(DA_FC449,100);
	set_val(DA_FC450,100);
	DA_FC478 = !DA_FC478;
	DA_FC256();
	set_val(DA_FC449,100);
	set_val(DA_FC450,100);
	vm_tctrl(0);
	wait( 540);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC52 {
	// 52
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	vm_tctrl(0);
	wait( 140);
	set_val(PS5_L2, 100);
	set_val(PS5_R2, 100);
	vm_tctrl(0);
	wait( 100);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC53 {
	// 53
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(inv(DA_FC1008), inv(DA_FC664));
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC54 {
	// 54
	set_val(DA_FC449, 100);
	set_val(DA_FC453, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC449, 100);
	set_val(DA_FC453, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC449, 100);
	set_val(DA_FC453, 0);
	set_val(DA_FC448, 100);
	DA_FC256();
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC449, 100);
	DA_FC256();
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC55 {
	// 55
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 290);
	set_val(PS5_L2, 100);
	set_val(PS5_R2, 100);
	vm_tctrl(0);
	wait( 300);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC56 {
	// 56
	set_val(DA_FC447, 100);
	set_val(DA_FC451,100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC451,100);
	set_val(DA_FC447, 100);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC451,100);
	set_val(DA_FC447, 0);
	set_val(DA_FC448, 100);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC57 {
	//57
	set_val(DA_FC449, 100);
	DA_FC259();
	// <-/-> 
	vm_tctrl(0);
	wait( 300);
	//            
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC58 {
	//58
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC59 {
	//59
	set_val(DA_FC449,100);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( DA_FC1011);
	DA_FC259();
	// up   
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( DA_FC1011);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC60 {
	// 60
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( 100);
	DA_FC261();
	// Zero 
	DA_FC253(DA_FC1008, DA_FC664);
	vm_tctrl(0);
	wait( 60);
	DA_FC259();
	DA_FC253(DA_FC1008, DA_FC664);
	// up   
	vm_tctrl(0);
	wait( 320);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC261();
	// Zero   
	vm_tctrl(0);
	wait( 220);
	DA_FC253(DA_FC1008, DA_FC664);
	DA_FC259();
	DA_FC253(DA_FC1008, DA_FC664);
	// up   
	vm_tctrl(0);
	wait( 100);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC61 {
	// 61
	call(DA_FC84);
	DA_FC253(0, 0);
	call(DA_FC85);
	call(DA_FC85);
	call(DA_FC85);
	call(DA_FC85);
	call(DA_FC85);
	set_val(DA_FC451, 100);
	DA_FC260();
	// down 
	vm_tctrl(0);
	wait( 70);
	set_val(DA_FC451, 100);
	DA_FC261();
	// Zero 
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC451, 100);
	DA_FC259();
	// up   
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 600);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC62 {
	// 62
	set_val(DA_FC1074, inv(DA_FC1008));
	set_val(DA_FC1075, inv(DA_FC664));
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC1074, inv(DA_FC1008));
	set_val(DA_FC1075, inv(DA_FC664));
	set_val(DA_FC450, 100);
	set_val(DA_FC449, 100);
	set_val(PS5_R3, 100);
	vm_tctrl(0);
	wait( 60);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC63 {
	//63
	vm_tctrl(0);
	wait( 100);
	DA_FC253(0, 0);
	DA_FC259();
	vm_tctrl(0);
	wait( 70);
	DA_FC253(0, 0);
	DA_FC261()  vm_tctrl(0);
	wait( 70);
	DA_FC253(0, 0);
	DA_FC259()  vm_tctrl(0);
	wait( 70);
	DA_FC253(0, 0);
	DA_FC261()  vm_tctrl(0);
	wait( 70);
	DA_FC253(0, 0);
	DA_FC260();
	vm_tctrl(0);
	wait( 70);
	DA_FC253(0, 0);
	  
	vm_tctrl(0);
	wait( 350);
	}

combo DA_FC64 {
	//64
	set_val(PS5_R3,100);
	if (DA_FC478) DA_FC667 = DA_FC587 + 1;
	else DA_FC667 = DA_FC587 - 1;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 70);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 400);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC65 {
	// 65
	call(DA_FC84);
	DA_FC253(0,0);
	vm_tctrl(0);
	wait( 60);
	set_val(PS5_R3,100);
	if (DA_FC478) DA_FC667 = DA_FC587 + 1;
	else DA_FC667 = DA_FC587 - 1;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 70);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 400);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC66 {
	//66
	call(DA_FC84);
	DA_FC253(0,0);
	set_val(DA_FC451,100);
	set_val(DA_FC452,100);
	vm_tctrl(0);
	wait( 750);
	}
combo DA_FC67 {
	//67
	set_val(PS5_R3,100);
	if (DA_FC478) DA_FC667 = DA_FC587 + 2;
	else DA_FC667 = DA_FC587 - 2;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 70);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 400);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC68 {
	//68
	set_val(DA_FC451,100);
	set_val(PS5_R3,100);
	if (DA_FC478) DA_FC667 = DA_FC587 ;
	else DA_FC667 = DA_FC587;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 70);
	set_val(DA_FC451,100);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 400);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC69 {
	//69
	call(DA_FC84);
	set_val(DA_FC451,100);
	set_val(PS5_R3,100);
	if (DA_FC478) DA_FC667 = DA_FC587 ;
	else DA_FC667 = DA_FC587;
	DA_FC248(DA_FC667);
	DA_FC253(DA_FC1102, DA_FC665);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 70);
	set_val(DA_FC451,100);
	DA_FC253(DA_FC1102, DA_FC665);
	vm_tctrl(0);
	wait( 400);
	  
	vm_tctrl(0);
	wait( 350);
	}
combo DA_FC70 {
	//70
	DA_FC253(0,0);
	set_val(DA_FC450,100);
	set_val(DA_FC449,100);
	DA_FC257();
	vm_tctrl(0);
	wait( 350);
	vm_tctrl(0);
	wait( 350);

	set_val(DA_FC450,100);
	set_val(DA_FC449,100);
	vm_tctrl(0);
	wait( 400);
	}

/////////////////
int DA_FC140 ;
int DA_FC712 ;
int DA_FC713 ;
int DA_FC714;
int DA_FC715;
function DA_FC126(DA_FC127){
	DA_FC712 = 2;
	// Adjust the rotation
	DA_FC713 = 987654;
	// Modify the modulo
	DA_FC140 = 54321;
	// Change the offset
	DA_FC714 = (DA_FC127 >> DA_FC712) | (DA_FC127 << (32 - DA_FC712));
	DA_FC715 = (((DA_FC714 >> ((DA_FC714 & 0xF) % 13)) & 0x7FFFF) + DA_FC140) % DA_FC713 + 123456;
	return DA_FC715;
	}
define DA_FC717 = -1;
define DA_FC531 = -2;
define DA_FC719 = -3;
define DA_FC720 = 0;
define DA_FC532 = 1;
function DA_FC128(DA_FC127, DA_FC130, DA_FC131) {
	if(DA_FC127 > DA_FC131) return DA_FC130;
	if(DA_FC127 < DA_FC130) return DA_FC131;
	return DA_FC127;
	}
int DA_FC724,DA_FC725;
function DA_FC132(DA_FC133,DA_FC134,DA_FC135,DA_FC136,DA_FC137,DA_FC138){
	// By Jbaze122
	if(!DA_FC138){
		print(DA_FC141(DA_FC139(DA_FC133),DA_FC136,DA_FC134),DA_FC135,DA_FC136,DA_FC137,DA_FC133)    
	}
	else{
		if(DA_FC133 < 0){
			putc_oled(1,45);
					}
		if(DA_FC133){
			for(DA_FC724 = DA_FC145(DA_FC133) + DA_FC725 = (DA_FC133 < 0 ),DA_FC133 = abs(DA_FC133);
			DA_FC133 > 0;
			DA_FC724-- , DA_FC725++){
				putc_oled(DA_FC724,DA_FC133%10 + 48);
				DA_FC133 = DA_FC133/10;
							}
					}
		else{
			putc_oled(1,48);
			DA_FC725 = 1        
		}
		puts_oled(DA_FC141(DA_FC725,DA_FC136,DA_FC134),DA_FC135,DA_FC136,DA_FC725 ,DA_FC137);
			}
	}
int DA_FC746;
function DA_FC139(DA_FC140) {
	// By Swizzy
	DA_FC746 = 0;
	do {
		DA_FC140++;
		DA_FC746++;
			}
	while (duint8(DA_FC140));
	return DA_FC746;
	}
function DA_FC141(DA_FC142,DA_FC136,DA_FC134) {
	// By Jbaze122
	if(DA_FC134 == -3){
		return 128 - ((DA_FC142 * (7 + (DA_FC136 > 1) + DA_FC136 * 4)) + 3 );
			}
	if(DA_FC134 == -2){
		return 64 - ((DA_FC142 * (7 + (DA_FC136 > 1) + DA_FC136 * 4)) / 2);
			}
	if(DA_FC134 == -1){
		return 3
	}
	return DA_FC134;
	}
function DA_FC145(DA_FC146) {
	for(DA_FC724 = 1;
	DA_FC724 < 11;
	DA_FC724++){
		if(!(abs(DA_FC146) / pow(10,DA_FC724))){
			return DA_FC724;
			break;
					}
			}
	return 1;
	}
//--------------------------------------------------------------   
//======================================== 
//  Penalties FIFA 23  v. 1.0                                    
//========================================
function DA_FC147() {
	if (get_ival(DA_FC447)) {
		// Power limiter
		set_val(DA_FC447, 0);
		if (get_ival(DA_FC449)) DA_FC752 = 50;
		// chip penalty power
		if (!get_ival(DA_FC449)) DA_FC752 = 440;
		// normal penalty power
		combo_run(DA_FC71);
			}
	if (DA_FC751 > 0) set_polar(POLAR_LS, DA_FC751 * -1, 32767);
	// Aim - Lock
	if (get_ival(PS5_RIGHT) && get_ival(PS5_DOWN)) DA_FC751 = 345;
	// DOWN_RIGHT 
	if (get_ival(PS5_RIGHT) && get_ival(PS5_UP)) DA_FC751 = 45;
	// UP_RIGHT
	if (get_ival(PS5_LEFT) && get_ival(PS5_UP)) DA_FC751 = 135;
	// UP_LEFT
	if (get_ival(PS5_LEFT) && get_ival(PS5_DOWN)) DA_FC751 = 225;
	// DOWN_LEFT
	if (event_press(PS5_LEFT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA_FC751 = 180;
	// LEFT
	if (event_press(PS5_RIGHT) && !get_ival(PS5_UP) && !get_ival(PS5_DOWN)) DA_FC751 = 1;
	// RIGHT
	if (event_press(PS5_UP) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA_FC751 = 90;
	// UP_MIDDLE
	if (event_press(PS5_DOWN) && !get_ival(PS5_RIGHT) && !get_ival(PS5_LEFT)) DA_FC751 = 270;
	// DOWN_MIDDLE
}
int DA_FC752;
int DA_FC552;
int DA_FC751;
combo DA_FC71 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC752);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 3800);
	DA_FC552 = !DA_FC552;
	// reset after each penalty
}
define DA_FC755 = 19;
function DA_FC148(DA_FC149, DA_FC150) {
	if (DA_FC354 == DA_FC150) {
		if (event_press(PS5_RIGHT)) {
			DA_FC149 = clamp(DA_FC149 + 1, 0, DA_FC758[DA_FC354]);
			// Max Amount of Toggles From Array
			DA_FC359 = TRUE;
					}
		if (event_press(PS5_LEFT)) {
			DA_FC149 = clamp(DA_FC149 - 1, 0, DA_FC758[DA_FC354]);
			// Max Amount of Toggles From Array
			DA_FC359 = TRUE;
					}
		/* Print Strings here... */
		if (DA_FC354 == 0) {
			print(DA_FC228(DA_FC181(DA_FC762[DA_FC360]) ,OLED_FONT_SMALL_WIDTH),DA_FC755  ,OLED_FONT_SMALL , OLED_WHITE ,DA_FC762[DA_FC360]);
					}
		else if (DA_FC354 == 1) {
			print(DA_FC228(DA_FC181(DA_FC764[DA_FC361]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC764[DA_FC361]);
					}
		else if (DA_FC354 == 2) {
			print(DA_FC228(DA_FC181(DA_FC764[DA_FC362]) ,OLED_FONT_SMALL_WIDTH ),DA_FC755  ,OLED_FONT_SMALL , OLED_WHITE ,DA_FC764[DA_FC362]);
					}
		else if (DA_FC354 == 5) {
			print(DA_FC228(DA_FC181(DA_FC768[DA_FC365]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC768[DA_FC365]);
					}
		else if (DA_FC354 == 6) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC366]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC366]);
					}
		else if (DA_FC354 == 7) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC367]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC367]);
					}
		else if (DA_FC354 == 8) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC368]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC368]);
					}
		else if (DA_FC354 == 9) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC369]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC369]);
					}
		else if (DA_FC354 == 20) {
			print(DA_FC228(DA_FC181(DA_FC778[DA_FC110]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC778[DA_FC110]);
					}
		else {
			if (DA_FC149 == 1)        print(DA_FC228(DA_FC181(DA_FC780[1]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[1])      else        print(DA_FC228(DA_FC181(DA_FC780[0]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[0])    
		}
			}
	return DA_FC149;
	}
//============================================================
function DA_FC151(DA_FC149, DA_FC150) {
	if (DA_FC355 == DA_FC150) {
		// Which valNameIdx number we are on \\
		if (get_ival(PS5_L2)) {
			// Ads 
			if (event_press(PS5_RIGHT)) {
				DA_FC149 += DA_FC786[DA_FC355][2] // Increase value by desired in Array 
				        DA_FC359 = TRUE;
							}
			if (event_press(PS5_LEFT)) {
				DA_FC149 -= DA_FC786[DA_FC355][2] // Increase value by desired in Array 
				        DA_FC359 = TRUE;
							}
			DA_FC149 = clamp(DA_FC149, DA_FC786[DA_FC355][0], DA_FC786[DA_FC355][1]);
			// Min && Max Value
		}
		/*===============================================================================================================================
    Display Toggle Strings 
    =================================================================================================================================
    */
		if (DA_FC355 == 8) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC386]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC386])    
		}
		else if (DA_FC355 == 9) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC387]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC387])    
		}
		else if (DA_FC355 == 10) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC388]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC388])    
		}
		else if (DA_FC355 == 11) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC389]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC389])    
		}
		else if (DA_FC355 == 12) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC390]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC390])    
		}
		else if (DA_FC355 == 13) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC391]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC391])    
		}
		else if (DA_FC355 == 14) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC392]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC392])    
		}
		else if (DA_FC355 == 15) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC393]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC393])    
		}
		else if (DA_FC355 == 16) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC394]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC394])    
		}
		else if (DA_FC355 == 17) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC275]),OLED_FONT_SMALL_WIDTH ),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC275]) 
		}
		else if(DA_FC355 == 18){
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC276]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC276]) 
		}
		else if(DA_FC355 == 19){
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC277]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC277]) 
		}
		else if(DA_FC355 == 20){
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC278]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC770[DA_FC278]) 
		}
		else if(DA_FC355 == 21){
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC279]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC805[DA_FC279])      
		}
		else if(DA_FC355 == 22){
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC407]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC407])    
		}
		else if (DA_FC355 == 23) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC408]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC408])    
		}
		else if (DA_FC355 == 24) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC409]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC409])    
		}
		else if (DA_FC355 == 25) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC410]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC410])    
		}
		else if (DA_FC355 == 26) {
			print(DA_FC228(DA_FC181(DA_FC770[DA_FC411]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC770[DA_FC411])    
		}
		else if (DA_FC355 == 27) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC412]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC412])    
		}
		else if (DA_FC355 == 28) {
			print(DA_FC228(DA_FC181(DA_FC829[DA_FC413]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC413])    
		}
		else if (DA_FC355 == 29) {
			print(DA_FC228(DA_FC181(DA_FC831[DA_FC414]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC414])    
		}
		else if (DA_FC355 == 30) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC415]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC415])    
		}
		else if (DA_FC355 == 31) {
			print(DA_FC228(DA_FC181(DA_FC829[DA_FC416]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC416])    
		}
		else if (DA_FC355 == 32) {
			print(DA_FC228(DA_FC181(DA_FC831[DA_FC417]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC417])    
		}
		else if (DA_FC355 == 33) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC418]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC418])    
		}
		else if (DA_FC355 == 34) {
			print(DA_FC228(DA_FC181(DA_FC829[DA_FC419]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC419])    
		}
		else if (DA_FC355 == 35) {
			print(DA_FC228(DA_FC181(DA_FC831[DA_FC420]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC420])    
		}
		else if (DA_FC355 == 36) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC421]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC421])    
		}
		else if (DA_FC355 == 37) {
			print(DA_FC228(DA_FC181(DA_FC829[DA_FC422]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC829[DA_FC422])    
		}
		else if (DA_FC355 == 38) {
			print(DA_FC228(DA_FC181(DA_FC831[DA_FC423]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC831[DA_FC423])    
		}
		else if (DA_FC355 == 41) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC426]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC426])    
		}
		else if (DA_FC355 == 48) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC433]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC433])    
		}
		else if (DA_FC355 == 49) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC434]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC434])    
		}
		else if (DA_FC355 == 50) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC435]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC435])    
		}
		else if (DA_FC355 == 51) {
			print(DA_FC228(DA_FC181(DA_FC805[DA_FC436]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC805[DA_FC436])    
		}
		else if(DA_FC355 == 0){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC439]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC439]) 
		}
		else if(DA_FC355 == 1){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC440]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC440]) 
		}
		else if(DA_FC355 == 2){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC441]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC441]) 
		}
		else if(DA_FC355 == 3){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC442]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC442]) 
		}
		else if(DA_FC355 == 4){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC443]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC443]) 
		}
		else if(DA_FC355 == 5){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC444]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC444]) 
		}
		else if(DA_FC355 == 6){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC445]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC445]) 
		}
		else if(DA_FC355 == 7){
			print(DA_FC228(DA_FC181(DA_FC861[DA_FC446]),OLED_FONT_SMALL_WIDTH),DA_FC755,OLED_FONT_SMALL,OLED_WHITE,DA_FC861[DA_FC446]) 
		}
		else{
			if (DA_FC149 == 1)        print(DA_FC228(DA_FC181(DA_FC780[1]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[1])      else        print(DA_FC228(DA_FC181(DA_FC780[0]), OLED_FONT_SMALL_WIDTH), DA_FC755, OLED_FONT_SMALL, OLED_WHITE, DA_FC780[0])    
		}
		DA_FC184(0);
			}
	return DA_FC149;
	}
/*
==================================================================================================================
Edit Values Function For Edit Menu 
==================================================================================================================
*/
function DA_FC154(DA_FC149, DA_FC150) {
	if (DA_FC355 == DA_FC150) {
		if (get_ival(PS5_L2)) {
			// Ads 
			if (event_press(PS5_RIGHT)) {
				DA_FC149 += DA_FC786[DA_FC355][2] // Increase value by desired in Array 
				        DA_FC359 = TRUE;
							}
			if (event_press(PS5_LEFT)) {
				DA_FC149 -= DA_FC786[DA_FC355][2] // Decrease value by desired in Array 
				        DA_FC359 = TRUE;
							}
			if (event_press(PS5_UP)) {
				DA_FC149 += DA_FC786[DA_FC355][3] // Increase value by desired in Array 
				        DA_FC359 = TRUE;
							}
			if (event_press(PS5_DOWN)) {
				DA_FC149 -= DA_FC786[DA_FC355][3] // Increase value by desired in Array 
				        DA_FC359 = TRUE;
							}
			DA_FC149 = clamp(DA_FC149, DA_FC786[DA_FC355][0], DA_FC786[DA_FC355][1]);
			// Min && Max Value
		}
		DA_FC231(DA_FC149, DA_FC234(DA_FC149));
		// Display Value 
	}
	return DA_FC149;
	}
int DA_FC888, DA_FC889, DA_FC890;
function DA_FC157(DA_FC127, DA_FC159, DA_FC160, DA_FC161, DA_FC136) {
	DA_FC889 = 1;
	DA_FC890 = 10000;
	if (DA_FC127 < 0) //--neg numbers                     
	  {
		putc_oled(DA_FC889, 45);
		//--add leading "-" 
		DA_FC889 += 1;
		DA_FC127 = abs(DA_FC127);
			}
	for (DA_FC888 = 5;
	DA_FC888 >= 1;
	DA_FC888--) {
		if (DA_FC159 >= DA_FC888) {
			putc_oled(DA_FC889, DA_FC896[DA_FC127 / DA_FC890]);
			DA_FC127 = DA_FC127 % DA_FC890;
			DA_FC889 += 1;
					}
		DA_FC890 /= 10;
			}
	puts_oled(DA_FC160, DA_FC161, DA_FC136, DA_FC889 - 1, OLED_WHITE);
	// adjustable value centered in X
}
const string DA_FC568 = " No Edit Variable";
const string DA_FC567 = " A/CROSS to Edit ";
const string DA_FC563 = "MOD;";
const string DA_FC565 = "MSL;";
int DA_FC900;
function DA_FC163(DA_FC146) {
	//  find_digits(value)                                
//        return Number of Digits in Value Passed     
	DA_FC146 = abs(DA_FC146);
	if (DA_FC146 / 10000 > 0) return 5;
	if (DA_FC146 / 1000 > 0) return 4;
	if (DA_FC146 / 100 > 0) return 3;
	if (DA_FC146 / 10 > 0) return 2;
	return 1;
	}
const int8 DA_FC896[] =  //      0  1  2  3  4  5  6  7  8  9  (column numbers)
  {
	48,    49,    50,    51,    52,    53,    54,    55,    56,    57  
}
;
int DA_FC901, DA_FC902;
/*     
======================================================
Logo Picture : Default ZEN Logo
======================================================
*/
const image DA_FC904 = {
	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
}
;
combo DA_FC72 {
	call(DA_FC73);
	DA_FC180();
	vm_tctrl(0);
	wait( 2400);
	cls_oled(0);
	image_oled(0, 0, TRUE, TRUE, DA_FC904[0]);
	vm_tctrl(0);
	wait( get_rtime());
	vm_tctrl(0);
	wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DA_FC904[0]);
	vm_tctrl(0);
	wait( get_rtime());
	vm_tctrl(0);
	wait( 1000)call(DA_FC74);
	vm_tctrl(0);
	wait( 1000);
	DA_FC356 = TRUE;
	}
combo DA_FC73 {
	cls_oled(OLED_BLACK);
	}
const int8 DA_FC1309[] = {
	OLED_FONT_SMALL_HEIGHT, OLED_FONT_MEDIUM_HEIGHT, OLED_FONT_LARGE_HEIGHT 
}
const int8 DA_FC1310[] = {
	OLED_FONT_SMALL_WIDTH, OLED_FONT_MEDIUM_WIDTH, OLED_FONT_LARGE_WIDTH 
}
int DA_FC906;
function DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169) {
	// Decrement display buffer value for proper arithmetic
	DA_FC906--;
	// Check horizontal alignment
	switch(DA_FC166) {
		case DA_FC919 {
			DA_FC166 = OLED_WIDTH - (DA_FC906 * DA_FC1310[DA_FC168]) - 4;
			// Additional 4 for padding from border
			break;
					}
		case DA_FC918 {
			DA_FC166 = (OLED_WIDTH >> 1) - ((DA_FC906 * DA_FC1310[DA_FC168]) >> 1);
			break;
					}
		// No alignLeft case is needed because alignLeft is set to the proper left alignment already
	}
	// Check vertical alignment
	switch(DA_FC167) {
		case DA_FC918 {
			DA_FC167 = (OLED_HEIGHT >> 1) - (DA_FC1309[DA_FC168] >> 1);
			break;
					}
		case DA_FC921 {
			DA_FC167 = OLED_HEIGHT - DA_FC1309[DA_FC168] - 4;
			// Additional 4 for padding from border
			break;
					}
		// No alignTop case is needed because alignTop is set to the proper top alignment already
	}
	puts_oled(DA_FC166, DA_FC167, DA_FC168, DA_FC906, DA_FC169);
	// Output display buffer
	DA_FC906 = 1;
	// Reset display buffer
}
enum {
	DA_FC918 = -2, DA_FC919, DA_FC920 = 5, DA_FC921 = -1, DA_FC922 = 5 
}

data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0);
combo DA_FC74 {
	vm_tctrl(0);
	wait(360);
	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0)
	set_rumble(RUMBLE_A, 50);
	vm_tctrl(0);
	wait( 720);
	set_rumble(RUMBLE_A, 50);
	set_rumble(RUMBLE_B, 100);
	vm_tctrl(0);
	wait( 720);
	reset_rumble();
	vm_tctrl(0);
	wait( 1560);
	}
function DA_FC170(DA_FC166, DA_FC167, DA_FC173, DA_FC168, DA_FC169) {
	DA_FC178(DA_FC173);
	DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169);
	}
function DA_FC176(DA_FC177) {
	putc_oled(DA_FC906, DA_FC177);
	DA_FC906++;
	}
function DA_FC178(DA_FC179) {
	// Loop through each character of the string
	do {
		DA_FC176(dint8(DA_FC179));
		// Insert character into display buffer
		DA_FC179++;
		// Move to next character of the string
	}
	while(dint8(DA_FC179)) // Check if a next character exists
}
const int16 DA_FC1311[] = {
	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6
}
const int16 DA_FC1312[] = {
	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6
}
const int16 DA_FC1313[] = {
	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328
}
// sin(x)
const int16 DA_FC1314[] = {
	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328
}
// cos(x)
int DA_FC934;
int DA_FC935;
int DA_FC936;
int DA_FC937;
int DA_FC938;
int DA_FC939;
int DA_FC940;
function DA_FC180() {
	// Must be a minimum of 2
	DA_FC940 = 3;
	// Get coordinates
//aimAssistX = (aimAssistRadius >> 1) * cos[aimAssistAngle];
//aimAssistY = aimAssistRadius * sin[aimAssistAngle];
	DA_FC938 = DA_FC940 * DA_FC1314[DA_FC939];
	DA_FC937 = DA_FC940 * DA_FC1311[DA_FC939];
	// Calculate offset
	DA_FC935 = ((DA_FC938 * DA_FC1313[DA_FC934]) / 328) - ((DA_FC937 * DA_FC1312[DA_FC934]) / 328);
	DA_FC936 = ((DA_FC938 * DA_FC1312[DA_FC934]) / 328) + ((DA_FC937 * DA_FC1313[DA_FC934]) / 328);
	// Prepare to output
	DA_FC938 = DA_FC935;
	DA_FC937 = DA_FC936;
	// Step ahead 
	DA_FC939 += 1;
	DA_FC934 += 45;
	if(DA_FC939 >= 360) {
		DA_FC939 %= 360;
			}
	if(DA_FC934 >= 360) {
		DA_FC934 %= 360;
			}
	// Print to OLED
	pixel_oled(64 + (((DA_FC938 / DA_FC940) * 30) / 328), 32 + (((DA_FC937 / DA_FC940) * 30) / 328), OLED_WHITE);
	}
int DA_FC943;
function DA_FC181(DA_FC140) {
	DA_FC943 = 0;
	do {
		DA_FC140++;
		DA_FC943++;
			}
	while (duint8(DA_FC140));
	return DA_FC943;
	}
int DA_FC946;
const uint8 DA_FC1315[] = {
	PS5_OPTIONS,  PS5_LEFT,  PS5_RIGHT,  PS5_UP,  PS5_DOWN,  PS5_CROSS,  PS5_CIRCLE,  PS5_SQUARE,  PS5_TRIANGLE,  PS5_R3,  PS5_L3,  PS5_RX,  PS5_RY,  PS5_PS,  PS5_TOUCH,  PS5_SHARE
}
;
function DA_FC183() {
	for (DA_FC946 = 0;
	DA_FC946 < sizeof(DA_FC1315) / sizeof(DA_FC1315[0]);
	DA_FC946++) {
		if (get_ival(DA_FC1315[DA_FC946]) || event_press(DA_FC1315[DA_FC946])) {
			set_val(DA_FC1315[DA_FC946], 0);
			// Add your condition to break the loop
		}
			}
	}
define DA_FC947 = 131;
define DA_FC948 = 132;
define DA_FC949 = 133;
define DA_FC950 = 134;
define DA_FC951 = 130;
define DA_FC952 = 89;
define DA_FC953 = 127;
define DA_FC954 = 65;
int DA_FC955;
int DA_FC956;
int DA_FC957 = 1;
define DA_FC958 = 36;
const string DA_FC959 = "Hold LT/L2 +";
function DA_FC184(DA_FC185) {
	line_oled(1, 48, 127, 48, 1, 1);
	// print text 
	print(2, 52, OLED_FONT_SMALL, 1, DA_FC959[0]);
	// print Left/ Right arrow
	rect_oled(90, 50, 127, 60, OLED_WHITE, DA_FC957);
	// rectangle left (line 1)
//rect_oled(100, 48, OLED_FONT_SMALL_WIDTH + 2, OLED_FONT_SMALL_HEIGHT + 2, OLED_WHITE, col_rec_right); // rectangle right (line 1)
	putc_oled(1, DA_FC949);
	puts_oled(91, 51, OLED_FONT_SMALL, 1, DA_FC955);
	putc_oled(1, DA_FC950);
	puts_oled(101, 51, OLED_FONT_SMALL, 1, DA_FC956);
	if (DA_FC185) {
		putc_oled(1, DA_FC947);
		puts_oled(111, 51, OLED_FONT_SMALL, 1, DA_FC955);
		putc_oled(1, DA_FC948);
		puts_oled(121, 51, OLED_FONT_SMALL, 1, DA_FC956);
			}
	}
const uint8 DA_FC1317 [] = {
	  PS5_R1,       // 0
	  PS5_R2,       // 1
	  PS5_R3,       // 2
	  PS5_L1,       // 3
	  PS5_L2,       // 4
	  PS5_L3,       // 5
	  PS5_TRIANGLE, // 6
	  PS5_CIRCLE,   // 7
	  PS5_CROSS,    // 8
	  PS5_SQUARE    // 9
	 
}
;
function DA_FC186() {
	DA_FC503 = get_pvar(SPVAR_1,0,1,0);
	DA_FC509 = get_pvar(SPVAR_2,0,1,0);
	DA_FC502 = get_pvar(SPVAR_3,11111, 99999,11111);
	DA_FC188();
	if (DA_FC213(0, 1, 0)) {
		DA_FC365 = DA_FC213(  0, 6, 0);
		DA_FC362 = DA_FC213(0, 3, 0);
		DA_FC363 = DA_FC213(0,1,0);
		DA_FC364 = DA_FC213(0,1,0);
		DA_FC275 = DA_FC213(0, 70, 0);
		DA_FC276 = DA_FC213(0, 70, 0);
		DA_FC277 = DA_FC213(0, 70, 0);
		DA_FC278 = DA_FC213(0, 70, 0);
		DA_FC279 = DA_FC213(0, 22, 8);
		DA_FC366 = DA_FC213(0, 70, 0);
		DA_FC367 = DA_FC213(0, 70, 0);
		DA_FC368 = DA_FC213(0, 70, 0);
		DA_FC369 = DA_FC213(0, 70, 0);
		DA_FC370 = DA_FC213(0, 1, 0);
		DA_FC371 = DA_FC213(0, 1, 0);
		DA_FC372 = DA_FC213(0, 1, 0);
		DA_FC373 = DA_FC213(0, 1, 0);
		DA_FC381 = DA_FC213(0, 1, 0);
		DA_FC407 = DA_FC213(0, 70, 0);
		DA_FC408 = DA_FC213(0, 70, 0);
		DA_FC409 = DA_FC213(0, 70, 0);
		DA_FC410 = DA_FC213(0, 70, 0);
		DA_FC411 = DA_FC213(0, 70, 0);
		DA_FC412 = DA_FC213(1, 25, 1);
		DA_FC413 = DA_FC213(0, 1, 0);
		DA_FC414 = DA_FC213(0, 1, 0);
		DA_FC415 = DA_FC213(1, 25, 5);
		DA_FC416 = DA_FC213(0, 1, 0);
		DA_FC417 = DA_FC213(0, 1, 0);
		DA_FC418 = DA_FC213(0, 25, 2);
		DA_FC419 = DA_FC213(0, 1, 0);
		DA_FC420 = DA_FC213(0, 1, 1);
		DA_FC421 = DA_FC213(1, 25, 8);
		DA_FC422 = DA_FC213(0, 1, 0);
		DA_FC423 = DA_FC213(0, 1, 1);
		DA_FC424 = DA_FC213(350, 600, 350);
		DA_FC425 = DA_FC213(350, 600, 445);
		DA_FC426 = DA_FC213(0, 22, 0);
		DA_FC427 = DA_FC213(0, 1, 0);
		DA_FC428 = DA_FC213(-100, 300, 0);
		DA_FC374 = DA_FC213(0, 1, 0);
		DA_FC375 = DA_FC213(0, 1, 0);
		DA_FC376 = DA_FC213(0, 1, 0);
		DA_FC377 = DA_FC213(0, 1, 0);
		DA_FC378 = DA_FC213(0, 1, 0);
		DA_FC429 = DA_FC213(-150, 150, 0);
		DA_FC430 = DA_FC213(-150, 150, 0);
		DA_FC431 = DA_FC213(0, 1, 0);
		DA_FC432 = DA_FC213(-150, 150, 0);
		DA_FC433 = DA_FC213(0, 22, 0);
		DA_FC434 = DA_FC213(0, 22, 0);
		DA_FC435 = DA_FC213(0, 22, 0);
		DA_FC436 = DA_FC213(0, 22, 0);
		DA_FC602 = DA_FC213(60, 400, 235);
		DA_FC438 = DA_FC213(0, 1, 0);
		DA_FC437 = DA_FC213(0, 1, 0);
		DA_FC361 = DA_FC213(0, 3, 0);
		DA_FC386 = DA_FC213(0, 70, 0);
		DA_FC387 = DA_FC213(0, 70, 0);
		DA_FC388 = DA_FC213(0, 70, 0);
		DA_FC391 = DA_FC213(0, 70, 0);
		DA_FC392 = DA_FC213(0, 70, 0);
		DA_FC393 = DA_FC213(0, 70, 0);
		DA_FC394 = DA_FC213(0, 22, 8);
		DA_FC379 = DA_FC213(0, 1, 0);
		DA_FC389 = DA_FC213(0, 70, 0);
		DA_FC390 = DA_FC213(0, 70, 0);
		DA_FC585 = DA_FC213(50, 1000, 500);
		DA_FC1197 = DA_FC213(0, 1, 0);
		DA_FC1190 = DA_FC213(0, 1, 0);
		DA_FC110 = DA_FC213(0, 6, 0);
		DA_FC406 = DA_FC213(0, 1, 0);
		DA_FC360 = DA_FC213(0, 2, 0);
		DA_FC439 = DA_FC213(0, 9, 9);
		DA_FC440 = DA_FC213(0, 9, 8);
		DA_FC441 = DA_FC213(0, 9, 3);
		DA_FC442 = DA_FC213(0, 9, 1);
		DA_FC443 = DA_FC213(0, 9, 4);
		DA_FC444 = DA_FC213(0, 9, 0);
		DA_FC445 = DA_FC213(0, 9, 7);
		DA_FC446 = DA_FC213(0, 9, 6);
		DA_FC382    = DA_FC213(0, 1, 0);
		// 1
		DA_FC383    = DA_FC213(0, 1, 0);
		// 2
		DA_FC384     = DA_FC213(0, 1, 0);
		// 3
		DA_FC395     = DA_FC213(60, 500, 120);
		// 4
		DA_FC396     = DA_FC213(60, 500, 350);
		// 5
		DA_FC397    = DA_FC213(0, 1, 0);
		// 6
		DA_FC398 = DA_FC213(0, 1, 0);
		// 7
		DA_FC399     = DA_FC213(50, 250, 80);
		// 8
		DA_FC400     = DA_FC213(100, 850, 180);
		// 9
		DA_FC401 = DA_FC213(0, 1, 0);
		// 10
		DA_FC402    = DA_FC213(0, 1, 0);
		// 11
		DA_FC403        = DA_FC213(80, 500, 120);
		// 12
		DA_FC404        = DA_FC213(80, 500, 350);
		// 13
		DA_FC405       = DA_FC213(0, 1, 0);
		// 14
		DA_FC455           = DA_FC213(-2500, 2500, 1000);
		// 14
		DA_FC29           = DA_FC213(0, 1, 0);
		// 1
		DA_FC475         = DA_FC213(0, 1, 0);
		// 1
		DA_FC473       = DA_FC213(0, 1, 0);
		// 1
	}
	else{
		DA_FC365 = 0;
		DA_FC362 = 0;
		DA_FC363 = 0;
		DA_FC364 = 0;
		DA_FC275 = 0;
		DA_FC276 = 0;
		DA_FC277 = 0;
		DA_FC278 = 0;
		DA_FC279 = 8;
		DA_FC366 = 0;
		DA_FC367 = 0;
		DA_FC368 = 0;
		DA_FC369 = 0;
		DA_FC370 = 0;
		DA_FC371 = 0;
		DA_FC372 = 0;
		DA_FC373 = 0;
		DA_FC381 = 0;
		DA_FC407 = 0;
		DA_FC408 = 0;
		DA_FC409 = 0;
		DA_FC410 = 0;
		DA_FC411 = 0;
		DA_FC412 = 1;
		DA_FC413 = 0;
		DA_FC414 = 0;
		DA_FC415 = 5;
		DA_FC416 = 0;
		DA_FC417 = 0;
		DA_FC418 = 2;
		DA_FC419 = 0;
		DA_FC420 = 1;
		DA_FC421 = 8;
		DA_FC422 = 0;
		DA_FC423 = 1;
		DA_FC424 = 350;
		DA_FC425 = 445;
		DA_FC426 = 0;
		DA_FC427 = 0;
		DA_FC428 = 0;
		DA_FC374 = 0;
		DA_FC375 = 0;
		DA_FC376 = 0;
		DA_FC377 = 0;
		DA_FC378 = 0;
		DA_FC429 = 0;
		DA_FC430 = 0;
		DA_FC431 = 0;
		DA_FC432 = 0;
		DA_FC433 = 0;
		DA_FC434 = 0;
		DA_FC435 = 0;
		DA_FC436 = 0;
		DA_FC602 = 235;
		DA_FC438 = 0;
		DA_FC437 = 0;
		DA_FC361 = 0;
		DA_FC386 = 0;
		DA_FC387 = 0;
		DA_FC388 = 0;
		DA_FC391 = 0;
		DA_FC392 = 0;
		DA_FC393 = 0;
		DA_FC394 = 8;
		DA_FC379 = 0;
		DA_FC389 = 0;
		DA_FC390 = 0;
		DA_FC585 = 500;
		DA_FC1197 = 0;
		DA_FC1190 = 0;
		DA_FC110 = 0;
		DA_FC406 = 0;
		DA_FC360 = 0;
		DA_FC439 = 9;
		DA_FC440 = 8;
		DA_FC441 = 3;
		DA_FC442 = 1;
		DA_FC443 = 4;
		DA_FC444 = 0;
		DA_FC445 = 7;
		DA_FC446 = 6;
		DA_FC382 = 0;
		DA_FC383 = 0;
		DA_FC384 = 0;
		DA_FC395 = 120;
		DA_FC396 = 350;
		DA_FC397 = 0;
		DA_FC398 = 0;
		DA_FC399 = 80;
		DA_FC400 = 180;
		DA_FC401 = 0;
		DA_FC402 = 0;
		DA_FC403 = 120;
		DA_FC404 = 360;
		DA_FC405 = 0;
		DA_FC455     = 50;
		DA_FC29     = 0;
		DA_FC475     = 0;
		DA_FC473     = 0;
			}
	if (DA_FC360 == 0) {
		DA_FC447 = PS5_CIRCLE;
		DA_FC448 = PS5_CROSS;
		DA_FC449 = PS5_L1;
		DA_FC450 = PS5_R1;
		DA_FC451 = PS5_L2;
		DA_FC452 = PS5_R2;
		DA_FC453 = PS5_SQUARE;
		DA_FC454 = PS5_TRIANGLE;
			}
	else if (DA_FC360 == 1) {
		DA_FC447      = PS5_SQUARE;
		// 1 
		DA_FC448      = PS5_CROSS ;
		// 2 
		DA_FC449    = PS5_L1    ;
		// 3 
		DA_FC450  = PS5_R1;
		// 4 
		DA_FC451    = PS5_L2;
		// 5 
		DA_FC452    = PS5_R2;
		// 6 
		DA_FC453     = PS5_CIRCLE;
		// 7 
		DA_FC454  = PS5_TRIANGLE;
		// 8   
	}
	else if (DA_FC360 == 2) {
		DA_FC447 = DA_FC1317[DA_FC439];
		DA_FC448 = DA_FC1317[DA_FC440];
		DA_FC449 = DA_FC1317[DA_FC441];
		DA_FC450 = DA_FC1317[DA_FC442];
		DA_FC451 = DA_FC1317[DA_FC443];
		DA_FC452 = DA_FC1317[DA_FC444];
		DA_FC453 = DA_FC1317[DA_FC445];
		DA_FC454 = DA_FC1317[DA_FC446];
			}
	}
//=========================================================
//  SAVE FUNCTION 
//=========================================================
function DA_FC187() {
	DA_FC188();
	DA_FC211(   1,0,     1);
	DA_FC211(DA_FC365, 0, 6);
	DA_FC211(DA_FC362, 0, 3);
	DA_FC211(DA_FC363, 0 , 1);
	DA_FC211(DA_FC364, 0 , 1);
	DA_FC211(DA_FC275, 0, 70);
	DA_FC211(DA_FC276, 0, 70);
	DA_FC211(DA_FC277, 0, 70);
	DA_FC211(DA_FC278, 0, 70);
	DA_FC211(DA_FC279, 0, 22);
	DA_FC211(DA_FC366, 0, 70);
	DA_FC211(DA_FC367, 0, 70);
	DA_FC211(DA_FC368, 0, 70);
	DA_FC211(DA_FC369, 0, 70);
	DA_FC211(DA_FC370, 0, 1);
	DA_FC211(DA_FC371, 0, 1);
	DA_FC211(DA_FC372, 0, 1);
	DA_FC211(DA_FC373, 0, 1);
	DA_FC211(DA_FC381, 0, 1);
	DA_FC211(DA_FC407, 0, 70);
	DA_FC211(DA_FC408, 0, 70);
	DA_FC211(DA_FC409, 0, 70);
	DA_FC211(DA_FC410, 0, 70);
	DA_FC211(DA_FC411, 0, 70);
	DA_FC211(DA_FC412, 1, 25);
	DA_FC211(DA_FC413, 0, 1);
	DA_FC211(DA_FC414, 0, 1);
	DA_FC211(DA_FC415, 1, 25);
	DA_FC211(DA_FC416, 0, 1);
	DA_FC211(DA_FC417, 0, 1);
	DA_FC211(DA_FC418, 0, 25);
	DA_FC211(DA_FC419, 0, 1);
	DA_FC211(DA_FC420, 0, 1);
	DA_FC211(DA_FC421, 1, 25);
	DA_FC211(DA_FC422, 0, 1);
	DA_FC211(DA_FC423, 0, 1);
	DA_FC211(DA_FC424, 350, 600);
	DA_FC211(DA_FC425, 350, 600);
	DA_FC211(DA_FC426, 0, 22);
	DA_FC211(DA_FC427, 0, 1);
	DA_FC211(DA_FC428, -100, 300);
	DA_FC211(DA_FC374, 0, 1);
	DA_FC211(DA_FC375, 0, 1);
	DA_FC211(DA_FC376, 0, 1);
	DA_FC211(DA_FC377, 0, 1);
	DA_FC211(DA_FC378, 0, 1);
	DA_FC211(DA_FC429, -150, 150);
	DA_FC211(DA_FC430, -150, 150);
	DA_FC211(DA_FC431, 0, 1);
	DA_FC211(DA_FC432, -150, 150);
	DA_FC211(DA_FC433, 0, 22);
	DA_FC211(DA_FC434, 0, 22);
	DA_FC211(DA_FC435, 0, 22);
	DA_FC211(DA_FC436, 0, 22);
	DA_FC211(DA_FC602, 60, 400);
	DA_FC211(DA_FC438, 0, 1);
	DA_FC211(DA_FC437, 0, 1);
	DA_FC211(DA_FC361, 0, 3);
	DA_FC211(DA_FC386, 0, 70);
	DA_FC211(DA_FC387, 0, 70);
	DA_FC211(DA_FC388, 0, 70);
	DA_FC211(DA_FC391, 0, 70);
	DA_FC211(DA_FC392, 0, 70);
	DA_FC211(DA_FC393, 0, 70);
	DA_FC211(DA_FC394, 0, 22);
	DA_FC211(DA_FC379, 0, 1);
	DA_FC211(DA_FC389, 0, 70);
	DA_FC211(DA_FC390, 0, 70);
	DA_FC211(DA_FC585, 50, 1000);
	DA_FC211(DA_FC1197, 0, 1);
	DA_FC211(DA_FC1190, 0, 1);
	DA_FC211(DA_FC110, 0, 6);
	DA_FC211(DA_FC406, 0, 1);
	DA_FC211(DA_FC360, 0, 2);
	DA_FC211(DA_FC439, 0, 9);
	DA_FC211(DA_FC440, 0, 9);
	DA_FC211(DA_FC441, 0, 9);
	DA_FC211(DA_FC442, 0, 9);
	DA_FC211(DA_FC443, 0, 9);
	DA_FC211(DA_FC444, 0, 9);
	DA_FC211(DA_FC445, 0, 9);
	DA_FC211(DA_FC446, 0, 9);
	DA_FC211(DA_FC382,    0, 1);
	DA_FC211(DA_FC383,    0, 1);
	DA_FC211(DA_FC384,     0, 1);
	DA_FC211(DA_FC395,     60, 500);
	DA_FC211(DA_FC396,     60, 500);
	DA_FC211(DA_FC397,    0, 1);
	DA_FC211(DA_FC398, 0, 1);
	DA_FC211(DA_FC399,     50, 250);
	DA_FC211(DA_FC400,     100, 850);
	DA_FC211(DA_FC401, 0, 1);
	DA_FC211(DA_FC402,    0, 1);
	DA_FC211(DA_FC403,        80, 500);
	DA_FC211(DA_FC404,        80, 500);
	DA_FC211(DA_FC405,       0, 1);
	DA_FC211(DA_FC455 ,          -2500,2500);
	DA_FC211(DA_FC29,           0,1);
	DA_FC211(DA_FC475,           0,1);
	DA_FC211(DA_FC473,           0,1);
	}
// Function used to reset the SPVAR state to where we begin, this one you can change if you like, the rest you should leave as-is or you risk breaking the logic of this. YOU HAVE BEEN WARNED!
function DA_FC188() {
	DA_FC976 = SPVAR_4;
	// Change this to say where it's safe to start storing data
	DA_FC977 = 0;
	// Should always be 0, unless you're using part of the first SPVAR in which case you should also change the next line to include the value you are storing in the bits you are using
	DA_FC988 = 0;
	}
// ------ DO NOT TOUCH ANYTHING BELOW THIS LINE UNLESS YOU KNOW WHAT YOU ARE DOING! ------ 
int DA_FC977, // Variable used to keep track of the next available bit
DA_FC976,// Variable used to keep track of the currently used SPVAR slot
DA_FC988,// Variable used to keep track of the current value with all the bits from the previous variables saved in the current SPVAR
DA_FC978,// Variable used temporarily during the various calculation steps
DA_FC986;
// Variable used to keep track of the number of bits required to represent the currently saved/loaded variable
// Function used to count the number of bits used by the given value
function DA_FC189(DA_FC190) {
	DA_FC978 = 0;
	// We need to start at 0, we use spvar_tmp here as we need to track the bits during our loop below
	while (DA_FC190) {
		// Loop while val is anything but 0
		DA_FC978++;
		// Increment the bit count by 1
		DA_FC190 = abs(DA_FC190 >> 1);
		// Shift the value down 1 bit, once we have no more bits set this will result in 0, unless the value is negative - in which case this will be endless, we do abs here to make it always
	}
	return DA_FC978;
	}
// Function used to count the number of bits used by 2 given values
function DA_FC191(DA_FC192, DA_FC193) {
	DA_FC978 = max(DA_FC189(DA_FC192), DA_FC189(DA_FC193));
	// Get the highest bit count required for either min or max
	if (DA_FC194(DA_FC192, DA_FC193)) {
		// Check if we need to know if the value is negative or not
		DA_FC978++;
		// If we need to track if the saved value is negative, we need 1 bit for that specifically - the others are used to store the actual value
	}
	return DA_FC978;
	}
// Function used to determine if either of 2 given values is negative
function DA_FC194(DA_FC192, DA_FC193) {
	return DA_FC192 < 0 || DA_FC193 < 0;
	}
// Function used to generate a bitmask for the sign bit, this will always be the highest bit in the range we're requesting it for, to do that - we need to start with the lowest bit set and move it up the number of steps there is between 1 and the bits we need, this needs to be a maximum of 31 but can never be negative
function DA_FC197(DA_FC198) {
	return 1 << clamp(DA_FC198 - 1, 0, 31);
	}
// Function used to generate a full bitmask (essentially all bits set up to and including the number of bits given)
function DA_FC199(DA_FC198) {
	if (DA_FC198 == 32) {
		// If we're wanting a bitmask for all bits, we can simply return -1 (which is all bits set to 1)
		return -1;
			}
	return 0x7FFFFFFF >> (31 - DA_FC198);
	// What we do here is basically take a value with all bits except the highest set and shift them down as many times as we need to get a mask that fits the bit count we're looking for
}
// Function used to generate a bitmask for just the bits required for the value part of a signed range, this means all the bits below the sign bit
function DA_FC201(DA_FC198) {
	return DA_FC199(DA_FC198 - 1);
	}
// Function used to pack a value that has potential for being negative in a way that we use the least number of bits we really need to represent the value
function DA_FC203(DA_FC190, DA_FC198) {
	if (DA_FC190 < 0) {
		// Check if we have a negative value, if so - handle it accordingly
		return (abs(DA_FC190) & DA_FC201(DA_FC198)) | DA_FC197(DA_FC198);
		// Get the positive version of the value and keep the bits that are within range of what we're doing and add the sign bit since we have a negative value and return the result
	}
	return DA_FC190 & DA_FC201(DA_FC198);
	// Get the bits that are within our range
}
// Function used to unpack (restore) a value that has potential for being negative, essentially reversing what pack_i does above
function DA_FC206(DA_FC190, DA_FC198) {
	if (DA_FC190 & DA_FC197(DA_FC198)) {
		// Check if the stored value is supposed to ve negative
		return 0 - (DA_FC190 & DA_FC201(DA_FC198));
		// Retrieve the stored positive value and subtract it from 0 (resulting in the same value except negative), return the result
	}
	return DA_FC190 & DA_FC201(DA_FC198);
	// Retrieve the stored positive value and return it
}
// Function used to read the value of a SPVAR without any limits
function DA_FC209(DA_FC210) {
	return get_pvar(DA_FC210, 0x80000000, 0x7FFFFFFF, 0);
	}
// Function used to save your value in the SPVARs, this is the function you'll be calling when saving a value. You need to provide the value to save aswell as the range (minimum and maximum value, this is how we determine how many bits to use when saving this value)
function DA_FC211(DA_FC190, min, max) {
	DA_FC986 = DA_FC191(min, max);
	// Set spvar_bits to the number of bits we need for this range
	DA_FC190 = clamp(DA_FC190, min, max);
	// Make sure the value is within our defined range to begin with
	if (DA_FC194(min, max)) {
		// If either min or max is negative, we need to pack this value as a possibly negative value
		DA_FC190 = DA_FC203(DA_FC190, DA_FC986);
		// Pack as signed value (possibly negative)
	}
	DA_FC190 = DA_FC190 & DA_FC199(DA_FC986);
	// Pack as unsigned value (always positive), this essentially just makes the resulting value not have any extra bits set - it's safe to use after the signed packing since we're not using any bits outside of the unsigned range anyways
	if (DA_FC986 >= 32 - DA_FC977) {
		// Check if there is not enough bits remaining to save this value as-is. if there aren't enough bits, we save what we can here and store the remaining bits in the next spvar, if this means we're hitting the end, we can make this smaller by handling the case where we use all bits here aswell
		DA_FC988 = DA_FC988 | (DA_FC190 << DA_FC977);
		// Add what we can to the current value where there is bits available to use
		set_pvar(DA_FC976, DA_FC988);
		// Save the current SPVAR before advancing to the next one
		DA_FC976++;
		// Move to the next slot
		DA_FC986 -= (32 - DA_FC977);
		// Update the required bits according to our needs for the next slot, if we don't do this here, we'll screw up the saved value by moving it too far out of range
		DA_FC190 = DA_FC190 >> (32 - DA_FC977);
		// Move the remaining bits down, discarding the bits we've already saved
		DA_FC977 = 0;
		// Reset the current bit counter since we're starting with a new SPVAR
		DA_FC988 = 0;
		// Reset our value so we start clean, we aren't currently using any bits anyways
	}
	DA_FC988 = DA_FC988 | (DA_FC190 << DA_FC977);
	// Merge the current SPVAR value with our currently value where there is space to keep our value
	DA_FC977 += DA_FC986;
	// Move up the counter of next available bit to where we are currently saving data at
	if (!DA_FC977) {
		DA_FC988 = 0;
		// Reset our value so we start clean, we aren't currently using any bits anyways
	}
	set_pvar(DA_FC976, DA_FC988);
	// Save the SPVAR with the current value, this won't write anything to flash unless the value changed - so we can do this for each variable saved to no risk missing anything
}
// Function used to read your value from the SPVARs, this is the function you'll be calling when reading a value. You need to provide the range (minimum and maximum value, this is how we determine how many bits to use when reading the value) aswell as a default value if what we read is out of range
function DA_FC213(min, max, DA_FC214) {
	DA_FC986 = DA_FC191(min, max);
	// Set spvar_bits to the number of bits we need for this range
	DA_FC988 = (DA_FC209(DA_FC976) >> DA_FC977) & DA_FC199(DA_FC986);
	// Read the current SPVAR value from flash and shift them into position, we'll handle split values next
	if (DA_FC986 >= 32 - DA_FC977) {
		// Check if we are dealing with a split SPVAR value, essentially if the current position means we're using more than 32 bits in the SPVAR, we need to retrieve the missing bits from the next SPVAR and put them back to our current value, we use the same space saving trick here as in the save function
		DA_FC988 = (DA_FC988 & DA_FC199(32 - DA_FC977)) | ((DA_FC209(DA_FC976 + 1) & DA_FC199(DA_FC986 - (32 - DA_FC977))) << (32 - DA_FC977));
		//Below is a breakdown of the line above, with each step done one at a time instead of all at once - this however increases codesize - the below code is to explain how it all works tho
//spvar_tmp = read_spvar_slot(spvar_current_slot + 1); // Read the SPVAR slot coming after the initial one we used to spvar_tmp from flash, we need to maintain the data we've read thus far, but also add on what we have in flash for the next SPVAR
//spvar_tmp = spvar_tmp & make_full_mask(spvar_bits - (32 - spvar_current_bit)); // Extract the bits we need need (the ones that didn't fit in the previous SPVAR)
//spvar_tmp = spvar_tmp << (32 - spvar_current_bit); // Move the bits into their original position, they were stored at the beginning of the new SPVAR but belong at the top of the currently read value
//spvar_current_value = (spvar_current_value & make_full_mask(32 - spvar_current_bit)) | spvar_tmp; // put all bits together again with the part read from the first SPVAR cleaned up to only include the bits from this variable/value and not all bits set in the upper range like they normally are
	}
	DA_FC977 += DA_FC986;
	// Move up the counter of next available bit to where we are will be reading data from next
	DA_FC988 = DA_FC988 & DA_FC199(DA_FC986);
	// Extract all bits included for this value and discard any other bits
	if (DA_FC977 >= 32) {
		DA_FC976++;
		// Move to the next SPVAR slot
		DA_FC977 -= 32;
		// Remove 32 from the spvar_current_bit tracker since we've gone beyond what we can do here
	}
	if (DA_FC194(min, max)) {
		// Check if the value can be negative and handle it accordingly
		DA_FC988 = DA_FC206(DA_FC988, DA_FC986);
		// Restore the signed, possibly negative value
	}
	if (DA_FC988 < min || DA_FC988 > max) {
		// Check if the value is below our specified min or above our specified max, if so - return the default value instead
		return DA_FC214;
		// This can be changed to min instead as a reasonable default with the default parameter being removed if you don't need to have a override value for the default when out of range, that will save a bit of code size
	}
	// Return the retrieved value to the user since it's within the expected range
	return DA_FC988;
	}
const string DA_FC995 = "SETTINGS";
const string DA_FC996 = "WAS SAVED";
combo DA_FC75 {
	vm_tctrl(0);
	wait( 20);
	cls_oled(0);
	DA_FC187();
	print(15, 2, OLED_FONT_MEDIUM, 1, DA_FC995[0]);
	print(10, 23, OLED_FONT_MEDIUM, 1, DA_FC996[0]);
	DA_FC997 = 1500;
	combo_run(DA_FC76);
	}
int DA_FC997 = 1500;
combo DA_FC76 {
	vm_tctrl(0);
	wait( DA_FC997);
	cls_oled(0);
	DA_FC358 = FALSE;
	}
//--------------------------------------------------------------   
define DA_FC998 = 0;
define DA_FC999 = 1;
define DA_FC1000 = 2;
define DA_FC1001 = 3;
define DA_FC1002 = 4;
define DA_FC1003 = 5;
define DA_FC1004 = 6;
define DA_FC1005 = 7;
int DA_FC667;
int DA_FC1007;
int DA_FC1008, DA_FC664;
// Direction of Left Stick         
int DA_FC478;
int DA_FC1011 = 50;
int DA_FC1012 = 200;
int DA_FC1013 = TRUE;
combo DA_FC77 {
	set_val(DA_FC448, 0);
	set_val(PS5_L3, 100);
	set_val(PS5_R3, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC448, 0);
	vm_tctrl(0);
	wait( 120);
	if (DA_FC427) DA_FC259();
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 50);
	}
///////////FK Combos//////////
int DA_FC609;
int DA_FC616;
combo DA_FC78 {
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, -100);
	vm_tctrl(0);
	wait( 70);
	if (DA_FC616) set_val(XB1_RX, 100);
	else set_val(XB1_RX, -100);
	set_val(XB1_RY, 100);
	vm_tctrl(0);
	wait( 2000);
	if (DA_FC616) set_val(XB1_RX, -100);
	else set_val(XB1_RX, 100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 200);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC424);
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, 100);
	set_val(XB1_LY,100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 1200);
	DA_FC609 = FALSE;
	DA_FC243(DA_FC609);
	}
///// Corner combos /////////
int DA_FC618;
int DA_FC619;
combo DA_FC79 {
	if (DA_FC619) set_val(XB1_RX, -100);
	else set_val(XB1_RX, 100);
	set_val(XB1_RY, 100);
	vm_tctrl(0);
	wait( 320);
	vm_tctrl(0);
	wait( 50);
	set_val(XB1_RY, -60);
	vm_tctrl(0);
	wait( 1100);
	vm_tctrl(0);
	wait( 50);
	if (DA_FC619) set_val(XB1_LX, 60);
	else set_val(XB1_LX, -60);
	vm_tctrl(0);
	wait( 120);
	vm_tctrl(0);
	wait( 50);
	set_val(XB1_LY, -100);
	set_val(DA_FC453, 100);
	set_val(DA_FC450, 100);
	set_val(DA_FC451, 100);
	DA_FC1146 = 4000;
	vm_tctrl(0);
	wait( DA_FC425);
	vm_tctrl(0);
	wait( 50);
	set_val(DA_FC453, 100);
	vm_tctrl(0);
	wait( 50);
	DA_FC618 = FALSE;
	DA_FC243(DA_FC618);
	}
int DA_FC1018 = TRUE;
function DA_FC215(DA_FC216) {
	if (DA_FC216) {
		DA_FC1019 = DA_FC1051;
			}
	else {
		DA_FC1019 = DA_FC1050;
			}
	combo_run(DA_FC80);
	}
int DA_FC1019;
combo DA_FC80 {
	DA_FC236(DA_FC1019);
	vm_tctrl(0);
	wait( 300);
	DA_FC236(DA_FC1048);
	vm_tctrl(0);
	wait( 100);
	DA_FC236(DA_FC1019);
	vm_tctrl(0);
	wait( 300);
	DA_FC236(DA_FC1048);
	}
// Define constants for the AA strength && stick threshold
define DA_FC1023 = 100;
define DA_FC1024 = 130;
const string DA_FC547 = "SCRIPT WAS";
function DA_FC217(DA_FC127, DA_FC219, DA_FC220) {
	if (!DA_FC352 && !DA_FC353) {
		// Clear OLED Screen                          
		cls_oled(0);
		// will clear oled   
		print(DA_FC219, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC220);
		if (DA_FC127) {
			print(DA_FC221(sizeof(DA_FC1028) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA_FC1028[0]);
			//MOD is ON
		}
		else {
			print(DA_FC221(sizeof(DA_FC1029) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA_FC1029[0]);
			// MOD is OFF
		}
		DA_FC215(DA_FC127);
			}
	}
function DA_FC221(DA_FC142, DA_FC136) {
	return (OLED_WIDTH / 2) - ((DA_FC142 * DA_FC136) / 2);
	}
const string DA_FC1029 = "OFF";
const string DA_FC1028 = "ON";
//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================
function DA_FC224(DA_FC133, DA_FC226, DA_FC127) {
	cls_oled(0);
	line_oled(1, 18, 127, 18, 1, 1);
	print(DA_FC133, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC226);
	DA_FC231(DA_FC127, DA_FC234(DA_FC127));
	DA_FC356 = TRUE;
	}
const string DA_FC589 = "EA PING";
const string DA_FC611 = "FK_POWER";
const string DA_FC603 = "MaxFnshPwr"const string DA_FC595 = "JK_Agg";
int DA_FC585;
int DA_FC602;
/*   
=================================================================
Center X Function (Made By Batts) 
=================================================================
*/
function DA_FC228(DA_FC142, DA_FC136) {
	return (OLED_WIDTH / 2) - ((DA_FC142 * DA_FC136) / 2);
	}
/*
=================================================================
NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/
int DA_FC1038;
int DA_FC1039, DA_FC1040;
function DA_FC231(DA_FC127, DA_FC159) {
	DA_FC1038 = 1;
	DA_FC1040 = 10000;
	if (DA_FC127 < 0) {
		//--neg numbers
		putc_oled(DA_FC1038, 45);
		//--add leading "-"
		DA_FC1038 += 1;
		DA_FC127 = abs(DA_FC127);
			}
	for (DA_FC1039 = 5;
	DA_FC1039 >= 1;
	DA_FC1039--) {
		if (DA_FC159 >= DA_FC1039) {
			putc_oled(DA_FC1038, (DA_FC127 / DA_FC1040) + 48);
			DA_FC127 %= DA_FC1040;
			DA_FC1038++;
			if (DA_FC1039 == 4) {
				putc_oled(DA_FC1038, 44);
				//--add ","
				DA_FC1038++;
							}
					}
		DA_FC1040 /= 10;
			}
	puts_oled(DA_FC228(DA_FC1038 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA_FC1038 - 1, OLED_WHITE);
	}
int DA_FC1044;
function DA_FC234(DA_FC235) {
	DA_FC1044 = 0;
	do {
		DA_FC235 /= 10;
		DA_FC1044++;
			}
	while (DA_FC235);
	return DA_FC1044;
	}
int DA_FC625;
define DA_FC1048 = 0;
define DA_FC1049 = 1;
define DA_FC1050 = 2;
define DA_FC1051 = 3;
define DA_FC1052 = 4;
define DA_FC1053 = 5;
define DA_FC1054 = 6;
define DA_FC1055 = 7;
const int16 data[][] = {
	{
		0,    0,    0  
	}
	, //0. ColorOFF
	  {
		0,    0,    255  
	}
	, //1. Blue     
	  {
		255,    0,    0  
	}
	, //2. Red      
	  {
		0,    255,    0  
	}
	, //3. Green    
	  {
		255,    0,    255  
	}
	, //4. Pink     
	  {
		0,    255,    255  
	}
	, //5. SkyBlue 
	  {
		255,    255,    0  
	}
	, //6. Yellow   
	  {
		255,    255,    255  
	}
	//7. White    
}
;
int DA_FC1056;
function DA_FC236(DA_FC169) {
	for (DA_FC1056 = 0;
	DA_FC1056 < 3;
	DA_FC1056++) {
		set_rgb(data[DA_FC169][0], data[DA_FC169][1], data[DA_FC169][2]);
			}
	}
const int8 DA_FC1327[] = {
	XB1_XBOX, // 0 
	  XB1_VIEW, // 1 
	  XB1_MENU, // 2 
	  PS5_R1, // 3 
	  PS5_R2, // 4 
	  XB1_RS, // 5 
	  PS5_L1, // 6 
	  PS5_L2, // 7 
	  XB1_LS, // 8 
	  PS5_UP, // 9 
	  PS5_DOWN, // 10  
	  PS5_LEFT, // 11  
	  PS5_RIGHT, // 12  
	  XB1_Y, // 13 
	  XB1_B, // 14 
	  XB1_A, // 15 
	  XB1_X, // 16 
	  XB1_PR1, // 17 
	  XB1_PR2, // 18 
	  XB1_PL1, // 19 
	  XB1_PL2, // 20 
	  PS5_TOUCH // 21  
}
int DA_FC628 = PS5_L3;
define DA_FC1058 = 1;
define DA_FC1059 = 2;
define DA_FC1060 = 3;
define DA_FC1061 = 2;
define DA_FC1062 = 3;
define DA_FC1063 = 4;
define DA_FC1064 = 5;
define DA_FC1065 = 6;
define DA_FC1066 = 7;
define DA_FC1067 = 8;
define DA_FC1068 = 9;
int DA_FC622 = FALSE;
// --- Right Stick Skills ( OLD _School)
int DA_FC1070;
int DA_FC1071;
int DA_FC1072;
int DA_FC1073;
define DA_FC1074 = PS5_LX;
define DA_FC1075 = PS5_LY;
define DA_FC1076 = PS5_RX;
define DA_FC1077 = PS5_RY;
function DA_FC238 () {
	if(!get_ival(XB1_RS) &&  !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450)) {
		// all Skills mode ){ 
//  Right Stick -->  UP
		if( get_ival(PS5_RY) < -70  && !DA_FC1070 && !combo_running(DA_FC0) ) {
			DA_FC1070 = TRUE;
			DA_FC478 = FALSE;
			DA_FC1007 = DA_FC275;
			            DA_FC241(DA_FC275);
		}
		//  Right Stick -->  DOWN
		if( get_ival(PS5_RY) >  70  && !DA_FC1071 && !combo_running(DA_FC0)) {
			DA_FC1071 = TRUE;
			DA_FC478 = TRUE;
			DA_FC1007 = DA_FC277;
			           DA_FC241(DA_FC277);
		}
		//  Right Stick --> LEFT
		if( get_ival(PS5_RX) < -70  && !DA_FC1072 && !combo_running(DA_FC0) ) {
			DA_FC1072 = TRUE;
			DA_FC478 = FALSE;
			DA_FC1007 = DA_FC278;
			              DA_FC241(DA_FC278);
		}
		// Right Stick --> RIGHT
		if( get_ival(PS5_RX) >  70  && !DA_FC1073 && !combo_running(DA_FC0) ) {
			DA_FC1073 = TRUE;
			DA_FC478 = TRUE;
			DA_FC1007 = DA_FC276;
			            DA_FC241(DA_FC276);
		}
			}
	//------------------------------------------------------
	if(abs(get_ival(PS5_RY))<20  && abs(get_ival(PS5_RX))<20){
		DA_FC1070 = 0;
		DA_FC1071  = 0;
		DA_FC1072  = 0;
		DA_FC1073  = 0;
			}
	}
function DA_FC239() {
	//1.1. RS = LS zone  
	if (DA_FC1105 == DA_FC587) {
		DA_FC478 = FALSE;
		// use One Way Skills
		if (DA_FC386) DA_FC241(DA_FC386);
			}
	//1.4. RS = opposite of LS zone  
	if (DA_FC1105 == DA_FC246(DA_FC587 + 4)) {
		// right_on does !matter here
//1.1.0. if LS --> UP (zone 0)
		DA_FC478 = FALSE;
		// use One Way Skills
		if (DA_FC393) DA_FC241(DA_FC393);
			}
	//-------------------
//1.2. RS = LS zone +1/-1
	if (DA_FC1105 == DA_FC246(DA_FC587 + 1)) {
		DA_FC478 = TRUE;
		if (DA_FC388) DA_FC241(DA_FC388);
			}
	if (DA_FC1105 == DA_FC246(DA_FC587 - 1)) {
		DA_FC478 = FALSE;
		if (DA_FC387) DA_FC241(DA_FC387);
			}
	//1.3. RS = LS zone +2/-2
	if (DA_FC1105 == DA_FC246(DA_FC587 + 2)) {
		DA_FC478 = TRUE;
		// use One Way Skills
		if (DA_FC390) DA_FC241(DA_FC390);
			}
	if (DA_FC1105 == DA_FC246(DA_FC587 - 2)) {
		DA_FC478 = FALSE;
		// use One Way Skills
		if (DA_FC389) DA_FC241(DA_FC389);
			}
	if (DA_FC1105 == DA_FC246(DA_FC587 + 3)) {
		DA_FC478 = TRUE;
		// use One Way Skills
		if (DA_FC392) DA_FC241(DA_FC392);
			}
	if (DA_FC1105 == DA_FC246(DA_FC587 - 3)) {
		DA_FC478 = FALSE;
		// use One Way Skills
		if (DA_FC391) DA_FC241(DA_FC391);
			}
	}
int DA_FC1090;
int DA_FC497 = 0;
function DA_FC240() {
	if(DA_FC1090){
		DA_FC497 += get_rtime();
			}
	//set_val(TRACE_2,timer_RS);
	if(DA_FC497 >= 3000){
		DA_FC497 = 0;
		DA_FC1090 = FALSE;
			}
	if (!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449)) {
		// all Skills mode ){ 
		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA_FC499 && !combo_running(DA_FC0)) {
			// getting RS zones
			DA_FC499 = TRUE;
			DA_FC1090 = TRUE;
			DA_FC497 = 0;
			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
			DA_FC239();
					}
		set_val(DA_FC1076, 0);
		set_val(DA_FC1077, 0);
			}
	//--- reset when RS is release
	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
		DA_FC499 = FALSE;
			}
	}
function DA_FC241(DA_FC242) {
	DA_FC1007 = DA_FC242;
	DA_FC216[-327 + (DA_FC242 * 3)] = TRUE;
	DA_FC1013 = FALSE;
	block = TRUE;
	}
int DA_FC1096;
combo DA_FC81 {
	set_rumble(DA_FC1096, 100);
	vm_tctrl(0);
	wait( 300);
	reset_rumble();
	vm_tctrl(0);
	wait( 20);
	}
function DA_FC243(DA_FC127) {
	if (DA_FC127) DA_FC1096 = RUMBLE_A;
	else DA_FC1096 = RUMBLE_B;
	combo_run(DA_FC81);
	}
int DA_FC1097 = 300;
int DA_FC1098 ;
combo DA_FC82 {
	DA_FC1098 = TRUE;
	//set_val(TRACE_1,tap);
	vm_tctrl(0);
	wait( DA_FC1097);
	// wait for second tap 
	DA_FC1098 = FALSE;
	}
///////////////////////////////
// Ball Roll   
combo DA_FC83 {
	vm_tctrl(0);
	wait( 45);
	set_val(PS5_RX, 0);
	set_val(PS5_RY, 0);
	vm_tctrl(0);
	wait( 160);
	}
//TR Combos //
combo DA_FC84 {
	DA_FC245();
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 20);
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 100);
	DA_FC253(0, 0);
	set_val(DA_FC452, 100);
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 60);
	DA_FC253(0, 0);
	vm_tctrl(0);
	wait( 150);
	DA_FC1013 = TRUE;
	vm_tctrl(0);
	wait( 350);
	}
function DA_FC245() {
	DA_FC667 = DA_FC587  DA_FC248(DA_FC667);
	DA_FC1008 = DA_FC1102;
	DA_FC664 = DA_FC665;
	}
combo DA_FC85 {
	set_val(DA_FC451, 100);
	set_val(DA_FC450, 100);
	vm_tctrl(0);
	wait( 100);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	wait( 100);
	DA_FC1013 = TRUE;
	vm_tctrl(0);
	wait( 350);
	}
const int8 DA_FC1328[][] = {
	//  X,    Y
{
		0,    -99  
	}
	, // 0 UP
	  {
		98,    -100  
	}
	, // 1 Up-Right
	  {
		97,    0  
	}
	, // 2 Right
	  {
		96,    99  
	}
	, // 3 Down right
	  {
		0,    99  
	}
	, // 4 Down
	  {
		-96,    98  
	}
	, // 5 Down Left
	  {
		-95,    0  
	}
	, // 6 Left
	  {
		-94,    -96  
	}
	// 7 Left Up
}
;
int DA_FC1102, DA_FC665, DA_FC587;
int DA_FC1105;
int DA_FC499;
int DA_FC1107;
function DA_FC246(DA_FC247) {
	DA_FC1107 = DA_FC247;
	if (DA_FC1107 < 0) DA_FC1107 = 8 - abs(DA_FC247);
	else if (DA_FC1107 >= 8) DA_FC1107 = DA_FC247 - 8  return DA_FC1107;
	}
function DA_FC248(DA_FC249) {
	if (DA_FC249 < 0) DA_FC249 = 8 - abs(DA_FC249);
	else if (DA_FC249 >= 8) DA_FC249 = DA_FC249 - 8;
	DA_FC1102 = DA_FC1328[DA_FC249][0];
	// X
	DA_FC665 = DA_FC1328[DA_FC249][1];
	// Y
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function DA_FC250(DA_FC251, DA_FC252) {
	set_val(DA_FC1076, DA_FC251);
	set_val(DA_FC1077, DA_FC252);
	}
function DA_FC253(DA_FC166, DA_FC167) {
	set_val(DA_FC1074, DA_FC166);
	set_val(DA_FC1075, DA_FC167);
	}
function DA_FC256() {
	if (DA_FC478) {
		// right      
		set_val(DA_FC1074, inv(DA_FC664));
		set_val(DA_FC1075, DA_FC1008);
			}
	else {
		//  left       
		set_val(DA_FC1074, DA_FC664);
		set_val(DA_FC1075, inv(DA_FC1008));
			}
	}
function DA_FC257() {
	if (DA_FC478) {
		// right          
		set_val(DA_FC1076, inv(DA_FC664));
		set_val(DA_FC1077, DA_FC1008);
			}
	else {
		//  left               
		set_val(DA_FC1076, DA_FC664);
		set_val(DA_FC1077, inv(DA_FC1008));
			}
	}
function DA_FC258() {
	if (!DA_FC478) {
		// right             
		set_val(DA_FC1076, inv(DA_FC664));
		set_val(DA_FC1077, DA_FC1008);
			}
	else {
		//  left               
		set_val(DA_FC1076, DA_FC664);
		set_val(DA_FC1077, inv(DA_FC1008));
			}
	}
function DA_FC259() {
	set_val(DA_FC1076, DA_FC1008);
	set_val(DA_FC1077, DA_FC664);
	}
function DA_FC260() {
	set_val(DA_FC1076, inv(DA_FC1008));
	set_val(DA_FC1077, inv(DA_FC664));
	}
function DA_FC261() {
	set_val(DA_FC1076, 0);
	set_val(DA_FC1077, 0);
	}
int DA_FC1123;
function DA_FC262() {
	if ((event_press(DA_FC448)  ) && !combo_running(DA_FC86) && (DA_FC1146  <= 0 || (DA_FC1146 < 3000 && DA_FC1146 > 1  )) && !get_ival(DA_FC452) && DA_FC550 > 500 &&!get_ival(DA_FC451) &&!get_ival(DA_FC447) &&!get_ival(DA_FC450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_ipolar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA_FC86) ) {
		//set_val(PassBtn,0);
		combo_run(DA_FC86);
			}
	if (combo_running(DA_FC86) && (        get_ival(DA_FC452) ||        get_ival(DA_FC451) ||        get_ival(DA_FC447) ||        get_ival(DA_FC450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_ipolar(POLAR_RS,POLAR_RADIUS) >= 1500      )) {
		combo_stop(DA_FC86);
		//set_polar(POLAR_RS,0,0) ;
		DA_FC1261 = TRUE;
			}
	}
combo DA_FC86 {
vm_tctrl(0);
wait(750);
set_val(DA_FC449,100);
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);
if(DA_FC1123 == 1 ){
set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)}
vm_tctrl(0);
wait(60);
vm_tctrl(0);
wait(60);

	}
combo DA_FC87 {
	vm_tctrl(0);
	wait( 800);
	DA_FC1147 = 0;
	}
int DA_FC1124 = 1600;
int DA_FC1125 = 1600;
int DA_FC1126 = 1600;
int DA_FC1127 = TRUE;
// change to FALSE if you don't want optimum power restriction for normal shots (after release sprint) 1 seconds  .
////////////////////////////// - PowerShots - Restrictions /////////////////////////////////
int DA_FC1128 = TRUE;
// change to FALSE if you don't want powershots power restrictions .
int DA_FC659 = FALSE;
// Change to TRUE if you want Single Button Timed Power Shots . 
//int Timed_Power_Shot_Button = PS5_TOUCH; // Change to your desired Single Button Timed Power Shots .
////////////////////////////////DA_FC1190 - Finesse //////////////////////////////////
int DA_FC1130 = TRUE;
// change to FALSE if you want to turn off timed finesse shots .
int DA_FC653 = FALSE;
// Change to TRUE if you want Single Button Timed Finesse Shots . 
//int Timed_FINESSE_Button = PS5_R3; // Change to your desired Single Button Timed Finesse Shots .
///////////////////////////////////////////////////////////////////////////////////
////////////////////////////////DA_FC1190 - Trivela //////////////////////////////////
int DA_FC1132 = TRUE;
// change to FALSE if you want to turn off timed trivela shots .
int DA_FC655 = FALSE;
// Change to TRUE if you want Single Button Timed Finesse Shots . 
//int Timed_Trivela_Shot_Button = XB1_PR2; // Change to your desired Single Button Timed Finesse Shots .
///////////////////////////////////////////////////////////////////////////////////
////////////////////////////////DA_FC1190 - Shots //////////////////////////////////
int DA_FC1134 = TRUE;
// change to FALSE if you want to turn off timed shots .
int DA_FC657 = FALSE;
// Change to TRUE if you want Single Button Timed normal Shots . 
//int Timed_Shot_Button = XB1_PR2; // Change to your desired Single Button Timed normal Shots .
function DA_FC263(){
	if (get_ival(DA_FC449)) {
		DA_FC1136 = 1000;
		DA_FC1155 = 0;
		DA_FC550 = 1;
		combo_stop(DA_FC96);
			}
	if (event_press(DA_FC451) || event_press(DA_FC434)) {
		DA_FC1136 = 4000;
		DA_FC1155 = 0;
		DA_FC1124 = 1600;
			}
	if (get_ival(DA_FC450) && !get_ival(DA_FC449) ) {
		DA_FC1136 = 0;
		DA_FC1155 = 0;
		DA_FC1124 = 1600;
			}
	else if (get_ival(DA_FC449)){
		DA_FC1136 = 1000;
			}
	if (DA_FC1136 > 0) {
		DA_FC1136 -= get_rtime();
			}
	if (DA_FC1136 < 0) {
		DA_FC1136 = 0;
			}
	DA_FC1214 = DA_FC428;
	if (event_release(DA_FC448)) {
		DA_FC1142 = 1;
		DA_FC1155 = 0;
		DA_FC550 = 1;
	}
	if (event_release(DA_FC454)) {
		DA_FC1143 = 1;
		DA_FC1155 = 0;
		DA_FC550 = 1;
	}
	if (event_release(DA_FC449)) {
		DA_FC1125 = 1;
		DA_FC1155 = 0;
		DA_FC1124 = 1600;
			}
	if (event_release(DA_FC450)) {
		DA_FC1126 = 1;
		DA_FC1155 = 0;
		DA_FC1124 = 1600;
			}
	if (event_release(DA_FC453) || (get_ival(DA_FC454) && get_ival(DA_FC449))) {
		DA_FC1146 = 4000;
		DA_FC1155 = 0;
		// after_skill_timer = 1600;
	}
	if (get_ival(DA_FC448) && DA_FC1146 < 4000 && DA_FC1146 > 3500) {
		DA_FC1147 = DA_FC1146;
		DA_FC1146 = 0;
			}
	if (DA_FC1124 < 1510) {
		DA_FC1124 += get_rtime();
			}
	//if(get_ival(PS5_RX) > 25 || get_ival(PS5_RX) < -25 || get_ival(PS5_RY) > 25 || get_ival(PS5_RY) < -25){ set_polar2(POLAR_RS, get_ipolar(POLAR_RS,POLAR_ANGLE),10078)}
//set_val(TRACE_6,tf_accuracy)
	if (DA_FC1125 < 1600) {
		DA_FC1125 += get_rtime();
			}
	if (DA_FC1126 < 1600) {
		DA_FC1126 += get_rtime();
			}
	if (DA_FC1146 > 0) {
		DA_FC1146 -= get_rtime();
			}
	if (DA_FC1146 < 0) {
		DA_FC1146 = 0;
			}
	if (DA_FC1142 < 5100) {
		DA_FC1142 += get_rtime();
			}
	if (DA_FC1143 < 4100) {
		DA_FC1143 += get_rtime();
			}
	if (DA_FC1155 > 0) {
		DA_FC1155 -= get_rtime();
			}
	if (DA_FC1155 < 0) {
		DA_FC1155 = 0;
			}
	if (abs(get_ival(PS5_RX)) > 30 || abs(get_ival(PS5_RY)) > 30) {
		DA_FC1124 = 1;
		DA_FC1155 = 0;
			}
	if (combo_running(DA_FC93)) {
		set_val(DA_FC448, 0);
		if(get_ival(DA_FC448)){
			DA_FC663 = 0;
			combo_stop(DA_FC88);
			set_val(DA_FC448, 0);
			combo_stop(DA_FC93);
			combo_run(DA_FC49);
					}
			}
	if ((combo_running(DA_FC98) || combo_running(DA_FC89))) {
		set_val(DA_FC448, 0);
		if(get_ival(DA_FC448)){
			DA_FC550 = 1;
			DA_FC663 = 0;
			combo_stop(DA_FC88);
			set_val(DA_FC448, 0);
			combo_stop(DA_FC98);
			combo_stop(DA_FC89);
			combo_run(DA_FC49);
					}
			}
	if (event_press(DA_FC447)) {
		combo_run(DA_FC87);
			}
	if (DA_FC550 > 1500) {
		if (DA_FC1125 < 1500) {
			DA_FC1160 = 120;
					}
		if (DA_FC1126 < 1500) {
			DA_FC1160 = 228;
					}
		else {
			DA_FC1160 = 200;
					}
			}
	if (DA_FC550 < 1500) {
		DA_FC1160 = 450;
			}
	if (DA_FC550 > 2700) {
		DA_FC1164 = 920;
			}
	else if (DA_FC550 >= 0 && DA_FC550 < 2700) {
		DA_FC1164 = 725;
			}
	}
function DA_FC264() {
	if (DA_FC1127) {
		if ((DA_FC550 <= 600 || (DA_FC1124 <= 1500 && DA_FC1124 > 1) || ( DA_FC1125 <= 150 || DA_FC1126 <= 150)) && event_press(DA_FC447) ) {
			if (!get_ival(DA_FC450) && !get_ival(DA_FC449) && !get_ival(DA_FC451) && !get_ival(DA_FC452)) {
				set_val(DA_FC447, 0);
				if (DA_FC1146 < 4000 && DA_FC1146 > 1) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC91);
									}
				else {
					set_val(DA_FC447, 0);
					combo_run(DA_FC89);
					DA_FC1155 = 9000;
					//  combo_run(ShotBlock);
				}
							}
					}
			}
	if (DA_FC1134) {
		if (DA_FC550 > 1000 && !DA_FC1155 && (!get_ival(DA_FC450) && !get_ival(PS5_L3) && event_press(DA_FC447)) &&  DA_FC1125 > 150 &&  DA_FC1126 > 150) {
			if (!get_ival(DA_FC449) && !get_ival(DA_FC451)) {
				set_val(DA_FC447, 0);
				if (((DA_FC1143 > 1 && DA_FC1143 <= 2500) || (DA_FC1142 > 1 && DA_FC1142 <= 3000)) &&  DA_FC1124 != 1600) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC90);
					DA_FC1155 = 9000;
									}
				else if (((DA_FC1143 > 2500 && DA_FC1143 <= 4000) || (DA_FC1142 > 3000 && DA_FC1142 <= 3500))  &&  DA_FC1124 != 1600) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC89);
					DA_FC1155 = 9000;
									}
				else if ((DA_FC1146 < 4000 && DA_FC1146 > 1)) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC91);
					DA_FC1155 = 9000;
									}
				else {
					set_val(DA_FC447, 0);
					DA_FC268();
					//  combo_run(ShotBlock);
					DA_FC1155 = 9000;
									}
				DA_FC1155 = 9000;
							}
					}
			}
	//// Timed Power Shots Triggers ////
	if (DA_FC1128) {
		if (get_ival(DA_FC449) && get_ival(DA_FC450)) {
			if (!get_ival(DA_FC451) && !get_ival(DA_FC452) && (DA_FC1146 && DA_FC1142 > 1 && DA_FC1142 <= 1500) || (!DA_FC1146 && DA_FC1142 > 1 && DA_FC1142 <= 1500) || (DA_FC1142 > 1500 && !DA_FC1146) && !DA_FC1155) {
				if (event_press(DA_FC447)) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC99);
					DA_FC1155 = 9000;
									}
							}
					}
			}
	//// Timed Trivela Triggers ////
	if (DA_FC1132) {
		if (!get_ival(DA_FC452) && !get_ival(DA_FC449) && !get_ival(DA_FC450)) {
			if (get_ival(DA_FC451) && get_ival(DA_FC447)) {
				DA_FC270();
				set_val(DA_FC447, 0);
				DA_FC1155 = 9000;
							}
					}
			}
	//// Timed Finesse Triggers ////
	if (DA_FC1130) {
		if (get_ival(DA_FC450) && !get_ival(DA_FC449) && !DA_FC1136) {
			if (!get_ival(DA_FC451) && !get_ival(DA_FC452) && !DA_FC1155) {
				if (get_ival(DA_FC447) && DA_FC550 >= 1000) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC96);
					DA_FC1155 = 9000;
									}
				if (get_ival(DA_FC447) && DA_FC550 < 1000 && !DA_FC1136) {
					set_val(DA_FC447, 0);
					combo_run(DA_FC97);
									}
							}
					}
			}
	if(combo_running(DA_FC91)){
		DA_FC663 = 0;
		combo_stop(DA_FC88)  
	}
	if (get_ival(DA_FC449) || DA_FC1136 > 0) {
		// power shot confliction fix
		combo_stop(DA_FC96);
		combo_stop(DA_FC98);
		combo_stop(DA_FC97);
			}

	if (combo_running(DA_FC89) || combo_running(DA_FC93) || combo_running(DA_FC98) || combo_running(DA_FC99) || combo_running(DA_FC96)) {
		if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452) > 30) {
			combo_stop(DA_FC93);
			combo_stop(DA_FC98);
			combo_stop(DA_FC99);
			combo_stop(DA_FC96);
			combo_stop(DA_FC89);
			DA_FC663 = 0;
			combo_stop(DA_FC88)    
		}
			}
	if (combo_running(DA_FC89) || combo_running(DA_FC90)) {
		if (get_ival(DA_FC448) || (get_ival(PS5_L2) && get_ival(PS5_R2)) || get_ival(DA_FC452)) {
			combo_stop(DA_FC91);
			combo_stop(DA_FC90);
			combo_stop(DA_FC89);
			DA_FC663 = 0;
			combo_stop(DA_FC88)    
		}
			}
	if (event_press(DA_FC447) && DA_FC1155 > 100 && DA_FC1155 < 8990) {
		set_val(DA_FC447, 0);
		combo_stop(DA_FC93);
		combo_stop(DA_FC98);
		combo_stop(DA_FC99);
		combo_stop(DA_FC96);
		combo_stop(DA_FC89);
		DA_FC663 = 0;
		combo_stop(DA_FC88)    combo_run(DA_FC92);
			}
	//set_val(TRACE_3,shooting_timer);
/// avoid conflictions with (instant skills ) mod . 
	if (!DA_FC659) {
		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
			combo_stop(DA_FC99);
					}
			}
	if (!DA_FC653) {
		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
			combo_stop(DA_FC96);
					}
			}
	if (!DA_FC655) {
		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
			combo_stop(DA_FC93);
			DA_FC663 = 0;
			combo_stop(DA_FC88)    
		}
			}
	if (!DA_FC657) {
		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) {
			combo_stop(DA_FC98);
					}
			}
	if ((get_ival(DA_FC452) || get_ival(DA_FC448)) && !DA_FC365) {
		combo_stop(DA_FC4);
		combo_stop(DA_FC47);
		combo_stop(DA_FC33);
			}
	}
define DA_FC1168 = 15;
define DA_FC1169 = 15;
int DA_FC1170 = 0;
define DA_FC1171 = 8000;
// define the hold time in milliseconds
define DA_FC1172 = 4;
define DA_FC1173 = 2000;
// delay in milliseconds
int DA_FC663 = 0;
// Declare the specified array of allowed angles outside of main {}
const int16 DA_FC1329[] = {
	15, 20, 25 ,30,35    ,145,150 , 155, 160,165 ,    195, 200,205, 210,215,325  ,330, 335, 340,345
}
;
const int16 DA_FC1330[] = {
	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305
}
;
int DA_FC1175 = FALSE;
//int i;
int DA_FC1176;
int DA_FC1177;
// Declare the currentAngle variable outside of main {}
int DA_FC1178;
// Declare the difference variable outside of main {}
int DA_FC1179;
function DA_FC265 () {
	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) {
		// Get the current angle from the left stick
		DA_FC1178 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
		// Reset angleAllowed to FALSE at the beginning of each loop iteration
		DA_FC1175 = FALSE;
		// Iterate through the allowed angles array
		for ( DA_FC946 = 0;
		DA_FC946 < sizeof(DA_FC1330) / sizeof(DA_FC1330[0]);
		DA_FC946++) {
			if (DA_FC1178 == DA_FC1330[DA_FC946]) {
				DA_FC1175 = TRUE;
				break;
							}
					}
		// If the angle is not allowed, find the nearest allowed angle
		if (!DA_FC1175) {
			DA_FC1176 = DA_FC1330[0];
			DA_FC1177 = abs(DA_FC1178 - DA_FC1330[0]);
			for ( DA_FC946 = 1;
			DA_FC946 < sizeof(DA_FC1330) / sizeof(DA_FC1330[0]);
			DA_FC946++) {
				DA_FC1179 = abs(DA_FC1178 - DA_FC1330[DA_FC946]);
				if (DA_FC1179 < DA_FC1177) {
					DA_FC1176 = DA_FC1330[DA_FC946];
					DA_FC1177 = DA_FC1179;
									}
							}
			// Set the nearest allowed angle as the output
			set_polar(POLAR_LS, DA_FC1176, 32767);
					}
			}
	// Your additional logic or main loop code can go here
}
function DA_FC266 () {
	if (get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000) {
		// Get the current angle from the left stick
		DA_FC1178 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
		// Reset angleAllowed to FALSE at the beginning of each loop iteration
		DA_FC1175 = FALSE;
		// Iterate through the allowed angles array
		for ( DA_FC946 = 0;
		DA_FC946 < sizeof(DA_FC1329) / sizeof(DA_FC1329[0]);
		DA_FC946++) {
			if (DA_FC1178 == DA_FC1329[DA_FC946]) {
				DA_FC1175 = TRUE;
				break;
							}
					}
		// If the angle is not allowed, find the nearest allowed angle
		if (!DA_FC1175) {
			DA_FC1176 = DA_FC1329[0];
			DA_FC1177 = abs(DA_FC1178 - DA_FC1329[0]);
			for ( DA_FC946 = 1;
			DA_FC946 < sizeof(DA_FC1329) / sizeof(DA_FC1329[0]);
			DA_FC946++) {
				DA_FC1179 = abs(DA_FC1178 - DA_FC1329[DA_FC946]);
				if (DA_FC1179 < DA_FC1177) {
					DA_FC1176 = DA_FC1329[DA_FC946];
					DA_FC1177 = DA_FC1179;
									}
							}
			// Set the nearest allowed angle as the output
			set_polar(POLAR_LS, DA_FC1176, 32767);
					}
			}
	// Your additional logic or main loop code can go here
}
int DA_FC1190;
function DA_FC267() {
	if (combo_running(DA_FC88) && (        get_ival(DA_FC452) ||        get_ival(DA_FC447) ||        get_ival(DA_FC448) ||        get_ival(DA_FC453) ||        get_ival(DA_FC454) ||        get_ival(DA_FC449)      )) {
		combo_stop(DA_FC88);
		DA_FC663 = 0;
			}
	if (DA_FC1170 == 0) {
		if ( ( DA_FC1146 == 0 && !combo_running(DA_FC91) && !combo_running(DA_FC99) && get_ipolar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( event_press(DA_FC447) || (DA_FC663 == 1 ||     combo_running(DA_FC97) || combo_running(DA_FC95)|| combo_running(DA_FC93) ||  combo_running(DA_FC96)     || combo_running(DA_FC89) || combo_running(DA_FC98) || combo_running(DA_FC90) ||  combo_running(DA_FC92)  ))     ) {
			if(DA_FC437)DA_FC266 ();
			else if (DA_FC438)DA_FC265 ();
			combo_restart(DA_FC88);
					}
			}
	else{
		combo_stop(DA_FC88);
			}
	}
combo DA_FC88 {
	if(DA_FC437)DA_FC266 ();
	else if (DA_FC438)DA_FC265 ();
	vm_tctrl(0);
	wait( 4000);
	DA_FC663 = 0;
	}
combo DA_FC89 {
	set_val(DA_FC449,0);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( random(210, 215) + DA_FC430);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait(600);
	wait( 2000);
	}
function DA_FC268() {
	if (DA_FC550 > 600 && DA_FC550 <= 800) {
		DA_FC1191 = 240;
			}
	if (DA_FC550 > 800 && DA_FC550 <= 1000) {
		DA_FC1191 = 230;
			}
	if (DA_FC550 > 1000 && DA_FC550 <= 1500) {
		DA_FC1191 = 225;
			}
	if (DA_FC550 > 1500 && DA_FC550 <= 2000) {
		DA_FC1191 = 235;
			}
	if (DA_FC550 > 2000) {
		DA_FC1191 = 218;
			}
	combo_run(DA_FC98);
	}
combo DA_FC90 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( random(170, 190));
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 500);
	}
combo DA_FC91 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 205);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 300);
	}
combo DA_FC92 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 190);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 400);
	}
int DA_FC1196;
int DA_FC1197;
int DA_FC29;
int DA_FC475;
int DA_FC473;
int DA_FC1201;
int DA_FC1202;
function DA_FC269() {
	}
combo DA_FC93 {
	if (DA_FC1197) {
		set_val(DA_FC447, 0);
		DA_FC1201 = 400;
			}
	else {
		DA_FC1201 = 0;
			}
	if (DA_FC1197) {
		DA_FC250(0, DA_FC1202);
		DA_FC253(0, 0);
			}
	vm_tctrl(0);
	wait(DA_FC1201);
	if (DA_FC1197) {
		set_val(DA_FC451, 100);
		set_val(DA_FC450, 0);
		DA_FC1201 = 60;
			}
	else {
		set_val(DA_FC450, 100);
		//set_val(PaceCtrol, 100);
		DA_FC1201 = 60;
			}
	DA_FC269();
	set_val(DA_FC447,0);
	vm_tctrl(0);
	wait(DA_FC1201);
	set_val(DA_FC451, 0);
	set_val(DA_FC450, 0);
	set_val(DA_FC447,0);
	vm_tctrl(0);
	DA_FC269();
	wait(DA_FC1201);
	if (DA_FC1197) {
		DA_FC1201 = 0;
			}
	else {
		DA_FC1201 = 60;
			}
	set_val(DA_FC450, 0);
	set_val(DA_FC451, 0);
	set_val(DA_FC447,0);
	wait(DA_FC1201);
	//shot//
	vm_tctrl(0);
	set_val(DA_FC447, 100);
	set_val(DA_FC451, 100);
	DA_FC269();
	wait(random(265, 268) +   DA_FC429 );
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	if (DA_FC1197) {
		DA_FC1201 = 25;
			}
	else {
		DA_FC1201 = 30;
			}
	DA_FC269();
	wait(random(0,2) + DA_FC1215 + DA_FC1214 + DA_FC1201 );
	set_val(DA_FC451, 100);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	DA_FC269();
	wait(random(0,2) + 60);
	// TIMED Action
	vm_tctrl(0);
	set_val(DA_FC447, 0);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	DA_FC269();
	wait(random(0,2) + 80);
	vm_tctrl(0);
	set_val(DA_FC451, 100);
	vm_tctrl(0);
	DA_FC269();
	wait(2500);
	}
int DA_FC1143;
int DA_FC1146;
int DA_FC550;
int DA_FC1142;
int DA_FC1214;
// 
int DA_FC1215 = 111;
// 
//const int16 Finishing_Anlges[] = {60,300,120,240}; 
int DA_FC1136;
int DA_FC1147;
int DA_FC1218;
function DA_FC270() {
	DA_FC1218 = 360 - get_polar(POLAR_LS, POLAR_ANGLE);
	if ((DA_FC1218 > 10 && DA_FC1218 < 80) || (DA_FC1218 > 110 && DA_FC1218 < 170)) {
		DA_FC1202 = 100;
			}
	if ((DA_FC1218 < 350 && DA_FC1218 > 280) || (DA_FC1218 < 260 && DA_FC1218 > 190)) {
		DA_FC1202 = -100;
			}
	if (DA_FC1146 == 0 && (DA_FC550 >= 750 || ((DA_FC1147 > 3000 && DA_FC1142 > 1 && DA_FC1142 < 5000)))) {
		if (DA_FC550 <= 2000 && DA_FC1142 > 1500) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 170;
			//Trivela_ROLL = TRUE;
		}
		if (DA_FC550 <= 2000 && DA_FC1142 > 1 && DA_FC1142 <= 1500) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 202;
					}
		if (DA_FC550 > 2000 || (DA_FC1142 > 1 && DA_FC1142 <= 1500)) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 151;
					}
		if ((DA_FC550 > 2000 && DA_FC1142 > 1500) || DA_FC1147 > 1 && DA_FC1142 > 1) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 152;
					}
		if ((DA_FC550 < 2000 && DA_FC1142 > 1500) || DA_FC1146 > 1 && DA_FC1142 > 1) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 149;
					}
		if (DA_FC1142 > 1500) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 148;
					}
		if (!DA_FC550 > 2000 && DA_FC1147 > 1 && DA_FC1142 > 1 && DA_FC1142 <= 1500) {
			set_val(DA_FC447, 0);
			DA_FC1215 = 147;
					}
		set_val(DA_FC447, 0);
		combo_stop(DA_FC98);
		combo_stop(DA_FC99);
		combo_stop(DA_FC96);
		combo_stop(DA_FC89);
		combo_stop(DA_FC95);
		combo_stop(DA_FC92);
		combo_stop(DA_FC91);
		combo_run(DA_FC93);
			}
	else {
		if (DA_FC1146) {
			set_val(DA_FC447, 0);
			combo_run(DA_FC94);
					}
		else {
			if (DA_FC550 < 750) {
				set_val(DA_FC447, 0);
				combo_run(DA_FC95);
							}
					}
			}
	/*
  set_val(TRACE_1,DYN_Acc);
  set_val(TRACE_2,Pass_Timer);
  set_val(TRACE_3,TRACER);
  set_val(TRACE_4,cross_timer); 
  */
}
combo DA_FC94 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait(random(0, 6) + random(200, 205));
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait(random(0, 6) + 700);
	}
combo DA_FC95 {
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( random(200, 205) + DA_FC429 )  set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 700);
	}
int DA_FC1222 = 257;
int DA_FC1160 = 150;
// time between shot and the timed shot ((pause time)).
int DA_FC1224 = 0;
combo DA_FC96 {
	set_val(DA_FC451, 100);
	set_val(DA_FC450, 0);
	set_val(DA_FC447,0);
	vm_tctrl(0);
	DA_FC269();
	wait(random(0,2) + 60);
	set_val(DA_FC451, 0);
	set_val(DA_FC450, 0);
	set_val(DA_FC447,0);
	vm_tctrl(0);
	DA_FC269();
	wait(random(0,2) + 60);
	DA_FC1224 = DA_FC428;
	DA_FC269();
	vm_tctrl(0);
	set_val(DA_FC450, 100);
	set_val(DA_FC447,0);
	wait( 60);
	set_val(DA_FC450, 65);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC1222 + random(-2, 2) +  DA_FC432);
	set_val(DA_FC450, 75);
	set_val(DA_FC447, 0);
	DA_FC1196 = DA_FC1160;
	//set_val(TRACE_3,TRACE_TIME);
	vm_tctrl(0);
	wait( DA_FC1160 + DA_FC1224 - 58 + DA_FC457 );
	// time between shot and the timed shot ((pause time)).
	set_val(DA_FC450, 85);
	if(DA_FC1190)set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(DA_FC450, 100);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 3000);
	}
combo DA_FC97 {
	set_val(DA_FC450, 100);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( 160 + DA_FC432 );
	set_val(DA_FC450, 100);
	set_val(DA_FC447, 0);
	vm_tctrl(0);
	wait( 3000);
	}
int DA_FC1155;
int DA_FC1226 = 220;
int DA_FC1191;
int DA_FC1228 = 0;
combo DA_FC98 {
	DA_FC1228 = DA_FC428;
	set_val(DA_FC447, 100);
	// set_polar(POLAR_LS,my_angle,32767);
	vm_tctrl(0);
	wait( DA_FC1226);
	set_val(DA_FC447, 0);
	// Finish_Aim();
// set_polar(POLAR_LS,aim_host,32767);
	vm_tctrl(0);
	wait( DA_FC1191 + (DA_FC1228) + 22 + DA_FC458);
	if(DA_FC1190)set_val(DA_FC447, 100);
	//Finish_Aim();
//set_polar(POLAR_LS,aim_host,32767);
	vm_tctrl(0);
	wait( DA_FC1226);
	set_val(DA_FC447, 0);
	//Finish_Aim();
//set_polar(POLAR_LS,aim_host,32767);
	vm_tctrl(0);
	wait( 2000);
	}
int DA_FC1230 = TRUE;
/// 
int DA_FC1164;
int DA_FC1232 = 260;
int DA_FC1233 = 0;
combo DA_FC99 {
	set_val(DA_FC449, 100);
	set_val(DA_FC450, 100);
	if (DA_FC1230) {
		DA_FC1233 = DA_FC428;
			}
	else {
		DA_FC1233 = 0  
	}
	set_val(DA_FC447, 100);
	// set_polar(POLAR_LS,my_angle,32767);
	vm_tctrl(0);
	wait( DA_FC1232);
	//set_polar(POLAR_LS,my_angle,32767);
	vm_tctrl(0);
	wait( DA_FC1164 + DA_FC1233 + 40) // Animation + AimLock (1)
	  // if(!cross_timer)set_val(ShotBtn    ,100); // Timed _ Shot
	  // set_polar(POLAR_LS,my_angle,32767);
	  //    vm_tctrl(0);wait( 60);
	  //Finish_Aim();
	  // set_polar(POLAR_LS,my_angle,32767);
	  // vm_tctrl(0);wait( 2500) // Animation + AimLock (2)  
}
int DA_FC1236;
int DA_FC1237 = 145;
// Power of chip shot determind by user in S.G
combo DA_FC100 {
	set_val(DA_FC447, 100);
	set_val(DA_FC449, 100);
	vm_tctrl(0);
	wait( DA_FC1237);
	vm_tctrl(0);
	wait( 500);
	}
int DA_FC646;
int DA_FC645;
int DA_FC648;
int DA_FC647;
combo DA_FC101 {
	set_val(DA_FC454, 0);
	vm_tctrl(0);
	wait( 30);
	set_val(DA_FC454, 100);
	vm_tctrl(0);
	wait( 60);
	}
//=================================
int DA_FC649;
int DA_FC650;
combo DA_FC102 {
	set_val(DA_FC453, 100);
	vm_tctrl(0);
	wait( DA_FC650);
	}
define DA_FC1244 = TRUE;
define DA_FC1245 = 95;
define DA_FC1246 = 10;
define DA_FC1247 = 70;
// Polar Recoil
define DA_FC1248 = FALSE;
define DA_FC1249 = 50;
define DA_FC1250 = 95;
define DA_FC1251 = XB1_LT;
define DA_FC1252 = XB1_RT;
define DA_FC1253 = XB1_LX;
define DA_FC1254 = XB1_LY;
define DA_FC1255 = POLAR_LS;
int DA_FC1256;
combo DA_FC103 {
	set_polar(POLAR_LS,0,0);
	vm_tctrl(0);
	wait(60);
	vm_tctrl(0);
	wait(60);
	}
function DA_FC271() {
	/*if(event_release(SprintBtn) || event_release(PassBtn) || event_release(PaceCtrol) ){
combo_run(stop_please);
}*/
// Support jockey or Defense Mode with enhanced movements
	if (    get_ival(DA_FC452) > 30 &&    (get_ival(DA_FC451) || get_ival(DA_FC448)) &&    (!get_ival(DA_FC453) || !get_ival(DA_FC447))  ) {
		// Perform defensive combo
//set_val(PassBtn, 0);
set_val(DA_FC452, 0);
		if(!get_ival(DA_FC447)){
			set_val(DA_FC451,100);
					}
		else{
			set_val(DA_FC451,0);
					}
		  combo_run(DA_FC105);
		if(DA_FC431 == TRUE){
			combo_run(DA_FC104);
					}
			}
	else {
		combo_stop(DA_FC105);
		combo_stop(DA_FC104);
			}
	}
combo DA_FC104 {
	// Control actions based on AI_def_support
	if (DA_FC431 == TRUE) {
		set_val(DA_FC450, 100);
		DA_FC1257 = 60;
			}
	else {
		DA_FC1257 = 0;
			}
	set_val(DA_FC451, 0);
	vm_tctrl(0);
	wait( DA_FC1257);
	// Reset actions based on AI_def_support
	if (DA_FC431 == TRUE) {
		set_val(DA_FC450, 0);
		DA_FC1257 = 60;
			}
	else {
		DA_FC1257 = 0;
			}
	set_val(DA_FC451, 0);
	vm_tctrl(0);
	wait( DA_FC1257);
	// More actions based on AI_def_support
	if (DA_FC431 == TRUE) {
		set_val(DA_FC450, 100);
			}
	vm_tctrl(0);
	wait( 750);
	vm_tctrl(0);
	wait( 750);
	}
combo DA_FC105 {
	sensitivity(PS4_LX, NOT_USE, 129);
	sensitivity(PS4_LY, NOT_USE, 129);
		// Define a combo for defensive actions
	if(!get_ival(DA_FC447)){
		set_val(DA_FC451,100);
			}
	else{
		set_val(DA_FC451,0);
			}
	set_val(DA_FC452, 100);
	//def_JK_com();
	vm_tctrl(0);
	wait(600);
	// Define a combo for defensive actions
	if(!get_ival(DA_FC447)){
		set_val(DA_FC451,100);
			}
	else{
		set_val(DA_FC451,0);
			}
	set_val(DA_FC452, 100);
	//def_JK_com();
	vm_tctrl(0);
	wait(DA_FC585);
	if(!get_ival(DA_FC447)){
		set_val(DA_FC451,100);
			}
	else{
		set_val(DA_FC451,0);
			}
			
    set_val(DA_FC452, 0);
	vm_tctrl(0);
	wait(500);
	}
int DA_FC1259;
// (360 / 15 Degrees) = 24
int DA_FC1257 ;
//int Polar_Enh_Move_on_off = TRUE;
/*         
=================================================================
EM Polar V2                                                                                                                    
=================================================================
*/
int DA_FC1261 = TRUE;
int DA_FC1262;
int DA_FC1263;
int DA_FC1264;
int DA_FC1265;
int DA_FC1266;
function DA_FC272() {
	if(!get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC449) && !combo_running(DA_FC105) ){
		if (DA_FC123(POLAR_LS, POLAR_RADIUS) > 1800) {
			DA_FC1264 += get_rtime();
			if(DA_FC1264 > (DA_FC455 * 2)) DA_FC1264 = 0;
			if(DA_FC1264 <= DA_FC455){
				DA_FC116(POLAR_LS, DA_FC123(POLAR_LS,POLAR_ANGLE), DA_FC123(POLAR_LS, POLAR_RADIUS));
							}
			if(DA_FC1264 >  DA_FC455){
					sensitivity(PS4_LX, NOT_USE, 71);
					sensitivity(PS4_LY, NOT_USE, 65);
				}
					}
			}
			/*if(event_release(FinesseShot)){
			 pace_toggle = 1;
			 toggle_timer = 400;
			 }
			 if(toggle_timer > 0 && !get_ival(PassBtn) && !get_ival(ShotBtn) && !get_ival(ThroughBall) && !get_ival(SprintBtn) && !get_ival(CrossBtn)){
			 toggle_timer -= get_rtime();
			 set_val(PaceCtrol,100);
			 set_val(FinesseShot,100);
			 }
			 if(toggle_timer < 10 && toggle_timer > 0 ){
			 toggle_timer = 0;
			 }
			 */
			 
			 
			 
			 
			 
			 
			
	// Pass_Assist();
// Polar_Aim_Assist();
}
// Other functions in your code, if any, should also be updated.
/*function get_rtime() {
  if (vm_speed_onoff == 0)
    return 10;
  if (vm_speed_onoff == 1)
    return 8;
  if (vm_speed_onoff == 2)
    return 8;
  if (vm_speed_onoff == 2)
    return 6;
  if (vm_speed_onoff == 4)
    return 4;
  if (vm_speed_onoff == 5)
    return 2;
  if (vm_speed_onoff == 6)
    return 2;
  // Default case, return a suitable value
  return 10; // You can choose an appropriate value here
}*/
combo DA_FC106 {
	set_val(DA_FC448,100);
	vm_tctrl(0);
	wait( DA_FC646);
	set_val(DA_FC448,  0);
	vm_tctrl(0);
	wait( 30);
	if(DA_FC401){
		set_val(DA_FC448,100);
			}
	vm_tctrl(0);
	wait( 60);
	}
combo DA_FC107 {
	set_val(DA_FC448,  0);
	vm_tctrl(0);
	wait( 30);
	set_val(DA_FC448,100);
	vm_tctrl(0);
	wait( 60);
	}
combo DA_FC108 {
	set_val(DA_FC454,100);
	vm_tctrl(0);
	wait( DA_FC648);
	set_val(DA_FC454,  0);
	vm_tctrl(0);
	wait( 30);
	if(DA_FC398){
		set_val(DA_FC454,100);
			}
	vm_tctrl(0);
	wait( 60);
	}
//================================= 