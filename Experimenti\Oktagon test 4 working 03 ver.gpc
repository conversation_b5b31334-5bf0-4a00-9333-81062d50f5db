// Constants for stick identification and scaling
define stickX = XB1_LX;
define stickY = XB1_LY;
define MAX_VAL = 70;     // Adjust this to scale octagon size (1-100)
// 100 = full size, 60 = 60% size, etc.
define INV_SQRT2_NUM = 707;    // Approximately 1/√2 * 1000
define INV_SQRT2_DEN = 1000;   // Denominator for diagonal scaling

// Global variables
int x, y;
int abs_x, abs_y;
int L_inf, L_1;      // For L-infinity and L1 norms
int octNorm;         // Octagonal norm
int scaled_L1;       // Scaled L1 norm
int output_x, output_y;
int rotated_x, rotated_y;   // For rotated outputs

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Compute absolute values
    abs_x = abs(x);
    abs_y = abs(y);

    // Calculate L-infinity (max) norm and L1 (Manhattan) norm
    if (abs_x > abs_y) {
        L_inf = abs_x;
    } else {
        L_inf = abs_y;
    }
    L_1 = abs_x + abs_y;
    
    // Scale L1 norm for octagonal shape
    scaled_L1 = (L_1 * INV_SQRT2_NUM) / INV_SQRT2_DEN;
    
    // Octagonal norm is the maximum of L_inf and scaled_L1
    if (L_inf > scaled_L1) {
        octNorm = L_inf;
    } else {
        octNorm = scaled_L1;
    }
    
    // If the stick is moved (nonzero input)
    if (octNorm > 0) {
        // Scale outputs while maintaining direction and apply MAX_VAL scaling
        output_x = (x * MAX_VAL) / octNorm;
        output_y = (y * MAX_VAL) / octNorm;
        
        // Clamp outputs to MAX_VAL limits
        if (output_x > MAX_VAL) output_x = MAX_VAL;
        if (output_x < -MAX_VAL) output_x = -MAX_VAL;
        if (output_y > MAX_VAL) output_y = MAX_VAL;
        if (output_y < -MAX_VAL) output_y = -MAX_VAL;
    } else {
        output_x = 0;
        output_y = 0;
    }
    
    // Apply a 45-degree rotation (clockwise) to the output vector.
    // Rotation matrix for 45° clockwise is:
    // [ cos45   sin45 ]
    // [-sin45   cos45 ]
    // With cos45 = sin45 ≈ 707/1000.
    rotated_x = ((output_x + output_y) * INV_SQRT2_NUM) / INV_SQRT2_DEN;
    rotated_y = ((output_y - output_x) * INV_SQRT2_NUM) / INV_SQRT2_DEN;
    
    // Set final rotated values to the stick outputs
    set_val(stickX, rotated_x);
    set_val(stickY, rotated_y);
}
 