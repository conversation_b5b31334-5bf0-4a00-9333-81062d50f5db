/* --------------------------------------------------------------------
 * Script: o22 copy.gpc
 * Purpose: Octagonal normalization and 22.5° rotation for analog stick inputs
 * Version: 1.1
 *
 * Improvements:
 *   - Organized sections with clear headers
 *   - Enhanced readability with descriptive variable names and inline comments
 *   - Added TRACE outputs for key debugging operations
 * --------------------------------------------------------------------
 */

// Constants for stick identification and scaling
define o22_stickX = XB1_LX;
define o22_stickY = XB1_LY;
define o22_MAX_VAL = 90;     // Adjust this to scale octagon size (1-100)

// Constants for octagonal normalization
define o22_INV_SQRT2_NUM = 7071;   // More precise 1/√2 * 10000
define o22_INV_SQRT2_DEN = 10000;  // Increased precision denominator

// Constants for 22.5° rotation (clockwise)
// cos(22.5°) ≈ 0.92388 and sin(22.5°) ≈ 0.382683,
// scaled by 10000 for fixed-point math.
define o22_COS22_NUM = 9239;   // More precise cos(22.5°) * 10000
define o22_SIN22_NUM = 3827;   // More precise sin(22.5°) * 10000
define o22_ROT_DEN   = 10000;  // Increased precision denominator

/* ----------------------------- Initialization Section ----------------------------- */

// Global variables (must be global)
int x, y;
int o22_abs_x, o22_abs_y;
int o22_L_inf, o22_L_1;      // For L-infinity and L1 norms
int o22_octNorm;         // Octagonal norm
int o22_scaled_L1;       // Scaled L1 norm
int o22_output_x, o22_output_y;
int o22_rotated_x, o22_rotated_y;   // For rotated outputs
int o22_prev_x, o22_prev_y;  // Previous output values for smoothing
int o22_smooth_factor = 2;    // Smoothing factor (higher = more smoothing)

/* ----------------------------- Main Loop ----------------------------- */
main {

    o22();

}

/*
Note on 22.5° Counterclockwise Rotation:
If you prefer a 22.5° counterclockwise rotation, replace the rotation block with:
    o22_rotated_x = ((o22_output_x * o22_COS22_NUM) - (o22_output_y * o22_SIN22_NUM)) / o22_ROT_DEN;
    o22_rotated_y = ((o22_output_x * o22_SIN22_NUM) + (o22_output_y * o22_COS22_NUM)) / o22_ROT_DEN;
*/

function o22() {

    // Get current stick values
    x = get_val(o22_stickX);
    y = get_val(o22_stickY);
    
    // Compute absolute values
    o22_abs_x = abs(x);
    o22_abs_y = abs(y);

    // Calculate L-infinity (max) norm and L1 (Manhattan) norm
    if (o22_abs_x > o22_abs_y) {
        o22_L_inf = o22_abs_x;
    } else {
        o22_L_inf = o22_abs_y;
    }
    o22_L_1 = o22_abs_x + o22_abs_y;
    
    // Scale L1 norm for octagonal shape with increased precision
    o22_scaled_L1 = (o22_L_1 * o22_INV_SQRT2_NUM) / o22_INV_SQRT2_DEN;
    
    // Octagonal norm is the maximum of o22_L_inf and o22_scaled_L1
    if (o22_L_inf > o22_scaled_L1) {
        o22_octNorm = o22_L_inf;
    } else {
        o22_octNorm = o22_scaled_L1;
    }
    
    // If the stick is moved (nonzero input)
    if (o22_octNorm > 0) {
        // Scale outputs while maintaining direction and apply o22_MAX_VAL scaling
        o22_output_x = (x * o22_MAX_VAL) / o22_octNorm;
        o22_output_y = (y * o22_MAX_VAL) / o22_octNorm;
        
        // Apply smoothing
        o22_output_x = ((o22_output_x * (o22_smooth_factor - 1)) + o22_prev_x) / o22_smooth_factor;
        o22_output_y = ((o22_output_y * (o22_smooth_factor - 1)) + o22_prev_y) / o22_smooth_factor;
        
        // Clamp outputs to o22_MAX_VAL limits
        if (o22_output_x > o22_MAX_VAL) o22_output_x = o22_MAX_VAL;
        if (o22_output_x < -o22_MAX_VAL) o22_output_x = -o22_MAX_VAL;
        if (o22_output_y > o22_MAX_VAL) o22_output_y = o22_MAX_VAL;
        if (o22_output_y < -o22_MAX_VAL) o22_output_y = -o22_MAX_VAL;
        
        // Store current values for next iteration's smoothing
        o22_prev_x = o22_output_x;
        o22_prev_y = o22_output_y;
    } else {
        o22_output_x = 0;
        o22_output_y = 0;
        o22_prev_x = 0;
        o22_prev_y = 0;
    }
    
    // Rotate the normalized output by 22.5° clockwise.
    // Rotation matrix for a clockwise rotation by θ is:
    //   x' = x*cosθ + y*sinθ
    //   y' = -x*sinθ + y*cosθ
    o22_rotated_x = ((o22_output_x * o22_COS22_NUM) + (o22_output_y * o22_SIN22_NUM)) / o22_ROT_DEN;
    o22_rotated_y = ((-o22_output_x * o22_SIN22_NUM) + (o22_output_y * o22_COS22_NUM)) / o22_ROT_DEN;
    
    // Set final rotated values to the stick outputs
    set_val(o22_stickX, o22_rotated_x);
    set_val(o22_stickY, o22_rotated_y);
}
