// GPS Zones Calculation v3

// Global variables
int order[8];
int polar_angle;
int debug_mode;
int test_angle;
int LX;
int LY;
int angle;
int polar_LS;
int index;
int zone_p;
int zone_RS;
int zone_LS;

// Variables for circular movement
int current_angle;       // Current angle of the left stick
int target_angle = -1;   // Target angle to move to (-1 means no target)
define move_speed = 5;      // Speed of rotation in degrees per frame
define stick_radius = 32767; // Full radius value
int is_moving = FALSE;   // Flag to indicate if we're currently in a movement
int local_angle
init {
    order[0] = 0;
    order[1] = 1;
    order[2] = 2;
    order[3] = 3;
    order[4] = 4;
    order[5] = 5;
    order[6] = 6;
    order[7] = 7;
    
    debug_mode = TRUE;
    test_angle = 0;
}

// Calculate angle from stick position
function calc_stick_angle(x, y) {

    if (x == 0 && y == 0) {
        return 0;
    }
    
    local_angle = atan2(y, x);
    if (local_angle < 0) {
        local_angle = local_angle + 360;
    }
    return local_angle;
}

// Function to set the target angle for the left stick
// The stick will move along the full radius to reach this angle
function set_target_angle(new_target) {
    target_angle = new_target;
    current_angle = get_polar(POLAR_LS, POLAR_ANGLE);
    is_moving = TRUE;
}
    int clockwise_dist;
    int counter_dist;
// Function to update the left stick movement along the circular path
function update_circular_movement() {

    // If we're not moving or no target is set, do nothing
    if (!is_moving || target_angle < 0) {
        return;
    }
    
    // Get current angle if not already set
    if (current_angle < 0) {
        current_angle = get_polar(POLAR_LS, POLAR_ANGLE);
    }
    
    // Calculate the clockwise and counterclockwise distances
    clockwise_dist = (target_angle - current_angle + 360) % 360;
    counter_dist = (current_angle - target_angle + 360) % 360;
    
    // Determine if we've reached the target (or close enough)
    if (clockwise_dist <= move_speed || counter_dist <= move_speed) {
        // We've reached the target, set the exact angle
        set_polar(POLAR_LS, target_angle, stick_radius);
        is_moving = FALSE;
        return;
    }
    
    // Determine which direction is shorter
    if (clockwise_dist <= counter_dist) {
        // Move clockwise
        current_angle = (current_angle + move_speed) % 360;
    } else {
        // Move counterclockwise
        current_angle = (current_angle - move_speed + 360) % 360;
    }
    
    // Apply the new position
    set_polar(POLAR_LS, current_angle, stick_radius);
}
    int local_polar_LS;

// Function to calculate zone for POLAR_LS
function calc_zone() {

    local_polar_LS = get_polar(POLAR_LS, POLAR_ANGLE);
    
    if (local_polar_LS < 0 || local_polar_LS >= 360) {
        return -1;
    }
    
    index = ((local_polar_LS + 23) % 360) / 45;
    zone_p = order[index];
    return zone_p;
}
    int local_polar_angle;
// Function to calculate zone for POLAR_RS
function calc_RS() {

    local_polar_angle = get_polar(POLAR_RS, POLAR_ANGLE);
    
    if (local_polar_angle < 0 || local_polar_angle >= 360) {
        return -1;
    }
    
    index = ((local_polar_angle + 23) % 360) / 45;
    zone_RS = order[index];
    return zone_RS;
}
int LEFT;
int RIGHT;
// Debug combo to display zone information
combo show_debug_info {
    set_led(LEFT, zone_LS * 30);
    set_led(RIGHT, zone_RS * 30);
    wait(100);
}

// Rumble feedback combo
combo debug_rumble {
    set_rumble(RUMBLE_A, 100);
    set_rumble(RUMBLE_B, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
    set_rumble(RUMBLE_B, 0);
}

// Main block
main {
    // Toggle debug mode with SELECT/SHARE button
    if (event_press(XB1_VIEW)) {
        debug_mode = !debug_mode;
        if (debug_mode) {
            combo_run(debug_rumble);
        }
    }

    // Calculate zones
    zone_LS = calc_zone();
    zone_RS = calc_RS();
    
    // Example: Press A to move to 175 degrees
    if (event_press(XB1_A)) {
        set_target_angle(175);
    }
    
    // Example: Press B to move to 270 degrees
    if (event_press(XB1_B)) {
        set_target_angle(270);
    }
    
    // Update the circular movement each frame
    update_circular_movement();

    // Debug display when holding RT/R2
    if (get_val(XB1_RT) && debug_mode) {
        combo_run(show_debug_info);
    }

    // Example usage of zones
    if (zone_LS != -1 && zone_RS != -1) {
        if (zone_LS == 1) {  // up-right
            if (debug_mode) {
                set_val(XB1_RB, 100);
            }
        }
        if (zone_LS == 3) {  // up-left
            if (debug_mode) {
                set_val(XB1_RB, 100);
            }
        }
        if (zone_LS == 5) {  // down-left
            if (debug_mode) {
                set_val(XB1_LT, 100);
            }
        }
        if (zone_LS == 7) {  // down-right
            if (debug_mode) {
                set_val(XB1_LT, 100);
            }
        }
    }

    // Store stick values for next frame
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);
}


// 	Zone 0: approximately 337.5° to 22.5° (which is right)
//  Zone 1: approximately 22.5° to 67.5° (which is up-right)
//  Zone 2: approximately 67.5° to 112.5° (which is up)
//  Zone 3: approximately 112.5° to 157.5° (which is up-left)
//  Zone 4: approximately 157.5° to 202.5° (which is left)
//  Zone 5: approximately 202.5° to 247.5° (which is down-left)
//  Zone 6: approximately 247.5° to 292.5° (which is down)
//  Zone 7: approximately 292.5° to 337.5° (which is down-right) 