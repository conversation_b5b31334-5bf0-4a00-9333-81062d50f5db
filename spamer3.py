# Python equivalent of the AutoHotkey script
# Requires the 'pynput' library: pip install pynput

from pynput import keyboard
import threading
import time
import sys
import random  # Added for random interval generation

# --- Configuration ---
SPAM_KEY_TO_SEND = 'q'  # The key to spam (single character)

# Spam intervals: Map hotkey characters to their intervals in seconds
SPAM_INTERVALS = {
    'a': random.choice([1.30, 1.32, 1.34, 1.36, 1.38, 1.40, 1.42, 1.44, 1.46, 1.48, 1.50]),   # 20ms steps between 1.3-1.5s
    's': 1.0,   # 0.9 second interval
    'd': random.choice([0.60, 0.62, 0.64, 0.66, 0.68, 0.70]),   # 20ms steps between 0.6-0.7s
    'f': 0.1    # 0.1 second interval
}
STOP_SPAM_KEY_CHAR = 'y'    # Character to stop any spamming
SEND_F5_KEY_CHAR = 'g'      # Character to trigger sending F5
PAUSE_RESUME_KEY_CHAR = '#' # Character to pause/resume the script
EXIT_SCRIPT_KEY = keyboard.Key.esc  # pynput Key object for Escape key

# --- Global State ---
active_timer_key_char = ""    # Keeps track of which timer key char is currently running ('a', 's', 'd', 'f')
active_timer_object = None    # Stores the active threading.Timer object
spam_active = False           # Flag to control the spamming loop
script_paused = False         # Flag to indicate if the script's hotkeys are paused

# --- Keyboard Controller (for sending keys) ---
kb_controller = keyboard.Controller()

# --- Functions ---

def console_tooltip(message, duration_seconds=None):
    """
    Simulates AHK ToolTip by printing to console.
    The 'duration_seconds' is not actively used to clear the message in this simple version.
    """
    print(f"[INFO] {message}")

def spam_function_for_timer():
    """
    Function executed by the timer to send the spam key.
    It reschedules itself if spamming is still active and script is not paused.
    """
    global spam_active, active_timer_object, active_timer_key_char, SPAM_KEY_TO_SEND, SPAM_INTERVALS, script_paused

    if not spam_active or not active_timer_key_char or script_paused: # Check script_paused here too
        active_timer_object = None # Ensure timer object is cleared if spam stopped externally or script paused
        return

    # Send the configured key
    try:
        kb_controller.press(SPAM_KEY_TO_SEND)
        kb_controller.release(SPAM_KEY_TO_SEND)
        # print(f"Sent: {SPAM_KEY_TO_SEND}") # Uncomment for debug
    except Exception as e:
        print(f"[ERROR] Could not send key '{SPAM_KEY_TO_SEND}': {e}")


    # Reschedule the timer if spamming should continue
    current_interval_seconds = SPAM_INTERVALS.get(active_timer_key_char)
    if spam_active and current_interval_seconds is not None and not script_paused:
        active_timer_object = threading.Timer(current_interval_seconds, spam_function_for_timer)
        active_timer_object.daemon = True  # Allow program to exit even if timer is running
        active_timer_object.start()
    else:
        active_timer_object = None # Clear timer if spam stopped or key interval not found or script paused

def stop_spam_timer():
    """Stops the active spamming timer."""
    global active_timer_object, active_timer_key_char, spam_active
    
    if not spam_active and active_timer_object is None: # Already stopped
        return

    spam_active = False  # Signal the timer loop to stop rescheduling

    if active_timer_object:
        active_timer_object.cancel()  # Stop the current timer
        active_timer_object = None
    
    console_tooltip("Spam stopped")
    active_timer_key_char = ""

def start_spam_timer(key_char, interval_seconds):
    """Starts a new spamming timer."""
    global active_timer_key_char, active_timer_object, spam_active, script_paused

    if script_paused: # Do not start spam if script is paused
        console_tooltip("Cannot start spam, script is paused.")
        return

    # If a different timer is running, stop it first
    if spam_active and active_timer_key_char != "" and active_timer_key_char != key_char:
        stop_spam_timer() # This will print "Spam stopped"

    spam_active = True
    active_timer_key_char = key_char

    # Cancel any existing timer object before starting a new one
    if active_timer_object:
        active_timer_object.cancel()
        active_timer_object = None

    # Initial call to the spam function, which will then reschedule itself
    active_timer_object = threading.Timer(interval_seconds, spam_function_for_timer)
    active_timer_object.daemon = True
    active_timer_object.start()
    
    display_spam_key = SPAM_KEY_TO_SEND
    if isinstance(SPAM_KEY_TO_SEND, keyboard.Key):
        display_spam_key = SPAM_KEY_TO_SEND.name
        
    console_tooltip(f"'{display_spam_key}' spam started (via '{key_char}' key, {interval_seconds*1000:.0f}ms interval)")

def handle_spam_toggle(key_char_pressed):
    """Handles toggling spam on/off for a given key character."""
    global active_timer_key_char, spam_active, SPAM_INTERVALS, script_paused

    if script_paused: # Ignore if script is paused
        return

    if key_char_pressed not in SPAM_INTERVALS:
        print(f"[ERROR] Internal Script Error: Key '{key_char_pressed}' not found in SPAM_INTERVALS map!")
        return

    interval_seconds = SPAM_INTERVALS[key_char_pressed]

    if spam_active and active_timer_key_char == key_char_pressed:
        stop_spam_timer()
    else:
        start_spam_timer(key_char_pressed, interval_seconds)

def do_send_f5():
    """Sends an F5 key press."""
    global script_paused
    if script_paused: # Ignore if script is paused
        return
    console_tooltip("Sending F5")
    try:
        kb_controller.press(keyboard.Key.f5)
        kb_controller.release(keyboard.Key.f5)
    except Exception as e:
        print(f"[ERROR] Could not send F5: {e}")

keep_listening = True # Flag to control the listener loop

def do_exit_script():
    """Prepares the script to exit cleanly."""
    global keep_listening
    console_tooltip("Exiting script...")
    stop_spam_timer()  # Clean up any active timer
    keep_listening = False # Signal the listener to stop
    return False # Returning False from on_press callback stops the listener

# --- Hotkey Listener Callback ---
def on_key_press(key):
    """Callback function for key presses."""
    global SPAM_INTERVALS, STOP_SPAM_KEY_CHAR, SEND_F5_KEY_CHAR, EXIT_SCRIPT_KEY
    global PAUSE_RESUME_KEY_CHAR, script_paused, keep_listening, spam_active

    if not keep_listening:
        return False

    pressed_key_value = None
    try:
        pressed_key_value = key.char
    except AttributeError:
        pressed_key_value = key

    # --- Highest priority: Exit Script ---
    if pressed_key_value == EXIT_SCRIPT_KEY:
        return do_exit_script()

    # --- Next priority: Pause/Resume Script ---
    if isinstance(pressed_key_value, str) and pressed_key_value == PAUSE_RESUME_KEY_CHAR:
        script_paused = not script_paused
        if script_paused:
            console_tooltip(f"Script paused. Press '{PAUSE_RESUME_KEY_CHAR}' to resume.")
            if spam_active: # If spamming was active, stop it
                stop_spam_timer() # This will also print "Spam stopped"
        else:
            console_tooltip(f"Script resumed. Press '{PAUSE_RESUME_KEY_CHAR}' to pause.")
        return keep_listening # Consume the pause/resume key

    # --- If script is paused, ignore other hotkeys ---
    if script_paused:
        return keep_listening

    # --- Process other hotkeys if not paused ---
    if isinstance(pressed_key_value, str):
        if pressed_key_value in SPAM_INTERVALS:
            handle_spam_toggle(pressed_key_value)
        elif pressed_key_value == STOP_SPAM_KEY_CHAR:
            stop_spam_timer()
        elif pressed_key_value == SEND_F5_KEY_CHAR:
            do_send_f5()
            
    return keep_listening

# --- Main Execution ---
if __name__ == "__main__":
    print("Python Hotkey Script (AHK_equivalent) Started.")
    print(f" - Spam '{SPAM_KEY_TO_SEND}' using keys: {', '.join(SPAM_INTERVALS.keys())}")
    print(f" - Press '{STOP_SPAM_KEY_CHAR}' to stop spam.")
    print(f" - Press '{SEND_F5_KEY_CHAR}' to send F5.")
    print(f" - Press '{PAUSE_RESUME_KEY_CHAR}' to pause/resume script.")
    print(f" - Press 'Esc' (Escape key) to exit.")
    print("Listening for hotkeys...")

    with keyboard.Listener(on_press=on_key_press) as listener:
        try:
            listener.join()
        except Exception as e:
            print(f"[ERROR] An error occurred with the listener: {e}")
        finally:
            print("Python Hotkey Script Exited.")
