// Define base sensitivity value as a percentage (e.g., 200 = 200% sensitivity)
int sensitivityValue = 300;  // Adjust this value as needed

// Variables for sensitivity calculations
int val_s;    // Stores sign of input value (+1 or -1)
int _val;     // Stores the modified input value
int baseSen;  // Calculated sensitivity scaling factor
int midVal;   // Midpoint value for response curve

main {
    // Calculate 'sen' to prevent overflow
    baseSen = (sensitivityValue * 16384) / 100;
    
    // Midpoint value for linear response (half of max 32767)
    midVal = 16384;
    
    // Apply custom sensitivity to all stick axes (LX, LY, RX, RY)
    _sensitivity(POLAR_LX, midVal, baseSen);
    _sensitivity(POLAR_LY, midVal, baseSen);
    _sensitivity(POLAR_RX, midVal, baseSen);
    _sensitivity(POLAR_RY, midVal, baseSen);
}

// Custom sensitivity function to modify stick response
function _sensitivity(id, mid, sen) {
    // Get current stick value
    _val = get_val(id);
    
    // Clamp to safe range
    if(_val > 32767) {
        _val = 32767;
    } else if(_val < -32768) {
        _val = -32768;
    }
    
    // If midpoint is specified (not NOT_USE)
    if(mid != NOT_USE) {
        // Store sign of input value (+1 for positive, -1 for negative)
        if(_val >= 0) {
            val_s = 1;
        } else {
            val_s = -1;
        }
        
        // Take absolute value
        _val = _val * val_s;
        
        // Apply two-part response curve based on midpoint
        if(_val <= mid) {
            // Below midpoint: Scale input linearly from 0 to midpoint
            _val = (_val * 16384) / mid;
        } else {
            // Above midpoint: Scale input linearly from midpoint to max
            _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
        }
        
        // Restore original sign
        _val = _val * val_s;
    }
    
    // If sensitivity multiplier specified, apply it
    if(sen != NOT_USE) {
        // Multiply by sensitivity and divide to normalize
        _val = (_val * sen) / 16384;
    }
    
    // Clamp the result to valid range
    if(_val > 32767) {
        _val = 32767;
    } else if(_val < -32768) {
        _val = -32768;
    }
    
    // Set modified value back to stick
    set_val(id, _val);
}