
																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON>.Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/























































 int D22201[0]; init { 	D22118(); 	combo_run(D221); 	combo_run(D222); 	combo_run(D223); 	combo_run(D224); 	combo_run(D225); 	combo_run(D226); 	combo_run(D227); 	combo_run(D228); 	combo_run(D229); 	combo_run(D2210); 	combo_run(D2211); 	combo_run(D2212); 	combo_run(D2213); 	combo_run(D2214); 	combo_run(D2215); 	combo_run(D2216); 	combo_run(D2217); 	combo_run(D2218); 	combo_run(D2219); 	combo_run(D2220); 	combo_run(D2221); 	combo_run(D2222); 	combo_run(D2223); 	combo_run(D2224); 	combo_run(D2225); 	combo_run(D2226); 	combo_run(D2227); 	combo_run(D2228); 	combo_run(D2229); 	combo_run(D2230); 	combo_run(D2231); 	combo_run(D2232); 	combo_run(D2233); 	combo_run(D2234); 	combo_run(D2235); 	combo_run(D2236); 	combo_run(D2237); 	combo_run(D2238); 	combo_run(D2239); 	combo_run(D2240); 	combo_run(D2241); 	combo_run(D2242); 	combo_run(D2243); 	combo_run(D2244); 	combo_run(D2245); 	combo_run(D2246); 	combo_run(D2247); 	combo_run(D2248); 	combo_run(D2249); 	combo_run(D2250); 	combo_run(D2251); 	combo_run(D2252); 	combo_run(D2253); 	combo_run(D2254); 	combo_run(D2255); 	combo_run(D2256); 	combo_run(D2257); 	combo_run(D2258); 	combo_run(D2259); 	combo_run(D2260); 	combo_run(D2261); 	combo_run(D2262); 	combo_run(D2263); 	combo_run(D2264); 	combo_run(D2265); 	combo_run(D2266); 	combo_run(D2267); 	combo_run(D2268); 	combo_run(D2269); 	combo_run(D2270); 	combo_stop(D221); 	combo_stop(D222); 	combo_stop(D223); 	combo_stop(D224); 	combo_stop(D225); 	combo_stop(D226); 	combo_stop(D227); 	combo_stop(D228); 	combo_stop(D229); 	combo_stop(D2210); 	combo_stop(D2211); 	combo_stop(D2212); 	combo_stop(D2213); 	combo_stop(D2214); 	combo_stop(D2215); 	combo_stop(D2216); 	combo_stop(D2217); 	combo_stop(D2218); 	combo_stop(D2219); 	combo_stop(D2220); 	combo_stop(D2221); 	combo_stop(D2222); 	combo_stop(D2223); 	combo_stop(D2224); 	combo_stop(D2225); 	combo_stop(D2226); 	combo_stop(D2227); 	combo_stop(D2228); 	combo_stop(D2229); 	combo_stop(D2230); 	combo_stop(D2231); 	combo_stop(D2232); 	combo_stop(D2233); 	combo_stop(D2234); 	combo_stop(D2235); 	combo_stop(D2236); 	combo_stop(D2237); 	combo_stop(D2238); 	combo_stop(D2239); 	combo_stop(D2240); 	combo_stop(D2241); 	combo_stop(D2242); 	combo_stop(D2243); 	combo_stop(D2244); 	combo_stop(D2245); 	combo_stop(D2246); 	combo_stop(D2247); 	combo_stop(D2248); 	combo_stop(D2249); 	combo_stop(D2250); 	combo_stop(D2251); 	combo_stop(D2252); 	combo_stop(D2253); 	combo_stop(D2254); 	combo_stop(D2255); 	combo_stop(D2256); 	combo_stop(D2257); 	combo_stop(D2258); 	combo_stop(D2259); 	combo_stop(D2260); 	combo_stop(D2261); 	combo_stop(D2262); 	combo_stop(D2263); 	combo_stop(D2264); 	combo_stop(D2265); 	combo_stop(D2266); 	combo_stop(D2267); 	combo_stop(D2268); 	combo_stop(D2269); 	combo_stop(D2270); 	combo_run(D22110); } int D22264 ; int D22265; int D22266; int D22267; int D22268; define D22269 = 0; define D22270 = 1; define D22271 = 2; define D22272 = 3; define D22273 = 4; define D22274 = 5; define D22275 = 6; define D22276 = 7; define D22277 = 8; define D22278 = 9; define D22279 = 10; define D22280 = 11; define D22281 = 12; define D22282 = 13; define D22283 = 14; define D22284 = 15; define D22285 = 16; define D22286 = 17; define D22287 = 18; define D22288 = 19; define D22289 = 20; define D22290 = 21; define D22291 = 22; define D2223 = 23; define D22293 = 24; define D22294 = 25; define D22295 = 26; define D22296 = 27; define D22297 = 28; define D22298 = 29; define D22299 = 30; define D22300 = 31; define D22301 = 32; define D22302 = 33; define D22303 = 34; define D22304 = 35; define D22305 = 36; define D22306 = 37; define D22307 = 38; define D22308 = 39; define D22309 = 40; define D22310 = 41; define D22311 = 42; define D22312 = 43; define D22313 = 44; define D22314 = 45; define D22315 = 46; define D22316 = 47; define D22317 = 48; define D22318 = 49; define D22319 = 50; define D22320 = 51; define D22321 = 52; define D22322 = 53; define D22323 = 54; define D22324 = 55; define D22325 = 56; define D22326 = 57; define D22327 = 58; define D22328 = 59; define D22329 = 60; define D22330 = 61; define D22331 = 62; define D22332 = 63; define D22333 = 64; define D22334 = 65; define D22335 = 66; define D22336 = 67; define D22337 = 68; define D22338 = 69; define D22339 = 70; define D22340 = 0; function D22112(D22113) { 	if (D22113 == 0) vm_tctrl(-0); 	else if (D22113 == 1) vm_tctrl(2); 	else if (D22113 == 2) vm_tctrl(-2); 	else if (D22113 == 3) vm_tctrl(-4); 	else if (D22113 == 4) vm_tctrl(-6); 	else if (D22113 == 5) vm_tctrl(-8); 	else if (D22113 == 6) vm_tctrl(-9); } int D22341, D22342; int D22343, D22344; int D22345 = FALSE, D22346; int D22347 = TRUE; int D22348; const string D22822[] = { 	"Off",  "On" } ; int D22349; int D22350; int D22351; int D22352; int D22353; int D22354; int D22355; int D22356; int D22357; int D22358; int D22359; int D22360; int D22361; int D22362; int D22363; int D22364; int D22365; int D22366; int D22367; int D22368; int D22113; int D22370; int D22371 ; int D22372 ; int D22373 ; define D22374 = 24; int D22375; int D22376; int D22377; int D22378; int D22379; int D22380; int D22381; int D22382; int D22383; int D22384 ; int D22385 ; int D22386 ; int D22387 ; int D22388 ; int D22389 ; int D22390 ; int D22391 ; int D22392 ; int D22393 ; int D22394 ; int D22395; int D22396; int D22397; int D22398; int D22399; int D22400; int D22401; int D22402; int D22403; int D22404; int D22405; int D22406; int D22407; int D22408; int D22409; int D22410; int D22411; int D22412; int D22413; int D22414; int D22415; int D22416; int D22417; int D22418; int D22419; int D22420; int D22421; int D22422; int D22423; int D22424; int D22425; int D22426; int D22427; int D22428 ; int D22429 ; int D22430 ; int D22431; int D22432 ; int D22433 ; int D22434 ; int D22435; int D22436 ; int D22437 ; int D22438 ; int D22439; int D22440 ; int D22441 ; int D22442 ; int D22443; int D22444; int D22445; int D22446; int D22447; int D22448; const int16 D22828[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		2800,12000,100,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 D22569[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 D22800[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    6,   	    1,   	    1,  	1,  	1   } ; const string D22579[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string D22578[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string D22804 [] = { 	"Classic","Alternative","Custom", ""  } ; const string D22903 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string D22820[] = { 	"0",  "2",  "-2",  "-4",  "-6",  "-8",  "-9",  "" } ; const string D22806[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string D22873[] = { 	"Right",  "Left",  "" } ; const string D22871[] = { 	"One Tap",  "Double Tap",  "" } ; const string D22810[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string D22812[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string D22847[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int D22449 = -1; int D22450 = -1; int D22451 = -1; int D22452 = -1; int D22453 = -1; int D22454; int D22455; int D22456; int D22457; int D22458; const uint8 D221352[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 D221353[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 D221354[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 D221355[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 D221356[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 D221357[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D221358[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 D221359[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 D221360[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 D221361[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 D221362[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 D221363[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 D221364[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 D221365[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D221366[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 D221367[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function D22114(D22115) { 	if (D22115 == 9) { 		D22459 = -1; 			} 	else if (D22115 <= 0) { 		D22459 = 1; 			} 	else if (D22115 > 9 ) { 		D22115 = 0; 			} 	D22115 += D22459; 	return D22115; 	} function D22116() { 	vm_tctrl(0); 	if(D2229 && D22352){ 		if(D22545 < 1000){ 			D22462 = 10; 			D22488   = 10; 			D22486  = 10; 					} 			} 	if(D22465 && D22353){ 		D22463 = FALSE; 		if(D22545 < 1000){ 			D22462 = 11; 			D22488   = 11; 			D22486  = 11; 					} 			} 	if(D22463 && D22353){ 		D22465 = FALSE; 		if(D22545 < 1000){ 			D22462 = 10; 			D22488   = 10; 			D22486  = 10; 					} 			} 			       if(D22545 >= 1000){     D22467 = D22114(D22467);     D22485 = D22114(D22485);     D22486 = D22114(D22486);     D22462 = D22114(D22462);     D22488 = D22114(D22488);     } 	if(D22352){ 		if(D22491 == D22583){ 			D22468 = !D22468; 			if(D221352[D22467]) D22226(D221352[D22467]); 					} 		if(D22491 == D22231 (D22583 + 4)){ 			D22468 = FALSE; 			if(D221359[D22485]) D22226(D221359[D22485]); 					} 		if(D22491 == D22231 (D22583 + 1) ){ 			D22468 = TRUE; 			if(D221354[D22462]) D22226(D221354[D22462]); 					} 		if(D22491 == D22231 (D22583 - 1) ){ 			D22468 = FALSE; 			if(D221353[D22462]) D22226(D221353[D22462]); 					} 		if(D22491 == D22231 (D22583 + 2) ){ 			D22468 = TRUE; 			if(D221356[D22488]) D22226(D221356[D22488]); 					} 		if(D22491 == D22231 (D22583 - 2) ){ 			D22468 = FALSE; 			if(D221355[D22488]) D22226(D221355[D22488]); 					} 		if(D22491 == D22231 (D22583 + 3) ){ 			D22468 = TRUE; 			if(D221357[D22486]) D22226(D221357[D22486]); 					} 		if(D22491 == D22231 (D22583 - 3) ){ 			D22468 = FALSE; 			if(D221358[D22486]) D22226(D221358[D22486]); 					} 			} 	else if(D22353){ 		if(D22491 == D22583){ 			D22468 = !D22468; 			if(D221360[D22467]) D22226(D221360[D22467]); 					} 		if(D22491 == D22231 (D22583 + 4)){ 			D22468 = FALSE; 			if(D221367[D22485]) D22226(D221367[D22485]); 					} 		if(D22491 == D22231 (D22583 + 1) ){ 			D22468 = TRUE; 			if(D221362[D22462]) D22226(D221362[D22462]); 					} 		if(D22491 == D22231 (D22583 - 1) ){ 			D22468 = FALSE; 			if(D221361[D22462]) D22226(D221361[D22462]); 					} 		if(D22491 == D22231 (D22583 + 2) ){ 			D22468 = TRUE; 			if(D221364[D22488]) D22226(D221364[D22488]); 					} 		if(D22491 == D22231 (D22583 - 2) ){ 			D22468 = FALSE; 			if(D221363[D22488]) D22226(D221363[D22488]); 					} 		if(D22491 == D22231 (D22583 + 3) ){ 			D22468 = TRUE; 			if(D221365[D22486]) D22226(D221365[D22486]); 					} 		if(D22491 == D22231 (D22583 - 3) ){ 			D22468 = FALSE; 			if(D221366[D22486]) D22226(D221366[D22486]); 					} 			} } int D22467; int D22485; int D22486; int D22462; int D22488; function D22117() { 	if(D221129){ 		D22489 += get_rtime(); 			} 	if(D22489 >= 3000){ 		D22489 = 0; 		D221129 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(D22440) && !get_ival(D22441) && !get_ival(D22439) && !get_ival(D22438)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !D22492 && !combo_running(D220)) { 			D22491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D22492 = TRUE; 			D221129 = TRUE; 			D22489 = 0; 			vm_tctrl(0); 			D22116(); 					} 		set_val(D221110, 0); 		set_val(D221111, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		D22492 = FALSE; 			} 	} function D22118() { 	D22171(); 	if (D22375 == 0 && D22376 == 0 && D22377 == 0 && D22378 == 0 && D22379 == 0 && D22380 == 0 && D22381 == 0 && D22382 == 0) { 		D22375 = 4; 		D22376 = 41; 		D22377 = 41; 		D22378 = 42; 		D22379 = 42; 		D22380 = 31; 		D22381 = 31; 		D22382 = 31; 			} 	D22942 = get_slot(); 	} int D22459 = 1; int D22496; int D22497; int D22498 = TRUE; int D22499[6]; int D22500; int D22501; int D22502; int D22503; function D22119(D22120, D22121, D22122) { 	D22122 = (D22122 * 14142) / 46340; 	if (D22121 <= 0) { 		set_polar2(D22120, (D22121 = (abs(D22121) + 360) % 360), min(D22122, D22507[D22121 % 90])); 		return; 			} 	set_polar2(D22120, inv(D22121 % 360), min(D22122, D22507[D22121 % 90])); 	} function D22123(D22120,D22125) { 	if (D22125) return (get_ipolar(D22120, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + D22120), 2) + pow(get_ival(43 + D22120), 2))) + 1; 	} const int16 D22507[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int D22510 = 1; combo D220{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && D22123(POLAR_RS,POLAR_RADIUS) > 2000){ 		D22258(POLAR_RS, 2500, 12000); 		} 	set_val(TRACE_1,D22512);  D22112(D22113); 	if(!D22497){ 		D22497 = TRUE; 		D22496 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,D22497); 		set_pvar(SPVAR_3,D22496); 		D22498 = TRUE; 			} 	if(!D22503){ 		vm_tctrl(0); 	 		if(event_press(PS4_LEFT)){ 			D22502 = D22128(D22502 + 1 ,0,5)D22498 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			D22502 = D22128(D22502 - 1 ,0,5)D22498 = TRUE 		} 		if(event_press(PS4_UP)){ 			D22499[D22502]  = D22128(D22499[D22502] + 1 ,0,9)D22498 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			D22499[D22502]  = D22128(D22499[D22502] - 1 ,0,9)D22498 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			D22500 = 0; 			for(D22501 = 5; 			D22501 >= 0; 			D22501--){ 				D22500 += D22499[D22501] * pow(10,D22501) 			} 			if(D22500 == D22126(D22496)){ 				D22503 = TRUE; 				set_pvar(SPVAR_2,D22503)  			} 			D22498 = TRUE; 					} 			} 	if(D22498){ 		cls_oled(0)if(!D22503){ 			D22132(D22496,D22526,10,OLED_FONT_MEDIUM,OLED_WHITE,D22527)for( D22501 = 0; 			D22501 < 6; 			D22501++){ 				D22132(D22499[D22501],85 - (D22501 * 10),40,OLED_FONT_MEDIUM,!(D22501 == D22502),D22527) 			} 					} 		D22498 = FALSE; 			} 	if(D22503){ 		if (get_ival(D22436) || get_ival(D22440) || get_ival(D22438) || get_ival(D22439) || D22345 || combo_running(D2272) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			D22112(D22113); 					} 		if(get_ival(D22441) > 40 || (!get_ival(D22438) && !get_ival(D22439))){ 			if(get_ival(D22436)){ 				vm_tctrl(0); 				if(get_ptime(D22436) > D22598){ 					set_val(D22436,0); 									} 							} 					} 		if(!get_ival(D22438)){ 			if(get_ival(D22436)){ 				vm_tctrl(0); 				if(get_ptime(D22436) > D22598){ 					set_val(D22436,0); 									} 							} 					} 		if (D22345) { 			vm_tctrl(0); 			if(D22346 < 8050){ 				D22346 += get_rtime(); 							} 			if (D22346 >= 8000) { 				cls_oled(OLED_BLACK); 				D22346 = 0; 				D22345 = FALSE; 							} 					} 		if (block) { 			if (D22510 < 310) { 				D22510 += get_rtime(); 							} 			if (D22510 <= 300 ) { 				D22168(); 							} 			if (D22510 > 300 ) { 				block = FALSE; 				D22510 = 1; 				D22707 = TRUE; 							} 			if (D22510 < 0) { 				D22510 = 1; 							} 			if (D22510 <= 100) { 				combo_stop(D2288); 				combo_stop(D2297); 				combo_stop(D2289); 				combo_stop(D2298); 				combo_stop(D2295); 				combo_stop(D2296); 				combo_stop(D2292); 				combo_stop(D2294); 				combo_stop(D2291); 				combo_stop(D2287); 				combo_stop(D2285); 				combo_stop(D2290); 				combo_stop(D22107); 				combo_stop(D22109); 				combo_stop(D22100); 				combo_stop(D22108); 				combo_stop(D2299); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(D22426){ 			D22427 = FALSE; 					} 		if(D22427){ 			D22426 = FALSE; 					} 		if(D22350){ 			D22351 = FALSE; 			D22352 = FALSE; 			D22353 = FALSE; 					} 		if(D22351){ 			D22350 = FALSE; 			D22352 = FALSE; 			D22353 = FALSE; 					} 		if(D22352){ 			D22350 = FALSE; 			D22351 = FALSE; 			D22353 = FALSE; 					} 		if(D22353){ 			D22350 = FALSE; 			D22351 = FALSE; 			D22352 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				D221164 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				D221164 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				D221052 = !D221052; 				D22228(D221284); 				D22202(D221052, sizeof(D22542) - 1, D22542[0]); 				D22345 = TRUE; 							} 					} 		if (D221052) { 			if(D22370){ 				D22256(); 			} 			if (D22368) { 				D22255(); 							} 			if (event_release(D22441)) { 				D22545 = 1; 							} 			if (D22545 < 8000) { 				D22545 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					D22547 = !D22547; 					D22228(D22547); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (D22547) { 				if (D22547) D22221(D221084); 				if (D22547) { 					D22147(); 									} 							} 			else if (!get_ival(D22441)) { 				D22221(D221087); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						D22341 = TRUE; 						D22348 = TRUE; 						D22347 = FALSE; 						if (!D22341) { 							D22347 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!D22347) { 					if (D22341 || D22342) { 						vm_tctrl(0); 					} 					if (D22341) { 						combo_stop(D2272); 						vm_tctrl(0); 						D22349= D22148(D22349,0  ); 						D22350 = D22148(D22350, 1); 						D22351  = D22148(D22351   ,2  ); 						D22352  = D22148(D22352 , 3); 						D22353  = D22148(D22353 , 4); 						D22354 = D22148(D22354, 5); 						D22355 = D22148(D22355, 6); 						D22356 = D22148(D22356, 7); 						D22357 = D22148(D22357, 8); 						D22358 = D22148(D22358, 9); 						D22359 = D22148(D22359, 10); 						D22360 = D22148(D22360, 11); 						D22361 = D22148(D22361, 12); 						D22362 = D22148(D22362,13); 						D22363 = D22148(D22363, 14); 						D22364 = D22148(D22364, 15); 						D22365 = D22148(D22365, 16); 						D22366 = D22148(D22366, 17); 						D22367 = D22148(D22367, 18); 						D22368 = D22148(D22368, 19); 						D22113 = D22148(D22113, 20); 						D22370 = D22148(D22370, 21); 						D22371              = D22148(D22371              ,22  ); 						D22372              = D22148(D22372              ,23  ); 						D22373               = D22148(D22373               ,24  ); 						if (event_press(PS4_DOWN)) { 							D22343 = clamp(D22343 + 1, 0, D22374); 							D22348 = TRUE; 													} 						if (event_press(PS4_UP)) { 							D22343 = clamp(D22343 - 1, 0, D22374); 							D22348 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							D22341 = FALSE; 							D22347 = FALSE; 							D22348 = FALSE; 							vm_tctrl(0); 							combo_run(D2275); 													} 						if (D22569[D22343][2] == 1) { 							if(D22343 == 0 ){ 								if(D22349 == 2 ){ 									if (event_press(PS4_CROSS)) { 										D22344 = D22569[D22343][0]; 										D22341 = FALSE; 										D22342 = TRUE; 										D22348 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									D22344 = D22569[D22343][0]; 									D22341 = FALSE; 									D22342 = TRUE; 									D22348 = TRUE; 																	} 															} 													} 						D22168(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, D22559[0]); 						D22157(D22343 + 1, D22163(D22343 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, D22561[0]); 						D22157(D22942, D22163(D22942), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(D22343 == 0 ){ 							if(D22349 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, D22563[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, D22564[0]); 															} 													} 						else{ 							if (D22569[D22343][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, D22564[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, D22563[0]); 															} 													} 											} 					if (D22342) { 						D22428               = D22151(D22428, 0); 						D22429               = D22151(D22429, 1); 						D22430             = D22151(D22430, 2); 						D22431           = D22151(D22431, 3); 						D22432             = D22151(D22432, 4); 						D22433             = D22151(D22433, 5); 						D22434              = D22151(D22434, 6); 						D22435           = D22151(D22435, 7); 						D22375          = D22151(D22375, 8); 						D22376   = D22151(D22376, 9); 						D22377 = D22151(D22377, 10); 						D22378      = D22151(D22378, 11); 						D22379    = D22151(D22379, 12); 						D22380    = D22151(D22380, 13); 						D22381    = D22151(D22381, 14); 						D22382      = D22151(D22382, 15); 						D22383      = D22151(D22383, 16); 						D22264              = D22151(D22264, 17); 						D22265           = D22151(D22265, 18); 						D22266            = D22151(D22266, 19); 						D22267            = D22151(D22267, 20); 						D22268= D22151(D22268, 21); 						D22396               = D22151(D22396, 22); 						D22397               = D22151(D22397, 23); 						D22398                   = D22151(D22398, 24); 						D22399                   = D22151(D22399, 25); 						D22400                   = D22151(D22400, 26); 						D22401   = D22151(D22401, 27); 						D22402   = D22151(D22402, 28); 						D22403 = D22151(D22403, 29); 						D22404   = D22151(D22404, 30); 						D22405   = D22151(D22405, 31); 						D22406 = D22151(D22406, 32); 						D22407   = D22151(D22407, 33); 						D22408   = D22151(D22408, 34); 						D22409 = D22151(D22409, 35); 						D22410   = D22151(D22410, 36); 						D22411   = D22151(D22411, 37); 						D22412 = D22151(D22412, 38); 						D22413   = D22154(D22413, 39); 						D22414         = D22154(D22414, 40); 						D22415   = D22151(D22415, 41); 						D22416     = D22151(D22416, 42); 						D22417                   = D22154(D22417, 43); 						D221240 = D22151(D221240, 54); 						D221233 = D22151(D221233, 55); 						D22418               = D22154(D22418, 44); 						D22419 = D22154(D22419, 45); 						D22420     = D22151(D22420, 46); 						D22421               = D22154(D22421, 47); 						D22422 = D22151(D22422, 48); 						D22423 = D22151(D22423, 49); 						D22424 = D22151(D22424, 50); 						D22425 = D22151(D22425, 51); 						D22426               = D22151(D22426, 52); 						D22427                 = D22151(D22427, 53); 						D22384       = D22154(D22384     ,56 ); 						D22385       = D22154(D22385     ,57 ); 						D22386      = D22151(D22386    ,58 ); 						D22387   = D22151(D22387 ,59 ); 						D22388       = D22154(D22388     ,60 ); 						D22389       = D22154(D22389     ,61 ); 						D22390   = D22151(D22390 ,62 ); 						D22391      = D22151(D22391    ,63 ); 						D22392          = D22154(D22392        ,64 ); 						D22393          = D22154(D22393        ,65 ); 						D22394         = D22151(D22394       ,66 ); 						D22444             = D22154(D22444           ,67 ); 						D2229             = D22151(D2229           ,68); 						D22465           = D22151(D22465         ,69); 						D22463         = D22151(D22463       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								D22344 = clamp(D22344 + 1, D22569[D22343][0], D22569[D22343][1]); 								D22348 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								D22344 = clamp(D22344 - 1, D22569[D22343][0], D22569[D22343][1]); 								D22348 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							D22341 = TRUE; 							D22342 = FALSE; 							D22348 = TRUE; 													} 						D22168(); 						D22944 = D22828[D22344][0]; 						D22945 = D22828[D22344][1]; 						if (D22828[D22344][4] == 0) { 							D22157(D22944, D22163(D22944), 4, 20, OLED_FONT_SMALL); 							D22157(D22945, D22163(D22945), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (D22348) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (D22342) { 							print(D22213(D22166(D22578[D22344]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D22578[D22344]); 													} 						else { 							print(D22213(D22166(D22579[D22343]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, D22579[D22343]); 													} 						D22348 = FALSE; 					} 									} 				if (!D22341 && !D22342) { 					if (D22347) { 						cls_oled(0); 						combo_run(D2272); 						D22347 = FALSE; 						D22345 = TRUE; 						vm_tctrl(0); 					} 					if(D22349 == 0){ 						D22436      = PS4_CIRCLE; 						D22437      = PS4_CROSS ; 						D22438    = PS4_L1    ; 						D22439  = PS4_R1; 						D22440    = PS4_L2; 						D22441    = PS4_R2; 						D22442     = PS4_SQUARE; 						D22443  = PS4_TRIANGLE; 					} 					else if(D22349 == 1){ 						D22436      = PS4_SQUARE; 						D22437      = PS4_CROSS ; 						D22438    = PS4_L1    ; 						D22439  = PS4_R1; 						D22440    = PS4_L2; 						D22441    = PS4_R2; 						D22442     = PS4_CIRCLE; 						D22443  = PS4_TRIANGLE; 					} 					else if(D22349 == 2){ 						D22436      = D221381[D22428]; 						D22437      = D221381[D22429] ; 						D22438    = D221381[D22430]  ; 						D22439  = D221381[D22431]; 						D22440    = D221381[D22432]; 						D22441    = D221381[D22433]; 						D22442     = D221381[D22434]; 						D22443  = D221381[D22435]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !D221187) { 						set_val(D22437, 0); 						vm_tctrl(0); 						combo_run(D2277); 											} 					if (D22707) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							D22583 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							D221042 = D221392[D22583][0]; 							D22659 = D221392[D22583][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							D22417 += 5; 							D22209(D22213(sizeof(D22585) - 1, OLED_FONT_MEDIUM_WIDTH), D22585[0], D22417); 													} 						if (event_press(PS4_LEFT)) { 							D22417 -= 5; 							D22209(D22213(sizeof(D22585) - 1, OLED_FONT_MEDIUM_WIDTH), D22585[0], D22417); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !D22605 ) { 						if (event_press(PS4_UP)) { 							D22590 += 25; 							D22209(D22213(sizeof(D22591) - 1, OLED_FONT_MEDIUM_WIDTH), D22591[0], D22590); 													} 						if (event_press(PS4_DOWN)) { 							D22590 -= 25; 							D22209(D22213(sizeof(D22591) - 1, OLED_FONT_MEDIUM_WIDTH), D22591[0], D22590); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (D22363) { 						D22247(); 											} 					if (D22364) { 						D22248(); 						D22249(); 											} 					if (!D22364) { 						if (get_ival(D22436)) { 							if (event_press(PS4_RIGHT)) { 								D22598 += 2; 								D22209(D22213(sizeof(D22599) - 1, OLED_FONT_MEDIUM_WIDTH), D22599[0], D22598); 															} 							if (event_press(PS4_LEFT)) { 								D22598 -= 2; 								D22209(D22213(sizeof(D22599) - 1, OLED_FONT_MEDIUM_WIDTH), D22599[0], D22598); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(D22438) ){ 							if(get_ival(D22436) && get_ptime(D22436) > D22598){ 								set_val(D22436,0); 															} 													} 											} 					if(D22367){ 						D22252(); 											} 					if (D22359) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								D22605 = !D22605; 								D22228(D22605); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (D22605 && D22359) { 						vm_tctrl(0); 						combo_stop(D2285); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								D22413 += 10; 								D22209(D22213(sizeof(D22607) - 1, OLED_FONT_MEDIUM_WIDTH), D22607[0], D22413); 															} 							if (event_press(PS4_DOWN)) { 								D22413 -= 10; 								D22209(D22213(sizeof(D22607) - 1, OLED_FONT_MEDIUM_WIDTH), D22607[0], D22413); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						D22221(D221086); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								D22612 = FALSE; 								vm_tctrl(0); 								combo_run(D2278); 															} 							if (event_press(PS4_LEFT)) { 								D22612 = TRUE; 								vm_tctrl(0); 								combo_run(D2278); 															} 							set_val(PS4_L1,0); 													} 											} 					if (D22360) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								D22614 = !D22614; 								D22228(D22614); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (D22614 && D22360) { 						vm_tctrl(0); 						D22221(D221088); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								D22615 = FALSE; 								vm_tctrl(0); 								combo_run(D2279); 															} 							if (event_press(PS4_RIGHT)) { 								D22615 = TRUE; 								vm_tctrl(0); 								combo_run(D2279); 															} 													} 											} 					if(D22352 || D22353 ){ 						D22117(); 											} 					if (D22350) { 						if (D22350 == D221092) D22618 = TRUE; 						if (D22350 == D221093) { 							if (event_press(D221391[-1 +D22383]) && get_brtime(D221391[-1 +D22383]) <= 200) { 								D22618 = !D22618; 								D22228(D22618); 															} 							set_val(D221391[-1 +D22383], 0); 													} 						if (D22350 > 0 && D22350 < 3 && D22618 == 1) { 							D22225(); 													} 						else if (D22350 == 3) { 							if (get_ival(D221391[-1 +D22383])) { 								D22225(); 															} 							set_val(D221391[-1 +D22383], 0); 													} 											} 									if (D22351) { 						if (D22351 == D221092) D22621 = TRUE; 						if (D22351 == D221093) { 							if (event_press(D221391[-1 +D22268]) && get_brtime(D221391[-1 +D22268]) <= 200) { 								D22621 = !D22621; 								D22228(D22621); 															} 							set_val(D221391[-1 +D22268], 0); 													} 						if (D22351 > 0 && D22351 < 3 && D22621 == 1) { 							D22223(); 													} 						else if (D22351 == 3) { 							if (get_ival(D221391[-1 +D22268])) { 								D22223(); 															} 							set_val(D221391[-1 +D22268], 0); 													} 											} 					if (D22354) { 						if (D22354 == 1) { 							D22624 = PS4_R3; 							D22621 = FALSE; 													} 						if (D22354 == 2) { 							D22624 = PS4_L3; 							D22621 = FALSE; 													} 						if (D22354 == 3) { 							D22624 = XB1_PR1; 							D22621 = FALSE; 													} 						if (D22354 == 4) { 							D22624 = XB1_PR2; 							D22621 = FALSE; 													} 						if (D22354 == 5) { 							D22624 = XB1_PL1; 							D22621 = FALSE; 													} 						if (D22354 == 6) { 							D22624 = XB1_PL2; 							D22621 = FALSE; 													} 						if(get_ival(D22624)){ 							if(D22396 || D22397){ 								if( D22396 && event_press(PS4_L1)){ 									D22468 = FALSE; 									D22512 = D22396  ; 									D22226( D22396   ); 								} 								if( D22397 && event_press(PS4_R1)){ 									D22468 = TRUE; 									D22512 =  D22397 ; 									D22226( D22397   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( D22398 ){ 								if(event_press(PS4_SQUARE)){ 									D22468 = FALSE; 									D22512 =  D22398  ; 													combo_stop(D2288); 				combo_stop(D2297); 				combo_stop(D2289); 				combo_stop(D2298); 				combo_stop(D2295); 				combo_stop(D2296); 				combo_stop(D2292); 				combo_stop(D2294); 				combo_stop(D2291); 				combo_stop(D2287); 				combo_stop(D2285); 				combo_stop(D2290); 				combo_stop(D22107); 				combo_stop(D22109); 				combo_stop(D22100); 				combo_stop(D22108); 				combo_stop(D2299); 									D22226( D22398   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									D22468 = TRUE; 									D22512 =  D22398  ; 									D22226( D22398   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( D22399 ){ 								if(event_press(PS4_CROSS)){ 									D22468 = FALSE; 									D22512 =  D22399  ; 									D22226( D22399   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(D2288); 				combo_stop(D2297); 				combo_stop(D2289); 				combo_stop(D2298); 				combo_stop(D2295); 				combo_stop(D2296); 				combo_stop(D2292); 				combo_stop(D2294); 				combo_stop(D2291); 				combo_stop(D2287); 				combo_stop(D2285); 				combo_stop(D2290); 				combo_stop(D22107); 				combo_stop(D22109); 				combo_stop(D22100); 				combo_stop(D22108); 				combo_stop(D2299); 									D22468 = TRUE; 									D22512 =  D22399  ; 									D22226( D22399   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( D22400 ){ 								if(event_press(PS4_R3)){ 									D22468 = FALSE; 									D22512 =  D22400  ; 									D22226( D22400   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(D22624,0); 											} 					if (D22355) { 						if (D22402 == 1) { 							if (event_press(D221391[-1 + D22401]) && !D221139) { 								vm_tctrl(0); 								combo_run(D2282); 															} 							else if (event_press(D221391[-1 + D22401]) && D221139) { 								set_val(D221391[-1 + D22401], 0); 								D22468 = !D22403; 								D22512 = D22355; 								D22226(D22355); 															} 													} 						else { 							if (event_press(D221391[-1 + D22401])) { 								D22468 = !D22403; 								set_val(D221391[-1 + D22401], 0); 								D22512 = D22355; 								D22226(D22355); 															} 													} 					} 					if (D22357) { 						if (D22408 == 1) { 							if (event_press(D221391[-1 +D22407]) && !D221139) { 								vm_tctrl(0); 								combo_run(D2282); 															} 							else if (event_press(D221391[-1 +D22407]) && D221139) { 								set_val(D221391[-1 +D22407], 0); 								D22468 = !D22409; 								D22512 = D22357; 								D22226(D22357); 															} 													} 						else { 							if (event_press(D221391[-1 +D22407])) { 								D22468 = !D22409; 								set_val(D221391[-1 +D22407], 0); 								D22512 = D22357; 								D22226(D22357); 															} 													} 					} 					if (D22356) { 						if (D22405 == 1) { 							if (event_press(D221391[-1 +D22404]) && !D221139) { 								vm_tctrl(0); 								combo_run(D2282); 															} 							else if (event_press(D221391[-1 +D22404]) && D221139) { 								set_val(D221391[-1 +D22404], 0); 								D22468 = !D22406; 								D22512 = D22356; 								D22226(D22356); 															} 													} 						else { 							if (event_press(D221391[-1 +D22404])) { 								D22468 = !D22406; 								set_val(D221391[-1 +D22404], 0); 								D22512 = D22356; 								D22226(D22356); 															} 													} 					} 					if (D22358) { 						if (D22411 == 1) { 							if (event_press(D221391[-1 +D22410]) && !D221139) { 								vm_tctrl(0); 								combo_run(D2282); 															} 							else if (event_press(D221391[-1 +D22410]) && D221139) { 								set_val(D221391[-1 +D22410], 0); 								D22468 = !D22412; 								D22512 = D22358; 								D22226(D22358); 															} 													} 						else { 							if (event_press(D221391[-1 +D22410])) { 								D22468 = !D22412; 								set_val(D221391[-1 +D22410], 0); 								D22512 = D22358; 								D22226(D22358); 															} 													} 					} 					if (D22512 == D22299 && combo_running(D2230)) set_val(D22438, 100); 					if(D22372){ 						if(!block){ 							if(!get_val(D22440)){ 								if( !get_val(D22441)){ 									if(get_val(D22437)){ 										D22640 += get_rtime(); 																			} 									if(D22391){ 										if(get_ival(D22437) && get_ptime(D22437) > D22389){ 											set_val(D22437,0); 																					} 																			} 									if(event_release(D22437)){ 										if( D22640 < D22388 ){ 											D22641 = D22388 - D22640; 											combo_run(D22107); 																					} 										else{ 											if(D22390) combo_run(D22108); 																					} 										D22640 = 0; 																			} 																	} 							} 						} 											} 					if(D22371){ 						if(!block){ 							if(!get_ival(D22440)){ 								if( !get_ival(D22441)){ 									if(get_ival(D22443)){ 										D22642 += get_rtime(); 																			} 									if(event_release(D22443)){ 										if(D22642 < D22384){ 											D22643 = D22384 - D22642; 											combo_run(D22109); 																					} 										else{ 											if(D22387) combo_run(D2299); 																					} 										D22642 = 0; 																			} 																	} 							} 						} 											} 					if(D22373){ 						if(!block){ 							if(get_ival(D22442)){ 								D22644 += get_rtime(); 															} 							if(D22394){ 								if(get_ival(D22442) && get_ptime(D22442) > D22393){ 									set_val(D22442,0); 																	} 															} 							if(event_release(D22442)){ 								if(D22644 && (D22644 < D22392)){ 									D22645 = D22392 - D22644; 									combo_run(D22100); 																	} 								D22644 = 0; 															} 													} 											} 					if (D22361) { 						if (event_press(D221391[-1 +D22415])) { 							vm_tctrl(0); 							combo_run(D2277); 													} 						set_val(D221391[-1 +D22415], 0); 											} 					if(!D22365){ 						D22418 = 0 ; 						D22419 = 0; 						D22420 = FALSE; 						D22421 = 0; 											} 					if (D22366) { 						D22248(); 						if (D22422 == 0) { 							D22648 = FALSE; 							D22446 = 0; 													} 						else { 							D22648 = TRUE; 							D22446 = 40; 													} 						if (D22423 == 0) { 							D22650 = FALSE; 							D22445 = 0; 													} 						else { 							D22650 = TRUE; 							D22445 = 85; 													} 						if (D22424 == 0) { 							D22652 = FALSE; 							D22447 = 0; 													} 						else { 							D22652 = TRUE; 							D22447 = -15; 													} 						if (D22425 == 0) { 							D22654 = FALSE; 													} 						else { 							D22654 = TRUE; 													} 						if(D22424 == 6 || D22423 == 6 || D22422 == 6){ 							if (get_ival(D221391[-1 + D22424]) || get_ival(D221391[-1 + D22423]) || get_ival(D221391[-1 + D22422])){ 								combo_run(D220); 															} 													} 						if (D22652) { 							if (get_val(D221391[-1 + D22424])) { 								set_val(D221391[-1 + D22424], 0); 								combo_run(D2297); 								D221196 = 9000; 															} 													} 						if (D22654) { 							if (get_val(D221391[-1 + D22425])) { 								set_val(D221391[-1 + D22425], 0); 								combo_run(D2298); 								D221196 = 9000; 							} 							if (combo_running(D2298)) { 								if (get_ival(D22437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D22441) > 30) { 									combo_stop(D2298); 																	} 															} 													} 						if (D22650) { 							if (get_val(D221391[-1 + D22423])) { 								set_val(D221391[-1 + D22423], 0); 								D22254(); 								D221196 = 9000; 															} 													} 						if (D22648) { 							if (get_val(D221391[-1 + D22422])) { 								set_val(D221391[-1 + D22422], 0); 								combo_run(D2295); 								D221196 = 9000; 															} 													} 											} 					else{ 						D22446 = 0; 						D22447 = 0; 						D22445 = 0; 											} 					if (D22368) { 						D22255(); 											} 									} 							} 								if (combo_running(D22111) && (  get_ival(D22437) ||   get_ival(D22443) ||         get_ival(D22440) ||        get_ival(D22436) ||        get_ival(D22439) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(D22111); 			} 					} 		else { 			if (!get_ival(D22441)) D22221(D221085); 					} 			} 			D22257(); 	} combo D221 { 	set_val(D22440, 100); 	set_val(D22438,100); 	D22242(); 	wait(400); 	set_val(D22437,100); 	wait(90); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D222 { 	set_val(D22440, 100); 	set_val(D22438,100); 	D22242(); 	wait(400); 	set_val(D22436,100); 	wait(220); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D223 { 	call(D2228); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	call(D2298); 	D22238(D221042, D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 800); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D22439,100); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	} combo D224 { 	D22244(); 	wait(50); 	D22242(); 	wait(50); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D225 { 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D226 { 	if (D22468) D22676 = D22583 + 1; 	else D22676 = D22583 - 1; 	D22233(D22676); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 1000); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D227 { 	D22245(); 	D22468 = FALSE; 	wait(D221045 + random(1,5)); 	D22242(); 	wait(D221045 + random(1,5)); 	D22245(); 	wait(D221045 + random(1,5)); 	D22468 = TRUE; 	D22242(); 	wait(D221045 + random(1,5)); 	wait(350); 	} combo D228 { 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22468 = TRUE; 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22468 = FALSE; 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D229 { 	D22468 = TRUE; 	D22242(); 	wait(D221045 + random(1,5)); 	D22245(); 	wait(D221045 + random(1,5)); 	D22468 = FALSE; 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2210 { 	D22468 = FALSE; 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22468 = TRUE; 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2211 { 	D22238(0,0); 	set_val(D22438,100); 	set_val(D22439,100); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	} combo D2212 { 	set_val(D221108, inv(D221042)); 	set_val(D221109, inv(D22659)); 	set_val(D22439, 100); 	set_val(D22438, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D221108, inv(D221042)); 	set_val(D221109, inv(D22659)); 	set_val(D22439, 100); 	set_val(D22438, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 500); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2213 { 	D22238(0, 0); 	set_val(D22440, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(0, 0); 	set_val(D22440, 100); 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(0, 0); 	set_val(D22440, 100); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	D22238(0, 0); 	set_val(D22440, 100); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2214 { 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(inv(D221042), inv(D22659)); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	D22238(inv(D221042), inv(D22659)); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2215 { 	set_val(D22438, 100); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 500); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2216 { 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22468) D22676 = D22583 + 3; 	else  D22676 = D22583 - 3; 	D22233(D22676); 	D22235(D221143,D22662); 	wait(D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22468) D22676 = D22583 + 1; 	else  D22676 = D22583 - 1; 	D22233(D22676); 	D22235(D221143,D22662); 	wait(D221045 + random(1,5)); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2217 { 	set_val(D22438,100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2218 { 	set_val(D22440,100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440,100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440,100); 	D22242(); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D22440,100); 	set_val(D22441,100); 	wait(50); 	wait(350); 	} combo D2219 { 	set_val(D22440,100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440,100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440,100); 	D22242(); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2220 { 	D22243(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	D22242()   if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22468 = !D22468; 	D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 1000); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2221 { 	set_val(D22438,100); 	D22245(); 	wait(50); 	D22238(0,0); 	set_val(D22438,100); 	wait(50); 	set_val(D22438,100); 	D22245(); 	wait(50); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2222 { 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2223 { 	D22245(); 	wait(D221045 + random(1,5)); 	D22246()wait(D221045 + random(1,5)); 	D22245(); 	wait(D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2224 { 	set_val(D22439, 100); 	set_val(D22438, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 20); 	set_val(D22439, 100); 	set_val(D22438, 100); 	if (D22468) D22676 = D22583 + 4; 	else { 		D22676 = D22583 - 4; 			} 	D22233(D22676); 	D22235(D221143, D22662); 	set_val(D22441, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2225 { 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 30); 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2226 { 	set_val(D22440, 100); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D22440, 100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2227 { 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 30); 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2228 { 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	set_val(PS4_R3,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); } combo D2229 { 	D22242(); 	set_val(D22441, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 310); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2230 { 	if (D22512 == D22301) D221046 = 200; 	else D221046 = 1; 	if(D22113 > 4)vm_tctrl(0); 	wait( D221046); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2231 { 	set_val(D22438, 100)D22244(); 	D22238(D221042,D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D22438, 100)D22246(); 	D22238(D221042,D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D22438, 100)D22242(); 	D22238(D221042,D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	D22238(D221042,D22659); 	wait(465); 	D22238(D221042,D22659); 	set_val(D22440, 100); 	set_val(D22441, 100); 	wait(50); 	if (D22468) D22676 = D22583 - 1; 	else D22676 = D22583 + 1; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	if (D22468) D22676 = D22583 + 4; 	else D22676 = D22583 - 4; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 700); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2232 { 	if (D22512 == D22301) D221046 = 200; 	else D221046 = 1; 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221046); 	D22244(); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2233 { 	if (D22468) D22676 = D22583 - 2; 	else D22676 = D22583 + 2; 	D22233(D22676); 	D22235(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 280); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	if (D22468) D22676 = D22583 + 2; 	else D22676 = D22583 - 2; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 45); 	set_val(D22436, 100); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 45); 	D22238(D221143, D22662); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 45); 	D22238(D221143, D22662); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 45); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 500); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2234 { 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 280); 	D22241()  set_val(D22436, 100); 	set_val(D22440, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22241()  set_val(D22440, 100); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22241()  set_val(D22440, 100); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 250); 	D22241()  if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2235 { 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2236 { 	set_val(D22438, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22438, 100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22438, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2237 { 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2238 { 	set_val(D22438, 100); 	D22243(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22438, 100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22438, 100); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2239 { 	D22245(); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22242(); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2240 { 	if (D22468) D22676 = D22583 + 3; 	else D22676 = D22583 - 3; 	D22233(D22676); 	D22238(D221143, D22662); 	set_val(D22436, 100); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440,100); 	D22238(D221143, D22662); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D22440,100); 	D22238(D221143, D22662); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440,100); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2241 { 	set_val(D22438, 100); 	D22244(); 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22438, 100); 	D22246(); 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 65); 	set_val(D22438, 100); 	D22238(0, 0); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if (D22468) D22676 = D22583 + 1; 	else D22676 = D22583 - 1; 	D22233(D22676); 	set_val(D22441,0); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 200); 	set_val(D22441,0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2242 { 	if (D22512 == D22301) D221046 = 200; 	else D221046 = 1; 	if(D22113 > 4)vm_tctrl(0); 	wait( D221046); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2243 { 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2244 { 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if (D22512 == D22314) D22241(); 	set_val(D22440, 100); 	set_val(D22441, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 200); 	if (D22512 == D22314) D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2245 { 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	if (D22512 == D22314) D22241(); 	set_val(D22440, 100); 	set_val(D22441, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 200); 	if (D22512 == D22314) D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2246 { 	call(D2233)call(D2235); 	} combo D2247 {    D22707 = FALSE; 	D22238(D221042, D22659); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(D221042, D22659); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	D22707 = FALSE; 	wait( D221045 + random(1,5)); 	D22238(D221042, D22659); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	D22707 = FALSE; 	wait( D221045 + random(1,5)); 	set_val(D22440, 100); 	set_val(D22441, 100); 	D22238(inv(D221042), inv(D22659)); 	if(D22113 > 4)vm_tctrl(0); 	D22707 = FALSE; 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	D22707 = TRUE; 	} combo D2248 { 	D22238(D221042, D22659); 	set_val(XB1_LS, 100); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(D221042, D22659); 	D22246(); 	set_val(XB1_LS, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	D22238(D221042, D22659); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( D221045 + random(1,5)); 	set_val(D22440, 100); 	set_val(D22441, 100); 	if (D22468) D22676 = D22583 + 4; 	else D22676 = D22583 - 4; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 220); 	if (D22468) D22676 = D22583 + 4; 	else D22676 = D22583 - 4; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if (D22468) D22676 = D22583 + 1; 	else D22676 = D22583 - 1; 	D22233(D22676); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 600); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2249 { 	set_val(D22437, 0); 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 80); 	if(D22113 > 4)vm_tctrl(0); 	wait( 500); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2250 { 	set_val(D22436, 100); 	set_val(D22441,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22441,100); 	set_val(D22436, 100); 	set_val(D22437, 100); 	set_val(D22441,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22436, 0); 	set_val(D22437, 100); 	set_val(D22441,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2251 { 	set_val(D22438,100); 	set_val(D22439,100); 	D22238(inv(D221042), inv(D22659)); 	if(D22113 > 4)vm_tctrl(0); 	wait( 200); 	set_val(D22438,100); 	set_val(D22439,100); 	D22468 = FALSE; 	D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 50); 	set_val(D22438,100); 	set_val(D22439,100); 	D22468 = !D22468; 	D22241(); 	set_val(D22438,100); 	set_val(D22439,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 540); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2252 { 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2253 { 	D22238(inv(D221042), inv(D22659)); 	set_val(D22440, 100); 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(inv(D221042), inv(D22659)); 	set_val(D22440, 100); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(inv(D221042), inv(D22659)); 	set_val(D22440, 100); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2254 { 	set_val(D22438, 100); 	set_val(D22442, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22438, 100); 	set_val(D22442, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22438, 100); 	set_val(D22442, 0); 	set_val(D22437, 100); 	D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22438, 100); 	D22241(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2255 { 	set_val(D22436, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo D2256 { 	set_val(D22436, 100); 	set_val(D22440,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440,100); 	set_val(D22436, 100); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440,100); 	set_val(D22436, 0); 	set_val(D22437, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2257 { 	set_val(D22438, 100); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 300); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2258 { 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2259 { 	set_val(D22438,100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D22438,100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22244(); 	set_val(D22438,100); 	wait(50); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2260 { 	D22238(D221042, D22659); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	D22246(); 	D22238(D221042, D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	D22244(); 	D22238(D221042, D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 320); 	D22238(D221042, D22659); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 220); 	D22238(D221042, D22659); 	D22244(); 	D22238(D221042, D22659); 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2261 { 	call(D2283); 	D22238(0, 0); 	call(D2284); 	call(D2284); 	call(D2284); 	call(D2284); 	call(D2284); 	set_val(D22440, 100); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D22440, 100); 	D22246(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440, 100); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(D22440, 100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 600); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2262 { 	set_val(D22440,100); 	set_val(D22439,100); 	if (D22468) D22676 = D22583 - 2; 	else D22676 = D22583 + 2; 	D22233(D22676); 	D22235(D221143, D22662); 	wait(50); 	set_val(D22439,100); 	set_val(D22440,100); 	if (D22468) D22676 = D22583 - 3; 	else D22676 = D22583 + 3; 	D22233(D22676); 	D22235(D221143, D22662); 	wait(50); 	set_val(D22439,100); 	set_val(D22440,100); 	if (D22468) D22676 = D22583 - 4; 	else D22676 = D22583 + 4; 	D22233(D22676); 	D22235(D221143, D22662); 	wait(50); 	set_val(D22439,100); 	set_val(D22440,100); 	if (D22468) D22676 = D22583 - 5; 	else D22676 = D22583 + 5; 	D22233(D22676); 	D22235(D221143, D22662); 	set_val(D22440,100); 	set_val(D22439,100); 	wait(50); 	set_val(D22439,100); 	set_val(D22440,100); 	if (D22468) D22676 = D22583 - 6; 	else D22676 = D22583 + 6; 	D22233(D22676); 	D22235(D221143, D22662); 	wait(50); 	} combo D2263 { 	if(D22113 > 4)vm_tctrl(0); 	wait( 100); 	D22238(0, 0); 	D22244(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(0, 0); 	D22246()  if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(0, 0); 	D22244()  if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(0, 0); 	D22246()  if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(0, 0); 	D22245(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(0, 0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2264 { 	set_val(PS4_R3,100); 	if (D22468) D22676 = D22583 + 1; 	else D22676 = D22583 - 1; 	D22233(D22676); 	D22238(D221143, D22662); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2265 { 	call(D2283); 	D22238(0,0); 	if(D22113 > 4)vm_tctrl(0); 	wait( 60); 	set_val(PS4_R3,100); 	if (D22468) D22676 = D22583 + 1; 	else D22676 = D22583 - 1; 	D22233(D22676); 	D22238(D221143, D22662); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2266 { 	call(D2283); 	D22238(0,0); 	set_val(D22440,100); 	set_val(D22441,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 750); 	} combo D2267 { 	set_val(PS4_R3,100); 	if (D22468) D22676 = D22583 + 2; 	else D22676 = D22583 - 2; 	D22233(D22676); 	D22238(D221143, D22662); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2268 { 	set_val(D22440,100); 	set_val(PS4_R3,100); 	if (D22468) D22676 = D22583 ; 	else D22676 = D22583; 	D22233(D22676); 	D22238(D221143, D22662); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D22440,100); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2269 { 	call(D2283); 	set_val(D22440,100); 	set_val(PS4_R3,100); 	if (D22468) D22676 = D22583 ; 	else D22676 = D22583; 	D22233(D22676); 	D22238(D221143, D22662); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 70); 	set_val(D22440,100); 	D22238(D221143, D22662); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	} combo D2270 { 	D22238(0,0); 	set_val(D22439,100); 	set_val(D22438,100); 	D22242(); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	if(D22113 > 4)vm_tctrl(0); 	wait( 350); 	set_val(D22439,100); 	set_val(D22438,100); 	if(D22113 > 4)vm_tctrl(0); 	wait( 400); 	} int D22140 ; int D22753 ; int D22754 ; int D22755; int D22756; function D22126(D22127){ 	D22753 = 2; 	D22754 = 987654; 	D22140 = 54321; 	D22755 = (D22127 >> D22753) | (D22127 << (32 - D22753)); 	D22756 = (((D22755 >> ((D22755 & 0xF) % 13)) & 0x7FFFF) + D22140) % D22754 + 123456; 	return D22756; 	} define D22758 = -1; define D22526 = -2; define D22760 = -3; define D22761 = 0; define D22527 = 1; function D22128(D22127, D22130, D22131) { 	if(D22127 > D22131) return D22130; 	if(D22127 < D22130) return D22131; 	return D22127; 	} int D22765,D22766; function D22132(D22133,D22134,D22135,D22136,D22137,D22138){ 	if(!D22138){ 		print(D22141(D22139(D22133),D22136,D22134),D22135,D22136,D22137,D22133)     	} 	else{ 		if(D22133 < 0){ 			putc_oled(1,45); 					} 		if(D22133){ 			for(D22765 = D22145(D22133) + D22766 = (D22133 < 0 ),D22133 = abs(D22133); 			D22133 > 0; 			D22765-- , D22766++){ 				putc_oled(D22765,D22133%10 + 48); 				D22133 = D22133/10; 							} 					} 		else{ 			putc_oled(1,48); 			D22766 = 1         		} 		puts_oled(D22141(D22766,D22136,D22134),D22135,D22136,D22766 ,D22137); 			} 	} int D22787; function D22139(D22140) { 	D22787 = 0; 	do { 		D22140++; 		D22787++; 			} 	while (duint8(D22140)); 	return D22787; 	} function D22141(D22142,D22136,D22134) { 	if(D22134 == -3){ 		return 128 - ((D22142 * (7 + (D22136 > 1) + D22136 * 4)) + 3 ); 			} 	if(D22134 == -2){ 		return 64 - ((D22142 * (7 + (D22136 > 1) + D22136 * 4)) / 2); 			} 	if(D22134 == -1){ 		return 3 	} 	return D22134; 	} function D22145(D22146) { 	for(D22765 = 1; 	D22765 < 11; 	D22765++){ 		if(!(abs(D22146) / pow(10,D22765))){ 			return D22765; 			break; 					} 			} 	return 1; 	} function D22147() { 	if (get_ival(D22436)) { 		set_val(D22436, 0); 		if (get_ival(D22438)) D22794 = 50; 		if (!get_ival(D22438)) D22794 = 410; 		combo_run(D2271); 			} 	if (D22793 > 0) set_polar(POLAR_LS, D22793 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) D22793 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) D22793 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) D22793 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) D22793 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D22793 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) D22793 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D22793 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) D22793 = 270; } int D22794; int D22547; int D22793; combo D2271 { 	set_val(D22436, 100); 	vm_tctrl(0);wait( D22794); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	D22547 = !D22547; } define D22797 = 19; function D22148(D22149, D22150) { 	if (D22343 == D22150) { 		if (event_press(PS4_RIGHT)) { 			D22149 = clamp(D22149 + 1, 0, D22800[D22343]); 			D22348 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			D22149 = clamp(D22149 - 1, 0, D22800[D22343]); 			D22348 = TRUE; 					} 		if (D22343 == 0) { 			print(D22213(D22166(D22804[D22349]) ,OLED_FONT_SMALL_WIDTH),D22797  ,OLED_FONT_SMALL , OLED_WHITE ,D22804[D22349]); 					} 		else if (D22343 == 1) { 			print(D22213(D22166(D22806[D22350]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22806[D22350]); 					} 		else if (D22343 == 2) { 			print(D22213(D22166(D22806[D22351]) ,OLED_FONT_SMALL_WIDTH ),D22797  ,OLED_FONT_SMALL , OLED_WHITE ,D22806[D22351]); 					} 		else if (D22343 == 5) { 			print(D22213(D22166(D22810[D22354]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22810[D22354]); 					} 		else if (D22343 == 6) { 			print(D22213(D22166(D22812[D22355]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22355]); 					} 		else if (D22343 == 7) { 			print(D22213(D22166(D22812[D22356]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22356]); 					} 		else if (D22343 == 8) { 			print(D22213(D22166(D22812[D22357]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22357]); 					} 		else if (D22343 == 9) { 			print(D22213(D22166(D22812[D22358]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22358]); 					} 		else if (D22343 == 20) { 			print(D22213(D22166(D22820[D22113]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22820[D22113]); 					} 		else { 			if (D22149 == 1)        print(D22213(D22166(D22822[1]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22822[1])      else        print(D22213(D22166(D22822[0]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22822[0])     		} 			} 	return D22149; 	} function D22151(D22149, D22150) { 	if (D22344 == D22150) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D22149 += D22828[D22344][2]  				        D22348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D22149 -= D22828[D22344][2]  				        D22348 = TRUE; 							} 			D22149 = clamp(D22149, D22828[D22344][0], D22828[D22344][1]); 		} 		if (D22344 == 8) { 			print(D22213(D22166(D22812[D22375]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22375])     		} 		else if (D22344 == 9) { 			print(D22213(D22166(D22812[D22376]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22376])     		} 		else if (D22344 == 10) { 			print(D22213(D22166(D22812[D22377]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22377])     		} 		else if (D22344 == 11) { 			print(D22213(D22166(D22812[D22378]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22378])     		} 		else if (D22344 == 12) { 			print(D22213(D22166(D22812[D22379]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22379])     		} 		else if (D22344 == 13) { 			print(D22213(D22166(D22812[D22380]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22380])     		} 		else if (D22344 == 14) { 			print(D22213(D22166(D22812[D22381]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22381])     		} 		else if (D22344 == 15) { 			print(D22213(D22166(D22812[D22382]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22382])     		} 		else if (D22344 == 16) { 			print(D22213(D22166(D22847[D22383]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22383])     		} 		else if (D22344 == 17) { 			print(D22213(D22166(D22812[D22264]),OLED_FONT_SMALL_WIDTH ),D22797,OLED_FONT_SMALL,OLED_WHITE,D22812[D22264])  		} 		else if(D22344 == 18){ 			print(D22213(D22166(D22812[D22265]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22812[D22265])  		} 		else if(D22344 == 19){ 			print(D22213(D22166(D22812[D22266]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22812[D22266])  		} 		else if(D22344 == 20){ 			print(D22213(D22166(D22812[D22267]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22812[D22267])  		} 		else if(D22344 == 21){ 			print(D22213(D22166(D22847[D22268]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22847[D22268])       		} 		else if(D22344 == 22){ 			print(D22213(D22166(D22812[D22396]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22396])     		} 		else if (D22344 == 23) { 			print(D22213(D22166(D22812[D22397]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22397])     		} 		else if (D22344 == 24) { 			print(D22213(D22166(D22812[D22398]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22398])     		} 		else if (D22344 == 25) { 			print(D22213(D22166(D22812[D22399]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22399])     		} 		else if (D22344 == 26) { 			print(D22213(D22166(D22812[D22400]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22812[D22400])     		} 		else if (D22344 == 27) { 			print(D22213(D22166(D22847[D22401]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22401])     		} 		else if (D22344 == 28) { 			print(D22213(D22166(D22871[D22402]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22871[D22402])     		} 		else if (D22344 == 29) { 			print(D22213(D22166(D22873[D22403]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22873[D22403])     		} 		else if (D22344 == 30) { 			print(D22213(D22166(D22847[D22404]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22404])     		} 		else if (D22344 == 31) { 			print(D22213(D22166(D22871[D22405]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22871[D22405])     		} 		else if (D22344 == 32) { 			print(D22213(D22166(D22873[D22406]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22873[D22406])     		} 		else if (D22344 == 33) { 			print(D22213(D22166(D22847[D22407]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22407])     		} 		else if (D22344 == 34) { 			print(D22213(D22166(D22871[D22408]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22871[D22408])     		} 		else if (D22344 == 35) { 			print(D22213(D22166(D22873[D22409]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22873[D22409])     		} 		else if (D22344 == 36) { 			print(D22213(D22166(D22847[D22410]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22410])     		} 		else if (D22344 == 37) { 			print(D22213(D22166(D22871[D22411]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22871[D22411])     		} 		else if (D22344 == 38) { 			print(D22213(D22166(D22873[D22412]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22873[D22412])     		} 		else if (D22344 == 41) { 			print(D22213(D22166(D22847[D22415]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22415])     		} 		else if (D22344 == 48) { 			print(D22213(D22166(D22847[D22422]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22422])     		} 		else if (D22344 == 49) { 			print(D22213(D22166(D22847[D22423]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22423])     		} 		else if (D22344 == 50) { 			print(D22213(D22166(D22847[D22424]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22424])     		} 		else if (D22344 == 51) { 			print(D22213(D22166(D22847[D22425]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22847[D22425])     		} 		else if(D22344 == 0){ 			print(D22213(D22166(D22903[D22428]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22428])  		} 		else if(D22344 == 1){ 			print(D22213(D22166(D22903[D22429]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22429])  		} 		else if(D22344 == 2){ 			print(D22213(D22166(D22903[D22430]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22430])  		} 		else if(D22344 == 3){ 			print(D22213(D22166(D22903[D22431]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22431])  		} 		else if(D22344 == 4){ 			print(D22213(D22166(D22903[D22432]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22432])  		} 		else if(D22344 == 5){ 			print(D22213(D22166(D22903[D22433]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22433])  		} 		else if(D22344 == 6){ 			print(D22213(D22166(D22903[D22434]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22434])  		} 		else if(D22344 == 7){ 			print(D22213(D22166(D22903[D22435]),OLED_FONT_SMALL_WIDTH),D22797,OLED_FONT_SMALL,OLED_WHITE,D22903[D22435])  		} 		else{ 			if (D22149 == 1)        print(D22213(D22166(D22822[1]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22822[1])      else        print(D22213(D22166(D22822[0]), OLED_FONT_SMALL_WIDTH), D22797, OLED_FONT_SMALL, OLED_WHITE, D22822[0])     		} 		D22169(0); 			} 	return D22149; 	} function D22154(D22149, D22150) { 	if (D22344 == D22150) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				D22149 += D22828[D22344][2]  				        D22348 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				D22149 -= D22828[D22344][2]  				        D22348 = TRUE; 							} 			if (event_press(PS4_UP)) { 				D22149 += D22828[D22344][3]  				        D22348 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				D22149 -= D22828[D22344][3]  				        D22348 = TRUE; 							} 			D22149 = clamp(D22149, D22828[D22344][0], D22828[D22344][1]); 		} 		D22216(D22149, D22219(D22149)); 	} 	return D22149; 	} int D22930, D22931, D22932; function D22157(D22127, D22159, D22160, D22161, D22136) { 	D22931 = 1; 	D22932 = 10000; 	if (D22127 < 0)  	  { 		putc_oled(D22931, 45); 		D22931 += 1; 		D22127 = abs(D22127); 			} 	for (D22930 = 5; 	D22930 >= 1; 	D22930--) { 		if (D22159 >= D22930) { 			putc_oled(D22931, D22938[D22127 / D22932]); 			D22127 = D22127 % D22932; 			D22931 += 1; 					} 		D22932 /= 10; 			} 	puts_oled(D22160, D22161, D22136, D22931 - 1, OLED_WHITE); } const string D22564 = " No Edit Variable"; const string D22563 = " A/CROSS to Edit "; const string D22559 = "MOD;"; const string D22561 = "MSL;"; int D22942; function D22163(D22146) { 	D22146 = abs(D22146); 	if (D22146 / 10000 > 0) return 5; 	if (D22146 / 1000 > 0) return 4; 	if (D22146 / 100 > 0) return 3; 	if (D22146 / 10 > 0) return 2; 	return 1; 	} const int8 D22938[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int D22944, D22945; const image D22947 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo D2272 { 	call(D2273); 	D22165(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, D22947[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, D22947[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(D2274); 	vm_tctrl(0);wait( 1000); 	D22345 = TRUE; 	} combo D2273 { 	cls_oled(OLED_BLACK); 	} int D22949; enum { 	D22950 = -2, D22951, D22952 = 5, D22953 = -1, D22954 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo D2274 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 D221375[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 D221376[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 D221377[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 D221378[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int D22955; int D22956; int D22957; int D22958; int D22959; int D22960; int D22961; function D22165() { 	D22961 = 3; 	D22959 = D22961 * D221378[D22960]; 	D22958 = D22961 * D221375[D22960]; 	D22956 = ((D22959 * D221377[D22955]) / 328) - ((D22958 * D221376[D22955]) / 328); 	D22957 = ((D22959 * D221376[D22955]) / 328) + ((D22958 * D221377[D22955]) / 328); 	D22959 = D22956; 	D22958 = D22957; 	D22960 += 1; 	D22955 += 45; 	if(D22960 >= 360) { 		D22960 %= 360; 			} 	if(D22955 >= 360) { 		D22955 %= 360; 			} 	pixel_oled(64 + (((D22959 / D22961) * 30) / 328), 32 + (((D22958 / D22961) * 30) / 328), OLED_WHITE); 	} int D22965; function D22166(D22140) { 	D22965 = 0; 	do { 		D22140++; 		D22965++; 			} 	while (duint8(D22140)); 	return D22965; 	} int D22968; const uint8 D221379[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function D22168() { 	for (D22968 = 0; 	D22968 < sizeof(D221379) / sizeof(D221379[0]); 	D22968++) { 		if (get_ival(D221379[D22968]) || event_press(D221379[D22968])) { 			set_val(D221379[D22968], 0); 		} 			} 	} define D22969 = 131; define D22970 = 132; define D22971 = 133; define D22972 = 134; define D22973 = 130; define D22974 = 89; define D22975 = 127; define D22976 = 65; int D22977; int D22978; int D22979 = 1; define D22980 = 36; const string D22981 = "Hold LT/L2 +"; function D22169(D22170) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, D22981[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, D22979); 	putc_oled(1, D22971); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, D22977); 	putc_oled(1, D22972); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, D22978); 	if (D22170) { 		putc_oled(1, D22969); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, D22977); 		putc_oled(1, D22970); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, D22978); 			} 	} const uint8 D221381 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function D22171() { 	D22991 = sizeof(data); 	D22497 = get_pvar(SPVAR_1,0,1,0); 	D22503 = get_pvar(SPVAR_2,0,1,0); 	D22496 = get_pvar(SPVAR_3,11111, 99999,11111); 	D22173(); 	if (D22198(0, 1, 0)) { 		D22354 = D22198(  0, 6, 0); 		D22351 = D22198(0, 3, 0); 		D22352 = D22198(0,1,0); 		D22353 = D22198(0,1,0); 		D22264 = D22198(0, 70, 0); 		D22265 = D22198(0, 70, 0); 		D22266 = D22198(0, 70, 0); 		D22267 = D22198(0, 70, 0); 		D22268 = D22198(0, 22, 8); 		D22355 = D22198(0, 70, 0); 		D22356 = D22198(0, 70, 0); 		D22357 = D22198(0, 70, 0); 		D22358 = D22198(0, 70, 0); 		D22359 = D22198(0, 1, 0); 		D22360 = D22198(0, 1, 0); 		D22361 = D22198(0, 1, 0); 		D22362 = D22198(0, 1, 0); 		D22370 = D22198(0, 1, 0); 		D22396 = D22198(0, 70, 0); 		D22397 = D22198(0, 70, 0); 		D22398 = D22198(0, 70, 0); 		D22399 = D22198(0, 70, 0); 		D22400 = D22198(0, 70, 0); 		D22401 = D22198(1, 25, 1); 		D22402 = D22198(0, 1, 0); 		D22403 = D22198(0, 1, 0); 		D22404 = D22198(1, 25, 5); 		D22405 = D22198(0, 1, 0); 		D22406 = D22198(0, 1, 0); 		D22407 = D22198(0, 25, 2); 		D22408 = D22198(0, 1, 0); 		D22409 = D22198(0, 1, 1); 		D22410 = D22198(1, 25, 8); 		D22411 = D22198(0, 1, 0); 		D22412 = D22198(0, 1, 1); 		D22413 = D22198(350, 600, 350); 		D22414 = D22198(350, 600, 445); 		D22415 = D22198(0, 22, 0); 		D22416 = D22198(0, 1, 0); 		D22417 = D22198(-100, 300, 0); 		D22363 = D22198(0, 1, 0); 		D22364 = D22198(0, 1, 0); 		D22365 = D22198(0, 1, 0); 		D22366 = D22198(0, 1, 0); 		D22367 = D22198(0, 1, 0); 		D22418 = D22198(-150, 150, 0); 		D22419 = D22198(-150, 150, 0); 		D22420 = D22198(0, 1, 0); 		D22421 = D22198(-150, 150, 0); 		D22422 = D22198(0, 22, 0); 		D22423 = D22198(0, 22, 0); 		D22424 = D22198(0, 22, 0); 		D22425 = D22198(0, 22, 0); 		D22598 = D22198(60, 400, 235); 		D22427 = D22198(0, 1, 0); 		D22426 = D22198(0, 1, 0); 		D22350 = D22198(0, 3, 0); 		D22375 = D22198(0, 70, 0); 		D22376 = D22198(0, 70, 0); 		D22377 = D22198(0, 70, 0); 		D22380 = D22198(0, 70, 0); 		D22381 = D22198(0, 70, 0); 		D22382 = D22198(0, 70, 0); 		D22383 = D22198(0, 22, 8); 		D22368 = D22198(0, 1, 0); 		D22378 = D22198(0, 70, 0); 		D22379 = D22198(0, 70, 0); 		D22590 = D22198(0, 2500, 1100); 		D221240 = D22198(0, 1, 0); 		D221233 = D22198(0, 1, 0); 		D22113 = D22198(0, 6, 0); 		D22395 = D22198(0, 1, 0); 		D22349 = D22198(0, 2, 0); 		D22428 = D22198(0, 9, 9); 		D22429 = D22198(0, 9, 8); 		D22430 = D22198(0, 9, 3); 		D22431 = D22198(0, 9, 1); 		D22432 = D22198(0, 9, 4); 		D22433 = D22198(0, 9, 0); 		D22434 = D22198(0, 9, 7); 		D22435 = D22198(0, 9, 6); 		D22371    = D22198(0, 1, 0); 		D22372    = D22198(0, 1, 0); 		D22373     = D22198(0, 1, 0); 		D22384     = D22198(60, 500, 120); 		D22385     = D22198(60, 500, 350); 		D22386    = D22198(0, 1, 0); 		D22387 = D22198(0, 1, 0); 		D22388     = D22198(50, 250, 80); 		D22389     = D22198(100, 850, 180); 		D22390 = D22198(0, 1, 0); 		D22391    = D22198(0, 1, 0); 		D22392        = D22198(80, 500, 120); 		D22393        = D22198(80, 500, 350); 		D22394       = D22198(0, 1, 0); 		D22444           = D22198(2800, 12000, 5000); 		D2229           = D22198(0, 1, 0); 		D22465         = D22198(0, 1, 0); 		D22463       = D22198(0, 1, 0); 	} 	else{ 		D22354 = 0; 		D22351 = 0; 		D22352 = 0; 		D22353 = 0; 		D22264 = 0; 		D22265 = 0; 		D22266 = 0; 		D22267 = 0; 		D22268 = 8; 		D22355 = 0; 		D22356 = 0; 		D22357 = 0; 		D22358 = 0; 		D22359 = 0; 		D22360 = 0; 		D22361 = 0; 		D22362 = 0; 		D22370 = 0; 		D22396 = 0; 		D22397 = 0; 		D22398 = 0; 		D22399 = 0; 		D22400 = 0; 		D22401 = 1; 		D22402 = 0; 		D22403 = 0; 		D22404 = 5; 		D22405 = 0; 		D22406 = 0; 		D22407 = 2; 		D22408 = 0; 		D22409 = 1; 		D22410 = 8; 		D22411 = 0; 		D22412 = 1; 		D22413 = 350; 		D22414 = 445; 		D22415 = 0; 		D22416 = 0; 		D22417 = 0; 		D22363 = 0; 		D22364 = 0; 		D22365 = 0; 		D22366 = 0; 		D22367 = 0; 		D22418 = 0; 		D22419 = 0; 		D22420 = 0; 		D22421 = 0; 		D22422 = 0; 		D22423 = 0; 		D22424 = 0; 		D22425 = 0; 		D22598 = 235; 		D22427 = 0; 		D22426 = 0; 		D22350 = 0; 		D22375 = 0; 		D22376 = 0; 		D22377 = 0; 		D22380 = 0; 		D22381 = 0; 		D22382 = 0; 		D22383 = 8; 		D22368 = 0; 		D22378 = 0; 		D22379 = 0; 		D22590 = 1100; 		D221240 = 0; 		D221233 = 0; 		D22113 = 0; 		D22395 = 0; 		D22349 = 0; 		D22428 = 9; 		D22429 = 8; 		D22430 = 3; 		D22431 = 1; 		D22432 = 4; 		D22433 = 0; 		D22434 = 7; 		D22435 = 6; 		D22371 = 0; 		D22372 = 0; 		D22373 = 0; 		D22384 = 120; 		D22385 = 350; 		D22386 = 0; 		D22387 = 0; 		D22388 = 80; 		D22389 = 180; 		D22390 = 0; 		D22391 = 0; 		D22392 = 120; 		D22393 = 360; 		D22394 = 0; 		D22444     = 5000; 		D2229     = 0; 		D22465     = 0; 		D22463     = 0; 			} 	if (D22349 == 0) { 		D22436 = PS4_CIRCLE; 		D22437 = PS4_CROSS; 		D22438 = PS4_L1; 		D22439 = PS4_R1; 		D22440 = PS4_L2; 		D22441 = PS4_R2; 		D22442 = PS4_SQUARE; 		D22443 = PS4_TRIANGLE; 			} 	else if (D22349 == 1) { 		D22436      = PS4_SQUARE; 		D22437      = PS4_CROSS ; 		D22438    = PS4_L1    ; 		D22439  = PS4_R1; 		D22440    = PS4_L2; 		D22441    = PS4_R2; 		D22442     = PS4_CIRCLE; 		D22443  = PS4_TRIANGLE; 	} 	else if (D22349 == 2) { 		D22436 = D221381[D22428]; 		D22437 = D221381[D22429]; 		D22438 = D221381[D22430]; 		D22439 = D221381[D22431]; 		D22440 = D221381[D22432]; 		D22441 = D221381[D22433]; 		D22442 = D221381[D22434]; 		D22443 = D221381[D22435]; 			} 	} function D22172() { 	D22173(); 	D22196(   1,0,     1); 	D22196(D22354, 0, 6); 	D22196(D22351, 0, 3); 	D22196(D22352, 0 , 1); 	D22196(D22353, 0 , 1); 	D22196(D22264, 0, 70); 	D22196(D22265, 0, 70); 	D22196(D22266, 0, 70); 	D22196(D22267, 0, 70); 	D22196(D22268, 0, 22); 	D22196(D22355, 0, 70); 	D22196(D22356, 0, 70); 	D22196(D22357, 0, 70); 	D22196(D22358, 0, 70); 	D22196(D22359, 0, 1); 	D22196(D22360, 0, 1); 	D22196(D22361, 0, 1); 	D22196(D22362, 0, 1); 	D22196(D22370, 0, 1); 	D22196(D22396, 0, 70); 	D22196(D22397, 0, 70); 	D22196(D22398, 0, 70); 	D22196(D22399, 0, 70); 	D22196(D22400, 0, 70); 	D22196(D22401, 1, 25); 	D22196(D22402, 0, 1); 	D22196(D22403, 0, 1); 	D22196(D22404, 1, 25); 	D22196(D22405, 0, 1); 	D22196(D22406, 0, 1); 	D22196(D22407, 0, 25); 	D22196(D22408, 0, 1); 	D22196(D22409, 0, 1); 	D22196(D22410, 1, 25); 	D22196(D22411, 0, 1); 	D22196(D22412, 0, 1); 	D22196(D22413, 350, 600); 	D22196(D22414, 350, 600); 	D22196(D22415, 0, 22); 	D22196(D22416, 0, 1); 	D22196(D22417, -100, 300); 	D22196(D22363, 0, 1); 	D22196(D22364, 0, 1); 	D22196(D22365, 0, 1); 	D22196(D22366, 0, 1); 	D22196(D22367, 0, 1); 	D22196(D22418, -150, 150); 	D22196(D22419, -150, 150); 	D22196(D22420, 0, 1); 	D22196(D22421, -150, 150); 	D22196(D22422, 0, 22); 	D22196(D22423, 0, 22); 	D22196(D22424, 0, 22); 	D22196(D22425, 0, 22); 	D22196(D22598, 60, 400); 	D22196(D22427, 0, 1); 	D22196(D22426, 0, 1); 	D22196(D22350, 0, 3); 	D22196(D22375, 0, 70); 	D22196(D22376, 0, 70); 	D22196(D22377, 0, 70); 	D22196(D22380, 0, 70); 	D22196(D22381, 0, 70); 	D22196(D22382, 0, 70); 	D22196(D22383, 0, 22); 	D22196(D22368, 0, 1); 	D22196(D22378, 0, 70); 	D22196(D22379, 0, 70); 	D22196(D22590, 0, 2500); 	D22196(D221240, 0, 1); 	D22196(D221233, 0, 1); 	D22196(D22113, 0, 6); 	D22196(D22395, 0, 1); 	D22196(D22349, 0, 2); 	D22196(D22428, 0, 9); 	D22196(D22429, 0, 9); 	D22196(D22430, 0, 9); 	D22196(D22431, 0, 9); 	D22196(D22432, 0, 9); 	D22196(D22433, 0, 9); 	D22196(D22434, 0, 9); 	D22196(D22435, 0, 9); 	D22196(D22371,    0, 1); 	D22196(D22372,    0, 1); 	D22196(D22373,     0, 1); 	D22196(D22384,     60, 500); 	D22196(D22385,     60, 500); 	D22196(D22386,    0, 1); 	D22196(D22387, 0, 1); 	D22196(D22388,     50, 250); 	D22196(D22389,     100, 850); 	D22196(D22390, 0, 1); 	D22196(D22391,    0, 1); 	D22196(D22392,        80, 500); 	D22196(D22393,        80, 500); 	D22196(D22394,       0, 1); 	D22196(D22444 ,         2800,12000); 	D22196(D2229,           0,1); 	D22196(D22465,           0,1); 	D22196(D22463,           0,1); 	} function D22173() { 	D22998 = SPVAR_4; 	D22999 = 0; 	D221001 = 0; 	} int D22999,  D22998, D221001, D221002, D221003; function D22174(D22175) { 	D221002 = 0; 	while (D22175) { 		D221002++; 		D22175 = abs(D22175 >> 1); 	} 	return D221002; 	} function D22176(D22177, D22178) { 	D221002 = max(D22174(D22177), D22174(D22178)); 	if (D22179(D22177, D22178)) { 		D221002++; 	} 	return D221002; 	} function D22179(D22177, D22178) { 	return D22177 < 0 || D22178 < 0; 	} function D22182(D22183) { 	return 1 << clamp(D22183 - 1, 0, 31); 	} function D22184(D22183) { 	if (D22183 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - D22183); } function D22186(D22183) { 	return D22184(D22183 - 1); 	} function D22188(D22175, D22183) { 	if (D22175 < 0) { 		return (abs(D22175) & D22186(D22183)) | D22182(D22183); 	} 	return D22175 & D22186(D22183); } function D22191(D22175, D22183) { 	if (D22175 & D22182(D22183)) { 		return 0 - (D22175 & D22186(D22183)); 	} 	return D22175 & D22186(D22183); } function D22194(D22195) { 	return get_pvar(D22195, 0x80000000, 0x7FFFFFFF, 0); 	} function D22196(D22175, min, max) { 	D221003 = D22176(min, max); 	D22175 = clamp(D22175, min, max); 	if (D22179(min, max)) { 		D22175 = D22188(D22175, D221003); 	} 	D22175 = D22175 & D22184(D221003); 	if (D221003 >= 32 - D22999) { 		D221001 = D221001 | (D22175 << D22999); 		set_pvar(D22998, D221001); 		D22998++; 		D221003 -= (32 - D22999); 		D22175 = D22175 >> (32 - D22999); 		D22999 = 0; 		D221001 = 0; 	} 	D221001 = D221001 | (D22175 << D22999); 	D22999 += D221003; 	if (!D22999) { 		D221001 = 0; 	} 	set_pvar(D22998, D221001); } function D22198(min, max, D22199) { 	D221003 = D22176(min, max); 	D221001 = (D22194(D22998) >> D22999) & D22184(D221003); 	if (D221003 >= 32 - D22999) { 		D221001 = (D221001 & D22184(32 - D22999)) | ((D22194(D22998 + 1) & D22184(D221003 - (32 - D22999))) << (32 - D22999)); 	} 	D22999 += D221003; 	D221001 = D221001 & D22184(D221003); 	if (D22999 >= 32) { 		D22998++; 		D22999 -= 32; 	} 	if (D22179(min, max)) { 		D221001 = D22191(D221001, D221003); 	} 	if (D221001 < min || D221001 > max) { 		return D22199; 	} 		if(D22201[285] != 7581){      D22198(min, max, D22199); 	} 	return D221001; 	} const string D221029 = "SETTINGS"; const string D221030 = "WAS SAVED"; combo D2275 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	D22172(); 	print(15, 2, OLED_FONT_MEDIUM, 1, D221029[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, D221030[0]); 	D221031 = 1500; 	combo_run(D2276); 	} int D221031 = 1500; combo D2276 { 	vm_tctrl(0);wait( D221031); 	cls_oled(0); 	D22347 = FALSE; 	} define D221032 = 0; define D221033 = 1; define D221034 = 2; define D221035 = 3; define D221036 = 4; define D221037 = 5; define D221038 = 6; define D221039 = 7; int D22676; int D22512; int D221042, D22659; int D22468; int D221045 = 49; int D221046 = 200; int D22707 = TRUE; combo D2277 { 	set_val(D22437, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(D22437, 0); 	vm_tctrl(0);wait( 120); 	if (D22416) D22244(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int D22605; int D22612; combo D2278 { 	if (D22612) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (D22612) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (D22612) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(D22436, 100); 	vm_tctrl(0);wait( D22413); 	if (D22612) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	D22605 = FALSE; 	D22228(D22605); 	} int D22614; int D22615; combo D2279 { 	if (D22615) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (D22615) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(D22442, 100); 	set_val(D22439, 100); 	set_val(D22440, 100); 	D221187 = 4000; 	vm_tctrl(0);wait( D22414); 	vm_tctrl(0);wait( 50); 	set_val(D22442, 100); 	vm_tctrl(0);wait( 50); 	D22614 = FALSE; 	D22228(D22614); 	} int D221052 = TRUE; function D22200(D22201) { 	if (D22201) { 		D221053 = D221085; 			} 	else { 		D221053 = D221084; 			} 	combo_run(D2280); 	} int D221053; combo D2280 { 	D22221(D221053); 	vm_tctrl(0);wait( 300); 	D22221(D221082); 	vm_tctrl(0);wait( 100); 	D22221(D221053); 	vm_tctrl(0);wait( 300); 	D22221(D221082); 	} define D221057 = 100; define D221058 = 130; const string D22542 = "SCRIPT WAS"; function D22202(D22127, D22204, D22205) { 	if (!D22341 && !D22342) { 		cls_oled(0); 		print(D22204, 3, OLED_FONT_MEDIUM, OLED_WHITE, D22205); 		if (D22127) { 			print(D22206(sizeof(D221062) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D221062[0]); 		} 		else { 			print(D22206(sizeof(D221063) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, D221063[0]); 		} 		D22200(D22127); 			} 	} function D22206(D22142, D22136) { 	return (OLED_WIDTH / 2) - ((D22142 * D22136) / 2); 	} const string D221063 = "OFF"; const string D221062 = "ON"; function D22209(D22133, D22211, D22127) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(D22133, 0, OLED_FONT_MEDIUM, OLED_WHITE, D22211); 	D22216(D22127, D22219(D22127)); 	D22345 = TRUE; 	} const string D22585 = "EA PING"; const string D22607 = "FK_POWER"; const string D22599 = "MaxFnshPwr"const string D22591 = "JK_Agg"; int D22590; int D22598; function D22213(D22142, D22136) { 	return (OLED_WIDTH / 2) - ((D22142 * D22136) / 2); 	} int D221072; int D221073, D221074; function D22216(D22127, D22159) { 	D221072 = 1; 	D221074 = 10000; 	if (D22127 < 0) { 		putc_oled(D221072, 45); 		D221072 += 1; 		D22127 = abs(D22127); 			} 	for (D221073 = 5; 	D221073 >= 1; 	D221073--) { 		if (D22159 >= D221073) { 			putc_oled(D221072, (D22127 / D221074) + 48); 			D22127 %= D221074; 			D221072++; 			if (D221073 == 4) { 				putc_oled(D221072, 44); 				D221072++; 							} 					} 		D221074 /= 10; 			} 	puts_oled(D22213(D221072 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, D221072 - 1, OLED_WHITE); 	} int D221078; function D22219(D22220) { 	D221078 = 0; 	do { 		D22220 /= 10; 		D221078++; 			} 	while (D22220); 	return D221078; 	} int D22621; define D221082 = 0; define D221083 = 1; define D221084 = 2; define D221085 = 3; define D221086 = 4; define D221087 = 5; define D221088 = 6; define D221089 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int D221090; function D22221(D22222) { 	for (D221090 = 0; 	D221090 < 3; 	D221090++) { 		set_rgb(data[D22222][0], data[D22222][1], data[D22222][2]); 			} 	} const int8 D221391[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int D22624 = PS4_L3; define D221092 = 1; define D221093 = 2; define D221094 = 3; define D221095 = 2; define D221096 = 3; define D221097 = 4; define D221098 = 5; define D221099 = 6; define D221100 = 7; define D221101 = 8; define D221102 = 9; int D22618 = FALSE; int D221104; int D221105; int D221106; int D221107; define D221108 = PS4_LX; define D221109 = PS4_LY; define D221110 = PS4_RX; define D221111 = PS4_RY; function D22223 () { if(D22351 == 3){ 		if( get_ival(PS4_RY) < -70  && !D221104 && !combo_running(D220) ) { 			D221104 = TRUE; 			D22468 = FALSE; 			D22512 = D22264; 			            D22226(D22264); 		} 		if( get_ival(PS4_RY) >  70  && !D221105 && !combo_running(D220)) { 			D221105 = TRUE; 			D22468 = TRUE; 			D22512 = D22266; 			           D22226(D22266); 		} 		if( get_ival(PS4_RX) < -70  && !D221106 && !combo_running(D220) ) { 			D221106 = TRUE; 			D22468 = FALSE; 			D22512 = D22267; 			              D22226(D22267); 		} 		if( get_ival(PS4_RX) >  70  && !D221107 && !combo_running(D220) ) { 			D221107 = TRUE; 			D22468 = TRUE; 			D22512 = D22265; 			            D22226(D22265); 		} 			set_val(D221110,0);              set_val(D221111,0);  			} 	else if(D22351 < 3 && !get_ival(XB1_RS) &&  !get_ival(D22440) && !get_ival(D22441) && !get_ival(D22439)) { 		if( get_ival(PS4_RY) < -70  && !D221104 && !combo_running(D220) ) { 			D221104 = TRUE; 			D22468 = FALSE; 			D22512 = D22264; 			            D22226(D22264); 		} 		if( get_ival(PS4_RY) >  70  && !D221105 && !combo_running(D220)) { 			D221105 = TRUE; 			D22468 = TRUE; 			D22512 = D22266; 			           D22226(D22266); 		} 		if( get_ival(PS4_RX) < -70  && !D221106 && !combo_running(D220) ) { 			D221106 = TRUE; 			D22468 = FALSE; 			D22512 = D22267; 			              D22226(D22267); 		} 		if( get_ival(PS4_RX) >  70  && !D221107 && !combo_running(D220) ) { 			D221107 = TRUE; 			D22468 = TRUE; 			D22512 = D22265; 			            D22226(D22265); 		} 			set_val(D221110,0);              set_val(D221111,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		D221104 = 0; 		D221105  = 0; 		D221106  = 0; 		D221107  = 0; 			} 	} function D22224() { 	if (D22491 == D22583) { 		D22468 = FALSE; 		if (D22375) D22226(D22375); 			} 	if (D22491 == D22231(D22583 + 4)) { 		D22468 = FALSE; 		if (D22382) D22226(D22382); 			} 	if (D22491 == D22231(D22583 + 1)) { 		D22468 = TRUE; 		if (D22377) D22226(D22377); 			} 	if (D22491 == D22231(D22583 - 1)) { 		D22468 = FALSE; 		if (D22376) D22226(D22376); 			} 	if (D22491 == D22231(D22583 + 2)) { 		D22468 = TRUE; 		if (D22379) D22226(D22379); 			} 	if (D22491 == D22231(D22583 - 2)) { 		D22468 = FALSE; 		if (D22378) D22226(D22378); 			} 	if (D22491 == D22231(D22583 + 3)) { 		D22468 = TRUE; 		if (D22381) D22226(D22381); 			} 	if (D22491 == D22231(D22583 - 3)) { 		D22468 = FALSE; 		if (D22380) D22226(D22380); 			} 	} int D221129; int D22489 = 0; function D22225() { 	if(D221129){ 		D22489 += get_rtime(); 			} 	if(D22489 >= 3000){ 		D22489 = 0; 		D221129 = FALSE; 			} 	if(D22350 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D22492 && !combo_running(D220)) { 			D22492 = TRUE; 			D221129 = TRUE; 			D22489 = 0; 			D22491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D22224(); 					} 		set_val(D221110, 0); 		set_val(D221111, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(D22440) && !get_ival(D22441) && !get_ival(D22439) && !get_ival(D22438)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !D22492 && !combo_running(D220)) { 			D22492 = TRUE; 			D221129 = TRUE; 			D22489 = 0; 			D22491 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			D22224(); 					} 		set_val(D221110, 0); 		set_val(D221111, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		D22492 = FALSE; 			} 	} function D22226(D22227) { 	D22512 = D22227; 	D22201[-336 + (D22227 * 3)] = TRUE; 	D22707 = FALSE; 	block = TRUE; 	} int D221137; combo D2281 { 	set_rumble(D221137, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function D22228(D22127) { 	if (D22127) D221137 = RUMBLE_A; 	else D221137 = RUMBLE_B; 	combo_run(D2281); 	} int D221138 = 300; int D221139 ; combo D2282 { 	D221139 = TRUE; 	vm_tctrl(0);wait( D221138); 	D221139 = FALSE; 	} combo D2283 { 	D22230(); 	D22238(0, 0); 	vm_tctrl(0);wait( 20); 	D22238(0, 0); 	vm_tctrl(0);wait( 100); 	D22238(0, 0); 	set_val(D22441, 100); 	D22238(0, 0); 	vm_tctrl(0);wait( 60); 	D22238(0, 0); 	vm_tctrl(0);wait( 150); 	D22707 = TRUE; 	vm_tctrl(0);wait( 350); 	} function D22230() { 	D22676 = D22583  D22233(D22676); 	D221042 = D221143; 	D22659 = D22662; 	} combo D2284 { 	set_val(D22440, 100); 	set_val(D22439, 100); 	vm_tctrl(0);wait( 100); 	set_val(D22440, 100); 	vm_tctrl(0);wait( 100); 	D22707 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 D221392[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int D221143, D22662, D22583; int D22491; int D22492; int D221148; function D22231(D22232) { 	D221148 = D22232; 	if (D221148 < 0) D221148 = 8 - abs(D22232); 	else if (D221148 >= 8) D221148 = D22232 - 8  return D221148; 	} function D22233(D22234) { 	if (D22234 < 0) D22234 = 8 - abs(D22234); 	else if (D22234 >= 8) D22234 = D22234 - 8; 	D221143 = D221392[D22234][0]; 	D22662 = D221392[D22234][1]; } function D22235(D22236, D22237) { 	set_val(D221110, D22236); 	set_val(D221111, D22237); 	} function D22238(D22239, D22240) { 	set_val(D221108, D22239); 	set_val(D221109, D22240); 	} function D22241() { 	if (D22468) { 		set_val(D221108, inv(D22659)); 		set_val(D221109, D221042); 			} 	else { 		set_val(D221108, D22659); 		set_val(D221109, inv(D221042)); 			} 	} function D22242() { 	if (D22468) { 		set_val(D221110, inv(D22659)); 		set_val(D221111, D221042); 			} 	else { 		set_val(D221110, D22659); 		set_val(D221111, inv(D221042)); 			} 	} function D22243() { 	if (!D22468) { 		set_val(D221110, inv(D22659)); 		set_val(D221111, D221042); 			} 	else { 		set_val(D221110, D22659); 		set_val(D221111, inv(D221042)); 			} 	} function D22244() { 	set_val(D221110, D221042); 	set_val(D221111, D22659); 	} function D22245() { 	set_val(D221110, inv(D221042)); 	set_val(D221111, inv(D22659)); 	} function D22246() { 	set_val(D221110, 0); 	set_val(D221111, 0); 	} int D221164; function D22247() { 	if ((event_press(D22437)  ) && !combo_running(D2285) && (D221187  <= 0 || (D221187 < 3000 && D221187 > 1  )) && !get_ival(D22441) && D22545 > 500 &&!get_ival(D22440) &&!get_ival(D22436) &&!get_ival(D22439) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(D2285) ) { 		combo_run(D2285); 			} 	if (combo_running(D2285) && (        get_ival(D22441) ||        get_ival(D22440) ||        get_ival(D22436) ||        get_ival(D22439) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(D2285); 		D221310 = TRUE; 			} 	} combo D2285 { vm_tctrl(0);wait(750); set_val(D22438,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(D221164 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo D2286 { 	vm_tctrl(0);wait( 800); 	D221188 = 0; 	} int D221165 = 1600; int D221166 = 1600; int D221167 = 1600; int D221168 = TRUE; int D221169 = TRUE; int D22654 = FALSE; int D221171 = TRUE; int D22648 = FALSE; int D221173 = TRUE; int D22650 = FALSE; int D221175 = TRUE; int D22652 = FALSE; function D22248(){ 	if (get_ival(D22438)) { 		D221177 = 1000; 		D221196 = 0; 		D22545 = 1; 		combo_stop(D2295); 			} 	if (event_press(D22440) || event_press(D22423)) { 		D221177 = 4000; 		D221196 = 0; 		D221165 = 1600; 			} 	if (get_ival(D22439) && !get_ival(D22438) ) { 		D221177 = 0; 		D221196 = 0; 		D221165 = 1600; 			} 	else if (get_ival(D22438)){ 		D221177 = 1000; 			} 	if (D221177 > 0) { 		D221177 -= get_rtime(); 			} 	if (D221177 < 0) { 		D221177 = 0; 			} 	D221256 = D22417; 	if (event_release(D22437)) { 		D221183 = 1; 		D221196 = 0; 		D22545 = 1; 	} 	if (event_release(D22443)) { 		D221184 = 1; 		D221196 = 0; 		D22545 = 1; 	} 	if (event_release(D22438)) { 		D221166 = 1; 		D221196 = 0; 		D221165 = 1600; 			} 	if (event_release(D22439)) { 		D221167 = 1; 		D221196 = 0; 		D221165 = 1600; 			} 	if (event_release(D22442) || (get_ival(D22443) && get_ival(D22438))) { 		D221187 = 4000; 		D221196 = 0; 	} 	if (get_ival(D22437) && D221187 < 4000 && D221187 > 3500) { 		D221188 = D221187; 		D221187 = 0; 			} 	if (D221165 < 1510) { 		D221165 += get_rtime(); 			} 	if (D221166 < 1600) { 		D221166 += get_rtime(); 			} 	if (D221167 < 1600) { 		D221167 += get_rtime(); 			} 	if (D221187 > 0) { 		D221187 -= get_rtime(); 			} 	if (D221187 < 0) { 		D221187 = 0; 			} 	if (D221183 < 5100) { 		D221183 += get_rtime(); 			} 	if (D221184 < 4100) { 		D221184 += get_rtime(); 			} 	if (D221196 > 0) { 		D221196 -= get_rtime(); 			} 	if (D221196 < 0) { 		D221196 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		D221165 = 1; 		D221196 = 0; 			} 	if (combo_running(D2292)) { 		set_val(D22437, 0); 		if(get_ival(D22437)){ 			D221199 = 0; 			combo_stop(D2287); 			set_val(D22437, 0); 			combo_stop(D2292); 			combo_run(D2249); 					} 			} 	if ((combo_running(D2297) || combo_running(D2288))) { 		set_val(D22437, 0); 		if(get_ival(D22437)){ 			D22545 = 1; 			D221199 = 0; 			combo_stop(D2287); 			set_val(D22437, 0); 			combo_stop(D2297); 			combo_stop(D2288); 			combo_run(D2249); 					} 			} 	if (event_press(D22436)) { 		combo_run(D2286); 			} 	if (D22545 > 1500) { 		if (D221166 < 1500) { 			D221201 = 120; 					} 		if (D221167 < 1500) { 			D221201 = 228; 					} 		else { 			D221201 = 200; 					} 			} 	if (D22545 < 1500) { 		D221201 = 450; 			} 	if (D22545 > 2700) { 		D221205 = 920; 			} 	else if (D22545 >= 0 && D22545 < 2700) { 		D221205 = 725; 			} 	} function D22249() { 	if (D221168) { 		if ((D22545 <= 600 || (D221165 <= 1500 && D221165 > 1) || ( D221166 <= 150 || D221167 <= 150)) && event_press(D22436) ) { 			if (!get_ival(D22439) && !get_ival(D22438) && !get_ival(D22440) && !get_ival(D22441)) { 				set_val(D22436, 0); 				if (D221187 < 4000 && D221187 > 1) { 					set_val(D22436, 0); 					combo_run(D2290); 									} 				else { 					set_val(D22436, 0); 					combo_run(D2288); 					D221196 = 9000; 				} 							} 					} 			} 	if (D221175) { 		if (D22545 > 1000 && !D221196 && (!get_ival(D22439) && !get_ival(PS4_L3) && event_press(D22436)) &&  D221166 > 150 &&  D221167 > 150) { 			if (!get_ival(D22438) && !get_ival(D22440)) { 				set_val(D22436, 0); 				if (((D221184 > 1 && D221184 <= 2500) || (D221183 > 1 && D221183 <= 3000)) &&  D221165 != 1600) { 					set_val(D22436, 0); 					combo_run(D2289); 					D221196 = 9000; 									} 				else if (((D221184 > 2500 && D221184 <= 4000) || (D221183 > 3000 && D221183 <= 3500))  &&  D221165 != 1600) { 					set_val(D22436, 0); 					combo_run(D2288); 					D221196 = 9000; 									} 				else if ((D221187 < 4000 && D221187 > 1)) { 					set_val(D22436, 0); 					combo_run(D2290); 					D221196 = 9000; 									} 				else { 					set_val(D22436, 0); 					D22253(); 					D221196 = 9000; 									} 				D221196 = 9000; 							} 					} 			} 	if (D221169) { 		if (get_ival(D22438) && get_ival(D22439)) { 			if (!get_ival(D22440) && !get_ival(D22441) && (D221187 && D221183 > 1 && D221183 <= 1500) || (!D221187 && D221183 > 1 && D221183 <= 1500) || (D221183 > 1500 && !D221187) && !D221196) { 				if (event_press(D22436)) { 					set_val(D22436, 0); 					combo_run(D2298); 					D221196 = 9000; 									} 							} 					} 			} 	if (D221173) { 		if (!get_ival(D22441) && !get_ival(D22438) && !get_ival(D22439)) { 			if (get_ival(D22440) && get_ival(D22436)) { 				D22254(); 				set_val(D22436, 0); 				D221196 = 9000; 							} 					} 			} 	if (D221171) { 		if (get_ival(D22439) && !get_ival(D22438) && !D221177) { 			if (!get_ival(D22440) && !get_ival(D22441) && !D221196) { 				if (get_ival(D22436) && D22545 >= 1000) { 					set_val(D22436, 0); 					combo_run(D2295); 					D221196 = 9000; 									} 				if (get_ival(D22436) && D22545 < 1000 && !D221177) { 					set_val(D22436, 0); 					combo_run(D2296); 									} 							} 					} 			} 	if(combo_running(D2290)){ 		D221199 = 0; 		combo_stop(D2287)   	} 	if (get_ival(D22438) || D221177 > 0) { 		combo_stop(D2295); 		combo_stop(D2297); 		combo_stop(D2296); 			} 	if (combo_running(D2288) || combo_running(D2292) || combo_running(D2297) || combo_running(D2298) || combo_running(D2295)) { 		if (get_ival(D22437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D22441) > 30) { 			combo_stop(D2292); 			combo_stop(D2297); 			combo_stop(D2298); 			combo_stop(D2295); 			combo_stop(D2288); 			D221199 = 0; 			combo_stop(D2287)     		} 			} 	if (combo_running(D2288) || combo_running(D2289)) { 		if (get_ival(D22437) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(D22441)) { 			combo_stop(D2290); 			combo_stop(D2289); 			combo_stop(D2288); 			D221199 = 0; 			combo_stop(D2287)     		} 			} 	if (event_press(D22436) && D221196 > 100 && D221196 < 8990) { 		set_val(D22436, 0); 		combo_stop(D2292); 		combo_stop(D2297); 		combo_stop(D2298); 		combo_stop(D2295); 		combo_stop(D2288); 		D221199 = 0; 		combo_stop(D2287)    combo_run(D2291); 			} 	if (!D22654) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2298); 					} 			} 	if (!D22648) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2295); 					} 			} 	if (!D22650) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2292); 			D221199 = 0; 			combo_stop(D2287)     		} 			} 	if (!D22652) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(D2297); 					} 			} 	if ((get_ival(D22441) || get_ival(D22437)) && !D22354) { 		combo_stop(D224); 		combo_stop(D2247); 		combo_stop(D2233); 			} 	} define D221209 = 15; define D221210 = 15; int D221211 = 0; define D221212 = 8000; define D221213 = 4; define D221214 = 2000; int D221199 = 0; const int16 D221393[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 D221394[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int D221216 = FALSE; int D221217; int D221218; int D221219; int D221220; function D22250 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D221219 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D221216 = FALSE; 		for ( D22968 = 0; 		D22968 < sizeof(D221394) / sizeof(D221394[0]); 		D22968++) { 			if (D221219 == D221394[D22968]) { 				D221216 = TRUE; 				break; 							} 					} 		if (!D221216) { 			D221217 = D221394[0]; 			D221218 = abs(D221219 - D221394[0]); 			for ( D22968 = 1; 			D22968 < sizeof(D221394) / sizeof(D221394[0]); 			D22968++) { 				D221220 = abs(D221219 - D221394[D22968]); 				if (D221220 < D221218) { 					D221217 = D221394[D22968]; 					D221218 = D221220; 									} 							} 			set_polar(POLAR_LS, D221217, 32767); 					} 			} } function D22251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		D221219 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		D221216 = FALSE; 		for ( D22968 = 0; 		D22968 < sizeof(D221393) / sizeof(D221393[0]); 		D22968++) { 			if (D221219 == D221393[D22968]) { 				D221216 = TRUE; 				break; 							} 					} 		if (!D221216) { 			D221217 = D221393[0]; 			D221218 = abs(D221219 - D221393[0]); 			for ( D22968 = 1; 			D22968 < sizeof(D221393) / sizeof(D221393[0]); 			D22968++) { 				D221220 = abs(D221219 - D221393[D22968]); 				if (D221220 < D221218) { 					D221217 = D221393[D22968]; 					D221218 = D221220; 									} 							} 			set_polar(POLAR_LS, D221217, 32767); 					} 			} } int D221233; function D22252() { 	if (combo_running(D2287) && ( event_press(D22436)    ||   get_ival(D22441) ||         get_ival(D22437) ||        get_ival(D22442) ||        get_ival(D22443) ||        get_ival(D22438)      )) { 		combo_stop(D2287); 		D221199 = 0; 			} 	if (D221211 == 0) { 		if ( ( D221187 == 0 && !combo_running(D2290) && !combo_running(D2298) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(D22436) || (D221199 == 1 ||     combo_running(D2296) || combo_running(D2294)|| combo_running(D2292) ||  combo_running(D2295)     || combo_running(D2288) || combo_running(D2297) || combo_running(D2289) ||  combo_running(D2291)  ))     ) { 			if(D22426)D22251 (); 			else if (D22427)D22250 (); 			combo_stop(D22103); 			combo_run(D2287); 					} 			} 	else{ 		combo_stop(D2287); 			} 	} combo D2287 { 	if(D22426)D22251 (); 	else if (D22427)D22250 (); 	combo_stop(D22103); 	vm_tctrl(0); 	wait(4000); 	D221199 = 0; 	} combo D2288 { 	set_val(D22438,0); 	set_val(D22436, 100); 	vm_tctrl(0);wait( random(210, 215) + D22419); 	set_val(D22436, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function D22253() { 	if (D22545 > 600 && D22545 <= 800) { 		D221234 = 240; 			} 	if (D22545 > 800 && D22545 <= 1000) { 		D221234 = 230; 			} 	if (D22545 > 1000 && D22545 <= 1500) { 		D221234 = 225; 			} 	if (D22545 > 1500 && D22545 <= 2000) { 		D221234 = 235; 			} 	if (D22545 > 2000) { 		D221234 = 218; 			} 	combo_run(D2297); 	} combo D2289 { 	set_val(D22436, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 500); 	} combo D2290 { 	set_val(D22436, 100); 	vm_tctrl(0);wait( 205); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 300); 	} combo D2291 { 	set_val(D22436, 100); 	vm_tctrl(0);wait( 190); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 400); 	} int D221239; int D221240; int D2229; int D22465; int D22463; int D221244; int D221245; combo D2292 { 	if (D221240) { 		set_val(D22436, 0); 		D221244 = 350; 			} 	else { 		D221244 = 0; 			} 	if (D221240) { 		D22242(); 		D22238(0, 0); 			} 	vm_tctrl(0); 	wait(D221244); 	if (D221240) { 		set_val(D22439, 0); 		D221244 = 60; 			} 	else { 		set_val(D22440, 0); 		D221244 = 60; 			} 	set_val(D22436,0); 	vm_tctrl(0);wait(D221244); 	set_val(D22440, 0); 	set_val(D22439, 0); 	set_val(D22436,0); 	vm_tctrl(0);wait(D221244); 	if (D221240) { 		D221244 = 0; 			} 	else { 		D221244 = 60; 			} 	set_val(D22439, 0); 	set_val(D22440, 0); 	set_val(D22436,0); 	vm_tctrl(0);wait(D221244); 	set_val(D22436, 100); 	set_val(D22440, 100); 	vm_tctrl(0);wait(random(265, 268) +   D22418 ); 	set_val(D22440, 100); 	set_val(D22436, 0); 	if (D221240) { 		D221244 = 15; 			} 	else { 		D221244 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + D221257 + D221256 + D221244 ); 	set_val(D22440, 100); 	set_val(D22436, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D22436, 0); 	set_val(D22440, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(D22440, 100); 	vm_tctrl(0);wait(2500); 	} int D221184; int D221187; int D22545; int D221183; int D221256; int D221257 = 111; int D221177; int D221188; int D221260; function D22254() { 	D221260 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((D221260 > 5 && D221260 < 40) ) {         D22468 = FALSE; 			} 	if ((D221260 > 40 && D221260 < 85)){ 		D22468 = TRUE; 		    } 	if ((D221260 > 95 && D221260 < 130) ) { 		D22468 = FALSE; 		    } 	 if((D221260 > 130 && D221260 < 175)) { 		D22468 = TRUE; 		} 			if ((D221260 > 185 && D221260 < 220) ) {        D22468 = FALSE; 			} 	if ((D221260 > 220 && D221260 < 265)){        D22468 = TRUE; 		    } 	if ((D221260 > 275 && D221260 < 310) ) {        D22468 = FALSE; 		    } 	 if((D221260 > 310 && D221260 < 355)) { 		D22468 = TRUE; 		} 	if (D221187 == 0 && (D22545 >= 750 || ((D221188 > 3000 && D221183 > 1 && D221183 < 5000)))) { 		if (D22545 <= 2000 && D221183 > 1500) { 			set_val(D22436, 0); 			D221257 = 170; 		} 		if (D22545 <= 2000 && D221183 > 1 && D221183 <= 1500) { 			set_val(D22436, 0); 			D221257 = 202; 					} 		if (D22545 > 2000 || (D221183 > 1 && D221183 <= 1500)) { 			set_val(D22436, 0); 			D221257 = 151; 					} 		if ((D22545 > 2000 && D221183 > 1500) || D221188 > 1 && D221183 > 1) { 			set_val(D22436, 0); 			D221257 = 152; 					} 		if ((D22545 < 2000 && D221183 > 1500) || D221187 > 1 && D221183 > 1) { 			set_val(D22436, 0); 			D221257 = 149; 					} 		if (D221183 > 1500) { 			set_val(D22436, 0); 			D221257 = 148; 					} 		if (!D22545 > 2000 && D221188 > 1 && D221183 > 1 && D221183 <= 1500) { 			set_val(D22436, 0); 			D221257 = 147; 					} 		set_val(D22436, 0); 		combo_stop(D2297); 		combo_stop(D2298); 		combo_stop(D2295); 		combo_stop(D2288); 		combo_stop(D2294); 		combo_stop(D2291); 		combo_stop(D2290); 		combo_run(D2292); 			} 	else { 		if (D221187) { 			set_val(D22436, 0); 			combo_run(D2293); 					} 		else { 			if (D22545 < 750) { 				set_val(D22436, 0); 				combo_run(D2294); 							} 					} 			} } combo D2293 { 	set_val(D22436, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(D22436, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo D2294 { 	set_val(D22436, 100); 	vm_tctrl(0);wait( random(200, 205) + D22418 )  set_val(D22436, 0); 	vm_tctrl(0);wait( 700); 	} int D221270 = 246; int D221201 = 150; int D221272 = 0; combo D2295 { 	set_val(D22440, 100); 	set_val(D22439, 0); 	set_val(D22436,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(D22440, 0); 	set_val(D22439, 0); 	set_val(D22436,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	D221272 = D22417; 	set_val(D22439, 100); 	set_val(D22436,0); 	vm_tctrl(0);wait( 60); 	set_val(D22439, 100); 	set_val(D22436, 100); 	vm_tctrl(0);wait( D221270 + 10 + random(-2, 2) +  D22421); 	set_val(D22439, 100); 	set_val(D22436, 0); 	D221239 = D221201; 	vm_tctrl(0);wait( D221201 + D221272 - 58 + D22446 ); 	set_val(D22439, 100); 	if(D221233)set_val(D22436, 100); 	vm_tctrl(0);wait( 60); 	set_val(D22439, 100); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 3000); 	} combo D2296 { 	set_val(D22439, 100); 	set_val(D22436, 100); 	vm_tctrl(0);wait( 160 + D22421 ); 	set_val(D22439, 100); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 3000); 	} int D221196; int D221274 = 220; int D221234; int D221276 = 0; combo D2297 { 	D221276 = D22417; 	set_val(D22436, 100); 	vm_tctrl(0);wait( D221274 + D22419); 	set_val(D22436, 0); 	vm_tctrl(0);wait( D221234 + (D221276) + 22 + D22447); 	if(D221233)set_val(D22436, 100); 	vm_tctrl(0);wait( 60); 	set_val(D22436, 0); 	vm_tctrl(0);wait( 2000); 	} int D221278 = TRUE; int D221205; int D221280 = 245; int D221281 = 0; combo D2298 { 	set_val(D22438, 100); 	set_val(D22439, 100); 	if (D221278) { 		D221281 = D22417; 			} 	else { 		D221281 = 0   	} 	set_val(D22436, 100); 	vm_tctrl(0);wait( D221280); 	vm_tctrl(0);wait( D221205 + D221281 + 40)  } int D221284; int D221285 = 145; int D22641; int D22640; int D22643; int D22642; combo D2299 { 	set_val(D22443, 0); 	vm_tctrl(0);wait( 30); 	set_val(D22443, 100); 	vm_tctrl(0);wait( 60); 	} int D22644; int D22645; combo D22100 { 	set_val(D22442, 100); 	vm_tctrl(0);wait( D22645); 	} define D221292 = TRUE; define D221293 = 95; define D221294 = 10; define D221295 = 70; define D221296 = FALSE; define D221297 = 50; define D221298 = 95; define D221299 = XB1_LT; define D221300 = XB1_RT; define D221301 = XB1_LX; define D221302 = XB1_LY; define D221303 = POLAR_LS; int D221304; function D22255() { 	if (    get_ival(D22441) > 30 &&    (get_ival(D22440) || get_ival(D22437)) &&    (!get_ival(D22442) || !get_ival(D22436))  ) { set_val(D22441, 0); 	if( D22123(POLAR_LS,POLAR_RADIUS) > 2000){ 		D22258(POLAR_LS, 5000, 2500); 		} 		if(!get_ival(D22436)){ 			set_val(D22440,100); 					} 		else{ 			set_val(D22440,0); 					} 		  combo_run(D22102); 		if(D22420 == TRUE){ 			combo_run(D22101); 					} 			} 	else { 		combo_stop(D22102); 		combo_stop(D22101); 			} 	} combo D22101 { 	if (D22420 == TRUE) { 		set_val(D22439, 100); 		D221306 = 60; 			} 	else { 		D221306 = 0; 			} 	set_val(D22440, 0); 	vm_tctrl(0);wait( D221306); 	if (D22420 == TRUE) { 		set_val(D22439, 0); 		D221306 = 60; 			} 	else { 		D221306 = 0; 			} 	set_val(D22440, 0); 	vm_tctrl(0);wait( D221306); 	if (D22420 == TRUE) { 		set_val(D22439, 100); 			} 	vm_tctrl(0);wait( 750); 	vm_tctrl(0);wait( 750); 	} combo D22102 { 	if(!get_ival(D22436)){ 		set_val(D22440,100); 			} 	else{ 		set_val(D22440,0); 			} 	set_val(D22441, 100); 	vm_tctrl(0);wait(D22590); 	if(!get_ival(D22436)){ 		set_val(D22440,100); 			} 	else{ 		set_val(D22440,0); 			}     set_val(D22441, 0); 	vm_tctrl(0);wait(500); 	} int D221308; int D221306 ; int D221310 = TRUE; int D221311; int D221312; int D221313; int D221314; int D221315; function D22256() { 	  if(event_release(D22441)){        D221316 = 500;     }    if(event_release(D22443) || (get_ival(D22438) && ( event_release(D22437) ))){        D221317 = 3000;     }    if(D221316){        D221316 -= get_rtime();    }    if(D221317){        D221317 -= get_rtime();    }    if ( get_ival(D22441) && (!get_ival(D22440)) && D221316 <= 0 && D221317 <= 0 ) {           combo_stop(D22103);         combo_stop(D22104);         combo_run(D22105);    }    if (( get_ival(XB1_LS) || get_ival(XB1_RS)  || get_ival(D22438)  || get_ival(D22440) ||      get_ival(D22437) || get_ival(D22443) || get_ival(D22436) || get_ival(D22442)   || get_ival(XB1_PR1) ||     get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(D221110))> 20 || abs(get_ival(D221111))> 20) ) )){        combo_stop(D22103);        combo_run(D22104);        combo_stop(D22105);    }    if ( get_val(D22441)&&( (abs(get_ival(D221110))> 20 || abs(get_ival(D221111))> 20) )){    combo_stop(D22105);    }    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(D22440) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(D22104)     && !get_ival(D22438) &&  !get_ival(D22441) && !get_ival(D22437) && !get_ival(D22443) && !get_ival(D22436)){        combo_run(D22103);    } } int D221316; int D221317; combo D22103 {    	if( D22123(POLAR_LS,POLAR_RADIUS) > 2000){ 		D22258(POLAR_LS, 5000, D22444); 		} 	wait(60); 	if( D22123(POLAR_LS,POLAR_RADIUS) > 2000){ 	D22258(POLAR_LS, 5000, 1500); 	} 	wait(120); 	} combo D22104 { D22258(POLAR_LS, 5000, 10000); 	wait(200); }  combo D22105 { 	D22258(POLAR_LS, 5000, 12000); 	wait(2000); } combo D22106{ } combo D22107 { 	set_val(D22437,100); 	vm_tctrl(0);wait( D22641); 	set_val(D22437,  0); 	vm_tctrl(0);wait( 30); 	if(D22390){ 		set_val(D22439,100); 			} 	vm_tctrl(0);wait( 60); 	} combo D22108 { 	set_val(D22439,  100); 	vm_tctrl(0);wait( 60);     wait( 60); 	} combo D22109 { 	set_val(D22443,100); 	vm_tctrl(0);wait( D22643); 	set_val(D22443,  0); 	vm_tctrl(0);wait( 30); 	if(D22387){ 		set_val(D22443,100); 			} 	vm_tctrl(0);wait( 60); 	} int D22991 int D221328 combo D22110 { 	combo_suspend(D22110) 	vm_tctrl(0);wait(361) } function D22257 (){ 	if(D22991[D221328] != 361){ 	    D221328-- 	} 	else{ 		if(inv(D221328) != 290){ 			D22257(); 		} 	} } int D221331; combo D22111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int D22175; function D22258(D22259, D22260, D22261) {   D22175 = D22123(D22259, POLAR_RADIUS);   if(D22260) {     if(D22175 <= D22260) D22175 = (D22175 * 5000) / D22260;     else D22175 = ((5000 * (D22175 - D22260)) / (10000 - D22260)) + 5000;   }   if (D22261) D22175 = (D22175 * D22261) / 10000;   set_polar2(D22259, D22123(D22259, POLAR_ANGLE), min(D22175, 14142));   if (D22259 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; }  