define OSCILLATION_PERIOD = 3000; // 3 seconds in milliseconds
define HALF_PERIOD = 1500;    // Half period for going up and down

int counter;
int going_up;

init {
    counter = 0;
    going_up = 1;
}

main {
    if(get_val(XB1_RT) > 0) {
        // Set the RT value to our counter
        set_val(XB1_RT, counter);
        
        // Update counter
        if(going_up) {
            counter = counter + 2;  // Increase by 2 for faster oscillation
            if(counter >= 100) {
                going_up = 0;
            }
        } else {
            counter = counter - 2;  // Decrease by 2 for faster oscillation
            if(counter <= 0) {
                going_up = 1;
            }
        }
    } else {
        set_val(XB1_RT, get_val(XB1_RT));
    }
}