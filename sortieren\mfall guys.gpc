// Apply the Right Stick Polar Values to the Left Stick
main {
  _set_polar(POLAR_RS, _get_polar(POLAR_LS, POLAR_ANGLE), _get_ipolar(POLAR_LS, POLAR_RADIUS));
}
function _set_polar(stick, angle, radius) {
  radius = (radius * 14142) / 46340;
  if (angle <= 0) {
    set_polar2(stick, (angle = (abs(angle) + 360) % 360), min(radius, polarMax[angle % 90]));
    return;
  }
  set_polar2(stick, inv(angle % 360), min(radius, polarMax[angle % 90]));
}
function _get_polar(stick, angleOrRadius) {
  if (angleOrRadius) return (360 - get_polar(stick, POLAR_ANGLE)) % 360;
  return isqrt(~(pow(get_val(42 + stick), 2) + pow(get_val(43 + stick), 2))) + 1;
}
function _get_ipolar(stick,angleOrRadius) {
  if (angleOrRadius) return (360 - get_ipolar(stick, POLAR_ANGLE)) % 360;
  return isqrt(~(pow(get_ival(42 + stick), 2) + pow(get_ival(43 + stick), 2))) + 1;
}
const int16 polarMax[] = { 10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001 }; 