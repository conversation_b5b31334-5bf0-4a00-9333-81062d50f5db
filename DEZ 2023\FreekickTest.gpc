// Variable related to free kick power (adjustable)
int DA_FC424 = 350;
// Variable with an unspecified purpose (adjustable)
int DA_FC360;

//----- Button Layout ----------------
int DA_FC439 ;
// 42
int DA_FC440 ;
// 43
int DA_FC441 ;
// 44
int DA_FC442;
// 45
int DA_FC443 ;
// 46
int DA_FC444 ;
// 47
int DA_FC445 ;
// 48
int DA_FC446;
// 49
//--------------------------------------------------
int DA_FC447 ;
// 42
int DA_FC448 ;
// 43
int DA_FC449 ;
// 44
int DA_FC450;
// 45
int DA_FC451 ;
// 46
int DA_FC452 ;
// 47
int DA_FC453 ;
// 48
int DA_FC454;
// 49
int DA_FC455;
int DA_FC456;
int DA_FC457;
int DA_FC458;
int DA_FC459;
//====================================================================

// Main script execution block
main {

// BL CLASIC
					if(DA_FC360 == 0){
						DA_FC447      = XB1_B;
						// 1 
						DA_FC448      = XB1_A ;
						// 2 
						DA_FC449    = XB1_LB    ;
						// 3 
						DA_FC450  = XB1_RB;
						// 4 
						DA_FC451    = XB1_LT;
						// 5 
						DA_FC452    = XB1_RT;
						// 6 
						DA_FC453     = XB1_X;
						// 7 
						DA_FC454  = XB1_Y;
						// 8         
					}
					// BL Alternative  
					else if(DA_FC360 == 1){
						DA_FC447      = XB1_X;
						// 1 
						DA_FC448      = XB1_A ;
						// 2 
						DA_FC449    = XB1_LB    ;
						// 3 
						DA_FC450  = XB1_RB;
						// 4 
						DA_FC451    = XB1_LT;
						// 5 
						DA_FC452    = XB1_RT;
						// 6 
						DA_FC453     = XB1_B;
						// 7 
						DA_FC454  = XB1_Y;
						// 8             
					}
					// BL Custom  
					else if(DA_FC360 == 2){
						DA_FC447      = DA_FC1317[DA_FC439];
						// 1 
						DA_FC448      = DA_FC1317[DA_FC440] ;
						// 2 
						DA_FC449    = DA_FC1317[DA_FC441]  ;
						// 3 
						DA_FC450  = DA_FC1317[DA_FC442];
						// 4 
						DA_FC451    = DA_FC1317[DA_FC443];
						// 5 
						DA_FC452    = DA_FC1317[DA_FC444];
						// 6 
						DA_FC453     = DA_FC1317[DA_FC445];
						// 7 
						DA_FC454  = DA_FC1317[DA_FC446];
						// 8         
					}

    // Always execute this block (no need for flags)
    vm_tctrl(0);
    combo_stop(DA_FC86);
    // If the Xbox One Right Stick is active
    if (get_ival(XB1_RS)) {
        // Increase free kick power on PS5 UP button press
        if (event_press(XB1_UP)) {
            DA_FC424 += 10;
            DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
        }
        // Decrease free kick power on PS5 DOWN button press
        if (event_press(XB1_DOWN)) {
            DA_FC424 -= 10;
            DA_FC224(DA_FC228(sizeof(DA_FC611) - 1, OLED_FONT_MEDIUM_WIDTH), DA_FC611[0], DA_FC424);
        }
        // Disable PS5 UP and DOWN button actions
        set_val(XB1_UP, 0);
        set_val(XB1_DOWN, 0);
    }
    // Update display (likely showing power level or status)
    DA_FC236(DA_FC1052);
    // Start a specific combo on PS5 RIGHT button press
    if (event_press(XB1_RIGHT)) {
        DA_FC616 = FALSE;
        vm_tctrl(0);
        combo_run(DA_FC78);
    }
    // Start another combo on PS5 LEFT button press
    if (event_press(XB1_LEFT)) {
        DA_FC616 = TRUE;
        vm_tctrl(0);
        combo_run(DA_FC78);
    }
}

//=======================================
//  DISPLAY EDIT VALUE ON THE FLY        
//=======================================
function DA_FC224(DA_FC133, DA_FC226, DA_FC127) {
	cls_oled(0);
	line_oled(1, 18, 127, 18, 1, 1);
	print(DA_FC133, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA_FC226);
	DA_FC231(DA_FC127, DA_FC234(DA_FC127));
	DA_FC356 = TRUE;
	}
const string DA_FC589 = "EA PING";
// A constant string related to free kick power display
const string DA_FC611 = "FK_POWER";
const string DA_FC603 = "MaxFnshPwr"const string DA_FC595 = "JK_Agg";
int DA_FC585;
int DA_FC602;
   

// Free kick combo definition
combo DA_FC86 {
    vm_tctrl(0);
    wait(750);
    set_val(DA_FC449, 100);
    vm_tctrl(0);
    wait(60);
    vm_tctrl(0);
    wait(60);
    if (DA_FC1123 == 1) {
        set_val(XB1_RX, 100);
    } else {
        set_val(XB1_RX, -100);
    }
    vm_tctrl(0);
    wait(60);
    vm_tctrl(0);
    wait(60);
}

// Another combo definition
combo DA_FC87 {
    vm_tctrl(0);
    wait(800);
    DA_FC1147 = 0;
}

// Free kick related variables and combos
int DA_FC609;
int DA_FC616;

///////////FK Combos//////////


combo DA_FC78 {
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, -100);
	vm_tctrl(0);
	wait( 70);
	if (DA_FC616) set_val(XB1_RX, 100);
	else set_val(XB1_RX, -100);
	set_val(XB1_RY, 100);
	vm_tctrl(0);
	wait( 2000);
	if (DA_FC616) set_val(XB1_RX, -100);
	else set_val(XB1_RX, 100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 200);
	set_val(DA_FC447, 100);
	vm_tctrl(0);
	wait( DA_FC424);
	if (DA_FC616) set_val(XB1_LX, 100);
	else set_val(XB1_LX, 100);
	set_val(XB1_LY,100);
	vm_tctrl(0);
	wait( 50);
	vm_tctrl(0);
	wait( 1200);
	DA_FC609 = FALSE;
	DA_FC243(DA_FC609);
	}

// Function to calculate the length of a number
int DA_FC1044;
function DA_FC234(DA_FC235) {
    DA_FC1044 = 0;
    do {
        DA_FC235 /= 10;
        DA_FC1044++;
    } while (DA_FC235);
    return DA_FC1044;
}

// Additional variables and functions
int DA_FC356 = FALSE, DA_FC357;
int DA_FC906;

// Function to display text on an OLED screen with alignment
function DA_FC165(DA_FC166, DA_FC167, DA_FC168, DA_FC169) {
    // Decrement display buffer value for proper arithmetic
    DA_FC906--;
    // Check horizontal alignment
    switch (DA_FC166) {
        case DA_FC919 {
            DA_FC166 = OLED_WIDTH - (DA_FC906 * DA_FC1310[DA_FC168]) - 4;
            // Additional 4 for padding from border
            break;
        }
        case DA_FC918 {
            DA_FC166 = (OLED_WIDTH >> 1) - ((DA_FC906 * DA_FC1310[DA_FC168]) >> 1);
            break;
        }
        // No alignLeft case is needed because alignLeft is set to the proper left alignment already
    }
    // Check vertical alignment
    switch (DA_FC167) {
        case DA_FC918 {
            DA_FC167 = (OLED_HEIGHT >> 1) - (DA_FC1309[DA_FC168] >> 1);
            break;
        }
        case DA_FC921 {
            DA_FC167 = OLED_HEIGHT - DA_FC1309[DA_FC168] - 4;
            // Additional 4 for padding from border
            break;
        }
        // No alignTop case is needed because alignTop is set to the proper top alignment already
    }
    puts_oled(DA_FC166, DA_FC167, DA_FC168, DA_FC906, DA_FC169);
    // Output display buffer
    DA_FC906 = 1;
    // Reset display buffer
}

define DA_FC1052 = 4;
// Data for color definitions
data(32, 68, 97, 114, 107, 32, 65, 110, 103, 101, 108, 32, 0);

const int16 data[][] = {
    {
        0, 0, 0
    },
    //0. ColorOFF
    {
        0, 0, 255
    },
    //1. Blue
    {
        255, 0, 0
    },
    //2. Red
    {
        0, 255, 0
    },
    //3. Green
    {
        255, 0, 255
    },
    //4. Pink
    {
        0, 255, 255
    },
    //5. SkyBlue
    {
        255, 255, 0
    },
    //6. Yellow
    {
        255, 255, 255
    }
    //7. White
};

// Additional variables
int DA_FC1038;
int DA_FC1039, DA_FC1040;

/*   
=================================================================
Center X Function (Made By Batts) 
=================================================================
*/
function DA_FC228(DA_FC142, DA_FC136) {
	return (OLED_WIDTH / 2) - ((DA_FC142 * DA_FC136) / 2);
	}
/*
=================================================================
NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/

function DA_FC236(DA_FC169) {
	for (DA_FC1056 = 0;
	DA_FC1056 < 3;
	DA_FC1056++) {
		set_rgb(data[DA_FC169][0], data[DA_FC169][1], data[DA_FC169][2]);
			}
	}
	
	function DA_FC231(DA_FC127, DA_FC159) {
	DA_FC1038 = 1;
	DA_FC1040 = 10000;
	if (DA_FC127 < 0) {
		//--neg numbers
		putc_oled(DA_FC1038, 45);
		//--add leading "-"
		DA_FC1038 += 1;
		DA_FC127 = abs(DA_FC127);
			}
	for (DA_FC1039 = 5;
	DA_FC1039 >= 1;
	DA_FC1039--) {
		if (DA_FC159 >= DA_FC1039) {
			putc_oled(DA_FC1038, (DA_FC127 / DA_FC1040) + 48);
			DA_FC127 %= DA_FC1040;
			DA_FC1038++;
			if (DA_FC1039 == 4) {
				putc_oled(DA_FC1038, 44);
				//--add ","
				DA_FC1038++;
							}
					}
		DA_FC1040 /= 10;
			}
	puts_oled(DA_FC228(DA_FC1038 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA_FC1038 - 1, OLED_WHITE);
	}
	
int DA_FC1056;


const uint8 DA_FC1317 [] = {
	  XB1_RB,       // 0
	  XB1_RT,       // 1
	  PS5_R3,       // 2
	  XB1_LB,       // 3
	  XB1_LT,       // 4
	  PS5_L3,       // 5
	  XB1_Y, // 6
	  XB1_B,   // 7
	  XB1_A,    // 8
	  XB1_X    // 9
	 
}

int DA_FC1096;
combo DA_FC81 {
	set_rumble(DA_FC1096, 100);
	vm_tctrl(0);
	wait( 300);
	reset_rumble();
	vm_tctrl(0);
	wait( 20);
	}
function DA_FC243(DA_FC127) {
	if (DA_FC127) DA_FC1096 = RUMBLE_A;
	else DA_FC1096 = RUMBLE_B;
	combo_run(DA_FC81);
	} 