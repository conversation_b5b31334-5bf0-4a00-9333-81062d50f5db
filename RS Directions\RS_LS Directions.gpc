

main {
if(get_val(XB1_PR2)) {set_val(XB1_PR2,0);combo_run(TEST_RL);}
if(get_val(XB1_PL2)) {set_val(XB1_PL2,0);combo_run(TEST_LR);}
if(get_val(XB1_RS)) {set_val(XB1_RS,0);combo_run(TEST);}
if(get_val(XB1_LS)) {set_val(XB1_LS,0);combo_run(TEST_zigzag);}
}

function move_down() {
	set_polar(POLAR_RS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_up() {
	set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_left() {
	set_polar(POLAR_RS, 270 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_right() {
	set_polar(POLAR_RS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

combo TEST {      
    move_down ();         // up   
	wait(100);
	wait(50);
	move_down ();
	wait(100); 
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	wait(200);
//move_left ();
//wait(100);
//move_down ();
//wait(100);

}  


combo TEST_LR { 
	move_left ();
	wait(300);
	//wait(50);
    move_down ();   
	wait(100);
	//wait(50);
	move_right ();
	wait(300); 
	wait(50);
	//wait(100);
}  

combo TEST_RL { 
	move_right ();
	wait(300);
	//wait(50);
    move_down ();   
	wait(100);
	//wait(50);
	move_left ();
	wait(300); 
	wait(50);
	//wait(100);
}  

combo BR2 { 
	move_left ();
	wait(500);
	wait(200);
	move_right ();
	wait(500);
	wait(200);

}  

combo TEST_sprint {      
    move_down ();         // up   
	wait(100);
	wait(50);
	move_down ();
	wait(100); 
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	wait(200);
set_val(XB1_RB, 100);
wait(250);
set_val(XB1_RB, 0);
set_val(XB1_RT, 100);
wait(300);

}  


combo TEST_zigzag {      
    move_down ();         // up   
	wait(100);
	wait(50);
	move_down ();
	wait(100); 
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	wait(200);
	wait(50);
	set_val(XB1_RB, 100);
move_up ();
wait(100);
wait(50);
set_val(XB1_RB, 100);
move_up ();
wait(100);
wait(50);

}  


combo zigzag {      

move_up ();
wait(200);
wait(50);
move_right ();
wait(200);
wait(50);
move_up ();
wait(200);
wait(50);

}   