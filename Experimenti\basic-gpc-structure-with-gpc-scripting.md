[![Website logo](https://archbee-image-uploads.s3.amazonaws.com/IwUlH55FdaPsVELSqm1Fc/nuSw6YqerwC7liEa8mRlX_cronuslogodark.png)](https://guide.cronus.support/gpc/<https:/beta.cronusmax.com>)
[Cronus Shop](https://guide.cronus.support/gpc/<https:/shop.cronusmax.com>)[GamePacks](https://guide.cronus.support/gpc/<https:/cronus.support/gamepacks>)[Forums](https://guide.cronus.support/gpc/<https:/cronus.support/forums>)[Discord](https://guide.cronus.support/gpc/<https:/cronus.support/discord>)[YouTube](https://guide.cronus.support/gpc/<https:/cronus.support/youtube>)[Cronus ZEN Marketplace](https://guide.cronus.support/gpc/<https:/marketplace.cmindapi.com/>)
📘Cronus Zen Guide
📗GamePacks
📗GPC Script Guide
GPC Developer Guides
# Basic GPC Structure
A GPC script can be split into sections. There can be a total of 9 different sections and, in any user-made script, they should be laid out in the order shown in the example below. Only the **main** section is mandatory as it is the heart of any script and is run in a constant loop.
# Definitions[](https://guide.cronus.support/gpc/<#n5fP4>)
﻿
The define is used to assign a value to a word, creating a named constant. Definitions can be placed anywhere except inside the init, main, a combo, or function.
In this example, we define zero, one, and two as the values 0, 1, and 2 respectively. These are static values that cannot be altered during run time. So, should we use the word 'one' at any point in the script it is the same as typing the number 1. 
GPC
1// Definitions Section (OPTIONAL) 2define zero = 0; 3define one = 1; 4define two = 2;
// Definitions Section (OPTIONAL) define zero = 0; define one = 1; define two = 2;
﻿
For more detailed information, check out the [Definitions](https://guide.cronus.support/gpc/<https:/beta.cronusmax.com/gpc/gpc-scripting-definitions> "Definitions") page.
# data[](https://guide.cronus.support/gpc/<#PrE2i>)
﻿
The data section is an array of bytes (8-bit integer) that is placed at the start of the Virtual Address Space in GPC bytecode. The values are read-only and cannot be altered at run time. The user can access these values via the zero-based data array. 
GPC
1// Data Section (OPTIONAL) 2data (zero, one, two, 10, 128, 40);
// Data Section (OPTIONAL) data (zero, one, two, 10, 128, 40);
﻿
For example in this script we could do this to access the array: 
GPC
1example4 = duint8(4); //example4 = 128
example4 = duint8(4); //example4 = 128
﻿
For more detailed information, check out the [Data Section](https://guide.cronus.support/gpc/</gpc/data-section>)﻿ page.
# const Arrays[](https://guide.cronus.support/gpc/<#vn66M>)
﻿
The const array section is similar to the data section, except it's easier to manage as you let the compiler track where the data is stored - under the hood it uses the data section, but in a different way.
Const arrays consist of 2 different types, one dimension and multi-dimensional - the difference between them is how the data is structured/accessed, see below for how they're defined:
GPC
1const int8 single[] = { 0, 1, 2, 3 }; 2const int8 two_dimensional[][] = { 3 { 0, 1, 2 }, 4 { 3, 4, 5 } 5};
const int8 single[] = { 0, 1, 2, 3 }; const int8 two_dimensional[][] = { { 0, 1, 2 }, { 3, 4, 5 } };
﻿
For more detailed information, check out the [Const Arrays](https://guide.cronus.support/gpc/</gpc/gpc-scripting-const-arrays>)﻿ page.
# remap[](https://guide.cronus.support/gpc/<#hfgN0>)
﻿
In this section, we can alter the behavior of the controls with remap. In this example, we are telling the Virtual Machine that a value assigned to the **Left Bumper** should be sent to the **Right Bumper** instead and vice versa. 
GPC
1// Remapping Section (OPTIONAL) 2remap XB1_LB -> XB1_RB; 3remap XB1_RB -> XB1_LB;
// Remapping Section (OPTIONAL) remap XB1_LB -> XB1_RB; remap XB1_RB -> XB1_LB;
﻿
It is important to note that button remaps are applied once the main has finished and just before the output report is sent to the console. This means scripting should be programmed without considering the remapping. 
For example, if at some point in this script we were to set the **Left Bumper** to 100 like so:
GPC
1set_val(XB1_LB, 100);
set_val(XB1_LB, 100); 
﻿
When the main procedure finishes, the output report would initially contain a value of 100 for the **Left Bumper.** When the remaps are processed, this value would then instead be assigned to the **Right Bumper** and the output report would be modified. When the final output report is sent to the console, it would contain a value of 100 for the **Right Bumper** instead of the Left. 
For more detailed information, check out the [Remapping](https://guide.cronus.support/gpc/</gpc/remapping>)﻿ page.
# Variables[](https://guide.cronus.support/gpc/<#VvtVc>)
﻿
A variable is a point in the stack memory where a value can be placed anywhere except inside of the main{}combo{} or function(). These variables are global and GPC and can be accessed at any point within the script. Variables are not static and can be altered during run time. 
If a variable is not assigned a value in this section, it is initialized with the value 0. Such as the variables example2 and example3 in this script: 
GPC
1// Variable Initialization Section (OPTIONAL) 2int example1 = 10; 3int example2, example3; 4int example4 = 17;
// Variable Initialization Section (OPTIONAL) int example1 = 10; int example2, example3; int example4 = 17;
﻿
For more detailed information, check out the [Variables](https://guide.cronus.support/gpc/</gpc/gpc-scripting-variables>)﻿ page.
# init[](https://guide.cronus.support/gpc/<#s9pFC>)
﻿
The init section is similar to main with the exception that it is only run once when the script is loaded into the virtual machine.
It can run the same commands and functions as the main section such as combos and user-created functions. It is generally used to populate variables and arrays to set up the script. In this example, if the Cronus Zen has a PS4 controller connected when the script is first loaded, example2 is assigned a value of 27. Otherwise, it is assigned a value of 1. 
GPC
1// GPC Initialization Section (OPTIONAL) 2init { 3 if(get_controller() == PIO_PS4){ 4 example2 = 27; 5 } else { 6 example2 = 1; 7 } 8}
// GPC Initialization Section (OPTIONAL) init { if(get_controller() == PIO_PS4){ example2 = 27; } else { example2 = 1; } }
﻿
For more detailed information, check out the [Init Section](https://guide.cronus.support/gpc/</gpc/gpc-scripting-init-section>)﻿ page.
# main[](https://guide.cronus.support/gpc/<#kGgJI>)
﻿
The main section is the heart and soul of any GPC script, all functions and combos are initially executed from this section. It is the only mandatory section and every GPC script must have one. 
Unlike combos and user-created functions, a GPC may only have one main section and it is run in a loop. 
The Virtual Machine runs through the code in order and generates an output report as it goes. When the Virtual Machine gets to the end of the main section, the output report is then ready to be sent to the console. Once the console requests new data, the output report is sent and the main starts another run. 
GPC
1// GPC Main Section (MANDATORY) 2main { 3 if(example_function()){ 4 if(get_val(example3)){ 5 combo_run(testing); 6 } 7 } 8}
// GPC Main Section (MANDATORY) main { if(example_function()){ if(get_val(example3)){ combo_run(testing); } } }
﻿
As commands are run through in order, setting the value of a button in more than one place means that only the last command is sent. 
For example, in this script, a value of -100 for the LY axis will be sent to the console. The console will not see the LY axis set to 100 because the output report for that control is modified again before it is sent to the console. 
GPC
1main { 2 set_val(XB1_LY, 100); 3 set_val(XB1_LY, -100); 4}
main { set_val(XB1_LY, 100); set_val(XB1_LY, -100); }
﻿
For more detailed information, check out the [Main Section](https://guide.cronus.support/gpc/</gpc/gpc-scripting-main-section>)﻿ page.
# combo[](https://guide.cronus.support/gpc/<#aEiiK>)
﻿
A combo is a function that will perform a set of instructions in order and for the amount of time assigned to the wait command directly after the commands.
In this script, when the combo is run, it will set identifier 20 (X on an Xbox controller or Square on a PlayStation controller) to 100% (Fully pressed) for the time set in the variable example1 (10 milliseconds in this case) and then do nothing for 100 milliseconds.
GPC
1// Combo Section (Optional) 2combo testing{ 3 set_val(20, 100); 4 wait(example1); 5 wait(100); 6}
// Combo Section (Optional) combo testing{ set_val(20, 100); wait(example1); wait(100); }
﻿
You can assign multiple commands before a single wait statement. For example, in the following combo, both the Left Bumper and Right Trigger will be pressed for 500 milliseconds (half a second) when the combo is run. It will then do nothing for 500 milliseconds.
GPC
1combo LB_AND_RT { 2 set_val(XB1_LB, 100); 3 set_val(XB1_RT, 100); 4 5 wait (500); 6 wait (500); 7}
combo LB_AND_RT { set_val(XB1_LB, 100); set_val(XB1_RT, 100); wait (500); wait (500); }
﻿
For more detailed information, check out the [Combo Section](https://guide.cronus.support/gpc/</gpc/gpc-scripting-combo-section>)﻿ page.
# Function[](https://guide.cronus.support/gpc/<#haFQw>)
﻿
A user-created function is similar to the main section. Commands are processed in order and any GPC which is valid in the main section can be used here. Functions must be placed at the end of the GPC script.
The main difference with functions is they are only run when called and can return a value. When a value is returned from a function, it is terminated and any code beyond that point is not executed.
GPC user functions are global, this means that can be called from the init, main and combo sections. A function can even be called from within another function.
GPC
1// Function Section (Optional) 2function example_function(){ 3 if(get_val(example2)){ 4 example3 = 18; 5 return 1; 6 } else if(get_val(example4)){ 7 example3 = 19; 8 return 1; 9 } 10 return 0; 11}
// Function Section (Optional) function example_function(){ if(get_val(example2)){ example3 = 18; return 1; } else if(get_val(example4)){ example3 = 19; return 1; } return 0; }
﻿
# Putting it all together...[](https://guide.cronus.support/gpc/<#QMDVK>)
﻿
GPC
1// Definitions Section (OPTIONAL) 2define zero = 0; 3define one = 1; 4define two = 2; 5 6// Data Section (OPTIONAL) 7data (zero, one, two, 10, 128, 40); 8 9// Const array Section (OPTIONAL) 10const int8 single[] = { 0, 1, 2, 3 }; 11const int8 two_dimensional[][] = { 12 { 0, 1, 2 }, 13 { 3, 4, 5 } 14}; 15 16// Remapping Section (OPTIONAL) 17remap XB1_LB -> XB1_RB; 18remap XB1_RB -> XB1_LB; 19 20// Variable Initialization Section (OPTIONAL) 21int example1 = 10; 22int example2, example3; 23int example4 = 17; 24 25// GPC Initialization Section (OPTIONAL) 26init { 27 if(get_controller() == PIO_PS4){ 28 example2 = 27; 29 } else { 30 example2 = 1; 31 } 32} 33 34// GPC Main Section (MANDATORY) 35main { 36 if(example_function()){ 37 if(get_val(example3)){ 38 combo_run(testing); 39 } 40 } 41} 42 43// Combo Section (Optional) 44combo testing{ 45 set_val(20, 100); 46 wait(example1); 47 wait(100); 48} 49 50// Function Section (Optional) 51function example_function(){ 52 if(get_val(example2)){ 53 example3 = 18; 54 return 1; 55 } else if(get_val(example4)){ 56 example3 = 19; 57 return 1; 58 } 59 return 0; 60}
// Definitions Section (OPTIONAL) define zero = 0; define one = 1; define two = 2; // Data Section (OPTIONAL) data (zero, one, two, 10, 128, 40); // Const array Section (OPTIONAL) const int8 single[] = { 0, 1, 2, 3 }; const int8 two_dimensional[][] = { { 0, 1, 2 }, { 3, 4, 5 } }; // Remapping Section (OPTIONAL) remap XB1_LB -> XB1_RB; remap XB1_RB -> XB1_LB; // Variable Initialization Section (OPTIONAL) int example1 = 10; int example2, example3; int example4 = 17; // GPC Initialization Section (OPTIONAL) init { if(get_controller() == PIO_PS4){ example2 = 27; } else { example2 = 1; } } // GPC Main Section (MANDATORY) main { if(example_function()){ if(get_val(example3)){ combo_run(testing); } } } // Combo Section (Optional) combo testing{ set_val(20, 100); wait(example1); wait(100); } // Function Section (Optional) function example_function(){ if(get_val(example2)){ example3 = 18; return 1; } else if(get_val(example4)){ example3 = 19; return 1; } return 0; }
﻿
﻿
[PREVIOUSBasic Syntax](https://guide.cronus.support/gpc/</gpc/basic-syntax> "Basic Syntax")[NEXTA Simple Tutorial](https://guide.cronus.support/gpc/</gpc/a-simple-tutorial> "A Simple Tutorial")
[Docs powered by Archbee](https://guide.cronus.support/gpc/<https:/www.archbee.com/?utm_campaign=hosted-docs&utm_medium=referral&utm_source=guide.cronus.support>)
TABLE OF CONTENTS
[Definitions](https://guide.cronus.support/gpc/<#n5fP4> "Definitions")
[data](https://guide.cronus.support/gpc/<#PrE2i> "data")
[const Arrays](https://guide.cronus.support/gpc/<#vn66M> "const Arrays")
[remap](https://guide.cronus.support/gpc/<#hfgN0> "remap")
[Variables](https://guide.cronus.support/gpc/<#VvtVc> "Variables")
[init](https://guide.cronus.support/gpc/<#s9pFC> "init")
[main](https://guide.cronus.support/gpc/<#kGgJI> "main")
[combo](https://guide.cronus.support/gpc/<#aEiiK> "combo")
[Function](https://guide.cronus.support/gpc/<#haFQw> "Function")
[Putting it all together...](https://guide.cronus.support/gpc/<#QMDVK> "Putting it all together...")
[Docs powered by Archbee](https://guide.cronus.support/gpc/<https:/www.archbee.com/?utm_campaign=hosted-docs&utm_medium=referral&utm_source=guide.cronus.support>)
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
Press space bar to start a drag. When dragging you can use the arrow keys to move the item around and escape to cancel. Some screen readers may require you to be in focus mode or to use your pass through key 
