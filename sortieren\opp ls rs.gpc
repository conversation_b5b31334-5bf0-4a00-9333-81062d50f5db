define Stick_Press_LB_Threshold = 95;
int rightStickMagnitude; // Declare rightStickMagnitude at the top

main {
    if(get_val(XB1_PL1)){
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        if(get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500) {
        set_val(XB1_LB, 100);set_val(XB1_RB, 100);set_val(XB1_LT, 100);
            set_val(POLAR_RX, inv(get_val(POLAR_LX)));
            set_val(POLAR_RY, inv(get_val(POLAR_LY)));
        }
        if(rightStickMagnitude >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
        {
            // Add your code here for what should happen when this condition is true
        }
    }
}