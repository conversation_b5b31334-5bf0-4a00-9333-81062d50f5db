// Example code for pass() function switch with LED indicators

// Global variable for pass function state
int passEnabled = TRUE;  // TRUE by default

main {
    if (passEnabled) {
        set_led(LED_GREEN);
        pass();
    } else {
        set_led(LED_RED);
    }
}

// Add button to toggle pass function (using PS4_SHARE or XB1_VIEW button)
init {
    if (event_press(PS4_SHARE)) {
        passEnabled = !passEnabled;  // Toggle between TRUE and FALSE
    }
}
