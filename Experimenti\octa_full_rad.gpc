// Global arrays declared without inline initialization
int cosTable[36];
int sinTable[36];
int init_done = 0;

// Global temporary variable for use inside functions
int temp_index;

// Global variables for octagon mapping.
int angle;
int cos_val;
int sin_val;
int abs_cos;
int abs_sin;
int L_inf;
int L1;
int scaled_L1;
int norm;
int out_x;
int out_y;
int rot_x;
int rot_y;

// Function to initialize the lookup tables.
function init_lookup_tables() {
    if (init_done == 1) { return; }
    
    cosTable[0] = 1000;
    cosTable[1] = 984;
    cosTable[2] = 940;
    cosTable[3] = 866;
    cosTable[4] = 766;
    cosTable[5] = 642;
    cosTable[6] = 500;
    cosTable[7] = 342;
    cosTable[8] = 173;
    cosTable[9] = 0;
    cosTable[10] = -173;
    cosTable[11] = -342;
    cosTable[12] = -500;
    cosTable[13] = -642;
    cosTable[14] = -766;
    cosTable[15] = -866;
    cosTable[16] = -940;
    cosTable[17] = -984;
    cosTable[18] = -1000;
    cosTable[19] = -984;
    cosTable[20] = -940;
    cosTable[21] = -866;
    cosTable[22] = -766;
    cosTable[23] = -642;
    cosTable[24] = -500;
    cosTable[25] = -342;
    cosTable[26] = -173;
    cosTable[27] = 0;
    cosTable[28] = 173;
    cosTable[29] = 342;
    cosTable[30] = 500;
    cosTable[31] = 642;
    cosTable[32] = 766;
    cosTable[33] = 866;
    cosTable[34] = 940;
    cosTable[35] = 984;
    
    sinTable[0] = 0;
    sinTable[1] = 173;
    sinTable[2] = 342;
    sinTable[3] = 500;
    sinTable[4] = 642;
    sinTable[5] = 766;
    sinTable[6] = 866;
    sinTable[7] = 940;
    sinTable[8] = 984;
    sinTable[9] = 1000;
    sinTable[10] = 984;
    sinTable[11] = 940;
    sinTable[12] = 866;
    sinTable[13] = 766;
    sinTable[14] = 642;
    sinTable[15] = 500;
    sinTable[16] = 342;
    sinTable[17] = 173;
    sinTable[18] = 0;
    sinTable[19] = -173;
    sinTable[20] = -342;
    sinTable[21] = -500;
    sinTable[22] = -642;
    sinTable[23] = -766;
    sinTable[24] = -866;
    sinTable[25] = -940;
    sinTable[26] = -984;
    sinTable[27] = -1000;
    sinTable[28] = -984;
    sinTable[29] = -940;
    sinTable[30] = -866;
    sinTable[31] = -766;
    sinTable[32] = -642;
    sinTable[33] = -500;
    sinTable[34] = -342;
    sinTable[35] = -173;
    
    init_done = 1;
}

// Function to return cosine for an angle in degrees using the lookup table.
function cos_deg(int ang) {
    init_lookup_tables();  // Ensure the table is filled.
    while (ang < 0) { ang += 360; }
    ang = ang % 360;
    temp_index = (ang + 5) / 10;  // Round to nearest multiple of 10.
    temp_index = temp_index % 36;
    return cosTable[temp_index];
}

// Function to return sine for an angle in degrees using the lookup table.
function sin_deg(int ang) {
    init_lookup_tables();  // Ensure the table is filled.
    while (ang < 0) { ang += 360; }
    ang = ang % 360;
    temp_index = (ang + 5) / 10;  // Round to nearest multiple of 10.
    temp_index = temp_index % 36;
    return sinTable[temp_index];
}

main {

if(get_val(XB1_LS)) {
//set_val(XB1_RS, 0);
set_val(XB1_LT, 100);set_val(XB1_RT, 100);
    set_polar2(POLAR_LS,get_polar(POLAR_LS,POLAR_ANGLE),9900);
    }

if(get_val(XB1_RS)) {
set_val(XB1_RS, 0);
    // Get the angle from the right stick.
    angle = get_polar(POLAR_LS, POLAR_ANGLE);
    
    // Retrieve cosine and sine from the lookup functions.
    cos_val = cos_deg(angle);
    sin_val = sin_deg(angle);
    
    // Compute absolute values.
    if (cos_val < 0) {
        abs_cos = -cos_val;
    } else {
        abs_cos = cos_val;
    }
    
    if (sin_val < 0) {
        abs_sin = -sin_val;
    } else {
        abs_sin = sin_val;
    }
    
    // Compute L-infinity norm and L1 norm (for octagon mapping).
    if (abs_cos > abs_sin) {
        L_inf = abs_cos;
    } else {
        L_inf = abs_sin;
    }
    L1 = abs_cos + abs_sin;
    // Scale L1: 707/1000 approximates 1/√2.
    scaled_L1 = (L1 * 707) / 1000;
    
    // Octagon norm is the maximum of L_inf and the scaled L1.
    if (L_inf > scaled_L1) {
        norm = L_inf;
    } else {
        norm = scaled_L1;
    }
    
    // Compute the fixed full-tilt output (magnitude 95) along the octagon boundary.
    if (norm != 0) {
        out_x = (95 * cos_val) / norm;
        out_y = (95 * sin_val) / norm;
    } else {
        out_x = 0;
        out_y = 0;
    }
    
    // Apply a 45° clockwise rotation.
    // Rotation:  X' = (X + Y)*0.707,  Y' = (Y - X)*0.707.
    rot_x = ((out_x + out_y) * 707) / 1000;
    rot_y = ((out_y - out_x) * 707) / 1000;
    
    // Output the rotated values to the left stick.
    set_val(XB1_LX, rot_x);
    set_val(XB1_LY, -rot_y);
    }
}