// Extended Octagonal Boundary
// Creates 8-directional movement with extended diagonal reach

// Constants
define DEADZONE = 30;
define MAX_INPUT = 100;

// Lookup table for angle adjustments (45 values)
// Values increase at diagonals to extend the boundary outward
const int anglesMax[] = { 
    100, 102, 104, 106, 108, 110, 112, 114, 116, 118,
    120, 122, 124, 126, 128, 130, 132, 134, 136, 138,
    140, 142, 144, 142, 140, 138, 136, 134, 132, 130,
    128, 126, 124, 122, 120, 118, 116, 114, 112, 110,
    108, 106, 104, 102, 100 
};

// Global variables used by the functions
int LX;
int LY;
int scaled_x;
int scaled_y;
int angle;
int radius;
int sign;
int abs_val;
int output;
int mag_sq;
int limit_sq;
int mag;
int iSqrtValue;
int iSqrtRes;
int iSqrtBit;

// Integer square root using a bitwise method
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes = 0;
    iSqrtBit = 1 << 14;  // Starting bit (highest power of 4 <= value)

    while(iSqrtBit > iSqrtValue)
        iSqrtBit >>= 2;

    while(iSqrtBit != 0) {
        if(iSqrtValue >= iSqrtRes + iSqrtBit) {
            iSqrtValue -= iSqrtRes + iSqrtBit;
            iSqrtRes = (iSqrtRes >> 1) + iSqrtBit;
        } else {
            iSqrtRes >>= 1;
        }
        iSqrtBit >>= 2;
    }
    return iSqrtRes;
}

// Applies deadzone to a single axis value and scales it
function applyDeadzone(val) {
    if(val >= 0)
        sign = 1;
    else
        sign = -1;

    abs_val = abs(val);
    if(abs_val <= DEADZONE)
        return 0;
    if(abs_val > MAX_INPUT)
        abs_val = MAX_INPUT;
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    return sign * output;
}

// Maps the joystick input to a circular octagon
// Results are stored in global variables scaled_x and scaled_y.
function mapCircularOctagon(x, y) {
    scaled_x = applyDeadzone(x);
    scaled_y = applyDeadzone(y);
    mag_sq = scaled_x * scaled_x + scaled_y * scaled_y;
    limit_sq = MAX_INPUT * MAX_INPUT;
    if(mag_sq > limit_sq) {
        mag = intSqrt(mag_sq);
        scaled_x = (scaled_x * MAX_INPUT) / mag;
        scaled_y = (scaled_y * MAX_INPUT) / mag;
    }
}

main {
    // Read left stick raw values
    LX = get_val(XB1_LX);
    LY = get_val(XB1_LY);
    
    // Get polar values from the stick
    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
    
    // If the left stick is in analog mode, apply circular mapping
    if(get_val(XB1_LS)) {
        mapCircularOctagon(LX, LY);
        set_val(XB1_LX, scaled_x);
        set_val(XB1_LY, scaled_y);
    } else {
        // Use polar adjustment with lookup table to extend radius at diagonals
        set_polar2(POLAR_LS, angle, max(radius, radius * anglesMax[angle % 45] / 100));
    }
}
