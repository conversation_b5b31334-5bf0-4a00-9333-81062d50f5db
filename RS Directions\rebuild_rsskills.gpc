/* *
* GPC SCRIPT
*
*  GPC is a scripting language with C-like syntax.
*  To learn more access GPC Language Reference on Help menu.
* *********************************************************** */

main {

    //
    // The main procedure is called before every report is sent to
    // console. You can think of this procedure as a loop which only
    // ends when the script is unloaded.
    //
    // TODO: handle/change values of buttons, analog stick and/or sensors
    //

}

int DA_FC1090;
int DA_FC497 = 0;
function DA_FC114() {
	if(DA_FC1090){
		DA_FC497 += get_rtime();
			}
	//set_val(TRACE_2,timer_RS);
	if(DA_FC497 >= 3000){
		DA_FC497 = 0;
		DA_FC1090 = FALSE;
			}
	if (!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449)) {
		// all Skills mode ){ 
		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA_FC499 && !combo_running(DA_FC0)) {
			// getting RS zones
			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
			DA_FC499 = TRUE;
			DA_FC1090 = TRUE;
			DA_FC497 = 0;
			vm_tctrl(0);
			DA_FC113();
					}
		set_val(DA_FC1076, 0);
		set_val(DA_FC1077, 0);
			}
	//--- reset when RS is release
	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
		DA_FC499 = FALSE;
			}
	}
	
int DA_FC1090;
int DA_FC497 = 0;
function DA_FC240() {
	if(DA_FC1090){
		DA_FC497 += get_rtime();
			}
	//set_val(TRACE_2,timer_RS);
	if(DA_FC497 >= 3000){
		DA_FC497 = 0;
		DA_FC1090 = FALSE;
			}
	if (!get_ival(XB1_RS) && !get_ival(DA_FC451) && !get_ival(DA_FC452) && !get_ival(DA_FC450) && !get_ival(DA_FC449)) {
		// all Skills mode ){ 
		if ((get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA_FC499 && !combo_running(DA_FC0)) {
			// getting RS zones
			DA_FC499 = TRUE;
			DA_FC1090 = TRUE;
			DA_FC497 = 0;
			DA_FC1105 = ((((get_ipolar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;
			DA_FC239();
					}
		set_val(DA_FC1076, 0);
		set_val(DA_FC1077, 0);
			}
	//--- reset when RS is release
	if (get_ipolar(POLAR_RS,POLAR_RADIUS) < 2000) {
		DA_FC499 = FALSE;
			}
	}	
	
	
	// Check and update indices until they are different from the previous values
	if(DA_FC363){
		//1.1. RS = LS zone  
		if(DA_FC1105 == DA_FC587){
			DA_FC478 = !DA_FC478;
			// use One Way Skills
			if(DA_FC1286[DA_FC477]) DA_FC241(DA_FC1286[DA_FC477]);
					}
		//1.4. RS = opposite of LS zone  
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 4)){
			// right_on does not matter here
//1.1.0. if LS --> UP (zone 0)
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1293[DA_FC493]) DA_FC241(DA_FC1293[DA_FC493]);
					}
		//-------------------
//1.2. RS = LS zone +1/-1
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 1) ){
			DA_FC478 = TRUE;
			if(DA_FC1288[DA_FC472]) DA_FC241(DA_FC1288[DA_FC472]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 1) ){
			DA_FC478 = FALSE;
			if(DA_FC1287[DA_FC472]) DA_FC241(DA_FC1287[DA_FC472]);
					}
		//1.3. RS = LS zone +2/-2
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 2) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1290[DA_FC496]) DA_FC241(DA_FC1290[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 2) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1289[DA_FC496]) DA_FC241(DA_FC1289[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 3) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1291[DA_FC494]) DA_FC241(DA_FC1291[DA_FC494]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 3) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1292[DA_FC494]) DA_FC241(DA_FC1292[DA_FC494]);
					}
			}
	if(DA_FC364){
		//1.1. RS = LS zone  
		if(DA_FC1105 == DA_FC587){
			DA_FC478 = !DA_FC478;
			// use One Way Skills
			if(DA_FC1294[DA_FC477]) DA_FC241(DA_FC1294[DA_FC477]);
					}
		//1.4. RS = opposite of LS zone  
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 4)){
			// right_on does not matter here
//1.1.0. if LS --> UP (zone 0)
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1301[DA_FC493]) DA_FC241(DA_FC1301[DA_FC493]);
					}
		//-------------------
//1.2. RS = LS zone +1/-1
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 1) ){
			DA_FC478 = TRUE;
			if(DA_FC1296[DA_FC472]) DA_FC241(DA_FC1296[DA_FC472]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 1) ){
			DA_FC478 = FALSE;
			if(DA_FC1295[DA_FC472]) DA_FC241(DA_FC1295[DA_FC472]);
					}
		//1.3. RS = LS zone +2/-2
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 2) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1298[DA_FC496]) DA_FC241(DA_FC1298[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 2) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1297[DA_FC496]) DA_FC241(DA_FC1297[DA_FC496]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 + 3) ){
			DA_FC478 = TRUE;
			// use One Way Skills
			if(DA_FC1299[DA_FC494]) DA_FC241(DA_FC1299[DA_FC494]);
					}
		if(DA_FC1105 == DA_FC246 (DA_FC587 - 3) ){
			DA_FC478 = FALSE;
			// use One Way Skills
			if(DA_FC1300[DA_FC494]) DA_FC241(DA_FC1300[DA_FC494]);
					}
			}
	DA_FC465 = DA_FC477;
	DA_FC466 = DA_FC493;
	DA_FC467 = DA_FC494;
	DA_FC468 = DA_FC472;
	DA_FC469 = DA_FC496;
	//set_val(TRACE_1,ACTIVE);
	// Update indices
}
int DA_FC477;
//7
int DA_FC493;
//2
int DA_FC494;
// 7
int DA_FC472;
//2
int DA_FC496;
//8	
	
 