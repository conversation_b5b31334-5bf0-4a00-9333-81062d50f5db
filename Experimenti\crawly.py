import asyncio
from crawl4ai import AsyncWebCrawler

async def main():
    try:
        async with AsyncWebCrawler() as crawler:
            # CORRECT method call
            result = await crawler.arun(url="https://guide.cronus.support/gpc/basic-gpc-structure-with-gpc-scripting")
            with open("basic-gpc-structure-with-gpc-scripting.md", "w", encoding="utf-8") as f:
                f.write(result.markdown)
            print("Data saved to basic-gpc-structure-with-gpc-scripting.md")
    except Exception as e:
        # Handle any errors that occur during crawling or file writing
        print(f"An error occurred: {e}")

# Run the asynchronous function
asyncio.run(main())