int modMenu,editMenu; 
int modNameIdx,valNameIdx;
int case_indic = 0;
 
/* Display Variables / ScreenSaver / Strings/Text  */
int screenSaver,blankScreen;
int displayTitle = TRUE;
int updateDisplay;

const string Toggle  [] = {"Off","On"};
const string misc[]     = {"v1.18","Template",""}; 
const string LEFT  = "LEFT > LEFT UP";
const string RIGHT = "RIGHT > RIGHT UP";
const string UP  = "UP > LEFT DOWN";
const string DOWN = "DOWN > RIGHT DOWN";
const string X = "X > MID UP";
main {


    if(!modMenu && !editMenu){
        /* Display The Title Screen When we Are NOT in any Menu s */
        if(displayTitle){ 
            cls_oled(0);

            displayTitle = FALSE;
            screenSaver  = TRUE;

            print(centerPosition(getStringLength( LEFT[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFT[0]);
            print(centerPosition(getStringLength(RIGHT[0]) ,OLED_FONT_SMALL_WIDTH), 18  ,OLED_FONT_SMALL , OLED_WHITE , RIGHT[0]);
            print(centerPosition(getStringLength( UP[0]) ,OLED_FONT_SMALL_WIDTH), 31  ,OLED_FONT_SMALL , OLED_WHITE , UP[0]);
            print(centerPosition(getStringLength(DOWN[0]) ,OLED_FONT_SMALL_WIDTH), 44  ,OLED_FONT_SMALL , OLED_WHITE , DOWN[0]);
			print(centerPosition(getStringLength(X[0]) ,OLED_FONT_SMALL_WIDTH), 55  ,OLED_FONT_SMALL , OLED_WHITE , X[0]);
        }
        }




if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (1);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (3);
      }
      set_val(XB1_RIGHT,0);
	}

    LED_Color(Green);
    if (get_val(XB1_LEFT)) {
      PN_Angle = 230;
      combo_run(p23);
    } //230 
    if (get_val(XB1_RIGHT)) {
      PN_Angle = 310;
      combo_run(p23);
    } //310
    if (get_val(XB1_UP)) {
      PN_Angle = 150;
      combo_run(p23);
    } //210
    if (get_val(XB1_DOWN)) {
      PN_Angle = 30;
      combo_run(p23);
    } //330
        if (get_val(XB1_X)) {
      PN_Angle = 90;
      combo_run(p23);
    } //330
  }



int Penalties_ON_Off;
int PN_Angle;

combo p23 {
  set_polar(POLAR_LS, PN_Angle, 32767);
  set_val(XB1_B, 100);
  wait(420);
  set_polar(POLAR_LS, PN_Angle, 32767);
  wait(1500); //Aim_Lock
  //wait(200); //Aim_Lock
  //set_val(XB1_RB, 100);
  //set_val(XB1_LB, 100);
  //wait(500); //Aim_Lock
  wait(5000);
  // load_slot (1);
}

define ColorOFF = 0;
define Blue = 1;
define Red = 2;
define Green = 3;
define Pink = 4;
define SkyBlue = 5;
define Yellow = 6;
define White = 7;

data(
  0, 0, 0, //0. ColorOFF
  2, 0, 0, //1. Blue     
  0, 2, 0, //2. Red      
  0, 0, 2, //3. Green    
  2, 2, 0, //4. Pink     
  2, 0, 2, //5. SkyBlue 
  0, 2, 2, //6. Yellow   
  2, 2, 2 //7. White    
); // end of data segment-------------- 
// COLOR LED function        
//-------------------------------------------------------------- 

int data_indx;

function LED_Color(color) {
  for (data_indx = 0; data_indx < 3; data_indx++) {
    set_led(data_indx, duint8((color * 3) + data_indx));
  }
}

function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
 } 