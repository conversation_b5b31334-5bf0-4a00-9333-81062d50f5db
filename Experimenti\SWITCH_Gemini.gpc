// SWITCH_GEMINI.gpc
// <PERSON><PERSON><PERSON> to activate a combo on XB1_LS press, alternating left stick direction
// with random start and 250ms wait time.
// Corrected to address 'wait is only allowed at the top level of combos' error.

// --- Constants ---
define WAIT_TIME = 250; // Wait time in milliseconds between direction changes

// --- Main Loop ---
main {
    // Activate combo on XB1_LS press
    if (event_press(XB1_LS)) {
        // Stop any previously running switch combo to prevent overlap
        // or if a release event was somehow missed.
        if (combo_running(SWITCH_PATTERN_A)) {
            combo_stop(SWITCH_PATTERN_A);
        }
        if (combo_running(SWITCH_PATTERN_B)) {
            combo_stop(SWITCH_PATTERN_B);
        }

        // Randomly choose which pattern combo to run
        if (random(0, 1)) { // random(min, max) is inclusive
            combo_run(SWITCH_PATTERN_A);
        } else {
            combo_run(SWITCH_PATTERN_B);
        }
    }

    // Stop combo on XB1_LS release
    if (event_release(XB1_LS)) {
        if (combo_running(SWITCH_PATTERN_A)) {
            combo_stop(SWITCH_PATTERN_A);
        }
        if (combo_running(SWITCH_PATTERN_B)) {
            combo_stop(SWITCH_PATTERN_B);
        }
    }
}

// --- Combo Definitions ---

// Combo Pattern A: Starts with -70 offset
combo SWITCH_PATTERN_A {
     set_polar(POLAR_LS, -70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(WAIT_TIME);
    set_polar(POLAR_LS, 70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(WAIT_TIME);
}

// Combo Pattern B: Starts with 70 offset
combo SWITCH_PATTERN_B {
    set_polar(POLAR_LS, 70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(WAIT_TIME);
    set_polar(POLAR_LS, -70 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
    wait(WAIT_TIME);
}