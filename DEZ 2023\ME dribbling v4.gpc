function icos(x) { return isin(x + 8192); }
function isin(x) {
  x = (x % 32767) << 17;
  if((x ^ (x * 2)) < 0) { x = (-2147483648) - x; }
  x = x >> 17;
  return x * ((98304) - (x * x) >> 11) >> 14;
}
int cos_angle, sin_angle;
function set_polar_dd(stick, angle, radius, dd_factor){

  dd_factor = (dd_factor * 32767) / 100;
  radius = (radius * 32767) / 10000;
  angle = (360 - angle) * 91;

  sin_angle = isin(angle); cos_angle = icos(angle);

  angle = 32767 - icos(abs(abs(sin_angle) - abs(cos_angle)));
  radius = radius * (32767 - ((angle * dd_factor) >> 15)) >> 15;

  set_val(42 + stick, clamp((radius * cos_angle) >> 15, -32767, 32767));
  set_val(43 + stick, clamp((radius * sin_angle) >> 15, -32767, 32767));

  return;
}
int r, a, dd;
main {
  stickize(POLAR_LX, POLAR_LY, 32767);

  r = get_polar(POLAR_LS, POLAR_RADIUS);
  a = get_polar(POLAR_LS, POLAR_ANGLE);
  dd = -9; // Range -50 < - > 100 

  set_polar_dd(POLAR_LS, a, r, dd);
  
  if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (2);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (3);
      }
      set_val(XB1_RIGHT,0);
	}

	if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(pressing);
    } else if(combo_running(pressing)) {
        combo_stop(pressing);
    }
    
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
        combo_run(TapB);
    
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {
        set_val(XB1_B,0);
        if (event_press(XB1_B)) combo_run(PressB);
    }
    
}
combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100); //750
	wait(440);
    set_val(XB1_LB, 100); // Press XB1_LB
    wait(80);             // Wait for 80ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(40);             // Wait for 40ms pause
    set_val(XB1_LB, 100); // Press XB1_LB again
    wait(250);            // Wait for 150ms
    set_val(XB1_LB, 0);   // Release XB1_LB
    wait(250);  
}

combo PressB {
    set_val(XB1_B, 100);
    wait(100);
}

combo TapB {
    set_val(XB1_B, 0);
    wait(250);
} 