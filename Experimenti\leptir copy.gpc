// Constants for stick identification
define stick = POLAR_LS;  // Left stick in polar mode
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Enhanced constants for octagon shape
define MAX_VAL = 100;     // Maximum stick value
define RATIO = 50;        // Ratio for flat sides (percentage)
define BLEND = 5;        // Blend range for transitions (percentage)
define DEADZONE = 5;      // Deadzone to prevent unwanted micro-movements

// Variables
int x, y;                 // Current stick values
int abs_x, abs_y;         // Absolute values
int max_val;              // Maximum value for current position
int blend_factor;         // Blending factor for transitions
int smoothed_x, smoothed_y; // Smoothed output values

// Smoothing function
function smooth_value(int current, int target, int factor) {
    return current + (target - current) * factor / 100;
}

function process_stick_movement() {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Apply deadzone
    if(abs(x) < DEADZONE && abs(y) < DEADZONE) {
        set_val(stickX, 0);
        set_val(stickY, 0);
        return;
    }
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Calculate blend factor with improved precision
        if(abs_x > abs_y) {
            blend_factor = (abs_y * 100) / abs_x;
        } else {
            blend_factor = (abs_x * 100) / abs_y;
        }
        
        // Enhanced transition between sides and corners
        if(blend_factor > 100 - BLEND) {
            // Near diagonal (corner)
            max_val = MAX_VAL;
        } else if(blend_factor < BLEND) {
            // Near cardinal (side)
            max_val = (MAX_VAL * RATIO) / 100;
        } else {
            // Smooth transition area
            max_val = (MAX_VAL * RATIO) / 100 + 
                     ((MAX_VAL - (MAX_VAL * RATIO) / 100) * 
                      (blend_factor - BLEND) / (100 - 2 * BLEND));
        }
        
        // Apply scaling while maintaining direction
        smoothed_x = (x * max_val) / MAX_VAL;
        smoothed_y = (y * max_val) / MAX_VAL;
        
        // Additional smoothing for more fluid movement
        smoothed_x = smooth_value(get_val(stickX), smoothed_x, 70);
        smoothed_y = smooth_value(get_val(stickY), smoothed_y, 70);
        
        // Set the modified values
        set_val(stickX, smoothed_x);
        set_val(stickY, smoothed_y);
    }
}

main {
    process_stick_movement();
}