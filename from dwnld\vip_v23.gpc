																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON><PERSON>Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/










































































 int DA231202[0]; init { 	DA231119(); 	combo_run(DA2311); 	combo_run(DA2312); 	combo_run(DA2313); 	combo_run(DA2314); 	combo_run(DA2315); 	combo_run(DA2316); 	combo_run(DA2317); 	combo_run(DA2318); 	combo_run(DA2319); 	combo_run(DA23110); 	combo_run(DA23111); 	combo_run(DA23112); 	combo_run(DA23113); 	combo_run(DA23114); 	combo_run(DA23115); 	combo_run(DA23116); 	combo_run(DA23117); 	combo_run(DA23118); 	combo_run(DA23119); 	combo_run(DA23120); 	combo_run(DA23121); 	combo_run(DA23122); 	combo_run(DA23123); 	combo_run(DA23124); 	combo_run(DA23125); 	combo_run(DA23126); 	combo_run(DA23127); 	combo_run(DA23128); 	combo_run(DA23129); 	combo_run(DA23130); 	combo_run(DA23131); 	combo_run(DA23132); 	combo_run(DA23133); 	combo_run(DA23134); 	combo_run(DA23135); 	combo_run(DA23136); 	combo_run(DA23137); 	combo_run(DA23138); 	combo_run(DA23139); 	combo_run(DA23140); 	combo_run(DA23141); 	combo_run(DA23142); 	combo_run(DA23143); 	combo_run(DA23144); 	combo_run(DA23145); 	combo_run(DA23146); 	combo_run(DA23147); 	combo_run(DA23148); 	combo_run(DA23149); 	combo_run(DA23150); 	combo_run(DA23151); 	combo_run(DA23152); 	combo_run(DA23153); 	combo_run(DA23154); 	combo_run(DA23155); 	combo_run(DA23156); 	combo_run(DA23157); 	combo_run(DA23158); 	combo_run(DA23159); 	combo_run(DA23160); 	combo_run(DA23161); 	combo_run(DA23162); 	combo_run(DA23163); 	combo_run(DA23164); 	combo_run(DA23165); 	combo_run(DA23166); 	combo_run(DA23167); 	combo_run(DA23168); 	combo_run(DA23169); 	combo_run(DA23170); 	combo_stop(DA2311); 	combo_stop(DA2312); 	combo_stop(DA2313); 	combo_stop(DA2314); 	combo_stop(DA2315); 	combo_stop(DA2316); 	combo_stop(DA2317); 	combo_stop(DA2318); 	combo_stop(DA2319); 	combo_stop(DA23110); 	combo_stop(DA23111); 	combo_stop(DA23112); 	combo_stop(DA23113); 	combo_stop(DA23114); 	combo_stop(DA23115); 	combo_stop(DA23116); 	combo_stop(DA23117); 	combo_stop(DA23118); 	combo_stop(DA23119); 	combo_stop(DA23120); 	combo_stop(DA23121); 	combo_stop(DA23122); 	combo_stop(DA23123); 	combo_stop(DA23124); 	combo_stop(DA23125); 	combo_stop(DA23126); 	combo_stop(DA23127); 	combo_stop(DA23128); 	combo_stop(DA23129); 	combo_stop(DA23130); 	combo_stop(DA23131); 	combo_stop(DA23132); 	combo_stop(DA23133); 	combo_stop(DA23134); 	combo_stop(DA23135); 	combo_stop(DA23136); 	combo_stop(DA23137); 	combo_stop(DA23138); 	combo_stop(DA23139); 	combo_stop(DA23140); 	combo_stop(DA23141); 	combo_stop(DA23142); 	combo_stop(DA23143); 	combo_stop(DA23144); 	combo_stop(DA23145); 	combo_stop(DA23146); 	combo_stop(DA23147); 	combo_stop(DA23148); 	combo_stop(DA23149); 	combo_stop(DA23150); 	combo_stop(DA23151); 	combo_stop(DA23152); 	combo_stop(DA23153); 	combo_stop(DA23154); 	combo_stop(DA23155); 	combo_stop(DA23156); 	combo_stop(DA23157); 	combo_stop(DA23158); 	combo_stop(DA23159); 	combo_stop(DA23160); 	combo_stop(DA23161); 	combo_stop(DA23162); 	combo_stop(DA23163); 	combo_stop(DA23164); 	combo_stop(DA23165); 	combo_stop(DA23166); 	combo_stop(DA23167); 	combo_stop(DA23168); 	combo_stop(DA23169); 	combo_stop(DA23170); 	combo_run(DA231110); } int DA231275 ; int DA231276; int DA231277; int DA231278; int DA231279; define DA231280 = 0; define DA231281 = 1; define DA231282 = 2; define DA231283 = 3; define DA231284 = 4; define DA231285 = 5; define DA231286 = 6; define DA231287 = 7; define DA231288 = 8; define DA231289 = 9; define DA231290 = 10; define DA231291 = 11; define DA231292 = 12; define DA231293 = 13; define DA231294 = 14; define DA231295 = 15; define DA231296 = 16; define DA231297 = 17; define DA231298 = 18; define DA231299 = 19; define DA231300 = 20; define DA231301 = 21; define DA231302 = 22; define DA23123 = 23; define DA231304 = 24; define DA231305 = 25; define DA231306 = 26; define DA231307 = 27; define DA231308 = 28; define DA231309 = 29; define DA231310 = 30; define DA231311 = 31; define DA231312 = 32; define DA231313 = 33; define DA231314 = 34; define DA231315 = 35; define DA231316 = 36; define DA231317 = 37; define DA231318 = 38; define DA231319 = 39; define DA231320 = 40; define DA231321 = 41; define DA231322 = 42; define DA231323 = 43; define DA231324 = 44; define DA231325 = 45; define DA231326 = 46; define DA231327 = 47; define DA231328 = 48; define DA231329 = 49; define DA231330 = 50; define DA231331 = 51; define DA231332 = 52; define DA231333 = 53; define DA231334 = 54; define DA231335 = 55; define DA231336 = 56; define DA231337 = 57; define DA231338 = 58; define DA231339 = 59; define DA231340 = 60; define DA231341 = 61; define DA231342 = 62; define DA231343 = 63; define DA231344 = 64; define DA231345 = 65; define DA231346 = 66; define DA231347 = 67; define DA231348 = 68; define DA231349 = 69; define DA231350 = 70; define DA231351 = 0; function DA231113(DA231114) { 	if (DA231114 == 0) vm_tctrl(-0); 	else if (DA231114 == 1) vm_tctrl(-1); 	else if (DA231114 == 2) vm_tctrl(-2); 	else if (DA231114 == 3) vm_tctrl(-3); 	else if (DA231114 == 4) vm_tctrl(-4); 	else if (DA231114 == 5) vm_tctrl(-5); 	else if (DA231114 == 6) vm_tctrl(-6); 	else if (DA231114 == 7) vm_tctrl(-7); 	else if (DA231114 == 8) vm_tctrl(-8); 	else if (DA231114 == 9) vm_tctrl(-9); } int DA231352, DA231353; int DA231354, DA231355; int DA231356 = FALSE, DA231357; int DA231358 = TRUE; int DA231359; const string DA231832[] = { 	"Off",  "On" } ; int DA231360; int DA231361; int DA231362; int DA231363; int DA231364; int DA231365; int DA231366; int DA231367; int DA231368; int DA231369; int DA231370; int DA231371; int DA231372; int DA231373; int DA231374; int DA231375; int DA231376; int DA231377; int DA231378; int DA231379; int DA231114; int DA231381; int DA231382 ; int DA231383 ; int DA231384 ; define DA231385 = 24; int DA231386; int DA231387; int DA231388; int DA231389; int DA231390; int DA231391; int DA231392; int DA231393; int DA231394; int DA231395 ; int DA231396 ; int DA231397 ; int DA231398 ; int DA231399 ; int DA231400 ; int DA231401 ; int DA231402 ; int DA231403 ; int DA231404 ; int DA231405 ; int DA231406; int DA231407; int DA231408; int DA231409; int DA231410; int DA231411; int DA231412; int DA231413; int DA231414; int DA231415; int DA231416; int DA231417; int DA231418; int DA231419; int DA231420; int DA231421; int DA231422; int DA231423; int DA231424; int DA231425; int DA231426; int DA231427; int DA231428; int DA231429; int DA231430; int DA231431; int DA231432; int DA231433; int DA231434; int DA231435; int DA231436; int DA231437; int DA231438; int DA231439 ; int DA231440 ; int DA231441 ; int DA231442; int DA231443 ; int DA231444 ; int DA231445 ; int DA231446; int DA231447 ; int DA231448 ; int DA231449 ; int DA231450; int DA231451 ; int DA231452 ; int DA231453 ; int DA231454; int DA231455; int DA231456; int DA231457; int DA231458; int DA231459; const int16 DA231838[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		13,30,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 DA231579[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 DA231810[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string DA231589[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string DA231588[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string DA231814 [] = { 	"Classic","Alternative","Custom", ""  } ; const string DA231913 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string DA231830[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string DA231816[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string DA231883[] = { 	"Right",  "Left",  "" } ; const string DA231881[] = { 	"One Tap",  "Double Tap",  "" } ; const string DA231820[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string DA231822[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string DA231857[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int DA231460 = -1; int DA231461 = -1; int DA231462 = -1; int DA231463 = -1; int DA231464 = -1; int DA231465; int DA231466; int DA231467; int DA231468; int DA231469; const uint8 DA2311368[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 DA2311369[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 DA2311370[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 DA2311371[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 DA2311372[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 DA2311373[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA2311374[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DA2311375[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 DA2311376[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 DA2311377[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 DA2311378[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 DA2311379[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 DA2311380[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 DA2311381[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DA2311382[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DA2311383[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function DA231115(DA231116) { 	if (DA231116 == 9) { 		DA231470 = -1; 			} 	else if (DA231116 <= 0) { 		DA231470 = 1; 			} 	else if (DA231116 > 9 ) { 		DA231116 = 0; 			} 	DA231116 += DA231470; 	return DA231116; 	} function DA231117() { 	vm_tctrl(0); 	if(DA23129 && DA231363){ 		if(DA231555 < 1000){ 			DA231473 = 10; 			DA231499   = 10; 			DA231497  = 10; 					} 			} 	if(DA231476 && DA231364){ 		DA231474 = FALSE; 		if(DA231555 < 1000){ 			DA231473 = 11; 			DA231499   = 11; 			DA231497  = 11; 					} 			} 	if(DA231474 && DA231364){ 		DA231476 = FALSE; 		if(DA231555 < 1000){ 			DA231473 = 10; 			DA231499   = 10; 			DA231497  = 10; 					} 			} 			       if(DA231555 >= 1000){     DA231478 = DA231115(DA231478);     DA231496 = DA231115(DA231496);     DA231497 = DA231115(DA231497);     DA231473 = DA231115(DA231473);     DA231499 = DA231115(DA231499);     } 	if(DA231363){ 		if(DA231502 == DA231593){ 			DA231479 = !DA231479; 			if(DA2311368[DA231478]) DA231227(DA2311368[DA231478]); 					} 		if(DA231502 == DA231232 (DA231593 + 4)){ 			DA231479 = FALSE; 			if(DA2311375[DA231496]) DA231227(DA2311375[DA231496]); 					} 		if(DA231502 == DA231232 (DA231593 + 1) ){ 			DA231479 = TRUE; 			if(DA2311370[DA231473]) DA231227(DA2311370[DA231473]); 					} 		if(DA231502 == DA231232 (DA231593 - 1) ){ 			DA231479 = FALSE; 			if(DA2311369[DA231473]) DA231227(DA2311369[DA231473]); 					} 		if(DA231502 == DA231232 (DA231593 + 2) ){ 			DA231479 = TRUE; 			if(DA2311372[DA231499]) DA231227(DA2311372[DA231499]); 					} 		if(DA231502 == DA231232 (DA231593 - 2) ){ 			DA231479 = FALSE; 			if(DA2311371[DA231499]) DA231227(DA2311371[DA231499]); 					} 		if(DA231502 == DA231232 (DA231593 + 3) ){ 			DA231479 = TRUE; 			if(DA2311373[DA231497]) DA231227(DA2311373[DA231497]); 					} 		if(DA231502 == DA231232 (DA231593 - 3) ){ 			DA231479 = FALSE; 			if(DA2311374[DA231497]) DA231227(DA2311374[DA231497]); 					} 			} 	else if(DA231364){ 		if(DA231502 == DA231593){ 			DA231479 = !DA231479; 			if(DA2311376[DA231478]) DA231227(DA2311376[DA231478]); 					} 		if(DA231502 == DA231232 (DA231593 + 4)){ 			DA231479 = FALSE; 			if(DA2311383[DA231496]) DA231227(DA2311383[DA231496]); 					} 		if(DA231502 == DA231232 (DA231593 + 1) ){ 			DA231479 = TRUE; 			if(DA2311378[DA231473]) DA231227(DA2311378[DA231473]); 					} 		if(DA231502 == DA231232 (DA231593 - 1) ){ 			DA231479 = FALSE; 			if(DA2311377[DA231473]) DA231227(DA2311377[DA231473]); 					} 		if(DA231502 == DA231232 (DA231593 + 2) ){ 			DA231479 = TRUE; 			if(DA2311380[DA231499]) DA231227(DA2311380[DA231499]); 					} 		if(DA231502 == DA231232 (DA231593 - 2) ){ 			DA231479 = FALSE; 			if(DA2311379[DA231499]) DA231227(DA2311379[DA231499]); 					} 		if(DA231502 == DA231232 (DA231593 + 3) ){ 			DA231479 = TRUE; 			if(DA2311381[DA231497]) DA231227(DA2311381[DA231497]); 					} 		if(DA231502 == DA231232 (DA231593 - 3) ){ 			DA231479 = FALSE; 			if(DA2311382[DA231497]) DA231227(DA2311382[DA231497]); 					} 			} } int DA231478; int DA231496; int DA231497; int DA231473; int DA231499; function DA231118() { 	if(DA2311139){ 		DA231500 += get_rtime(); 			} 	if(DA231500 >= 3000){ 		DA231500 = 0; 		DA2311139 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DA231451) && !get_ival(DA231452) && !get_ival(DA231450) && !get_ival(DA231449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !DA231503 && !combo_running(DA2310)) { 			DA231502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA231503 = TRUE; 			DA2311139 = TRUE; 			DA231500 = 0; 			vm_tctrl(0); 			DA231117(); 					} 		set_val(DA2311120, 0); 		set_val(DA2311121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		DA231503 = FALSE; 			} 	} function DA231119() { 	DA231172(); 	if (DA231386 == 0 && DA231387 == 0 && DA231388 == 0 && DA231389 == 0 && DA231390 == 0 && DA231391 == 0 && DA231392 == 0 && DA231393 == 0) { 		DA231386 = 4; 		DA231387 = 41; 		DA231388 = 41; 		DA231389 = 42; 		DA231390 = 42; 		DA231391 = 31; 		DA231392 = 31; 		DA231393 = 31; 			} 	DA231952 = get_slot(); 	} int DA231470 = 1; int DA231507; int DA231508; int DA231509 = TRUE; int DA231510[6]; int DA231511; int DA231512; int DA231513; int DA231514; function DA231120(DA231121, DA231122, DA231123) { 	DA231123 = (DA231123 * 14142) / 46340; 	if (DA231122 <= 0) { 		set_polar2(DA231121, (DA231122 = (abs(DA231122) + 360) % 360), min(DA231123, DA231518[DA231122 % 90])); 		return; 			} 	set_polar2(DA231121, inv(DA231122 % 360), min(DA231123, DA231518[DA231122 % 90])); 	} function DA231124(DA231121,DA231126) { 	if (DA231126) return (get_ipolar(DA231121, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + DA231121), 2) + pow(get_ival(43 + DA231121), 2))) + 1; 	} const int16 DA231518[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int DA231521 = 1; combo DA2310{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && DA231124(POLAR_RS,POLAR_RADIUS) > 2000){ 		DA231269(POLAR_RS, 2500, 12000); 		}  DA231113(DA231114); 	if(!DA231508){ 		DA231508 = TRUE; 		DA231507 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,DA231508); 		set_pvar(SPVAR_3,DA231507); 		DA231509 = TRUE; 			} 	if(!DA231514){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			DA231513 = DA231129(DA231513 + 1 ,0,5)DA231509 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			DA231513 = DA231129(DA231513 - 1 ,0,5)DA231509 = TRUE 		} 		if(event_press(PS4_UP)){ 			DA231510[DA231513]  = DA231129(DA231510[DA231513] + 1 ,0,9)DA231509 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			DA231510[DA231513]  = DA231129(DA231510[DA231513] - 1 ,0,9)DA231509 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			DA231511 = 0; 			for(DA231512 = 5; 			DA231512 >= 0; 			DA231512--){ 				DA231511 += DA231510[DA231512] * pow(10,DA231512) 			} 			if(DA231511 == DA231127(DA231507)){ 				DA231514 = TRUE; 				set_pvar(SPVAR_2,DA231514)  			} 			DA231509 = TRUE; 					} 			} 	if(DA231509){ 		cls_oled(0)if(!DA231514){ 			DA231133(DA231507,DA231536,10,OLED_FONT_MEDIUM,OLED_WHITE,DA231537)for( DA231512 = 0; 			DA231512 < 6; 			DA231512++){ 				DA231133(DA231510[DA231512],85 - (DA231512 * 10),40,OLED_FONT_MEDIUM,!(DA231512 == DA231513),DA231537) 			} 					} 		DA231509 = FALSE; 			} 	if(DA231514){ 		if (get_ival(DA231447) || get_ival(DA231451) || get_ival(DA231449) || get_ival(DA231450) || DA231356 || combo_running(DA23172) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			DA231113(DA231114); 					} 		if(get_ival(DA231452) > 40 || (!get_ival(DA231449) && !get_ival(DA231450))){ 			if(get_ival(DA231447)){ 				vm_tctrl(0); 				if(get_ptime(DA231447) > DA231608){ 					set_val(DA231447,0); 									} 							} 					} 		if(!get_ival(DA231449)){ 			if(get_ival(DA231447)){ 				vm_tctrl(0); 				if(get_ptime(DA231447) > DA231608){ 					set_val(DA231447,0); 									} 							} 					} 		if (DA231356) { 			vm_tctrl(0); 			if(DA231357 < 8050){ 				DA231357 += get_rtime(); 							} 			if (DA231357 >= 8000) { 				cls_oled(OLED_BLACK); 				DA231357 = 0; 				DA231356 = FALSE; 							} 					} 		if (block) { 		if (DA231114 > 7)combo_run(DA231112); 			if (DA231521 < 310) { 				DA231521 += get_rtime(); 							} 			if (DA231521 <= 300 ) { 				DA231169(); 							} 			if (DA231521 > 300 ) { 				block = FALSE; 				DA231521 = 1; 				DA231717 = TRUE; 							} 			if (DA231521 < 0) { 				DA231521 = 1; 							} 			if (DA231521 <= 100) { 				combo_stop(DA23188); 				combo_stop(DA23197); 				combo_stop(DA23189); 				combo_stop(DA23198); 				combo_stop(DA23195); 				combo_stop(DA23196); 				combo_stop(DA23192); 				combo_stop(DA23194); 				combo_stop(DA23191); 				combo_stop(DA23187); 				combo_stop(DA23185); 				combo_stop(DA23190); 				combo_stop(DA231107); 				combo_stop(DA231109); 				combo_stop(DA231100); 				combo_stop(DA231108); 				combo_stop(DA23199); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(DA231437){ 			DA231438 = FALSE; 					} 		if(DA231438){ 			DA231437 = FALSE; 					} 		if(DA231361){ 			DA231362 = FALSE; 			DA231363 = FALSE; 			DA231364 = FALSE; 					} 		if(DA231362){ 			DA231361 = FALSE; 			DA231363 = FALSE; 			DA231364 = FALSE; 					} 		if(DA231363){ 			DA231361 = FALSE; 			DA231362 = FALSE; 			DA231364 = FALSE; 					} 		if(DA231364){ 			DA231361 = FALSE; 			DA231362 = FALSE; 			DA231363 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				DA2311174 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				DA2311174 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				DA2311062 = !DA2311062; 				DA231229(DA2311294); 				DA231203(DA2311062, sizeof(DA231552) - 1, DA231552[0]); 				DA231356 = TRUE; 							} 					} 		if (DA2311062) { 			if(DA231381){ 				DA231266(); 			} 			if (DA231379) { 				DA231256(); 							} 			if (event_release(DA231452)) { 				DA231555 = 1; 							} 			if (DA231555 < 8000) { 				DA231555 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					DA231557 = !DA231557; 					DA231229(DA231557); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (DA231557) { 				if (DA231557) DA231222(DA2311094); 				if (DA231557) { 					DA231148(); 									} 							} 			else if (!get_ival(DA231452)) { 				DA231222(DA2311097); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						DA231352 = TRUE; 						DA231359 = TRUE; 						DA231358 = FALSE; 						if (!DA231352) { 							DA231358 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!DA231358) { 					if (DA231352 || DA231353) { 						vm_tctrl(0); 					} 					if (DA231352) { 						combo_stop(DA23172); 						vm_tctrl(0); 						DA231360= DA231149(DA231360,0  ); 						DA231361 = DA231149(DA231361, 1); 						DA231362  = DA231149(DA231362   ,2  ); 						DA231363  = DA231149(DA231363 , 3); 						DA231364  = DA231149(DA231364 , 4); 						DA231365 = DA231149(DA231365, 5); 						DA231366 = DA231149(DA231366, 6); 						DA231367 = DA231149(DA231367, 7); 						DA231368 = DA231149(DA231368, 8); 						DA231369 = DA231149(DA231369, 9); 						DA231370 = DA231149(DA231370, 10); 						DA231371 = DA231149(DA231371, 11); 						DA231372 = DA231149(DA231372, 12); 						DA231373 = DA231149(DA231373,13); 						DA231374 = DA231149(DA231374, 14); 						DA231375 = DA231149(DA231375, 15); 						DA231376 = DA231149(DA231376, 16); 						DA231377 = DA231149(DA231377, 17); 						DA231378 = DA231149(DA231378, 18); 						DA231379 = DA231149(DA231379, 19); 						DA231114 = DA231149(DA231114, 20); 						DA231381 = DA231149(DA231381, 21); 						DA231382              = DA231149(DA231382              ,22  ); 						DA231383              = DA231149(DA231383              ,23  ); 						DA231384               = DA231149(DA231384               ,24  ); 						if (event_press(PS4_DOWN)) { 							DA231354 = clamp(DA231354 + 1, 0, DA231385); 							DA231359 = TRUE; 													} 						if (event_press(PS4_UP)) { 							DA231354 = clamp(DA231354 - 1, 0, DA231385); 							DA231359 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							DA231352 = FALSE; 							DA231358 = FALSE; 							DA231359 = FALSE; 							vm_tctrl(0); 							combo_run(DA23175); 													} 						if (DA231579[DA231354][2] == 1) { 							if(DA231354 == 0 ){ 								if(DA231360 == 2 ){ 									if (event_press(PS4_CROSS)) { 										DA231355 = DA231579[DA231354][0]; 										DA231352 = FALSE; 										DA231353 = TRUE; 										DA231359 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									DA231355 = DA231579[DA231354][0]; 									DA231352 = FALSE; 									DA231353 = TRUE; 									DA231359 = TRUE; 																	} 															} 													} 						DA231169(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DA231569[0]); 						DA231158(DA231354 + 1, DA231164(DA231354 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DA231571[0]); 						DA231158(DA231952, DA231164(DA231952), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(DA231354 == 0 ){ 							if(DA231360 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, DA231573[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, DA231574[0]); 															} 													} 						else{ 							if (DA231579[DA231354][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, DA231574[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, DA231573[0]); 															} 													} 											} 					if (DA231353) { 						DA231439               = DA231152(DA231439, 0); 						DA231440               = DA231152(DA231440, 1); 						DA231441             = DA231152(DA231441, 2); 						DA231442           = DA231152(DA231442, 3); 						DA231443             = DA231152(DA231443, 4); 						DA231444             = DA231152(DA231444, 5); 						DA231445              = DA231152(DA231445, 6); 						DA231446           = DA231152(DA231446, 7); 						DA231386          = DA231152(DA231386, 8); 						DA231387   = DA231152(DA231387, 9); 						DA231388 = DA231152(DA231388, 10); 						DA231389      = DA231152(DA231389, 11); 						DA231390    = DA231152(DA231390, 12); 						DA231391    = DA231152(DA231391, 13); 						DA231392    = DA231152(DA231392, 14); 						DA231393      = DA231152(DA231393, 15); 						DA231394      = DA231152(DA231394, 16); 						DA231275              = DA231152(DA231275, 17); 						DA231276           = DA231152(DA231276, 18); 						DA231277            = DA231152(DA231277, 19); 						DA231278            = DA231152(DA231278, 20); 						DA231279= DA231152(DA231279, 21); 						DA231407               = DA231152(DA231407, 22); 						DA231408               = DA231152(DA231408, 23); 						DA231409                   = DA231152(DA231409, 24); 						DA231410                   = DA231152(DA231410, 25); 						DA231411                   = DA231152(DA231411, 26); 						DA231412   = DA231152(DA231412, 27); 						DA231413   = DA231152(DA231413, 28); 						DA231414 = DA231152(DA231414, 29); 						DA231415   = DA231152(DA231415, 30); 						DA231416   = DA231152(DA231416, 31); 						DA231417 = DA231152(DA231417, 32); 						DA231418   = DA231152(DA231418, 33); 						DA231419   = DA231152(DA231419, 34); 						DA231420 = DA231152(DA231420, 35); 						DA231421   = DA231152(DA231421, 36); 						DA231422   = DA231152(DA231422, 37); 						DA231423 = DA231152(DA231423, 38); 						DA231424   = DA231155(DA231424, 39); 						DA231425         = DA231155(DA231425, 40); 						DA231426   = DA231152(DA231426, 41); 						DA231427     = DA231152(DA231427, 42); 						DA231428                   = DA231155(DA231428, 43); 						DA2311250 = DA231152(DA2311250, 54); 						DA2311243 = DA231152(DA2311243, 55); 						DA231429               = DA231155(DA231429, 44); 						DA231430 = DA231155(DA231430, 45); 						DA231431     = DA231152(DA231431, 46); 						DA231432               = DA231155(DA231432, 47); 						DA231433 = DA231152(DA231433, 48); 						DA231434 = DA231152(DA231434, 49); 						DA231435 = DA231152(DA231435, 50); 						DA231436 = DA231152(DA231436, 51); 						DA231437               = DA231152(DA231437, 52); 						DA231438                 = DA231152(DA231438, 53); 						DA231395       = DA231155(DA231395     ,56 ); 						DA231396       = DA231155(DA231396     ,57 ); 						DA231397      = DA231152(DA231397    ,58 ); 						DA231398   = DA231152(DA231398 ,59 ); 						DA231399       = DA231155(DA231399     ,60 ); 						DA231400       = DA231155(DA231400     ,61 ); 						DA231401   = DA231152(DA231401 ,62 ); 						DA231402      = DA231152(DA231402    ,63 ); 						DA231403          = DA231155(DA231403        ,64 ); 						DA231404          = DA231155(DA231404        ,65 ); 						DA231405         = DA231152(DA231405       ,66 ); 						DA231455             = DA231155(DA231455           ,67 ); 						DA23129             = DA231152(DA23129           ,68); 						DA231476           = DA231152(DA231476         ,69); 						DA231474         = DA231152(DA231474       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								DA231355 = clamp(DA231355 + 1, DA231579[DA231354][0], DA231579[DA231354][1]); 								DA231359 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								DA231355 = clamp(DA231355 - 1, DA231579[DA231354][0], DA231579[DA231354][1]); 								DA231359 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							DA231352 = TRUE; 							DA231353 = FALSE; 							DA231359 = TRUE; 													} 						DA231169(); 						DA231954 = DA231838[DA231355][0]; 						DA231955 = DA231838[DA231355][1]; 						if (DA231838[DA231355][4] == 0) { 							DA231158(DA231954, DA231164(DA231954), 4, 20, OLED_FONT_SMALL); 							DA231158(DA231955, DA231164(DA231955), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (DA231359) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (DA231353) { 							print(DA231214(DA231167(DA231588[DA231355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA231588[DA231355]); 													} 						else { 							print(DA231214(DA231167(DA231589[DA231354]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DA231589[DA231354]); 													} 						DA231359 = FALSE; 					} 									} 				if (!DA231352 && !DA231353) { 					if (DA231358) { 						cls_oled(0); 						combo_run(DA23172); 						DA231358 = FALSE; 						DA231356 = TRUE; 						vm_tctrl(0); 					} 					if(DA231360 == 0){ 						DA231447      = PS4_CIRCLE; 						DA231448      = PS4_CROSS ; 						DA231449    = PS4_L1    ; 						DA231450  = PS4_R1; 						DA231451    = PS4_L2; 						DA231452    = PS4_R2; 						DA231453     = PS4_SQUARE; 						DA231454  = PS4_TRIANGLE; 					} 					else if(DA231360 == 1){ 						DA231447      = PS4_SQUARE; 						DA231448      = PS4_CROSS ; 						DA231449    = PS4_L1    ; 						DA231450  = PS4_R1; 						DA231451    = PS4_L2; 						DA231452    = PS4_R2; 						DA231453     = PS4_CIRCLE; 						DA231454  = PS4_TRIANGLE; 					} 					else if(DA231360 == 2){ 						DA231447      = DA2311397[DA231439]; 						DA231448      = DA2311397[DA231440] ; 						DA231449    = DA2311397[DA231441]  ; 						DA231450  = DA2311397[DA231442]; 						DA231451    = DA2311397[DA231443]; 						DA231452    = DA2311397[DA231444]; 						DA231453     = DA2311397[DA231445]; 						DA231454  = DA2311397[DA231446]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DA2311197) { 						set_val(DA231448, 0); 						vm_tctrl(0); 						combo_run(DA23177); 											} 					if (DA231717) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							DA231593 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							DA2311052 = DA2311408[DA231593][0]; 							DA231669 = DA2311408[DA231593][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							DA231428 += 5; 							DA231210(DA231214(sizeof(DA231595) - 1, OLED_FONT_MEDIUM_WIDTH), DA231595[0], DA231428); 													} 						if (event_press(PS4_LEFT)) { 							DA231428 -= 5; 							DA231210(DA231214(sizeof(DA231595) - 1, OLED_FONT_MEDIUM_WIDTH), DA231595[0], DA231428); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !DA231615 ) { 						if (event_press(PS4_UP)) { 							DA231600 += 25; 							DA231210(DA231214(sizeof(DA231601) - 1, OLED_FONT_MEDIUM_WIDTH), DA231601[0], DA231600); 													} 						if (event_press(PS4_DOWN)) { 							DA231600 -= 25; 							DA231210(DA231214(sizeof(DA231601) - 1, OLED_FONT_MEDIUM_WIDTH), DA231601[0], DA231600); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (DA231374) { 						DA231248(); 											} 					if (DA231375) { 						DA231249(); 						DA231250(); 											} 					if (!DA231375) { 						if (get_ival(DA231447)) { 							if (event_press(PS4_RIGHT)) { 								DA231608 += 2; 								DA231210(DA231214(sizeof(DA231609) - 1, OLED_FONT_MEDIUM_WIDTH), DA231609[0], DA231608); 															} 							if (event_press(PS4_LEFT)) { 								DA231608 -= 2; 								DA231210(DA231214(sizeof(DA231609) - 1, OLED_FONT_MEDIUM_WIDTH), DA231609[0], DA231608); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(DA231449) ){ 							if(get_ival(DA231447) && get_ptime(DA231447) > DA231608){ 								set_val(DA231447,0); 															} 													} 											} 					if(DA231378){ 						DA231253(); 											} 					if (DA231370) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								DA231615 = !DA231615; 								DA231229(DA231615); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (DA231615 && DA231370) { 						vm_tctrl(0); 						combo_stop(DA23185); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								DA231424 += 10; 								DA231210(DA231214(sizeof(DA231617) - 1, OLED_FONT_MEDIUM_WIDTH), DA231617[0], DA231424); 															} 							if (event_press(PS4_DOWN)) { 								DA231424 -= 10; 								DA231210(DA231214(sizeof(DA231617) - 1, OLED_FONT_MEDIUM_WIDTH), DA231617[0], DA231424); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						DA231222(DA2311096); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								DA231622 = FALSE; 								vm_tctrl(0); 								combo_run(DA23178); 															} 							if (event_press(PS4_LEFT)) { 								DA231622 = TRUE; 								vm_tctrl(0); 								combo_run(DA23178); 															} 							set_val(PS4_L1,0); 													} 											} 					if (DA231371) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								DA231624 = !DA231624; 								DA231229(DA231624); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (DA231624 && DA231371) { 						vm_tctrl(0); 						DA231222(DA2311098); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								DA231625 = FALSE; 								vm_tctrl(0); 								combo_run(DA23179); 															} 							if (event_press(PS4_RIGHT)) { 								DA231625 = TRUE; 								vm_tctrl(0); 								combo_run(DA23179); 															} 													} 											} 					if(DA231363 || DA231364 ){ 						DA231118(); 											} 					if (DA231361) { 						if (DA231361 == DA2311102) DA231628 = TRUE; 						if (DA231361 == DA2311103) { 							if (event_press(DA2311407[-1 +DA231394]) && get_brtime(DA2311407[-1 +DA231394]) <= 200) { 								DA231628 = !DA231628; 								DA231229(DA231628); 															} 							set_val(DA2311407[-1 +DA231394], 0); 													} 						if (DA231361 > 0 && DA231361 < 3 && DA231628 == 1) { 							DA231226(); 													} 						else if (DA231361 == 3) { 							if (get_ival(DA2311407[-1 +DA231394])) { 								DA231226(); 															} 							set_val(DA2311407[-1 +DA231394], 0); 													} 											} 									if (DA231362) { 						if (DA231362 == DA2311102) DA231631 = TRUE; 						if (DA231362 == DA2311103) { 							if (event_press(DA2311407[-1 +DA231279]) && get_brtime(DA2311407[-1 +DA231279]) <= 200) { 								DA231631 = !DA231631; 								DA231229(DA231631); 															} 							set_val(DA2311407[-1 +DA231279], 0); 													} 						if (DA231362 > 0 && DA231362 < 3 && DA231631 == 1) { 							DA231224(); 													} 						else if (DA231362 == 3) { 							if (get_ival(DA2311407[-1 +DA231279])) { 								DA231224(); 															} 							set_val(DA2311407[-1 +DA231279], 0); 													} 											} 					if (DA231365) { 						if (DA231365 == 1) { 							DA231634 = PS4_R3; 							DA231631 = FALSE; 													} 						if (DA231365 == 2) { 							DA231634 = PS4_L3; 							DA231631 = FALSE; 													} 						if (DA231365 == 3) { 							DA231634 = XB1_PR1; 							DA231631 = FALSE; 													} 						if (DA231365 == 4) { 							DA231634 = XB1_PR2; 							DA231631 = FALSE; 													} 						if (DA231365 == 5) { 							DA231634 = XB1_PL1; 							DA231631 = FALSE; 													} 						if (DA231365 == 6) { 							DA231634 = XB1_PL2; 							DA231631 = FALSE; 													} 						if(get_ival(DA231634)){ 							if(DA231407 || DA231408){ 								if( DA231407 && event_press(PS4_L1)){ 									DA231479 = FALSE; 									DA2311051 = DA231407  ; 									DA231227( DA231407   ); 								} 								if( DA231408 && event_press(PS4_R1)){ 									DA231479 = TRUE; 									DA2311051 =  DA231408 ; 									DA231227( DA231408   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( DA231409 ){ 								if(event_press(PS4_SQUARE)){ 									DA231479 = FALSE; 									DA2311051 =  DA231409  ; 													combo_stop(DA23188); 				combo_stop(DA23197); 				combo_stop(DA23189); 				combo_stop(DA23198); 				combo_stop(DA23195); 				combo_stop(DA23196); 				combo_stop(DA23192); 				combo_stop(DA23194); 				combo_stop(DA23191); 				combo_stop(DA23187); 				combo_stop(DA23185); 				combo_stop(DA23190); 				combo_stop(DA231107); 				combo_stop(DA231109); 				combo_stop(DA231100); 				combo_stop(DA231108); 				combo_stop(DA23199); 									DA231227( DA231409   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									DA231479 = TRUE; 									DA2311051 =  DA231409  ; 									DA231227( DA231409   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( DA231410 ){ 								if(event_press(PS4_CROSS)){ 									DA231479 = FALSE; 									DA2311051 =  DA231410  ; 									DA231227( DA231410   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(DA23188); 				combo_stop(DA23197); 				combo_stop(DA23189); 				combo_stop(DA23198); 				combo_stop(DA23195); 				combo_stop(DA23196); 				combo_stop(DA23192); 				combo_stop(DA23194); 				combo_stop(DA23191); 				combo_stop(DA23187); 				combo_stop(DA23185); 				combo_stop(DA23190); 				combo_stop(DA231107); 				combo_stop(DA231109); 				combo_stop(DA231100); 				combo_stop(DA231108); 				combo_stop(DA23199); 									DA231479 = TRUE; 									DA2311051 =  DA231410  ; 									DA231227( DA231410   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( DA231411 ){ 								if(event_press(PS4_R3)){ 									DA231479 = FALSE; 									DA2311051 =  DA231411  ; 									DA231227( DA231411   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(DA231634,0); 											} 					if (DA231366) { 						if (DA231413 == 1) { 							if (event_press(DA2311407[-1 + DA231412]) && !DA2311149) { 								vm_tctrl(0); 								combo_run(DA23182); 															} 							else if (event_press(DA2311407[-1 + DA231412]) && DA2311149) { 								set_val(DA2311407[-1 + DA231412], 0); 								DA231479 = !DA231414; 								DA2311051 = DA231366; 								DA231227(DA231366); 															} 													} 						else { 							if (event_press(DA2311407[-1 + DA231412])) { 								DA231479 = !DA231414; 								set_val(DA2311407[-1 + DA231412], 0); 								DA2311051 = DA231366; 								DA231227(DA231366); 															} 													} 					} 					if (DA231368) { 						if (DA231419 == 1) { 							if (event_press(DA2311407[-1 +DA231418]) && !DA2311149) { 								vm_tctrl(0); 								combo_run(DA23182); 															} 							else if (event_press(DA2311407[-1 +DA231418]) && DA2311149) { 								set_val(DA2311407[-1 +DA231418], 0); 								DA231479 = !DA231420; 								DA2311051 = DA231368; 								DA231227(DA231368); 															} 													} 						else { 							if (event_press(DA2311407[-1 +DA231418])) { 								DA231479 = !DA231420; 								set_val(DA2311407[-1 +DA231418], 0); 								DA2311051 = DA231368; 								DA231227(DA231368); 															} 													} 					} 					if (DA231367) { 						if (DA231416 == 1) { 							if (event_press(DA2311407[-1 +DA231415]) && !DA2311149) { 								vm_tctrl(0); 								combo_run(DA23182); 															} 							else if (event_press(DA2311407[-1 +DA231415]) && DA2311149) { 								set_val(DA2311407[-1 +DA231415], 0); 								DA231479 = !DA231417; 								DA2311051 = DA231367; 								DA231227(DA231367); 															} 													} 						else { 							if (event_press(DA2311407[-1 +DA231415])) { 								DA231479 = !DA231417; 								set_val(DA2311407[-1 +DA231415], 0); 								DA2311051 = DA231367; 								DA231227(DA231367); 															} 													} 					} 					if (DA231369) { 						if (DA231422 == 1) { 							if (event_press(DA2311407[-1 +DA231421]) && !DA2311149) { 								vm_tctrl(0); 								combo_run(DA23182); 															} 							else if (event_press(DA2311407[-1 +DA231421]) && DA2311149) { 								set_val(DA2311407[-1 +DA231421], 0); 								DA231479 = !DA231423; 								DA2311051 = DA231369; 								DA231227(DA231369); 															} 													} 						else { 							if (event_press(DA2311407[-1 +DA231421])) { 								DA231479 = !DA231423; 								set_val(DA2311407[-1 +DA231421], 0); 								DA2311051 = DA231369; 								DA231227(DA231369); 															} 													} 					} 					if (DA2311051 == DA231310 && combo_running(DA23130)) set_val(DA231449, 100); 					if(DA231383){ 						if(!block){ 							if(!get_val(DA231451)){ 								if( !get_val(DA231452)){ 									if(get_val(DA231448)){ 										DA231650 += get_rtime(); 																			} 									if(DA231402){ 										if(get_ival(DA231448) && get_ptime(DA231448) > DA231400){ 											set_val(DA231448,0); 																					} 																			} 									if(event_release(DA231448)){ 										if( DA231650 < DA231399 ){ 											DA231651 = DA231399 - DA231650; 											combo_run(DA231107); 																					} 										else{ 											if(DA231401) combo_run(DA231108); 																					} 										DA231650 = 0; 																			} 																	} 							} 						} 											} 					if(DA231382){ 						if(!block){ 							if(!get_ival(DA231451)){ 								if( !get_ival(DA231452)){ 									if(get_ival(DA231454)){ 										DA231652 += get_rtime(); 																			} 									if(event_release(DA231454)){ 										if(DA231652 < DA231395){ 											DA231653 = DA231395 - DA231652; 											combo_run(DA231109); 																					} 										else{ 											if(DA231398) combo_run(DA23199); 																					} 										DA231652 = 0; 																			} 																	} 							} 						} 											} 					if(DA231384){ 						if(!block){ 							if(get_ival(DA231453)){ 								DA231654 += get_rtime(); 															} 							if(DA231405){ 								if(get_ival(DA231453) && get_ptime(DA231453) > DA231404){ 									set_val(DA231453,0); 																	} 															} 							if(event_release(DA231453)){ 								if(DA231654 && (DA231654 < DA231403)){ 									DA231655 = DA231403 - DA231654; 									combo_run(DA231100); 																	} 								DA231654 = 0; 															} 													} 											} 					if (DA231372) { 						if (event_press(DA2311407[-1 +DA231426])) { 							vm_tctrl(0); 							combo_run(DA23177); 													} 						set_val(DA2311407[-1 +DA231426], 0); 											} 					if(!DA231376){ 						DA231429 = 0 ; 						DA231430 = 0; 						DA231431 = FALSE; 						DA231432 = 0; 											} 					if (DA231377) { 						DA231249(); 						if (DA231433 == 0) { 							DA231658 = FALSE; 							DA231457 = 0; 													} 						else { 							DA231658 = TRUE; 							DA231457 = 40; 													} 						if (DA231434 == 0) { 							DA231660 = FALSE; 							DA231456 = 0; 													} 						else { 							DA231660 = TRUE; 							DA231456 = 85; 													} 						if (DA231435 == 0) { 							DA231662 = FALSE; 							DA231458 = 0; 													} 						else { 							DA231662 = TRUE; 							DA231458 = -15; 													} 						if (DA231436 == 0) { 							DA231664 = FALSE; 													} 						else { 							DA231664 = TRUE; 													} 						if(DA231435 == 6 || DA231434 == 6 || DA231433 == 6){ 							if (get_ival(DA2311407[-1 + DA231435]) || get_ival(DA2311407[-1 + DA231434]) || get_ival(DA2311407[-1 + DA231433])){ 								combo_run(DA2310); 															} 													} 						if (DA231662) { 							if (get_val(DA2311407[-1 + DA231435])) { 								set_val(DA2311407[-1 + DA231435], 0); 								combo_run(DA23197); 								DA2311206 = 9000; 															} 													} 						if (DA231664) { 							if (get_val(DA2311407[-1 + DA231436])) { 								set_val(DA2311407[-1 + DA231436], 0); 								combo_run(DA23198); 								DA2311206 = 9000; 							} 							if (combo_running(DA23198)) { 								if (get_ival(DA231448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA231452) > 30) { 									combo_stop(DA23198); 																	} 															} 													} 						if (DA231660) { 							if (get_val(DA2311407[-1 + DA231434])) { 								set_val(DA2311407[-1 + DA231434], 0); 								DA231255(); 								DA2311206 = 9000; 															} 													} 						if (DA231658) { 							if (get_val(DA2311407[-1 + DA231433])) { 								set_val(DA2311407[-1 + DA231433], 0); 								combo_run(DA23195); 								DA2311206 = 9000; 															} 													} 											} 					else{ 						DA231457 = 0; 						DA231458 = 0; 						DA231456 = 0; 											} 					if (DA231379) { 						DA231256(); 											} 									} 							} 								if (combo_running(DA231111) && (  get_ival(DA231448) ||   get_ival(DA231454) ||         get_ival(DA231451) ||        get_ival(DA231447) ||        get_ival(DA231450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA231111); 			} 					} 		else { 			if (!get_ival(DA231452)) DA231222(DA2311095); 					} 			} 			DA231268(); 	} combo DA2311 { 	set_val(DA231451, 100); 	set_val(DA231449,100); 	DA231243(); 	wait(400); 	set_val(DA231448,100); 	wait(90); 	wait( 400); 	} combo DA2312 { 	set_val(DA231451, 100); 	set_val(DA231449,100); 	DA231243(); 	wait(400); 	set_val(DA231447,100); 	wait(220); 	wait( 400); 	} combo DA2313 { 	call(DA23128); 	wait( 100); 	call(DA23198); 	DA231239(DA2311052, DA231669); 	wait( 800); 	wait( 350); 	set_val(DA231450,100); 	set_val(DA231449,100); 	wait( 400); 	} combo DA2314 { 	DA231245(); 	wait(50); 	DA231243(); 	wait(50); 	wait( 350); 	} combo DA2315 { 	DA231243(); 	wait( DA2311055 + random(1,5)); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA2316 { 	if (DA231479) DA231686 = DA231593 + 1; 	else DA231686 = DA231593 - 1; 	DA231234(DA231686); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	DA231239(DA2311153, DA231672); 	wait( DA2311055 + random(1,5)); 	DA231239(DA2311153, DA231672); 	wait( 1000); 	wait( 350); 	} combo DA2317 { 	DA231246(); 	DA231479 = FALSE; 	wait(DA2311055 + random(1,5)); 	DA231243(); 	wait(DA2311055 + random(1,5)); 	DA231246(); 	wait(DA2311055 + random(1,5)); 	DA231479 = TRUE; 	DA231243(); 	wait(DA2311055 + random(1,5)); 	wait(350); 	} combo DA2318 { 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231479 = TRUE; 	DA231243(); 	wait( DA2311055 + random(1,5)); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231479 = FALSE; 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA2319 { 	DA231479 = TRUE; 	DA231243(); 	wait(DA2311055 + random(1,5)); 	DA231246(); 	wait(DA2311055 + random(1,5)); 	DA231479 = FALSE; 	DA231243(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23110 { 	DA231479 = FALSE; 	DA231243(); 	wait( DA2311055 + random(1,5)); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231479 = TRUE; 	DA231243(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23111 { 	DA231239(0,0); 	set_val(DA231449,100); 	set_val(DA231450,100); 	DA231245(); 	wait( 60); 	wait( 60); 	} combo DA23112 { 	set_val(DA2311118, inv(DA2311052)); 	set_val(DA2311119, inv(DA231669)); 	set_val(DA231450, 100); 	set_val(DA231449, 100); 	wait( 60); 	set_val(DA2311118, inv(DA2311052)); 	set_val(DA2311119, inv(DA231669)); 	set_val(DA231450, 100); 	set_val(DA231449, 100); 	wait( 500); 	wait( 350); 	} combo DA23113 { 	DA231239(0, 0); 	set_val(DA231451, 100); 	wait( 60); 	DA231239(0, 0); 	set_val(DA231451, 100); 	set_val(DA231447, 100); 	wait( 60); 	DA231239(0, 0); 	set_val(DA231451, 100); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 80); 	DA231239(0, 0); 	set_val(DA231451, 100); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	wait( 350); 	} combo DA23114 { 	set_val(DA231447, 100); 	wait( 60); 	DA231239(inv(DA2311052), inv(DA231669)); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 80); 	DA231239(inv(DA2311052), inv(DA231669)); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	wait( 350); 	} combo DA23115 { 	set_val(DA231449, 100); 	DA231243(); 	wait( 500); 	wait( 350); 	} combo DA23116 { 	DA231246(); 	wait( DA2311055 + random(1,5)); 	if(DA231479) DA231686 = DA231593 + 3; 	else  DA231686 = DA231593 - 3; 	DA231234(DA231686); 	DA231236(DA2311153,DA231672); 	wait(DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	if(DA231479) DA231686 = DA231593 + 1; 	else  DA231686 = DA231593 - 1; 	DA231234(DA231686); 	DA231236(DA2311153,DA231672); 	wait(DA2311055 + random(1,5)); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23117 { 	set_val(DA231449,100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	set_val(DA231449,100); 	wait( DA2311055 + random(1,5)); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23118 { 	set_val(DA231451,100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451,100); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451,100); 	DA231243(); 	set_val(DA231451,100); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	set_val(DA231451,100); 	set_val(DA231452,100); 	wait(50); 	wait(350); 	} combo DA23119 { 	set_val(DA231451,100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451,100); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451,100); 	DA231243(); 	set_val(DA231451,100); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23120 { 	DA231244(); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	DA231243()    	wait( DA2311055 + random(1,5)); 	DA231479 = !DA231479; 	DA231242(); 	wait( 1000); 	wait( 350); 	} combo DA23121 { 	set_val(DA231449,100); 	DA231246(); 	wait(50); 	DA231239(0,0); 	set_val(DA231449,100); 	wait(50); 	set_val(DA231449,100); 	DA231246(); 	wait(50); 	wait( 350); 	} combo DA23122 { 	DA231239(0, 0); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23123 { 	DA231246(); 	wait(DA2311055 + random(1,5)); 	DA231247()wait(DA2311055 + random(1,5)); 	DA231246(); 	wait(DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23124 { 	set_val(DA231450, 100); 	set_val(DA231449, 100); 	wait( 20); 	set_val(DA231450, 100); 	set_val(DA231449, 100); 	if (DA231479) DA231686 = DA231593 + 4; 	else { 		DA231686 = DA231593 - 4; 			} 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	set_val(DA231452, 100); 	wait( 100); 	wait( 350); 	} combo DA23125 { 	set_val(DA231451, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451, 100); 	wait( 30); 	set_val(DA231451, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23126 { 	set_val(DA231451, 100); 	DA231245(); 	wait( 70); 	set_val(DA231451, 100); 	DA231247(); 	wait( 60); 	set_val(DA231451, 100); 	DA231246(); 	wait( 60); 	wait( 350); 	} combo DA23127 { 	set_val(DA231451, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451, 100); 	wait( 30); 	set_val(DA231451, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231239(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} combo DA23128 { 	DA231243(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo DA23129 { 	DA231243(); 	set_val(DA231452, 0); 	wait( 310); 	wait( 100); 	wait( 350); 	} combo DA23130 { 	if (DA2311051 == DA231312) DA2311056 = 200; 	else DA2311056 = 1; 	wait( DA2311056); 	DA231245(); 	wait( 70); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( 70); 	wait( 350); 	} combo DA23131 { 	set_val(DA231449, 100)DA231245(); 	DA231239(DA2311052,DA231669); 	wait( 50); 	set_val(DA231449, 100)DA231247(); 	DA231239(DA2311052,DA231669); 	wait( 50); 	set_val(DA231449, 100)DA231243(); 	DA231239(DA2311052,DA231669); 	wait( 50); 	DA231239(DA2311052,DA231669); 	wait(465); 	DA231239(DA2311052,DA231669); 	set_val(DA231451, 100); 	set_val(DA231452, 100); 	wait(50); 	if (DA231479) DA231686 = DA231593 - 1; 	else DA231686 = DA231593 + 1; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 50); 	if (DA231479) DA231686 = DA231593 + 4; 	else DA231686 = DA231593 - 4; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 700); 	wait( 350); 	} combo DA23132 { 	if (DA2311051 == DA231312) DA2311056 = 200; 	else DA2311056 = 1; 	set_val(DA231451,100); 	wait( DA2311056); 	DA231245(); 	set_val(DA231451,100); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	set_val(DA231451,100); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	set_val(DA231451,100); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23133 { 	if (DA231479) DA231686 = DA231593 - 2; 	else DA231686 = DA231593 + 2; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	wait( 280); 	DA231247(); 	wait( 50); 	if (DA231479) DA231686 = DA231593 + 2; 	else DA231686 = DA231593 - 2; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 45); 	set_val(DA231447, 100); 	DA231239(DA2311153, DA231672); 	wait( 45); 	DA231239(DA2311153, DA231672); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 45); 	DA231239(DA2311153, DA231672); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 45); 	DA231239(DA2311153, DA231672); 	wait( 100); 	DA231239(DA2311153, DA231672); 	wait( 500); 	wait( 350); 	} combo DA23134 { 	DA231243(); 	wait( 280); 	DA231242()  set_val(DA231447, 100); 	set_val(DA231451, 100); 	wait( 60); 	DA231242()  set_val(DA231451, 100); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 60); 	DA231242()  set_val(DA231451, 100); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	wait( 250); 	DA231242()   	wait( 300); 	wait( 350); 	} combo DA23135 { 	DA231243(); 	wait( 300); 	DA231245(); 	wait( 60); 	wait( 350); 	} combo DA23136 { 	set_val(DA231449, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231449, 100); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231449, 100); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23137 { 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23138 { 	set_val(DA231449, 100); 	DA231244(); 	wait( 60); 	set_val(DA231449, 100); 	DA231247(); 	wait( 60); 	set_val(DA231449, 100); 	DA231243(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo DA23139 { 	DA231246(); 	set_val(DA231449,100); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	set_val(DA231449,100); 	wait( 70); 	DA231243(); 	set_val(DA231449,100); 	wait( 70); 	wait( 350); 	} combo DA23140 { 	if (DA231479) DA231686 = DA231593 + 3; 	else DA231686 = DA231593 - 3; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	set_val(DA231447, 100); 	set_val(DA231451,100); 	wait( 60); 	set_val(DA231451,100); 	DA231239(DA2311153, DA231672); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 80); 	set_val(DA231451,100); 	DA231239(DA2311153, DA231672); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	set_val(DA231451,100); 	DA231239(DA2311153, DA231672); 	wait( 300); 	wait( 350); 	} combo DA23141 { 	set_val(DA231449, 100); 	DA231245(); 	DA231239(0, 0); 	wait( DA2311055 + random(1,5)); 	set_val(DA231449, 100); 	DA231247(); 	DA231239(0, 0); 	wait( 65); 	set_val(DA231449, 100); 	DA231239(0, 0); 	DA231246(); 	wait( DA2311055 + random(1,5)); 	if (DA231479) DA231686 = DA231593 + 1; 	else DA231686 = DA231593 - 1; 	DA231234(DA231686); 	set_val(DA231452,0); 	DA231239(DA2311153, DA231672); 	wait( 200); 	set_val(DA231452,0); 	wait( 350); 	} combo DA23142 { 	if (DA2311051 == DA231312) DA2311056 = 200; 	else DA2311056 = 1; 	wait( DA2311056); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	wait( 350); 	} combo DA23143 { 	DA231246(); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} combo DA23144 { 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	if (DA2311051 == DA231325) DA231242(); 	set_val(DA231451, 100); 	set_val(DA231452, 100); 	wait( 200); 	if (DA2311051 == DA231325) DA231242(); 	wait( 300); 	wait( 350); 	} combo DA23145 { 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231247(); 	wait( DA2311055 + random(1,5)); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	if (DA2311051 == DA231325) DA231242(); 	set_val(DA231451, 100); 	set_val(DA231452, 100); 	wait( 200); 	if (DA2311051 == DA231325) DA231242(); 	wait( 300); 	wait( 350); 	} combo DA23146 { 	call(DA23133)call(DA23135); 	} combo DA23147 {    DA231717 = FALSE; 	DA231239(DA2311052, DA231669); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231239(DA2311052, DA231669); 	DA231247(); 	DA231717 = FALSE; 	wait( DA2311055 + random(1,5)); 	DA231239(DA2311052, DA231669); 	DA231243(); 	DA231717 = FALSE; 	wait( DA2311055 + random(1,5)); 	set_val(DA231451, 100); 	set_val(DA231452, 100); 	DA231239(inv(DA2311052), inv(DA231669)); 	DA231717 = FALSE; 	wait( 400); 	wait( 350); 	DA231717 = TRUE; 	} combo DA23148 { 	DA231239(DA2311052, DA231669); 	set_val(XB1_LS, 100); 	DA231245(); 	wait( DA2311055 + random(1,5)); 	DA231239(DA2311052, DA231669); 	DA231247(); 	set_val(XB1_LS, 100); 	wait( DA2311055 + random(1,5)); 	DA231239(DA2311052, DA231669); 	DA231243(); 	wait( DA2311055 + random(1,5)); 	set_val(DA231451, 100); 	set_val(DA231452, 100); 	if (DA231479) DA231686 = DA231593 + 4; 	else DA231686 = DA231593 - 4; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 220); 	if (DA231479) DA231686 = DA231593 + 4; 	else DA231686 = DA231593 - 4; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 60); 	if (DA231479) DA231686 = DA231593 + 1; 	else DA231686 = DA231593 - 1; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	wait( 600); 	wait( 350); 	} combo DA23149 { 	set_val(DA231448, 0); 	set_val(DA231447, 100); 	wait( 80); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 80); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo DA23150 { 	set_val(DA231447, 100); 	set_val(DA231452,100); 	wait( 60); 	set_val(DA231452,100); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	set_val(DA231452,100); 	wait( 60); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	set_val(DA231452,100); 	wait( 60); 	wait( 350); 	} combo DA23151 { 	set_val(DA231449,100); 	set_val(DA231450,100); 	DA231239(inv(DA2311052), inv(DA231669)); 	wait( 200); 	set_val(DA231449,100); 	set_val(DA231450,100); 	DA231479 = FALSE; 	DA231242(); 	wait( 50); 	set_val(DA231449,100); 	set_val(DA231450,100); 	DA231479 = !DA231479; 	DA231242(); 	set_val(DA231449,100); 	set_val(DA231450,100); 	wait( 540); 	wait( 350); 	} combo DA23152 { 	set_val(DA231447, 100); 	wait( 60); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 60); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} combo DA23153 { 	DA231239(inv(DA2311052), inv(DA231669)); 	set_val(DA231451, 100); 	set_val(DA231447, 100); 	wait( 60); 	DA231239(inv(DA2311052), inv(DA231669)); 	set_val(DA231451, 100); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 60); 	DA231239(inv(DA2311052), inv(DA231669)); 	set_val(DA231451, 100); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	DA231239(0, 0); 	wait( 300); 	wait( 350); 	} combo DA23154 { 	set_val(DA231449, 100); 	set_val(DA231453, 100); 	wait( 60); 	set_val(DA231449, 100); 	set_val(DA231453, 100); 	set_val(DA231448, 100); 	wait( 60); 	set_val(DA231449, 100); 	set_val(DA231453, 0); 	set_val(DA231448, 100); 	DA231242(); 	wait( 60); 	set_val(DA231449, 100); 	DA231242(); 	wait( 300); 	wait( 350); 	} combo DA23155 { 	set_val(DA231447, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo DA23156 { 	set_val(DA231447, 100); 	set_val(DA231451,100); 	wait( 60); 	set_val(DA231451,100); 	set_val(DA231447, 100); 	set_val(DA231448, 100); 	wait( 60); 	set_val(DA231451,100); 	set_val(DA231447, 0); 	set_val(DA231448, 100); 	wait( 60); 	wait( 350); 	} combo DA23157 { 	set_val(DA231449, 100); 	DA231245(); 	wait( 300); 	wait( 350); 	} combo DA23158 { 	DA231246(); 	wait( 70); 	DA231247(); 	wait( 70); 	DA231245(); 	wait( 70); 	wait( 350); 	} combo DA23159 { 	set_val(DA231449,100); 	DA231246(); 	wait( 70); 	set_val(DA231449,100); 	DA231247(); 	wait( 70); 	DA231245(); 	set_val(DA231449,100); 	wait(50); 	wait( 350); 	} combo DA23160 { 	DA231239(DA2311052, DA231669); 	DA231246(); 	wait( 100); 	DA231247(); 	DA231239(DA2311052, DA231669); 	wait( 60); 	DA231245(); 	DA231239(DA2311052, DA231669); 	wait( 320); 	DA231239(DA2311052, DA231669); 	DA231247(); 	wait( 220); 	DA231239(DA2311052, DA231669); 	DA231245(); 	DA231239(DA2311052, DA231669); 	wait( 100); 	wait( 350); 	} combo DA23161 { 	call(DA23183); 	DA231239(0, 0); 	call(DA23184); 	call(DA23184); 	call(DA23184); 	call(DA23184); 	call(DA23184); 	set_val(DA231451, 100); 	DA231246(); 	wait( 70); 	set_val(DA231451, 100); 	DA231247(); 	wait( 60); 	set_val(DA231451, 100); 	DA231245(); 	wait( 60); 	set_val(DA231451, 100); 	wait( 600); 	wait( 350); 	} combo DA23162 { 	set_val(DA231451,100); 	set_val(DA231450,100); 	if (DA231479) DA231686 = DA231593 - 2; 	else DA231686 = DA231593 + 2; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	wait(50); 	set_val(DA231450,100); 	set_val(DA231451,100); 	if (DA231479) DA231686 = DA231593 - 3; 	else DA231686 = DA231593 + 3; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	wait(50); 	set_val(DA231450,100); 	set_val(DA231451,100); 	if (DA231479) DA231686 = DA231593 - 4; 	else DA231686 = DA231593 + 4; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	wait(50); 	set_val(DA231450,100); 	set_val(DA231451,100); 	if (DA231479) DA231686 = DA231593 - 5; 	else DA231686 = DA231593 + 5; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	set_val(DA231451,100); 	set_val(DA231450,100); 	wait(50); 	set_val(DA231450,100); 	set_val(DA231451,100); 	if (DA231479) DA231686 = DA231593 - 6; 	else DA231686 = DA231593 + 6; 	DA231234(DA231686); 	DA231236(DA2311153, DA231672); 	wait(50); 	} combo DA23163 { 	wait( 100); 	DA231239(0, 0); 	DA231245(); 	wait( 70); 	DA231239(0, 0); 	DA231247()   	wait( 70); 	DA231239(0, 0); 	DA231245()   	wait( 70); 	DA231239(0, 0); 	DA231247()   	wait( 70); 	DA231239(0, 0); 	DA231246(); 	wait( 70); 	DA231239(0, 0); 	wait( 350); 	} combo DA23164 { 	set_val(PS4_R3,100); 	if (DA231479) DA231686 = DA231593 + 1; 	else DA231686 = DA231593 - 1; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	DA231239(DA2311153, DA231672); 	wait( 70); 	DA231239(DA2311153, DA231672); 	wait( 400); 	wait( 350); 	} combo DA23165 { 	call(DA23183); 	DA231239(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (DA231479) DA231686 = DA231593 + 1; 	else DA231686 = DA231593 - 1; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	DA231239(DA2311153, DA231672); 	wait( 70); 	DA231239(DA2311153, DA231672); 	wait( 400); 	wait( 350); 	} combo DA23166 { 	call(DA23183); 	DA231239(0,0); 	set_val(DA231451,100); 	set_val(DA231452,100); 	wait( 750); 	} combo DA23167 { 	set_val(PS4_R3,100); 	if (DA231479) DA231686 = DA231593 + 2; 	else DA231686 = DA231593 - 2; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	DA231239(DA2311153, DA231672); 	wait( 70); 	DA231239(DA2311153, DA231672); 	wait( 400); 	wait( 350); 	} combo DA23168 { 	set_val(DA231451,100); 	set_val(PS4_R3,100); 	if (DA231479) DA231686 = DA231593 ; 	else DA231686 = DA231593; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	DA231239(DA2311153, DA231672); 	wait( 70); 	set_val(DA231451,100); 	DA231239(DA2311153, DA231672); 	wait( 400); 	wait( 350); 	} combo DA23169 { 	call(DA23183); 	set_val(DA231451,100); 	set_val(PS4_R3,100); 	if (DA231479) DA231686 = DA231593 ; 	else DA231686 = DA231593; 	DA231234(DA231686); 	DA231239(DA2311153, DA231672); 	DA231239(DA2311153, DA231672); 	wait( 70); 	set_val(DA231451,100); 	DA231239(DA2311153, DA231672); 	wait( 400); 	wait( 350); 	} combo DA23170 { 	DA231239(0,0); 	set_val(DA231450,100); 	set_val(DA231449,100); 	DA231243(); 	wait( 350); 	wait( 350); 	set_val(DA231450,100); 	set_val(DA231449,100); 	wait( 400); 	} int DA231141 ; int DA231763 ; int DA231764 ; int DA231765; int DA231766; function DA231127(DA231128){ 	DA231763 = 2; 	DA231764 = 987654; 	DA231141 = 54321; 	DA231765 = (DA231128 >> DA231763) | (DA231128 << (32 - DA231763)); 	DA231766 = (((DA231765 >> ((DA231765 & 0xF) % 13)) & 0x7FFFF) + DA231141) % DA231764 + 123456; 	return DA231766; 	} define DA231768 = -1; define DA231536 = -2; define DA231770 = -3; define DA231771 = 0; define DA231537 = 1; function DA231129(DA231128, DA231131, DA231132) { 	if(DA231128 > DA231132) return DA231131; 	if(DA231128 < DA231131) return DA231132; 	return DA231128; 	} int DA231775,DA231776; function DA231133(DA231134,DA231135,DA231136,DA231137,DA231138,DA231139){ 	if(!DA231139){ 		print(DA231142(DA231140(DA231134),DA231137,DA231135),DA231136,DA231137,DA231138,DA231134)     	} 	else{ 		if(DA231134 < 0){ 			putc_oled(1,45); 					} 		if(DA231134){ 			for(DA231775 = DA231146(DA231134) + DA231776 = (DA231134 < 0 ),DA231134 = abs(DA231134); 			DA231134 > 0; 			DA231775-- , DA231776++){ 				putc_oled(DA231775,DA231134%10 + 48); 				DA231134 = DA231134/10; 							} 					} 		else{ 			putc_oled(1,48); 			DA231776 = 1         		} 		puts_oled(DA231142(DA231776,DA231137,DA231135),DA231136,DA231137,DA231776 ,DA231138); 			} 	} int DA231797; function DA231140(DA231141) { 	DA231797 = 0; 	do { 		DA231141++; 		DA231797++; 			} 	while (duint8(DA231141)); 	return DA231797; 	} function DA231142(DA231143,DA231137,DA231135) { 	if(DA231135 == -3){ 		return 128 - ((DA231143 * (7 + (DA231137 > 1) + DA231137 * 4)) + 3 ); 			} 	if(DA231135 == -2){ 		return 64 - ((DA231143 * (7 + (DA231137 > 1) + DA231137 * 4)) / 2); 			} 	if(DA231135 == -1){ 		return 3 	} 	return DA231135; 	} function DA231146(DA231147) { 	for(DA231775 = 1; 	DA231775 < 11; 	DA231775++){ 		if(!(abs(DA231147) / pow(10,DA231775))){ 			return DA231775; 			break; 					} 			} 	return 1; 	} function DA231148() { 	if (get_ival(DA231447)) { 		set_val(DA231447, 0); 		if (get_ival(DA231449)) DA231804 = 50; 		if (!get_ival(DA231449)) DA231804 = 410; 		combo_run(DA23171); 			} 	if (DA231803 > 0) set_polar(POLAR_LS, DA231803 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) DA231803 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) DA231803 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) DA231803 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) DA231803 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA231803 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DA231803 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA231803 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DA231803 = 270; } int DA231804; int DA231557; int DA231803; combo DA23171 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait( DA231804); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	DA231557 = !DA231557; } define DA231807 = 19; function DA231149(DA231150, DA231151) { 	if (DA231354 == DA231151) { 		if (event_press(PS4_RIGHT)) { 			DA231150 = clamp(DA231150 + 1, 0, DA231810[DA231354]); 			DA231359 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			DA231150 = clamp(DA231150 - 1, 0, DA231810[DA231354]); 			DA231359 = TRUE; 					} 		if (DA231354 == 0) { 			print(DA231214(DA231167(DA231814[DA231360]) ,OLED_FONT_SMALL_WIDTH),DA231807  ,OLED_FONT_SMALL , OLED_WHITE ,DA231814[DA231360]); 					} 		else if (DA231354 == 1) { 			print(DA231214(DA231167(DA231816[DA231361]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231816[DA231361]); 					} 		else if (DA231354 == 2) { 			print(DA231214(DA231167(DA231816[DA231362]) ,OLED_FONT_SMALL_WIDTH ),DA231807  ,OLED_FONT_SMALL , OLED_WHITE ,DA231816[DA231362]); 					} 		else if (DA231354 == 5) { 			print(DA231214(DA231167(DA231820[DA231365]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231820[DA231365]); 					} 		else if (DA231354 == 6) { 			print(DA231214(DA231167(DA231822[DA231366]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231366]); 					} 		else if (DA231354 == 7) { 			print(DA231214(DA231167(DA231822[DA231367]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231367]); 					} 		else if (DA231354 == 8) { 			print(DA231214(DA231167(DA231822[DA231368]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231368]); 					} 		else if (DA231354 == 9) { 			print(DA231214(DA231167(DA231822[DA231369]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231369]); 					} 		else if (DA231354 == 20) { 			print(DA231214(DA231167(DA231830[DA231114]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231830[DA231114]); 					} 		else { 			if (DA231150 == 1)        print(DA231214(DA231167(DA231832[1]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231832[1])      else        print(DA231214(DA231167(DA231832[0]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231832[0])     		} 			} 	return DA231150; 	} function DA231152(DA231150, DA231151) { 	if (DA231355 == DA231151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA231150 += DA231838[DA231355][2]  				        DA231359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA231150 -= DA231838[DA231355][2]  				        DA231359 = TRUE; 							} 			DA231150 = clamp(DA231150, DA231838[DA231355][0], DA231838[DA231355][1]); 		} 		if (DA231355 == 8) { 			print(DA231214(DA231167(DA231822[DA231386]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231386])     		} 		else if (DA231355 == 9) { 			print(DA231214(DA231167(DA231822[DA231387]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231387])     		} 		else if (DA231355 == 10) { 			print(DA231214(DA231167(DA231822[DA231388]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231388])     		} 		else if (DA231355 == 11) { 			print(DA231214(DA231167(DA231822[DA231389]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231389])     		} 		else if (DA231355 == 12) { 			print(DA231214(DA231167(DA231822[DA231390]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231390])     		} 		else if (DA231355 == 13) { 			print(DA231214(DA231167(DA231822[DA231391]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231391])     		} 		else if (DA231355 == 14) { 			print(DA231214(DA231167(DA231822[DA231392]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231392])     		} 		else if (DA231355 == 15) { 			print(DA231214(DA231167(DA231822[DA231393]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231393])     		} 		else if (DA231355 == 16) { 			print(DA231214(DA231167(DA231857[DA231394]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231394])     		} 		else if (DA231355 == 17) { 			print(DA231214(DA231167(DA231822[DA231275]),OLED_FONT_SMALL_WIDTH ),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231822[DA231275])  		} 		else if(DA231355 == 18){ 			print(DA231214(DA231167(DA231822[DA231276]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231822[DA231276])  		} 		else if(DA231355 == 19){ 			print(DA231214(DA231167(DA231822[DA231277]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231822[DA231277])  		} 		else if(DA231355 == 20){ 			print(DA231214(DA231167(DA231822[DA231278]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231822[DA231278])  		} 		else if(DA231355 == 21){ 			print(DA231214(DA231167(DA231857[DA231279]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231857[DA231279])       		} 		else if(DA231355 == 22){ 			print(DA231214(DA231167(DA231822[DA231407]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231407])     		} 		else if (DA231355 == 23) { 			print(DA231214(DA231167(DA231822[DA231408]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231408])     		} 		else if (DA231355 == 24) { 			print(DA231214(DA231167(DA231822[DA231409]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231409])     		} 		else if (DA231355 == 25) { 			print(DA231214(DA231167(DA231822[DA231410]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231410])     		} 		else if (DA231355 == 26) { 			print(DA231214(DA231167(DA231822[DA231411]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231822[DA231411])     		} 		else if (DA231355 == 27) { 			print(DA231214(DA231167(DA231857[DA231412]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231412])     		} 		else if (DA231355 == 28) { 			print(DA231214(DA231167(DA231881[DA231413]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231881[DA231413])     		} 		else if (DA231355 == 29) { 			print(DA231214(DA231167(DA231883[DA231414]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231883[DA231414])     		} 		else if (DA231355 == 30) { 			print(DA231214(DA231167(DA231857[DA231415]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231415])     		} 		else if (DA231355 == 31) { 			print(DA231214(DA231167(DA231881[DA231416]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231881[DA231416])     		} 		else if (DA231355 == 32) { 			print(DA231214(DA231167(DA231883[DA231417]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231883[DA231417])     		} 		else if (DA231355 == 33) { 			print(DA231214(DA231167(DA231857[DA231418]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231418])     		} 		else if (DA231355 == 34) { 			print(DA231214(DA231167(DA231881[DA231419]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231881[DA231419])     		} 		else if (DA231355 == 35) { 			print(DA231214(DA231167(DA231883[DA231420]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231883[DA231420])     		} 		else if (DA231355 == 36) { 			print(DA231214(DA231167(DA231857[DA231421]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231421])     		} 		else if (DA231355 == 37) { 			print(DA231214(DA231167(DA231881[DA231422]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231881[DA231422])     		} 		else if (DA231355 == 38) { 			print(DA231214(DA231167(DA231883[DA231423]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231883[DA231423])     		} 		else if (DA231355 == 41) { 			print(DA231214(DA231167(DA231857[DA231426]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231426])     		} 		else if (DA231355 == 48) { 			print(DA231214(DA231167(DA231857[DA231433]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231433])     		} 		else if (DA231355 == 49) { 			print(DA231214(DA231167(DA231857[DA231434]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231434])     		} 		else if (DA231355 == 50) { 			print(DA231214(DA231167(DA231857[DA231435]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231435])     		} 		else if (DA231355 == 51) { 			print(DA231214(DA231167(DA231857[DA231436]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231857[DA231436])     		} 		else if(DA231355 == 0){ 			print(DA231214(DA231167(DA231913[DA231439]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231439])  		} 		else if(DA231355 == 1){ 			print(DA231214(DA231167(DA231913[DA231440]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231440])  		} 		else if(DA231355 == 2){ 			print(DA231214(DA231167(DA231913[DA231441]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231441])  		} 		else if(DA231355 == 3){ 			print(DA231214(DA231167(DA231913[DA231442]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231442])  		} 		else if(DA231355 == 4){ 			print(DA231214(DA231167(DA231913[DA231443]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231443])  		} 		else if(DA231355 == 5){ 			print(DA231214(DA231167(DA231913[DA231444]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231444])  		} 		else if(DA231355 == 6){ 			print(DA231214(DA231167(DA231913[DA231445]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231445])  		} 		else if(DA231355 == 7){ 			print(DA231214(DA231167(DA231913[DA231446]),OLED_FONT_SMALL_WIDTH),DA231807,OLED_FONT_SMALL,OLED_WHITE,DA231913[DA231446])  		} 		else{ 			if (DA231150 == 1)        print(DA231214(DA231167(DA231832[1]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231832[1])      else        print(DA231214(DA231167(DA231832[0]), OLED_FONT_SMALL_WIDTH), DA231807, OLED_FONT_SMALL, OLED_WHITE, DA231832[0])     		} 		DA231170(0); 			} 	return DA231150; 	} function DA231155(DA231150, DA231151) { 	if (DA231355 == DA231151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DA231150 += DA231838[DA231355][2]  				        DA231359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DA231150 -= DA231838[DA231355][2]  				        DA231359 = TRUE; 							} 			if (event_press(PS4_UP)) { 				DA231150 += DA231838[DA231355][3]  				        DA231359 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				DA231150 -= DA231838[DA231355][3]  				        DA231359 = TRUE; 							} 			DA231150 = clamp(DA231150, DA231838[DA231355][0], DA231838[DA231355][1]); 		} 		DA231217(DA231150, DA231220(DA231150)); 	} 	return DA231150; 	} int DA231940, DA231941, DA231942; function DA231158(DA231128, DA231160, DA231161, DA231162, DA231137) { 	DA231941 = 1; 	DA231942 = 10000; 	if (DA231128 < 0)  	  { 		putc_oled(DA231941, 45); 		DA231941 += 1; 		DA231128 = abs(DA231128); 			} 	for (DA231940 = 5; 	DA231940 >= 1; 	DA231940--) { 		if (DA231160 >= DA231940) { 			putc_oled(DA231941, DA231948[DA231128 / DA231942]); 			DA231128 = DA231128 % DA231942; 			DA231941 += 1; 					} 		DA231942 /= 10; 			} 	puts_oled(DA231161, DA231162, DA231137, DA231941 - 1, OLED_WHITE); } const string DA231574 = " No Edit Variable"; const string DA231573 = " A/CROSS to Edit "; const string DA231569 = "MOD;"; const string DA231571 = "MSL;"; int DA231952; function DA231164(DA231147) { 	DA231147 = abs(DA231147); 	if (DA231147 / 10000 > 0) return 5; 	if (DA231147 / 1000 > 0) return 4; 	if (DA231147 / 100 > 0) return 3; 	if (DA231147 / 10 > 0) return 2; 	return 1; 	} const int8 DA231948[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int DA231954, DA231955; const image DA231957 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DA23172 { 	call(DA23173); 	DA231166(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, DA231957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DA231957[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(DA23174); 	vm_tctrl(0);wait( 1000); 	DA231356 = TRUE; 	} combo DA23173 { 	cls_oled(OLED_BLACK); 	} int DA231959; enum { 	DA231960 = -2, DA231961, DA231962 = 5, DA231963 = -1, DA231964 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo DA23174 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 DA2311391[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DA2311392[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DA2311393[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 DA2311394[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int DA231965; int DA231966; int DA231967; int DA231968; int DA231969; int DA231970; int DA231971; function DA231166() { 	DA231971 = 3; 	DA231969 = DA231971 * DA2311394[DA231970]; 	DA231968 = DA231971 * DA2311391[DA231970]; 	DA231966 = ((DA231969 * DA2311393[DA231965]) / 328) - ((DA231968 * DA2311392[DA231965]) / 328); 	DA231967 = ((DA231969 * DA2311392[DA231965]) / 328) + ((DA231968 * DA2311393[DA231965]) / 328); 	DA231969 = DA231966; 	DA231968 = DA231967; 	DA231970 += 1; 	DA231965 += 45; 	if(DA231970 >= 360) { 		DA231970 %= 360; 			} 	if(DA231965 >= 360) { 		DA231965 %= 360; 			} 	pixel_oled(64 + (((DA231969 / DA231971) * 30) / 328), 32 + (((DA231968 / DA231971) * 30) / 328), OLED_WHITE); 	} int DA231975; function DA231167(DA231141) { 	DA231975 = 0; 	do { 		DA231141++; 		DA231975++; 			} 	while (duint8(DA231141)); 	return DA231975; 	} int DA231978; const uint8 DA2311395[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function DA231169() { 	for (DA231978 = 0; 	DA231978 < sizeof(DA2311395) / sizeof(DA2311395[0]); 	DA231978++) { 		if (get_ival(DA2311395[DA231978]) || event_press(DA2311395[DA231978])) { 			set_val(DA2311395[DA231978], 0); 		} 			} 	} define DA231979 = 131; define DA231980 = 132; define DA231981 = 133; define DA231982 = 134; define DA231983 = 130; define DA231984 = 89; define DA231985 = 127; define DA231986 = 65; int DA231987; int DA231988; int DA231989 = 1; define DA231990 = 36; const string DA231991 = "Hold LT/L2 +"; function DA231170(DA231171) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, DA231991[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, DA231989); 	putc_oled(1, DA231981); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DA231987); 	putc_oled(1, DA231982); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DA231988); 	if (DA231171) { 		putc_oled(1, DA231979); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DA231987); 		putc_oled(1, DA231980); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DA231988); 			} 	} const uint8 DA2311397 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function DA231172() { 	DA2311001 = sizeof(data); 	DA231508 = get_pvar(SPVAR_1,0,1,0); 	DA231514 = get_pvar(SPVAR_2,0,1,0); 	DA231507 = get_pvar(SPVAR_3,11111, 99999,11111); 	DA231174(); 	if (DA231199(0, 1, 0)) { 		DA231365 = DA231199(  0, 6, 0); 		DA231362 = DA231199(0, 3, 0); 		DA231363 = DA231199(0,1,0); 		DA231364 = DA231199(0,1,0); 		DA231275 = DA231199(0, 70, 0); 		DA231276 = DA231199(0, 70, 0); 		DA231277 = DA231199(0, 70, 0); 		DA231278 = DA231199(0, 70, 0); 		DA231279 = DA231199(0, 22, 8); 		DA231366 = DA231199(0, 70, 0); 		DA231367 = DA231199(0, 70, 0); 		DA231368 = DA231199(0, 70, 0); 		DA231369 = DA231199(0, 70, 0); 		DA231370 = DA231199(0, 1, 0); 		DA231371 = DA231199(0, 1, 0); 		DA231372 = DA231199(0, 1, 0); 		DA231373 = DA231199(0, 1, 0); 		DA231381 = DA231199(0, 1, 0); 		DA231407 = DA231199(0, 70, 0); 		DA231408 = DA231199(0, 70, 0); 		DA231409 = DA231199(0, 70, 0); 		DA231410 = DA231199(0, 70, 0); 		DA231411 = DA231199(0, 70, 0); 		DA231412 = DA231199(1, 25, 1); 		DA231413 = DA231199(0, 1, 0); 		DA231414 = DA231199(0, 1, 0); 		DA231415 = DA231199(1, 25, 5); 		DA231416 = DA231199(0, 1, 0); 		DA231417 = DA231199(0, 1, 0); 		DA231418 = DA231199(0, 25, 2); 		DA231419 = DA231199(0, 1, 0); 		DA231420 = DA231199(0, 1, 1); 		DA231421 = DA231199(1, 25, 8); 		DA231422 = DA231199(0, 1, 0); 		DA231423 = DA231199(0, 1, 1); 		DA231424 = DA231199(350, 600, 350); 		DA231425 = DA231199(350, 600, 445); 		DA231426 = DA231199(0, 22, 0); 		DA231427 = DA231199(0, 1, 0); 		DA231428 = DA231199(-100, 300, 0); 		DA231374 = DA231199(0, 1, 0); 		DA231375 = DA231199(0, 1, 0); 		DA231376 = DA231199(0, 1, 0); 		DA231377 = DA231199(0, 1, 0); 		DA231378 = DA231199(0, 1, 0); 		DA231429 = DA231199(-150, 150, 0); 		DA231430 = DA231199(-150, 150, 0); 		DA231431 = DA231199(0, 1, 0); 		DA231432 = DA231199(-150, 150, 0); 		DA231433 = DA231199(0, 22, 0); 		DA231434 = DA231199(0, 22, 0); 		DA231435 = DA231199(0, 22, 0); 		DA231436 = DA231199(0, 22, 0); 		DA231608 = DA231199(60, 400, 235); 		DA231438 = DA231199(0, 1, 0); 		DA231437 = DA231199(0, 1, 0); 		DA231361 = DA231199(0, 3, 0); 		DA231386 = DA231199(0, 70, 0); 		DA231387 = DA231199(0, 70, 0); 		DA231388 = DA231199(0, 70, 0); 		DA231391 = DA231199(0, 70, 0); 		DA231392 = DA231199(0, 70, 0); 		DA231393 = DA231199(0, 70, 0); 		DA231394 = DA231199(0, 22, 8); 		DA231379 = DA231199(0, 1, 0); 		DA231389 = DA231199(0, 70, 0); 		DA231390 = DA231199(0, 70, 0); 		DA231600 = DA231199(0, 2500, 1100); 		DA2311250 = DA231199(0, 1, 0); 		DA2311243 = DA231199(0, 1, 0); 		DA231114 = DA231199(0, 10, 0); 		DA231406 = DA231199(0, 1, 0); 		DA231360 = DA231199(0, 2, 0); 		DA231439 = DA231199(0, 9, 9); 		DA231440 = DA231199(0, 9, 8); 		DA231441 = DA231199(0, 9, 3); 		DA231442 = DA231199(0, 9, 1); 		DA231443 = DA231199(0, 9, 4); 		DA231444 = DA231199(0, 9, 0); 		DA231445 = DA231199(0, 9, 7); 		DA231446 = DA231199(0, 9, 6); 		DA231382    = DA231199(0, 1, 0); 		DA231383    = DA231199(0, 1, 0); 		DA231384     = DA231199(0, 1, 0); 		DA231395     = DA231199(60, 500, 120); 		DA231396     = DA231199(60, 500, 350); 		DA231397    = DA231199(0, 1, 0); 		DA231398 = DA231199(0, 1, 0); 		DA231399     = DA231199(50, 250, 80); 		DA231400     = DA231199(100, 850, 180); 		DA231401 = DA231199(0, 1, 0); 		DA231402    = DA231199(0, 1, 0); 		DA231403        = DA231199(80, 500, 120); 		DA231404        = DA231199(80, 500, 350); 		DA231405       = DA231199(0, 1, 0); 		DA231455           = DA231199(13, 30, 13); 		DA23129           = DA231199(0, 1, 0); 		DA231476         = DA231199(0, 1, 0); 		DA231474       = DA231199(0, 1, 0); 	} 	else{ 		DA231365 = 0; 		DA231362 = 0; 		DA231363 = 0; 		DA231364 = 0; 		DA231275 = 0; 		DA231276 = 0; 		DA231277 = 0; 		DA231278 = 0; 		DA231279 = 8; 		DA231366 = 0; 		DA231367 = 0; 		DA231368 = 0; 		DA231369 = 0; 		DA231370 = 0; 		DA231371 = 0; 		DA231372 = 0; 		DA231373 = 0; 		DA231381 = 0; 		DA231407 = 0; 		DA231408 = 0; 		DA231409 = 0; 		DA231410 = 0; 		DA231411 = 0; 		DA231412 = 1; 		DA231413 = 0; 		DA231414 = 0; 		DA231415 = 5; 		DA231416 = 0; 		DA231417 = 0; 		DA231418 = 2; 		DA231419 = 0; 		DA231420 = 1; 		DA231421 = 8; 		DA231422 = 0; 		DA231423 = 1; 		DA231424 = 350; 		DA231425 = 445; 		DA231426 = 0; 		DA231427 = 0; 		DA231428 = 0; 		DA231374 = 0; 		DA231375 = 0; 		DA231376 = 0; 		DA231377 = 0; 		DA231378 = 0; 		DA231429 = 0; 		DA231430 = 0; 		DA231431 = 0; 		DA231432 = 0; 		DA231433 = 0; 		DA231434 = 0; 		DA231435 = 0; 		DA231436 = 0; 		DA231608 = 235; 		DA231438 = 0; 		DA231437 = 0; 		DA231361 = 0; 		DA231386 = 0; 		DA231387 = 0; 		DA231388 = 0; 		DA231391 = 0; 		DA231392 = 0; 		DA231393 = 0; 		DA231394 = 8; 		DA231379 = 0; 		DA231389 = 0; 		DA231390 = 0; 		DA231600 = 1100; 		DA2311250 = 0; 		DA2311243 = 0; 		DA231114 = 0; 		DA231406 = 0; 		DA231360 = 0; 		DA231439 = 9; 		DA231440 = 8; 		DA231441 = 3; 		DA231442 = 1; 		DA231443 = 4; 		DA231444 = 0; 		DA231445 = 7; 		DA231446 = 6; 		DA231382 = 0; 		DA231383 = 0; 		DA231384 = 0; 		DA231395 = 120; 		DA231396 = 350; 		DA231397 = 0; 		DA231398 = 0; 		DA231399 = 80; 		DA231400 = 180; 		DA231401 = 0; 		DA231402 = 0; 		DA231403 = 120; 		DA231404 = 360; 		DA231405 = 0; 		DA231455     = 13; 		DA23129     = 0; 		DA231476     = 0; 		DA231474     = 0; 			} 	if (DA231360 == 0) { 		DA231447 = PS4_CIRCLE; 		DA231448 = PS4_CROSS; 		DA231449 = PS4_L1; 		DA231450 = PS4_R1; 		DA231451 = PS4_L2; 		DA231452 = PS4_R2; 		DA231453 = PS4_SQUARE; 		DA231454 = PS4_TRIANGLE; 			} 	else if (DA231360 == 1) { 		DA231447      = PS4_SQUARE; 		DA231448      = PS4_CROSS ; 		DA231449    = PS4_L1    ; 		DA231450  = PS4_R1; 		DA231451    = PS4_L2; 		DA231452    = PS4_R2; 		DA231453     = PS4_CIRCLE; 		DA231454  = PS4_TRIANGLE; 	} 	else if (DA231360 == 2) { 		DA231447 = DA2311397[DA231439]; 		DA231448 = DA2311397[DA231440]; 		DA231449 = DA2311397[DA231441]; 		DA231450 = DA2311397[DA231442]; 		DA231451 = DA2311397[DA231443]; 		DA231452 = DA2311397[DA231444]; 		DA231453 = DA2311397[DA231445]; 		DA231454 = DA2311397[DA231446]; 			} 	} function DA231173() { 	DA231174(); 	DA231197(   1,0,     1); 	DA231197(DA231365, 0, 6); 	DA231197(DA231362, 0, 3); 	DA231197(DA231363, 0 , 1); 	DA231197(DA231364, 0 , 1); 	DA231197(DA231275, 0, 70); 	DA231197(DA231276, 0, 70); 	DA231197(DA231277, 0, 70); 	DA231197(DA231278, 0, 70); 	DA231197(DA231279, 0, 22); 	DA231197(DA231366, 0, 70); 	DA231197(DA231367, 0, 70); 	DA231197(DA231368, 0, 70); 	DA231197(DA231369, 0, 70); 	DA231197(DA231370, 0, 1); 	DA231197(DA231371, 0, 1); 	DA231197(DA231372, 0, 1); 	DA231197(DA231373, 0, 1); 	DA231197(DA231381, 0, 1); 	DA231197(DA231407, 0, 70); 	DA231197(DA231408, 0, 70); 	DA231197(DA231409, 0, 70); 	DA231197(DA231410, 0, 70); 	DA231197(DA231411, 0, 70); 	DA231197(DA231412, 1, 25); 	DA231197(DA231413, 0, 1); 	DA231197(DA231414, 0, 1); 	DA231197(DA231415, 1, 25); 	DA231197(DA231416, 0, 1); 	DA231197(DA231417, 0, 1); 	DA231197(DA231418, 0, 25); 	DA231197(DA231419, 0, 1); 	DA231197(DA231420, 0, 1); 	DA231197(DA231421, 1, 25); 	DA231197(DA231422, 0, 1); 	DA231197(DA231423, 0, 1); 	DA231197(DA231424, 350, 600); 	DA231197(DA231425, 350, 600); 	DA231197(DA231426, 0, 22); 	DA231197(DA231427, 0, 1); 	DA231197(DA231428, -100, 300); 	DA231197(DA231374, 0, 1); 	DA231197(DA231375, 0, 1); 	DA231197(DA231376, 0, 1); 	DA231197(DA231377, 0, 1); 	DA231197(DA231378, 0, 1); 	DA231197(DA231429, -150, 150); 	DA231197(DA231430, -150, 150); 	DA231197(DA231431, 0, 1); 	DA231197(DA231432, -150, 150); 	DA231197(DA231433, 0, 22); 	DA231197(DA231434, 0, 22); 	DA231197(DA231435, 0, 22); 	DA231197(DA231436, 0, 22); 	DA231197(DA231608, 60, 400); 	DA231197(DA231438, 0, 1); 	DA231197(DA231437, 0, 1); 	DA231197(DA231361, 0, 3); 	DA231197(DA231386, 0, 70); 	DA231197(DA231387, 0, 70); 	DA231197(DA231388, 0, 70); 	DA231197(DA231391, 0, 70); 	DA231197(DA231392, 0, 70); 	DA231197(DA231393, 0, 70); 	DA231197(DA231394, 0, 22); 	DA231197(DA231379, 0, 1); 	DA231197(DA231389, 0, 70); 	DA231197(DA231390, 0, 70); 	DA231197(DA231600, 0, 2500); 	DA231197(DA2311250, 0, 1); 	DA231197(DA2311243, 0, 1); 	DA231197(DA231114, 0, 10); 	DA231197(DA231406, 0, 1); 	DA231197(DA231360, 0, 2); 	DA231197(DA231439, 0, 9); 	DA231197(DA231440, 0, 9); 	DA231197(DA231441, 0, 9); 	DA231197(DA231442, 0, 9); 	DA231197(DA231443, 0, 9); 	DA231197(DA231444, 0, 9); 	DA231197(DA231445, 0, 9); 	DA231197(DA231446, 0, 9); 	DA231197(DA231382,    0, 1); 	DA231197(DA231383,    0, 1); 	DA231197(DA231384,     0, 1); 	DA231197(DA231395,     60, 500); 	DA231197(DA231396,     60, 500); 	DA231197(DA231397,    0, 1); 	DA231197(DA231398, 0, 1); 	DA231197(DA231399,     50, 250); 	DA231197(DA231400,     100, 850); 	DA231197(DA231401, 0, 1); 	DA231197(DA231402,    0, 1); 	DA231197(DA231403,        80, 500); 	DA231197(DA231404,        80, 500); 	DA231197(DA231405,       0, 1); 	DA231197(DA231455 ,         13,30); 	DA231197(DA23129,           0,1); 	DA231197(DA231476,           0,1); 	DA231197(DA231474,           0,1); 	} function DA231174() { 	DA2311008 = SPVAR_4; 	DA2311009 = 0; 	DA2311011 = 0; 	} int DA2311009,  DA2311008, DA2311011, DA2311012, DA2311013; function DA231175(DA231176) { 	DA2311012 = 0; 	while (DA231176) { 		DA2311012++; 		DA231176 = abs(DA231176 >> 1); 	} 	return DA2311012; 	} function DA231177(DA231178, DA231179) { 	DA2311012 = max(DA231175(DA231178), DA231175(DA231179)); 	if (DA231180(DA231178, DA231179)) { 		DA2311012++; 	} 	return DA2311012; 	} function DA231180(DA231178, DA231179) { 	return DA231178 < 0 || DA231179 < 0; 	} function DA231183(DA231184) { 	return 1 << clamp(DA231184 - 1, 0, 31); 	} function DA231185(DA231184) { 	if (DA231184 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - DA231184); } function DA231187(DA231184) { 	return DA231185(DA231184 - 1); 	} function DA231189(DA231176, DA231184) { 	if (DA231176 < 0) { 		return (abs(DA231176) & DA231187(DA231184)) | DA231183(DA231184); 	} 	return DA231176 & DA231187(DA231184); } function DA231192(DA231176, DA231184) { 	if (DA231176 & DA231183(DA231184)) { 		return 0 - (DA231176 & DA231187(DA231184)); 	} 	return DA231176 & DA231187(DA231184); } function DA231195(DA231196) { 	return get_pvar(DA231196, 0x80000000, 0x7FFFFFFF, 0); 	} function DA231197(DA231176, min, max) { 	DA2311013 = DA231177(min, max); 	DA231176 = clamp(DA231176, min, max); 	if (DA231180(min, max)) { 		DA231176 = DA231189(DA231176, DA2311013); 	} 	DA231176 = DA231176 & DA231185(DA2311013); 	if (DA2311013 >= 32 - DA2311009) { 		DA2311011 = DA2311011 | (DA231176 << DA2311009); 		set_pvar(DA2311008, DA2311011); 		DA2311008++; 		DA2311013 -= (32 - DA2311009); 		DA231176 = DA231176 >> (32 - DA2311009); 		DA2311009 = 0; 		DA2311011 = 0; 	} 	DA2311011 = DA2311011 | (DA231176 << DA2311009); 	DA2311009 += DA2311013; 	if (!DA2311009) { 		DA2311011 = 0; 	} 	set_pvar(DA2311008, DA2311011); } function DA231199(min, max, DA231200) { 	DA2311013 = DA231177(min, max); 	DA2311011 = (DA231195(DA2311008) >> DA2311009) & DA231185(DA2311013); 	if (DA2311013 >= 32 - DA2311009) { 		DA2311011 = (DA2311011 & DA231185(32 - DA2311009)) | ((DA231195(DA2311008 + 1) & DA231185(DA2311013 - (32 - DA2311009))) << (32 - DA2311009)); 	} 	DA2311009 += DA2311013; 	DA2311011 = DA2311011 & DA231185(DA2311013); 	if (DA2311009 >= 32) { 		DA2311008++; 		DA2311009 -= 32; 	} 	if (DA231180(min, max)) { 		DA2311011 = DA231192(DA2311011, DA2311013); 	} 	if (DA2311011 < min || DA2311011 > max) { 		return DA231200; 	} 		if(DA231202[289] != 7591){     DA231199(min, max, DA231200); 	} 	return DA2311011; 	} const string DA2311039 = "SETTINGS"; const string DA2311040 = "WAS SAVED"; combo DA23175 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	DA231173(); 	print(15, 2, OLED_FONT_MEDIUM, 1, DA2311039[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, DA2311040[0]); 	DA2311041 = 1500; 	combo_run(DA23176); 	} int DA2311041 = 1500; combo DA23176 { 	vm_tctrl(0);wait( DA2311041); 	cls_oled(0); 	DA231358 = FALSE; 	} define DA2311042 = 0; define DA2311043 = 1; define DA2311044 = 2; define DA2311045 = 3; define DA2311046 = 4; define DA2311047 = 5; define DA2311048 = 6; define DA2311049 = 7; int DA231686; int DA2311051; int DA2311052, DA231669; int DA231479; int DA2311055 = 49; int DA2311056 = 200; int DA231717 = TRUE; combo DA23177 { 	set_val(DA231448, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA231448, 0); 	vm_tctrl(0);wait( 120); 	if (DA231427) DA231245(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int DA231615; int DA231622; combo DA23178 { 	if (DA231622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (DA231622) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (DA231622) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(DA231447, 100); 	vm_tctrl(0);wait( DA231424); 	if (DA231622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	DA231615 = FALSE; 	DA231229(DA231615); 	} int DA231624; int DA231625; combo DA23179 { 	if (DA231625) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (DA231625) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(DA231453, 100); 	set_val(DA231450, 100); 	set_val(DA231451, 100); 	DA2311197 = 4000; 	vm_tctrl(0);wait( DA231425); 	vm_tctrl(0);wait( 50); 	set_val(DA231453, 100); 	vm_tctrl(0);wait( 50); 	DA231624 = FALSE; 	DA231229(DA231624); 	} int DA2311062 = TRUE; function DA231201(DA231202) { 	if (DA231202) { 		DA2311063 = DA2311095; 			} 	else { 		DA2311063 = DA2311094; 			} 	combo_run(DA23180); 	} int DA2311063; combo DA23180 { 	DA231222(DA2311063); 	vm_tctrl(0);wait( 300); 	DA231222(DA2311092); 	vm_tctrl(0);wait( 100); 	DA231222(DA2311063); 	vm_tctrl(0);wait( 300); 	DA231222(DA2311092); 	} define DA2311067 = 100; define DA2311068 = 130; const string DA231552 = "SCRIPT WAS"; function DA231203(DA231128, DA231205, DA231206) { 	if (!DA231352 && !DA231353) { 		cls_oled(0); 		print(DA231205, 3, OLED_FONT_MEDIUM, OLED_WHITE, DA231206); 		if (DA231128) { 			print(DA231207(sizeof(DA2311072) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA2311072[0]); 		} 		else { 			print(DA231207(sizeof(DA2311073) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DA2311073[0]); 		} 		DA231201(DA231128); 			} 	} function DA231207(DA231143, DA231137) { 	return (OLED_WIDTH / 2) - ((DA231143 * DA231137) / 2); 	} const string DA2311073 = "OFF"; const string DA2311072 = "ON"; function DA231210(DA231134, DA231212, DA231128) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(DA231134, 0, OLED_FONT_MEDIUM, OLED_WHITE, DA231212); 	DA231217(DA231128, DA231220(DA231128)); 	DA231356 = TRUE; 	} const string DA231595 = "EA PING"; const string DA231617 = "FK_POWER"; const string DA231609 = "MaxFnshPwr"const string DA231601 = "JK_Agg"; int DA231600; int DA231608; function DA231214(DA231143, DA231137) { 	return (OLED_WIDTH / 2) - ((DA231143 * DA231137) / 2); 	} int DA2311082; int DA2311083, DA2311084; function DA231217(DA231128, DA231160) { 	DA2311082 = 1; 	DA2311084 = 10000; 	if (DA231128 < 0) { 		putc_oled(DA2311082, 45); 		DA2311082 += 1; 		DA231128 = abs(DA231128); 			} 	for (DA2311083 = 5; 	DA2311083 >= 1; 	DA2311083--) { 		if (DA231160 >= DA2311083) { 			putc_oled(DA2311082, (DA231128 / DA2311084) + 48); 			DA231128 %= DA2311084; 			DA2311082++; 			if (DA2311083 == 4) { 				putc_oled(DA2311082, 44); 				DA2311082++; 							} 					} 		DA2311084 /= 10; 			} 	puts_oled(DA231214(DA2311082 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DA2311082 - 1, OLED_WHITE); 	} int DA2311088; function DA231220(DA231221) { 	DA2311088 = 0; 	do { 		DA231221 /= 10; 		DA2311088++; 			} 	while (DA231221); 	return DA2311088; 	} int DA231631; define DA2311092 = 0; define DA2311093 = 1; define DA2311094 = 2; define DA2311095 = 3; define DA2311096 = 4; define DA2311097 = 5; define DA2311098 = 6; define DA2311099 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int DA2311100; function DA231222(DA231223) { 	for (DA2311100 = 0; 	DA2311100 < 3; 	DA2311100++) { 		set_rgb(data[DA231223][0], data[DA231223][1], data[DA231223][2]); 			} 	} const int8 DA2311407[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int DA231634 = PS4_L3; define DA2311102 = 1; define DA2311103 = 2; define DA2311104 = 3; define DA2311105 = 2; define DA2311106 = 3; define DA2311107 = 4; define DA2311108 = 5; define DA2311109 = 6; define DA2311110 = 7; define DA2311111 = 8; define DA2311112 = 9; int DA231628 = FALSE; int DA2311114; int DA2311115; int DA2311116; int DA2311117; define DA2311118 = PS4_LX; define DA2311119 = PS4_LY; define DA2311120 = PS4_RX; define DA2311121 = PS4_RY; function DA231224 () { if(DA231362 == 3){ 		if( get_ival(PS4_RY) < -70  && !DA2311114 && !combo_running(DA2310) ) { 			DA2311114 = TRUE; 			DA231479 = FALSE; 			DA2311051 = DA231275; 			            DA231227(DA231275); 		} 		if( get_ival(PS4_RY) >  70  && !DA2311115 && !combo_running(DA2310)) { 			DA2311115 = TRUE; 			DA231479 = TRUE; 			DA2311051 = DA231277; 			           DA231227(DA231277); 		} 		if( get_ival(PS4_RX) < -70  && !DA2311116 && !combo_running(DA2310) ) { 			DA2311116 = TRUE; 			DA231479 = FALSE; 			DA2311051 = DA231278; 			              DA231227(DA231278); 		} 		if( get_ival(PS4_RX) >  70  && !DA2311117 && !combo_running(DA2310) ) { 			DA2311117 = TRUE; 			DA231479 = TRUE; 			DA2311051 = DA231276; 			            DA231227(DA231276); 		} 			set_val(DA2311120,0);              set_val(DA2311121,0);  			} 	else if(DA231362 < 3 && !get_ival(XB1_RS) &&  !get_ival(DA231451) && !get_ival(DA231452) && !get_ival(DA231450)) { 		if( get_ival(PS4_RY) < -70  && !DA2311114 && !combo_running(DA2310) ) { 			DA2311114 = TRUE; 			DA231479 = FALSE; 			DA2311051 = DA231275; 			            DA231227(DA231275); 		} 		if( get_ival(PS4_RY) >  70  && !DA2311115 && !combo_running(DA2310)) { 			DA2311115 = TRUE; 			DA231479 = TRUE; 			DA2311051 = DA231277; 			           DA231227(DA231277); 		} 		if( get_ival(PS4_RX) < -70  && !DA2311116 && !combo_running(DA2310) ) { 			DA2311116 = TRUE; 			DA231479 = FALSE; 			DA2311051 = DA231278; 			              DA231227(DA231278); 		} 		if( get_ival(PS4_RX) >  70  && !DA2311117 && !combo_running(DA2310) ) { 			DA2311117 = TRUE; 			DA231479 = TRUE; 			DA2311051 = DA231276; 			            DA231227(DA231276); 		} 			set_val(DA2311120,0);              set_val(DA2311121,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		DA2311114 = 0; 		DA2311115  = 0; 		DA2311116  = 0; 		DA2311117  = 0; 			} 	} function DA231225() { 	if (DA231502 == DA231593) { 		DA231479 = FALSE; 		if (DA231386) DA231227(DA231386); 			} 	if (DA231502 == DA231232(DA231593 + 4)) { 		DA231479 = FALSE; 		if (DA231393) DA231227(DA231393); 			} 	if (DA231502 == DA231232(DA231593 + 1)) { 		DA231479 = TRUE; 		if (DA231388) DA231227(DA231388); 			} 	if (DA231502 == DA231232(DA231593 - 1)) { 		DA231479 = FALSE; 		if (DA231387) DA231227(DA231387); 			} 	if (DA231502 == DA231232(DA231593 + 2)) { 		DA231479 = TRUE; 		if (DA231390) DA231227(DA231390); 			} 	if (DA231502 == DA231232(DA231593 - 2)) { 		DA231479 = FALSE; 		if (DA231389) DA231227(DA231389); 			} 	if (DA231502 == DA231232(DA231593 + 3)) { 		DA231479 = TRUE; 		if (DA231392) DA231227(DA231392); 			} 	if (DA231502 == DA231232(DA231593 - 3)) { 		DA231479 = FALSE; 		if (DA231391) DA231227(DA231391); 			} 	} int DA2311139; int DA231500 = 0; function DA231226() { 	if(DA2311139){ 		DA231500 += get_rtime(); 			} 	if(DA231500 >= 3000){ 		DA231500 = 0; 		DA2311139 = FALSE; 			} 	if(DA231361 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA231503 && !combo_running(DA2310)) { 			DA231503 = TRUE; 			DA2311139 = TRUE; 			DA231500 = 0; 			DA231502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA231225(); 					} 		set_val(DA2311120, 0); 		set_val(DA2311121, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(DA231451) && !get_ival(DA231452) && !get_ival(DA231450) && !get_ival(DA231449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DA231503 && !combo_running(DA2310)) { 			DA231503 = TRUE; 			DA2311139 = TRUE; 			DA231500 = 0; 			DA231502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DA231225(); 					} 		set_val(DA2311120, 0); 		set_val(DA2311121, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		DA231503 = FALSE; 			} 	} function DA231227(DA231228) { 	DA2311051 = DA231228; 	DA231202[-339 + (DA231228 * 3)] = TRUE; 	DA231717 = FALSE; 	block = TRUE; 	if (DA231114 > 7)vm_tctrl(0); 	} int DA2311147; combo DA23181 { 	set_rumble(DA2311147, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function DA231229(DA231128) { 	if (DA231128) DA2311147 = RUMBLE_A; 	else DA2311147 = RUMBLE_B; 	combo_run(DA23181); 	} int DA2311148 = 300; int DA2311149 ; combo DA23182 { 	DA2311149 = TRUE; 	vm_tctrl(0);wait( DA2311148); 	DA2311149 = FALSE; 	} combo DA23183 { 	DA231231(); 	DA231239(0, 0); 	vm_tctrl(0);wait( 20); 	DA231239(0, 0); 	vm_tctrl(0);wait( 100); 	DA231239(0, 0); 	set_val(DA231452, 100); 	DA231239(0, 0); 	vm_tctrl(0);wait( 60); 	DA231239(0, 0); 	vm_tctrl(0);wait( 150); 	DA231717 = TRUE; 	vm_tctrl(0);wait( 350); 	} function DA231231() { 	DA231686 = DA231593  DA231234(DA231686); 	DA2311052 = DA2311153; 	DA231669 = DA231672; 	} combo DA23184 { 	set_val(DA231451, 100); 	set_val(DA231450, 100); 	vm_tctrl(0);wait( 100); 	set_val(DA231451, 100); 	vm_tctrl(0);wait( 100); 	DA231717 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 DA2311408[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int DA2311153, DA231672, DA231593; int DA231502; int DA231503; int DA2311158; function DA231232(DA231233) { 	DA2311158 = DA231233; 	if (DA2311158 < 0) DA2311158 = 8 - abs(DA231233); 	else if (DA2311158 >= 8) DA2311158 = DA231233 - 8  return DA2311158; 	} function DA231234(DA231235) { 	if (DA231235 < 0) DA231235 = 8 - abs(DA231235); 	else if (DA231235 >= 8) DA231235 = DA231235 - 8; 	DA2311153 = DA2311408[DA231235][0]; 	DA231672 = DA2311408[DA231235][1]; } function DA231236(DA231237, DA231238) { 	set_val(DA2311120, DA231237); 	set_val(DA2311121, DA231238); 	} function DA231239(DA231240, DA231241) { 	set_val(DA2311118, DA231240); 	set_val(DA2311119, DA231241); 	} function DA231242() { 	if (DA231479) { 		set_val(DA2311118, inv(DA231669)); 		set_val(DA2311119, DA2311052); 			} 	else { 		set_val(DA2311118, DA231669); 		set_val(DA2311119, inv(DA2311052)); 			} 	} function DA231243() { 	if (DA231479) { 		set_val(DA2311120, inv(DA231669)); 		set_val(DA2311121, DA2311052); 			} 	else { 		set_val(DA2311120, DA231669); 		set_val(DA2311121, inv(DA2311052)); 			} 	} function DA231244() { 	if (!DA231479) { 		set_val(DA2311120, inv(DA231669)); 		set_val(DA2311121, DA2311052); 			} 	else { 		set_val(DA2311120, DA231669); 		set_val(DA2311121, inv(DA2311052)); 			} 	} function DA231245() { 	set_val(DA2311120, DA2311052); 	set_val(DA2311121, DA231669); 	} function DA231246() { 	set_val(DA2311120, inv(DA2311052)); 	set_val(DA2311121, inv(DA231669)); 	} function DA231247() { 	set_val(DA2311120, 0); 	set_val(DA2311121, 0); 	} int DA2311174; function DA231248() { 	if ((event_press(DA231448)  ) && !combo_running(DA23185) && (DA2311197  <= 0 || (DA2311197 < 3000 && DA2311197 > 1  )) && !get_ival(DA231452) && DA231555 > 500 &&!get_ival(DA231451) &&!get_ival(DA231447) &&!get_ival(DA231450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DA23185) ) { 		combo_run(DA23185); 			} 	if (combo_running(DA23185) && (        get_ival(DA231452) ||        get_ival(DA231451) ||        get_ival(DA231447) ||        get_ival(DA231450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DA23185); 		DA2311329 = TRUE; 			} 	} combo DA23185 { vm_tctrl(0);wait(750); set_val(DA231449,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(DA2311174 == 1 ){ set_val(XB1_RX,100)}else{set_val(XB1_RX,-100)} vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo DA23186 { 	vm_tctrl(0);wait( 800); 	DA2311198 = 0; 	} int DA2311175 = 1600; int DA2311176 = 1600; int DA2311177 = 1600; int DA2311178 = TRUE; int DA2311179 = TRUE; int DA231664 = FALSE; int DA2311181 = TRUE; int DA231658 = FALSE; int DA2311183 = TRUE; int DA231660 = FALSE; int DA2311185 = TRUE; int DA231662 = FALSE; function DA231249(){ 	if (get_ival(DA231449)) { 		DA2311187 = 1000; 		DA2311206 = 0; 		DA231555 = 1; 		combo_stop(DA23195); 			} 	if (event_press(DA231451) || event_press(DA231434)) { 		DA2311187 = 4000; 		DA2311206 = 0; 		DA2311175 = 1600; 			} 	if (get_ival(DA231450) && !get_ival(DA231449) ) { 		DA2311187 = 0; 		DA2311206 = 0; 		DA2311175 = 1600; 			} 	else if (get_ival(DA231449)){ 		DA2311187 = 1000; 			} 	if (DA2311187 > 0) { 		DA2311187 -= get_rtime(); 			} 	if (DA2311187 < 0) { 		DA2311187 = 0; 			} 	DA2311266 = DA231428; 	if (event_release(DA231448)) { 		DA2311193 = 1; 		DA2311206 = 0; 		DA231555 = 1; 	} 	if (event_release(DA231454)) { 		DA2311194 = 1; 		DA2311206 = 0; 		DA231555 = 1; 	} 	if (event_release(DA231449)) { 		DA2311176 = 1; 		DA2311206 = 0; 		DA2311175 = 1600; 			} 	if (event_release(DA231450)) { 		DA2311177 = 1; 		DA2311206 = 0; 		DA2311175 = 1600; 			} 	if (event_release(DA231453) || (get_ival(DA231454) && get_ival(DA231449))) { 		DA2311197 = 4000; 		DA2311206 = 0; 	} 	if (get_ival(DA231448) && DA2311197 < 4000 && DA2311197 > 3500) { 		DA2311198 = DA2311197; 		DA2311197 = 0; 			} 	if (DA2311175 < 1510) { 		DA2311175 += get_rtime(); 			} 	if (DA2311176 < 1600) { 		DA2311176 += get_rtime(); 			} 	if (DA2311177 < 1600) { 		DA2311177 += get_rtime(); 			} 	if (DA2311197 > 0) { 		DA2311197 -= get_rtime(); 			} 	if (DA2311197 < 0) { 		DA2311197 = 0; 			} 	if (DA2311193 < 5100) { 		DA2311193 += get_rtime(); 			} 	if (DA2311194 < 4100) { 		DA2311194 += get_rtime(); 			} 	if (DA2311206 > 0) { 		DA2311206 -= get_rtime(); 			} 	if (DA2311206 < 0) { 		DA2311206 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		DA2311175 = 1; 		DA2311206 = 0; 			} 	if (combo_running(DA23192)) { 		set_val(DA231448, 0); 		if(get_ival(DA231448)){ 			DA2311209 = 0; 			combo_stop(DA23187); 			set_val(DA231448, 0); 			combo_stop(DA23192); 			combo_run(DA23149); 					} 			} 	if ((combo_running(DA23197) || combo_running(DA23188))) { 		set_val(DA231448, 0); 		if(get_ival(DA231448)){ 			DA231555 = 1; 			DA2311209 = 0; 			combo_stop(DA23187); 			set_val(DA231448, 0); 			combo_stop(DA23197); 			combo_stop(DA23188); 			combo_run(DA23149); 					} 			} 	if (event_press(DA231447)) { 		combo_run(DA23186); 			} 	if (DA231555 > 1500) { 		if (DA2311176 < 1500) { 			DA2311211 = 120; 					} 		if (DA2311177 < 1500) { 			DA2311211 = 228; 					} 		else { 			DA2311211 = 200; 					} 			} 	if (DA231555 < 1500) { 		DA2311211 = 450; 			} 	if (DA231555 > 2700) { 		DA2311215 = 920; 			} 	else if (DA231555 >= 0 && DA231555 < 2700) { 		DA2311215 = 725; 			} 	} function DA231250() { 	if (DA2311178) { 		if ((DA231555 <= 600 || (DA2311175 <= 1500 && DA2311175 > 1) || ( DA2311176 <= 150 || DA2311177 <= 150)) && event_press(DA231447) ) { 			if (!get_ival(DA231450) && !get_ival(DA231449) && !get_ival(DA231451) && !get_ival(DA231452)) { 				set_val(DA231447, 0); 				if (DA2311197 < 4000 && DA2311197 > 1) { 					set_val(DA231447, 0); 					combo_run(DA23190); 									} 				else { 					set_val(DA231447, 0); 					combo_run(DA23188); 					DA2311206 = 9000; 				} 							} 					} 			} 	if (DA2311185) { 		if (DA231555 > 1000 && !DA2311206 && (!get_ival(DA231450) && !get_ival(PS4_L3) && event_press(DA231447)) &&  DA2311176 > 150 &&  DA2311177 > 150) { 			if (!get_ival(DA231449) && !get_ival(DA231451)) { 				set_val(DA231447, 0); 				if (((DA2311194 > 1 && DA2311194 <= 2500) || (DA2311193 > 1 && DA2311193 <= 3000)) &&  DA2311175 != 1600) { 					set_val(DA231447, 0); 					combo_run(DA23189); 					DA2311206 = 9000; 									} 				else if (((DA2311194 > 2500 && DA2311194 <= 4000) || (DA2311193 > 3000 && DA2311193 <= 3500))  &&  DA2311175 != 1600) { 					set_val(DA231447, 0); 					combo_run(DA23188); 					DA2311206 = 9000; 									} 				else if ((DA2311197 < 4000 && DA2311197 > 1)) { 					set_val(DA231447, 0); 					combo_run(DA23190); 					DA2311206 = 9000; 									} 				else { 					set_val(DA231447, 0); 					DA231254(); 					DA2311206 = 9000; 									} 				DA2311206 = 9000; 							} 					} 			} 	if (DA2311179) { 		if (get_ival(DA231449) && get_ival(DA231450)) { 			if (!get_ival(DA231451) && !get_ival(DA231452) && (DA2311197 && DA2311193 > 1 && DA2311193 <= 1500) || (!DA2311197 && DA2311193 > 1 && DA2311193 <= 1500) || (DA2311193 > 1500 && !DA2311197) && !DA2311206) { 				if (event_press(DA231447)) { 					set_val(DA231447, 0); 					combo_run(DA23198); 					DA2311206 = 9000; 									} 							} 					} 			} 	if (DA2311183) { 		if (!get_ival(DA231452) && !get_ival(DA231449) && !get_ival(DA231450)) { 			if (get_ival(DA231451) && get_ival(DA231447)) { 				DA231255(); 				set_val(DA231447, 0); 				DA2311206 = 9000; 							} 					} 			} 	if (DA2311181) { 		if (get_ival(DA231450) && !get_ival(DA231449) && !DA2311187) { 			if (!get_ival(DA231451) && !get_ival(DA231452) && !DA2311206) { 				if (get_ival(DA231447) && DA231555 >= 1000) { 					set_val(DA231447, 0); 					combo_run(DA23195); 					DA2311206 = 9000; 									} 				if (get_ival(DA231447) && DA231555 < 1000 && !DA2311187) { 					set_val(DA231447, 0); 					combo_run(DA23196); 									} 							} 					} 			} 	if(combo_running(DA23190)){ 		DA2311209 = 0; 		combo_stop(DA23187)   	} 	if (get_ival(DA231449) || DA2311187 > 0) { 		combo_stop(DA23195); 		combo_stop(DA23197); 		combo_stop(DA23196); 			} 	if (combo_running(DA23188) || combo_running(DA23192) || combo_running(DA23197) || combo_running(DA23198) || combo_running(DA23195)) { 		if (get_ival(DA231448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA231452) > 30) { 			combo_stop(DA23192); 			combo_stop(DA23197); 			combo_stop(DA23198); 			combo_stop(DA23195); 			combo_stop(DA23188); 			DA2311209 = 0; 			combo_stop(DA23187)     		} 			} 	if (combo_running(DA23188) || combo_running(DA23189)) { 		if (get_ival(DA231448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DA231452)) { 			combo_stop(DA23190); 			combo_stop(DA23189); 			combo_stop(DA23188); 			DA2311209 = 0; 			combo_stop(DA23187)     		} 			} 	if (event_press(DA231447) && DA2311206 > 100 && DA2311206 < 8990) { 		set_val(DA231447, 0); 		combo_stop(DA23192); 		combo_stop(DA23197); 		combo_stop(DA23198); 		combo_stop(DA23195); 		combo_stop(DA23188); 		DA2311209 = 0; 		combo_stop(DA23187)    combo_run(DA23191); 			} 	if (!DA231664) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA23198); 					} 			} 	if (!DA231658) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA23195); 					} 			} 	if (!DA231660) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA23192); 			DA2311209 = 0; 			combo_stop(DA23187)     		} 			} 	if (!DA231662) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DA23197); 					} 			} 	if ((get_ival(DA231452) || get_ival(DA231448)) && !DA231365) { 		combo_stop(DA2314); 		combo_stop(DA23147); 		combo_stop(DA23133); 			} 	} define DA2311219 = 15; define DA2311220 = 15; int DA2311221 = 0; define DA2311222 = 8000; define DA2311223 = 4; define DA2311224 = 2000; int DA2311209 = 0; const int16 DA2311409[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 DA2311410[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int DA2311226 = FALSE; int DA2311227; int DA2311228; int DA2311229; int DA2311230; function DA231251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA2311229 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA2311226 = FALSE; 		for ( DA231978 = 0; 		DA231978 < sizeof(DA2311410) / sizeof(DA2311410[0]); 		DA231978++) { 			if (DA2311229 == DA2311410[DA231978]) { 				DA2311226 = TRUE; 				break; 							} 					} 		if (!DA2311226) { 			DA2311227 = DA2311410[0]; 			DA2311228 = abs(DA2311229 - DA2311410[0]); 			for ( DA231978 = 1; 			DA231978 < sizeof(DA2311410) / sizeof(DA2311410[0]); 			DA231978++) { 				DA2311230 = abs(DA2311229 - DA2311410[DA231978]); 				if (DA2311230 < DA2311228) { 					DA2311227 = DA2311410[DA231978]; 					DA2311228 = DA2311230; 									} 							} 			set_polar(POLAR_LS, DA2311227, 32767); 					} 			} } function DA231252 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DA2311229 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DA2311226 = FALSE; 		for ( DA231978 = 0; 		DA231978 < sizeof(DA2311409) / sizeof(DA2311409[0]); 		DA231978++) { 			if (DA2311229 == DA2311409[DA231978]) { 				DA2311226 = TRUE; 				break; 							} 					} 		if (!DA2311226) { 			DA2311227 = DA2311409[0]; 			DA2311228 = abs(DA2311229 - DA2311409[0]); 			for ( DA231978 = 1; 			DA231978 < sizeof(DA2311409) / sizeof(DA2311409[0]); 			DA231978++) { 				DA2311230 = abs(DA2311229 - DA2311409[DA231978]); 				if (DA2311230 < DA2311228) { 					DA2311227 = DA2311409[DA231978]; 					DA2311228 = DA2311230; 									} 							} 			set_polar(POLAR_LS, DA2311227, 32767); 					} 			} } int DA2311243; function DA231253() { 	if (combo_running(DA23187) && ( event_press(DA231447)    ||   get_ival(DA231452) ||         get_ival(DA231448) ||        get_ival(DA231453) ||        get_ival(DA231454) ||        get_ival(DA231449)      )) { 		combo_stop(DA23187); 		DA2311209 = 0; 			} 	if (DA2311221 == 0) { 		if ( ( DA2311197 == 0 && !combo_running(DA23190) && !combo_running(DA23198) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(DA231447) || (DA2311209 == 1 ||     combo_running(DA23196) || combo_running(DA23194)|| combo_running(DA23192) ||  combo_running(DA23195)     || combo_running(DA23188) || combo_running(DA23197) || combo_running(DA23189) ||  combo_running(DA23191)  ))     ) { 			if(DA231437)DA231252 (); 			else if (DA231438)DA231251 (); 			combo_stop(DA231103); 			combo_run(DA23187); 					} 			} 	else{ 		combo_stop(DA23187); 			} 	} combo DA23187 { 	if(DA231437)DA231252 (); 	else if (DA231438)DA231251 (); 	combo_stop(DA231103); 	vm_tctrl(0); 	wait(4000); 	DA2311209 = 0; 	} combo DA23188 { 	set_val(DA231449,0); 	set_val(DA231447, 100); 	vm_tctrl(0);wait( random(210, 215) + DA231430); 	set_val(DA231447, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function DA231254() { 	if (DA231555 > 600 && DA231555 <= 800) { 		DA2311244 = 240; 			} 	if (DA231555 > 800 && DA231555 <= 1000) { 		DA2311244 = 230; 			} 	if (DA231555 > 1000 && DA231555 <= 1500) { 		DA2311244 = 225; 			} 	if (DA231555 > 1500 && DA231555 <= 2000) { 		DA2311244 = 235; 			} 	if (DA231555 > 2000) { 		DA2311244 = 218; 			} 	combo_run(DA23197); 	} combo DA23189 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 500); 	} combo DA23190 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait( 205); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 300); 	} combo DA23191 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait( 190); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 400); 	} int DA2311249; int DA2311250; int DA23129; int DA231476; int DA231474; int DA2311254; int DA2311255; combo DA23192 { 	if (DA2311250) { 		set_val(DA231447, 0); 		DA2311254 = 350; 			} 	else { 		DA2311254 = 0; 			} 	if (DA2311250) { 		DA231243(); 		DA231239(0, 0); 			} 	vm_tctrl(0); 	wait(DA2311254); 	if (DA2311250) { 		set_val(DA231450, 0); 		DA2311254 = 60; 			} 	else { 		set_val(DA231451, 0); 		DA2311254 = 60; 			} 	set_val(DA231447,0); 	vm_tctrl(0);wait(DA2311254); 	set_val(DA231451, 0); 	set_val(DA231450, 0); 	set_val(DA231447,0); 	vm_tctrl(0);wait(DA2311254); 	if (DA2311250) { 		DA2311254 = 0; 			} 	else { 		DA2311254 = 60; 			} 	set_val(DA231450, 0); 	set_val(DA231451, 0); 	set_val(DA231447,0); 	vm_tctrl(0);wait(DA2311254); 	set_val(DA231447, 100); 	set_val(DA231451, 100); 	vm_tctrl(0);wait(random(265, 268) +   DA231429 ); 	set_val(DA231451, 100); 	set_val(DA231447, 0); 	if (DA2311250) { 		DA2311254 = 15; 			} 	else { 		DA2311254 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + DA2311267 + DA2311266 + DA2311254 ); 	set_val(DA231451, 100); 	set_val(DA231447, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA231447, 0); 	set_val(DA231451, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(DA231451, 100); 	vm_tctrl(0);wait(2500); 	} int DA2311194; int DA2311197; int DA231555; int DA2311193; int DA2311266; int DA2311267 = 111; int DA2311187; int DA2311198; int DA2311270; function DA231255() { 	DA2311270 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DA2311270 > 5 && DA2311270 < 40) ) {         DA231479 = FALSE; 			} 	if ((DA2311270 > 40 && DA2311270 < 85)){ 		DA231479 = TRUE; 		    } 	if ((DA2311270 > 95 && DA2311270 < 130) ) { 		DA231479 = FALSE; 		    } 	 if((DA2311270 > 130 && DA2311270 < 175)) { 		DA231479 = TRUE; 		} 			if ((DA2311270 > 185 && DA2311270 < 220) ) {        DA231479 = FALSE; 			} 	if ((DA2311270 > 220 && DA2311270 < 265)){        DA231479 = TRUE; 		    } 	if ((DA2311270 > 275 && DA2311270 < 310) ) {        DA231479 = FALSE; 		    } 	 if((DA2311270 > 310 && DA2311270 < 355)) { 		DA231479 = TRUE; 		} 	if (DA2311197 == 0 && (DA231555 >= 750 || ((DA2311198 > 3000 && DA2311193 > 1 && DA2311193 < 5000)))) { 		if (DA231555 <= 2000 && DA2311193 > 1500) { 			set_val(DA231447, 0); 			DA2311267 = 170; 		} 		if (DA231555 <= 2000 && DA2311193 > 1 && DA2311193 <= 1500) { 			set_val(DA231447, 0); 			DA2311267 = 202; 					} 		if (DA231555 > 2000 || (DA2311193 > 1 && DA2311193 <= 1500)) { 			set_val(DA231447, 0); 			DA2311267 = 151; 					} 		if ((DA231555 > 2000 && DA2311193 > 1500) || DA2311198 > 1 && DA2311193 > 1) { 			set_val(DA231447, 0); 			DA2311267 = 152; 					} 		if ((DA231555 < 2000 && DA2311193 > 1500) || DA2311197 > 1 && DA2311193 > 1) { 			set_val(DA231447, 0); 			DA2311267 = 149; 					} 		if (DA2311193 > 1500) { 			set_val(DA231447, 0); 			DA2311267 = 148; 					} 		if (!DA231555 > 2000 && DA2311198 > 1 && DA2311193 > 1 && DA2311193 <= 1500) { 			set_val(DA231447, 0); 			DA2311267 = 147; 					} 		set_val(DA231447, 0); 		combo_stop(DA23197); 		combo_stop(DA23198); 		combo_stop(DA23195); 		combo_stop(DA23188); 		combo_stop(DA23194); 		combo_stop(DA23191); 		combo_stop(DA23190); 		combo_run(DA23192); 			} 	else { 		if (DA2311197) { 			set_val(DA231447, 0); 			combo_run(DA23193); 					} 		else { 			if (DA231555 < 750) { 				set_val(DA231447, 0); 				combo_run(DA23194); 							} 					} 			} } combo DA23193 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(DA231447, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo DA23194 { 	set_val(DA231447, 100); 	vm_tctrl(0);wait( random(200, 205) + DA231429 )  set_val(DA231447, 0); 	vm_tctrl(0);wait( 700); 	} int DA2311280 = 246; int DA2311211 = 150; int DA2311282 = 0; combo DA23195 { 	set_val(DA231451, 100); 	set_val(DA231450, 0); 	set_val(DA231447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DA231451, 0); 	set_val(DA231450, 0); 	set_val(DA231447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	DA2311282 = DA231428; 	set_val(DA231450, 100); 	set_val(DA231447,0); 	vm_tctrl(0);wait( 60); 	set_val(DA231450, 100); 	set_val(DA231447, 100); 	vm_tctrl(0);wait( DA2311280 + 10 + random(-2, 2) +  DA231432); 	set_val(DA231450, 100); 	set_val(DA231447, 0); 	DA2311249 = DA2311211; 	vm_tctrl(0);wait( DA2311211 + DA2311282 - 58 + DA231457 ); 	set_val(DA231450, 100); 	if(DA2311243)set_val(DA231447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA231450, 100); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 3000); 	} combo DA23196 { 	set_val(DA231450, 100); 	set_val(DA231447, 100); 	vm_tctrl(0);wait( 160 + DA231432 ); 	set_val(DA231450, 100); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 3000); 	} int DA2311206; int DA2311284 = 220; int DA2311244; int DA2311286 = 0; combo DA23197 { 	DA2311286 = DA231428; 	set_val(DA231447, 100); 	vm_tctrl(0);wait( DA2311284 + DA231430); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( DA2311244 + (DA2311286) + 22 + DA231458); 	if(DA2311243)set_val(DA231447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DA231447, 0); 	vm_tctrl(0);wait( 2000); 	} int DA2311288 = TRUE; int DA2311215; int DA2311290 = 245; int DA2311291 = 0; combo DA23198 { 	set_val(DA231449, 100); 	set_val(DA231450, 100); 	if (DA2311288) { 		DA2311291 = DA231428; 			} 	else { 		DA2311291 = 0   	} 	set_val(DA231447, 100); 	vm_tctrl(0);wait( DA2311290); 	vm_tctrl(0);wait( DA2311215 + DA2311291 + 40)  } int DA2311294; int DA2311295 = 145; int DA231651; int DA231650; int DA231653; int DA231652; combo DA23199 { 	set_val(DA231454, 0); 	vm_tctrl(0);wait( 30); 	set_val(DA231454, 100); 	vm_tctrl(0);wait( 60); 	} int DA231654; int DA231655; combo DA231100 { 	set_val(DA231453, 100); 	vm_tctrl(0);wait( DA231655); 	} define DA2311302 = TRUE; define DA2311303 = 95; define DA2311304 = 10; define DA2311305 = 70; define DA2311306 = FALSE; define DA2311307 = 50; define DA2311308 = 95; define DA2311309 = XB1_LT; define DA2311310 = XB1_RT; define DA2311311 = XB1_LX; define DA2311312 = XB1_LY; define DA2311313 = POLAR_LS; int DA2311314; function DA231256() { 	if (    get_ival(DA231452) > 30 &&    (get_ival(DA231451) || get_ival(DA231448)) &&    (!get_ival(DA231453) || !get_ival(DA231447))  ) { set_val(DA231452, 0); 		if(!get_ival(DA231447)){ 			set_val(DA231451,100); 					} 		else{ 			set_val(DA231451,0); 					} 		  combo_run(DA231102); 		if(DA231431 == TRUE){ 			combo_run(DA231101); 					} 			} 	else { 		combo_stop(DA231102); 		combo_stop(DA231101); 			} 	} combo DA231101 { 	if (DA231431 == TRUE) { 		set_val(DA231450, 100); 		DA2311315 = 60; 			} 	else { 		DA2311315 = 0; 			} 	set_val(DA231451, 0); 	vm_tctrl(0);wait( DA2311315); 	if (DA231431 == TRUE) { 		set_val(DA231450, 0); 		DA2311315 = 60; 			} 	else { 		DA2311315 = 0; 			} 	set_val(DA231451, 0); 	vm_tctrl(0);wait( DA2311315); 	if (DA231431 == TRUE) { 		set_val(DA231450, 100); 			} 	vm_tctrl(0);wait( 750); 	vm_tctrl(0);wait( 750); 	} combo DA231102 { 	if(!get_ival(DA231447)){ 		set_val(DA231451,100); 			} 	else{ 		set_val(DA231451,0); 			} 	set_val(DA231452, 100); 	vm_tctrl(0);wait(DA231600); 	if(!get_ival(DA231447)){ 		set_val(DA231451,100); 			} 	else{ 		set_val(DA231451,0); 			}     set_val(DA231452, 0); 	vm_tctrl(0);wait(500); 	} int DA2311317; int DA2311315 ; function DA231257(DA231240) { return DA231259(DA231240 + 8192); } function DA231259(DA231240) {   DA231240 = (DA231240 % 32767) << 17;   if((DA231240 ^ (DA231240 * 2)) < 0) { DA231240 = (-2147483648) - DA231240; }   DA231240 = DA231240 >> 17;   return DA231240 * ((98304) - (DA231240 * DA231240) >> 11) >> 14; } int DA2311321, DA2311322; function DA231261(DA231121, DA231122, DA231123, DA231265){   DA231265 = (DA231265 * 32767) / 100;   DA231123 = (DA231123 * 32767) / 10000;   DA231122 = (360 - DA231122) * 91;   DA2311322 = DA231259(DA231122); DA2311321 = DA231257(DA231122);   DA231122 = 32767 - DA231257(abs(abs(DA2311322) - abs(DA2311321)));   DA231123 = DA231123 * (32767 - ((DA231122 * DA231265) >> 15)) >> 15;   set_val(42 + DA231121, clamp((DA231123 * DA2311321) >> 15, -32767, 32767));   set_val(43 + DA231121, clamp((DA231123 * DA2311322) >> 15, -32767, 32767));   return; } int DA2311327, DA2311328; int DA2311329 = TRUE; int DA2311330; int DA2311331; int DA2311332; int DA2311333; int DA2311334; function DA231266() {    if ( get_ival(DA231452) && (!get_ival(DA231451)) && DA2311335 <= 0 && DA2311336 <= 0 ) {           combo_stop(DA231103);         combo_stop(DA231104);    }    if (( get_ival(XB1_LS) || get_ival(XB1_RS)  || get_ival(DA231449)  || get_ival(DA231451) ||      get_ival(DA231448) || get_ival(DA231454) || get_ival(DA231447) || get_ival(DA231453)   || get_ival(XB1_PR1) ||     get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(DA2311120))> 20 || abs(get_ival(DA2311121))> 20) ) )){        combo_stop(DA231103);        combo_run(DA231104);    }    if ( get_val(DA231452)&&( (abs(get_ival(DA2311120))> 20 || abs(get_ival(DA2311121))> 20) )){    combo_stop(DA231105);    }    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(DA231451) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(DA231104)     && !get_ival(DA231449) &&  !get_ival(DA231452) && !get_ival(DA231448) && !get_ival(DA231454) && !get_ival(DA231447)){        combo_run(DA231103);    } } int DA2311335; int DA2311336; function DA231267(){   stickize(POLAR_RX, POLAR_RY, 32767);   DA2311327 = get_polar(POLAR_LS, POLAR_RADIUS);   DA2311328 = get_polar(POLAR_LS, POLAR_ANGLE);   DA231261(POLAR_LS, DA2311328, DA2311327, DA231455);   } combo DA231103 {    DA231267(); vm_tctrl(0); wait(400); vm_tctrl(0); wait(1000); 	} combo DA231104 { DA231269(POLAR_LS, 5000, 10000); vm_tctrl(0); 	wait(200); }  combo DA231105 { 	DA231269(POLAR_LS, 5000, 12000); 	vm_tctrl(0); 	wait(2000); } combo DA231106{ } combo DA231107 { 	set_val(DA231448,100); 	vm_tctrl(0);wait( DA231651); 	set_val(DA231448,  0); 	vm_tctrl(0);wait( 30); 	if(DA231401){ 		set_val(DA231450,100); 			} 	vm_tctrl(0);wait( 60); 	} combo DA231108 { 	set_val(DA231450,  100); 	vm_tctrl(0);wait( 60);     wait( 60); 	} combo DA231109 { 	set_val(DA231454,100); 	vm_tctrl(0);wait( DA231653); 	set_val(DA231454,  0); 	vm_tctrl(0);wait( 30); 	if(DA231398){ 		set_val(DA231454,100); 			} 	vm_tctrl(0);wait( 60); 	} int DA2311001 int DA2311344 combo DA231110 { 	combo_suspend(DA231110) 	vm_tctrl(0);wait(361) } function DA231268 (){ 	if(DA2311001[DA2311344] != 361){ 	    DA2311344-- 	} 	else{ 		if(inv(DA2311344) != 297){ 			DA231268(); 		} 	} } int DA2311347; combo DA231111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int DA231176; function DA231269(DA231270, DA231271, DA231272) {   DA231176 = DA231124(DA231270, POLAR_RADIUS);   if(DA231271) {     if(DA231176 <= DA231271) DA231176 = (DA231176 * 5000) / DA231271;     else DA231176 = ((5000 * (DA231176 - DA231271)) / (10000 - DA231271)) + 5000;   }   if (DA231272) DA231176 = (DA231176 * DA231272) / 10000;   set_polar2(DA231270, DA231124(DA231270, POLAR_ANGLE), min(DA231176, 14142));   if (DA231270 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; } combo DA231112{ vm_tctrl(-7); wait(1000); } 