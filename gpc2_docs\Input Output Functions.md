# Input Output Functions

set_val
set_val — Set I/O value

Description
void set_val(uint8 io, fix32 value);
void set_val(uint8 io, int32 value);
Set the I/O value indexed by io to value.

Parameters
io: Index of an I/O value.
value: The new value to set.
Return Value
No value is returned.

Examples
Example #1 set_val() example
main {
    // set_val can accept fix32 ...
    set_val(STICK_2_X, 90.0);
    // ... and integer types.
    set_val(STICK_2_Y, 90);
}

get_val
get_val — Get I/O value

Description
fix32 get_val(uint8 io);
Get the I/O value indexed by io.

An I/O value is initialized with the current state value of the input element to it mapped.
If an I/O value was programmatically set by set_val(), get_val() will return the value programmatically set.
Parameters
io: Index of an I/O value.
Return Value
The current I/O value.

Examples
Example #1 get_val() example
main {
    if(get_val(BUTTON_5) >= 75.5) {
        set_val(BUTTON_5, 100.0);
    }
}

get_actual
get_actual — Get actual I/O value

Description
fix32 get_actual(uint8 io);
Get the original initialized I/O value indexed by io.

An I/O value is initialized with the current state value of the input element to it mapped.
get_actual() will always return the original initialized value, even if set_val() is used to set an new value.
Parameters
io: Index of an I/O value.
Return Value
The actual I/O value.

Examples
Example #1 get_actual() example
main {
    set_val(BUTTON_5, 0.0);
    if(get_actual(BUTTON_5) >= 75.5) {
        set_val(BUTTON_5, 100.0);
    }
}

get_prev
get_prev — Get previous I/O value

Description
fix32 get_prev(uint8 io);
Get the previous I/O value indexed by io.

Parameters
io: Index of an I/O value.
Return Value
The previous I/O value.

Examples
Example #1 get_prev() example
main {
    if(get_prev(BUTTON_5) == 0.0 && get_actual(BUTTON_5) > 0.0) {
        set_val(BUTTON_5, 0.0);
    }
}

get_port
get_port — Get origin port of I/O value

Description
uint8 get_port(uint8 io);
Retrieve the port that originated the I/O value indexed by io.

Parameters
io: Index of an I/O value.
Return Value
The port index, accordingly table 1.

Table 1
Index	Port Designator	Port Label
1	PORT_USB_A	Input-A
2	PORT_USB_B	Input-B
0	PORT_USB_C	Output
3	PORT_USB_D	Prog
4	PORT_BT_A	
5	PORT_BT_B	
6	PORT_BT_C	
Examples
Example #1 get_port() example
main {
    // If BUTTON_16 is active ...
    if(get_actual(BUTTON_16)) {
        // and if the value is comming from USB-B ...
        if(get_port(BUTTON_16) == PORT_USB_B) {
            // don't forward the input to the console.
            set_val(BUTTON_16, 0.0);
        }
    } 
}

is_active
is_active — Is I/O active?

Description
bool is_active(uint8 io);
Check if the I/O indexed by io is active.

An I/O is active if its value is outside the resting range.
Parameters
io: Index of an I/O.
Return Value
Returns true if the I/O is active, FALSE otherwise.

Examples
Example #1 is_active() example
main {
    if(is_active(STICK_1_X)) {
        set_val(BUTTON_5, 100.0);
    }
}

is_release
is_release — Is I/O release?

Description
bool is_release(uint8 io);
Check if the I/O indexed by io is release.

An I/O is release if its value is inside the resting range.
Parameters
io: Index of an I/O.
Return Value
Returns true if the I/O is release, FALSE otherwise.

Examples
Example #1 is_active() example
main {
    if(is_release(ACCEL_2_Y)) {
        set_val(BUTTON_8, 100.0);
    }
}

time_active
time_active — Time passed since last active event

Description
uint32 time_active(uint8 io);
The time that has passed since the I/O indexed by io last entered in active state.

The time_active() return value does not necessary means the I/O is currently active.
Parameters
io: Index of an I/O.
Return Value
Returns time expressed in milli-seconds.

Examples
Example #1 time_active() example
main {
    if(is_active(STICK_1_X) && time_active(STICK_1_X) <= 1000) {
        set_val(BUTTON_5, 100.0);
    }
}

time_release
time_release — Time passed since last release event

Description
uint32 time_release(uint8 io);
The time that has passed since the I/O indexed by io last entered in release state.

The time_release() return value does not necessary means the I/O is currently release.
Parameters
io: Index of an I/O.
Return Value
Returns time expressed in milli-seconds.

Examples
Example #1 time_release() example
main {
    if(time_release(STICK_1_X) >= 250 && time_release(STICK_1_X) <= 500) {
        set_val(BUTTON_5, 100.0);
    }
}

event_active
event_active — I/O changed to active state

Description
bool event_active(uint8 io);
Check if the I/O indexed by io changed its state from release to active.

Parameters
io: Index of an I/O.
Return Value
Returns true if the I/O entered in active state, FALSE otherwise.

Examples
Example #1 event_active() example
main {
    if(event_active(BUTTON_13)) {
        combo_run(Example);
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(50);
    set_val(BUTTON_8, 100.0);
    wait(50);
    set_val(BUTTON_16, 100.0);
    wait(50);
}

event_release
event_release — I/O changed to release state

Description
bool event_release(uint8 io);
Check if the I/O indexed by io changed its state from active to release.

Parameters
io: Index of an I/O.
Return Value
Returns true if the I/O entered in release state, FALSE otherwise.

Examples
Example #1 event_release() example
main {
    if(event_release(BUTTON_13)) {
        combo_stop(Example);
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(50);
    set_val(BUTTON_8, 100.0);
    wait(50);
    set_val(BUTTON_16, 100.0);
    wait(50);
}

check_active
check_active — Is I/O active longer than?

Description
bool check_active(uint8 io, uint32 ms);
Check if the I/O indexed by io is in active state for ms milli-seconds or more.

check_active() is the combination of is_active() with time_active().
Parameters
io: Index of an I/O.
ms: Time expressed in milli-seconds.
Return Value
Returns true if the I/O is active longer than ms milli-seconds, FALSE otherwise.

Examples
Example #1 check_active() example
main {
    if(check_active(BUTTON_19, 5000)) {
        set_val(BUTTON_5, 100.0);
    }
}

check_release
check_release — Is I/O release longer than?

Description
bool check_release(uint8 io, uint32 ms);
Check if the I/O indexed by io is in release state for ms milli-seconds or more.

check_release() is the combination of is_release() with time_release().
Parameters
io: Index of an I/O.
ms: Time expressed in milli-seconds.
Return Value
Returns true if the I/O is release longer than ms milli-seconds, FALSE otherwise.

Examples
Example #1 check_release() example
main {
    if(check_release(BUTTON_19, 250)) {
        set_val(BUTTON_5, 0.0);
    }
}

inhibit
inhibit — Inhibit I/O

Description
void inhibit(uint8 io, uint32 ms);
Inhibit the I/O indexed by io for ms milli-seconds.

The inhibit() sets the I/O value to zero until its active time is greater than or equal to ms milli-seconds.
Parameters
io: Index of an I/O.
ms: Time expressed in milli-seconds.
Return Value
No value is returned.

Examples
Example #1 inhibit() example
main {
    inhibit(BUTTON_12, 1250);
}