// Left and Right Stick Sensitivity Enhancer
// This script modifies the sensitivity of both analog sticks for better control
// The sensitivity is adjustable through the sensitivityValue variable (0-200)

// Global sensitivity setting (200 = double sensitivity, 100 = normal, 50 = half)
int sensitivityValue = 200; 

main {
    // Apply sensitivity adjustments to all stick axes
    // 16384 is the midpoint value (32767/2)
    // Sensitivity is calculated as a percentage of max value (32767)
    _sensitivity(POLAR_LX, 16384, sensitivityValue * 32767/100);  // Left stick X-axis
    _sensitivity(POLAR_LY, 16384, sensitivityValue * 32767/100);  // Left stick Y-axis
    _sensitivity(POLAR_RX, 16384, sensitivityValue * 32767/100);  // Right stick X-axis
    _sensitivity(POLAR_RY, 16384, sensitivityValue * 32767/100);  // Right stick Y-axis
}

// Variables for sensitivity calculations
int _val_s, _val;

// Function to adjust stick sensitivity
// Parameters:
//   id: Stick axis identifier (POLAR_LX, POLAR_LY, etc.)
//   mid: Midpoint value for the axis
//   sen: Sensitivity multiplier
function _sensitivity(id, mid, sen) {
    // Clamp input value between valid range
    _val = clamp(get_val(id), -32767, 32766);
    
    // Handle non-standard midpoint cases
    if(mid == NOT_USE) {
        _val_s = -1;
        if(_val >= 0) _val_s = 1;
        _val *= _val_s;
        // Apply piece-wise linear transformation
        if(_val <= mid) _val = (_val * 16384) / mid;
        else _val = ((16384 * (_val - mid)) / (32767 - mid)) + 16384;
        _val *= _val_s;
    }
    
    // Apply sensitivity adjustment if specified
    if(sen != NOT_USE) {
        _val = (_val * sen) >> 15;  // Bit shift for efficient division
    }
    
    // Set the final value, ensuring it stays within valid range
    set_val(id, clamp(_val, -32768, 32767));
}