/* ********************* GPC FILE RECOVERED ********************* */


int rt_output, rt_timer;
int rt_delay = 90;
int rt_delay2 = 30;// Delay in ms between steps (lower = faster ramp)
int XB1_RB_input_state; // Variable to store the input state
int current_time;

main {
    // GPC: Use temporary variable for addition
    current_time = get_rtime();
    rt_timer = rt_timer + current_time; 

    XB1_RB_input_state = get_val(XB1_LB); // Read the *input* state

    // GPC: Use parentheses for if conditions
    if (XB1_RB_input_state > 0) { // Check if input XB1_RB is pressed 
        if (rt_timer >= rt_delay) {
            rt_timer = 0;
            if (rt_output < 100) {
                 // GPC: Use temporary variable for addition
                 rt_output = rt_output + 4; 
            }
        }
    } else { // Input XB1_RB is released
        if (rt_timer >= rt_delay2) {
            rt_timer = 0;
            if (rt_output > 0) {
                // GPC: Use temporary variable for subtraction
                rt_output = rt_output - 4; 
            }
        }
    }

    // GPC: Use spaces for function calls
   	set_val(XB1_LB, rt_output); // Set the output value for XB1_RB (the ramp/oscillation)

    // Now, control XB1_LB based on input state and rt_output
    // GPC: Use parentheses for if conditions
    if (XB1_RB_input_state == 0) { // If input XB1_RB is released
        if (rt_output > 0) { // And the ramp value is still positive
            // GPC: Use spaces for function calls
            set_val(XB1_LT, 100); // Activate LT
        } else {
            set_val(XB1_LT, 0); // Deactivate LT
        }
    } else { // If input XB1_RB is pressed
        set_val(XB1_LT, 0); // Deactivate LT
    }
}