# Security Policy

## Reporting Issues

Please report security vulnerabilities to [security contact].

## Best Practices

1. **API Tokens**
   - Never commit tokens to version control
   - Rotate tokens regularly
   - Use minimal required permissions

2. **Environment Variables**
   - Keep .env files secure and private
   - Use separate tokens for development/production

3. **Access Control**
   - Regularly audit Confluence space access
   - Follow principle of least privilege 