
  // ============  HOW it WORKs ============// : 
//1 - the script triggered by pressing Shot button and direction where you aim ,  
//    while nothing else pressed , like when no sprnt or L2 or L1 ..etc .
//2-  it will generate fixed power for both setiuations , for inside box you just need to press short period on shot button
//    that suitable for inside 18 yards box finishing ,, for outside of box hold shot button long.
//3 - it will lock the corner of where you aim to grant a perfect aim as possible .
  
  //==================================================// 
   // MAKE SURE NO Shooting SCRIPTS Generated From FIFA Generator // 
   
      //============ COPY THis at the start of the script ============//
   
int Shot_wait ; 
int ShotBoxWait ;
   
   
   
   
   
   
   //============ COPY THis just before the END MAIN SECTION ============//

if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn)){
 
		if( get_val(ShotBtn ) ){
		    set_val(ShotBtn,0);
			
			ShotBoxWait += get_ptime(ShotBtn);
			if (ShotBoxWait > 350)  { Shot_wait =  240; combo_run(Inside_Box_Finishing); }
		
		
			if (ShotBoxWait < 350 )  { Shot_wait =  220; combo_run(Inside_Box_Finishing) }		
	}
	
	}
	
  

  // ============ END OF COPY TO MAIN SECTION ============ //
  
  
  
  
   // ============ COPY THis TO COMBOS SECTION ============ //
    
combo Shooting_Setup {
set_val(ShotBtn, 0);
  set_val(PlayerRun,100);
  RA_L_R () ; 
  wait(30);//
  set_val(ShotBtn, 0);
  set_val(PaceCtrol,100);
  set_val(SprintBtn,100);
  wait(30);//     
}  

combo Inside_Box_Finishing {
AUTO_EXIT()
call (Shooting_Setup )
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(Shot_wait);
    set_val(PS4_R1,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(PS4_R1,100);
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);  
    ShotBoxWait=0
} 




function AUTO_EXIT() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
	
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 

	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE

		  
		 
	}
	}
	


 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }
