define RIGHT_MAG_THRESHOLD = 17;
define RIGHT_MAG_TARGET    = 100;

int rxVal;
int ryVal;
int lxVal;
int lyVal;
int rightMagnitude;
int leftMagnitude;

int scaledRX;
int scaledRY;
int scaledLX;
int scaledLY;
int result;
int temp;

main {

    rxVal = get_val(XB1_RX);
    ryVal = get_val(XB1_RY);
    lxVal = get_val(XB1_LX);
    lyVal = get_val(XB1_LY);

    // Calculate magnitudes for both sticks
    rightMagnitude = sqrt(rxVal * rxVal + ryVal * ryVal);
    leftMagnitude = sqrt(lxVal * lxVal + lyVal * lyVal);

    // Handle right stick
    if(rightMagnitude >= RIGHT_MAG_THRESHOLD && rightMagnitude != 0) {
        scaledRX = (rxVal * RIGHT_MAG_TARGET) / rightMagnitude;
        scaledRY = (ryVal * RIGHT_MAG_TARGET) / rightMagnitude;
        set_val(XB1_RX, scaledRX);
        set_val(XB1_RY, scaledRY);
    }

    // Handle left stick
    if(leftMagnitude >= RIGHT_MAG_THRESHOLD && leftMagnitude != 0) {
        scaledLX = (lxVal * RIGHT_MAG_TARGET) / leftMagnitude;
        scaledLY = (lyVal * RIGHT_MAG_TARGET) / leftMagnitude;
        set_val(XB1_LX, scaledLX);
        set_val(XB1_LY, scaledLY);
    }
}

function sqrt(x) {
    if (x <= 0) return 0;
    
    result = x;
    temp = 0;
    
    do {
        temp = result;
        result = (result + x / result) / 2;
    } while (temp > result);
    
    return result;
}