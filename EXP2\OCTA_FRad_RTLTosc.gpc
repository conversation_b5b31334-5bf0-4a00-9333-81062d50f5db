int triggerTimer_RT = 0;            // Timer to manage RT oscillation timing
int triggerValue_RT = 80;        // Initial trigger value for RT (starts at 70)
int increasing_RT = TRUE;           // Start by increasing triggerValue_RT
int triggerTimer_LT = 0;            // Timer to manage LT oscillation timing
int triggerValue_LT = 80;       // Initial trigger value for LT (starts at 100)
int increasing_LT = TRUE;          // Start by decreasing triggerValue_LT

define TriggerControl_RT = XB1_RT; // Right Trigger
define TriggerControl_LT = XB1_LT; // Left Trigger
// It can be any button that supports a range of values.
// For example, L2 or R2 can have values between 0 and 100,
// while R1 and L1 are either 0 or 100, meaning no oscillation is needed for them.

// Speed Control: Change oscillation speed .
// Lower values = faster oscillation, higher values = slower oscillation
int speedControl = 10;
int rt_pressed;
int lt_pressed;


int RS_triggerTimer = 0;            // Timer to manage oscillation timing
int RS_triggerValue_RT = 90;        // Initial trigger value for RT (starts at 70)
int RS_increasing = TRUE;           // Start by increasing triggerValue_RT
define RS_TriggerControl_RT = XB1_RT; // Right Trigger
define RS_TriggerControl_LT = XB1_LT; // Left Trigger
// It can be any button that supports a range of values.
// For example, L2 or R2 can have values between 0 and 100,
// while R1 and L1 are either 0 or 100, meaning no oscillation is needed for them.

// Speed Control: Change oscillation speed .
// Lower values = faster oscillation, higher values = slower oscillation
int RS_speedControl = 35;
int RS_rt_pressed;
int RS_lt_pressed;
int RS_triggerValue_LT; // Temporary variable for LT value calculation


// Ellipse ratios
define X_RATIO = 100;
define Y_RATIO = 40;

// Auto-rotation settings
define ROTATION_SPEED = 25;
define ROTATION_RADIUS = 100;

int x, y;
int last_x = 0;
// Remember last stick position X
int last_y = 0;
// Remember last stick position Y
int last_step = 0;
// Remember last direction step
int rotation_step = 0;
int rotation_counter = 0;
int cos_a;
int sin_a;
int rx;
int ry;
// Add zone-based rotation matrix components
const int8 cos_table[] = {
	100, 71, 0, -71, -100, -71, 0, 71
}
;
const int8 sin_table[] = {
	0, 71, 100, 71, 0, -71, -100, -71
}
;
// Function to determine which zone (0-7) the stick position falls into
function get_zone(int x, int y) {
	if (abs(x) > abs(y) * 2) {
		if (x > 0) return 0;
		// Right
		return 4;
		// Left
	}
	else if (abs(y) > abs(x) * 2) {
		if (y > 0) return 2;
		// Down
		return 6;
		// Up
	}
	else {
		if (x > 0 && y > 0) return 1;
		// Down-Right
		if (x < 0 && y > 0) return 3;
		// Down-Left
		if (x < 0 && y < 0) return 5;
		// Up-Left
		if (x > 0 && y < 0) return 7;
		// Up-Right
		return 0;
		// Default to right for any other case (including center)
	}
	return 0;
	// Fallback return for compiler
}
int zone;
int lastPressTime;
// Time of the last press
int mode;
// 0 = no press, 1 = single press, 2 = double press
int currentTime;
// For timing calculations
int timer;
// Accumulated timer
init {
	lastPressTime = 0;
	mode = 0;
	currentTime = 0;
	timer = 0;
	}
// Constants for stick identification and scaling
define o22_stickX = XB1_LX;
define o22_stickY = XB1_LY;
int o22_MAX_VAL = 90;
// Adjust this to scale octagon size (1-100)
// Constants for octagonal normalization
define o22_INV_SQRT2_NUM = 707;
// Approximately 1/√2 * 1000
define o22_INV_SQRT2_DEN = 1000;
// Denominator for diagonal scaling
// Constants for 22.5° rotation (clockwise)
// cos(22.5°) ≈ 0.92388 and sin(22.5°) ≈ 0.382683,
// scaled by 1000 for fixed-point math.
define o22_COS22_NUM = 924;
// Approximation of cos(22.5°) * 1000
define o22_SIN22_NUM = 383;
// Approximation of sin(22.5°) * 1000
define o22_ROT_DEN   = 1000;
// Denominator for rotation scaling
// Global variables (must be global)
//int x, y;
int o22_abs_x, o22_abs_y;
int o22_L_inf, o22_L_1;
// For L-infinity and L1 norms
int o22_octNorm;
// Octagonal norm
int o22_scaled_L1;
// Scaled L1 norm
int o22_output_x, o22_output_y;
int o22_rotated_x, o22_rotated_y;
// For rotated outputs
////////////////////////////////////////////////////////////////////////////////
// LS_Dribbling_CoPilot.gpc
// Enhanced left stick dribbling script with improved mathematics and mechanics
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// GLOBAL VARIABLES
////////////////////////////////////////////////////////////////////////////////
int LX, LY;
// Raw stick inputs
int scaled_x, scaled_y;
// Final outputs after processing
int prev_x, prev_y;
// Previous frame values for smoothing
int velocity_x, velocity_y;
// Velocity tracking for acceleration
// Tunable Constants
int A_DEADZONE = 5;
// Inner deadzone threshold (0-100)
int A_MAX_INPUT = 70;
// Maximum stick value
int A_SMOOTH_FACTOR = 0;
// Input smoothing (0-100)
int A_ACCEL_THRESHOLD = 10;
// Speed threshold for acceleration (0-100)
int A_SENSITIVITY = 65;
// Base sensitivity percentage (100 = normal)
// Octagon shaping constants
int INV_SQRT2_NUM = 700;
// For octagonal corners (700-725 range)
int INV_SQRT2_DEN = 1024;
// Denominator for diagonal scaling
// Processing variables
int sign_x, sign_y, abs_x, abs_y;
int L_inf, L_1, octNorm;
int accel_factor;
int smooth_x, smooth_y;
int scaled_L1;
// For octagonal shape calculation
int sensitivity_scaled;
// For sensitivity calculations
int scaledNorm;
// For proper stick scaling
int raw_scaled_x, raw_scaled_y;
// Pre-smoothing values
// Octagonal boundary for analog stick
// Creates 8-directional movement with precise angles
// Constants for stick identification
define stick_B = POLAR_LS;
// Left stick in polar mode
define stickX = XB1_LX;
// Left stick X axis
define stickY = XB1_LY;
// Left stick Y axis
// Constants for octagon shape
define MAX_VAL_B = 100;
// Maximum stick value
define RATIO_B = 70;
// Ratio for flat sides (percentage)
define BLEND_B = 20;
// Blend range for transitions (percentage)
//int x, y;
//int abs_x, abs_y;
int max_val_B;
int blend_factor;
// Constants for octagon shape
define MAX_VAL = 100;
// Maximum stick value
define RATIO = 60;
// Ratio for flat sides (percentage)
int start_value = 10;
// Starting value for ratio_radius
int max_val_radius = 180;
int ratio_radius = 110;
int max_component;
int scale;
//int LX, LY;
//int scaled_x, scaled_y;
////////////////////////////////////////////////////////////////////////////////
// GLOBAL
////////////////////////////////////////////////////////////////////////////////
const int8 anglesMax[] = {
	// Index: 0..9
	100, 99, 98, 97, 96, 95, 94, 94, 93, 92,   // 10..19
	    91, 90, 89, 88, 87, 86, 85, 85, 84, 83,   // 20..29
	    82, 78, 70, 78, 82, 83, 84, 85, 85, 86,   // 30..39
	    87, 88, 89, 90, 91, 92, 93, 94, 94, 95,   // 40..44
	    96, 97, 98, 99, 100
}
;
int angle, radius;
const string INFO1  = "OCTAGONE";
const string INFO2 = "CONVEX";
const string LEFTP  = "PING";
const string RIGHTP = "AI ANGLE";
const string MAXRAD = "MAX RADIUS";
// Add this line for phase display
const string ANGLEP = "AGILE FREQ";
// Add this line for angle display
const string RADIUSP = "B SHOT PWR";
define RIGHT_MAG_THRESHOLD = 17;
// Random seed will be updated each frame
int randomSeed = 0;
// Global Variables
int rb_val = 0;
int lb_val = 0;
int ptime;
int virtmach = -9;
// Variables for random radius control
int random_update_timer;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;
int AngleInterval = 165;
int AI_VALUES[7];
int AI_VALUES_COUNT;
int current_index = 0;
// Virtual Machine Speed
init {
	AI_VALUES[0] = 160;
	AI_VALUES[1] = 165;
	AI_VALUES[2] = 170;
	AI_VALUES[3] = 180;
	AI_VALUES[4] = 190;
	AI_VALUES[5] = 200;
	AI_VALUES[6] = 150;
	AI_VALUES_COUNT = 7;
	}
init {
	AI_VALUES_COUNT = sizeof(AI_VALUES) / sizeof(AI_VALUES[0]);
	}
define PHASE_MULTIPLIER = 10;
int lt_counter;
// For LT oscillation
int going_up;
// For LT direction
int combo_active;
// Track if LT+RT is pressed
int rt_timer;
// For RT spam
int rb_timer;
// For RB timing
init {
	lt_counter = 50;
	// Start at 50 for LT
	going_up = 1;
	combo_active = 0;
	rt_timer = 0;
	rb_timer = 0;
	}
////////////////////////////////////////////////////////////////////////////////
// Global Variables (all int declarations outside functions)
////////////////////////////////////////////////////////////////////////////////
//int LX, LY;              // Raw stick inputs
//int scaled_x, scaled_y;  // Final outputs after shaping
int DEADZONE   = 30;
// Quadratische Deadzone
int MAX_INPUT  = 70;
// Maximaler Wert je Achse
int MAX_SUM    = 140;
// Sum(|x|+|y|) -> Grenze fürs Oktagon
// Helper globals for deadzone scaling
int sign, abs_val, output, sum, scale_factor;
// Helper globals for circular clamp
int mag_sq, limit_sq, mag;
// Variables for the integer square-root function
int iSqrtValue, iSqrtRes, iSqrtBit, iSqrtTemp;
int ls_last_press_time;
// Track time of last press
int ls_last_release_time;
// Track time of last release
int ls_is_double_press;
// Flag for double press detection
int ls_press_count = 0;
// Track number of presses within time window

define STICK_IDLE_TIMEOUT = 300;
define CYCLE_TIME_MS = 10;
define IDLE_CYCLES_THRESHOLD = STICK_IDLE_TIMEOUT / CYCLE_TIME_MS;
int last_lx = 0;
int last_ly = 0;
int idle_counter = 0;
int current_lx;
int current_ly;
int target_radius;

//int lt_counter;    // For LT oscillation
//int going_up;      // For LT direction
//int combo_active;  // Track if LT+RT is pressed
//int rt_timer;      // For RT spam
//int rb_timer;      // For RB timing

init {
    lt_counter = 50;  // Start at 50 for LT
    going_up = 1;
    combo_active = 0;
    rt_timer = 0;
    rb_timer = 0;
}

main {



/*
	if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(pressing);
    } else if(combo_running(pressing)) {
        combo_stop(pressing);
    }
*/
set_rgb(255,0,0)
	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_LEFT)) {
			load_slot (2);
					}
		set_val(XB1_LEFT,0);
			}
	if (get_val(XB1_VIEW) ) {
		if(event_release(XB1_RIGHT)) {
			load_slot (2);
					}
		set_val(XB1_RIGHT,0);
			}
	//   DISABLE / ENABLE ENTIRE SCRIPT
	if(get_ival(XB1_LT)){
		if(event_press(XB1_VIEW)){
			KS_EntireScript = !KS_EntireScript;
			f_set_notify (KS_EntireScript);
					}
		set_val(XB1_VIEW,0);
			}
	//   DISABLE / ENABLE ENTIRE SCRIPT
	if( !KS_EntireScript){
		set_rgb(0,255,255);
		// BLUE

		// Update the random timer
		if(random_update_timer) {
			random_update_timer -= get_rtime();
					}

    current_lx = get_val(XB1_LX);
    current_ly = get_val(XB1_LY);

    if (current_lx != last_lx || current_ly != last_ly) {
        idle_counter = 0;
        last_lx = current_lx;
        last_ly = current_ly;
    } else {
        if (idle_counter <= IDLE_CYCLES_THRESHOLD) {
             idle_counter = idle_counter + 1;
        }
    }

LT_RT_Oscillation();
		LX = get_val(XB1_LX);
		// Get stick values first
		LY = get_val(XB1_LY);
		// Only process stick inputs if both triggers are pressed or both triggers are released
		if((get_val(XB1_PL1) == 0 && get_val(XB1_PR1) == 0) || (get_val(XB1_PL1) > 0 && get_val(XB1_PR1) > 0)) {
			// Execute FAKE_SHOT combo when either X or LS is pressed and triggers are active
			if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {

				if(event_press(XB1_X) || event_press(XB1_LS)) {

					set_val(XB1_LT, 0);
					set_val(XB1_RT, 0);
					set_val(XB1_X, 0);
					combo_run(FAKE_SHOT);
									}
		}
			angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
			radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
			if(get_val(XB1_LS)) {
        		target_radius = 0;
        		if (idle_counter <= IDLE_CYCLES_THRESHOLD) {
            	target_radius = 9700;
        		}
        		set_polar2(POLAR_LS, get_polar(POLAR_LS, POLAR_ANGLE), target_radius);
							}
			else if(get_val(XB1_RS)) {
				set_val(XB1_RS, 0);
					RS_rt_pressed = get_ival(RS_TriggerControl_RT);
					RS_lt_pressed = get_ival(RS_TriggerControl_LT);

				RS_Trigger_Oscillation_Control();  // Oscillate when a trigger is pressed


				process_stick_movement();
				//set_polar2(POLAR_LS,get_polar(POLAR_LS,POLAR_ANGLE),9400);
			}
			else {
				// Apply set_polar2 with anglesMax when neither XB1_LS nor XB1_RS is active
				set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
							}
			vm_tctrl(virtmach)      // Polarisierungs-Werte abfragen
			    angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
			radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
			// Reset shared values at the start of each loop iteration
			rb_val = 0;
			lb_val = 0;
			// Handle radius-based LB behavior first
			if(radius < 2000) {
				if(get_ival(XB1_LB)) {
					set_val(XB1_LT, 100);
					set_val(XB1_RT, 100);
					set_val(XB1_LB, 0);
					if(!combo_running(LeftRightCombo)) {
						combo_run(LeftRightCombo);
											}
									}
				else {
					combo_stop(LeftRightCombo);
					set_val(XB1_LT, 0);
					set_val(XB1_RT, 0);
									}
							}
			// When radius >= 2000, we keep the original LB value
			else {
				if(get_ival(XB1_LB)) {
					lb_val = 100;
									}
				else {
					lb_val = 0;
									}
							}
			// Only handle X and Y and A buttons if triggers are not pressed
			if(!get_ival(XB1_LT) && !get_ival(XB1_RT)) {
			zone_pass();
				handle_rb_related_button(XB1_X);
				handle_rb_related_button(XB1_Y);
				handle_lb_related_button(XB1_A);
							}
			// **Directly handle XB1_RB press**
			if(get_ival(XB1_RB)) {
				rb_val = 100;
							}
			// Set RB based on aggregated value
			set_val(XB1_RB, rb_val);
			// Only set LB from lb_val if radius >= 2000
			if(radius >= 2000) {
				set_val(XB1_LB, lb_val);
							}

			// Finesse LB+B
			if(get_val(XB1_LB) && get_val(XB1_B)) {
				combo_run(finesse);
							}

			// Shots with B
			// Quick tap detection: If B button is released within 99ms
			if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
				combo_run(TapB);
				// Run quick tap combo
			}
			// Long press detection: If B button is held for 230ms or longer
			else if (get_ival(XB1_B) && get_ptime(XB1_B) >= ratio_radius) {
				set_val(XB1_B, 0);
				// Clear the button input
				if (event_press(XB1_B)) combo_run(PressB);
				// Run long press combo on initial press
			}
			//				}
			// Shots with RB+B
			if (get_val(XB1_RB)) {
				if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
					combo_run(TapB180);
									}
				else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {
					set_val(XB1_B,0);
					if (event_press(XB1_B)) combo_run(PressB99);
									}
							}
			if(get_ival(XB1_RT)){
				if(event_press(XB1_RIGHT)) {
					current_index = (current_index + 1) % AI_VALUES_COUNT;
					AngleInterval = AI_VALUES[current_index];
					on_the_fly_display(10, ANGLEP[0], AngleInterval);
					//print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
				}
				if(event_press(XB1_LEFT)) {
					current_index = (current_index - 1 + AI_VALUES_COUNT) % AI_VALUES_COUNT;
					AngleInterval = AI_VALUES[current_index];
					on_the_fly_display(10, ANGLEP[0], AngleInterval);
					//print(centerPosition(getStringLength(ANGLEP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, ANGLEP[0]);
				}
				set_val(PS4_RIGHT, 0);
				set_val(PS4_LEFT, 0);
							}
			if(get_ival(XB1_LT)){
				if(event_press(XB1_UP) && o22_MAX_VAL < 100) {
					o22_MAX_VAL += 2;
					on_the_fly_display(10, MAXRAD[0], o22_MAX_VAL);
									}
				if(event_press(XB1_DOWN) && o22_MAX_VAL > 1) {
					o22_MAX_VAL -= 2;
					on_the_fly_display(10, MAXRAD[0], o22_MAX_VAL);
									}
				set_val(PS4_UP, 0);
				set_val(PS4_DOWN, 0);
				// Ratio of radius
				if(event_press(XB1_RIGHT) && ratio_radius < start_value + 180) {
					ratio_radius += 10;
					on_the_fly_display(10, RADIUSP[0], ratio_radius);
									}
				if(event_press(XB1_LEFT) && ratio_radius > start_value) {
					ratio_radius -= 10;
					on_the_fly_display(10, RADIUSP[0], ratio_radius);
									}
				set_val(PS4_RIGHT, 0);
				set_val(PS4_LEFT, 0);
							}
		}

		// Entire Script ON/OFF
		else {
			set_rgb(255,0,255);
					}
			}	// End of Entire Script
// End of MAIN
}

function pass() {
	radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
	angle = get_polar(POLAR_LS, POLAR_ANGLE);
	if (radius < 2400) {
		set_polar(POLAR_LS, 0, 0);
		set_val(XB1_LB, 0);
			}
	else if (radius >= 2400 && radius < 8000) {
		set_val(XB1_LB, 100);
		if (!get_val(XB1_LS)) {
			// Only apply when XB1_LS is not active
			set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
					}
		else {
			set_polar2(POLAR_LS, angle, 8000);
					}
			}
	else if (radius >= 8000) {
		if (!get_val(XB1_LS)) {
			// Only apply when XB1_LS is not active
			set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
					}
		else {
			set_polar2(POLAR_LS, angle, 10000);
					}
			}
	if (!get_val(XB1_LS)) {
		// Only apply octagon mapping when XB1_LS is not active
		LX = get_val(XB1_LX);
		LY = get_val(XB1_LY);
		map_circular_octagon(LX, LY);
		set_val(XB1_LX, scaled_x);
		set_val(XB1_LY, scaled_y);
			}
	}
function handle_rb_related_button(int button) {
	if(get_ival(button)) {
		ptime = get_ptime(button);
		if(ptime < 250) {
			set_val(button, 100);
					}
		else if(ptime >= 250 && ptime <= 380) {
			set_val(button, 100);
			rb_val = 100;
			// Activate XB1_RB
			combo_run (stop_lb);
					}
		else {
			set_val(button, 0);
					}
			}
	else {
		set_val(button, 0);
			}
	}
combo stop_lb {
	wait(100);
	set_val(XB1_LB, 0);
	wait(100);
	}
function handle_lb_related_button(int button) {
	if(get_ival(button)) {
		ptime = get_ptime(button);
		if(ptime < 250) {
			set_val(button, 100);
			lb_val = 100;
					}
		else if(ptime >= 250 && ptime <= 380) {
			set_val(button, 100);
			lb_val = 100;
			// Activate XB1_LB
		}
		else {
			set_val(button, 0);
					}
			}
	else {
		set_val(button, 0);
			}
	}
// Long press combo - Activates when B is held for 230ms or longer
combo PressB {
	set_val(XB1_B, 100);
	// Set B button to full press (100%)
	wait(100);
	// Hold for 100ms then release
}
// Quick tap combo - Activates when B is released within 99ms
combo TapB {
	set_val(XB1_B, 0);
	// Release B button immediately
	wait(50);
	// Wait 230ms before allowing next input
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(440);
	set_val(XB1_LB, 100);
	wait(80);
	set_val(XB1_LB, 0);
	wait(40);
	set_val(XB1_LB, 100);
	wait(300);
	set_val(XB1_LB, 0);
	wait(250);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;
combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
	}
combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
	}
combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
	}
int UltimatePower;
combo OutSideBox_Finishing_cmb {
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100);
	wait(UltimatePower);
	/////
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
	}
int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);
	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
	}
function getPolar(Stick, AngleOrRadius) {
	if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
	return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));
	}
//=======================================
//  DISPLAY EDIT VALUE ON THE FLY
//=======================================
function on_the_fly_display (f_string, f_print, f_val){
	cls_oled(0);
	line_oled(1,18,127,18,1,1);
	print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);
	NumberToString(f_val, FindDigits(f_val));
	time_to_clear_screen  = 2000;
	}
combo CLEAR_SCREEN {
	wait(20);
	cls_oled(0);
	}
/*=================================================================
 Center X Function (Made By Batts)
=================================================================
*/
function centerPosition(f_chars,f_font) {
	return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
	}
int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
	set_rumble(Vibrate_type,100);
	wait(300);
	reset_rumble();
	wait(20);
	}
int data_indx;
/*
=================================================================
  NumberToString () (Made By Batts)
=================================================================
*/
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
	bufferIndex = 1;
	digitIndex = 10000;
	if(f_val < 0) {
		//--neg numbers
		putc_oled(bufferIndex,45);
		//--add leading "-"
		bufferIndex += 1;
		f_val = abs(f_val);
			}
	for(charIndex = 5;
	charIndex >= 1;
	charIndex--) {
		if(f_digits >= charIndex) {
			putc_oled(bufferIndex,(f_val / digitIndex) + 48);
			f_val %= digitIndex;
			bufferIndex ++;
			if(charIndex == 4) {
				putc_oled(bufferIndex,44);
				//--add ","
				bufferIndex ++;
							}
					}
		digitIndex /= 10;
			}
	puts_oled(10,38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
	}
int logVal;
function FindDigits(num) {
	logVal = 0;
	do {
		num /= 10;
		logVal++;
			}
	while (num);
	return logVal;
	}
int stringLength;
function getStringLength(offset) {
	stringLength = 0;
	do {
		offset++;
		stringLength++;
			}
	while (duint8(offset));
	return stringLength;
	}
function set_ds4_led(colour) {
	set_led(LED_1, duint8 (colour * 4));
	set_led(LED_2, duint8 ((colour * 4) + 1));
	set_led(LED_3, duint8 ((colour * 4) + 2));
	set_led(LED_4, duint8 ((colour * 4) + 3));
	}
int KS_EntireScript = FALSE;
function f_set_notify (f_val){
	if(f_val)Vibrate_type = RUMBLE_A;
	else     Vibrate_type = RUMBLE_B;
	combo_run(NOTIFY_cmb);
	}
function LED_Color(color) {
	for( data_indx = 0;
	data_indx < 3;
	data_indx++ ) {
		set_led(data_indx,duint8 ((color * 3) + data_indx));
			}
	}
int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {
	return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
	}
const string OFF   = "OFF";
const string ON    = "ON";
function defend(){
	// Check for LT+RT combo
	if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
		combo_active = 1;
			}
	else {
		combo_active = 0;
		// Reset everything when combo is released
		set_val(XB1_LT, get_val(XB1_LT));
		set_val(XB1_RT, get_val(XB1_RT));
		set_val(XB1_LB, get_val(XB1_LB));
		lt_counter = 50;
		rt_timer = 0;
		rb_timer = 0;
			}
	if(combo_active) {
		// Handle LT oscillation (50-100)
		set_val(XB1_LT, lt_counter);
		if(going_up) {
			lt_counter = lt_counter + 2;
			// Much slower oscillation
			if(lt_counter >= 100) {
				going_up = 0;
							}
					}
		else {
			lt_counter = lt_counter - 2;
			// Much slower oscillation
			if(lt_counter <= 50) {
				going_up = 1;
							}
					}
		// Handle RT timing (1 second on, 500ms off)
		rt_timer = rt_timer + 1;
		if(rt_timer <= 60) {
			// 1 second on (100 ticks)
			set_val(XB1_RT, 100);
					}
		else if(rt_timer <= 80) {
			// 500ms off (50 ticks)
			set_val(XB1_RT, 0);
					}
		else {
			rt_timer = 0;
			// Reset cycle
		}
		// Handle RB timing (3 seconds on, 1 second off)
		rb_timer = rb_timer + 1;
		if(rb_timer <= 210) {
			// 3 seconds on (300 ticks)
			set_val(XB1_LB, 100);
					}
		else if(rb_timer <= 220) {
			// 1 second off (100 ticks)
			set_val(XB1_LB, 0);
					}
		else {
			rb_timer = 0;
			// Reset cycle
		}
			}
	}

// Function to generate a pseudo-random number
function getRandomNumber() {
	randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
	return randomSeed;
	}
// Get random number between min and max
function getRandomRange(min, max) {
	return (getRandomNumber() % (max - min + 1)) + min;
	}
////////////////////////////////////////////////////////////////////////////////
// GLOBALE VARIABLEN
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// COMBOS
////////////////////////////////////////////////////////////////////////////////
combo LeftRightCombo {
	// 1) Linken Stick nach links
	set_polar(POLAR_LS, 0, 32000);
	//set_val(XB1_LX, -100);
	wait(AngleInterval);
	// 2) Linken Stick nach rechts
	set_polar(POLAR_LS, 180, 32000);
	//set_val(XB1_LX, 100);
	wait(AngleInterval);
	}
combo FAKE_SHOT{
	set_val(XB1_B, 100);
	vm_tctrl(0);
	wait(40);
	set_val(XB1_B, 100);
	set_val(XB1_A, 100);
	vm_tctrl(0);
	wait(60);
	set_val(XB1_B, 0);
	set_val(XB1_A, 100);
	vm_tctrl(0);
	wait(60);
	//Get_LS_Output = TRUE;
}
////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
//   - Must not declare local int variables here, so we use the global ones
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
	iSqrtValue = value;
	iSqrtRes   = 0;
	iSqrtBit   = 1 << 14;
	// 2^14 = 16384 (enough for up to ~20000)
// Shift down until bit <= value
	while(iSqrtBit > iSqrtValue) {
		iSqrtBit = iSqrtBit >> 2;
			}
	while(iSqrtBit != 0) {
		iSqrtTemp = iSqrtRes + iSqrtBit;
		if(iSqrtValue >= iSqrtTemp) {
			iSqrtValue = iSqrtValue - iSqrtTemp;
			iSqrtRes   = iSqrtRes + (iSqrtBit << 1);
					}
		iSqrtRes = iSqrtRes >> 1;
		iSqrtBit = iSqrtBit >> 2;
			}
	return iSqrtRes;
	}
////////////////////////////////////////////////////////////////////////////////
// Function: apply_one_axis_deadzone(val)
//   - Applies a square deadzone on a single axis: [DEADZONE..100] -> [0..100]
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(int val) {
	// Vorzeichen
	if(val >= 0) {
		sign = 1;
			}
	else {
		sign = -1;
			}
	abs_val = abs(val);
	// Innerhalb der Deadzone -> 0
	if(abs_val <= DEADZONE) {
		return 0;
			}
	// Begrenzen, falls > MAX_INPUT
	if(abs_val > MAX_INPUT) {
		abs_val = MAX_INPUT;
			}
	// Linear von [DEADZONE..MAX_INPUT] nach [0..MAX_INPUT] skalieren
	output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
	return (sign * output);
	}
// Hauptfunktion zum Erzeugen des konvexen Oktagons
function map_convex_octagon(int x, int y) {
	// 1) Square Deadzone pro Achse
	scaled_x = apply_one_axis_deadzone(x);
	scaled_y = apply_one_axis_deadzone(y);
	// 2) Sum-of-abs Clamp -> |x| + |y| <= 200
	sum = abs(scaled_x) + abs(scaled_y);
	if(sum > MAX_SUM) {
		scale_factor = (MAX_SUM * 1000) / sum;
		scaled_x = (scaled_x * scale_factor) / 1000;
		scaled_y = (scaled_y * scale_factor) / 1000;
			}
	// 3) X/Y zusätzlich auf ±100 beschränken
	if(scaled_x >  MAX_INPUT) scaled_x =  MAX_INPUT;
	if(scaled_x < -MAX_INPUT) scaled_x = -MAX_INPUT;
	if(scaled_y >  MAX_INPUT) scaled_y =  MAX_INPUT;
	if(scaled_y < -MAX_INPUT) scaled_y = -MAX_INPUT;
	}
function apply_octagonal_boundary(_stickX, _stickY) {
	// Get current stick values
	x = get_val(_stickX);
	y = get_val(_stickY);
	if(abs(x) > 0 || abs(y) > 0) {
		// Get absolute values
		abs_x = abs(x);
		abs_y = abs(y);
		// Find the larger component
		if(abs_x > abs_y) {
			max_component = abs_x;
					}
		else {
			max_component = abs_y;
					}
		// Calculate scale based on max component
		scale = MAX_VAL;
		if(max_component > 0) {
			// If we're closer to cardinal direction, reduce scale
			if(abs_x > abs_y * 2 || abs_y > abs_x * 2) {
				scale = RATIO;
							}
					}
		// Apply scaling to maintain direction
		x = (x * scale) / MAX_VAL;
		y = (y * scale) / MAX_VAL;
		// Set modified values
		set_val(_stickX, x);
		set_val(_stickY, y);
		return TRUE;
			}
	return FALSE;
	}
function butterfly() {
	// Get current stick values
	x = get_val(stickX);
	y = get_val(stickY);
	if(abs(x) > 0 || abs(y) > 0) {
		// Get absolute values
		abs_x = abs(x);
		abs_y = abs(y);
		// Calculate blend factor based on how close we are to diagonal
// When abs_x == abs_y, we're at diagonal (45 degrees)
		if(abs_x > abs_y) {
			blend_factor = (abs_y * 100) / abs_x;
					}
		else {
			blend_factor = (abs_x * 100) / abs_y;
					}
		// Sharp transition between sides and corners
		if(blend_factor > 100 - BLEND_B) {
			// Near diagonal (corner)
			max_val_B = MAX_VAL_B;
					}
		else if(blend_factor < BLEND_B) {
			// Near cardinal (side)
			max_val_B = (MAX_VAL_B * RATIO_B) / 100;
					}
		else {
			// Transition area - sharp blend
			max_val_B = (MAX_VAL_B * RATIO_B) / 100 +                      ((MAX_VAL_B - (MAX_VAL_B * RATIO_B) / 100) *                       (blend_factor - BLEND_B) / (100 - 2 * BLEND_B));
					}
		// Apply scaling while maintaining direction
		x = (x * max_val_B) / MAX_VAL_B;
		y = (y * max_val_B) / MAX_VAL_B;
		// Set the modified values
		set_val(stickX, x);
		set_val(stickY, y);
			}
	}
////////////////////////////////////////////////////////////////////////////////
// PROCESSING PIPELINE
////////////////////////////////////////////////////////////////////////////////
function process_stick_input() {
	// 1. Determine signs and absolute values
	if(LX >= 0) sign_x = 1;
	else sign_x = -1;
	if(LY >= 0) sign_y = 1;
	else sign_y = -1;
	abs_x = abs(LX);
	abs_y = abs(LY);
	// 2. Apply octagonal mapping
	L_inf = max(abs_x, abs_y);
	L_1 = abs_x + abs_y;
	// Scale L_1 for octagonal shape (improved scaling)
	scaled_L1 = (L_1 * 1024) / (1024 + INV_SQRT2_NUM);
	octNorm = max(L_inf, scaled_L1);
	// 3. Apply deadzone
	if(octNorm <= A_DEADZONE) {
		scaled_x = 0;
		scaled_y = 0;
		return;
			}
	// 4. Clamp maximum input
	if(octNorm > A_MAX_INPUT) {
		octNorm = A_MAX_INPUT;
			}
	// 5. Scale from [A_DEADZONE..A_MAX_INPUT] -> [0..A_MAX_INPUT]
	scaledNorm = (octNorm - A_DEADZONE) * A_MAX_INPUT / (A_MAX_INPUT - A_DEADZONE);
	// 6. Calculate acceleration factor
	accel_factor = calc_acceleration(scaledNorm);
	// 7. Apply sensitivity and acceleration
	sensitivity_scaled = (A_SENSITIVITY * accel_factor) / 100;
	// 8. Compute properly scaled outputs maintaining the octagonal shape
	raw_scaled_x = (abs_x * scaledNorm / octNorm) * sensitivity_scaled / 100;
	raw_scaled_y = (abs_y * scaledNorm / octNorm) * sensitivity_scaled / 100;
	// 9. Apply smoothing
	smooth_x = apply_smoothing(raw_scaled_x, prev_x);
	smooth_y = apply_smoothing(raw_scaled_y, prev_y);
	// 10. Restore signs and clamp to A_MAX_INPUT
	scaled_x = clamp(smooth_x * sign_x, -A_MAX_INPUT, A_MAX_INPUT);
	scaled_y = clamp(smooth_y * sign_y, -A_MAX_INPUT, A_MAX_INPUT);
	}
////////////////////////////////////////////////////////////////////////////////
// HELPER FUNCTIONS
////////////////////////////////////////////////////////////////////////////////
function calc_acceleration(int speed) {
	if(speed <= A_ACCEL_THRESHOLD) {
		return 100;
		// No acceleration below threshold
	}
	// Progressive acceleration above threshold
	return 100 + ((speed - A_ACCEL_THRESHOLD) * 50) / (A_MAX_INPUT - A_ACCEL_THRESHOLD);
	}
function apply_smoothing(int current, int previous) {
	return (current * (100 - A_SMOOTH_FACTOR) + previous * A_SMOOTH_FACTOR) / 100;
	}
function o22() {
	// Get current stick values
	x = get_val(o22_stickX);
	y = get_val(o22_stickY);
	// Compute absolute values
	o22_abs_x = abs(x);
	o22_abs_y = abs(y);
	// Calculate L-infinity (max) norm and L1 (Manhattan) norm
	if (o22_abs_x > o22_abs_y) {
		o22_L_inf = o22_abs_x;
			}
	else {
		o22_L_inf = o22_abs_y;
			}
	o22_L_1 = o22_abs_x + o22_abs_y;
	// Scale L1 norm for octagonal shape
	o22_scaled_L1 = (o22_L_1 * o22_INV_SQRT2_NUM) / o22_INV_SQRT2_DEN;
	// Octagonal norm is the maximum of o22_L_inf and o22_scaled_L1
	if (o22_L_inf > o22_scaled_L1) {
		o22_octNorm = o22_L_inf;
			}
	else {
		o22_octNorm = o22_scaled_L1;
			}
	// If the stick is moved (nonzero input)
	if (o22_octNorm > 0) {
		// Scale outputs while maintaining direction and apply o22_MAX_VAL scaling
		o22_output_x = (x * o22_MAX_VAL) / o22_octNorm;
		o22_output_y = (y * o22_MAX_VAL) / o22_octNorm;
		// Clamp outputs to o22_MAX_VAL limits
		if (o22_output_x > o22_MAX_VAL) o22_output_x = o22_MAX_VAL;
		if (o22_output_x < -o22_MAX_VAL) o22_output_x = -o22_MAX_VAL;
		if (o22_output_y > o22_MAX_VAL) o22_output_y = o22_MAX_VAL;
		if (o22_output_y < -o22_MAX_VAL) o22_output_y = -o22_MAX_VAL;
			}
	else {
		o22_output_x = 0;
		o22_output_y = 0;
			}
	// Rotate the normalized output by 22.5° clockwise.
// Rotation matrix for a clockwise rotation by θ is:
//   x' = x*cosθ + y*sinθ
//   y' = -x*sinθ + y*cosθ
	o22_rotated_x = ((o22_output_x * o22_COS22_NUM) + (o22_output_y * o22_SIN22_NUM)) / o22_ROT_DEN;
	o22_rotated_y = ((-o22_output_x * o22_SIN22_NUM) + (o22_output_y * o22_COS22_NUM)) / o22_ROT_DEN;
	// Set final rotated values to the stick outputs
	set_val(o22_stickX, o22_rotated_x);
	set_val(o22_stickY, o22_rotated_y);
	}
// Constants
define star_MAX_INPUT = 99;
define star_DEADZONE = 1;
define star_ARRAY_SIZE = 23;
// Variables
int star_octagonMax[star_ARRAY_SIZE];
// Array declaration
int star_x, star_y, star_mag_sq, star_mag;
int star_angle, star_index, star_extension;
int star_scaled_x, star_scaled_y;
int star_sign, star_abs_val, star_output;
int star_i;
// Loop counter
// Variables for star_intSqrt
int star_iSqrtValue;
int star_iSqrtRes;
int star_iSqrtBit;
int star_iSqrtTemp;
////////////////////////////////////////////////////////////////////////////////
// Function: star_intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
////////////////////////////////////////////////////////////////////////////////
function star_intSqrt(value) {
	star_iSqrtValue = value;
	star_iSqrtRes   = 0;
	star_iSqrtBit   = 1 << 14;
	// 2^14 = 16384 (enough for up to ~20000)
// Shift down until bit <= value
	while(star_iSqrtBit > star_iSqrtValue) {
		star_iSqrtBit = star_iSqrtBit >> 2;
			}
	while(star_iSqrtBit != 0) {
		star_iSqrtTemp = star_iSqrtRes + star_iSqrtBit;
		if(star_iSqrtValue >= star_iSqrtTemp) {
			star_iSqrtValue = star_iSqrtValue - star_iSqrtTemp;
			star_iSqrtRes   = star_iSqrtRes + (star_iSqrtBit << 1);
					}
		star_iSqrtRes = star_iSqrtRes >> 1;
		star_iSqrtBit = star_iSqrtBit >> 2;
			}
	return star_iSqrtRes;
	}
// Initialize array values in main
function init_octagon_values() {
	star_octagonMax[0] = 100;
	// 0°
	star_octagonMax[1] = 99;
	// 2°
	star_octagonMax[2] = 98;
	// 4°
	star_octagonMax[3] = 96;
	// 6°
	star_octagonMax[4] = 94;
	// 8°
	star_octagonMax[5] = 91;
	// 10°
	star_octagonMax[6] = 88;
	// 12°
	star_octagonMax[7] = 85;
	// 14°
	star_octagonMax[8] = 82;
	// 16°
	star_octagonMax[9] = 79;
	// 18°
	star_octagonMax[10] = 76;
	// 20°
	star_octagonMax[11] = 74;
	// 22°
	star_octagonMax[12] = 72;
	// 24°
	star_octagonMax[13] = 70;
	// 26°
	star_octagonMax[14] = 69;
	// 28°
	star_octagonMax[15] = 68;
	// 30°
	star_octagonMax[16] = 67;
	// 32°
	star_octagonMax[17] = 66;
	// 34°
	star_octagonMax[18] = 66;
	// 36°
	star_octagonMax[19] = 65;
	// 38°
	star_octagonMax[20] = 65;
	// 40°
	star_octagonMax[21] = 65;
	// 42°
	star_octagonMax[22] = 65;
	// 44°
}
function apply_deadzone(int val) {
	if(val >= 0) {
		star_sign = 1;
			}
	else {
		star_sign = -1;
			}
	star_abs_val = abs(val);
	if(star_abs_val <= star_DEADZONE)        return 0;
	star_output = ((star_abs_val - star_DEADZONE) * star_MAX_INPUT) / (star_MAX_INPUT - star_DEADZONE);
	if(star_output > star_MAX_INPUT)        star_output = star_MAX_INPUT;
	return star_sign * star_output;
	}
function star_map_convex_octagon(int x, int y) {
	// Apply deadzone first
	star_scaled_x = apply_deadzone(x);
	star_scaled_y = apply_deadzone(y);
	// Calculate magnitude
	star_mag_sq = star_scaled_x * star_scaled_x + star_scaled_y * star_scaled_y;
	if(star_mag_sq == 0)        return;
	star_mag = star_intSqrt(star_mag_sq);
	// Get current angle (0-360)
	star_angle = get_ipolar(POLAR_LS, POLAR_ANGLE);
	// Get index into extension table (normalize to 0-45 degrees)
	star_index = star_angle % 45;
	if(star_index > 22) {
		star_index = 45 - star_index;
			}
	// Apply convex octagonal boundary
	star_extension = star_octagonMax[star_index];
	star_scaled_x = (star_scaled_x * star_extension) / 80;
	star_scaled_y = (star_scaled_y * star_extension) / 80;
	}
init {
	// Initialize the octagon values
	init_octagon_values();
	}
// Global arrays declared without inline initialization
int cosTable[36];
int sinTable[36];
int init_done = 0;
// Global temporary variable for use inside functions
int temp_index;
// Global variables for octagon mapping.
//int angle;
int cos_val;
int sin_val;
int abs_cos;
int abs_sin;
//int L_inf;
int L1;
//int scaled_L1;
int norm;
int out_x;
int out_y;
int rot_x;
int rot_y;
// Function to initialize the lookup tables.
function init_lookup_tables() {
	if (init_done == 1) {
		return;
			}
	cosTable[0] = 1000;
	cosTable[1] = 984;
	cosTable[2] = 940;
	cosTable[3] = 866;
	cosTable[4] = 766;
	cosTable[5] = 642;
	cosTable[6] = 500;
	cosTable[7] = 342;
	cosTable[8] = 173;
	cosTable[9] = 0;
	cosTable[10] = -173;
	cosTable[11] = -342;
	cosTable[12] = -500;
	cosTable[13] = -642;
	cosTable[14] = -766;
	cosTable[15] = -866;
	cosTable[16] = -940;
	cosTable[17] = -984;
	cosTable[18] = -1000;
	cosTable[19] = -984;
	cosTable[20] = -940;
	cosTable[21] = -866;
	cosTable[22] = -766;
	cosTable[23] = -642;
	cosTable[24] = -500;
	cosTable[25] = -342;
	cosTable[26] = -173;
	cosTable[27] = 0;
	cosTable[28] = 173;
	cosTable[29] = 342;
	cosTable[30] = 500;
	cosTable[31] = 642;
	cosTable[32] = 766;
	cosTable[33] = 866;
	cosTable[34] = 940;
	cosTable[35] = 984;
	sinTable[0] = 0;
	sinTable[1] = 173;
	sinTable[2] = 342;
	sinTable[3] = 500;
	sinTable[4] = 642;
	sinTable[5] = 766;
	sinTable[6] = 866;
	sinTable[7] = 940;
	sinTable[8] = 984;
	sinTable[9] = 1000;
	sinTable[10] = 984;
	sinTable[11] = 940;
	sinTable[12] = 866;
	sinTable[13] = 766;
	sinTable[14] = 642;
	sinTable[15] = 500;
	sinTable[16] = 342;
	sinTable[17] = 173;
	sinTable[18] = 0;
	sinTable[19] = -173;
	sinTable[20] = -342;
	sinTable[21] = -500;
	sinTable[22] = -642;
	sinTable[23] = -766;
	sinTable[24] = -866;
	sinTable[25] = -940;
	sinTable[26] = -984;
	sinTable[27] = -1000;
	sinTable[28] = -984;
	sinTable[29] = -940;
	sinTable[30] = -866;
	sinTable[31] = -766;
	sinTable[32] = -642;
	sinTable[33] = -500;
	sinTable[34] = -342;
	sinTable[35] = -173;
	init_done = 1;
	}
// Function to return cosine for an angle in degrees using the lookup table.
function cos_deg(int ang) {
	init_lookup_tables();
	// Ensure the table is filled.
	while (ang < 0) {
		ang += 360;
			}
	ang = ang % 360;
	temp_index = (ang + 5) / 10;
	// Round to nearest multiple of 10.
	temp_index = temp_index % 36;
	return cosTable[temp_index];
	}
// Function to return sine for an angle in degrees using the lookup table.
function sin_deg(int ang) {
	init_lookup_tables();
	// Ensure the table is filled.
	while (ang < 0) {
		ang += 360;
			}
	ang = ang % 360;
	temp_index = (ang + 5) / 10;
	// Round to nearest multiple of 10.
	temp_index = temp_index % 36;
	return sinTable[temp_index];
	}
function process_stick_movement() {
	x = get_val(stickX);
	y = get_val(stickY);
	if (abs(x) > 20 || abs(y) > 20) {
		zone = get_zone(x, y);
		cos_a = cos_table[zone];
		sin_a = sin_table[zone];
		// Elliptical scaling calculations
		rx = (x * cos_a + y * sin_a) / 100;
		ry = (-x * sin_a + y * cos_a) / 100;
		rx = (rx * X_RATIO) / 100;
		ry = (ry * Y_RATIO) / 100;
		x = (rx * cos_a - ry * sin_a) / 100;
		y = (rx * sin_a + ry * cos_a) / 100;
			}
	else {
		// When stick is centered, output neutral values
		x = 0;
		y = 0;
			}
	set_val(stickX, x);
	set_val(stickY, y);
	}

// ===========================
//   Trigger_Oscillation_Control_RT
// ===========================
// Oscillates RT value between 70 and 100.
function Trigger_Oscillation_Control_RT() {
    // Call get_rtime() once and store the value to track elapsed time
    triggerTimer_RT = triggerTimer_RT + get_rtime();

    // Ensure triggerValue_RT stays within valid range
    if (triggerValue_RT < 80) { triggerValue_RT = 80; }
    if (triggerValue_RT > 100) { triggerValue_RT = 100; }

    // Check if it's time to update the triggerValue
    if (triggerTimer_RT >= speedControl) {
        triggerTimer_RT = triggerTimer_RT - speedControl;

        // Update triggerValue_RT based on direction
        if (increasing_RT) {
            triggerValue_RT = triggerValue_RT + 1;
            if (triggerValue_RT >= 100) {
                triggerValue_RT = 100;
                increasing_RT = FALSE;
            }
        } else {
            triggerValue_RT = triggerValue_RT - 1;
            if (triggerValue_RT <= 80) {
                triggerValue_RT = 80;
                increasing_RT = TRUE;
            }
        }
    }
    // Note: set_val is now handled in the main loop
}

// ===========================
//   Trigger_Oscillation_Control_LT
// ===========================
// Oscillates LT value between 70 and 100.
function Trigger_Oscillation_Control_LT() {
    // Call get_rtime() once and store the value to track elapsed time
    triggerTimer_LT = triggerTimer_LT + get_rtime();

    // Ensure triggerValue_LT stays within valid range
    if (triggerValue_LT < 80) { triggerValue_LT = 80; }
    if (triggerValue_LT > 100) { triggerValue_LT = 100; }

    // Check if it's time to update the triggerValue
    if (triggerTimer_LT >= speedControl) {
        triggerTimer_LT = triggerTimer_LT - speedControl;

        // Update triggerValue_LT based on direction
        if (increasing_LT) {
            triggerValue_LT = triggerValue_LT + 1;
            if (triggerValue_LT >= 100) {
                triggerValue_LT = 100;
                increasing_LT = FALSE;
            }
        } else {
            triggerValue_LT = triggerValue_LT - 1;
            if (triggerValue_LT <= 80) {
                triggerValue_LT = 80;
                increasing_LT = TRUE;
            }
        }
    }
    // Note: set_val is now handled in the main loop
}

// ===========================
// Reset_Trigger_Oscillation_RT
// ===========================
// Resets the RT triggerValue and direction to the initial state.
function Reset_Trigger_Oscillation_RT() {
    triggerValue_RT = 80;     // Reset RT to initial state
    increasing_RT = TRUE;     // Reset RT to start increasing
    triggerTimer_RT = 0;      // Reset RT timer
}

// ===========================
// Reset_Trigger_Oscillation_LT
// ===========================
// Resets the LT triggerValue and direction to the initial state.
function Reset_Trigger_Oscillation_LT() {
    triggerValue_LT = 80;    // Reset LT to initial state (start at max)
    increasing_LT = TRUE;    // Reset LT to start decreasing
    triggerTimer_LT = 0;      // Reset LT timer
}


function RS_Trigger_Oscillation_Control() {


    // Call get_rtime() once and store the value to track elapsed time
    RS_triggerTimer = RS_triggerTimer + get_rtime();

    // Ensure triggerValue_RT stays within valid range at all times
    if (RS_triggerValue_RT < 90) {
        RS_triggerValue_RT = 90;  // Prevent triggerValue_RT from going below 70
    }
    if (RS_triggerValue_RT > 100) {
        RS_triggerValue_RT = 100; // Prevent triggerValue_RT from exceeding 100
    }

    // Check if it's time to update the triggerValue
    if (RS_triggerTimer >= speedControl) {
        // Update based on speedControl
        RS_triggerTimer = RS_triggerTimer - speedControl;

        // Update triggerValue_RT based on direction (increase or decrease)
        if (RS_increasing) {
            RS_triggerValue_RT = RS_triggerValue_RT + 1;  // Increase triggerValue_RT
            if (RS_triggerValue_RT >= 100) {
                RS_triggerValue_RT = 100;    // Cap at 100
                RS_increasing = FALSE;    // Reverse direction to decrease
            }
        } else {
            RS_triggerValue_RT = RS_triggerValue_RT - 1;  // Decrease triggerValue_RT
            if (RS_triggerValue_RT <= 90) {
                RS_triggerValue_RT = 90;     // Bottom limit at 70
                RS_increasing = TRUE;     // Reverse direction to increase
            }
        }
    }

    // Calculate opposite value for LT (when RT is 100, LT is 70; when RT is 70, LT is 100)
    RS_triggerValue_LT = 190 - RS_triggerValue_RT;

    // Apply the oscillation to the triggers
    set_val (RS_TriggerControl_RT, RS_triggerValue_RT);
    set_val (RS_TriggerControl_LT, RS_triggerValue_LT);
}

// ===========================
// RS_Reset_Trigger_Oscillation
// ===========================
// Resets the triggerValue and direction to the initial state when the triggers are released.
function RS_Reset_Trigger_Oscillation() {
    RS_triggerValue_RT = 90;     // Reset RT to initial state
    RS_increasing = TRUE;      // Reset to start increasing
    RS_triggerTimer = 0;       // Reset timer
    // LT value is implicitly reset via the calculation in RS_Trigger_Oscillation_Control
}

define zone_MAX_RADIUS = 28000;
define zone_REDUCED_RADIUS = 20000;

int zone_radius;
int zone_max_allowed_radius;

function zone_pass() {
    // Only apply zone pass if XB1_A, XB1_Y, or XB1_X are active
    if(get_ival(XB1_A) || get_ival(XB1_Y) || get_ival(XB1_X)) {
        // Store the zone_radius value to avoid repeated function calls
        zone_radius = get_polar(POLAR_LS, POLAR_RADIUS);

        // Check if either LT or RT is pressed
        if(get_ival(XB1_LT) || get_ival(XB1_RT)) {
            zone_max_allowed_radius = zone_MAX_RADIUS;
        } else {
            zone_max_allowed_radius = zone_REDUCED_RADIUS;
        }

        set_polar(POLAR_LS,
            zone_quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), zone_AngleInterval_2),
            min(zone_calculate_radius(), zone_max_allowed_radius) // Ensure zone_radius is within allowed limit
        );
    }
    // If none of the specified buttons are pressed, do nothing (pass through original stick values)
}

define zone_AngleInterval_2 = 16;    // Changed from 12 to 30 for 16 zones
// This defines the number of zones.  A value of 16 creates 16 zones.

function zone_quantize_angle(angle, interval) {
    // This function quantizes the angle into discrete zones.
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

function zone_calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)); // Calculate zone_radius using Pythagorean theorem
}

combo TEST_LE {

	move_right ();
	wait(100);
	move_down ();
	wait(100);
	move_left ();
	wait(100);
	wait(1000);

}


function move_down() {
	set_polar(POLAR_RS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30800);
}

function move_up() {
	set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 30800);
}

function move_left() {
	set_polar(POLAR_RS, 270 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_right() {
	set_polar(POLAR_RS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_down_45() {
	set_polar(POLAR_RS, 135 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30800);
}

function move_up_45() {
	set_polar(POLAR_RS, 315 - get_polar(POLAR_LS, POLAR_ANGLE), 30800);
}

function move_left_45() {
	set_polar(POLAR_RS, 225 - get_ipolar(POLAR_LS, POLAR_ANGLE), 30800);
}

function move_right_45() {
	set_polar(POLAR_RS, 45 - get_polar(POLAR_LS, POLAR_ANGLE), 30800);
}


// Combo that activates when LT and RT are pressed together
function LT_RT_Oscillation (){
    // Detect when both LT and RT are pressed
    if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
        combo_active = 1;

        // Handle LT oscillation (50-100)
        set_val(XB1_LT, lt_counter);
        if(going_up) {
            lt_counter = lt_counter + 0.75;  // Much slower oscillation (reduced from 1.5)
            if(lt_counter >= 100) {
                going_up = 0;
            }
        } else {
            lt_counter = lt_counter - 0.75;  // Much slower oscillation (reduced from 1.5)
            if(lt_counter <= 50) {
                going_up = 1;
            }
        }

        // Handle RT timing (2 seconds on, 1 second off)
        rt_timer = rt_timer + 1;
        if(rt_timer <= 320) {  // 2 seconds on (increased from 260)
            set_val(XB1_RT, 100);
        } else if(rt_timer <= 380) {  // 1 second off (increased from 280)
            set_val(XB1_RT, 0);
        } else {
            rt_timer = 0;  // Reset cycle
        }

        // Handle LB timing (4 seconds on, 2 seconds off)
        rb_timer = rb_timer + 1;
        if(rb_timer <= 280) {  // 4 seconds on (increased from 210)
            set_val(XB1_LB, 100);
        } else if(rb_timer <= 320) {  // 2 seconds off (increased from 220)
            set_val(XB1_LB, 0);
        } else {
            rb_timer = 0;  // Reset cycle
        }
    } else {
        // When combo is released
        combo_active = 0;
        // Reset everything
        set_val(XB1_LT, get_val(XB1_LT));
        set_val(XB1_RT, get_val(XB1_RT));
        set_val(XB1_LB, get_val(XB1_LB));
        lt_counter = 50;
        rt_timer = 0;
        rb_timer = 0;
    }
} ����������������