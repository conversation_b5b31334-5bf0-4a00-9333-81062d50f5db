// Script was generated with < FIFA 22 S.G.I > ver. 19.6 Date :12.05.21 Time: 22:17:04
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
  \__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
   $$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
   $$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
   $$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
   $$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
   $$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
   $$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
   $$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
   \__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//====================================================
// DEVICE :  Cronus ZEN device 
//====================================================
//====================================================
// YOUR BUTTON LAYOUT : CUSTOM
//====================================================
define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MODIFIER      = XB1_LB;     
define CrossBtn      = XB1_X; // Cross Btn 
define ThroughBall   = XB1_Y; // Through Ball Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL       =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
define FOUR_TOUCH_TURN_SKILLS         =47; // FIFA 22
define SKILLED_BRIDGE_SKILL           =48; // FIFA 22
define SCOOP_TURN_FAKE_SKILL          =49; // FIFA 22
define BALL_ROLL_STEP_OVER_SKILL      =50; // FIFA 22
define CANCELED_4_TOUCH_TURN_SKILL    =51; // FIFA 22
define FAKE_SHOT_CANCEL_SKILL         =52; // FIFA 22
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int flick_rs; 
int RS_Sens  = 50
                                               
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
// PADDLES
	if (get_val(XB1_PL2)) {
			set_val(XB1_LB,100);
			set_val(XB1_RB,100);
			set_val(XB1_Y,100);
		}
		
		if (get_val(XB1_PR2)) {
			set_val(XB1_LB,100);
			set_val(XB1_A,100);
		}

if (get_val(XB1_PL1)){set_val(XB1_LB,100); }
//if (get_val(XB1_PL1)){combo_run(Timed_Finesse_Finish) }
if (event_press(XB1_PR1)){combo_run(FAKE_SHOT2) }                                                 
                                                  
    //--------------------------------------------------------------
    //  turn ON Penalty  hold  L1 and press OPTIONS 
    if(get_val(PS4_L1)){                      
        if(event_press(PS4_OPTIONS)){             
            onoff_penalty = !onoff_penalty;   
            f_set_notify(onoff_penalty);           
        }                                         
       set_val(PS4_OPTIONS,0);                    
    }                                              
                                                   
	  //-----------------------------------------  
	  // ON / OFF FREE KICK MODE                   
	  // hold L1/LB and press SHARE/VIEW           
	  if(get_val(PS4_L1)){                          
        if(event_press(PS4_SHARE)){             
            onoff_FK = !onoff_FK;               
            f_set_notify(onoff_FK);           
        }                                       
       set_val(PS4_SHARE,0);                    
    }                                           
    //----------------------------------------- 
  if(onoff_penalty || onoff_FK) {// Penalties_FKeecks
       vm_tctrl(0); 
       ////////////////////////////////  
       // LED color indication           
       if(onoff_FK)        colorled(2,0,2,0);
       else if(onoff_penalty)   colorled(0,0,0,2); 
    if(onoff_FK){ f_FREE_KICK ();  }             
    if(onoff_penalty){ fPenalties ();  }  
  } else { // all other code
       ////////////////////////////////  
       // LED color indication           
       colorled(2,0,0,0);
 vm_tctrl(-8); // Run the VM every  2 ms - XBOX X/S 
				if(abs(get_val(PS4_RX) <12 ) && abs(!get_val(PS4_RX) <12)){
					vm_tctrl(-6); // USER choosed VM Speed From S.G 
					// Fixes RS switching when defending , coz higher vm = very high RS sens 
					//which lead to choose incorrect players sometimes 
                }
    //========================================
    // *** LEFT STICK DRIBBLING ***
    //========================================
        if (get_val(SprintBtn)){
            sensitivity(PS4_LX, NOT_USE, LS_Sprint_Sens);
            sensitivity(PS4_LY, NOT_USE, LS_Sprint_Sens);
        }
        if (!get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(PassBtn) && !get_val(ThroughBall) && !get_val(ShotBtn)  ){
            sensitivity(PS4_LX, NOT_USE, LS_Sens);
            sensitivity(PS4_LY, NOT_USE, LS_Sens);
        }
         
	///////////////////////////////////// 
	// CHIP SHOT                 
	if(event_press(XB1_RS)){ 
    vm_tctrl(0);            
		combo_run(CHIP_SHOT);   
	}                       
	set_val(XB1_LS,0); 
                       
                                                                
    //========================================================= 
    //  Timed Finesse Finish                                    
    //========================================================= 
    if(get_val(FinesseShot)){ 
	      if(event_press(ShotBtn) ){ 
            vm_tctrl(0);           
		        combo_run(Timed_Finesse_Finish ); 
	      } 
         set_val(ShotBtn,0);
    } 
    if(abs(get_val(MOVE_X))> 45 || abs(get_val(MOVE_Y))> 45){
        calc_zone (); 
        LX = ZONE_P[zone_p][0];
        LY = ZONE_P[zone_p][1];
    }
    //----------------------------------------------------------- 
                                      
            if(!get_val(XB1_RS) &&  !get_val(PaceCtrol) && !get_val(SprintBtn) && !get_val(FinesseShot)) { // all Skills mode ){ 
                if((abs(get_val(SKILL_STICK_X))> 45 || abs(get_val(SKILL_STICK_Y))> 45) && !flick_rs){ // getting RS zones
                    flick_rs = TRUE;
                    calc_RS() ;
                    RS_X = ZONE_P[zone_RS][0];
                    RS_Y = ZONE_P[zone_RS][1];
                    f_auto_skill_menu();
                }             
                set_val(SKILL_STICK_X,0); 
                set_val(SKILL_STICK_Y,0);
            }
            //--- reset when RS is release
            if(abs(get_ival(SKILL_STICK_X))< 20 && abs(get_ival(SKILL_STICK_Y))< 20){  
                flick_rs = FALSE;                                                                            
            } 
                                                
      if(ACTIVE == FLAIR_ROULETTE_SKILL && combo_running(ROULETTE)) set_val(PlayerRun,100);// L1
      if(ACTIVE == THREE_TOUCH_ROULETTE_SKILL && combo_running(TORNADO_SPIN)) set_val(PaceCtrol,100);// + L1
    //========================================
    // *** DYNAMIC FINISHING ***
    //========================================
    if(Dynamic_Finish_onoff) f_dynamic_finish (); 
    //===============================================
    //    GRROUND PASSES  MIN / MAX
    //===============================================
    if(!get_val(PaceCtrol)){    
        if( !get_val(SprintBtn)){
	        if(get_val(PassBtn)){
	            ground_pass_timer += get_rtime();
	        }
	        if(event_release(PassBtn)){
	            if( ground_pass_timer < Ground_Pass_MIN ){
	                GP_difference = Ground_Pass_MIN - ground_pass_timer;
	                combo_run(Ground_Pass_MIN_cmb);                     
                }else{
                    if(DoubletapGroundPass) combo_run(DoubleTapGroundPass_cmb);
                }
	            ground_pass_timer = 0;
	        }
        }
    }// PaceCtrol
    //===============================================
    //    TROUGH PASSES  MIN / MAX
    //===============================================
    if(!get_val(PaceCtrol)){    
	    if(get_val(ThroughBall)){
	    	trough_pass_timer += get_rtime();
	    }
	    if(event_release(ThroughBall)){
		    	if(trough_pass_timer < Trough_Pass_MIN){
		    		TroughP_difference = Trough_Pass_MIN - trough_pass_timer;
		    		
		    		combo_run(Trough_Pass_MIN_cmb);
		    	}else{
		    		if(DoubleTapTroughPass) combo_run(DOUBLE_TAP_TROUGH_cmb);
		    	}
	    	trough_pass_timer = 0;
	    }
   } // PaceCtrol
    // all Skills mode                
  }// all other code
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function f_dynamic_finish () { 
    if(event_release(CrossBtn)){
        cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1400;
    }
    
    if(cross_timer){
        cross_timer -= get_rtime();
    }
    
    if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && cross_timer <= 0 && !get_val(XB1_PR1) && !get_val(XB1_PR2) && !get_val(XB1_PL1) && !get_val(XB1_PL2)){
            set_val(ShotBtn,0);
            INSIDE_BOX_AIM();     
            if( after_sprint_timer > 225  ) {
                UltimatePower = random(205,209 ) ; 
                drval=0;
                combo_restart(Dynamic_Shoot); 
            }
            if( after_sprint_timer <= 0  ) {
                UltimatePower = 235 ;
                drval = 100;
                combo_restart(Dynamic_Shoot); 
            }
        }
    } 
    /// FakeShot Support avoid Conflictions
    if ( combo_running(Dynamic_Shoot) && (( get_val(PassBtn) || get_val(PlayerRun) ) )  ) {  
        combo_stop(Dynamic_Shoot);
    }
  
}

////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
int cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;
int Dynamic_Finish_onoff = TRUE;

combo Dynamic_Shoot {
    set_val(ShotBtn, 100);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(UltimatePower);
    
    set_val(PlayerRun, 0);
    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM();
    wait(50);
    
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}
function INSIDE_BOX_AIM() { 
    // Moving to UP
    if((get_val(PS4_LX) > -30 && get_val(PS4_LX) <30) && get_val(PS4_LY) < -35 ) // UP
    {  
        LA (0, -100);
    }
    // Moving to DOwn     
    if((get_val(PS4_LX) > -30 && get_val(PS4_LX) <30) && get_val(PS4_LY) > 35 ) // Down
    {  
        LA (0, 100);
    }
    // Moving to Right     
    if((get_val(PS4_LY) > -30 && get_val(PS4_LY) <30) && get_val(PS4_LX) > 35 ) // Right
    { 
        LA (100, 0);
    }
    // Moving to LEFT     
    if((get_val(PS4_LY) > -30 && get_val(PS4_LY) <30) && get_val(PS4_LX) < -35 ) // Left
    {  
        LA (-100, 0);
    }
    // Moving to UP-Right
    if(get_val(PS4_LX) > 30  && get_val(PS4_LY) < -50 ) // UP-Right
    {  
        LA (100, -100);
    }
    // Moving to Down-Right     
    if(get_val(PS4_LX) > 30 && get_val(PS4_LY) > 50 ) // Down-Right
    {   
        LA (100, 100);
    }
    // Moving to UP-Left    
    if(get_val(PS4_LX) < -30 && get_val(PS4_LY) < -50)  // UP-Left
    {   
        LA (-100, -100);
    }
    // Moving to Down-Left     
    if(get_val(PS4_LX) < -30 && get_val(PS4_LY) > 50) // Down-Left
    {   
        LA (-100, 100);
    }    
 }
       
int ground_pass_timer; 
int Ground_Pass_MIN = 80;
int Ground_Pass_MAX = 250;
int GP_difference;
int DoubletapGroundPass = FALSE;
int low_driven_pass = FALSE;
combo Ground_Pass_MIN_cmb {
    if(low_driven_pass)set_val(FinesseShot,100); 
	set_val(PassBtn,100);
	wait(GP_difference);
	set_val(PassBtn,  0);
	wait(30);
	if(DoubletapGroundPass){
		set_val(PassBtn,100);
    }
    wait(60);
}
combo DoubleTapGroundPass_cmb {
    set_val(PassBtn,  0);
    wait(30);
    set_val(PassBtn,100);
    wait(60);    
}

//=================================
int trough_pass_timer; 
int Trough_Pass_MIN = 80;
int Trough_Pass_MAX = 300;
int TroughP_difference;
int DoubleTapTroughPass = TRUE;
int trough_start;

combo Trough_Pass_MIN_cmb {
	set_val(ThroughBall,100);
	wait(TroughP_difference);
	if(DoubleTapTroughPass){
		set_val(ThroughBall,100);
	}
	wait(60);
}
combo DOUBLE_TAP_TROUGH_cmb {
	set_val(ThroughBall,  0);
	wait(30);
	set_val(ThroughBall,100);
	wait(60);
}

int time_to_dblclick = 300
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
combo CHIP_SHOT {
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    set_val(PS4_L3,100);
    wait( 80);
    set_val(ShotBtn,100);
    set_val(PlayerRun,100);
    wait(110);
}
////////////////////////////////////////////////////////////////
// VARIABLES  for FREE KICK MOD 
//-------------------------------------------- 
define TOP_SPIN       = 1;                     
define SIDE_SPIN      = 2;                     
define KNUCKLEBALL_FK = 3;                     
define spin_time      = 80;                    
//-------------------------------------------- 
                                  
int side_dir   = 100;                          
int FK_mode    = TOP_SPIN;

int FK_Shot_Power;                          
int FK_LX;
int FK_LY;

function f_FREE_KICK (){
vm_tctrl(0);
	//===============================
    // SIDE SPIN
    //===============================
    if (get_val(XB1_LT) ){
        if (event_press(PS4_RIGHT)){
            FK_LX=76 ; FK_LY=-40 ;
            FK_mode = SIDE_SPIN;                          
            side_dir =  100;   
            FK_Shot_Power = 500;
            combo_run(FreeKick_Menu);
        } 
        if (event_press(PS4_LEFT)){
            FK_LX=-76 ; FK_LY=-40 ;
            FK_mode = SIDE_SPIN;                          
            side_dir =  -100;
            FK_Shot_Power = 500;
            combo_run(FreeKick_Menu);                        
        } 
    }
    //===============================
    // TOP SPIN
    //===============================
    if (get_val(XB1_RT) ){
        if (event_press(PS4_RIGHT)){
            FK_LX= 28 ; FK_LY= -88 ;
            FK_mode = TOP_SPIN ;                         
            side_dir =  -100; 
            FK_Shot_Power = 500;
            combo_run(FreeKick_Menu);                        
        } 
        if (event_press(PS4_LEFT)){
            FK_LX=-28 ; FK_LY=-88;
            FK_mode = TOP_SPIN ;                         
            side_dir =  -100;  
            FK_Shot_Power = 500;        
            combo_run(FreeKick_Menu);                        
        } 
    }
    //===============================
    // KNUCKLEBALL
    //===============================
    if (get_val(XB1_RB) ){
        //--- RIGHT
        if (event_press(PS4_RIGHT)){
            FK_LX=63 ; FK_LY=-35;
            FK_mode = KNUCKLEBALL_FK;                     
            FK_Shot_Power = 460;               
            combo_run(FreeKick_Menu); 
        } 
        //--- LEFT
        if (event_press(PS4_LEFT)){
            FK_LX=-63 ; FK_LY=-35;
            FK_mode = KNUCKLEBALL_FK;                     
            FK_Shot_Power = 460;               
            combo_run(FreeKick_Menu); 
        } 
    }
    set_val(PS4_UP,   0);                       
    set_val(PS4_DOWN, 0);                       
    set_val(PS4_LEFT, 0);                      
    set_val(PS4_RIGHT,0);                           
    set_val(XB1_RT,0);
    set_val(XB1_LT,0);
    set_val(XB1_RB,0);
}


combo FreeKick_Menu {  
    LA(FK_LX,FK_LY);
    wait(600);
    LA(FK_LX,FK_LY);
    //set_val(PS4_RY,100);
    set_val(ShotBtn,100);
    wait(FK_Shot_Power);
    LA(FK_LX,FK_LY);
    wait(330);                                           
    /////////////////////////////////////////////////   
    //  FREE KICK MODE                                  
    if(FK_mode == TOP_SPIN )  combo_run(TOP_SPIN_FK);    
    if(FK_mode == SIDE_SPIN ) combo_run(SIDE_SPIN_FK);    
    if(FK_mode == KNUCKLEBALL_FK ) combo_run(KNUCKLEBALL);
}                                                       
//--------------------------------------------------- 
combo TOP_SPIN_FK  {                                  
    RA_ZERO();                                  
    wait(spin_time);                                           
    RA(0,100);  // DOWN                                
    wait(spin_time);                                           
    RA_ZERO();                                
    wait(spin_time);                                           
    RA(0,-100); // UP                                
    wait(spin_time); 
    wait(2000);
    //--------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                                                     
//--------------------------------------------------- 
combo SIDE_SPIN_FK  {                                 
    RA(0,100);   // DOWN                                
    wait(spin_time);                                           
    RA(side_dir,0);// LEFT or RIGHT           
    wait(spin_time);                                           
    RA(0,-100); // UP                                  
    wait(spin_time);
    wait(2000);
    //--------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                                                     
//--------------------------------------------------- 
combo KNUCKLEBALL {                                   
    RA(0,100); // DOWN                                 
    wait(spin_time);                                           
    RA_ZERO();                                 
    wait(spin_time);                                           
    RA(0,-100);    // UP                              
    wait(spin_time);                                           
    RA_ZERO();                                
    wait(spin_time);                                           
    RA(0,100);// DOWN                        
    wait(spin_time); 
    wait(2000);
    //-----------------
    FK_mode  = 0;
    onoff_FK = FALSE;
}                         
int tff_ping  = 70; 
combo InstantTimedFinish {
    CORNER()
    set_val(ShotBtn, 100);             
    wait(240);
    set_val(ShotBtn, 0);
    CORNER()
    wait(150 + tff_ping)
    set_val(ShotBtn, 100);             
    wait(240);  
} 

int long_finesse;
function CORNER() { 

     if (combo_running(InstantTimedFinish)){
         FINESSE_OR_NORMAL = 100;
     }else{
         FINESSE_OR_NORMAL =  25;
     }   
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {  
        right_on = FALSE;
        LA (FINESSE_OR_NORMAL, -90);
    }
          
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    { 
        right_on = TRUE;
        LA (FINESSE_OR_NORMAL, 90);
    }
    
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    { 
        right_on = TRUE;
        LA (inv(FINESSE_OR_NORMAL), -90);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {
        right_on = FALSE;
        LA (inv(FINESSE_OR_NORMAL),  90);
    }
          
}

function CORNER_FIX_MOVE() {
        
    // Moving to the UP - RIGHT -->
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
    {   right_on = FALSE;
        LA (100,-16);
    }
    // Moving to the DOWN - RIGHT -->      
    if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
    {   right_on = TRUE;
        LA (100, 16);
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
    {   right_on = TRUE; 
        LA (-100,-16); 
    }
    
    if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
    {   right_on = FALSE;
        LA (-100, 16);
    }
}

int ping = 20;
int FINESSE_OR_NORMAL;
int time_finish_ShotBtn = 240;// how long to hold shot
int timefinish_pause    = 150;// pause before second shot
combo Timed_Finesse_Finish {
    CORNER_FIX_MOVE() // this function will determine right_on ( True or False ) based on where is the player in Feield , 
                      //it grants a FAR post target exit because by default the finesse shots always targeting the far post .
                      
    CORNER ();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);             
    wait(time_finish_ShotBtn);
                              
    CORNER ();
    set_val(ShotBtn, 0); 
    set_val(FinesseShot, 100);
    wait(timefinish_pause + ping); // Ping is variable set to 0 for squad battle , still need more testing in online to entre a suitable value to it .
    
    CORNER();
    set_val(FinesseShot, 100);
    set_val(ShotBtn, 100);
    wait(time_finish_ShotBtn)            
           
} 
combo SKILLED_BRIDGE_cmb {
    set_val(PaceCtrol,100);
    set_val(FinesseShot,100);
    wait(60);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    set_val(FinesseShot,100);
    wait(80);
}

combo FAKE_SHOT2 {        
	set_val(CrossBtn,100);  
	wait(40);              
	set_val(CrossBtn,100);  
			 
	set_val(PassBtn,100); 
	wait(60);             
	set_val(CrossBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
} 
                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
}                                        
                                         
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
}                   
combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
}                  
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
}                        
///////////////////////////////
// Ball Roll                   
combo BALL_ROLL {               
	RA_L_R () ;    // Left or Right 
	wait(250);  
}          
//////////////////////////////////////////////////////   
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin 
combo TURN_AND_SPIN {  
  if(ACTIVE == BALL_ROLL_FAKE_TURN ) hold_btn = 200;//  Ball Roll Fake Turn L2 
	else hold_btn = 1;      
 wait(hold_btn);
	RA_UP ();      // up   
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);          
	RA_L_R () ;    // Left or Right 
	wait(w_rstick);    
}                   
////////////////////////////////////
//  Tornado Spin + L1    
combo TORNADO_SPIN {     
	RA_DOWN ();    // down  
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);         
	RA_L_R ();     //  <-/-> 
	wait(w_rstick);       
}                     
////////////////////////////////////////////////////////////// 
//28. LATERAL HEEL to HEEL /////////////////////////////////// 
////////////////////////////////////////////////////////////// 
// + L1   PlayerRun                  
combo LATERAL_HEELtoHEEL {  
    set_val(PlayerRun,100);
    RA_OPP () ;            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_ZERO ();            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_L_R () ;            
    wait(60);//           
    wait(300);            
}                         
combo FEINT_FORWARD {
 LA (0,0);           
	wait(w_rstick);     
 LA (0,0);           
	RA_DOWN (); // down 
	wait(w_rstick);     
 LA (0,0);           
	RA_ZERO (); // ZERO 
	wait(w_rstick);     
 LA (0,0);          
	RA_DOWN (); // down 
	wait(w_rstick);    
}                   
combo  TURN_BACK  {       
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);  
	RA_DOWN ();             
	wait(80);             
}                  
///////////////////////////////////////////////////
// ZONE FUNCTION
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(PS4_LX) >= 50) move_lx = 100;
    else if(get_val(PS4_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(PS4_LY) >= 50) move_ly = 100;
    else if(get_val( PS4_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(ZONE_P[zone_p][0] == move_lx && ZONE_P[zone_p][1] == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
}
function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - abs(d);
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}
int onoff_penalty;  
int onoff_FK;  
//=========================================================== 
// --- PENALTIES 
//*************************************************                                    
int dir;                                      
int LeftRight,UpDown; 
int Pen_X,Pen_Y;
int correct_X,correct_Y;
const int PenaltiX_Y [] []= {
{  87,   0},//0. 1 Right
{ -86,   0},//1. 2 Left
{   0, 100},//2. 3 Down
{   0, -70},//3. 4 Up
{  56,  90},//4. 8 Right Down
{  78, -38},//5. 9 Right Up
{ -56,  90},//6.11 Left Down  
{ -78, -38} //7.13 Left Up 
};
//---------------------------   
function fPenalties () {                              
    
    if(!get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )LeftRight = 1;// Right
		                                              
		if(event_press(PS4_LEFT) ) LeftRight = 2;// Left
		                                              
		if(event_press(PS4_DOWN))  UpDown    = 3;// Down
		                                              
		if(event_press(PS4_UP))    UpDown    = 4;// Up  
		                                              
		if(LeftRight && !UpDown){                       
		if(LeftRight == 1) dir = 1; // Right          
		else   dir = 2;             // Left           
		                                              
		if(dir == 1 ){                                
		   Pen_X = PenaltiX_Y [0][0] ;  //0.          
		   Pen_Y = PenaltiX_Y [0][1] ;                        
		}                                             
		                                              
		if(dir == 2 ){                                
		   Pen_X = PenaltiX_Y [1][0] ;  //1.           
		   Pen_Y = PenaltiX_Y [1][1] ;                     
		}                                             
		}                                               
		else if(!LeftRight && UpDown){                  
		if(UpDown == 3) dir = 3; // Down              
		else   dir = 4;          // Up                
		if(dir == 3 ){                                
		   Pen_X = PenaltiX_Y [2][0] ;  //2.           
		   Pen_Y = PenaltiX_Y [2][1] ;                   
		}                                             
		                                              
		if(dir == 4 ){                                
		   Pen_X = PenaltiX_Y [3][0] ;  //3.           
		   Pen_Y = PenaltiX_Y [3][1] ;                    
		}                                             
		                                              
		}                                               
		else if(LeftRight && UpDown){                   
		//---------------------------------------       
		  dir = (LeftRight * UpDown) + 5 ;            
		  // Right Down                               
		  if(dir == 8 ){                              
		      Pen_X = PenaltiX_Y [4][0] ;  //4.           
		      Pen_Y = PenaltiX_Y [4][1] ;                      
		  }                                           
		  //Right Up                                  
		  if(dir == 9 ){                              
		      Pen_X = PenaltiX_Y [5][0] ;  //5.           
		      Pen_Y = PenaltiX_Y [5][1] ;                     
		  }
		  // Left Down                                
		  if(dir == 11 ){                             
		      Pen_X = PenaltiX_Y [6][0] ;  //6.           
		      Pen_Y = PenaltiX_Y [6][1] ;                      
		  }           
		  // Left Up                                  
		  if(dir == 13 ){                             
		      Pen_X = PenaltiX_Y [7][0] ;  //7.           
		      Pen_Y = PenaltiX_Y [7][1] ;                        
		  }                                           
		                                  
		}                                               
     }else if(get_val(PS4_R2)){
		if(event_press(PS4_RIGHT) )correct_X += 1;// Right
		                                          
		if(event_press(PS4_LEFT) ) correct_X -= 1;// Left
		                                          
		if(event_press(PS4_DOWN))  correct_Y += 1;// Down
		                                          
		if(event_press(PS4_UP))    correct_Y -= 1;// Up  
     }
     
     	set_val(PS4_LX, Pen_X + correct_X);
     	set_val(PS4_LY, Pen_Y + correct_Y);
     	
      set_val(PS4_UP,   0);                       
      set_val(PS4_DOWN, 0);                       
      set_val(PS4_LEFT, 0);                      
      set_val(PS4_RIGHT,0);                      
      //----  reset the aiming direction  
      if(event_press(XB1_RS)){           
      	LeftRight = 0;                   
      	UpDown    = 0;                  
      	dir       = 0;
      	Pen_X     = 0;
      	Pen_Y     = 0;
      	correct_X = 0;
      	correct_Y = 0;
      }                              
      set_val(XB1_RS,0);           
}                         
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
int LS_Sens        = 96;
int LS_Sprint_Sens = 106;
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             
int temp_zone;
function calc_temp_zone(user_zone){
    temp_zone = user_zone;
    if(temp_zone < 0 ) temp_zone = 8 - abs(user_zone);
    else if(temp_zone >= 8) temp_zone = user_zone - 8;
    return temp_zone;
}
int Skill_Up_Up = FLAIR_ROULETTE_SKILL
int Skill_Up_Up_L = ELASTICO_SKILL
int Skill_Up_Up_R = REVERSE_ELASTICO_SKILL
int Skill_LEFT = BERBA_MCGEADY_SPIN_SKILL
int Skill_RIGHT = BERBA_MCGEADY_SPIN_SKILL
int Skill_Down_L = THREE_TOUCH_ROULETTE_SKILL
int Skill_Down_R = THREE_TOUCH_ROULETTE_SKILL
int Skill_Up_Down = FEINT_FORWARD_AND_TURN
function f_auto_skill_menu(){
	//1.1. RS = LS zone  
	if(zone_RS == zone_p){
		right_on = FALSE;// use One Way Skills
		ACTIVE = Skill_Up_Up;
		run_skill_combo(ACTIVE);
		
	}
	
	//1.4. RS = opposite of LS zone  
	if(zone_RS == calc_temp_zone (zone_p + 4)){ // right_on does not matter here
		//1.1.0. if LS --> UP (zone 0)
		right_on = FALSE;// use One Way Skills
		ACTIVE = Skill_Up_Down;
		run_skill_combo(ACTIVE);    
    }
    //-------------------
    //1.2. RS = LS zone +1/-1
    if(zone_RS == calc_temp_zone (zone_p + 1) ){
        right_on = TRUE;
        ACTIVE = Skill_Up_Up_R;
        run_skill_combo(ACTIVE);
    }
    if(zone_RS == calc_temp_zone (zone_p - 1) ){
        right_on = FALSE;
        ACTIVE = Skill_Up_Up_L;
        run_skill_combo(ACTIVE);
    }
    
    //1.3. RS = LS zone +2/-2
    if(zone_RS == calc_temp_zone (zone_p + 2) ){
        right_on = TRUE;// use One Way Skills
        ACTIVE = Skill_RIGHT;
        run_skill_combo(ACTIVE);
    }
    if(zone_RS == calc_temp_zone (zone_p - 2) ){
        right_on = FALSE;// use One Way Skills
        ACTIVE = Skill_LEFT;
        run_skill_combo(ACTIVE);
    }
    if(zone_RS == calc_temp_zone (zone_p + 3) ){
        right_on = TRUE;// use One Way Skills
        ACTIVE = Skill_Down_R;
        run_skill_combo(ACTIVE);
    }
    if(zone_RS == calc_temp_zone (zone_p - 3) ){
        right_on = FALSE;// use One Way Skills
        ACTIVE = Skill_Down_L;
        run_skill_combo(ACTIVE);
    }
    
}
function run_skill_combo( f_skill){
//-----------------------------------------------------------------------
if(f_skill == HEEL_TO_HEEL_FLICK_SKILL) combo_run(HEELtoHEEL);   //  2. Heel to Heel
if(f_skill == ELASTICO_SKILL) combo_run(ELASTICO);               // 12. ELASTICO_SKILL
if(f_skill == REVERSE_ELASTICO_SKILL)combo_run(REVERSE_ELASTICO);// 13. REVERSE_ELASTICO_SKILL
if(f_skill == ROULETTE_SKILL) combo_run(ROULETTE);               // 17. ROULETTE_SKILL
if(f_skill == FLAIR_ROULETTE_SKILL) combo_run(ROULETTE);         // 18. FLAIR_ROULETTE_SKILL
if(f_skill == BALL_ROLL_SKILL) combo_run(BALL_ROLL);             // 19. BALL_ROLL_SKILL
if(f_skill == BERBA_MCGEADY_SPIN_SKILL) combo_run(TURN_AND_SPIN);// 20. BERBA_MCGEADY_SPIN_SKILL
if(f_skill == THREE_TOUCH_ROULETTE_SKILL)combo_run(TORNADO_SPIN);// 23. THREE_TOUCH_ROULETTE_SKILL
if(f_skill == LATERAL_HEEL_TO_HEEL_SKILL) combo_run(LATERAL_HEELtoHEEL);  // 28. LATERAL_HEEL_TO_HEEL_SKILL
if(f_skill == FEINT_FORWARD_AND_TURN ) combo_run(FEINT_FORWARD); // 33. Feint Forward & Turn
if(f_skill == TURN_BACK ) combo_run(TURN_BACK);                  // 34. Turn Back
if(f_skill == SKILLED_BRIDGE_SKILL )  combo_run(SKILLED_BRIDGE_cmb);         // 48
}

int RS_X, RS_Y ;
int zone_RS;
function calc_RS(){
 if(get_val(XB1_RX) >= 45) RS_X = 100;
    else if(get_val(XB1_RX) <= -45) RS_X = -100;
    else RS_X = 0;
    if(get_val(XB1_RY) >= 45) RS_Y = 100;
    else if(get_val( XB1_RY) <= -45) RS_Y = -100;
    else RS_Y = 0;
    
    if(RS_X != 0 || RS_Y != 0) {
        zone_RS = 0; while(zone_RS < 8) {
            if(ZONE_P[zone_RS][0] == RS_X && ZONE_P[zone_RS][1] == RS_Y) {
                break;
            } zone_RS += 1;
        }
    }   
}
