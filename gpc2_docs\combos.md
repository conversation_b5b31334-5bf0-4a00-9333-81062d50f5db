# Combos

ombo (short for combination) is a term that designates a set of actions programmed to be automatically performed in sequence.

In addition to combos there are also macros.

Combo or Macro
Most of the time you will use a combo. Some reasons to do so:

You can create one very fast by code because of its easy to use syntax.
You can run multiple combos at the same time.
It allows you to mix the current user inputs into your combo like it is required for anti recoil.
You and the users you share your script with don't need a sd-card.
You probably want to record and use a macro for:

Precise stick movements
Long control sequence like when walking around doing a farming route in a game.
Doing complex moves/combos in fighting game that require precise timings.
Lowering the scripts size to be able to put more features into a single script.
Here is an incomplete list showing their different functionalty.

Feature	Combo	Macro
Requires a sd-card	No	Yes1)
Playback multiple ones at the same time	Yes	No
Allows user input during playback	Yes2)	Limited3)
Creation per code	Yes	No
Creation per GTuner IV graphical macro editor	No4)	Yes
Creation by recording	Yes5)	Yes6)
Size Limit	Yes7)	No8)9)
Create / Record
You can created a combo manually or you can use the macro recording of GTuner IV to record inputs and convert it to a combo as explained on this page.

Syntax
A combo is defined using syntax such as the following:

 combo SomeName {
    set_val(BUTTON_14, 100); // set BUTTON_14 to pressed
    wait(100);               // press for 100ms
 
    set_val(BUTTON_14, 0);   // set BUTTON_14 to released
    wait(100);               // release for 100ms
 
    set_val(BUTTON_14, 100); // set BUTTON_14 to pressed
    set_val(BUTTON_15, 100); // set BUTTON_15 to pressed
    wait(250);               // press both for 250ms
 
    wait(250);               // wait 250ms, without any control states set by code
}
To execute the combo above you need to use the combo_run(comboname) function.
Example to start it each time you press BUTTON_9:

main {
    if (event_active(BUTTON_9)) combo_run(SomeName);
}
For complete examples click here and/or take a look at the Related Functions listed below.

Related Functions
wait(ms); The wait command sets for how long the last set of actions (commands) must be execute. It's expressed in milliseconds and can range from 0 to 32767.
wait can be used within a combo only, not in main { } or anywhere else.
wait cannot exist inside conditionals like if.
call(comboname); Starts the execution of the combo comboname and waits for the called combo to finish before it continues the execution of the current combo
call can be used within a combo only, not in main { } or anywhere else.
call cannot exist inside conditionals like if.
combo_run(comboname); Start the execution of a combo.10)
combo_stop(comboname); Stop the execution of a combo.11)
combo_restart(comboname); Restart the execution of a combo.12)
combo_pause(comboname); Pause the execution of a combo.13)
Pitfalls
There are various pitfalls to watch out for when using combos.

Too low/short wait times
Times are in milli-seconds. A very short time like wait(1); might not be registered by the game as an input.
Try longer wait times like wait(20);.
Using call, rumble or led control functions
Only use those function within a wait(0); section
They are usually marked with something like this in the GPC Language Reference:
ATTENTION: The ffb_set() should NOT be called on every interaction of main.
Recall what the wait command does: It repeats all of the wait times section commands for the time specified. Some functions like rumble or led commands will flood the usb connection of the T2 to the console causing disconnects when they are send to often.
Example of correct usage:
combo Example {
  // section wait(0); begins here
  ffb_set(FFB_1,100.0,400); // rumble motor 1 full power for 400ms
  wait(0); // <-- section wait(0) ends here and wait(300) starts
  set_val(BUTTON_10,100);
  set_val(BUTTON_11,100);
  wait(300); // press BUTTON_10 and _11 for 300ms
}
Press and Release of a button
While you can use something like PressReleaseA most of the time you need to understand when not to use it:
combo PressReleaseA {
  set_val(BUTTON_10,100);
  wait(300); // press BUTTON_10 for 300ms
  wait(300); // do nothing for 300ms
}
 
combo PressReleaseB {
  set_val(BUTTON_10,100);
  wait(300); // press BUTTON_10 for 300ms
  set_val(BUTTON_10,0);
  wait(300); // release BUTTON_10 for 300ms
}
PressReleaseA : If you keep BUTTON_10 pressed manually, the button will not send as released by the script.
PressReleaseB : This will explicitly set the button to released for 300ms, even when you keep the button pressed manually.
1) Requires a sd-card for playback, you can record via GTuner IV without one.
2) Controls set by the combo will override your inputs only as long as the time is set for the current active section.
3) Only controls that aren't used during a macro can be manually used during the playback of a macro.
e.g. If your macro doesn't use BUTTON_5 at all you can press it manually and it will be received by the console.
4) As for the recording, you can create a macro via the editor and convert it to a combo.
5) You can only record a macro but convert it or parts of it to a combo
6) You can record macros via code too
7) Regular GPC combos can have to up to 255 states (a state is defined by the wait() statement). However, is possible to chain multiple combos with the call() function, so the maximum length is actually limited by the 8Kb bytecode size
8) You need a bit of code to playback or record a macro, the macro itself doesn't add to the scripts size limit.
9) The maximum length of a single macro file is 24hours.
More precisely up to 24 days, 20 hours, 31 minutes, 23 seconds and 650 milliseconds
10) combo_run has no effect if the combo is already running.
11) combo_stop has no effect if the combo is not running.
12) combo_restart has immediate effect on inactive or running combos.
13) combo_pause The execution of the combo can be resumed with the combo_run command.


combo_run
combo_run - Run a combo

Description
combo_run(<COMBO_IDENTIFIER>);
Run the combo declared as <COMBO_IDENTIFIER> from its current state. Normally the current state of an inactive combo is the initial state, unless combo_pause is executed.

This command has no effect if the combo is already running. combo_run can be called indiscriminately to keep the combo running indefinitely.
Parameters
<COMBO_IDENTIFIER>: Identifier of the combo to run.
Examples
Example #1 combo_run() example
main {
    // While BUTTON_2 is held-down ...
    if(get_val(BUTTON_2)) {
        // ... keep the combo "Example" running in an endless loop.
        combo_run(Example);
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(50);
    set_val(BUTTON_8, 100.0);
    wait(50);
    set_val(BUTTON_16, 100.0);
    wait(50);
}

combo_restart
combo_restart - Restart a combo

Description
combo_restart(<COMBO_IDENTIFIER>);
Restart the execution of the combo declared as <COMBO_IDENTIFIER> from its initial state.

This command has immediate effect on inactive or running combos.
Parameters
<COMBO_IDENTIFIER>: Identifier of the combo to restart.
Examples
Example #1 combo_restart() example
main {
    // If BUTTON_2 is pressed ...
    if(event_active(BUTTON_2)) {
        // ... restart the combo "Example" from its beginning.
        combo_restart(Example);
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(1000);
    set_val(BUTTON_8, 100.0);
    wait(1000);
    set_val(BUTTON_16, 100.0);
    wait(1000);
}

combo_pause
combo_pause - Pause a combo

Description
combo_pause(<COMBO_IDENTIFIER>);
Pause the execution of the combo declared as <COMBO_IDENTIFIER>. The execution of the combo can be resumed with combo_run command.

Parameters
<COMBO_IDENTIFIER>: Identifier of the combo to pause.
Examples
Example #1 combo_pause() example
main {
    // If BUTTON_2 is pressed ...
    if(event_active(BUTTON_2)) {
        // ... check if the combo "Example" is running ...
        if(Example) {
            // ... if yes, pause it.
            combo_pause(Example);
        } else {
            // ... if not, run it.
            combo_run(Example);
        }
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(1000);
    set_val(BUTTON_8, 100.0);
    wait(1000);
    set_val(BUTTON_16, 100.0);
    wait(1000);
}

combo_stop
combo_stop - Stop a combo

Description
combo_stop(<COMBO_IDENTIFIER>);
Stop the execution of the combo declared as <COMBO_IDENTIFIER> and sets the combo state to its initial state.

Parameters
<COMBO_IDENTIFIER>: Identifier of the combo to stop.
Examples
Example #1 combo_stop() example
main {
    // While BUTTON_2 is held-down ...
    if(get_val(BUTTON_2)) {
        // ... keep the combo "Example" running in an endless loop.
        combo_run(Example);
    } else if(Example) { // Else if combo "Example" is running ...
        // ... stop it immediately.
        combo_stop(Example);
    }
}
 
combo Example {
    set_val(BUTTON_4, 100.0);
    wait(50);
    set_val(BUTTON_8, 100.0);
    wait(50);
    set_val(BUTTON_16, 100.0);
    wait(50);
}

wait
wait - Defines an combo state

Description
wait(int16 ms);
Defines an combo state and sets how long which the defined state must last.

The wait() statement belongs exclusively to combos and cannot exist inside conditionals.
Parameters
ms: Time which the current state must last, expressed in milli-seconds (1s == 1000ms).
Examples
Example #1 wait() example
combo Example {
    // This is an combo state. In this state the BUTTON_4 and BUTTON_7 should 
    // be pressed and held-down for 250 milli-seconds.
    set_val(BUTTON_4, 100.0);
    set_val(BUTTON_7, 100.0);
    wait(250);
 
    // After 250 milli-seconds the combo moves to the next state.
 
    // This is the second combo state. In this state the BUTTON_6 should be 
    // pressed and held-down for 120 milli-seconds.
    set_val(BUTTON_6, 100.0);
    wait(120);
}

call
call - Call a combo

Description
call(<COMBO_IDENTIFIER>);
Start the execution of the combo declared as <COMBO_IDENTIFIER> and waits the called combo to finish before continue the execution of current combo.

The call() statement belongs exclusively to combos and cannot exist inside conditionals.
Parameters
<COMBO_IDENTIFIER>: Identifier of the combo to call.
Examples
Example #1 call() example
combo Example {
    // This is the first combo state.
    set_val(BUTTON_4, 100.0);
    set_val(BUTTON_7, 100.0);
    wait(250);
 
    // After 250 milli-seconds the combo moves to the next state.
 
    // This is the second combo state.
    // In this state the combo "PressSix" is called.
    call(PressSix);
 
    // call waits the combo "PressSix" to finish before move to the next state.
 
    // This is the third combo state.
    set_val(BUTTON_8, 100.0);
    wait(80);
}
 
combo PressSix {
    set_val(BUTTON_6, 100.0);
    wait(120);
}