
        // Script was generated with < FIFA 22 S.G.I > ver. 17.7 Date :11/09/21 Time: 5:46:54 AM
//====================================================================================================
/*    
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
*/ 
//====================================================================================================
                                                                       
                                                                       
//====================================================================================================
/*
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\   $$$$$$\  
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$  __$$\ 
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\__/  $$ |
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  | $$$$$$  |
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/ $$  ____/ 
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |      $$ |      
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$$$\ 
  \__|      \______|\__|      \__|  \__|      \________|\________|
*/
//====================================================================================================
/*
   $$$$$$$\  $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$$\        $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
   $$  __$$\ \_$$  _|$$  __$$\ $$ |  $$ |\__$$  __|      $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$ | $$  |
   $$ |  $$ |  $$ |  $$ /  \__|$$ |  $$ |   $$ |         $$ /  \__|  $$ |     $$ |  $$ /  \__|$$ |$$  / 
   $$$$$$$  |  $$ |  $$ |$$$$\ $$$$$$$$ |   $$ |         \$$$$$$\    $$ |     $$ |  $$ |      $$$$$  /  
   $$  __$$<   $$ |  $$ |\_$$ |$$  __$$ |   $$ |          \____$$\   $$ |     $$ |  $$ |      $$  $$<   
   $$ |  $$ |  $$ |  $$ |  $$ |$$ |  $$ |   $$ |         $$\   $$ |  $$ |     $$ |  $$ |  $$\ $$ |\$$\  
   $$ |  $$ |$$$$$$\ \$$$$$$  |$$ |  $$ |   $$ |         \$$$$$$  |  $$ |   $$$$$$\ \$$$$$$  |$$ | \$$\ 
   \__|  \__|\______| \______/ \__|  \__|   \__|          \______/   \__|   \______| \______/ \__|  \__|
*/
//====================================================================================================
//-------------------------------------------------------------- 
// DECLARATIONS  


define time_to_dblclick     = 300; // Time to Double click     
//====================================================
// DEVICE :  Cronus ZEN device 
//====================================================
//====================================================
// YOUR BUTTON LAYOUT : CUSTOM
//====================================================
define ShotBtn       = XB1_B; // Shot Btn         (default B/CIRCLE 
define PassBtn       = XB1_A; // Short Pass Btn   (default A/CROSS)
define PlayerRun     = XB1_RB; // Player Run       (default L1/LB) 
define FinesseShot   = XB1_LB; // Finesse Shot     (default R1/RB)
define PaceCtrol     = XB1_LT; // Protect Ball     (default L2/LT)
define SprintBtn     = XB1_RT; // Sprint Btn       (default R2/RT)
define CrossBtn      = XB1_X; // Cross Btn        (default X/SQUARE)
define ThroughBall   = XB1_Y; // Through Ball Btn (default Y/TRIANGLE)       
//====================================================
//-------------------------------------------------------------- 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;  
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define None                                = 0;
define FAKE_SHOT_SKILL                     = 1;
define HEEL_TO_HEEL_FLICK_SKILL            = 2;
define HEEL_FLICK_TURN_SKILL               = 3;
define RAINBOW_SKILL                       = 4;
define DRAG_BACK_SOMBRERO_SKILL            = 5;
define FAKE_PASS_SKILL                     = 6;
define DRAG_BACK_UNIVERSAL_SKILL           = 7;
define STEP_OVER_FEINT_SKILL               = 8;
define DRAG_TO_DRAG_SKILL                  = 9;
define HOCUS_POCUS_SKILL                   = 10;
define TRIPLE_ELASTICO_SKILL               = 11;
define ELASTICO_SKILL                      = 12;
define REVERSE_ELASTICO_SKILL              = 13;
define CRUYFF_TURN_SKILL                   = 14;
define LA_CROQUETA_SKILL                   = 15;
define RONALDO_CHOP_SKILL                  = 16;
define ROULETTE_SKILL                      = 17;
define FLAIR_ROULETTE_SKILL                = 18;
define BALL_ROLL_SKILL                     = 19;
define BERBA_MCGEADY_SPIN_SKILL            = 20;
define BOLASIE_FLICK_SKILL                 = 21;
define TORNADO_SKILL                       = 22;
define THREE_TOUCH_ROULETTE_SKILL          = 23;
define ALTERNATIVE_ELASTICO_CHOP_SKILL     = 24;
define BALL_ROLL_CHOP_SKILL                = 25;
define FEINT_AND_EXIT_SKILL                = 26;
define FEINT_L_EXIT_R_SKILL                = 27;
define LATERAL_HEEL_TO_HEEL_SKILL          = 28;
define WAKA_WAKA_SKILL                     = 29;
define BODY_FEINT_SKILL                    = 30;
define DRAG_TO_HEEL                        = 31;
define BALL_ROLL_FAKE_TURN                 = 32;
define FEINT_FORWARD_AND_TURN              = 33;
define TURN_BACK                           = 34;
define ADVANCED_CROQUETA                   = 35;
define CANCELED_THREE_TOUCH_ROULETTE_SKILL = 36;
define REVERSE_STEP_OVER_SKILL             = 37;
define FAKE_DRAG_BACK_SKILL                = 38;
define RAINBOW_TO_SCORPION_KICK_SKILL      = 39;
define STEP_OVER_BOOST_SKILL               = 40;
define CANCEL_SHOOT_SKILL                  = 41;
define DIRECTIONAL_NUTMEG_SKILL            = 42;
define CANCELED_BERBA_SPIN_SKILL           = 43;
define CANCELED_BERBA_SPIN_WITH_DIRECTION  = 44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL       = 45;
define DRIBBLING_SKILL                     = 46;
define FOUR_TOUCH_TURN_SKILLS              = 47;
define SKILLED_BRIDGE_SKILL                = 48;
define SCOOP_TURN_FAKE_SKILL               = 49;
define BALL_ROLL_STEP_OVER_SKILL           = 50;
define CANCELED_4_TOUCH_TURN_SKILL         = 51;
define FAKE_SHOT_CANCEL_SKILL              = 52;
define OKKOSHA_FLICK_SKILL                 = 53;
define ADVANCED_RAINBOW_SKILL              = 54;
define STOP_LA_CROQUETA_SKILL              = 55;
define JUGGLING_RAINBOW_SKILL              = 56;
define STOP_NEYMAR_ROLL_SKILL              = 57;
define STOP_V_DRAG_SKILL                   = 58;
define REV_OR_ELASTICO_SKILL               = 59;
define STOP_REV_OR_ELASTICO_SKILL          = 60;
define DRAG_REV_OR_ELASTICO_SKILL          = 61;
define FAKE_RABONA_SKILL                   = 62;
define RABONA_TO_REV_ELASTICO_SKILL        = 63;
define RABONA_TO_ELASTICO_SKILL            = 64;
define SOMBRERO_FLICK_SKILL                = 65;
define JUGGLE_BACK_SOMBRERO_SKILL          = 66;
define FAKE_BERBA_OPP_EXIT_SKILL           = 67;
define DIAGONAL_HEEL_CHOP_SKILL            = 68;
define FAKE_BERBA_FAKE_DRAG_SKILL          = 69;
define ELASTICO_CHOP_SKILL                 = 70;
define BALL_ROLL_CUT_180_SKILL             = 71;
define HEEL_TO_BALL_ROLL_SKILL             = 72;
define STUTTER_FEINT_SKILL                 = 73;
define JOG_OPENUP_FAKE_SHOT                = 74;
define SPIN_MOVE_LEFT_RIGHT_SKILL          = 75;
//--------------------------------------------------------------   
define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dEnd;
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 80;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int Temp; 
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
int RS_Sens  = 50
                                            
 int RS_X , RS_Y ;  
 define SKILLR_X      = XB1_RX ;
define SKILLR_Y      = XB1_RY ;
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
int run_combo_juggle = FALSE;
int pre_juggle ;

main {
  /////////////////////////////////////////////////
    //   DISABLE / ENABLE ENTIRE SCRIPT
    /////////////////////////////////////////////////
if(get_ival(PS4_L1)){
    if(event_press(PS4_R3)){ 
        KS_EntireScript = !KS_EntireScript;
        f_set_notify (KS_EntireScript);
    }
    set_val(PS4_R3,0);
}
                                  
/////////////////////////////////////////////////
//   DISABLE / ENABLE ENTIRE SCRIPT
if( !KS_EntireScript){  
    //--------------------------------------------------------------
    //  turn ON Penalty  hold  RT/R2 and press OPTIONS 
    //======================================== 
    //  Penalties FIFA 23  v. 1.0                                    
    //========================================
    if (get_ival(XB1_RT)) {
      if (event_press(PS4_OPTIONS)) {
        onoff_penalty = !onoff_penalty;
        f_set_notify(onoff_penalty);
      }
      set_val(PS4_OPTIONS, 0);
    }

    if (onoff_penalty) { // Penalties_FKeecks
      vm_tctrl(0);
      ////////////////////////////////  
      // LED color indication           
      if (onoff_penalty) LED_Color(Red); //// user color
      if (onoff_penalty) {
        fPenalties();
      }
    } else { // all other code
    LED_Color(Blue);
 if(time_to_clear_screen){
    time_to_clear_screen -= get_rtime();
    if(time_to_clear_screen <= 0)combo_run(CLEAR_SCREEN);
 }
            if(get_ival(XB1_RT)){                              
	            if(event_press(XB1_RIGHT)) {
	                EA_Ping +=5;
	                on_the_fly_display(centerPosition(sizeof(EA_PING)- 1,OLED_FONT_MEDIUM_WIDTH),EA_PING[0],EA_Ping);
            	}
            	if(event_press(XB1_LEFT)) {
	                EA_Ping -=5;
	                on_the_fly_display(centerPosition(sizeof(EA_PING)- 1,OLED_FONT_MEDIUM_WIDTH),EA_PING[0],EA_Ping);
                }
                set_val(PS4_RIGHT,0);
                set_val(PS4_LEFT ,0);
            }
            
            
EM_POLAR_V2();

if (get_val(XB1_VIEW) ) {
	if(event_release(XB1_LEFT)) {
		load_slot (3);
      }
      set_val(XB1_LEFT,0);
	}
if (get_val(XB1_VIEW) ) {
	if(event_release(XB1_RIGHT)) {
		load_slot (2);
      }
      set_val(XB1_RIGHT,0);
	}

   if(get_ival(XB1_PL1)){  
    set_val(XB1_RB, 100);
        }

if(get_ival(XB1_PR1)){combo_run(PARTIAL_TEAM_PRESS_cmb);}

 /*     if(event_press(XB1_PL2)){    
      		set_right_or_left ();
      		vm_tctrl(0);              
      		ACTIVE = BALL_ROLL_SKILL; combo_run(BALL_ROLL);  // 19. BALL_ROLL_SKILL
      }  
      */
//  

      if(event_press(XB1_PR2)){    
      		vm_tctrl(0);      
      		//set_right_or_left ();
      		ACTIVE = FAKE_SHOT_SKILL; combo_run(FAKE_SHOT);  //1. FAKE_SHOT
      }  

//if(get_ival(XB1_PR2)){combo_run(Chip_Finish_cmb);}
//if(event_press(XB1_PR2)){ vm_tctrl(0); combo_run(Wild_Alien_RB); }     
//if(event_press(XB1_PL2)){ vm_tctrl(0); combo_run(Wild_Alien_LB); } 	

      //======================================== 
      //  CHIP SHOT FINISH  v. 1.1                                    
      //========================================
      if (get_ival(PlayerRun)) {
        if (get_ival(ShotBtn)) {
          if (combo_running(Chip_Finish_cmb)) vm_tctrl(0);
          combo_run(Chip_Finish_cmb);
        }
        set_val(ShotBtn, 0);
      }
		   //=====================================
		   // FK MOD FIFA23
		   //=====================================
		   if(get_ival(PS4_L1)){
		    	if(event_press(PS4_SHARE)){
		    		   FreeKick_on_off = !FreeKick_on_off;	
               f_set_notify(FreeKick_on_off);
		    	}
		    	set_val(PS4_SHARE,0);
		    }
			if(FreeKick_on_off){   
                LED_Color(Pink);
          if(combo_running(FK_CURVE_cmb)) vm_tctrl(0);           
				if(get_ival(XB1_LB)){
					if ( event_press(XB1_RIGHT)){
					    fk_right_on = TRUE;
						combo_run(FK_CURVE_cmb);
					}
					if ( event_press(XB1_LEFT)){
						fk_right_on = FALSE;
                        combo_run(FK_CURVE_cmb);
                    }
                }
            }   
		   //=====================================
		   // Corner Goal MOD FIFA 23 v. 1.1
		   //=====================================
		    if(get_ival(PS4_L1)){
		    	if(event_press(PS4_OPTIONS)){
		    		   Corner_on_off = !Corner_on_off;	
               f_set_notify(Corner_on_off);
		    	}
		    	set_val(PS4_OPTIONS,0);
		    }
			if(Corner_on_off ){   
         if(combo_running(CORNER_CURVE_cmb)) vm_tctrl(0);           
                LED_Color(Yellow);
				if(get_ival(XB1_LB)){
					if ( event_press(XB1_LEFT)){
					    corn_right_on = FALSE;
						combo_run(CORNER_CURVE_cmb);
					}
					if ( event_press(XB1_RIGHT)){
					    corn_right_on = TRUE;
						combo_run(CORNER_CURVE_cmb);
					}			
				}
			}   

    //========================================
    // +++ DYNAMIC FINISHING FIFA 23 +++
    //========================================
    if(DynamicFinish_on_off){
       f_dynamic_finish (); 
    }
    
                                                                       

        	if(!get_ival(XB1_RS) &&  !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot)) { // all Skills mode ){ 
		if((abs(get_ival(SKILL_STICK_X))> 20 || abs(get_ival(SKILL_STICK_Y))> 20) && !flick_rs){ // getting RS zones
			flick_rs = TRUE;
			calc_RS() ;
			RS_X = ZONE_P[zone_RS][0];
			RS_Y = ZONE_P[zone_RS][1];
			f_auto_skill_menu();
		}			 
		set_val(SKILL_STICK_X,0); 
		set_val(SKILL_STICK_Y,0);
	}
    //--- reset when RS is release
    if(abs(get_ival(SKILL_STICK_X))< 20 && abs(get_ival(SKILL_STICK_Y))< 20){  
        flick_rs = FALSE;                                                                            
    } 
    
               if(Get_LS_Output){  
               if(abs(get_val(PS4_LX))> 45 || abs(get_val(PS4_LY))> 45){
                   calc_zone (); 
                   LX = ZONE_P[zone_p][0];
                   LY = ZONE_P[zone_p][1];
               }//======================================================
           }  

	      

    

    }// Entire Script ON/OFF 
        } // all other code
    else {LED_Color(Green); }
    // all Skills mode                
  }// all other code
   //--------------------------------------------------------------
               

combo Wild_Alien_RB {
    zone_saver();
    set_val(FinesseShot,100);
    LA(inv(LX),inv(LY));
    sensitivity(PS4_LX, 50, 60);
    sensitivity(PS4_LY, 60, 60);
    wait(random(240,260)); 
    set_val(XB1_RB,100);
    wait(random(240,260)); 
} 

combo Wild_Alien_LB {
    zone_saver();
    set_val(FinesseShot,100);
    LA(inv(LX),inv(LY));
    sensitivity(PS4_LX, 50, 40);
    sensitivity(PS4_LY, 60, 40);
    wait(random(240,260)); 
    set_val(XB1_LB,100);
    wait(random(240,260)); 
} 

combo TwoFingersPass { 
Pass_Assist ()
set_val(XB1_LT,100);
set_val(XB1_RB,100);
set_val(XB1_Y,100);
wait(random(190,240));
Pass_Assist ();
wait(random(900,1000));
}

combo PARTIAL_TEAM_PRESS_cmb {
	//set_val(XB1_LT,100);
	//wait(random(50,60));
	//set_val(XB1_LT,0);
    set_val(FinesseShot,100);
    wait(random(50,60));
    set_val(FinesseShot,0);
    wait(random(50,60));
    set_val(FinesseShot,100);
    wait(random(450,500));
    wait(random(160,180));
}


               
int zone_RS;
function f_auto_skill_menu(){
	set_val(TRACE_1,zone_p);
	
	set_val(TRACE_2,zone_RS);
	
	set_val(TRACE_3,calc_temp_zone (zone_p + 1));
	
	set_val(TRACE_4,calc_temp_zone (zone_p + 2));
	
	set_val(TRACE_5,calc_temp_zone (zone_p - 1));
	
	set_val(TRACE_6,calc_temp_zone (zone_p - 2));
	
	//1.1. RS = LS zone  
	if(zone_RS == zone_p){
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //HEEL_TO_HEEL_FLICK_SKILL);
		}
	}
	
	
	//1.4. RS = opposite of LS zone  
	if(zone_RS == calc_temp_zone (zone_p + 4)){ // right_on doesn't matter here
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == UP_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == DOWN_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
		if(zone_p == UP_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //TURN_BACK);
		}
	}
	//-------------------
	//1.2. RS = LS zone +1/-1
	if(zone_RS == calc_temp_zone (zone_p + 1) ){
		//1.1.0. if LS --> UP (zone 0)
	if(zone_p == UP){
		right_on = TRUE;// use One Way Skills
		run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
	}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
	}
	
	if(zone_RS == calc_temp_zone (zone_p - 1) ){
		//1.1.0. if LS --> UP (zone 0)
	if(zone_p == UP){
		right_on = FALSE;// use One Way Skills
		run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
	}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //FEINT_AND_EXIT_SKILL);
		}
	}
	
	//1.3. RS = LS zone +2/-2
	if(zone_RS == calc_temp_zone (zone_p + 2) ){
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
	}
	
			if(zone_RS == calc_temp_zone (zone_p + 3) ){
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		//1.1.5. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = TRUE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
	}
	
	if(zone_RS == calc_temp_zone (zone_p - 2) ){
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		//1.1.4. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //BERBA_MCGEADY_SPIN_SKILL);
		}
	}
	
	
		if(zone_RS == calc_temp_zone (zone_p - 3) ){
		//1.1.0. if LS --> UP (zone 0)
		if(zone_p == UP){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		//1.1.5. LS --> DOWN (zone 4)
		if(zone_p == DOWN){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == UP_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == DOWN_RIGHT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == DOWN_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
		if(zone_p == UP_LEFT){
			right_on = FALSE;// use One Way Skills
			run_skill_combo(STEP_OVER_BOOST_SKILL) //THREE_TOUCH_ROULETTE_SKILL);
		}
	}
	
}

function run_skill_combo( f_skill){
//-----------------------------------------------------------------------
if(f_skill == FAKE_SHOT_SKILL                         ) {ACTIVE = FAKE_SHOT_SKILL; combo_run(FAKE_SHOT);Get_LS_Output = FALSE;   }
if(f_skill == HEEL_TO_HEEL_FLICK_SKILL                ) {ACTIVE = HEEL_TO_HEEL_FLICK_SKILL; combo_run(HEELtoHEEL);Get_LS_Output = FALSE;     }
if(f_skill == HEEL_FLICK_TURN_SKILL                   ) {ACTIVE = HEEL_FLICK_TURN_SKILL; combo_run(HEELtoHEEL);Get_LS_Output = FALSE;    }
if(f_skill == RAINBOW_SKILL                           ) {ACTIVE = RAINBOW_SKILL; combo_run(RAINBOW);Get_LS_Output = FALSE;    }
if(f_skill == DRAG_BACK_SOMBRERO_SKILL                ) {Sombrero = TRUE; combo_run(DRAG_BACK);Get_LS_Output = FALSE;    }
if(f_skill == FAKE_PASS_SKILL                         ) {ACTIVE = FAKE_PASS_SKILL; combo_run(FAKE_SHOT);Get_LS_Output = FALSE;  }
if(f_skill == DRAG_BACK_UNIVERSAL_SKILL               ) {Sombrero = FALSE; combo_run(DRAG_BACK);Get_LS_Output = FALSE;   }
if(f_skill == STEP_OVER_FEINT_SKILL                   ) {ACTIVE = STEP_OVER_FEINT_SKILL; combo_run( STEP_OVER_FEINT );Get_LS_Output = FALSE;   }
if(f_skill == DRAG_TO_DRAG_SKILL                      ) {ACTIVE = DRAG_TO_DRAG_SKILL; combo_run(DRAG_TO_DRAG);Get_LS_Output = FALSE;  }
if(f_skill == HOCUS_POCUS_SKILL                       ) {ACTIVE = HOCUS_POCUS_SKILL; combo_run(HOCUS_POCUS);Get_LS_Output = FALSE;  }
if(f_skill == TRIPLE_ELASTICO_SKILL                   ) {ACTIVE = TRIPLE_ELASTICO_SKILL; combo_run(TRIPLE_ELASTICO);Get_LS_Output = FALSE;  }
if(f_skill == ELASTICO_SKILL                          ) {ACTIVE = ELASTICO_SKILL; combo_run(ELASTICO);Get_LS_Output = FALSE;    }
if(f_skill == REVERSE_ELASTICO_SKILL                  ) {ACTIVE = REVERSE_ELASTICO_SKILL; combo_run(REVERSE_ELASTICO);Get_LS_Output = FALSE;   }
if(f_skill == CRUYFF_TURN_SKILL                       ) {ACTIVE = CRUYFF_TURN_SKILL; combo_run(CRUYFF_TURN);Get_LS_Output = FALSE;    }
if(f_skill == LA_CROQUETA_SKILL                       ) {ACTIVE = LA_CROQUETA_SKILL; combo_run(LA_CROQUETA);Get_LS_Output = FALSE;     }
if(f_skill == RONALDO_CHOP_SKILL                      ) {ACTIVE = RONALDO_CHOP_SKILL; combo_run(FAKE_SHOT); Get_LS_Output = FALSE;    }
if(f_skill == ROULETTE_SKILL                          ) {ACTIVE = ROULETTE_SKILL; combo_run(ROULETTE);  Get_LS_Output = FALSE;  }
if(f_skill == FLAIR_ROULETTE_SKILL                    ) {ACTIVE = FLAIR_ROULETTE_SKILL; combo_run(ROULETTE);Get_LS_Output = FALSE;     }
if(f_skill == BALL_ROLL_SKILL                         ) {ACTIVE = BALL_ROLL_SKILL; combo_run(BALL_ROLL);   Get_LS_Output = FALSE;  }
if(f_skill == BERBA_MCGEADY_SPIN_SKILL                ) {ACTIVE = BERBA_MCGEADY_SPIN_SKILL; combo_run(TURN_AND_SPIN);Get_LS_Output = FALSE;    }
if(f_skill == BOLASIE_FLICK_SKILL                     ) {ACTIVE = BOLASIE_FLICK_SKILL; combo_run(TURN_AND_SPIN); Get_LS_Output = FALSE;   }
if(f_skill == TORNADO_SKILL                           ) {ACTIVE = TORNADO_SKILL; combo_run(TORNADO_SPIN); Get_LS_Output = FALSE;   }
if(f_skill == THREE_TOUCH_ROULETTE_SKILL              ) {ACTIVE = THREE_TOUCH_ROULETTE_SKILL; combo_run(TORNADO_SPIN); Get_LS_Output = FALSE;  }
if(f_skill == ALTERNATIVE_ELASTICO_CHOP_SKILL         ) {ACTIVE = ALTERNATIVE_ELASTICO_CHOP_SKILL; combo_run(TORNADO_SPIN); Get_LS_Output = FALSE;   }
if(f_skill == BALL_ROLL_CHOP_SKILL                    ) {ACTIVE = BALL_ROLL_CHOP_SKILL; combo_run(BALL_ROLL_CHOP); Get_LS_Output = FALSE;    }
if(f_skill == FEINT_AND_EXIT_SKILL                    ) {ACTIVE = FEINT_AND_EXIT_SKILL; combo_run(FEINT_EXIT); Get_LS_Output = FALSE;   }
if(f_skill == FEINT_L_EXIT_R_SKILL                    ) {ACTIVE = FEINT_L_EXIT_R_SKILL; combo_run(FEINT_EXIT); Get_LS_Output = FALSE;    }
if(f_skill == LATERAL_HEEL_TO_HEEL_SKILL              ) {ACTIVE = LATERAL_HEEL_TO_HEEL_SKILL; combo_run(LATERAL_HEELtoHEEL);Get_LS_Output = FALSE;     }
if(f_skill == WAKA_WAKA_SKILL                         ) {ACTIVE =  WAKA_WAKA_SKILL; combo_run(WAKA_WAKA);Get_LS_Output = FALSE;    }
if(f_skill == BODY_FEINT_SKILL                        ) {ACTIVE = BODY_FEINT_SKILL; combo_run(BODY_FEINT); Get_LS_Output = FALSE;   }
if(f_skill == DRAG_TO_HEEL                            ) {ACTIVE = DRAG_TO_HEEL ; combo_run(TORNADO_SPIN); Get_LS_Output = FALSE;  }
if(f_skill == BALL_ROLL_FAKE_TURN                     ) {ACTIVE = BALL_ROLL_FAKE_TURN ;   combo_run(TURN_AND_SPIN);Get_LS_Output = FALSE;    }
if(f_skill == FEINT_FORWARD_AND_TURN                  ) {ACTIVE = FEINT_FORWARD_AND_TURN ; combo_run(FEINT_FORWARD);Get_LS_Output = FALSE;    }
if(f_skill == TURN_BACK                               ) {ACTIVE = TURN_BACK ; combo_run(TURN_BACK);Get_LS_Output = FALSE;    }
if(f_skill == ADVANCED_CROQUETA                       ) {ACTIVE = ADVANCED_CROQUETA ; combo_run(ADVANCED_CROQUETA);Get_LS_Output = FALSE;     }
if(f_skill == CANCELED_THREE_TOUCH_ROULETTE_SKILL     ) {ACTIVE = CANCELED_THREE_TOUCH_ROULETTE_SKILL ; combo_run(CANCELED_THREE_TOUCH_ROULETTE); Get_LS_Output = FALSE;   }
if(f_skill == REVERSE_STEP_OVER_SKILL                 ) {ACTIVE = REVERSE_STEP_OVER_SKILL ; combo_run(REVERSE_STEP_OVER);Get_LS_Output = FALSE;     }
if(f_skill == FAKE_DRAG_BACK_SKILL                    ) {ACTIVE = FAKE_DRAG_BACK_SKILL ; combo_run(FAKE_DRAG_BACK);Get_LS_Output = FALSE;      }
if(f_skill == RAINBOW_TO_SCORPION_KICK_SKILL          ) {ACTIVE = RAINBOW_TO_SCORPION_KICK_SKILL  ; combo_run(RAINBOW_TO_SCORPION);Get_LS_Output = FALSE;  }
if(f_skill == STEP_OVER_BOOST_SKILL                   ) {ACTIVE = STEP_OVER_BOOST_SKILL ; combo_run(BOOSTED_STEPOVER);Get_LS_Output = FALSE;  }
if(f_skill == CANCEL_SHOOT_SKILL                      ) {ACTIVE = CANCEL_SHOOT_SKILL; combo_run(CANCEL_SHOOT);Get_LS_Output = FALSE;  }
if(f_skill == DIRECTIONAL_NUTMEG_SKILL                ) {ACTIVE = DIRECTIONAL_NUTMEG_SKILL; combo_run(NUTMEG_SKILL);Get_LS_Output = FALSE;  }
if(f_skill == CANCELED_BERBA_SPIN_SKILL               ) {ACTIVE = CANCELED_BERBA_SPIN_SKILL;  combo_run(CANCELED_TURN_AND_SPIN);Get_LS_Output = FALSE;   }
if(f_skill == CANCELED_BERBA_SPIN_WITH_DIRECTION      ) {ACTIVE = CANCELED_BERBA_SPIN_WITH_DIRECTION; combo_run(CANCELED_TURN_AND_SPIN);Get_LS_Output = FALSE;   }
if(f_skill == BALL_ROLL_TO_SCOOP_TURN_SKILL           ) {ACTIVE = BALL_ROLL_TO_SCOOP_TURN_SKILL; combo_run(BALL_ROLL_SCOOP_TURN); Get_LS_Output = FALSE;  }
if(f_skill == DRIBBLING_SKILL                         ) {ACTIVE = DRIBBLING_SKILL ; start = TRUE; combo_run(DRIBBLING_SKILL_cmb);Get_LS_Output = FALSE;  }
if(f_skill == FOUR_TOUCH_TURN_SKILLS                  ) {ACTIVE = FOUR_TOUCH_TURN_SKILLS ;  combo_run(FOUR_TOUCH_TURN_cmb);Get_LS_Output = FALSE;  }
if(f_skill == SKILLED_BRIDGE_SKILL                    ) {ACTIVE = SKILLED_BRIDGE_SKILL ;  combo_run(SKILLED_BRIDGE_cmb);Get_LS_Output = FALSE;  }
if(f_skill == SCOOP_TURN_FAKE_SKILL                   ) {ACTIVE = SCOOP_TURN_FAKE_SKILL; combo_run(SCOOP_TURN_FAKE);Get_LS_Output = FALSE;  }
if(f_skill == BALL_ROLL_STEP_OVER_SKILL               ) {ACTIVE = BALL_ROLL_STEP_OVER_SKILL; combo_run(BALL_ROLL_STEP_OVER_cmb);Get_LS_Output = FALSE;  }
if(f_skill == CANCELED_4_TOUCH_TURN_SKILL             ) {ACTIVE = CANCELED_4_TOUCH_TURN_SKILL; combo_run(CANCEL_FOUR_TOUCH_TURN_cmb);Get_LS_Output = FALSE;  }
if(f_skill == FAKE_SHOT_CANCEL_SKILL                  ) {ACTIVE = FAKE_SHOT_CANCEL_SKILL ; combo_run(FAKE_SHOT_CANCEL_cmb);Get_LS_Output = FALSE;  }
if(f_skill == OKKOSHA_FLICK_SKILL                     ) {ACTIVE = OKKOSHA_FLICK_SKILL ; combo_run(OKKOSHA_FLICK_cmb);   Get_LS_Output = FALSE;  }
if(f_skill == ADVANCED_RAINBOW_SKILL                  ) {ACTIVE = ADVANCED_RAINBOW_SKILL ; combo_run(ADVANCED_RAINBOW_cmb); Get_LS_Output = FALSE;   }
if(f_skill == STOP_LA_CROQUETA_SKILL                  ) {ACTIVE = STOP_LA_CROQUETA_SKILL ; combo_run(STOP_LA_CROQUETA_cmb); Get_LS_Output = FALSE;   }
if(f_skill == JUGGLING_RAINBOW_SKILL                  ) {ACTIVE = JUGGLING_RAINBOW_SKILL ; combo_run(JUGGLING_RAINBOW_cmb); Get_LS_Output = FALSE;   }
if(f_skill == STOP_NEYMAR_ROLL_SKILL                  ) {ACTIVE = STOP_NEYMAR_ROLL_SKILL ; combo_run(STOP_NEYMAR_ROLL_cmb);  Get_LS_Output = FALSE;  }
if(f_skill == STOP_V_DRAG_SKILL                       ) {ACTIVE = STOP_V_DRAG_SKILL ; combo_run(STOP_V_DRAG_cmb);  Get_LS_Output = FALSE;   }
if(f_skill == REV_OR_ELASTICO_SKILL                   ) {ACTIVE = REV_OR_ELASTICO_SKILL ; combo_run(REV_OR_ELASTICO_cmb);Get_LS_Output = FALSE;      }
if(f_skill == STOP_REV_OR_ELASTICO_SKILL              ) {ACTIVE = STOP_REV_OR_ELASTICO_SKILL ; combo_run(STOP_REV_OR_ELASTICO_cmb);Get_LS_Output = FALSE;    }
if(f_skill == DRAG_REV_OR_ELASTICO_SKILL              ) {ACTIVE = DRAG_REV_OR_ELASTICO_SKILL ; combo_run(DRAG_REV_OR_ELASTICO_cmb);Get_LS_Output = FALSE;    }
if(f_skill == FAKE_RABONA_SKILL                       ) {ACTIVE = FAKE_RABONA_SKILL ; combo_run(FAKE_RABONA_cmb);  Get_LS_Output = FALSE;  }
if(f_skill == RABONA_TO_REV_ELASTICO_SKILL            ) {ACTIVE = RABONA_TO_REV_ELASTICO_SKILL ; combo_run(RABONA_TO_REV_ELASTICO_cmb); Get_LS_Output = FALSE;  }
if(f_skill == RABONA_TO_ELASTICO_SKILL                ) {ACTIVE = RABONA_TO_ELASTICO_SKILL ; combo_run(RABONA_TO_ELASTICO_cmb); Get_LS_Output = FALSE;  }
if(f_skill == SOMBRERO_FLICK_SKILL                    ) {ACTIVE = SOMBRERO_FLICK_SKILL ; combo_run(SOMBRERO_FLICK_cmb);  Get_LS_Output = FALSE;   }
if(f_skill == JUGGLE_BACK_SOMBRERO_SKILL              ) {ACTIVE = JUGGLE_BACK_SOMBRERO_SKILL ; combo_run(JUGGLE_BACK_SOMBRERO_cmb);Get_LS_Output = FALSE;     }
if(f_skill == FAKE_BERBA_OPP_EXIT_SKILL               ) {ACTIVE = FAKE_BERBA_OPP_EXIT_SKILL ; combo_run(FAKE_BARBA_OPP_EXIT_cmb); Get_LS_Output = FALSE;  }
if(f_skill == DIAGONAL_HEEL_CHOP_SKILL                ) {ACTIVE = DIAGONAL_HEEL_CHOP_SKILL; combo_run(DIAGONAL_HEEL_CHOP_cmb);Get_LS_Output = FALSE;    }
if(f_skill == FAKE_BERBA_FAKE_DRAG_SKILL              ) {ACTIVE = FAKE_BERBA_FAKE_DRAG_SKILL; combo_run(FAKE_BARBA_TO_FAKE_DRAG_cmb); Get_LS_Output = FALSE;   }
if(f_skill == ELASTICO_CHOP_SKILL                     ) {ACTIVE = ELASTICO_CHOP_SKILL;combo_run(ELASTICO_SHOP_cmb);  Get_LS_Output = FALSE;  }
if(f_skill == BALL_ROLL_CUT_180_SKILL                 ) {ACTIVE = BALL_ROLL_CUT_180_SKILL ; combo_run(BAL_ROLL_CUT_180_cmb);Get_LS_Output = FALSE;    }
if(f_skill == HEEL_TO_BALL_ROLL_SKILL                 ) {ACTIVE = HEEL_TO_BALL_ROLL_SKILL; combo_run(HEEL_to_BALL_ROLL_cmb);Get_LS_Output = FALSE;  }
if(f_skill == STUTTER_FEINT_SKILL                     ) {ACTIVE = STUTTER_FEINT_SKILL; combo_run(STUTTER_FEINT_cmb );Get_LS_Output = FALSE;  }
if(f_skill == JOG_OPENUP_FAKE_SHOT                    ) {ACTIVE = JOG_OPENUP_FAKE_SHOT; combo_run(JOG_OPENUP_FAKE_SHOT_cmb );Get_LS_Output = FALSE;  }
if(f_skill == SPIN_MOVE_LEFT_RIGHT_SKILL              ) {ACTIVE = SPIN_MOVE_LEFT_RIGHT_SKILL; combo_run(ROULETTE);Get_LS_Output = FALSE; }
}

int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function set_right_or_left () {
    right_on = FALSE; 
    if (zone_p == 4 || zone_p == 3 || zone_p == 7 ) { 
        right_on = TRUE; 
    } /// 
}

int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}                                              
int start;
combo DRIBBLING_SKILL_cmb {               
    set_val(FinesseShot,100); 
    wait(20);
    set_val(FinesseShot,100);       
    LA_L_R();
    wait(375);
    wait(20);
    set_val(FinesseShot, 0);
    set_val(SprintBtn,100);  
    wait(800);
    start = FALSE;
    Get_LS_Output = TRUE;
}  
int Get_LS_Output;
combo BOOSTED_STEPOVER  {
    RA_L_R () ;    // Left or Right 
    set_val(SprintBtn,0);
    sensitivity(PS4_LX, 50, 40);
    sensitivity(PS4_LY, 60, 40);
    wait(310); 
    sensitivity(PS4_LX, 50, 150);
    wait(100);     
    Get_LS_Output = TRUE;
}

combo BOOSTED_STEPOVER_ORIG  {
	if (right_on) dEnd = zone_p + 1;
    else dEnd = zone_p - 1;
	calc_relative_xy(dEnd);
	RA_UP ();
	wait(w_rstick);	
	RA_L_R();
	LA (move_lx,move_ly);
	wait(w_rstick);	
	LA (move_lx,move_ly);
	wait(500);
    Get_LS_Output = TRUE;
}

combo FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down                  
    wait(w_rstick);     
    Get_LS_Output = TRUE;
}

combo SKILLED_BRIDGE_cmb {
    set_val(PaceCtrol,100);
    RA_UP();
    wait(w_rstick);
    set_val(PaceCtrol,100);
    RA_ZERO();
    wait(w_rstick);
    set_val(PaceCtrol,100);
    RA_DOWN();
    wait(w_rstick);
    Get_LS_Output = TRUE;
}

combo SCOOP_TURN_FAKE {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PaceCtrol,100);
    wait(40); 
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
    wait(250);
    LA_L_R()
    wait(300); 
    Get_LS_Output = TRUE;
}        
                                                                
///////////////////////////////////////////////////////////////////
// 1. Fake Shot           ////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo FAKE_SHOT {        
	set_val(ShotBtn,100);  
	wait(40);              
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	wait(60);             
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);           
    Get_LS_Output = TRUE;
}                  
                                                                
///////////////////////////////////////////////////////////////////
// 2.  Heel to Heel ///////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo HEELtoHEEL {                        
	RA_UP();       // up                     
	wait(w_rstick);                          
	RA_ZERO ();    // ZERO                   
	wait(w_rstick);                          
	RA_DOWN ();    // down                  
	wait(w_rstick);                         
    Get_LS_Output = TRUE;
}                                        
                                         
                                                                
///////////////////////////////////////////////////////////////////
// 3. RAINBOW   //////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////
combo RAINBOW {          
	RA_DOWN ();    // down 
	wait(w_rstick);        
	RA_UP();       // up   
	wait(w_rstick);        
    Get_LS_Output = TRUE;
}                       
                        
combo DRAG_BACK {            
	set_val(MOVE_X,inv(LX));    
	set_val(MOVE_Y,inv(LY));   
 set_val(FinesseShot,100);  
 set_val(PlayerRun,100);  
	wait(60);                  
	set_val(MOVE_X,inv(LX));    
	set_val(MOVE_Y,inv(LY));   
 set_val(FinesseShot,100);  
 set_val(PlayerRun,100);  
	if(Sombrero) set_val(PS4_R3,100);
	wait(40);                  
    Get_LS_Output = TRUE;
}                            
                                                                
//////////////////////////////////////////////////////////////
// 2. STEP OVER  /////////////////////////////////////////////
//////////////////////////////////////////////////////////////
combo STEP_OVER_FEINT {      
    RA_UP ();         // up   
	wait(w_rstick);             
	RA_L_R ();       // <-/->   
	wait(w_rstick);             
    Get_LS_Output = TRUE;
}                            
/////////////////////////////// 
// Drag to Drag       
combo DRAG_TO_DRAG {    
	LA(0,0);             
	set_val(PaceCtrol,100);
	wait(40);            
	LA(0,0);             
	set_val(PaceCtrol,100);
	set_val(ShotBtn,100);
	wait(40);            
	LA(0,0);             
	set_val(PaceCtrol,100);
	set_val(ShotBtn,100);
	set_val(PassBtn,100);
	wait(80);            
	LA(0,0);             
	set_val(PaceCtrol,100);
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	wait(60);            
    Get_LS_Output = TRUE;
}                     
combo HOCUS_POCUS {     
	RA_DOWN (); // Down    
	wait(w_rstick);        
	right_on = FALSE;      
	RA_L_R () ;    // L    
	wait(w_rstick);        
	RA_DOWN ();    // down 
	wait(w_rstick);        
	right_on = TRUE;      
	RA_L_R () ;    // R    
	wait(w_rstick);        
    Get_LS_Output = TRUE;
}                      
combo TRIPLE_ELASTICO { 
	RA_DOWN ();      // Down 
	wait(w_rstick);         
	right_on = TRUE;      
	RA_L_R () ;    // R  
	wait(w_rstick);       
	RA_DOWN ();    // down 
	wait(w_rstick);       
	right_on = FALSE;     
	RA_L_R () ;    // L   
	wait(w_rstick);      
    Get_LS_Output = TRUE;
}                    
combo ELASTICO  {  
	right_on = TRUE;   
	RA_L_R () ;    // R 
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);      
	right_on = FALSE;    
	RA_L_R () ;    // L 
	wait(w_rstick);     
    Get_LS_Output = TRUE;
}                   
combo REVERSE_ELASTICO  {  
	right_on = FALSE;   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = TRUE;   
	RA_L_R () ;    // L 
	wait(w_rstick);   
    Get_LS_Output = TRUE;
}                  
combo ELASTICO_SHOP_cmb  {  
	set_val(FinesseShot,100);   
	RA_L_R () ;    // R  
	wait(w_rstick);      
	set_val(FinesseShot,100);   
	RA_DOWN ();    // down
	wait(w_rstick);     
	right_on = !right_on;   
	set_val(FinesseShot,100);   
	RA_L_R () ;    // L 
	wait(w_rstick);   
    Get_LS_Output = TRUE;
}                  
combo BAL_ROLL_CUT_180_cmb  {  
	set_val(PlayerRun,100);   
	RA_DOWN ();    // down
	wait(w_rstick);      
	set_val(PlayerRun,100);   
	RA_ZERO ();    // zero
	wait(w_rstick);      
	set_val(PlayerRun,100);   
	RA_DOWN ();    // down
	wait(w_rstick);     
    Get_LS_Output = TRUE;
}                  
///////////////////////////////
// 1. Cruyff Turn      
combo CRUYFF_TURN {    
	set_val(ShotBtn,100); 
	wait(40);             
	LA (inv(LX),inv(LY)); 
	set_val(ShotBtn,100); 
	set_val(PassBtn,100); 
	wait(80);             
	LA (inv(LX),inv(LY)); 
	set_val(ShotBtn,0);   
	set_val(PassBtn,100); 
	wait(60);             
    Get_LS_Output = TRUE;
}                      
combo LA_CROQUETA {        
  set_val(PlayerRun,100);  
  RA_L_R ();      // <-/-> 
  wait(500);//            
    Get_LS_Output = TRUE;
}                         
combo ROULETTE {         
	RA_DOWN ();     // down 
	wait(w_rstick);         
	RA_L_R ();      // <-/->
	wait(w_rstick);         
	RA_UP ();       // up   
	wait(w_rstick);         
    Get_LS_Output = TRUE;
}                        
///////////////////////////////
// Ball Roll                   
combo BALL_ROLL {               
    RA_L_R () ;    // Left or Right 
    set_val(SprintBtn,0);
    sensitivity(PS4_LX, 50, 40);
    sensitivity(PS4_LY, 60, 40);
    wait(310); 
    sensitivity(PS4_LX, 50, 150);
    wait(100);     
    Get_LS_Output = TRUE;
}          
//////////////////////////////////////////////////////   
// 20. Berba / Mcgeady Spin  / 21. Bolasie Flick + R1 / 32 Ball Roll Fake Turn L2 + Berba Spin 
combo TURN_AND_SPIN {  
  if(ACTIVE == BALL_ROLL_FAKE_TURN ) hold_btn = 200;//  Ball Roll Fake Turn L2 
	else hold_btn = 1;      
 wait(hold_btn);
	RA_UP ();      // up   
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);          
	RA_L_R () ;    // Left or Right 
	wait(w_rstick);    
    Get_LS_Output = TRUE;
}                   
////////////////////////////////////
//  Tornado Spin + L1    
combo TORNADO_SPIN {     
	RA_DOWN ();    // down  
	wait(w_rstick);         
	RA_ZERO ();    // ZERO  
	wait(w_rstick);         
	RA_L_R ();     //  <-/-> 
	wait(w_rstick);       
    Get_LS_Output = TRUE;
}                     
/////////////////////////////// 
//25.  Ball Roll Chop           
combo BALL_ROLL_CHOP {         
	RA_L_R () ;    // Left or Right 
	wait(300);                     
	RA_ZERO ();    // ZERO         
	wait(w_rstick);                
	RA_OPP () ;    // Left or Right
	wait(300);                    
    Get_LS_Output = TRUE;
}                             
combo FEINT_EXIT {             
	RA_OPP ();                    
	wait(w_rstick);               
	RA_DOWN (); // down           
	wait(w_rstick);               
	RA_L_R ();  //  <-/->         
	wait(w_rstick);               
    Get_LS_Output = TRUE;
}                            
////////////////////////////////////////////////////////////// 
//28. LATERAL HEEL to HEEL /////////////////////////////////// 
////////////////////////////////////////////////////////////// 
// + L1   PlayerRun                  
combo LATERAL_HEELtoHEEL {  
    set_val(PlayerRun,100);
    RA_OPP () ;            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_ZERO ();            
    wait(60);//            
    set_val(PlayerRun,100);
    RA_L_R () ;            
    wait(60);//           
    wait(300);            
    Get_LS_Output = TRUE;
}                         
combo WAKA_WAKA {      
	RA_OPP();  // L       
	wait(w_rstick);       
	LA (0,0);             
	RA_UP();       // up  
	wait(w_rstick);       
                       
	LA (0,0);             
	RA_L_R()     // L     
	wait(w_rstick);       
	right_on = !right_on; 
	LA_L_R();             
	wait(1000);           
    Get_LS_Output = TRUE;
}                      
                       
combo BODY_FEINT  {  
	RA_L_R () ;    // R 
	wait(100);      
 RA_ZERO ();     
	wait(80);      
	LA_L_R();       
	wait(600);      
	wait(600);      
    Get_LS_Output = TRUE;
}                
combo FEINT_FORWARD {
 LA (0,0);           
	wait(w_rstick);     
 LA (0,0);           
	RA_DOWN (); // down 
	wait(w_rstick);     
 LA (0,0);           
	RA_ZERO (); // ZERO 
	wait(w_rstick);     
 LA (0,0);          
	RA_DOWN (); // down 
	wait(w_rstick);    
    Get_LS_Output = TRUE;
}                   
combo  TURN_BACK  {       
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);  
	RA_DOWN ();             
	wait(80);             
    Get_LS_Output = TRUE;
}                  
combo ADVANCED_CROQUETA { 
  set_val(PlayerRun,100); 
  RA_L_R ();      // <-/->
  wait(300);//            
    set_val(PS4_L2,100);     
    set_val(PS4_R2,100);     
  LA_L_R();               
  wait(800);// 800        
    Get_LS_Output = TRUE;
}                         
combo CANCELED_THREE_TOUCH_ROULETTE { 
    RA_DOWN ();    // down       
    wait(w_rstick);              
    RA_ZERO ();    // ZERO      
    wait(w_rstick);             
    RA_L_R ();     //  <-/->    
    wait(w_rstick);             
    set_val(PS4_L2,100);     
    set_val(PS4_R2,100);     
    //LA_L_R();                 
    wait(300);// 800            
    Get_LS_Output = TRUE;
}                              
                                                                
//////////////////////////////////////////////////////////////
// 37. REVERSE STEP OVER  ///////////////////////////
//////////////////////////////////////////////////////////////
combo REVERSE_STEP_OVER {      
	RA_L_R ();       // <-/->   
	wait(w_rstick);             
     RA_UP ();         // up   
	wait(w_rstick);             
    Get_LS_Output = TRUE;
}                            
combo FAKE_DRAG_BACK {   
   LA (inv(LX),inv(LY)); 
   wait(200); //350  
   right_on = FALSE;
   LA_L_R ();            
   wait(50);   //120         
   right_on = !right_on; 
   LA_L_R ();            
   wait(540);           
    Get_LS_Output = TRUE;
}   

combo RAINBOW_TO_SCORPION {          
	RA_DOWN ();    // down 
	wait(40);
	RA_UP(); // up 
	wait(190);//200
	set_val(PaceCtrol,100);// L2
	wait(30);
	set_val(PaceCtrol,100);// L2
	set_val(ShotBtn,100);  // Shoot
	wait(180);        	   
    Get_LS_Output = TRUE;
}                       
combo CANCEL_SHOOT {
     set_val(ShotBtn,100);
     wait(290);
     set_val(PS4_L2,100);
     set_val(PS4_R2,100);
     wait(300);
    Get_LS_Output = TRUE;
}
combo NUTMEG_SKILL {
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	wait(20);
	set_val(FinesseShot,100);
	set_val(PlayerRun,100);
	if (right_on) dEnd = zone_p + 1;
    else { 
    	dEnd = zone_p - 1;
		if(dEnd < 0 ) dEnd = 7;
	}
	calc_relative_xy(dEnd);
    RA (move_lx,move_ly);
    set_val(SprintBtn,100);
	wait(100);    
    Get_LS_Output = TRUE;
}
combo CANCELED_TURN_AND_SPIN {  
    RA_UP ();      // up   
    wait(w_rstick);         
    RA_ZERO ();    // ZERO  
    wait(w_rstick);          
    RA_L_R () ;    // Left or Right 
    wait(w_rstick);
    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
    wait(200);
    if( ACTIVE == CANCELED_BERBA_SPIN_WITH_DIRECTION) LA_L_R();
    wait(300);
    Get_LS_Output = TRUE;
}    
combo BALL_ROLL_SCOOP_TURN {
    RA_L_R () ;
    wait(280);
    LA_L_R()
    set_val(ShotBtn,100); 
    set_val(PaceCtrol,100);
    wait(40); 
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(60);
    LA_L_R()
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(60);
    Get_LS_Output = TRUE;
}        
combo BALL_ROLL_STEP_OVER_cmb { 
	RA_L_R (); //  <-/->  
	wait(300);    
	RA_UP();      
	wait(60);   
    Get_LS_Output = TRUE;
}          
combo CANCEL_FOUR_TOUCH_TURN_cmb {
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down  
    wait(w_rstick);
    set_val(PaceCtrol,100);
    wait(30);
    set_val(PaceCtrol,100);
    RA_DOWN ();    // down 
    wait(w_rstick);
    LA(0,0);
    wait(400);
    set_val(PS4_L2,100);
    set_val(PS4_L1,100);
    set_val(PS4_R1,100);
    set_val(PS4_R2,100);
    wait(70);      
    Get_LS_Output = TRUE;
}
combo FAKE_SHOT_CANCEL_cmb {        
    set_val(ShotBtn,100);  
    wait(40);              
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);             
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    wait(140);
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
    wait(100);
    Get_LS_Output = TRUE;
}                  

combo OKKOSHA_FLICK_cmb {        
  set_val(PlayerRun,100);
  RA_UP ();      // <-/-> 
  wait(300);//            
    Get_LS_Output = TRUE;
} 
combo ADVANCED_RAINBOW_cmb { 
    LA(LX,LY);
	RA_DOWN ();    // down 
	wait(100); 
	RA_ZERO();     // Zero 
	LA(LX,LY);
	wait(40);
	RA_UP(); 
	LA(LX,LY);      // up   
	wait(320); 
	LA(LX,LY);
	RA_ZERO();     // Zero   
	wait(220); 
	LA(LX,LY);
	RA_UP(); 
    LA(LX,LY);       // up   
    wait(100);
    Get_LS_Output = TRUE;
} 

combo STOP_LA_CROQUETA_cmb {
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(LA_CROQUETA);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo JUGGLING_RAINBOW_cmb {
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  wait(60);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol,100);
  RA_DOWN ();    // down 
  wait(70);      
  set_val(PaceCtrol,100);
  RA_ZERO();     // Zero 
  wait(40);
  set_val(PaceCtrol,100);
  RA_UP();        // up   
  wait(70);
  set_val(PaceCtrol,100);
  wait(800);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}
combo STOP_NEYMAR_ROLL_cmb {
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  RA_L_R();
  wait(200);
  RA_L_R();
  LA(LX,LY);
  wait(125);
  wait(300);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}
combo STOP_V_DRAG_cmb {
LS_BlockOutput = TRUE;
    call(STOP_PLAYER_cmb);
    set_val(SprintBtn,100);
    wait(125);
    set_val(ShotBtn,100); 
    set_val(SprintBtn,100);
    wait(30); 
    LA_L_R();
    set_val(SprintBtn,100);
    set_val(ShotBtn,100);
    set_val(PassBtn,100); 
    wait(30);
    LA_L_R();
    set_val(SprintBtn,100);
    set_val(ShotBtn,0);
    set_val(PassBtn,100);
    wait(30);
    LA_L_R();
    wait(400);
    LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo REV_OR_ELASTICO_cmb {
  LS_BlockOutput = TRUE;
  RA_OPP (); // down  
  wait(w_rstick);  
  RA_DOWN (); // down  
  wait(w_rstick);
  RA_L_R ();  //  <-/->   
  wait(w_rstick); 
  wait(300);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}
combo STOP_REV_OR_ELASTICO_cmb {
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  call(FEINT_EXIT);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo DRAG_REV_OR_ELASTICO_cmb {
  LS_BlockOutput = TRUE;
  zone_saver();
  call(DRAG_BACK);
  wait(280);
  RA_OPP ();                    
  wait(w_rstick);               
  RA_DOWN (); // down  
  wait(w_rstick);               
  RA_L_R ();  //  <-/->   
  wait(w_rstick); 
  wait(300);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo FAKE_RABONA_cmb{
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    wait(40);
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
    wait(60);
    LA(inv(LX),inv(LY));
    set_val(PaceCtrol,100);
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
    wait(60);
    LA(0,0);
    wait(300);
    Get_LS_Output = TRUE;
}

combo RABONA_TO_REV_ELASTICO_cmb {
  LS_BlockOutput = TRUE;
  zone_saver(); 
  call(FAKE_RABONA_cmb);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  //1//
  RA_UP();   // original elastico is (RA_OPP())
  wait(w_rstick); 
  //2//
  right_on = FALSE; //ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R ();
  wait(w_rstick);    
  //3//
  RA_DOWN ()  // original elastico is (RA_L_R())
  wait(w_rstick); 
  wait(400);
  //RA functions rotation done
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo RABONA_TO_ELASTICO_cmb {
  LS_BlockOutput = TRUE;
  zone_saver(); 
  call(FAKE_RABONA_cmb);
  wait(100);
  // NOW player is at zone_p+2 ( example : if he was running up , after fake rabona he face right ).
  // now I will perform reverse - elastico  manually to fit face right .
  // LX,LY values still for running (UP) ,, so we will make the rotation for RA Functions instead of using +2 zone_p method.
  //1//
  RA_DOWN();       // original elastico is (RA_OPP())                
  wait(w_rstick); 
  //2//
  right_on = FALSE; //ALWAYS to player back which is left direction in our case (REV-ELASTico) // original elastico is (RA_DOWN())
  RA_L_R ();
  wait(w_rstick);    
  //3//
  RA_UP ()    // original elastico is (RA_L_R())
  wait(w_rstick); 
  wait(400); 
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}
combo SOMBRERO_FLICK_cmb {
LS_BlockOutput = TRUE;
    call(STOP_PLAYER_cmb);
    wait(100);
    RA_UP();
    wait(50);
    RA_ZERO ()
    wait(50);
    RA_UP()
    wait(50);
    RA_ZERO ()
    wait(50);
    RA_DOWN(); 
    wait(50);
    wait(700);
    LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}
combo JUGGLE_BACK_SOMBRERO_cmb {
  LS_BlockOutput = TRUE;
  call(STOP_PLAYER_cmb);
  wait(100);
  call(JUGGLING_cmb);
  call(JUGGLING_cmb);
  set_val(PaceCtrol,100);
  set_val(FinesseShot,100);
  LA(inv(LX),inv(LY));
  wait(400);
  LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
}

combo FAKE_BARBA_OPP_EXIT_cmb{
	LA(LX,LY);
	RA_UP();
	wait(w_rstick);
	LA(LX,LY);
	RA_ZERO();
	wait(w_rstick);
	LA(LX,LY);
	RA_L_R();
	wait(w_rstick);
	set_val(PaceCtrol,100);
	set_val(SprintBtn,100);
	LA(inv(LX),inv(LY));
	wait(600);
    Get_LS_Output = TRUE;
}
combo DIAGONAL_HEEL_CHOP_cmb {

    if(right_on) dEnd = zone_p + 3;
    else dEnd = zone_p - 3;
    calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    set_val(ShotBtn,100); 
    wait(40);             
    LA (move_lx,move_ly); 
    set_val(ShotBtn,100); 
    set_val(PassBtn,100); 
    wait(80);             
    LA (move_lx,move_ly);
    set_val(ShotBtn,0);   
    set_val(PassBtn,100); 
    wait(60); 
    LA (move_lx,move_ly);
    wait(300);
    Get_LS_Output = TRUE;
}             
combo FAKE_BARBA_TO_FAKE_DRAG_cmb{
    LA(LX,LY);
    LS_BlockOutput = TRUE;
    set_val(XB1_LS,100);
    RA_UP();
    wait(w_rstick);
    LA(LX,LY);
    RA_ZERO();
    set_val(XB1_LS,100);
    wait(w_rstick);
    LA(LX,LY);
    RA_L_R();
    wait(w_rstick);
    set_val(PaceCtrol,100);
    set_val(SprintBtn,100);
    if(right_on) dEnd = zone_p + 4;
    else dEnd = zone_p - 4;
    calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    wait(220);
    if(right_on) dEnd = zone_p + 4;
    else dEnd = zone_p - 4;
    calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    wait(40);
    if(right_on) dEnd = zone_p + 1;
    else dEnd = zone_p - 1;
    calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    wait(600);
    LS_BlockOutput = FALSE;
    Get_LS_Output  = TRUE;
}
                         
combo HEEL_to_BALL_ROLL_cmb { 
	  LS_BlockOutput = TRUE;
    set_val(PlayerRun,100);
    RA_UP();  
    LA(0,0);// up     
    wait(w_rstick);
    set_val(PlayerRun,100);
    RA_ZERO ();    // ZERO 
    LA(0,0);
    wait(w_rstick); 
    set_val(PlayerRun,100);
    LA(0,0);
    RA_DOWN ();    // down 
    wait(w_rstick);
    if(right_on) dEnd = zone_p + 1;
    else dEnd = zone_p - 1;
    calc_relative_xy(dEnd);
    LA (move_lx,move_ly);
    wait(200);
    LS_BlockOutput = FALSE;
    Get_LS_Output = TRUE;
} 
combo STUTTER_FEINT_cmb {
	set_val(PaceCtrol,100);// hold L2
	RA_L_R();       // lef/right                    
	wait(w_rstick);
	right_on = !right_on;
	set_val(PaceCtrol,100);// hold L2
	RA_L_R();       // lef/right                    
	wait(w_rstick);
    Get_LS_Output = TRUE;
}
combo JOG_OPENUP_FAKE_SHOT_cmb {
	set_val(PlayerRun,100);
	set_val(CrossBtn,100);  
	wait(40); 
	set_val(PlayerRun,100);
	set_val(CrossBtn,100);  
	set_val(PassBtn,100); 
	wait(60);
	set_val(PlayerRun,100);
	set_val(CrossBtn,0);  
	set_val(PassBtn,100);
	LA_L_R();
	wait(60);
	set_val(PlayerRun,100); 
	LA_L_R();
	wait(300);
    Get_LS_Output = TRUE;
}        

//--------------------------------------------------------------   
//======================================== 
//  Penalties FIFA 23  v. 1.0                                    
//========================================
function fPenalties() {

  if (get_ival(ShotBtn)) { // Power limiter
    set_val(ShotBtn, 0);
    if (get_ival(PlayerRun)) PN_Shot_Power = 179; // chip shot penalties
    if (!get_ival(PlayerRun)) PN_Shot_Power = 470;
    combo_run(RESET_PN);
  }
  if (PN_Angle > 0) set_polar(POLAR_LS, inv(PN_Angle), 32767); // Aim - Lock
  if (get_ival(XB1_RIGHT) && get_ival(XB1_DOWN)) PN_Angle = 345; // DOWN_RIGHT 
  if (get_ival(XB1_RIGHT) && get_ival(XB1_UP)) PN_Angle = 45; // UP_RIGHT
  if (get_ival(XB1_LEFT) && get_ival(XB1_UP)) PN_Angle = 135; // UP_LEFT
  if (get_ival(XB1_LEFT) && get_ival(XB1_DOWN)) PN_Angle = 225; // DOWN_LEFT
}
int PN_Shot_Power;
int onoff_penalty;
int PN_Angle;
combo RESET_PN {
  set_val(ShotBtn, 100);
  vm_tctrl(0);wait(PN_Shot_Power);
  vm_tctrl(0);wait(50);
  vm_tctrl(0);wait(3600);
  onoff_penalty = !onoff_penalty; // reset after each penalty
}
/*         
=================================================================
 EM Polar V2                                                                                                                    
=================================================================
*/
int POLAR_RESET = TRUE;
int EM_Angles_Control;
int Passing_Angle;
int AngleInterval;
int Radius;
function EM_POLAR_V2() {
  combo_run(EM_DR);
  Pass_Assist();
  Polar_Aim_Assist();
  //Defense_Mod23();
  Enhamced_Move_4_5 ();
}
function Enhamced_Move_4_5 (){   
    // Support jockey or Defence Mode with enhanced movements 
    if ( get_ival(SprintBtn) && ( get_ival(PaceCtrol) || get_ival(PassBtn) )  ) {    
         sensitivity(PS4_LX, 50, 106);
         combo_stop(LS_DRIBBLING_4_5_cmb);
         combo_stop(SENSITIVITY_HOLDER_4_5_cmb);
    }
    
    if ( get_ival(SprintBtn) && (!get_ival(PaceCtrol)) ) {  // support of sprint exits 
         sensitivity(PS4_LX, 50, 106);              
         combo_stop(LS_DRIBBLING_4_5_cmb);
         combo_stop(SENSITIVITY_HOLDER_4_5_cmb);
    }
    /////////Temp Block LS Dribbling ///////////
    if (( get_ival(XB1_LS) || get_ival(XB1_RS) || get_ival(FinesseShot) || get_ival(PlayerRun)  || get_ival(PaceCtrol) ||  
    get_ival(PassBtn) || get_ival(ThroughBall) || get_ival(ShotBtn) || get_ival(CrossBtn)   || get_ival(XB1_PR1) || 
    get_ival(XB1_PR2) || get_ival(XB1_PL1) || get_ival(XB1_PL2) || ( (abs(get_ival(SKILL_STICK_X))> 45 || abs(get_ival(SKILL_STICK_Y))> 45) ) )){
        combo_stop(LS_DRIBBLING_4_5_cmb);
        combo_run(SENSITIVITY_HOLDER_4_5_cmb);
    }
    ///////// LS Dribbling in Action ///////////
    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(PaceCtrol) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(SENSITIVITY_HOLDER_4_5_cmb)
    && !get_ival(FinesseShot) && !get_ival(PlayerRun) &&  !get_ival(SprintBtn) && !get_ival(PassBtn) && !get_ival(ThroughBall) && !get_ival(ShotBtn)){
        combo_run(LS_DRIBBLING_4_5_cmb);
    }
}
combo LS_DRIBBLING_4_5_cmb {     
	sensitivity(PS4_LX, 38, 65);
	sensitivity(PS4_LY, 38, 66);
	wait(60);
	wait(60);
	sensitivity(PS4_LX, 38, 91);
	sensitivity(PS4_LY, 38, 91);
	wait(60);
}
   
combo SENSITIVITY_HOLDER_4_5_cmb {
	sensitivity(PS4_LX, 50, 100);
	sensitivity(PS4_LY, 50, 100);
	wait(1100);
} 
combo EM_DR {
movements();
Boost_Radius();
wait(random(60,70));
Boost_Radius();
wait(random(60,70));
}
int MoveMentsBooster = 4;
function Boost_Radius(){
if (get_ival(PS4_LX) >  12) set_val(PS4_LX,get_ival(PS4_LX) + MoveMentsBooster);
if (get_ival(PS4_LX) < -12) set_val(PS4_LX,get_ival(PS4_LX) - MoveMentsBooster);
if (get_ival(PS4_LY) >  12) set_val(PS4_LY,get_ival(PS4_LY) + MoveMentsBooster);
if (get_ival(PS4_LY) < -12) set_val(PS4_LY,get_ival(PS4_LY) - MoveMentsBooster);
if (abs(get_ival(PS4_LX)) > 90 &&  abs(get_ival(PS4_LY)) < 10){set_val(PS4_LY,0);}

}

function movements() {
  AngleInterval = 2 * EM_Angles_Control;
  if (POLAR_RESET) {EM_Angles_Control = 360}  // 18*2 = 36 Angles of ball control.
  if ((abs(get_ival(PS4_LX)) > 17 || abs(get_ival(PS4_LY)) > 17) && !get_ival(SprintBtn) && !get_ival(PaceCtrol)){
      set_polar(POLAR_LS, ((inv(get_polar(POLAR_LS, POLAR_ANGLE) * AngleInterval) / 360) * 360) / AngleInterval, min(isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2)), 32767));
  }
  Passing_Angle = inv(~(get_polar(POLAR_LS, POLAR_ANGLE))) //(360/AngleInterval); // Anti-Clockwise
  
  if(get_ival(SprintBtn)){
  Boost_Radius();
  }
  
  }
int Pass_Or_Through;
function Pass_Assist() {
  if ((get_ival(PassBtn) || get_ival(ThroughBall)) && !get_ival(PlayerRun) && !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(ShotBtn)) {
    if (get_ival(PassBtn)) Pass_Or_Through = PassBtn;
    if (get_ival(ThroughBall)) Pass_Or_Through = ThroughBall;

    gpass_timer += get_rtime();
    combo_restart(PS_Assist);
  }
  if (event_release(PassBtn)) {
    if (gpass_timer < GPass_MIN) {
      GP_diff = GPass_MIN - gpass_timer;
      combo_run(PS_Assist);
    }
    gpass_timer = 0;
  }
  if (combo_running(PS_Assist) && (
      get_ival(SprintBtn)   ||
      get_ival(PaceCtrol)   ||
      get_ival(ShotBtn)     ||
      get_ival(FinesseShot) ||
      get_ival(XB1_PR1)     ||
      get_ival(XB1_PR2)     ||
      get_ival(XB1_PL1)     ||
      get_ival(XB1_PL2)
    )) {
    combo_stop(PS_Assist);
    POLAR_RESET = TRUE;
  }
  

}

function Polar_Aim_Assist() {
  if (get_ival(ShotBtn)  && !get_ival(SprintBtn))combo_run(FINISH_AIM_ASSIST);
}
int Finish_wait_factor = 100;
combo FINISH_AIM_ASSIST {
  vm_tctrl(0);
  LA(0,0);
  wait(Finish_wait_factor); // powering up
  POLAR_RESET = FALSE;
  if (!get_ival(PaceCtrol) && !get_ival(FinesseShot) && !get_ival(SprintBtn) ) EM_Angles_Control = 20; // 20 * 2 = 40 angles of normal finishing .
  else if (!get_ival(PaceCtrol) && !get_ival(SprintBtn) && get_ival(FinesseShot) ) EM_Angles_Control = 4 ;  // 4*2 =  8 angles of Finesse / Trivela finishing .
  vm_tctrl(0);
  wait(1000);
  POLAR_RESET = TRUE;
}
int roll_angle;
int GPass_MIN = 35;
int GP_diff;
int gpass_timer;
combo PS_Assist {
  zone_saver();
  roll_angle = inv(~(get_polar(POLAR_LS, POLAR_ANGLE)));
  POLAR_RESET = FALSE;
  set_val(XB1_LS, 100);
  EM_Angles_Control = 360; // 14*2 = 28 Angles
  set_val(Pass_Or_Through, 100);
  Radius = 32767 - 5000;
  vm_tctrl(0);wait(GP_diff);
  POLAR_RESET = TRUE;
}
function Defense_Mod23() {
if(get_ival(ShotBtn))set_val(PassBtn, 0);
  if (get_ival(SprintBtn) && !get_ival(ShotBtn)) {
    if (((get_ival(PaceCtrol) || get_ival(PassBtn)))) {
      set_val(PassBtn, 0);
      set_val(PaceCtrol, 100);
    } else {
      POLAR_RESET = TRUE;
    }
  }
}

////////////////////EM - END/////////////////////
/////////////////////////////////////////////////

  ///////////FK Combos//////////
int free_kick_power = 375;
int FreeKick_on_off;  
int fk_right_on;       
combo  FK_CURVE_cmb { 
   if(fk_right_on)set_val(XB1_RX,100);
   else  set_val(XB1_RX,-100);
   wait(350); 
   wait(200);
   set_val(XB1_LY,-100);
   set_val(ShotBtn,100);
   wait(free_kick_power);
   wait(50);
   set_val(PaceCtrol,100);
   wait(1200);
   FreeKick_on_off = FALSE;
   f_set_notify(FreeKick_on_off);
}

int corner_power = 476;
///// Corner combos /////////
int Corner_on_off;
int corn_right_on ;
combo CORNER_CURVE_cmb {
   if(corn_right_on)set_val(XB1_RX,-100); 
   else set_val(XB1_RX,100);
   set_val(XB1_RY,100);
   wait(350);
   wait(50);
   set_val(XB1_RY,-100);
   wait(450);  
   wait(50);
   if(corn_right_on)set_val(XB1_LX,100);
   else set_val(XB1_LX,-100);
   wait(90);
   wait(50);
   set_val(XB1_LY,-100);
   set_val(CrossBtn,100);
   set_val(FinesseShot,100);
   set_val(PaceCtrol,100);
   wait(corner_power);
   wait(50);
   set_val(CrossBtn,100);
   wait(50);
   Corner_on_off = FALSE;
   f_set_notify(Corner_on_off);
}

int OpenUp_on_off = TRUE;
combo OPEN_UP_cmb { 
    wait(160);  
    set_val(PS4_L3,100); 
    wait(100);           
}     

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2); 
} 
const string OFF   = "OFF";       
const string ON    = "ON";

   //=======================================
   //  DISPLAY EDIT VALUE ON THE FLY        
   //=======================================
function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 
const string EA_PING = "EA PING";   
   
   
/*   
=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}
/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
} 
int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}
combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}            

int OnOFF_Skills;
int ModifierBtn;
int dubltime;
define ColorOFF  = 0;
define Blue      = 1;
define Red       = 2;
define Green     = 3;
define Pink      = 4;
define SkyBlue   = 5;
define Yellow    = 6;
define White     = 7;
                      
data(                 
  0,0,0, //0. ColorOFF
  2,0,0, //1. Blue     
  0,2,0, //2. Red      
  0,0,2, //3. Green    
  2,2,0, //4. Pink     
  2,0,2, //5. SkyBlue 
  0,2,2, //6. Yellow   
  2,2,2  //7. White    
); // end of data segment-------------- 
// COLOR LED function        
//-------------------------------------------------------------- 
                                       
int data_indx;
function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int EA_Ping = 20;
const int8 SelectBtn [] ={
XB1_XBOX, // 0 
XB1_VIEW, // 1 
XB1_MENU, // 2 
XB1_RB,   // 3 
XB1_RT,   // 4 
XB1_RS,   // 5 
XB1_LB,   // 6 
XB1_LT,   // 7 
XB1_LS,   // 8 
XB1_UP,   // 9 
XB1_DOWN, // 10  
XB1_LEFT, // 11  
XB1_RIGHT,// 12  
XB1_Y,    // 13 
XB1_B,    // 14 
XB1_A,    // 15 
XB1_X,    // 16 
XB1_PR1,  // 17 
XB1_PR2,  // 18 
XB1_PL1,  // 19 
XB1_PL2,  // 20 
PS4_TOUCH // 21  
}

int ShootPower;
int DynamicFinish_on_off    = TRUE;
int low_driven_finesse_btn  = XB1_RS;
int LowDrivenFinesse_on = TRUE;
int Through_LOP_Timer;
int cross_timer; 
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int Pass_Timer;
int UT_Ping = 20;  // 
int DYN_Acc;     
int power_time;
int final_power;
int outsidee_shot_trigger = FinesseShot;

const uint8 power_rest[] = { 60, 214, 225 };

function f_dynamic_finish () {

                              
    UT_Ping = EA_Ping;
    
    	if (event_release(PassBtn)) { // 
		Pass_Timer = 3500;
	}
	if (event_release(SprintBtn)) {
		after_sprint_timer = 3000; //  
	}

	if (event_release(CrossBtn)) {
		cross_timer = 4000; // 
	}
	if (cross_timer) {
		cross_timer -= get_rtime();
	}
	if (event_release(ThroughBall)) {
		Through_LOP_Timer = 3500; //
	}
	if (Pass_Timer) {
		Pass_Timer -= get_rtime();
	}
	if (Through_LOP_Timer) {
		Through_LOP_Timer -= get_rtime();
	}
	if (after_sprint_timer) {
		after_sprint_timer -= get_rtime();
	}

 //>>>>>> Driven-Finish <<<<<<<//
 // user selected button -------------
    if(LowDrivenFinesse_on){
        if( get_val(low_driven_finesse_btn)) {               
            if (Pass_Timer < 1200 ) { 
                set_val(ShotBtn,  0); 
                UltimatePower = random(90,100);
                DYN_Acc = random(185,188);
                combo_run(Driven_cmb);
            }
            if (Pass_Timer > 1200 || after_sprint_timer > 1500) { 
                set_val(ShotBtn,  0); 
                UltimatePower = random(90,100);
                DYN_Acc = random(350,360);
                combo_run(Driven_cmb);
            }
        } 
    }///End of driven
   //--------------------------------------------------------------------------  



  if (!get_ival(FinesseShot) && !get_ival(PaceCtrol) && !get_ival(PlayerRun) && !get_ival(SprintBtn) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2) && !get_ival(XB1_PL1) && !get_ival(XB1_PL2)) {
  
    // Force a hold
  if (event_press(ShotBtn))
    combo_run(HOLD_SHOT);

  // Current hold time
  if (get_ival(ShotBtn))
    power_time += get_rtime();
  else power_time = 0;

  // Reset/block??
  if (power_time >= 220) {
    set_val(ShotBtn, 0);
    //combo_stop(HOLD_SHOT); // Maybe this instead??
    power_time = 0;
  }

  // Countdown 'final_power'
  if (combo_running(HOLD_SHOT))
    final_power = max(0, final_power - get_rtime());

  // Set 'final_power' based on hold time range
  if(power_time)
    if (power_time < 120)
      final_power = power_rest[0];
    else if (power_time < 215)
      final_power = power_rest[1];
    else if (power_time < 220)
      final_power = power_rest[2];

  // Stop hold at end of countdown
  if (!final_power)
    combo_stop(HOLD_SHOT);
    set_val(ShotBtn,0);
  set_val(TRACE_1, final_power);

}
              
//>>>>>> OUTSIDE-Box <<<<<<<//
 // Trivela Shot ------------------------------------------------------------------  
  if(event_press(XB1_PL2)){  ///
  //  if(!get_ival(PaceCtrol) && get_ival(ShotBtn) && get_ival(outsidee_shot_trigger)){  ///
    set_val(outsidee_shot_trigger,0);
    combo_run(outsidee_shot_disable);
    set_val(outsidee_shot_trigger,0);
    set_val(ShotBtn,0);
    UltimatePower = random(265,270);
    DYN_Acc = random(180,185);
        //------------------------------
        if (after_sprint_timer < 1500){
            vm_tctrl(0);
            set_val(ShotBtn,0);
            UltimatePower = random(265,270);
            DYN_Acc = random(130,135);
            combo_run(OutSideBox_Finishing_cmb);
        }
        //------------------------------
        if (Pass_Timer < 1200 && after_sprint_timer < 3000 && after_sprint_timer > 1500){
            set_val(ShotBtn,0);
            UltimatePower = random(265,270);
            DYN_Acc = random(100,110);
            combo_run(OutSideBox_Finishing_cmb);
        }
        //------------------------------    
        if (Pass_Timer > 1200 && after_sprint_timer < 3000 && after_sprint_timer > 1500){
            set_val(ShotBtn,0);
            UltimatePower = random(265,270);
            DYN_Acc = random(150,160);
            combo_run(OutSideBox_Finishing_cmb);
        }
    }// End Of Outside Conditions
    //////////////////////////
    /////HEADERS FINISHING////
    //////////////////////////
                
    if(cross_timer > 0 && event_press(ShotBtn) && !get_ival(FinesseShot) && !get_ival(PlayerRun) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2) && !get_ival(XB1_PL1) && !get_ival(XB1_PL2)){
        set_val(ShotBtn,0);                
        vm_tctrl(0);
        UltimatePower = random(206,215); 
        DYN_Acc = random(700,800);
        combo_run(Header_Shoot_cmb); 
    }    

    /// FakeShot Support avoid Conflictions
    if (( combo_running(HOLD_SHOT) || combo_running(Driven_cmb) || combo_running(OutSideBox_Finishing_cmb)|| combo_running(Header_Shoot_cmb) )  ) { 
        vm_tctrl(0);
        if ( get_ival(PassBtn) || get_ival(PlayerRun) ){ 
            combo_stop(OutSideBox_Finishing_cmb);
            combo_stop(Header_Shoot_cmb);
            combo_stop(Driven_cmb);
            combo_stop(Timed_SafeShot_cmb);
            combo_stop(HOLD_SHOT);
        }
    }
}

combo HOLD_SHOT {
set_val(ShotBtn,100);
wait(220);
}
int drval;
combo Dynamic_Shoot {
    set_val(SprintBtn, 100);
    set_val(ShotBtn, 100);
    set_val(FinesseShot, drval);
     
    wait(UltimatePower);

    set_val(FinesseShot, 0);
    set_val(ShotBtn, 0);
     
    wait(50);
    
    set_val(ShotBtn, 0);
    set_val(PS4_L3,100);
    drval=0;
    UltimatePower=0;
    set_val(ShotBtn, 0);
    wait(600); 
}
combo Timed_SafeShot_cmb { 
wait(DYN_Acc + UT_Ping); // 
set_val(ShotBtn,100);
wait(random(60,100));
set_val(ShotBtn,0);
wait(700);
}

combo Driven_cmb {
    INSIDE_BOX_AIM(37,100);  
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100); 
    set_val(FinesseShot, 100); 
      
    wait(UltimatePower); /////  Shot power of DYN      
    set_val(ShotBtn, 0);
       
    wait(DYN_Acc + UT_Ping); // Dynamic Accurcey  
    set_val(ShotBtn, 100);   
      
    wait(60);   // TIMED Action   
    set_val(ShotBtn, 0);
      
      
    wait(80)
      
    set_val(PS4_L3,100);
      
    wait(900); 
}
combo InsideBox_Finishing_cmb {

    set_val(ShotBtn, 100); 
      
    wait(UltimatePower); /////  Shot power of DYN      
    INSIDE_BOX_AIM(37,100);
    set_val(ShotBtn, 0);
       
    wait(DYN_Acc + UT_Ping); // Dynamic Accurcey  
   // set_val(ShotBtn, 100);   
      
    wait(60);   // TIMED Action   
    set_val(ShotBtn, 0); 
    wait(80)
}
combo Header_Shoot_cmb {
    INSIDE_BOX_AIM(100,15);
        
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100); 
    wait(UltimatePower); /////  Shot power of DYN      
    set_val(ShotBtn, 0);
      
    wait(DYN_Acc + UT_Ping); // Dynamic Accurcey
    set_val(ShotBtn, 100);
      
    wait(60);   // TIMED Action
    set_val(ShotBtn, 0);
      
      
    wait(80)
      
    set_val(PS4_L3,100);
      
    wait(900);  
}
   combo OutSideBox_Finishing_cmb { 
    set_val(ShotBtn, 0);
    set_val(PaceCtrol, 0);
    set_val(PS4_L3,0);
    INSIDE_BOX_AIM(37,100);
    wait(160);
    INSIDE_BOX_AIM(37,100);
    set_val(PS4_L3,0);
    set_val(PaceCtrol, 100);
    set_val(ShotBtn, 100); 
    wait(UltimatePower); ///// 
    INSIDE_BOX_AIM(37,100);
    set_val(PaceCtrol, 100);
    set_val(ShotBtn, 0);
    wait(DYN_Acc + UT_Ping); 
    INSIDE_BOX_AIM(37,100);
    set_val(PaceCtrol, 100);
    set_val(ShotBtn, 100);
    wait(60);   // TIMED Action
    INSIDE_BOX_AIM(37,100);
    set_val(FinesseShot,0);
    set_val(ShotBtn, 0); 
    set_val(PaceCtrol, 100); 
    wait(80);
    INSIDE_BOX_AIM(37,100);
    set_val(PaceCtrol, 100);
    wait(900);            
}  

combo outsidee_shot_disable {
if(!get_ival(PaceCtrol))set_val(outsidee_shot_trigger,0);
wait(1400);
}

define AlwaysON        = 1;
define Double_Tap_Btn  = 2;
define Modifier_Button = 3;
define DoubleTapL3  = 2;
define DoubleTapR3  = 3;
define ModifierL3   = 4;
define ModifierR3   = 5;
define ModifierPR1  = 6;
define ModifierPR2  = 7;
define ModifierPL1  = 8;
define ModifierPL2  = 9;
int Skill_OnOff = FALSE;
int flick_rs;
int temp_zone;
function calc_temp_zone(user_zone){
    temp_zone = user_zone;
    if(temp_zone < 0 ) temp_zone = 8 - abs(user_zone);
    else if(temp_zone >= 8) temp_zone = user_zone - 8;
    return temp_zone;
}

int Chip_Power = 179; // Power of chip shot determind by user in S.G
combo Chip_Finish_cmb {
  set_val(ShotBtn, 100);
  set_val(PlayerRun, 100);
  vm_tctrl(0);wait(Chip_Power);
  vm_tctrl(0);wait(500);
}
int rs_val = 15;
function calc_RS(){
 if(get_ival(XB1_RX) >= rs_val) RS_X = 100;
    else if(get_ival(XB1_RX) <= inv(rs_val)) RS_X = -100;
    else RS_X = 0;
    if(get_ival(XB1_RY) >= rs_val) RS_Y = 100;
    else if(get_ival( XB1_RY) <= inv(rs_val)) RS_Y = -100;
    else RS_Y = 0;
    
    if(RS_X != 0 || RS_Y != 0) {
        zone_RS = 0; while(zone_RS < 8) {
            if(ZONE_P[zone_RS][0] == RS_X && ZONE_P[zone_RS][1] == RS_Y) {
                break;
            } zone_RS += 1;
        }
    }   
}
int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {     
 if(get_ival(PS4_LX) >= 12) AIM_X = f_LX ; 
 else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX) ;  
               
 if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY ;  
 else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}  
int KS_EntireScript = FALSE;






combo STOP_PLAYER_cmb {
  zone_saver(); 
  wait(20);
  wait(100);
  set_val(SprintBtn,100);
  wait(40);
  wait(160);
    Get_LS_Output = TRUE;
}

function zone_saver() {
dEnd = zone_p
calc_relative_xy(dEnd);
LX = move_lx;
LY = move_ly;
}

combo JUGGLING_cmb{
set_val(PaceCtrol,100);
set_val(FinesseShot,100);
wait(100);
set_val(PaceCtrol,100);
wait(100);
    Get_LS_Output = TRUE;
}
int LS_BlockOutput;
///////////////////////////////////////////////////
// ZONE FUNCTION
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_ival(PS4_LX) >= 35) move_lx = 100;
    else if(get_ival(PS4_LX) <= -35) move_lx = -100;
    else move_lx = 0;
    if(get_ival(PS4_LY) >= 35) move_ly = 100;
    else if(get_ival( PS4_LY) <= -35) move_ly = -100;
    else move_ly = 0;
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(ZONE_P[zone_p][0] == move_lx && ZONE_P[zone_p][1] == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
}
function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - abs(d);
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}                  
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                                                  