int LS_Angle;
int LS_Direction;

main {
 
LS_Angle = (360 - get_polar(POLAR_LS,POLAR_ANGLE));
LS_Direction = get_polar(POLAR_LS,POLAR_ANGLE) * -1;

    if(get_ival(XB1_PL1)){
        set_val(XB1_PL1,0);
    if(LS_Angle < 180)
        set_polar(POLAR_RS,(LS_Direction + 90  ), 32767);
    else if (LS_Angle >180)
        set_polar(POLAR_RS,(LS_Direction - 90  ), 32767);
    }

    if(get_ival(XB1_PR1)){
        set_val(XB1_PR1,0);
    if(LS_Angle < 180)
        set_polar(POLAR_RS,(LS_Direction - 90  ), 32767);
    else if (LS_Angle >180)
    set_polar(POLAR_RS,(LS_Direction + 90  ), 32767);
    }
}