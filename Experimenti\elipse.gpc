// Elliptical boundary for analog stick
// Creates smooth elliptical movement with configurable axes

// Constants for stick identification
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Ellipse axis ratios (percentage of maximum)
define X_RATIO = 100;    // X-axis scale (100 = full range)
define Y_RATIO = 10;     // Y-axis scale (60% of full range)

int x, y;

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Apply elliptical scaling
        x = (x * X_RATIO) / 100;
        y = (y * Y_RATIO) / 100;
        
        // Set modified values
        set_val(stickX, x);
        set_val(stickY, y);
    }
}