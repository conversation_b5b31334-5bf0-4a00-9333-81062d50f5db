import tkinter as tk
from tkinter import scrolledtext, messagebox, filedialog
import subprocess
import sys
import os
import threading

class DNSCacheViewerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("DNS Cache Viewer")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Create the main frame
        self.main_frame = tk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Platform detection label
        self.platform_label = tk.Label(
            self.main_frame, 
            text=f"Detected Platform: {self.get_platform_name()}",
            font=("Arial", 10, "bold")
        )
        self.platform_label.pack(anchor=tk.W, pady=(0, 5))
        
        # Create button frame
        self.button_frame = tk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Create buttons
        self.refresh_button = tk.But<PERSON>(
            self.button_frame, 
            text="Refresh DNS Cache", 
            command=self.refresh_dns_cache,
            padx=10
        )
        self.refresh_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_button = tk.Button(
            self.button_frame, 
            text="Clear Display", 
            command=self.clear_display,
            padx=10
        )
        self.clear_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_button = tk.Button(
            self.button_frame, 
            text="Save to File", 
            command=self.save_to_file,
            padx=10
        )
        self.save_button.pack(side=tk.LEFT)
        
        # Create text area with scrollbars
        self.text_area = scrolledtext.ScrolledText(
            self.main_frame, 
            wrap=tk.WORD, 
            font=("Courier New", 10)
        )
        self.text_area.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = tk.Label(
            self.main_frame, 
            textvariable=self.status_var, 
            bd=1, 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))
        
        # Initial message
        self.text_area.insert(tk.END, "Welcome to DNS Cache Viewer!\n")
        self.text_area.insert(tk.END, "Click 'Refresh DNS Cache' to view the current DNS cache.\n\n")
        self.text_area.insert(tk.END, f"This application is running on {self.get_platform_name()}.\n")
        
    def get_platform_name(self):
        """Returns a user-friendly platform name."""
        if sys.platform == "win32":
            return "Windows"
        elif sys.platform == "darwin":
            return "macOS"
        elif sys.platform.startswith("linux"):
            return "Linux"
        else:
            return f"Unsupported OS ({sys.platform})"
    
    def refresh_dns_cache(self):
        """Refresh the DNS cache display."""
        self.clear_display()
        self.status_var.set("Fetching DNS cache...")
        self.refresh_button.config(state=tk.DISABLED)
        
        # Run in a separate thread to keep UI responsive
        threading.Thread(target=self._fetch_dns_cache, daemon=True).start()
    
    def _fetch_dns_cache(self):
        """Fetch DNS cache in a separate thread."""
        try:
            # Redirect stdout to capture output
            import io
            from contextlib import redirect_stdout
            
            output = io.StringIO()
            with redirect_stdout(output):
                if sys.platform == "win32":
                    self.display_dns_cache_windows()
                elif sys.platform == "darwin":
                    self.display_dns_cache_macos()
                elif sys.platform.startswith("linux"):
                    self.display_dns_cache_linux()
                else:
                    print(f"Unsupported operating system: {sys.platform}")
                    print("This application currently supports Windows, macOS, and basic Linux checks.")
            
            # Update UI in the main thread
            self.root.after(0, self._update_text_area, output.getvalue())
            self.root.after(0, self._update_status, "DNS cache displayed successfully")
        except Exception as e:
            self.root.after(0, self._update_status, f"Error: {str(e)}")
        finally:
            self.root.after(0, lambda: self.refresh_button.config(state=tk.NORMAL))
    
    def _update_text_area(self, text):
        """Update text area with the given text."""
        self.text_area.insert(tk.END, text)
        self.text_area.see(tk.END)  # Scroll to the end
    
    def _update_status(self, status):
        """Update status bar with the given status."""
        self.status_var.set(status)
    
    def clear_display(self):
        """Clear the text area."""
        self.text_area.delete(1.0, tk.END)
        self.status_var.set("Display cleared")
    
    def save_to_file(self):
        """Save the content of the text area to a file."""
        content = self.text_area.get(1.0, tk.END)
        if not content.strip():
            messagebox.showinfo("Info", "Nothing to save. Please refresh DNS cache first.")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Save DNS Cache"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                self.status_var.set(f"Saved to {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
                self.status_var.set("Error saving file")
    
    # DNS cache display functions from the original script
    def display_dns_cache_windows(self):
        """
        Executes 'ipconfig /displaydns' on Windows and returns its output.
        Handles potential errors if the command is not found or fails.
        """
        try:
            # Run the command and capture the output
            result = subprocess.run(['ipconfig', '/displaydns'], capture_output=True, text=True, check=True, 
                                   encoding='cp850', errors='ignore')  # Common Windows console encoding
            
            # Print the standard output from the command
            print("--- DNS Resolver Cache ---")
            print(result.stdout)
            print("--- End of Cache ---")
            
        except FileNotFoundError:
            # Handle the case where 'ipconfig' command is not found (unlikely on Windows)
            print("Error: 'ipconfig' command not found. Make sure you are running this on a Windows system.")
        except subprocess.CalledProcessError as e:
            # Handle errors during command execution
            print(f"Error executing command: {e}")
            # Sometimes error messages are in stderr
            if e.stderr:
                print(f"Error details: {e.stderr}")
        except Exception as e:
            # Catch any other unexpected errors
            print(f"An unexpected error occurred: {e}")

    def display_dns_cache_macos(self):
        """
        Executes 'dscacheutil -q host' on macOS and returns its output.
        """
        try:
            # Command to query the host cache on macOS
            result = subprocess.run(['dscacheutil', '-q', 'host'], capture_output=True, text=True, check=True)
            print("--- DNS Resolver Cache (macOS) ---")
            print(result.stdout)
            print("--- End of Cache ---")
        except FileNotFoundError:
            print("Error: 'dscacheutil' command not found. Make sure you are running this on macOS.")
        except subprocess.CalledProcessError as e:
            print(f"Error executing command: {e}")
            if e.stderr:
                print(f"Error details: {e.stderr}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

    def display_dns_cache_linux(self):
        """
        Attempts to display DNS cache info on Linux using systemd-resolve or nscd.
        Note: Linux DNS caching is less standardized than Windows/macOS.
        """
        print("--- DNS Resolver Cache (Linux) ---")
        # Try systemd-resolved (common on modern systems)
        try:
            print("\nAttempting with systemd-resolve:")
            # Check statistics which often includes cache info or size
            result_stats = subprocess.run(['systemd-resolve', '--statistics'], capture_output=True, text=True, check=True)
            print(result_stats.stdout)
            print("Note: Full cache dump might require specific permissions or is very verbose.")
            print("Displayed statistics instead.")
            
        except FileNotFoundError:
            print("systemd-resolve not found.")
        except subprocess.CalledProcessError as e:
            print(f"Error with systemd-resolve: {e}")
            if e.stderr:
                print(f"Error details: {e.stderr}")
        except Exception as e:
            print(f"An unexpected error occurred with systemd-resolve: {e}")
        
        # Try nscd (older systems or specific configurations)
        try:
            print("\nAttempting with nscd:")
            # Query the hosts database statistics from nscd
            result_nscd = subprocess.run(['nscd', '-g'], capture_output=True, text=True, check=True)
            print(result_nscd.stdout)
        except FileNotFoundError:
            print("nscd command not found or nscd service not running.")
        except subprocess.CalledProcessError as e:
            print(f"Error with nscd: {e}")
            if e.stderr:
                print(f"Error details: {e.stderr}")
        except Exception as e:
            print(f"An unexpected error occurred with nscd: {e}")
        
        print("\nNote: Linux DNS caching varies. Check your specific distribution and configuration (e.g., systemd-resolved, dnsmasq, nscd).")
        print("--- End of Cache (Linux) ---")


# --- Main execution ---
if __name__ == "__main__":
    root = tk.Tk()
    app = DNSCacheViewerApp(root)
    root.mainloop()
