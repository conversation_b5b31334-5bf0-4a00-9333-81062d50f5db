#SingleInstance Force
#Requires AutoHotkey v2.0

; Initialize variables
activeTimer := ""  ; Keeps track of which timer is currently running
activeInterval := 0  ; Keeps track of current interval

; Function to stop all timers
StopAllTimers() {
    SetTimer SpamQ, 0
    activeTimer := ""
    activeInterval := 0
    ToolTip("Q spam stopped")
    SetTimer () => ToolTip(), -2000  ; Toolt<PERSON> stays for 2 seconds
}

; Function to start a timer
StartTimer(key, interval) {
    global activeTimer, activeInterval
    
    ; If this timer is already running, stop it
    if (activeTimer = key) {
        StopAllTimers()
        return
    }
    
    ; If a different timer is running, stop it first
    if (activeTimer != "") {
        StopAllTimers()
    }
    
    ; Start the new timer
    activeTimer := key
    activeInterval := interval
    SetTimer SpamQ, interval
    ToolTip("Q spam started - " interval/1000 " second interval")
    SetTimer () => ToolTip(), -2000  ; Tooltip stays for 2 seconds
}

; Timer function
SpamQ() {
    Send "{q}"
}

; Hotkeys with Ctrl modifier
^a::{
    if (activeTimer = "a") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("a", 1000)  ; 1 second interval
    }
}

^s::{
    if (activeTimer = "s") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("s", 2500)  ; 2.5 second interval
    }
}

^d::{
    if (activeTimer = "d") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("d", 5000)  ; 5 second interval
    }
}

; Ctrl + F to press Q every 100ms
^f::{
    if (activeTimer = "f") {
        StopAllTimers()  ; If the same timer is running, stop it
    } else {
        StartTimer("f", 100)  ; 100ms interval
    }
}


; Ctrl + Y to stop any active Q spamming
^y::{
    StopAllTimers()
}

; Escape key to exit the script
$Esc::{
    ExitApp()
}

; Block all other keys from triggering the script
#HotIf
SetKeyDelay(0, 0)  ; Ensure no key delay to prevent visibility of key presses
