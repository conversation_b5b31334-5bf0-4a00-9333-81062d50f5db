/////////////////////////
// ALPHA - TESTERS - T2// 
/////////////////////////


// Your script must have ( Dynamic FINISHING ONLY ) with this values .
         
         //minimum power  210 
         //maximum power  216 
         //driben power   230 
         //sprint release 2000 
         //if you don't want driven shots at all go with 7000 sprint release . I don't think you can dribble for 7 seconds without sprinting then shot lol.
         
         
// your script must have (Timed Finesse Shots ) with these values  .

   // Timed finishing time : 230
   // wait for second shot : 150 
   // tick ping on and add your ping you see regulary in rivals
   
//////////////////
// 1st  step - .// 
//////////////////
   
               //search for (after_sprint_timer > 225)  without () make that to >>>>>  after_sprint_timer > 0
 
//////////////////
// 2nd  step - .// 
//////////////////

              // search for inside_box_aim() function ... and replace it with the one below 


function INSIDE_BOX_AIM() { 

    if(get_val(PS4_LX) >= 12) set_val(MOVE_X,45);
    else if(get_val(PS4_LX) <= -12) set_val(MOVE_X,-45);

    if(get_val(PS4_LY) >= 12) set_val(MOVE_Y,90);
    else if(get_val( PS4_LY) <= -12) set_val(MOVE_Y,-90);

   
}

//////////////////
// 3rd  step - .// 
//////////////////

// search for (( combo Timed_Finesse_Finish )) without (()) .

// inside that combo between {} do the following : 

// - Delete CORNER_FIX_MOVE() .
// - Replace ANY CORNER (); to >>>>>  INSIDE_BOX_AIM();



//////////////////
// 4th  step - .// 
//////////////////
// search for ((combo Dynamic_Shoot )) without (()) .
// Remove any line inside it that containt (set_val(PS4_L3,100);)


// Awesome now test the script and let me know how was your experiment. Thanks for your time . <3








////////////////////////////////////////////////////////////////////////////////////////////