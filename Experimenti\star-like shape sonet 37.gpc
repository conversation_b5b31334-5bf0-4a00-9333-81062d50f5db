// Constants
define MAX_INPUT = 100;
define DEADZONE = 1;
define PI = 3.14159;

// Variables for stick values
int x, y;
int scaled_x, scaled_y;
int mag_sq, mag;
int extension;
int angle_rad;  

// Variables for deadzone application
int sign, abs_val, output;

// Variables for integer square root
int iSqrtValue;
int iSqrtRes;
int iSqrtBit;
int iSqrtTemp;

// Variables for octagon scaling
int abs_x, abs_y;
int octant;

////////////////////////////////////////////////////////////////////////////////
// Function: intSqrt(value)
//   - Computes an integer approximation of sqrt(value)
////////////////////////////////////////////////////////////////////////////////
function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes   = 0;
    iSqrtBit   = 1 << 14; // 2^14 = 16384 (enough for up to ~20000)

    // Shift down until bit <= value
    while(iSqrtBit > iSqrtValue) {
        iSqrtBit = iSqrtBit >> 2;
    }

    while(iSqrtBit != 0) {
        iSqrtTemp = iSqrtRes + iSqrtBit;
        if(iSqrtValue >= iSqrtTemp) {
            iSqrtValue = iSqrtValue - iSqrtTemp;
            iSqrtRes   = iSqrtRes + (iSqrtBit << 1);
        }
        iSqrtRes = iSqrtRes >> 1;
        iSqrtBit = iSqrtBit >> 2;
    }
    return iSqrtRes;
}

function apply_deadzone(int val) {
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);
    
    if(abs_val <= DEADZONE)
        return 0;
    
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    if(output > MAX_INPUT)
        output = MAX_INPUT;
        
    return sign * output;
}

////////////////////////////////////////////////////////////////////////////////
// Function: get_octagon_scale(x, y)
//   - Returns the scaling factor for a regular octagon based on direction
//   - Takes the x and y components of the vector instead of an angle
////////////////////////////////////////////////////////////////////////////////
function get_octagon_scale(int x, int y) {
    // Get absolute values of x and y
    abs_x = abs(x);
    abs_y = abs(y);
    
    // Special case first: prevent division by zero
    if (abs_y == 0) {
        return 100;
    }
    
    // Main logic for octagonal scaling
    if (abs_x >= abs_y) {
        return 100;  // Max scale on the cardinal directions
    }
    
    // For diagonal directions
    return (100 * abs_x) / abs_y + 41;  // The 41 adjusts the shape
}

////////////////////////////////////////////////////////////////////////////////
// Function: map_to_octagon(x, y)
//   - Maps input coordinates to an octagonal boundary
//   - Returns a pair of scaled coordinates in scaled_x and scaled_y
////////////////////////////////////////////////////////////////////////////////
function map_to_octagon(int x, int y) {
    // Apply deadzone first
    scaled_x = apply_deadzone(x);
    scaled_y = apply_deadzone(y);
    
    // If within deadzone, just return
    mag_sq = scaled_x * scaled_x + scaled_y * scaled_y;
    if(mag_sq == 0)
        return;
        
    // Calculate magnitude
    mag = intSqrt(mag_sq);
    
    // Get octagon scaling factor for this direction
    extension = get_octagon_scale(scaled_x, scaled_y);
    
    // Apply the scaling factor
    scaled_x = (scaled_x * extension) / mag;
    scaled_y = (scaled_y * extension) / mag;
    
    // Ensure we stay within MAX_INPUT bounds
    if(abs(scaled_x) > MAX_INPUT)
        scaled_x = MAX_INPUT * (scaled_x / abs(scaled_x));
    if(abs(scaled_y) > MAX_INPUT)
        scaled_y = MAX_INPUT * (scaled_y / abs(scaled_y));
}

main {
    // Get stick values
    x = get_val(XB1_LX);
    y = get_val(XB1_LY);
    
    // Apply octagonal mapping
    map_to_octagon(x, y);
    
    // Set final values
    set_val(XB1_LX, scaled_x);
    set_val(XB1_LY, scaled_y);
}