// Auto-rotation script for automatic scanning while ADS
// Rotates the right stick when ADS is active and stick is near neutral

// Button and axis definitions
define Ads = XB1_LT;
define axisX = XB1_RX;
define axisY = XB1_RY;
define aimStick = POLAR_RS;

// Configuration constants
define Radius = 20;     // Base radius for the rotation
define Speed = 2;       // Rotation speed in degrees per frame
define Release = 20;    // Threshold for stick movement detection

// Global variables
int Angle;              // Tracks the current rotation angle

main {
    // Check if ADS is active
    if(get_ival(Ads)) {
        // Check if right stick is near neutral position
        if(abs(get_ival(axisX)) < Release && abs(get_ival(axisY)) < Release) {
            // Update rotation angle and apply to right stick
            set_polar(aimStick, Angle = (Angle + Speed) % 360, Radius * 327);
        }
    }
}
