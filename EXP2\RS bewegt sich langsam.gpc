int total_time = 1000; // Total Time in MS you want the swipe up to be
int block_time = 500; // Total time in MS you want to block LT
int amount,run;
int step
main    {
    if(event_press(XB1_LT)){ 
        run = TRUE;    
    }
    if(run == TRUE){        
        if(step < (total_time / get_rtime())){
            set_val(XB1_RY,((step * (-1000 / (total_time / get_rtime()))) / 10))
        }        
        else{
            set_val(XB1_RY,-100)
            run = FALSE;
            step = 0;
        }
        step++
        block(XB1_LT,block_time)
    }
}