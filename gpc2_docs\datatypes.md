# Data Types

Introduction
For using the functions, better understanding of the examples, especially for using the Persistent Memory and Interactive Configuration you have to know the different Data Types available, their Value Range and their Storage Size (1 byte == 8bits).

You should choose the Type with the smallest Size by the value Range you will need for your variable. This will save script size and Persistent Memory space allowing you to put more stuff into a single script.
When your variable is of a different data type than the function requires or returns you can use type casting. Example:
The get_val() functions returns a value using fix32, the variable Value used for comparing is int8. To be able to compare them you can cast Value to fix32 in the if statement.

int8 Value=100;
if (get_val(BUTTON_1) == (fix32)Value) { } // (fix32) , (int) , ...
Data Types
Type	Range	Size	Examples / Notes
bool1)	FALSE or TRUE
(0 to 255)	1 byte	
bool b=TRUE; if (b) { //TRUE }
char2)	-128 to 127	1 byte	
char c='A'; if (c=='A') {}
int3)	-32768 to 32767	2 bytes	
int i=-400; if (i==-400) {}
uint8	0 to 255	1 byte	
uint8 u=190; if (u==190) {}
uint16	0 to 65535	2 bytes	
uint16 u=40123; if (u==40123) {}
uint32	0 to 4294967295	4 bytes	
uint32 u=312267; if (u==312267) {}
int8	-128 to 127	1 byte	
int8 i=-100; if (i==-100) {}
int16	-32768 to 32767	2 bytes	
int16 i=-400; if (i==-400) {}
int32	-2147483648 to 2147483647	4 bytes	
int32 i=40199; if (i==40199) {}
fix32	-32768.000000 to 32767.999985	4 bytes	
fix32 f=1400.0; if (f==1400.0) {}
You can use f for .0
1400f is the same as 1400.0
! but you can't use 0f for 0.0 !

fix32 4)	-127.99 to 127.99	2 bytes	
fix32 f=120.0; if (f==120.0) {}
Useful to save PMEM space 5) Example

Other available aliases
unsigned char	0 to 255	1 byte	alias of uint8
unsigned int	0 to 65535	2 bytes	alias of uint16
long	-2147483648 to 2147483647	4 bytes	alias of int32
long int	-2147483648 to 2147483647	4 bytes	alias of int32
unsigned long	0 to 4294967295	4 bytes	alias of uint32
unsigned long int	0 to 4294967295	4 bytes	alias of uint32
accum	-32768.000000 to 32767.999985	4 bytes	alias of fix32
1) bool is an alias of uint8, FALSE matches value 0, TRUE matches every other value
2) char is an alias of int8
3) int in GPC2 defaults to int16
4) fix32 : 2bytes Example
5) Persistent Memory Space