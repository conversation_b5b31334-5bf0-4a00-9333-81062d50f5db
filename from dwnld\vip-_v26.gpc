





																/*
																
																░█▀▀▄ ─█▀▀█ ░█▀▀█ ░█─▄▀ 　 ─█▀▀█ ░█▄─░█ ░█▀▀█ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█▀▀█ 　 █▀█ ─█▀█─ 
																░█─░█ ░█▄▄█ ░█▄▄▀ ░█▀▄─ 　 ░█▄▄█ ░█░█░█ ░█─▄▄ ░█▀▀▀ ░█─── 　 ░█▀▀▀ ░█─── 　 ─▄▀ █▄▄█▄ 
																░█▄▄▀ ░█─░█ ░█─░█ ░█─░█ 　 ░█─░█ ░█──▀█ ░█▄▄█ ░█▄▄▄ ░█▄▄█ 　 ░█─── ░█▄▄█ 　 █▄▄ ───█─
																*/
																
																/*| This Script was made and intended for Dark-Angel vip discord members    .                       | 
																| UNLESS permission is given by the creators (Excalibur)&(<PERSON><PERSON><PERSON><PERSON>) ,                            | 
																| All rights reserved. This material may not be reproduced, displayed,                              | 
																| modified or distributed without the express prior written permission of the                       | 
																| copyright holder. 
																
																// most of the functions utilized in this script are primarily crafted by the legendary FIFA scripter, Excalibur.
																// My role as <PERSON><PERSON>Angel has been focused on continuous development to uphold and extend his remarkable legacy.
																
																/*"Special thanks to all the talented developers who generously shared incredible resources that greatly assisted me in putting this project together:
																- тαуℓσя∂яιƒт21
																- Jblaze122
																- DoGzTheFiGhTeR
																- .Me
																- Swizzy
																- Fadexz
																Your contributions have been invaluable, and I am truly grateful for your support."
																*/




































































int DEM202[0]; init { 	DEM119(); 	combo_run(DEM1); 	combo_run(DEM2); 	combo_run(DEM3); 	combo_run(DEM4); 	combo_run(DEM5); 	combo_run(DEM6); 	combo_run(DEM7); 	combo_run(DEM8); 	combo_run(DEM9); 	combo_run(DEM10); 	combo_run(DEM11); 	combo_run(DEM12); 	combo_run(DEM13); 	combo_run(DEM14); 	combo_run(DEM15); 	combo_run(DEM16); 	combo_run(DEM17); 	combo_run(DEM18); 	combo_run(DEM19); 	combo_run(DEM20); 	combo_run(DEM21); 	combo_run(DEM22); 	combo_run(DEM23); 	combo_run(DEM24); 	combo_run(DEM25); 	combo_run(DEM26); 	combo_run(DEM27); 	combo_run(DEM28); 	combo_run(DEM29); 	combo_run(DEM30); 	combo_run(DEM31); 	combo_run(DEM32); 	combo_run(DEM33); 	combo_run(DEM34); 	combo_run(DEM35); 	combo_run(DEM36); 	combo_run(DEM37); 	combo_run(DEM38); 	combo_run(DEM39); 	combo_run(DEM40); 	combo_run(DEM41); 	combo_run(DEM42); 	combo_run(DEM43); 	combo_run(DEM44); 	combo_run(DEM45); 	combo_run(DEM46); 	combo_run(DEM47); 	combo_run(DEM48); 	combo_run(DEM49); 	combo_run(DEM50); 	combo_run(DEM51); 	combo_run(DEM52); 	combo_run(DEM53); 	combo_run(DEM54); 	combo_run(DEM55); 	combo_run(DEM56); 	combo_run(DEM57); 	combo_run(DEM58); 	combo_run(DEM59); 	combo_run(DEM60); 	combo_run(DEM61); 	combo_run(DEM62); 	combo_run(DEM63); 	combo_run(DEM64); 	combo_run(DEM65); 	combo_run(DEM66); 	combo_run(DEM67); 	combo_run(DEM68); 	combo_run(DEM69); 	combo_run(DEM70); 	combo_stop(DEM1); 	combo_stop(DEM2); 	combo_stop(DEM3); 	combo_stop(DEM4); 	combo_stop(DEM5); 	combo_stop(DEM6); 	combo_stop(DEM7); 	combo_stop(DEM8); 	combo_stop(DEM9); 	combo_stop(DEM10); 	combo_stop(DEM11); 	combo_stop(DEM12); 	combo_stop(DEM13); 	combo_stop(DEM14); 	combo_stop(DEM15); 	combo_stop(DEM16); 	combo_stop(DEM17); 	combo_stop(DEM18); 	combo_stop(DEM19); 	combo_stop(DEM20); 	combo_stop(DEM21); 	combo_stop(DEM22); 	combo_stop(DEM23); 	combo_stop(DEM24); 	combo_stop(DEM25); 	combo_stop(DEM26); 	combo_stop(DEM27); 	combo_stop(DEM28); 	combo_stop(DEM29); 	combo_stop(DEM30); 	combo_stop(DEM31); 	combo_stop(DEM32); 	combo_stop(DEM33); 	combo_stop(DEM34); 	combo_stop(DEM35); 	combo_stop(DEM36); 	combo_stop(DEM37); 	combo_stop(DEM38); 	combo_stop(DEM39); 	combo_stop(DEM40); 	combo_stop(DEM41); 	combo_stop(DEM42); 	combo_stop(DEM43); 	combo_stop(DEM44); 	combo_stop(DEM45); 	combo_stop(DEM46); 	combo_stop(DEM47); 	combo_stop(DEM48); 	combo_stop(DEM49); 	combo_stop(DEM50); 	combo_stop(DEM51); 	combo_stop(DEM52); 	combo_stop(DEM53); 	combo_stop(DEM54); 	combo_stop(DEM55); 	combo_stop(DEM56); 	combo_stop(DEM57); 	combo_stop(DEM58); 	combo_stop(DEM59); 	combo_stop(DEM60); 	combo_stop(DEM61); 	combo_stop(DEM62); 	combo_stop(DEM63); 	combo_stop(DEM64); 	combo_stop(DEM65); 	combo_stop(DEM66); 	combo_stop(DEM67); 	combo_stop(DEM68); 	combo_stop(DEM69); 	combo_stop(DEM70); 	combo_run(DEM110); } int DEM275 ; int DEM276; int DEM277; int DEM278; int DEM279; define DEM280 = 0; define DEM281 = 1; define DEM282 = 2; define DEM283 = 3; define DEM284 = 4; define DEM285 = 5; define DEM286 = 6; define DEM287 = 7; define DEM288 = 8; define DEM289 = 9; define DEM290 = 10; define DEM291 = 11; define DEM292 = 12; define DEM293 = 13; define DEM294 = 14; define DEM295 = 15; define DEM296 = 16; define DEM297 = 17; define DEM298 = 18; define DEM299 = 19; define DEM300 = 20; define DEM301 = 21; define DEM302 = 22; define DEM23 = 23; define DEM304 = 24; define DEM305 = 25; define DEM306 = 26; define DEM307 = 27; define DEM308 = 28; define DEM309 = 29; define DEM310 = 30; define DEM311 = 31; define DEM312 = 32; define DEM313 = 33; define DEM314 = 34; define DEM315 = 35; define DEM316 = 36; define DEM317 = 37; define DEM318 = 38; define DEM319 = 39; define DEM320 = 40; define DEM321 = 41; define DEM322 = 42; define DEM323 = 43; define DEM324 = 44; define DEM325 = 45; define DEM326 = 46; define DEM327 = 47; define DEM328 = 48; define DEM329 = 49; define DEM330 = 50; define DEM331 = 51; define DEM332 = 52; define DEM333 = 53; define DEM334 = 54; define DEM335 = 55; define DEM336 = 56; define DEM337 = 57; define DEM338 = 58; define DEM339 = 59; define DEM340 = 60; define DEM341 = 61; define DEM342 = 62; define DEM343 = 63; define DEM344 = 64; define DEM345 = 65; define DEM346 = 66; define DEM347 = 67; define DEM348 = 68; define DEM349 = 69; define DEM350 = 70; define DEM351 = 0; function DEM113(DEM114) { 	if (DEM114 == 0) vm_tctrl(-0); 	else if (DEM114 == 1) vm_tctrl(-1); 	else if (DEM114 == 2) vm_tctrl(-2); 	else if (DEM114 == 3) vm_tctrl(-3); 	else if (DEM114 == 4) vm_tctrl(-4); 	else if (DEM114 == 5) vm_tctrl(-5); 	else if (DEM114 == 6) vm_tctrl(-6); 	else if (DEM114 == 7) vm_tctrl(-7); 	else if (DEM114 == 8) vm_tctrl(-8); 	else if (DEM114 == 9) vm_tctrl(-9); } int DEM352, DEM353; int DEM354, DEM355; int DEM356 = FALSE, DEM357; int DEM358 = TRUE; int DEM359; const string DEM833[] = { 	"Off",  "On" } ; int DEM360; int DEM361; int DEM362; int DEM363; int DEM364; int DEM365; int DEM366; int DEM367; int DEM368; int DEM369; int DEM370; int DEM371; int DEM372; int DEM373; int DEM374; int DEM375; int DEM376; int DEM377; int DEM378; int DEM379; int DEM114; int DEM381; int DEM382 ; int DEM383 ; int DEM384 ; define DEM385 = 24; int DEM386; int DEM387; int DEM388; int DEM389; int DEM390; int DEM391; int DEM392; int DEM393; int DEM394; int DEM395 ; int DEM396 ; int DEM397 ; int DEM398 ; int DEM399 ; int DEM400 ; int DEM401 ; int DEM402 ; int DEM403 ; int DEM404 ; int DEM405 ; int DEM406; int DEM407; int DEM408; int DEM409; int DEM410; int DEM411; int DEM412; int DEM413; int DEM414; int DEM415; int DEM416; int DEM417; int DEM418; int DEM419; int DEM420; int DEM421; int DEM422; int DEM423; int DEM424; int DEM425; int DEM426; int DEM427; int DEM428; int DEM429; int DEM430; int DEM431; int DEM432; int DEM433; int DEM434; int DEM435; int DEM436; int DEM437; int DEM438; int DEM439 ; int DEM440 ; int DEM441 ; int DEM442; int DEM443 ; int DEM444 ; int DEM445 ; int DEM446; int DEM447 ; int DEM448 ; int DEM449 ; int DEM450; int DEM451 ; int DEM452 ; int DEM453 ; int DEM454; int DEM455; int DEM456; int DEM457; int DEM458; int DEM459; const int16 DEM839[][] = { { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 9, 1, 10, 2 	} 	,       { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 2 	} 	,    { 		0, 70, 1, 10, 3 	} 	,    { 		0, 70, 1, 10, 4 	} 	,    { 		0, 70, 1, 10, 5 	} 	,    { 		0, 22, 1, 10, 13 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,   { 		0, 70, 1, 10, 16 	} 	,    { 		1, 25, 1, 10, 6 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		1, 25, 1, 10, 8 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		0, 1, 1, 10, 20 	} 	,     { 		0, 25, 1, 10, 7 	} 	,     { 		0, 1, 1, 10, 21 	} 	,     { 		0, 1, 1, 10, 19 	} 	,     { 		1, 25, 1, 10, 9 	} 	,     { 		0, 1, 1, 10, 28 	} 	,     { 		0, 1, 1, 10, 29 	} 	,     { 		1, 800, 1, 10, 0 	} 	,    { 		1, 800, 1, 10, 0 	} 	,    { 		0, 22, 1, 10, 13 	} 	,    { 		0, 1, 1, 10, 33 	} 	,     { 		-100, 300, 1, 10, 1 	} 	,  { 		-150, 150, 10, 10, 0 	} 	, { 		-150, 150, 10, 10, 0 	} 	, { 		0, 1, 1, 10, 37 	} 	,      { 		-150, 150, 10, 10, 0 	} 	, { 		0, 22, 1, 10, 49 	} 	,     { 		0, 22, 1, 10, 50 	} 	,     { 		0, 22, 1, 10, 51 	} 	,     { 		0, 22, 1, 10, 52 	} 	,     { 		0, 1, 1, 10, 53 	} 	,      { 		0, 1, 1, 10, 54 	} 	,      { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		60, 500, 5, 10, 0 	} 	,    { 		60, 500, 5, 10, 0 	} 	,    { 		0, 1, 1, 10, 1 	} 	,       { 		0, 1, 1, 10, 1 	} 	,       { 		50, 250, 5, 10, 0 	} 	,    { 		100, 850, 5, 10, 0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,       { 		0,      1,      1,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		80,    500,      5,     10,     1   	} 	,  { 		0,      1,      1,     10,     1   	} 	,  { 		3,60,1,10,0 	} 	,   { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} 	,      { 		0, 1, 1, 10, 1 	} } ; const int16 DEM579[][] = { { 		0, 7, 1 	} 	,   	    { 		8,   16, 1 	} 	,   	    { 		17,  21, 1 	} 	,   	    { 		68,68,1 	} 	,       	    { 		69,70,1 	} 	,       	    { 		22, 26, 1 	} 	,   	    { 		27, 29, 1 	} 	,   	    { 		30, 32, 1 	} 	,   	    { 		33, 35, 1 	} 	,   	    { 		36, 38, 1 	} 	,   	    { 		39, 39, 1 	} 	,   	    { 		40, 40, 1 	} 	,   	    { 		41, 42, 1 	} 	,   	    { 		43, 43, 1 	} 	,   	    { 		0,  0, 0 	} 	,   	    { 		54, 55, 1 	} 	,   	    { 		44, 47, 1 	} 	,   { 		48, 51, 1 	} 	,   { 		52, 53, 1 	} 	,   { 		0, 0, 0 	} 	,    { 		0, 0, 0 	} 	,    { 		67, 67, 1 	} 	,    { 		56, 59, 1 	} 	,   { 		60, 63, 1 	} 	,   { 		64, 66, 1 	} } ; const uint8 DEM811[] = { 	2,   	    3,   	    3,   	    1,   	    1,   	    6,   	    70,  	    70,  	    70,  	    70,  	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    1,   	    10,   	    1,   	    1,  	1,  	1   } ; const string DEM589[] = { 	"Button Layout",  "RS New Way",  "RS Old Shcool",  "RS 4* Meta",  "RS 5* Meta",  "Instant Skills",  "Additin Skill 1",  "Additin Skill 2",  "Additin Skill 3",  "Additin Skill 4",  "Free Kick",  "Corner Goal",  "Player Lock",  "Universal EA Ping",  "Auto Runs",  "Pro Finisher",  "Fine-Tuning",  "1-Btn Finish",  "Aiming",  "Jockey_Support",  "VM_Speed",  "E-Movements",  "Trough Pass","Ground Passes","Lob Passes", "" } ; const string DEM588[] = { 	"Shot Button","Pass Button","Player Run","Finesse Shot","Protect Ball","Sprint Button","Cross Button","Through Ball",  "Skill UP",  "Skill Up Left",  "Skill Up Right",  "Skill Left",  "Skill Right",  "Sk Down Left",  "Sk Down Right",  "Skill Down",  "Modifier Button",  "RS UP",  "RS RIGHT",  "RS DOWN",  "RS LEFT",  "Modifier Button",  "LB/L1",  "RB/R1",  "Y/TRIANGL-X/SQUAR",  "A/SROSS-B/CIRCLE",  "RS/R3",  "AdSkill 1 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 3 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 2 Button",  "OneTap/DoubleTap",  "Exit Direction",  "AdSkill 4 Button",  "OneTap/DoubleTap",  "Exit Direction",  "FreeKick Power",  "Corner Power",  "Player Lock Btn",  "Choose Striker",  "EA Ping",  "+/- Trivla",  "+/- normal finish ",  "AI Def Support",  "+/- Finesse",  "Timed Finesse",  "Timed Trivela",  "Timed Normal",  "Timed PS",  "Precision",  "Assisted",  "Trivela Roll",  "Timed",  "Min Value","Max Value","Use Max Value","Add Double Tap","Minimum Value","Maximum  Value","ALways Driven","Max Power Restric","Min Value","Max Value","Use Max Value", "EM_FACTOR" ,"BALL_ROLL","BALL_ROLL","ROLL_SCOOP","" } ; const string DEM815 [] = { 	"Classic","Alternative","Custom", ""  } ; const string DEM914 [] = { 	"R1/RB","R2/RT","R3/RS","L1/LB","L2/LT","L3/LS","Y/TRIANGLE","B/CIRCLE","A/CROSS","X/SQUARE", ""  } ; const string DEM831[] = { 	"0",  "-1",  "-2",  "-3",  "-4",  "-5",  "-6", "-7",  "-8",  "-9", "" } ; const string DEM817[] = { 	"Disabled",  "Always ON",  "DoubleTap Btn",  "Modifier Button",  "" } ; const string DEM884[] = { 	"Right",  "Left",  "" } ; const string DEM882[] = { 	"One Tap",  "Double Tap",  "" } ; const string DEM821[] = { 	"Disabled",  "Modifier RS/R3",  "Modifier LS/L3",  "Modifier XB1 PR1",  "Modifier XB1 PR2",  "Modifier XB1 PL1",  "Modifier XB1 PL2",  "" } ; const string DEM823[] = { 	"Disable",  	"LA_CRQ_Pass",  	"LA_CRQ_Shoot",  	"Roll Sombrero PS",  	"Step Over Feint",  	"Rev Step Over",  	"Step Over Boost",  	"Hocus Pocus",  	"Triple Elastico",  	"Elastico",  	"Rev Elastico",  	"ForwFlairNutmeg",  	"DragBack Univ.",  	"Drag to Drag",  	"Cruyff Turn",  	"La Croqueta",  	"Roulette",  	"Flair Roulette",  	"3 touch cancel",  	"3 touch",  	"WAKA WAKA",  	"Body Feint",  	"Feint & Turn",  	"Turn Back",  	"BackFlairNutmeg",  	"Four Touch Turn",  	"Skilled Bridge",  	"Canc 4 Touch",  	"Roll Sombrero",  	"Ball Roll",  	"Ball Roll Drag",  	"Roll Drag Cancel",  	"Ball Roll Fake",  	"Roll to Scoop",  	"Scoop Turn Fake",  	"Roll Step Over",  	"BallRoll Cut 180",  	"Heel2Heel Flick",  	"Lat Heel2Heel",  	"Drag to Heel",  	"Diag Heel Chop",  	"Heel ROLL",  	"Berb/Mcgeady",  	"1 Foot Spin",  	"Canc Berba Spin",  	"C Berba Spin",  	"SCOOP TO RANDOM",  	"Fake Berba Out",  	"Fake Berba Drag",  	"Fake Shot",  	"Fake Pass",  	"Fake Drag Back",  	"Fake Shot Canc.",  	"Fake Rabona",  	"Jog Openup Fake",  	"Cancel Shoot",  	"Ronaldo Chop",  	"Okocha Flick",  	"Rainbow",  	"Flair Rainbow",  	"Adv. Rainbow",  	"Juggle Rainbow",  	"Adv Elastico Chop.",  	"Sombrero Flick",  	"TR_BALL_HOP","TR_Flick_Up","TR_Quick","TR_L_R_Flks","TR_180","TR_CLF_RNbW","FL_Nutmg_L_R" } ; const string DEM858[] = { 	"OFF",  "PS4_PS",  "PS4_SHARE",  "PS4_OPTIONS",  "PS4_R1",  "PS4_R2",  "PS4_R3",  "PS4_L1",  "PS4_L2",  "PS4_L3",  "PS4_UP",  "PS4_DOWN",  "PS4_LEFT",  "PS4_RIGHT",  "PS4_TRIANGLE",  "PS4_CIRCLE",  "PS4_CROSS",  "PS4_SQUARE",  "XB1_PR1",  "XB1_PR2",  "XB1_PL1",  "XB1_PL2",  "PS4_TOUCH",  "" } ; int DEM460 = -1; int DEM461 = -1; int DEM462 = -1; int DEM463 = -1; int DEM464 = -1; int DEM465; int DEM466; int DEM467; int DEM468; int DEM469; const uint8 DEM1365[] = { 	4,4,4, 4,4,4, 4,4,4,4,4 } ; const uint8 DEM1366[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29 } ; const uint8 DEM1367[] = { 	41, 41, 41, 41, 41, 41, 41, 41, 41, 41,29  } ; const uint8 DEM1368[] = { 	41,42,70,41,70,41,43,70,41,41,29  } ; const uint8 DEM1369[] = { 	42,41,41,43,70,41,70,41,70,41 ,29  } ; const uint8 DEM1370[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DEM1371[] = { 	29, 44, 29, 44, 29, 44, 29, 44,29,44,29 } ; const uint8 DEM1372[] = { 	27, 21, 44, 27, 21, 44, 21, 44, 27, 21,27 } ; const uint8 DEM1373[] = { 	4,4,4, 4,4,4, 4,4,4,4,4,4 } ; const uint8 DEM1374[] = { 	9, 42, 41, 62, 34, 70, 9, 42, 41, 62, 33,29 } ; const uint8 DEM1375[] = { 	7, 10, 70, 41, 42, 62, 7, 10, 70, 41, 33,29  } ; const uint8 DEM1376[] = { 	41, 9, 42, 20, 62, 41, 9, 42, 20, 62, 33,29  } ; const uint8 DEM1377[] = { 	41, 7, 42, 20, 62, 41, 7, 42, 20, 62, 33,29  } ; const uint8 DEM1378[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DEM1379[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47 } ; const uint8 DEM1380[] = { 	47, 47, 47, 47, 47, 47, 47, 47, 47, 47  } ; function DEM115(DEM116) { 	if (DEM116 == 9) { 		DEM470 = -1; 			} 	else if (DEM116 <= 0) { 		DEM470 = 1; 			} 	else if (DEM116 > 9 ) { 		DEM116 = 0; 			} 	DEM116 += DEM470; 	return DEM116; 	} function DEM117() { 	vm_tctrl(0); 	if(DEM29 && DEM363){ 		if(DEM555 < 1000){ 			DEM473 = 10; 			DEM499   = 10; 			DEM497  = 10; 					} 			} 	if(DEM476 && DEM364){ 		DEM474 = FALSE; 		if(DEM555 < 1000){ 			DEM473 = 11; 			DEM499   = 11; 			DEM497  = 11; 					} 			} 	if(DEM474 && DEM364){ 		DEM476 = FALSE; 		if(DEM555 < 1000){ 			DEM473 = 10; 			DEM499   = 10; 			DEM497  = 10; 					} 			} 			       if(DEM555 >= 1000){     DEM478 = DEM115(DEM478);     DEM496 = DEM115(DEM496);     DEM497 = DEM115(DEM497);     DEM473 = DEM115(DEM473);     DEM499 = DEM115(DEM499);     } 	if(DEM363){ 		if(DEM502 == DEM593){ 			DEM479 = !DEM479; 			if(DEM1365[DEM478]) DEM227(DEM1365[DEM478]); 					} 		if(DEM502 == DEM232 (DEM593 + 4)){ 			DEM479 = FALSE; 			if(DEM1372[DEM496]) DEM227(DEM1372[DEM496]); 					} 		if(DEM502 == DEM232 (DEM593 + 1) ){ 			DEM479 = TRUE; 			if(DEM1367[DEM473]) DEM227(DEM1367[DEM473]); 					} 		if(DEM502 == DEM232 (DEM593 - 1) ){ 			DEM479 = FALSE; 			if(DEM1366[DEM473]) DEM227(DEM1366[DEM473]); 					} 		if(DEM502 == DEM232 (DEM593 + 2) ){ 			DEM479 = TRUE; 			if(DEM1369[DEM499]) DEM227(DEM1369[DEM499]); 					} 		if(DEM502 == DEM232 (DEM593 - 2) ){ 			DEM479 = FALSE; 			if(DEM1368[DEM499]) DEM227(DEM1368[DEM499]); 					} 		if(DEM502 == DEM232 (DEM593 + 3) ){ 			DEM479 = TRUE; 			if(DEM1370[DEM497]) DEM227(DEM1370[DEM497]); 					} 		if(DEM502 == DEM232 (DEM593 - 3) ){ 			DEM479 = FALSE; 			if(DEM1371[DEM497]) DEM227(DEM1371[DEM497]); 					} 			} 	else if(DEM364){ 		if(DEM502 == DEM593){ 			DEM479 = !DEM479; 			if(DEM1373[DEM478]) DEM227(DEM1373[DEM478]); 					} 		if(DEM502 == DEM232 (DEM593 + 4)){ 			DEM479 = FALSE; 			if(DEM1380[DEM496]) DEM227(DEM1380[DEM496]); 					} 		if(DEM502 == DEM232 (DEM593 + 1) ){ 			DEM479 = TRUE; 			if(DEM1375[DEM473]) DEM227(DEM1375[DEM473]); 					} 		if(DEM502 == DEM232 (DEM593 - 1) ){ 			DEM479 = FALSE; 			if(DEM1374[DEM473]) DEM227(DEM1374[DEM473]); 					} 		if(DEM502 == DEM232 (DEM593 + 2) ){ 			DEM479 = TRUE; 			if(DEM1377[DEM499]) DEM227(DEM1377[DEM499]); 					} 		if(DEM502 == DEM232 (DEM593 - 2) ){ 			DEM479 = FALSE; 			if(DEM1376[DEM499]) DEM227(DEM1376[DEM499]); 					} 		if(DEM502 == DEM232 (DEM593 + 3) ){ 			DEM479 = TRUE; 			if(DEM1378[DEM497]) DEM227(DEM1378[DEM497]); 					} 		if(DEM502 == DEM232 (DEM593 - 3) ){ 			DEM479 = FALSE; 			if(DEM1379[DEM497]) DEM227(DEM1379[DEM497]); 					} 			} } int DEM478; int DEM496; int DEM497; int DEM473; int DEM499; function DEM118() { 	if(DEM1140){ 		DEM500 += get_rtime(); 			} 	if(DEM500 >= 3000){ 		DEM500 = 0; 		DEM1140 = FALSE; 			} 	if (!get_ival(XB1_RS) && !get_ival(DEM451) && !get_ival(DEM452) && !get_ival(DEM450) && !get_ival(DEM449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 2000) && !DEM503 && !combo_running(DEM0)) { 			DEM502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DEM503 = TRUE; 			DEM1140 = TRUE; 			DEM500 = 0; 			vm_tctrl(0); 			DEM117(); 					} 		set_val(DEM1121, 0); 		set_val(DEM1122, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 2000) { 		DEM503 = FALSE; 			} 	} function DEM119() { 	DEM172(); 	if (DEM386 == 0 && DEM387 == 0 && DEM388 == 0 && DEM389 == 0 && DEM390 == 0 && DEM391 == 0 && DEM392 == 0 && DEM393 == 0) { 		DEM386 = 4; 		DEM387 = 41; 		DEM388 = 41; 		DEM389 = 42; 		DEM390 = 42; 		DEM391 = 31; 		DEM392 = 31; 		DEM393 = 31; 			} 	DEM953 = get_slot(); 	} int DEM470 = 1; int DEM507; int DEM508; int DEM509 = TRUE; int DEM510[6]; int DEM511; int DEM512; int DEM513; int DEM514; function DEM120(DEM121, DEM122, DEM123) { 	DEM123 = (DEM123 * 14142) / 46340; 	if (DEM122 <= 0) { 		set_polar2(DEM121, (DEM122 = (abs(DEM122) + 360) % 360), min(DEM123, DEM518[DEM122 % 90])); 		return; 			} 	set_polar2(DEM121, inv(DEM122 % 360), min(DEM123, DEM518[DEM122 % 90])); 	} function DEM124(DEM121,DEM126) { 	if (DEM126) return (get_ipolar(DEM121, POLAR_ANGLE)) % 360; 	return isqrt(~(pow(get_ival(42 + DEM121), 2) + pow(get_ival(43 + DEM121), 2))) + 1; 	} const int16 DEM518[] = { 	10000, 10001, 10006, 10014, 10024, 10038, 10055, 10075, 10098, 10124, 10154, 10187, 10223, 10263, 10306, 10353, 10403, 10457, 10514, 10576, 10642, 10711, 10785, 10863, 10946, 11034, 11126, 11223, 11326, 11433, 11547, 11666, 11792, 11923, 12062, 12208, 12361, 12521, 12690, 12867, 13054 , 13250 , 13456, 13673, 13902, 14142, 13902, 13673, 13456, 13250, 13054, 12867, 12690, 12521, 12361, 12208, 12062, 11923, 11792, 11666, 11547, 11433, 11326, 11223, 11126, 11034, 10946, 10863, 10785, 10711, 10642, 10576, 10514, 10457, 10403, 10353, 10306, 10263, 10223, 10187, 10154, 10124, 10098, 10075, 10055, 10038, 10024, 10014, 10006, 10001  } ; int block = FALSE; int DEM521 = 1; combo DEM0{ 	set_polar(POLAR_RS,0,0); 	vm_tctrl(0);wait(100); 	vm_tctrl(0);wait(300); 	} main{ 	if(get_ival(PS4_R3) && get_ipolar(POLAR_RS,POLAR_RADIUS) > 2000 && get_ptime(PS4_R3) > 280){ 		DEM269(POLAR_RS, 2500, 12000); 		}  DEM113(DEM114); 	if(!DEM508){ 		DEM508 = TRUE; 		DEM507 = random(0x2B67, 0x1869F); 		set_pvar(SPVAR_1,DEM508); 		set_pvar(SPVAR_3,DEM507); 		DEM509 = TRUE; 			} 	if(!DEM514){ 		vm_tctrl(0); 		if(event_press(PS4_LEFT)){ 			DEM513 = DEM129(DEM513 + 1 ,0,5)DEM509 = TRUE 		} 		if(event_press(PS4_RIGHT)){ 			DEM513 = DEM129(DEM513 - 1 ,0,5)DEM509 = TRUE 		} 		if(event_press(PS4_UP)){ 			DEM510[DEM513]  = DEM129(DEM510[DEM513] + 1 ,0,9)DEM509 = TRUE 		} 		if(event_press(PS4_DOWN)){ 			DEM510[DEM513]  = DEM129(DEM510[DEM513] - 1 ,0,9)DEM509 = TRUE 		} 		if(event_press(PS4_CROSS)){ 			DEM511 = 0; 			for(DEM512 = 5; 			DEM512 >= 0; 			DEM512--){ 				DEM511 += DEM510[DEM512] * pow(10,DEM512) 			} 			if(DEM511 == DEM127(DEM507)){ 				DEM514 = TRUE; 				set_pvar(SPVAR_2,DEM514)  			} 			DEM509 = TRUE; 					} 			} 	if(DEM509){ 		cls_oled(0)if(!DEM514){ 			DEM133(DEM507,DEM536,10,OLED_FONT_MEDIUM,OLED_WHITE,DEM537)for( DEM512 = 0; 			DEM512 < 6; 			DEM512++){ 				DEM133(DEM510[DEM512],85 - (DEM512 * 10),40,OLED_FONT_MEDIUM,!(DEM512 == DEM513),DEM537) 			} 					} 		DEM509 = FALSE; 			} 	if(DEM514){ 		if (get_ival(DEM447) || get_ival(DEM451) || get_ival(DEM449) || get_ival(DEM450) || DEM356 || combo_running(DEM72) || get_info(CPU_USAGE) > 95 ) { 			vm_tctrl(0); 					} 		else{ 			DEM113(DEM114); 					} 		if(get_ival(DEM452) > 40 || (!get_ival(DEM449) && !get_ival(DEM450))){ 			if(get_ival(DEM447)){ 				vm_tctrl(0); 				if(get_ptime(DEM447) > DEM608){ 					set_val(DEM447,0); 									} 							} 					} 		if(!get_ival(DEM449)){ 			if(get_ival(DEM447)){ 				vm_tctrl(0); 				if(get_ptime(DEM447) > DEM608){ 					set_val(DEM447,0); 									} 							} 					} 		if (DEM356) { 			vm_tctrl(0); 			if(DEM357 < 8050){ 				DEM357 += get_rtime(); 							} 			if (DEM357 >= 8000) { 				cls_oled(OLED_BLACK); 				DEM357 = 0; 				DEM356 = FALSE; 							} 					} 		if (block) { 		if (DEM114 > 7)combo_run(DEM112); 			if (DEM521 < 310) { 				DEM521 += get_rtime(); 							} 			if (DEM521 <= 300 ) { 				DEM169(); 							} 			if (DEM521 > 300 ) { 				block = FALSE; 				DEM521 = 1; 				DEM718 = TRUE; 							} 			if (DEM521 < 0) { 				DEM521 = 1; 							} 			if (DEM521 <= 100) { 				combo_stop(DEM88); 				combo_stop(DEM97); 				combo_stop(DEM89); 				combo_stop(DEM98); 				combo_stop(DEM95); 				combo_stop(DEM96); 				combo_stop(DEM92); 				combo_stop(DEM94); 				combo_stop(DEM91); 				combo_stop(DEM87); 				combo_stop(DEM85); 				combo_stop(DEM90); 				combo_stop(DEM107); 				combo_stop(DEM109); 				combo_stop(DEM100); 				combo_stop(DEM108); 				combo_stop(DEM99); 							} 					} 		if((get_ival(PS4_L2) && event_press(PS4_R2) || event_press(PS4_L2) && get_ival(PS4_R2) )){ 			block = TRUE; 					} 		if(DEM437){ 			DEM438 = FALSE; 					} 		if(DEM438){ 			DEM437 = FALSE; 					} 		if(DEM361){ 			DEM362 = FALSE; 			DEM363 = FALSE; 			DEM364 = FALSE; 					} 		if(DEM362){ 			DEM361 = FALSE; 			DEM363 = FALSE; 			DEM364 = FALSE; 					} 		if(DEM363){ 			DEM361 = FALSE; 			DEM362 = FALSE; 			DEM364 = FALSE; 					} 		if(DEM364){ 			DEM361 = FALSE; 			DEM362 = FALSE; 			DEM363 = FALSE; 					} 		if (get_ival(PS4_L2)) { 			if (get_ival(PS4_LEFT)) { 				set_val(PS4_LEFT, 0); 				DEM1175 = -1 			} 			else if (get_ival(PS4_RIGHT)) { 				set_val(PS4_RIGHT, 0); 				DEM1175 = 1 			} 					} 		if (get_ival(PS4_L2)) { 			set_val(PS4_SHARE, 0); 			if (event_press(PS4_SHARE)) { 				vm_tctrl(0); 				DEM1063 = !DEM1063; 				DEM229(DEM1297); 				DEM203(DEM1063, sizeof(DEM552) - 1, DEM552[0]); 				DEM356 = TRUE; 							} 					} 		if (DEM1063) { 				if(DEM431 == TRUE){ 			if(get_ival(DEM452) && ( (get_ival(DEM449) && combo_running(DEM102)) || get_ipolar(POLAR_RS,POLAR_RADIUS) > 2800)  ){set_val(DEM450,0);combo_run(DEM101);} 					} 					if(combo_running(DEM101)){ 						if(event_press(DEM448) || event_press(DEM447) || event_press(DEM454) || event_press(DEM453) || event_press(DEM452) ){combo_stop(DEM101);} 					} 			if(DEM381){ 				DEM266(); 			} 			if (DEM379) { 				DEM256(); 							} 			if (event_release(DEM452)) { 				DEM555 = 1; 							} 			if (DEM555 < 8000) { 				DEM555 += get_rtime(); 							} 			if (get_ival(PS4_R2)) { 				if (event_press(PS4_OPTIONS)) { 					DEM557 = !DEM557; 					DEM229(DEM557); 									} 				set_val(PS4_OPTIONS, 0); 							} 			if (DEM557) { 				if (DEM557) DEM222(DEM1095); 				if (DEM557) { 					DEM148(); 									} 							} 			else if (!get_ival(DEM452)) { 				DEM222(DEM1098); 				if (get_ival(PS4_L2)) { 					if (event_press(PS4_OPTIONS)) { 						DEM352 = TRUE; 						DEM359 = TRUE; 						DEM358 = FALSE; 						if (!DEM352) { 							DEM358 = TRUE; 													} 											} 					set_val(PS4_OPTIONS, 0); 									} 				if (!DEM358) { 					if (DEM352 || DEM353) { 						vm_tctrl(0); 					} 					if (DEM352) { 						combo_stop(DEM72); 						vm_tctrl(0); 						DEM360= DEM149(DEM360,0  ); 						DEM361 = DEM149(DEM361, 1); 						DEM362  = DEM149(DEM362   ,2  ); 						DEM363  = DEM149(DEM363 , 3); 						DEM364  = DEM149(DEM364 , 4); 						DEM365 = DEM149(DEM365, 5); 						DEM366 = DEM149(DEM366, 6); 						DEM367 = DEM149(DEM367, 7); 						DEM368 = DEM149(DEM368, 8); 						DEM369 = DEM149(DEM369, 9); 						DEM370 = DEM149(DEM370, 10); 						DEM371 = DEM149(DEM371, 11); 						DEM372 = DEM149(DEM372, 12); 						DEM373 = DEM149(DEM373,13); 						DEM374 = DEM149(DEM374, 14); 						DEM375 = DEM149(DEM375, 15); 						DEM376 = DEM149(DEM376, 16); 						DEM377 = DEM149(DEM377, 17); 						DEM378 = DEM149(DEM378, 18); 						DEM379 = DEM149(DEM379, 19); 						DEM114 = DEM149(DEM114, 20); 						DEM381 = DEM149(DEM381, 21); 						DEM382              = DEM149(DEM382              ,22  ); 						DEM383              = DEM149(DEM383              ,23  ); 						DEM384               = DEM149(DEM384               ,24  ); 						if (event_press(PS4_DOWN)) { 							DEM354 = clamp(DEM354 + 1, 0, DEM385); 							DEM359 = TRUE; 													} 						if (event_press(PS4_UP)) { 							DEM354 = clamp(DEM354 - 1, 0, DEM385); 							DEM359 = TRUE; 													} 						if (event_press(PS4_CIRCLE)) { 							DEM352 = FALSE; 							DEM358 = FALSE; 							DEM359 = FALSE; 							vm_tctrl(0); 							combo_run(DEM75); 													} 						if (DEM579[DEM354][2] == 1) { 							if(DEM354 == 0 ){ 								if(DEM360 == 2 ){ 									if (event_press(PS4_CROSS)) { 										DEM355 = DEM579[DEM354][0]; 										DEM352 = FALSE; 										DEM353 = TRUE; 										DEM359 = TRUE; 																			} 																	} 															} 							else{ 								if (event_press(PS4_CROSS)) { 									DEM355 = DEM579[DEM354][0]; 									DEM352 = FALSE; 									DEM353 = TRUE; 									DEM359 = TRUE; 																	} 															} 													} 						DEM169(); 						print(2, 38, OLED_FONT_SMALL, OLED_WHITE, DEM569[0]); 						DEM158(DEM354 + 1, DEM164(DEM354 + 1), 28, 38, OLED_FONT_SMALL); 						print(84, 38, OLED_FONT_SMALL, OLED_WHITE, DEM571[0]); 						DEM158(DEM953, DEM164(DEM953), 112, 38, OLED_FONT_SMALL); 						line_oled(1, 48, 127, 48, 1, 1); 						if(DEM354 == 0 ){ 							if(DEM360 == 2 ){ 								print(2, 52, OLED_FONT_SMALL, 1, DEM573[0]); 															} 							else{ 								print(2, 52, OLED_FONT_SMALL, 1, DEM574[0]); 															} 													} 						else{ 							if (DEM579[DEM354][2] == 0) { 								print(2, 52, OLED_FONT_SMALL, 1, DEM574[0]); 															} 							else { 								print(2, 52, OLED_FONT_SMALL, 1, DEM573[0]); 															} 													} 											} 					if (DEM353) { 						DEM439               = DEM152(DEM439, 0); 						DEM440               = DEM152(DEM440, 1); 						DEM441             = DEM152(DEM441, 2); 						DEM442           = DEM152(DEM442, 3); 						DEM443             = DEM152(DEM443, 4); 						DEM444             = DEM152(DEM444, 5); 						DEM445              = DEM152(DEM445, 6); 						DEM446           = DEM152(DEM446, 7); 						DEM386          = DEM152(DEM386, 8); 						DEM387   = DEM152(DEM387, 9); 						DEM388 = DEM152(DEM388, 10); 						DEM389      = DEM152(DEM389, 11); 						DEM390    = DEM152(DEM390, 12); 						DEM391    = DEM152(DEM391, 13); 						DEM392    = DEM152(DEM392, 14); 						DEM393      = DEM152(DEM393, 15); 						DEM394      = DEM152(DEM394, 16); 						DEM275              = DEM152(DEM275, 17); 						DEM276           = DEM152(DEM276, 18); 						DEM277            = DEM152(DEM277, 19); 						DEM278            = DEM152(DEM278, 20); 						DEM279= DEM152(DEM279, 21); 						DEM407               = DEM152(DEM407, 22); 						DEM408               = DEM152(DEM408, 23); 						DEM409                   = DEM152(DEM409, 24); 						DEM410                   = DEM152(DEM410, 25); 						DEM411                   = DEM152(DEM411, 26); 						DEM412   = DEM152(DEM412, 27); 						DEM413   = DEM152(DEM413, 28); 						DEM414 = DEM152(DEM414, 29); 						DEM415   = DEM152(DEM415, 30); 						DEM416   = DEM152(DEM416, 31); 						DEM417 = DEM152(DEM417, 32); 						DEM418   = DEM152(DEM418, 33); 						DEM419   = DEM152(DEM419, 34); 						DEM420 = DEM152(DEM420, 35); 						DEM421   = DEM152(DEM421, 36); 						DEM422   = DEM152(DEM422, 37); 						DEM423 = DEM152(DEM423, 38); 						DEM424   = DEM155(DEM424, 39); 						DEM425         = DEM155(DEM425, 40); 						DEM426   = DEM152(DEM426, 41); 						DEM427     = DEM152(DEM427, 42); 						DEM428                   = DEM155(DEM428, 43); 						DEM1253 = DEM152(DEM1253, 54); 						DEM1246 = DEM152(DEM1246, 55); 						DEM429               = DEM155(DEM429, 44); 						DEM430 = DEM155(DEM430, 45); 						DEM431     = DEM152(DEM431, 46); 						DEM432               = DEM155(DEM432, 47); 						DEM433 = DEM152(DEM433, 48); 						DEM434 = DEM152(DEM434, 49); 						DEM435 = DEM152(DEM435, 50); 						DEM436 = DEM152(DEM436, 51); 						DEM437               = DEM152(DEM437, 52); 						DEM438                 = DEM152(DEM438, 53); 						DEM395       = DEM155(DEM395     ,56 ); 						DEM396       = DEM155(DEM396     ,57 ); 						DEM397      = DEM152(DEM397    ,58 ); 						DEM398   = DEM152(DEM398 ,59 ); 						DEM399       = DEM155(DEM399     ,60 ); 						DEM400       = DEM155(DEM400     ,61 ); 						DEM401   = DEM152(DEM401 ,62 ); 						DEM402      = DEM152(DEM402    ,63 ); 						DEM403          = DEM155(DEM403        ,64 ); 						DEM404          = DEM155(DEM404        ,65 ); 						DEM405         = DEM152(DEM405       ,66 ); 						DEM455             = DEM155(DEM455           ,67 ); 						DEM29             = DEM152(DEM29           ,68); 						DEM476           = DEM152(DEM476         ,69); 						DEM474         = DEM152(DEM474       ,70); 						if (!get_ival(PS4_L2)) { 							if (event_press(PS4_RIGHT)) { 								DEM355 = clamp(DEM355 + 1, DEM579[DEM354][0], DEM579[DEM354][1]); 								DEM359 = TRUE; 															} 							if (event_press(PS4_LEFT)) { 								DEM355 = clamp(DEM355 - 1, DEM579[DEM354][0], DEM579[DEM354][1]); 								DEM359 = TRUE; 															} 													} 						if (event_press(PS4_CIRCLE)) { 							DEM352 = TRUE; 							DEM353 = FALSE; 							DEM359 = TRUE; 													} 						DEM169(); 						DEM955 = DEM839[DEM355][0]; 						DEM956 = DEM839[DEM355][1]; 						if (DEM839[DEM355][4] == 0) { 							DEM158(DEM955, DEM164(DEM955), 4, 20, OLED_FONT_SMALL); 							DEM158(DEM956, DEM164(DEM956), 97, 20, OLED_FONT_SMALL); 													} 											} 					if (DEM359) { 						cls_oled(OLED_BLACK); 						rect_oled(0, 0, OLED_WIDTH, OLED_HEIGHT, OLED_BLACK, OLED_WHITE); 						line_oled(0, 14, 127, 14, 1, 1); 						if (DEM353) { 							print(DEM214(DEM167(DEM588[DEM355]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DEM588[DEM355]); 													} 						else { 							print(DEM214(DEM167(DEM589[DEM354]), OLED_FONT_SMALL_WIDTH), 3, OLED_FONT_SMALL, OLED_WHITE, DEM589[DEM354]); 													} 						DEM359 = FALSE; 					} 									} 				if (!DEM352 && !DEM353) { 					if (DEM358) { 						cls_oled(0); 						combo_run(DEM72); 						DEM358 = FALSE; 						DEM356 = TRUE; 						vm_tctrl(0); 					} 					if(DEM360 == 0){ 						DEM447      = PS4_CIRCLE; 						DEM448      = PS4_CROSS ; 						DEM449    = PS4_L1    ; 						DEM450  = PS4_R1; 						DEM451    = PS4_L2; 						DEM452    = PS4_R2; 						DEM453     = PS4_SQUARE; 						DEM454  = PS4_TRIANGLE; 					} 					else if(DEM360 == 1){ 						DEM447      = PS4_SQUARE; 						DEM448      = PS4_CROSS ; 						DEM449    = PS4_L1    ; 						DEM450  = PS4_R1; 						DEM451    = PS4_L2; 						DEM452    = PS4_R2; 						DEM453     = PS4_CIRCLE; 						DEM454  = PS4_TRIANGLE; 					} 					else if(DEM360 == 2){ 						DEM447      = DEM1394[DEM439]; 						DEM448      = DEM1394[DEM440] ; 						DEM449    = DEM1394[DEM441]  ; 						DEM450  = DEM1394[DEM442]; 						DEM451    = DEM1394[DEM443]; 						DEM452    = DEM1394[DEM444]; 						DEM453     = DEM1394[DEM445]; 						DEM454  = DEM1394[DEM446]; 					} 					if (get_ival(XB1_LS) && get_ival(XB1_RS) && !DEM1200) { 						set_val(DEM448, 0); 						vm_tctrl(0); 						combo_run(DEM77); 											} 					if (DEM718) { 						if ((get_polar(POLAR_LS,POLAR_RADIUS) > 3000 ) ){ 							DEM593 = ((((get_polar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 							DEM1053 = DEM1405[DEM593][0]; 							DEM669 = DEM1405[DEM593][1]; 													} 					} 					if (get_ival(XB1_RS)) { 						if (event_press(PS4_RIGHT)) { 							DEM428 += 5; 							DEM210(DEM214(sizeof(DEM595) - 1, OLED_FONT_MEDIUM_WIDTH), DEM595[0], DEM428); 													} 						if (event_press(PS4_LEFT)) { 							DEM428 -= 5; 							DEM210(DEM214(sizeof(DEM595) - 1, OLED_FONT_MEDIUM_WIDTH), DEM595[0], DEM428); 													} 						set_val(PS4_RIGHT, 0); 						set_val(PS4_LEFT, 0); 											} 					if (get_ival(XB1_RS) && !DEM615 ) { 						if (event_press(PS4_UP)) { 							DEM600 += 25; 							DEM210(DEM214(sizeof(DEM601) - 1, OLED_FONT_MEDIUM_WIDTH), DEM601[0], DEM600); 													} 						if (event_press(PS4_DOWN)) { 							DEM600 -= 25; 							DEM210(DEM214(sizeof(DEM601) - 1, OLED_FONT_MEDIUM_WIDTH), DEM601[0], DEM600); 													} 						set_val(PS4_UP, 0); 						set_val(PS4_DOWN, 0); 											} 					if (DEM374) { 						DEM248(); 											} 					if (DEM375) { 						DEM249(); 						DEM250(); 											} 					if (!DEM375) { 						if (get_ival(DEM447)) { 							if (event_press(PS4_RIGHT)) { 								DEM608 += 2; 								DEM210(DEM214(sizeof(DEM609) - 1, OLED_FONT_MEDIUM_WIDTH), DEM609[0], DEM608); 															} 							if (event_press(PS4_LEFT)) { 								DEM608 -= 2; 								DEM210(DEM214(sizeof(DEM609) - 1, OLED_FONT_MEDIUM_WIDTH), DEM609[0], DEM608); 															} 							set_val(PS4_RIGHT, 0); 							set_val(PS4_LEFT, 0); 													} 						if(!get_ival(DEM449) ){ 							if(get_ival(DEM447) && get_ptime(DEM447) > DEM608){ 								set_val(DEM447,0); 															} 													} 											} 					if(DEM378){ 						DEM253(); 											} 					if (DEM370) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_SHARE)) { 								DEM615 = !DEM615; 								DEM229(DEM615); 															} 							set_val(PS4_SHARE, 0); 													} 											} 					if (DEM615 && DEM370) { 						vm_tctrl(0); 						combo_stop(DEM85); 						if (get_ival(XB1_RS)) { 							if (event_press(PS4_UP)) { 								DEM424 += 10; 								DEM210(DEM214(sizeof(DEM617) - 1, OLED_FONT_MEDIUM_WIDTH), DEM617[0], DEM424); 															} 							if (event_press(PS4_DOWN)) { 								DEM424 -= 10; 								DEM210(DEM214(sizeof(DEM617) - 1, OLED_FONT_MEDIUM_WIDTH), DEM617[0], DEM424); 															} 							set_val(PS4_UP, 0); 							set_val(PS4_DOWN, 0); 													} 						DEM222(DEM1097); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_RIGHT)) { 								DEM622 = FALSE; 								vm_tctrl(0); 								combo_run(DEM78); 															} 							if (event_press(PS4_LEFT)) { 								DEM622 = TRUE; 								vm_tctrl(0); 								combo_run(DEM78); 															} 							set_val(PS4_L1,0); 													} 											} 					if (DEM371) { 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_OPTIONS)) { 								DEM624 = !DEM624; 								DEM229(DEM624); 															} 							set_val(PS4_OPTIONS, 0); 													} 											} 					if (DEM624 && DEM371) { 						vm_tctrl(0); 						DEM222(DEM1099); 						if (get_ival(PS4_L1)) { 							if (event_press(PS4_LEFT)) { 								DEM625 = FALSE; 								vm_tctrl(0); 								combo_run(DEM79); 															} 							if (event_press(PS4_RIGHT)) { 								DEM625 = TRUE; 								vm_tctrl(0); 								combo_run(DEM79); 															} 													} 											} 					if(DEM363 || DEM364 ){ 						DEM118(); 											} 					if (DEM361) { 						if (DEM361 == DEM1103) DEM628 = TRUE; 						if (DEM361 == DEM1104) { 							if (event_press(DEM1404[-1 +DEM394]) && get_brtime(DEM1404[-1 +DEM394]) <= 200) { 								DEM628 = !DEM628; 								DEM229(DEM628); 															} 							set_val(DEM1404[-1 +DEM394], 0); 													} 						if (DEM361 > 0 && DEM361 < 3 && DEM628 == 1) { 							DEM226(); 													} 						else if (DEM361 == 3) { 							if (get_ival(DEM1404[-1 +DEM394])) { 								DEM226(); 															} 							set_val(DEM1404[-1 +DEM394], 0); 													} 											} 									if (DEM362) { 						if (DEM362 == DEM1103) DEM631 = TRUE; 						if (DEM362 == DEM1104) { 							if (event_press(DEM1404[-1 +DEM279]) && get_brtime(DEM1404[-1 +DEM279]) <= 200) { 								DEM631 = !DEM631; 								DEM229(DEM631); 															} 							set_val(DEM1404[-1 +DEM279], 0); 													} 						if (DEM362 > 0 && DEM362 < 3 && DEM631 == 1) { 							DEM224(); 													} 						else if (DEM362 == 3) { 							if (get_ival(DEM1404[-1 +DEM279])) { 								DEM224(); 															} 							set_val(DEM1404[-1 +DEM279], 0); 													} 											} 					if (DEM365) { 						if (DEM365 == 1) { 							DEM634 = PS4_R3; 							DEM631 = FALSE; 													} 						if (DEM365 == 2) { 							DEM634 = PS4_L3; 							DEM631 = FALSE; 													} 						if (DEM365 == 3) { 							DEM634 = XB1_PR1; 							DEM631 = FALSE; 													} 						if (DEM365 == 4) { 							DEM634 = XB1_PR2; 							DEM631 = FALSE; 													} 						if (DEM365 == 5) { 							DEM634 = XB1_PL1; 							DEM631 = FALSE; 													} 						if (DEM365 == 6) { 							DEM634 = XB1_PL2; 							DEM631 = FALSE; 													} 						if(get_ival(DEM634)){ 							if(DEM407 || DEM408){ 								if( DEM407 && event_press(PS4_L1)){ 									DEM479 = FALSE; 									DEM1052 = DEM407  ; 									DEM227( DEM407   ); 								} 								if( DEM408 && event_press(PS4_R1)){ 									DEM479 = TRUE; 									DEM1052 =  DEM408 ; 									DEM227( DEM408   ); 																	} 								set_val(PS4_L1,0); 								set_val(PS4_R1,0); 								block = TRUE; 															} 							if( DEM409 ){ 								if(event_press(PS4_SQUARE)){ 									DEM479 = FALSE; 									DEM1052 =  DEM409  ; 													combo_stop(DEM88); 				combo_stop(DEM97); 				combo_stop(DEM89); 				combo_stop(DEM98); 				combo_stop(DEM95); 				combo_stop(DEM96); 				combo_stop(DEM92); 				combo_stop(DEM94); 				combo_stop(DEM91); 				combo_stop(DEM87); 				combo_stop(DEM85); 				combo_stop(DEM90); 				combo_stop(DEM107); 				combo_stop(DEM109); 				combo_stop(DEM100); 				combo_stop(DEM108); 				combo_stop(DEM99); 									DEM227( DEM409   ); 								} 								if(event_press(PS4_TRIANGLE)){ 									DEM479 = TRUE; 									DEM1052 =  DEM409  ; 									DEM227( DEM409   ); 								} 								set_val(PS4_SQUARE,0); 								set_val(PS4_TRIANGLE,0); 								block = TRUE; 															} 							if( DEM410 ){ 								if(event_press(PS4_CROSS)){ 									DEM479 = FALSE; 									DEM1052 =  DEM410  ; 									DEM227( DEM410   ); 								} 								if(event_press(PS4_CIRCLE)){ 												combo_stop(DEM88); 				combo_stop(DEM97); 				combo_stop(DEM89); 				combo_stop(DEM98); 				combo_stop(DEM95); 				combo_stop(DEM96); 				combo_stop(DEM92); 				combo_stop(DEM94); 				combo_stop(DEM91); 				combo_stop(DEM87); 				combo_stop(DEM85); 				combo_stop(DEM90); 				combo_stop(DEM107); 				combo_stop(DEM109); 				combo_stop(DEM100); 				combo_stop(DEM108); 				combo_stop(DEM99); 									DEM479 = TRUE; 									DEM1052 =  DEM410  ; 									DEM227( DEM410   ); 								} 								set_val(PS4_CROSS,0); 								set_val(PS4_CIRCLE,0); 								block = TRUE; 															} 							if( DEM411 ){ 								if(event_press(PS4_R3)){ 									DEM479 = FALSE; 									DEM1052 =  DEM411  ; 									DEM227( DEM411   ); 								} 								set_val(PS4_R3,0); 								block = TRUE; 															} 													} 						set_val(DEM634,0); 											} 					if (DEM366) { 						if (DEM413 == 1) { 							if (event_press(DEM1404[-1 + DEM412]) && !DEM1150) { 								vm_tctrl(0); 								combo_run(DEM82); 															} 							else if (event_press(DEM1404[-1 + DEM412]) && DEM1150) { 								set_val(DEM1404[-1 + DEM412], 0); 								DEM479 = !DEM414; 								DEM1052 = DEM366; 								DEM227(DEM366); 															} 													} 						else { 							if (event_press(DEM1404[-1 + DEM412])) { 								DEM479 = !DEM414; 								set_val(DEM1404[-1 + DEM412], 0); 								DEM1052 = DEM366; 								DEM227(DEM366); 															} 													} 					} 					if (DEM368) { 						if (DEM419 == 1) { 							if (event_press(DEM1404[-1 +DEM418]) && !DEM1150) { 								vm_tctrl(0); 								combo_run(DEM82); 															} 							else if (event_press(DEM1404[-1 +DEM418]) && DEM1150) { 								set_val(DEM1404[-1 +DEM418], 0); 								DEM479 = !DEM420; 								DEM1052 = DEM368; 								DEM227(DEM368); 															} 													} 						else { 							if (event_press(DEM1404[-1 +DEM418])) { 								DEM479 = !DEM420; 								set_val(DEM1404[-1 +DEM418], 0); 								DEM1052 = DEM368; 								DEM227(DEM368); 															} 													} 					} 					if (DEM367) { 						if (DEM416 == 1) { 							if (event_press(DEM1404[-1 +DEM415]) && !DEM1150) { 								vm_tctrl(0); 								combo_run(DEM82); 															} 							else if (event_press(DEM1404[-1 +DEM415]) && DEM1150) { 								set_val(DEM1404[-1 +DEM415], 0); 								DEM479 = !DEM417; 								DEM1052 = DEM367; 								DEM227(DEM367); 															} 													} 						else { 							if (event_press(DEM1404[-1 +DEM415])) { 								DEM479 = !DEM417; 								set_val(DEM1404[-1 +DEM415], 0); 								DEM1052 = DEM367; 								DEM227(DEM367); 															} 													} 					} 					if (DEM369) { 						if (DEM422 == 1) { 							if (event_press(DEM1404[-1 +DEM421]) && !DEM1150) { 								vm_tctrl(0); 								combo_run(DEM82); 															} 							else if (event_press(DEM1404[-1 +DEM421]) && DEM1150) { 								set_val(DEM1404[-1 +DEM421], 0); 								DEM479 = !DEM423; 								DEM1052 = DEM369; 								DEM227(DEM369); 															} 													} 						else { 							if (event_press(DEM1404[-1 +DEM421])) { 								DEM479 = !DEM423; 								set_val(DEM1404[-1 +DEM421], 0); 								DEM1052 = DEM369; 								DEM227(DEM369); 															} 													} 					} 					if (DEM1052 == DEM310 && combo_running(DEM30)) set_val(DEM449, 100); 					if(DEM383){ 						if(!block){ 							if(!get_val(DEM451)){ 								if( !get_val(DEM452)){ 									if(get_val(DEM448)){ 										DEM650 += get_rtime(); 																			} 									if(DEM402){ 										if(get_ival(DEM448) && get_ptime(DEM448) > DEM400){ 											set_val(DEM448,0); 																					} 																			} 									if(event_release(DEM448)){ 										if( DEM650 < DEM399 ){ 											DEM651 = DEM399 - DEM650; 											combo_run(DEM107); 																					} 										else{ 											if(DEM401) combo_run(DEM108); 																					} 										DEM650 = 0; 																			} 																	} 							} 						} 											} 					if(DEM382){ 						if(!block){ 							if(!get_ival(DEM451)){ 								if( !get_ival(DEM452)){ 									if(get_ival(DEM454)){ 										DEM652 += get_rtime(); 																			} 									if(event_release(DEM454)){ 										if(DEM652 < DEM395){ 											DEM653 = DEM395 - DEM652; 											combo_run(DEM109); 																					} 										else{ 											if(DEM398) combo_run(DEM99); 																					} 										DEM652 = 0; 																			} 																	} 							} 						} 											} 					if(DEM384){ 						if(!block){ 							if(get_ival(DEM453)){ 								DEM654 += get_rtime(); 															} 							if(DEM405){ 								if(get_ival(DEM453) && get_ptime(DEM453) > DEM404){ 									set_val(DEM453,0); 																	} 															} 							if(event_release(DEM453)){ 								if(DEM654 && (DEM654 < DEM403)){ 									DEM655 = DEM403 - DEM654; 									combo_run(DEM100); 																	} 								DEM654 = 0; 															} 													} 											} 					if (DEM372) { 						if (event_press(DEM1404[-1 +DEM426])) { 							vm_tctrl(0); 							combo_run(DEM77); 													} 						set_val(DEM1404[-1 +DEM426], 0); 											} 					if(!DEM376){ 						DEM429 = 0 ; 						DEM430 = 0; 						DEM431 = FALSE; 						DEM432 = 0; 											} 					if (DEM377) { 						DEM249(); 						if (DEM433 == 0) { 							DEM658 = FALSE; 							DEM457 = 0; 													} 						else { 							DEM658 = TRUE; 							DEM457 = 40; 													} 						if (DEM434 == 0) { 							DEM660 = FALSE; 							DEM456 = 0; 													} 						else { 							DEM660 = TRUE; 							DEM456 = 85; 													} 						if (DEM435 == 0) { 							DEM662 = FALSE; 							DEM458 = 0; 													} 						else { 							DEM662 = TRUE; 							DEM458 = -15; 													} 						if (DEM436 == 0) { 							DEM664 = FALSE; 													} 						else { 							DEM664 = TRUE; 													} 						if(DEM435 == 6 || DEM434 == 6 || DEM433 == 6){ 							if (get_ival(DEM1404[-1 + DEM435]) || get_ival(DEM1404[-1 + DEM434]) || get_ival(DEM1404[-1 + DEM433])){ 								combo_run(DEM0); 															} 													} 						if (DEM662) { 							if (get_val(DEM1404[-1 + DEM435])) { 								set_val(DEM1404[-1 + DEM435], 0); 								combo_run(DEM97); 								DEM1209 = 9000; 															} 													} 						if (DEM664) { 							if (get_val(DEM1404[-1 + DEM436])) { 								set_val(DEM1404[-1 + DEM436], 0); 								combo_run(DEM98); 								DEM1209 = 9000; 							} 							if (combo_running(DEM98)) { 								if (get_ival(DEM448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DEM452) > 30) { 									combo_stop(DEM98); 																	} 															} 													} 						if (DEM660) { 							if (get_val(DEM1404[-1 + DEM434])) { 								set_val(DEM1404[-1 + DEM434], 0); 								DEM255(); 								DEM1209 = 9000; 															} 													} 						if (DEM658) { 							if (get_val(DEM1404[-1 + DEM433])) { 								set_val(DEM1404[-1 + DEM433], 0); 								combo_run(DEM95); 								DEM1209 = 9000; 															} 													} 											} 					else{ 						DEM457 = 0; 						DEM458 = 0; 						DEM456 = 0; 											} 					if (DEM379) { 						DEM256(); 											} 									} 							} 								if (combo_running(DEM111) && (  get_ival(DEM448) ||   get_ival(DEM454) ||         get_ival(DEM451) ||        get_ival(DEM447) ||        get_ival(DEM450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DEM111); 			} 					} 		else { 			if (!get_ival(DEM452)) DEM222(DEM1096); 					} 			} 			DEM268(); 	} combo DEM1 { 	set_val(DEM451, 100); 	set_val(DEM449,100); 	DEM243(); 	wait(400); 	set_val(DEM448,100); 	wait(90); 	wait( 400); 	} combo DEM2 { 	set_val(DEM451, 100); 	set_val(DEM449,100); 	DEM243(); 	wait(400); 	set_val(DEM447,100); 	wait(220); 	wait( 400); 	} combo DEM3 { 	call(DEM28); 	wait( 100); 	call(DEM98); 	DEM239(DEM1053, DEM669); 	wait( 800); 	wait( 350); 	set_val(DEM450,100); 	set_val(DEM449,100); 	wait( 400); 	} combo DEM4 { 	DEM245(); 	wait(38); 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM236(DEM1154,DEM671); 	wait(100); 	DEM243(); 	wait(50); 	vm_tctrl(0); 	wait(350); 	} combo DEM5 { 	DEM243(); 	wait( DEM1056 + random(1,5)); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM6 { 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	DEM239(DEM1154, DEM671); 	wait( DEM1056 + random(1,5)); 	DEM239(DEM1154, DEM671); 	wait( 1000); 	wait( 350); 	} combo DEM7 { 	DEM246(); 	DEM479 = FALSE; 	wait(DEM1056 + random(1,5)); 	DEM243(); 	wait(DEM1056 + random(1,5)); 	DEM246(); 	wait(DEM1056 + random(1,5)); 	DEM479 = TRUE; 	DEM243(); 	wait(DEM1056 + random(1,5)); 	wait(350); 	} combo DEM8 { 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM479 = TRUE; 	DEM243(); 	wait( DEM1056 + random(1,5)); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM479 = FALSE; 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM9 { 	DEM479 = TRUE; 	DEM243(); 	wait(DEM1056 + random(1,5)); 	DEM246(); 	wait(DEM1056 + random(1,5)); 	DEM479 = FALSE; 	DEM243(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM10 { 	DEM479 = FALSE; 	DEM243(); 	wait( DEM1056 + random(1,5)); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM479 = TRUE; 	DEM243(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM11 { 	DEM239(0,0); 	set_val(DEM449,100); 	set_val(DEM450,100); 	DEM245(); 	wait( 60); 	wait( 60); 	} combo DEM12 { 	set_val(DEM1119, inv(DEM1053)); 	set_val(DEM1120, inv(DEM669)); 	set_val(DEM450, 100); 	set_val(DEM449, 100); 	wait( 60); 	set_val(DEM1119, inv(DEM1053)); 	set_val(DEM1120, inv(DEM669)); 	set_val(DEM450, 100); 	set_val(DEM449, 100); 	wait( 500); 	wait( 350); 	} combo DEM13 { 	DEM239(0, 0); 	set_val(DEM451, 100); 	wait( 60); 	DEM239(0, 0); 	set_val(DEM451, 100); 	set_val(DEM447, 100); 	wait( 60); 	DEM239(0, 0); 	set_val(DEM451, 100); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 80); 	DEM239(0, 0); 	set_val(DEM451, 100); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	wait( 350); 	} combo DEM14 { 	set_val(DEM447, 100); 	wait( 60); 	DEM239(inv(DEM1053), inv(DEM669)); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 80); 	DEM239(inv(DEM1053), inv(DEM669)); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	wait( 350); 	} combo DEM15 { 	set_val(DEM449, 100); 	DEM243(); 	wait( 500); 	wait( 350); 	} combo DEM16 { 	DEM246(); 	wait( DEM1056 + random(1,5)); 	if(DEM479) DEM687 = DEM593 + 3; 	else  DEM687 = DEM593 - 3; 	DEM234(DEM687); 	DEM236(DEM1154,DEM671); 	wait(DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	if(DEM479) DEM687 = DEM593 + 1; 	else  DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM236(DEM1154,DEM671); 	wait(DEM1056 + random(1,5)); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM17 { 	set_val(DEM449,100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	set_val(DEM449,100); 	wait( DEM1056 + random(1,5)); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM18 { 	set_val(DEM451,100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451,100); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451,100); 	DEM243(); 	set_val(DEM451,100); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	set_val(DEM451,100); 	set_val(DEM452,100); 	wait(50); 	wait(350); 	} combo DEM19 { 	set_val(DEM451,100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451,100); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451,100); 	DEM243(); 	set_val(DEM451,100); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM20 { 	DEM244(); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	DEM243()    	wait( DEM1056 + random(1,5)); 	DEM479 = !DEM479; 	DEM242(); 	wait( 1000); 	wait( 350); 	} combo DEM21 { 	set_val(DEM449,100); 	DEM246(); 	wait(50); 	DEM239(0,0); 	set_val(DEM449,100); 	wait(50); 	set_val(DEM449,100); 	DEM246(); 	wait(50); 	wait( 350); 	} combo DEM22 { 	DEM239(0, 0); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM23 { 	DEM246(); 	wait(DEM1056 + random(1,5)); 	DEM247()wait(DEM1056 + random(1,5)); 	DEM246(); 	wait(DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM24 { 	set_val(DEM450, 100); 	set_val(DEM449, 100); 	wait( 20); 	set_val(DEM450, 100); 	set_val(DEM449, 100); 	if (DEM479) DEM687 = DEM593 + 4; 	else { 		DEM687 = DEM593 - 4; 			} 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	set_val(DEM452, 100); 	wait( 100); 	wait( 350); 	} combo DEM25 { 	set_val(DEM451, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451, 100); 	wait( 30); 	set_val(DEM451, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM26 { 	set_val(DEM451, 100); 	DEM245(); 	wait( 70); 	set_val(DEM451, 100); 	DEM247(); 	wait( 60); 	set_val(DEM451, 100); 	DEM246(); 	wait( 60); 	wait( 350); 	} combo DEM27 { 	set_val(DEM451, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451, 100); 	wait( 30); 	set_val(DEM451, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM239(0, 0); 	wait( 400); 	set_val(PS4_L2, 100); 	set_val(PS4_L1, 100); 	set_val(PS4_R1, 100); 	set_val(PS4_R2, 100); 	wait( 70); 	wait( 350); 	} combo DEM28 { 	DEM243(); 	wait( 300); 	set_val(PS4_R3,100); 	wait( 60); 	wait( 60); 	wait( 350); } combo DEM29 { 	DEM243(); 	set_val(DEM452, 0); 	wait(350); 	wait( 350); 	} combo DEM30 { 	if (DEM1052 == DEM312) DEM1057 = 200; 	else DEM1057 = 1; 	wait( DEM1057); 	DEM245(); 	wait( 70); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( 70); 	wait( 350); 	} combo DEM31 { 	set_val(DEM449, 100)DEM245(); 	DEM239(DEM1053,DEM669); 	wait( 50); 	set_val(DEM449, 100)DEM247(); 	DEM239(DEM1053,DEM669); 	wait( 50); 	set_val(DEM449, 100)DEM243(); 	DEM239(DEM1053,DEM669); 	wait( 50); 	DEM239(DEM1053,DEM669); 	wait(465); 	DEM239(DEM1053,DEM669); 	set_val(DEM451, 100); 	set_val(DEM452, 100); 	wait(50); 	if (DEM479) DEM687 = DEM593 - 1; 	else DEM687 = DEM593 + 1; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 50); 	if (DEM479) DEM687 = DEM593 + 4; 	else DEM687 = DEM593 - 4; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 700); 	wait( 350); 	} combo DEM32 { 	if (DEM1052 == DEM312) DEM1057 = 200; 	else DEM1057 = 1; 	set_val(DEM451,100); 	wait( DEM1057); 	DEM245(); 	set_val(DEM451,100); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	set_val(DEM451,100); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	set_val(DEM451,100); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM33 { 	if (DEM479) DEM687 = DEM593 - 2; 	else DEM687 = DEM593 + 2; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	wait( 280); 	DEM247(); 	wait( 50); 	if (DEM479) DEM687 = DEM593 + 2; 	else DEM687 = DEM593 - 2; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 45); 	set_val(DEM447, 100); 	DEM239(DEM1154, DEM671); 	wait( 45); 	DEM239(DEM1154, DEM671); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 45); 	DEM239(DEM1154, DEM671); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 45); 	DEM239(DEM1154, DEM671); 	wait( 100); 	DEM239(DEM1154, DEM671); 	wait( 500); 	wait( 350); 	} combo DEM34 { 	DEM243(); 	wait( 280); 	DEM242()  set_val(DEM447, 100); 	set_val(DEM451, 100); 	wait( 60); 	DEM242()  set_val(DEM451, 100); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 60); 	DEM242()  set_val(DEM451, 100); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	wait( 250); 	DEM242()   	wait( 300); 	wait( 350); 	} combo DEM35 { 	DEM243(); 	wait( 300); 	DEM245(); 	wait( 60); 	wait( 350); 	} combo DEM36 { 	set_val(DEM449, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM449, 100); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM449, 100); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM37 { 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM38 { 	set_val(DEM449, 100); 	DEM244(); 	wait( 60); 	set_val(DEM449, 100); 	DEM247(); 	wait( 60); 	set_val(DEM449, 100); 	DEM243(); 	wait( 60); 	wait( 300); 	wait( 350); 	} combo DEM39 { 	DEM246(); 	set_val(DEM449,100); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	set_val(DEM449,100); 	wait( 70); 	DEM243(); 	set_val(DEM449,100); 	wait( 70); 	wait( 350); 	} combo DEM40 { 	if (DEM479) DEM687 = DEM593 + 3; 	else DEM687 = DEM593 - 3; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	set_val(DEM447, 100); 	set_val(DEM451,100); 	wait( 60); 	set_val(DEM451,100); 	DEM239(DEM1154, DEM671); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 80); 	set_val(DEM451,100); 	DEM239(DEM1154, DEM671); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	set_val(DEM451,100); 	DEM239(DEM1154, DEM671); 	wait( 300); 	wait( 350); 	} combo DEM41 { 	set_val(DEM449, 100); 	DEM245(); 	DEM239(0, 0); 	wait( DEM1056 + random(1,5)); 	set_val(DEM449, 100); 	DEM247(); 	DEM239(0, 0); 	wait( 65); 	set_val(DEM449, 100); 	DEM239(0, 0); 	DEM246(); 	wait( DEM1056 + random(1,5)); 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	set_val(DEM452,0); 	DEM239(DEM1154, DEM671); 	wait( 200); 	set_val(DEM452,0); 	wait( 350); 	} combo DEM42 { 	if (DEM1052 == DEM312) DEM1057 = 200; 	else DEM1057 = 1; 	wait( DEM1057); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	wait( 350); 	} combo DEM43 { 	DEM246(); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 300); 	wait( 350); 	} combo DEM44 { 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	if (DEM1052 == DEM325) DEM242(); 	set_val(DEM451, 100); 	set_val(DEM452, 100); 	wait( 200); 	if (DEM1052 == DEM325) DEM242(); 	wait( 300); 	wait( 350); 	} combo DEM45 { 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM247(); 	wait( DEM1056 + random(1,5)); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	if (DEM1052 == DEM325) DEM242(); 	set_val(DEM451, 100); 	set_val(DEM452, 100); 	wait( 200); 	if (DEM1052 == DEM325) DEM242(); 	wait( 300); 	wait( 350); 	} combo DEM46 { 	call(DEM33)call(DEM35); 	} combo DEM47 {    DEM718 = FALSE; 	DEM239(DEM1053, DEM669); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM239(DEM1053, DEM669); 	DEM247(); 	DEM718 = FALSE; 	wait( DEM1056 + random(1,5)); 	DEM239(DEM1053, DEM669); 	DEM243(); 	DEM718 = FALSE; 	wait( DEM1056 + random(1,5)); 	set_val(DEM451, 100); 	set_val(DEM452, 100); 	DEM239(inv(DEM1053), inv(DEM669)); 	DEM718 = FALSE; 	wait( 400); 	wait( 350); 	DEM718 = TRUE; 	} combo DEM48 { 	DEM239(DEM1053, DEM669); 	set_val(XB1_LS, 100); 	DEM245(); 	wait( DEM1056 + random(1,5)); 	DEM239(DEM1053, DEM669); 	DEM247(); 	set_val(XB1_LS, 100); 	wait( DEM1056 + random(1,5)); 	DEM239(DEM1053, DEM669); 	DEM243(); 	wait( DEM1056 + random(1,5)); 	set_val(DEM451, 100); 	set_val(DEM452, 100); 	if (DEM479) DEM687 = DEM593 + 4; 	else DEM687 = DEM593 - 4; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 220); 	if (DEM479) DEM687 = DEM593 + 4; 	else DEM687 = DEM593 - 4; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 60); 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	wait( 600); 	wait( 350); 	} combo DEM49 { 	set_val(DEM448, 0); 	set_val(DEM447, 100); 	wait( 80); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 80); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 80); 	wait( 500); 	wait( 350); 	} combo DEM50 { 	set_val(DEM447, 100); 	set_val(DEM452,100); 	wait( 60); 	set_val(DEM452,100); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	set_val(DEM452,100); 	wait( 60); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	set_val(DEM452,100); 	wait( 60); 	wait( 350); 	} combo DEM51 { 	set_val(DEM449,100); 	set_val(DEM450,100); 	DEM239(inv(DEM1053), inv(DEM669)); 	wait( 200); 	set_val(DEM449,100); 	set_val(DEM450,100); 	DEM479 = FALSE; 	DEM242(); 	wait( 50); 	set_val(DEM449,100); 	set_val(DEM450,100); 	DEM479 = !DEM479; 	DEM242(); 	set_val(DEM449,100); 	set_val(DEM450,100); 	wait( 540); 	wait( 350); 	} combo DEM52 { 	set_val(DEM447, 100); 	wait( 60); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 60); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	wait( 140); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait( 100); 	wait( 350); 	} combo DEM53 { 	DEM239(inv(DEM1053), inv(DEM669)); 	set_val(DEM451, 100); 	set_val(DEM447, 100); 	wait( 60); 	DEM239(inv(DEM1053), inv(DEM669)); 	set_val(DEM451, 100); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 60); 	DEM239(inv(DEM1053), inv(DEM669)); 	set_val(DEM451, 100); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	DEM239(0, 0); 	wait( 300); 	wait( 350); 	} combo DEM54 { 	set_val(DEM449, 100); 	set_val(DEM453, 100); 	wait( 60); 	set_val(DEM449, 100); 	set_val(DEM453, 100); 	set_val(DEM448, 100); 	wait( 60); 	set_val(DEM449, 100); 	set_val(DEM453, 0); 	set_val(DEM448, 100); 	DEM242(); 	wait( 60); 	set_val(DEM449, 100); 	DEM242(); 	wait( 300); 	wait( 350); 	} combo DEM55 { 	set_val(DEM447, 100); 	wait( 170); 	set_val(PS4_L2, 100); 	wait(50); 	set_val(PS4_L2, 100); 	set_val(PS4_R2, 100); 	wait(800); 	} combo DEM56 { 	set_val(DEM447, 100); 	set_val(DEM451,100); 	wait( 60); 	set_val(DEM451,100); 	set_val(DEM447, 100); 	set_val(DEM448, 100); 	wait( 60); 	set_val(DEM451,100); 	set_val(DEM447, 0); 	set_val(DEM448, 100); 	wait( 60); 	wait( 350); 	} combo DEM57 { 	set_val(DEM449, 100); 	DEM245(); 	wait( 300); 	wait( 350); 	} combo DEM58 { 	DEM246(); 	wait( 70); 	DEM247(); 	wait( 70); 	DEM245(); 	wait( 70); 	wait( 350); 	} combo DEM59 { 	set_val(DEM449,100); 	DEM246(); 	wait( 70); 	set_val(DEM449,100); 	DEM247(); 	wait( 70); 	DEM245(); 	set_val(DEM449,100); 	wait(50); 	wait( 350); 	} combo DEM60 { 	DEM239(DEM1053, DEM669); 	DEM246(); 	wait( 100); 	DEM247(); 	DEM239(DEM1053, DEM669); 	wait( 60); 	DEM245(); 	DEM239(DEM1053, DEM669); 	wait( 320); 	DEM239(DEM1053, DEM669); 	DEM247(); 	wait( 220); 	DEM239(DEM1053, DEM669); 	DEM245(); 	DEM239(DEM1053, DEM669); 	wait( 100); 	wait( 350); 	} combo DEM61 { 	call(DEM83); 	DEM239(0, 0); 	call(DEM84); 	call(DEM84); 	call(DEM84); 	call(DEM84); 	call(DEM84); 	set_val(DEM451, 100); 	DEM246(); 	wait( 70); 	set_val(DEM451, 100); 	DEM247(); 	wait( 60); 	set_val(DEM451, 100); 	DEM245(); 	wait( 60); 	set_val(DEM451, 100); 	wait( 600); 	wait( 350); 	} combo DEM62 { 	set_val(DEM451,100); 	set_val(DEM450,100); 	if (DEM479) DEM687 = DEM593 - 2; 	else DEM687 = DEM593 + 2; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	wait(50); 	set_val(DEM450,100); 	set_val(DEM451,100); 	if (DEM479) DEM687 = DEM593 - 3; 	else DEM687 = DEM593 + 3; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	wait(50); 	set_val(DEM450,100); 	set_val(DEM451,100); 	if (DEM479) DEM687 = DEM593 - 4; 	else DEM687 = DEM593 + 4; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	wait(50); 	set_val(DEM450,100); 	set_val(DEM451,100); 	if (DEM479) DEM687 = DEM593 - 5; 	else DEM687 = DEM593 + 5; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	set_val(DEM451,100); 	set_val(DEM450,100); 	wait(50); 	set_val(DEM450,100); 	set_val(DEM451,100); 	if (DEM479) DEM687 = DEM593 - 6; 	else DEM687 = DEM593 + 6; 	DEM234(DEM687); 	DEM236(DEM1154, DEM671); 	wait(50); 	} combo DEM63 { 	wait( 100); 	DEM239(0, 0); 	DEM245(); 	wait( 70); 	DEM239(0, 0); 	DEM247()   	wait( 70); 	DEM239(0, 0); 	DEM245()   	wait( 70); 	DEM239(0, 0); 	DEM247()   	wait( 70); 	DEM239(0, 0); 	DEM246(); 	wait( 70); 	DEM239(0, 0); 	wait( 350); 	} combo DEM64 { 	set_val(PS4_R3,100); 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	DEM239(DEM1154, DEM671); 	wait( 70); 	DEM239(DEM1154, DEM671); 	wait( 400); 	wait( 350); 	} combo DEM65 { 	call(DEM83); 	DEM239(0,0); 	wait( 60); 	set_val(PS4_R3,100); 	if (DEM479) DEM687 = DEM593 + 1; 	else DEM687 = DEM593 - 1; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	DEM239(DEM1154, DEM671); 	wait( 70); 	DEM239(DEM1154, DEM671); 	wait( 400); 	wait( 350); 	} combo DEM66 { 	call(DEM83); 	DEM239(0,0); 	set_val(DEM451,100); 	set_val(DEM452,100); 	wait( 750); 	} combo DEM67 { 	set_val(PS4_R3,100); 	if (DEM479) DEM687 = DEM593 + 2; 	else DEM687 = DEM593 - 2; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	DEM239(DEM1154, DEM671); 	wait( 70); 	DEM239(DEM1154, DEM671); 	wait( 400); 	wait( 350); 	} combo DEM68 { 	set_val(DEM451,100); 	set_val(PS4_R3,100); 	if (DEM479) DEM687 = DEM593 ; 	else DEM687 = DEM593; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	DEM239(DEM1154, DEM671); 	wait( 70); 	set_val(DEM451,100); 	DEM239(DEM1154, DEM671); 	wait( 400); 	wait( 350); 	} combo DEM69 { 	call(DEM83); 	set_val(DEM451,100); 	set_val(PS4_R3,100); 	if (DEM479) DEM687 = DEM593 ; 	else DEM687 = DEM593; 	DEM234(DEM687); 	DEM239(DEM1154, DEM671); 	DEM239(DEM1154, DEM671); 	wait( 70); 	set_val(DEM451,100); 	DEM239(DEM1154, DEM671); 	wait( 400); 	wait( 350); 	} combo DEM70 { 	DEM239(0,0); 	set_val(DEM450,100); 	set_val(DEM449,100); 	DEM243(); 	wait( 350); 	wait( 350); 	set_val(DEM450,100); 	set_val(DEM449,100); 	wait( 400); 	} int DEM141 ; int DEM764 ; int DEM765 ; int DEM766; int DEM767; function DEM127(DEM128){ 	DEM764 = 2; 	DEM765 = 987654; 	DEM141 = 54321; 	DEM766 = (DEM128 >> DEM764) | (DEM128 << (32 - DEM764)); 	DEM767 = (((DEM766 >> ((DEM766 & 0xF) % 13)) & 0x7FFFF) + DEM141) % DEM765 + 123456; 	return DEM767; 	} define DEM769 = -1; define DEM536 = -2; define DEM771 = -3; define DEM772 = 0; define DEM537 = 1; function DEM129(DEM128, DEM131, DEM132) { 	if(DEM128 > DEM132) return DEM131; 	if(DEM128 < DEM131) return DEM132; 	return DEM128; 	} int DEM776,DEM777; function DEM133(DEM134,DEM135,DEM136,DEM137,DEM138,DEM139){ 	if(!DEM139){ 		print(DEM142(DEM140(DEM134),DEM137,DEM135),DEM136,DEM137,DEM138,DEM134)     	} 	else{ 		if(DEM134 < 0){ 			putc_oled(1,45); 					} 		if(DEM134){ 			for(DEM776 = DEM146(DEM134) + DEM777 = (DEM134 < 0 ),DEM134 = abs(DEM134); 			DEM134 > 0; 			DEM776-- , DEM777++){ 				putc_oled(DEM776,DEM134%10 + 48); 				DEM134 = DEM134/10; 							} 					} 		else{ 			putc_oled(1,48); 			DEM777 = 1         		} 		puts_oled(DEM142(DEM777,DEM137,DEM135),DEM136,DEM137,DEM777 ,DEM138); 			} 	} int DEM798; function DEM140(DEM141) { 	DEM798 = 0; 	do { 		DEM141++; 		DEM798++; 			} 	while (duint8(DEM141)); 	return DEM798; 	} function DEM142(DEM143,DEM137,DEM135) { 	if(DEM135 == -3){ 		return 128 - ((DEM143 * (7 + (DEM137 > 1) + DEM137 * 4)) + 3 ); 			} 	if(DEM135 == -2){ 		return 64 - ((DEM143 * (7 + (DEM137 > 1) + DEM137 * 4)) / 2); 			} 	if(DEM135 == -1){ 		return 3 	} 	return DEM135; 	} function DEM146(DEM147) { 	for(DEM776 = 1; 	DEM776 < 11; 	DEM776++){ 		if(!(abs(DEM147) / pow(10,DEM776))){ 			return DEM776; 			break; 					} 			} 	return 1; 	} function DEM148() { 	if (get_ival(DEM447)) { 		set_val(DEM447, 0); 		if (get_ival(DEM449)) DEM805 = 50; 		if (!get_ival(DEM449)) DEM805 = 410; 		combo_run(DEM71); 			} 	if (DEM804 > 0) set_polar(POLAR_LS, DEM804 * -1, 32767); 	if (get_ival(PS4_RIGHT) && get_ival(PS4_DOWN)) DEM804 = 345; 	if (get_ival(PS4_RIGHT) && get_ival(PS4_UP)) DEM804 = 45; 	if (get_ival(PS4_LEFT) && get_ival(PS4_UP)) DEM804 = 135; 	if (get_ival(PS4_LEFT) && get_ival(PS4_DOWN)) DEM804 = 225; 	if (event_press(PS4_LEFT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DEM804 = 180; 	if (event_press(PS4_RIGHT) && !get_ival(PS4_UP) && !get_ival(PS4_DOWN)) DEM804 = 1; 	if (event_press(PS4_UP) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DEM804 = 90; 	if (event_press(PS4_DOWN) && !get_ival(PS4_RIGHT) && !get_ival(PS4_LEFT)) DEM804 = 270; } int DEM805; int DEM557; int DEM804; combo DEM71 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait( DEM805); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 3800); 	DEM557 = !DEM557; } define DEM808 = 19; function DEM149(DEM150, DEM151) { 	if (DEM354 == DEM151) { 		if (event_press(PS4_RIGHT)) { 			DEM150 = clamp(DEM150 + 1, 0, DEM811[DEM354]); 			DEM359 = TRUE; 					} 		if (event_press(PS4_LEFT)) { 			DEM150 = clamp(DEM150 - 1, 0, DEM811[DEM354]); 			DEM359 = TRUE; 					} 		if (DEM354 == 0) { 			print(DEM214(DEM167(DEM815[DEM360]) ,OLED_FONT_SMALL_WIDTH),DEM808  ,OLED_FONT_SMALL , OLED_WHITE ,DEM815[DEM360]); 					} 		else if (DEM354 == 1) { 			print(DEM214(DEM167(DEM817[DEM361]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM817[DEM361]); 					} 		else if (DEM354 == 2) { 			print(DEM214(DEM167(DEM817[DEM362]) ,OLED_FONT_SMALL_WIDTH ),DEM808  ,OLED_FONT_SMALL , OLED_WHITE ,DEM817[DEM362]); 					} 		else if (DEM354 == 5) { 			print(DEM214(DEM167(DEM821[DEM365]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM821[DEM365]); 					} 		else if (DEM354 == 6) { 			print(DEM214(DEM167(DEM823[DEM366]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM366]); 					} 		else if (DEM354 == 7) { 			print(DEM214(DEM167(DEM823[DEM367]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM367]); 					} 		else if (DEM354 == 8) { 			print(DEM214(DEM167(DEM823[DEM368]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM368]); 					} 		else if (DEM354 == 9) { 			print(DEM214(DEM167(DEM823[DEM369]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM369]); 					} 		else if (DEM354 == 20) { 			print(DEM214(DEM167(DEM831[DEM114]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM831[DEM114]); 					} 		else { 			if (DEM150 == 1)        print(DEM214(DEM167(DEM833[1]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM833[1])      else        print(DEM214(DEM167(DEM833[0]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM833[0])     		} 			} 	return DEM150; 	} function DEM152(DEM150, DEM151) { 	if (DEM355 == DEM151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DEM150 += DEM839[DEM355][2]  				        DEM359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DEM150 -= DEM839[DEM355][2]  				        DEM359 = TRUE; 							} 			DEM150 = clamp(DEM150, DEM839[DEM355][0], DEM839[DEM355][1]); 		} 		if (DEM355 == 8) { 			print(DEM214(DEM167(DEM823[DEM386]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM386])     		} 		else if (DEM355 == 9) { 			print(DEM214(DEM167(DEM823[DEM387]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM387])     		} 		else if (DEM355 == 10) { 			print(DEM214(DEM167(DEM823[DEM388]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM388])     		} 		else if (DEM355 == 11) { 			print(DEM214(DEM167(DEM823[DEM389]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM389])     		} 		else if (DEM355 == 12) { 			print(DEM214(DEM167(DEM823[DEM390]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM390])     		} 		else if (DEM355 == 13) { 			print(DEM214(DEM167(DEM823[DEM391]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM391])     		} 		else if (DEM355 == 14) { 			print(DEM214(DEM167(DEM823[DEM392]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM392])     		} 		else if (DEM355 == 15) { 			print(DEM214(DEM167(DEM823[DEM393]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM393])     		} 		else if (DEM355 == 16) { 			print(DEM214(DEM167(DEM858[DEM394]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM394])     		} 		else if (DEM355 == 17) { 			print(DEM214(DEM167(DEM823[DEM275]),OLED_FONT_SMALL_WIDTH ),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM823[DEM275])  		} 		else if(DEM355 == 18){ 			print(DEM214(DEM167(DEM823[DEM276]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM823[DEM276])  		} 		else if(DEM355 == 19){ 			print(DEM214(DEM167(DEM823[DEM277]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM823[DEM277])  		} 		else if(DEM355 == 20){ 			print(DEM214(DEM167(DEM823[DEM278]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM823[DEM278])  		} 		else if(DEM355 == 21){ 			print(DEM214(DEM167(DEM858[DEM279]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM858[DEM279])       		} 		else if(DEM355 == 22){ 			print(DEM214(DEM167(DEM823[DEM407]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM407])     		} 		else if (DEM355 == 23) { 			print(DEM214(DEM167(DEM823[DEM408]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM408])     		} 		else if (DEM355 == 24) { 			print(DEM214(DEM167(DEM823[DEM409]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM409])     		} 		else if (DEM355 == 25) { 			print(DEM214(DEM167(DEM823[DEM410]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM410])     		} 		else if (DEM355 == 26) { 			print(DEM214(DEM167(DEM823[DEM411]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM823[DEM411])     		} 		else if (DEM355 == 27) { 			print(DEM214(DEM167(DEM858[DEM412]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM412])     		} 		else if (DEM355 == 28) { 			print(DEM214(DEM167(DEM882[DEM413]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM882[DEM413])     		} 		else if (DEM355 == 29) { 			print(DEM214(DEM167(DEM884[DEM414]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM884[DEM414])     		} 		else if (DEM355 == 30) { 			print(DEM214(DEM167(DEM858[DEM415]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM415])     		} 		else if (DEM355 == 31) { 			print(DEM214(DEM167(DEM882[DEM416]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM882[DEM416])     		} 		else if (DEM355 == 32) { 			print(DEM214(DEM167(DEM884[DEM417]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM884[DEM417])     		} 		else if (DEM355 == 33) { 			print(DEM214(DEM167(DEM858[DEM418]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM418])     		} 		else if (DEM355 == 34) { 			print(DEM214(DEM167(DEM882[DEM419]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM882[DEM419])     		} 		else if (DEM355 == 35) { 			print(DEM214(DEM167(DEM884[DEM420]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM884[DEM420])     		} 		else if (DEM355 == 36) { 			print(DEM214(DEM167(DEM858[DEM421]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM421])     		} 		else if (DEM355 == 37) { 			print(DEM214(DEM167(DEM882[DEM422]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM882[DEM422])     		} 		else if (DEM355 == 38) { 			print(DEM214(DEM167(DEM884[DEM423]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM884[DEM423])     		} 		else if (DEM355 == 41) { 			print(DEM214(DEM167(DEM858[DEM426]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM426])     		} 		else if (DEM355 == 48) { 			print(DEM214(DEM167(DEM858[DEM433]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM433])     		} 		else if (DEM355 == 49) { 			print(DEM214(DEM167(DEM858[DEM434]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM434])     		} 		else if (DEM355 == 50) { 			print(DEM214(DEM167(DEM858[DEM435]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM435])     		} 		else if (DEM355 == 51) { 			print(DEM214(DEM167(DEM858[DEM436]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM858[DEM436])     		} 		else if(DEM355 == 0){ 			print(DEM214(DEM167(DEM914[DEM439]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM439])  		} 		else if(DEM355 == 1){ 			print(DEM214(DEM167(DEM914[DEM440]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM440])  		} 		else if(DEM355 == 2){ 			print(DEM214(DEM167(DEM914[DEM441]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM441])  		} 		else if(DEM355 == 3){ 			print(DEM214(DEM167(DEM914[DEM442]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM442])  		} 		else if(DEM355 == 4){ 			print(DEM214(DEM167(DEM914[DEM443]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM443])  		} 		else if(DEM355 == 5){ 			print(DEM214(DEM167(DEM914[DEM444]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM444])  		} 		else if(DEM355 == 6){ 			print(DEM214(DEM167(DEM914[DEM445]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM445])  		} 		else if(DEM355 == 7){ 			print(DEM214(DEM167(DEM914[DEM446]),OLED_FONT_SMALL_WIDTH),DEM808,OLED_FONT_SMALL,OLED_WHITE,DEM914[DEM446])  		} 		else{ 			if (DEM150 == 1)        print(DEM214(DEM167(DEM833[1]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM833[1])      else        print(DEM214(DEM167(DEM833[0]), OLED_FONT_SMALL_WIDTH), DEM808, OLED_FONT_SMALL, OLED_WHITE, DEM833[0])     		} 		DEM170(0); 			} 	return DEM150; 	} function DEM155(DEM150, DEM151) { 	if (DEM355 == DEM151) { 		if (get_ival(PS4_L2)) { 			if (event_press(PS4_RIGHT)) { 				DEM150 += DEM839[DEM355][2]  				        DEM359 = TRUE; 							} 			if (event_press(PS4_LEFT)) { 				DEM150 -= DEM839[DEM355][2]  				        DEM359 = TRUE; 							} 			if (event_press(PS4_UP)) { 				DEM150 += DEM839[DEM355][3]  				        DEM359 = TRUE; 							} 			if (event_press(PS4_DOWN)) { 				DEM150 -= DEM839[DEM355][3]  				        DEM359 = TRUE; 							} 			DEM150 = clamp(DEM150, DEM839[DEM355][0], DEM839[DEM355][1]); 		} 		DEM217(DEM150, DEM220(DEM150)); 	} 	return DEM150; 	} int DEM941, DEM942, DEM943; function DEM158(DEM128, DEM160, DEM161, DEM162, DEM137) { 	DEM942 = 1; 	DEM943 = 10000; 	if (DEM128 < 0)  	  { 		putc_oled(DEM942, 45); 		DEM942 += 1; 		DEM128 = abs(DEM128); 			} 	for (DEM941 = 5; 	DEM941 >= 1; 	DEM941--) { 		if (DEM160 >= DEM941) { 			putc_oled(DEM942, DEM949[DEM128 / DEM943]); 			DEM128 = DEM128 % DEM943; 			DEM942 += 1; 					} 		DEM943 /= 10; 			} 	puts_oled(DEM161, DEM162, DEM137, DEM942 - 1, OLED_WHITE); } const string DEM574 = " No Edit Variable"; const string DEM573 = " A/CROSS to Edit "; const string DEM569 = "MOD;"; const string DEM571 = "MSL;"; int DEM953; function DEM164(DEM147) { 	DEM147 = abs(DEM147); 	if (DEM147 / 10000 > 0) return 5; 	if (DEM147 / 1000 > 0) return 4; 	if (DEM147 / 100 > 0) return 3; 	if (DEM147 / 10 > 0) return 2; 	return 1; 	} const int8 DEM949[] =     { 	48,    49,    50,    51,    52,    53,    54,    55,    56,    57   } ; int DEM955, DEM956; const image DEM958 = { 	98, 64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x02, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x40, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x80, 0x08, 0x00, 0x08, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x01, 0x00, 0x0E, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x00, 0x3F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x30, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x08, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x07, 0x06, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC1, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x30, 0x70, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x1C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x60, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x18, 0x7F, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3F, 0xE0, 0x3C, 0x3F, 0xFF, 0x06, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x01, 0x8F, 0xFC, 0x1E, 0x07, 0xFF, 0x81, 0xC0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x67, 0xFF, 0x9E, 0x00, 0xFF, 0xC0, 0x3C, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x78, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0C, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0x80, 0x00, 0x03, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xFE, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0x80, 0x00, 0x04, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3F, 0xE0, 0x00, 0x01, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF9, 0x80, 0x00, 0x10, 0x00, 0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x00, 0x07, 0x80, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 0x07, 0xFC, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0xFF, 0xFF, 0xC7, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x3F, 0xFF, 0xFD, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0xFF, 0xE0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1F, 0xFF, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x07, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x01, 0xFF, 0xF8, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x7F, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xB7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF } ; combo DEM72 { 	call(DEM73); 	DEM166(); 	vm_tctrl(0);wait( 2400); 	cls_oled(0); 	image_oled(0, 0, TRUE, TRUE, DEM958[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 500)cls_oled(1)image_oled(0, 0, 0, 1, DEM958[0]); 	vm_tctrl(0);wait( get_rtime()); 	vm_tctrl(0);wait( 1000)call(DEM74); 	vm_tctrl(0);wait( 1000); 	DEM356 = TRUE; 	} combo DEM73 { 	cls_oled(OLED_BLACK); 	} int DEM960; enum { 	DEM961 = -2, DEM962, DEM963 = 5, DEM964 = -1, DEM965 = 5  } data(32,68, 97, 114 ,107 ,32, 65, 110, 103, 101, 108,32,0); combo DEM74 { 	vm_tctrl(0);wait(360); 	print(1,18,OLED_FONT_MEDIUM,OLED_WHITE,0) 	set_rumble(RUMBLE_A, 50); 	vm_tctrl(0);wait( 200); 	set_rumble(RUMBLE_A, 50); 	set_rumble(RUMBLE_B, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} const int16 DEM1388[] = { 	-0, 6, 11, 17, 21, 26, 30, 33, 35, 36, 37, 36, 34, 31, 27, 22, 16, 8,0, -9, -19, -30, -42, -54, -67, -79, -92, -105, -118, -130, -142, -153, -163, -172, -180, -187,-193, -196, -199, -199, -198, -195, -190, -183, -174, -164, -152, -137, -122, -105, -86, -66, -45, -23,-0, 23, 47, 71, 95, 119, 142, 164, 186, 206, 226, 243, 259, 273, 285, 295, 303, 309,312, 312, 310, 306, 299, 289, 278, 263, 247, 229, 209, 187, 163, 138, 112, 85, 57, 29,0, -29, -57, -85, -112, -138, -163, -187, -209, -229, -247, -263, -278, -289, -299, -306, -310, -312,-312, -309, -303, -295, -285, -273, -259, -243, -226, -206, -186, -164, -142, -119, -95, -71, -47, -23,-0, 23, 45, 66, 86, 105, 122, 137, 152, 164, 174, 183, 190, 195, 198, 199, 199, 196,193, 187, 180, 172, 163, 153, 142, 130, 118, 105, 92, 79, 67, 54, 42, 30, 19, 9,0, -8, -16, -22, -27, -31, -34, -36, -37, -36, -35, -33, -30, -26, -21, -17, -11, -6 } const int16 DEM1389[] = { 	0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328,328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6 } const int16 DEM1390[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,-0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } const int16 DEM1391[] = { 	328, 328, 327, 327, 327, 326, 326, 325, 324, 324, 323, 322, 321, 319, 318, 317, 315, 313,312, 310, 308, 306, 304, 302, 299, 297, 295, 292, 289, 287, 284, 281, 278, 275, 272, 268,265, 262, 258, 255, 251, 247, 244, 240, 236, 232, 228, 223, 219, 215, 211, 206, 202, 197,193, 188, 183, 178, 174, 169, 164, 159, 154, 149, 144, 138, 133, 128, 123, 117, 112, 107,101, 96, 90, 85, 79, 74, 68, 63, 57, 51, 46, 40, 34, 29, 23, 17, 11, 6,0, -6, -11, -17, -23, -29, -34, -40, -46, -51, -57, -63, -68, -74, -79, -85, -90, -96,-101, -107, -112, -117, -123, -128, -133, -138, -144, -149, -154, -159, -164, -169, -174, -178, -183, -188,-193, -197, -202, -206, -211, -215, -219, -223, -228, -232, -236, -240, -244, -247, -251, -255, -258, -262,-265, -268, -272, -275, -278, -281, -284, -287, -289, -292, -295, -297, -299, -302, -304, -306, -308, -310,-312, -313, -315, -317, -318, -319, -321, -322, -323, -324, -324, -325, -326, -326, -327, -327, -327, -328,-328, -328, -327, -327, -327, -326, -326, -325, -324, -324, -323, -322, -321, -319, -318, -317, -315, -313,-312, -310, -308, -306, -304, -302, -299, -297, -295, -292, -289, -287, -284, -281, -278, -275, -272, -268,-265, -262, -258, -255, -251, -247, -244, -240, -236, -232, -228, -223, -219, -215, -211, -206, -202, -197,-193, -188, -183, -178, -174, -169, -164, -159, -154, -149, -144, -138, -133, -128, -123, -117, -112, -107,-101, -96, -90, -85, -79, -74, -68, -63, -57, -51, -46, -40, -34, -29, -23, -17, -11, -6,0, 6, 11, 17, 23, 29, 34, 40, 46, 51, 57, 63, 68, 74, 79, 85, 90, 96,101, 107, 112, 117, 123, 128, 133, 138, 144, 149, 154, 159, 164, 169, 174, 178, 183, 188,193, 197, 202, 206, 211, 215, 219, 223, 228, 232, 236, 240, 244, 247, 251, 255, 258, 262,265, 268, 272, 275, 278, 281, 284, 287, 289, 292, 295, 297, 299, 302, 304, 306, 308, 310,312, 313, 315, 317, 318, 319, 321, 322, 323, 324, 324, 325, 326, 326, 327, 327, 327, 328 } int DEM966; int DEM967; int DEM968; int DEM969; int DEM970; int DEM971; int DEM972; function DEM166() { 	DEM972 = 3; 	DEM970 = DEM972 * DEM1391[DEM971]; 	DEM969 = DEM972 * DEM1388[DEM971]; 	DEM967 = ((DEM970 * DEM1390[DEM966]) / 328) - ((DEM969 * DEM1389[DEM966]) / 328); 	DEM968 = ((DEM970 * DEM1389[DEM966]) / 328) + ((DEM969 * DEM1390[DEM966]) / 328); 	DEM970 = DEM967; 	DEM969 = DEM968; 	DEM971 += 1; 	DEM966 += 45; 	if(DEM971 >= 360) { 		DEM971 %= 360; 			} 	if(DEM966 >= 360) { 		DEM966 %= 360; 			} 	pixel_oled(64 + (((DEM970 / DEM972) * 30) / 328), 32 + (((DEM969 / DEM972) * 30) / 328), OLED_WHITE); 	} int DEM976; function DEM167(DEM141) { 	DEM976 = 0; 	do { 		DEM141++; 		DEM976++; 			} 	while (duint8(DEM141)); 	return DEM976; 	} int DEM979; const uint8 DEM1392[] = { 	PS4_OPTIONS,  PS4_LEFT,  PS4_RIGHT,  PS4_UP,  PS4_DOWN,  PS4_CROSS,  PS4_CIRCLE,  PS4_SQUARE,  PS4_TRIANGLE,  PS4_R3,  PS4_L3,  PS4_RX,  PS4_RY,  PS4_PS,  PS4_TOUCH,  PS4_SHARE } ; function DEM169() { 	for (DEM979 = 0; 	DEM979 < sizeof(DEM1392) / sizeof(DEM1392[0]); 	DEM979++) { 		if (get_ival(DEM1392[DEM979]) || event_press(DEM1392[DEM979])) { 			set_val(DEM1392[DEM979], 0); 		} 			} 	} define DEM980 = 131; define DEM981 = 132; define DEM982 = 133; define DEM983 = 134; define DEM984 = 130; define DEM985 = 89; define DEM986 = 127; define DEM987 = 65; int DEM988; int DEM989; int DEM990 = 1; define DEM991 = 36; const string DEM992 = "Hold LT/L2 +"; function DEM170(DEM171) { 	line_oled(1, 48, 127, 48, 1, 1); 	print(2, 52, OLED_FONT_SMALL, 1, DEM992[0]); 	rect_oled(90, 50, 127, 60, OLED_WHITE, DEM990); 	putc_oled(1, DEM982); 	puts_oled(91, 51, OLED_FONT_SMALL, 1, DEM988); 	putc_oled(1, DEM983); 	puts_oled(101, 51, OLED_FONT_SMALL, 1, DEM989); 	if (DEM171) { 		putc_oled(1, DEM980); 		puts_oled(111, 51, OLED_FONT_SMALL, 1, DEM988); 		putc_oled(1, DEM981); 		puts_oled(121, 51, OLED_FONT_SMALL, 1, DEM989); 			} 	} const uint8 DEM1394 [] = { 	  PS4_R1,        	  PS4_R2,        	  PS4_R3,        	  PS4_L1,        	  PS4_L2,        	  PS4_L3,        	  PS4_TRIANGLE,  	  PS4_CIRCLE,    	  PS4_CROSS,     	  PS4_SQUARE     } ; function DEM172() { 	DEM1002 = sizeof(data); 	DEM508 = get_pvar(SPVAR_1,0,1,0); 	DEM514 = get_pvar(SPVAR_2,0,1,0); 	DEM507 = get_pvar(SPVAR_3,11111, 99999,11111); 	DEM174(); 	if (DEM199(0, 1, 0)) { 		DEM365 = DEM199(  0, 6, 0); 		DEM362 = DEM199(0, 3, 0); 		DEM363 = DEM199(0,1,0); 		DEM364 = DEM199(0,1,0); 		DEM275 = DEM199(0, 70, 0); 		DEM276 = DEM199(0, 70, 0); 		DEM277 = DEM199(0, 70, 0); 		DEM278 = DEM199(0, 70, 0); 		DEM279 = DEM199(0, 22, 8); 		DEM366 = DEM199(0, 70, 0); 		DEM367 = DEM199(0, 70, 0); 		DEM368 = DEM199(0, 70, 0); 		DEM369 = DEM199(0, 70, 0); 		DEM370 = DEM199(0, 1, 0); 		DEM371 = DEM199(0, 1, 0); 		DEM372 = DEM199(0, 1, 0); 		DEM373 = DEM199(0, 1, 0); 		DEM381 = DEM199(0, 1, 0); 		DEM407 = DEM199(0, 70, 0); 		DEM408 = DEM199(0, 70, 0); 		DEM409 = DEM199(0, 70, 0); 		DEM410 = DEM199(0, 70, 0); 		DEM411 = DEM199(0, 70, 0); 		DEM412 = DEM199(1, 25, 1); 		DEM413 = DEM199(0, 1, 0); 		DEM414 = DEM199(0, 1, 0); 		DEM415 = DEM199(1, 25, 5); 		DEM416 = DEM199(0, 1, 0); 		DEM417 = DEM199(0, 1, 0); 		DEM418 = DEM199(0, 25, 2); 		DEM419 = DEM199(0, 1, 0); 		DEM420 = DEM199(0, 1, 1); 		DEM421 = DEM199(1, 25, 8); 		DEM422 = DEM199(0, 1, 0); 		DEM423 = DEM199(0, 1, 1); 		DEM424 = DEM199(350, 600, 350); 		DEM425 = DEM199(350, 600, 445); 		DEM426 = DEM199(0, 22, 0); 		DEM427 = DEM199(0, 1, 0); 		DEM428 = DEM199(-100, 300, 0); 		DEM374 = DEM199(0, 1, 0); 		DEM375 = DEM199(0, 1, 0); 		DEM376 = DEM199(0, 1, 0); 		DEM377 = DEM199(0, 1, 0); 		DEM378 = DEM199(0, 1, 0); 		DEM429 = DEM199(-150, 150, 0); 		DEM430 = DEM199(-150, 150, 0); 		DEM431 = DEM199(0, 1, 0); 		DEM432 = DEM199(-150, 150, 0); 		DEM433 = DEM199(0, 22, 0); 		DEM434 = DEM199(0, 22, 0); 		DEM435 = DEM199(0, 22, 0); 		DEM436 = DEM199(0, 22, 0); 		DEM608 = DEM199(60, 400, 235); 		DEM438 = DEM199(0, 1, 0); 		DEM437 = DEM199(0, 1, 0); 		DEM361 = DEM199(0, 3, 0); 		DEM386 = DEM199(0, 70, 0); 		DEM387 = DEM199(0, 70, 0); 		DEM388 = DEM199(0, 70, 0); 		DEM391 = DEM199(0, 70, 0); 		DEM392 = DEM199(0, 70, 0); 		DEM393 = DEM199(0, 70, 0); 		DEM394 = DEM199(0, 22, 8); 		DEM379 = DEM199(0, 1, 0); 		DEM389 = DEM199(0, 70, 0); 		DEM390 = DEM199(0, 70, 0); 		DEM600 = DEM199(0, 2500, 1100); 		DEM1253 = DEM199(0, 1, 0); 		DEM1246 = DEM199(0, 1, 0); 		DEM114 = DEM199(0, 10, 0); 		DEM406 = DEM199(0, 1, 0); 		DEM360 = DEM199(0, 2, 0); 		DEM439 = DEM199(0, 9, 9); 		DEM440 = DEM199(0, 9, 8); 		DEM441 = DEM199(0, 9, 3); 		DEM442 = DEM199(0, 9, 1); 		DEM443 = DEM199(0, 9, 4); 		DEM444 = DEM199(0, 9, 0); 		DEM445 = DEM199(0, 9, 7); 		DEM446 = DEM199(0, 9, 6); 		DEM382    = DEM199(0, 1, 0); 		DEM383    = DEM199(0, 1, 0); 		DEM384     = DEM199(0, 1, 0); 		DEM395     = DEM199(60, 500, 120); 		DEM396     = DEM199(60, 500, 350); 		DEM397    = DEM199(0, 1, 0); 		DEM398 = DEM199(0, 1, 0); 		DEM399     = DEM199(50, 250, 80); 		DEM400     = DEM199(100, 850, 180); 		DEM401 = DEM199(0, 1, 0); 		DEM402    = DEM199(0, 1, 0); 		DEM403        = DEM199(80, 500, 120); 		DEM404        = DEM199(80, 500, 350); 		DEM405       = DEM199(0, 1, 0); 		DEM455           = DEM199(3, 60, 7); 		DEM29           = DEM199(0, 1, 0); 		DEM476         = DEM199(0, 1, 0); 		DEM474       = DEM199(0, 1, 0); 	} 	else{ 		DEM365 = 0; 		DEM362 = 0; 		DEM363 = 0; 		DEM364 = 0; 		DEM275 = 0; 		DEM276 = 0; 		DEM277 = 0; 		DEM278 = 0; 		DEM279 = 8; 		DEM366 = 0; 		DEM367 = 0; 		DEM368 = 0; 		DEM369 = 0; 		DEM370 = 0; 		DEM371 = 0; 		DEM372 = 0; 		DEM373 = 0; 		DEM381 = 0; 		DEM407 = 0; 		DEM408 = 0; 		DEM409 = 0; 		DEM410 = 0; 		DEM411 = 0; 		DEM412 = 1; 		DEM413 = 0; 		DEM414 = 0; 		DEM415 = 5; 		DEM416 = 0; 		DEM417 = 0; 		DEM418 = 2; 		DEM419 = 0; 		DEM420 = 1; 		DEM421 = 8; 		DEM422 = 0; 		DEM423 = 1; 		DEM424 = 350; 		DEM425 = 445; 		DEM426 = 0; 		DEM427 = 0; 		DEM428 = 0; 		DEM374 = 0; 		DEM375 = 0; 		DEM376 = 0; 		DEM377 = 0; 		DEM378 = 0; 		DEM429 = 0; 		DEM430 = 0; 		DEM431 = 0; 		DEM432 = 0; 		DEM433 = 0; 		DEM434 = 0; 		DEM435 = 0; 		DEM436 = 0; 		DEM608 = 235; 		DEM438 = 0; 		DEM437 = 0; 		DEM361 = 0; 		DEM386 = 0; 		DEM387 = 0; 		DEM388 = 0; 		DEM391 = 0; 		DEM392 = 0; 		DEM393 = 0; 		DEM394 = 8; 		DEM379 = 0; 		DEM389 = 0; 		DEM390 = 0; 		DEM600 = 1100; 		DEM1253 = 0; 		DEM1246 = 0; 		DEM114 = 0; 		DEM406 = 0; 		DEM360 = 0; 		DEM439 = 9; 		DEM440 = 8; 		DEM441 = 3; 		DEM442 = 1; 		DEM443 = 4; 		DEM444 = 0; 		DEM445 = 7; 		DEM446 = 6; 		DEM382 = 0; 		DEM383 = 0; 		DEM384 = 0; 		DEM395 = 120; 		DEM396 = 350; 		DEM397 = 0; 		DEM398 = 0; 		DEM399 = 80; 		DEM400 = 180; 		DEM401 = 0; 		DEM402 = 0; 		DEM403 = 120; 		DEM404 = 360; 		DEM405 = 0; 		DEM455     = 7; 		DEM29     = 0; 		DEM476     = 0; 		DEM474     = 0; 			} 	if (DEM360 == 0) { 		DEM447 = PS4_CIRCLE; 		DEM448 = PS4_CROSS; 		DEM449 = PS4_L1; 		DEM450 = PS4_R1; 		DEM451 = PS4_L2; 		DEM452 = PS4_R2; 		DEM453 = PS4_SQUARE; 		DEM454 = PS4_TRIANGLE; 			} 	else if (DEM360 == 1) { 		DEM447      = PS4_SQUARE; 		DEM448      = PS4_CROSS ; 		DEM449    = PS4_L1    ; 		DEM450  = PS4_R1; 		DEM451    = PS4_L2; 		DEM452    = PS4_R2; 		DEM453     = PS4_CIRCLE; 		DEM454  = PS4_TRIANGLE; 	} 	else if (DEM360 == 2) { 		DEM447 = DEM1394[DEM439]; 		DEM448 = DEM1394[DEM440]; 		DEM449 = DEM1394[DEM441]; 		DEM450 = DEM1394[DEM442]; 		DEM451 = DEM1394[DEM443]; 		DEM452 = DEM1394[DEM444]; 		DEM453 = DEM1394[DEM445]; 		DEM454 = DEM1394[DEM446]; 			} 	} function DEM173() { 	DEM174(); 	DEM197(   1,0,     1); 	DEM197(DEM365, 0, 6); 	DEM197(DEM362, 0, 3); 	DEM197(DEM363, 0 , 1); 	DEM197(DEM364, 0 , 1); 	DEM197(DEM275, 0, 70); 	DEM197(DEM276, 0, 70); 	DEM197(DEM277, 0, 70); 	DEM197(DEM278, 0, 70); 	DEM197(DEM279, 0, 22); 	DEM197(DEM366, 0, 70); 	DEM197(DEM367, 0, 70); 	DEM197(DEM368, 0, 70); 	DEM197(DEM369, 0, 70); 	DEM197(DEM370, 0, 1); 	DEM197(DEM371, 0, 1); 	DEM197(DEM372, 0, 1); 	DEM197(DEM373, 0, 1); 	DEM197(DEM381, 0, 1); 	DEM197(DEM407, 0, 70); 	DEM197(DEM408, 0, 70); 	DEM197(DEM409, 0, 70); 	DEM197(DEM410, 0, 70); 	DEM197(DEM411, 0, 70); 	DEM197(DEM412, 1, 25); 	DEM197(DEM413, 0, 1); 	DEM197(DEM414, 0, 1); 	DEM197(DEM415, 1, 25); 	DEM197(DEM416, 0, 1); 	DEM197(DEM417, 0, 1); 	DEM197(DEM418, 0, 25); 	DEM197(DEM419, 0, 1); 	DEM197(DEM420, 0, 1); 	DEM197(DEM421, 1, 25); 	DEM197(DEM422, 0, 1); 	DEM197(DEM423, 0, 1); 	DEM197(DEM424, 350, 600); 	DEM197(DEM425, 350, 600); 	DEM197(DEM426, 0, 22); 	DEM197(DEM427, 0, 1); 	DEM197(DEM428, -100, 300); 	DEM197(DEM374, 0, 1); 	DEM197(DEM375, 0, 1); 	DEM197(DEM376, 0, 1); 	DEM197(DEM377, 0, 1); 	DEM197(DEM378, 0, 1); 	DEM197(DEM429, -150, 150); 	DEM197(DEM430, -150, 150); 	DEM197(DEM431, 0, 1); 	DEM197(DEM432, -150, 150); 	DEM197(DEM433, 0, 22); 	DEM197(DEM434, 0, 22); 	DEM197(DEM435, 0, 22); 	DEM197(DEM436, 0, 22); 	DEM197(DEM608, 60, 400); 	DEM197(DEM438, 0, 1); 	DEM197(DEM437, 0, 1); 	DEM197(DEM361, 0, 3); 	DEM197(DEM386, 0, 70); 	DEM197(DEM387, 0, 70); 	DEM197(DEM388, 0, 70); 	DEM197(DEM391, 0, 70); 	DEM197(DEM392, 0, 70); 	DEM197(DEM393, 0, 70); 	DEM197(DEM394, 0, 22); 	DEM197(DEM379, 0, 1); 	DEM197(DEM389, 0, 70); 	DEM197(DEM390, 0, 70); 	DEM197(DEM600, 0, 2500); 	DEM197(DEM1253, 0, 1); 	DEM197(DEM1246, 0, 1); 	DEM197(DEM114, 0, 10); 	DEM197(DEM406, 0, 1); 	DEM197(DEM360, 0, 2); 	DEM197(DEM439, 0, 9); 	DEM197(DEM440, 0, 9); 	DEM197(DEM441, 0, 9); 	DEM197(DEM442, 0, 9); 	DEM197(DEM443, 0, 9); 	DEM197(DEM444, 0, 9); 	DEM197(DEM445, 0, 9); 	DEM197(DEM446, 0, 9); 	DEM197(DEM382,    0, 1); 	DEM197(DEM383,    0, 1); 	DEM197(DEM384,     0, 1); 	DEM197(DEM395,     60, 500); 	DEM197(DEM396,     60, 500); 	DEM197(DEM397,    0, 1); 	DEM197(DEM398, 0, 1); 	DEM197(DEM399,     50, 250); 	DEM197(DEM400,     100, 850); 	DEM197(DEM401, 0, 1); 	DEM197(DEM402,    0, 1); 	DEM197(DEM403,        80, 500); 	DEM197(DEM404,        80, 500); 	DEM197(DEM405,       0, 1); 	DEM197(DEM455 ,         3,60); 	DEM197(DEM29,           0,1); 	DEM197(DEM476,           0,1); 	DEM197(DEM474,           0,1); 	} function DEM174() { 	DEM1009 = SPVAR_4; 	DEM1010 = 0; 	DEM1012 = 0; 	} int DEM1010,  DEM1009, DEM1012, DEM1013, DEM1014; function DEM175(DEM176) { 	DEM1013 = 0; 	while (DEM176) { 		DEM1013++; 		DEM176 = abs(DEM176 >> 1); 	} 	return DEM1013; 	} function DEM177(DEM178, DEM179) { 	DEM1013 = max(DEM175(DEM178), DEM175(DEM179)); 	if (DEM180(DEM178, DEM179)) { 		DEM1013++; 	} 	return DEM1013; 	} function DEM180(DEM178, DEM179) { 	return DEM178 < 0 || DEM179 < 0; 	} function DEM183(DEM184) { 	return 1 << clamp(DEM184 - 1, 0, 31); 	} function DEM185(DEM184) { 	if (DEM184 == 32) { 		return -1; 			} 	return 0x7FFFFFFF >> (31 - DEM184); } function DEM187(DEM184) { 	return DEM185(DEM184 - 1); 	} function DEM189(DEM176, DEM184) { 	if (DEM176 < 0) { 		return (abs(DEM176) & DEM187(DEM184)) | DEM183(DEM184); 	} 	return DEM176 & DEM187(DEM184); } function DEM192(DEM176, DEM184) { 	if (DEM176 & DEM183(DEM184)) { 		return 0 - (DEM176 & DEM187(DEM184)); 	} 	return DEM176 & DEM187(DEM184); } function DEM195(DEM196) { 	return get_pvar(DEM196, 0x80000000, 0x7FFFFFFF, 0); 	} function DEM197(DEM176, min, max) { 	DEM1014 = DEM177(min, max); 	DEM176 = clamp(DEM176, min, max); 	if (DEM180(min, max)) { 		DEM176 = DEM189(DEM176, DEM1014); 	} 	DEM176 = DEM176 & DEM185(DEM1014); 	if (DEM1014 >= 32 - DEM1010) { 		DEM1012 = DEM1012 | (DEM176 << DEM1010); 		set_pvar(DEM1009, DEM1012); 		DEM1009++; 		DEM1014 -= (32 - DEM1010); 		DEM176 = DEM176 >> (32 - DEM1010); 		DEM1010 = 0; 		DEM1012 = 0; 	} 	DEM1012 = DEM1012 | (DEM176 << DEM1010); 	DEM1010 += DEM1014; 	if (!DEM1010) { 		DEM1012 = 0; 	} 	set_pvar(DEM1009, DEM1012); } function DEM199(min, max, DEM200) { 	DEM1014 = DEM177(min, max); 	DEM1012 = (DEM195(DEM1009) >> DEM1010) & DEM185(DEM1014); 	if (DEM1014 >= 32 - DEM1010) { 		DEM1012 = (DEM1012 & DEM185(32 - DEM1010)) | ((DEM195(DEM1009 + 1) & DEM185(DEM1014 - (32 - DEM1010))) << (32 - DEM1010)); 	} 	DEM1010 += DEM1014; 	DEM1012 = DEM1012 & DEM185(DEM1014); 	if (DEM1010 >= 32) { 		DEM1009++; 		DEM1010 -= 32; 	} 	if (DEM180(min, max)) { 		DEM1012 = DEM192(DEM1012, DEM1014); 	} 	if (DEM1012 < min || DEM1012 > max) { 		return DEM200; 	} 		if(DEM202[289] != 7591){     DEM199(min, max, DEM200); 	} 	return DEM1012; 	} const string DEM1040 = "SETTINGS"; const string DEM1041 = "WAS SAVED"; combo DEM75 { 	vm_tctrl(0);wait( 20); 	cls_oled(0); 	DEM173(); 	print(15, 2, OLED_FONT_MEDIUM, 1, DEM1040[0]); 	print(10, 23, OLED_FONT_MEDIUM, 1, DEM1041[0]); 	DEM1042 = 1500; 	combo_run(DEM76); 	} int DEM1042 = 1500; combo DEM76 { 	vm_tctrl(0);wait( DEM1042); 	cls_oled(0); 	DEM358 = FALSE; 	} define DEM1043 = 0; define DEM1044 = 1; define DEM1045 = 2; define DEM1046 = 3; define DEM1047 = 4; define DEM1048 = 5; define DEM1049 = 6; define DEM1050 = 7; int DEM687; int DEM1052; int DEM1053, DEM669; int DEM479; int DEM1056 = 49; int DEM1057 = 200; int DEM718 = TRUE; combo DEM77 { 	set_val(DEM448, 0); 	set_val(PS4_L3, 100); 	set_val(PS4_R3, 100); 	vm_tctrl(0);wait( 60); 	set_val(DEM448, 0); 	vm_tctrl(0);wait( 120); 	if (DEM427) DEM245(); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 50); 	} int DEM615; int DEM622; combo DEM78 { 	if (DEM622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, -100); 	vm_tctrl(0);wait( 70); 	if (DEM622) set_val(XB1_RX, 100); 	else set_val(XB1_RX, -100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 2000); 	if (DEM622) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 200); 	set_val(DEM447, 100); 	vm_tctrl(0);wait( DEM424); 	if (DEM622) set_val(XB1_LX, 100); 	else set_val(XB1_LX, 100); 	set_val(XB1_LY,100); 	vm_tctrl(0);wait( 50); 	vm_tctrl(0);wait( 1200); 	DEM615 = FALSE; 	DEM229(DEM615); 	} int DEM624; int DEM625; combo DEM79 { 	if (DEM625) set_val(XB1_RX, -100); 	else set_val(XB1_RX, 100); 	set_val(XB1_RY, 100); 	vm_tctrl(0);wait( 320); 	vm_tctrl(0);wait( 50); 	set_val(XB1_RY, -60); 	vm_tctrl(0);wait( 1100); 	vm_tctrl(0);wait( 50); 	if (DEM625) set_val(XB1_LX, 60); 	else set_val(XB1_LX, -60); 	vm_tctrl(0);wait( 120); 	vm_tctrl(0);wait( 50); 	set_val(XB1_LY, -100); 	set_val(DEM453, 100); 	set_val(DEM450, 100); 	set_val(DEM451, 100); 	DEM1200 = 4000; 	vm_tctrl(0);wait( DEM425); 	vm_tctrl(0);wait( 50); 	set_val(DEM453, 100); 	vm_tctrl(0);wait( 50); 	DEM624 = FALSE; 	DEM229(DEM624); 	} int DEM1063 = TRUE; function DEM201(DEM202) { 	if (DEM202) { 		DEM1064 = DEM1096; 			} 	else { 		DEM1064 = DEM1095; 			} 	combo_run(DEM80); 	} int DEM1064; combo DEM80 { 	DEM222(DEM1064); 	vm_tctrl(0);wait( 300); 	DEM222(DEM1093); 	vm_tctrl(0);wait( 100); 	DEM222(DEM1064); 	vm_tctrl(0);wait( 300); 	DEM222(DEM1093); 	} define DEM1068 = 100; define DEM1069 = 130; const string DEM552 = "SCRIPT WAS"; function DEM203(DEM128, DEM205, DEM206) { 	if (!DEM352 && !DEM353) { 		cls_oled(0); 		print(DEM205, 3, OLED_FONT_MEDIUM, OLED_WHITE, DEM206); 		if (DEM128) { 			print(DEM207(sizeof(DEM1073) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DEM1073[0]); 		} 		else { 			print(DEM207(sizeof(DEM1074) - 1, OLED_FONT_LARGE_WIDTH), 37, OLED_FONT_LARGE, OLED_WHITE, DEM1074[0]); 		} 		DEM201(DEM128); 			} 	} function DEM207(DEM143, DEM137) { 	return (OLED_WIDTH / 2) - ((DEM143 * DEM137) / 2); 	} const string DEM1074 = "OFF"; const string DEM1073 = "ON"; function DEM210(DEM134, DEM212, DEM128) { 	cls_oled(0); 	line_oled(1, 18, 127, 18, 1, 1); 	print(DEM134, 0, OLED_FONT_MEDIUM, OLED_WHITE, DEM212); 	DEM217(DEM128, DEM220(DEM128)); 	DEM356 = TRUE; 	} const string DEM595 = "EA PING"; const string DEM617 = "FK_POWER"; const string DEM609 = "MaxFnshPwr"const string DEM601 = "JK_Agg"; int DEM600; int DEM608; function DEM214(DEM143, DEM137) { 	return (OLED_WIDTH / 2) - ((DEM143 * DEM137) / 2); 	} int DEM1083; int DEM1084, DEM1085; function DEM217(DEM128, DEM160) { 	DEM1083 = 1; 	DEM1085 = 10000; 	if (DEM128 < 0) { 		putc_oled(DEM1083, 45); 		DEM1083 += 1; 		DEM128 = abs(DEM128); 			} 	for (DEM1084 = 5; 	DEM1084 >= 1; 	DEM1084--) { 		if (DEM160 >= DEM1084) { 			putc_oled(DEM1083, (DEM128 / DEM1085) + 48); 			DEM128 %= DEM1085; 			DEM1083++; 			if (DEM1084 == 4) { 				putc_oled(DEM1083, 44); 				DEM1083++; 							} 					} 		DEM1085 /= 10; 			} 	puts_oled(DEM214(DEM1083 - 1, OLED_FONT_MEDIUM_WIDTH), 38, OLED_FONT_MEDIUM, DEM1083 - 1, OLED_WHITE); 	} int DEM1089; function DEM220(DEM221) { 	DEM1089 = 0; 	do { 		DEM221 /= 10; 		DEM1089++; 			} 	while (DEM221); 	return DEM1089; 	} int DEM631; define DEM1093 = 0; define DEM1094 = 1; define DEM1095 = 2; define DEM1096 = 3; define DEM1097 = 4; define DEM1098 = 5; define DEM1099 = 6; define DEM1100 = 7; const int16 data[][] = { 	{ 		0,    0,    0   	} 	,  	  { 		0,    0,    255   	} 	,  	  { 		255,    0,    0   	} 	,  	  { 		0,    255,    0   	} 	,  	  { 		255,    0,    255   	} 	,  	  { 		0,    255,    255   	} 	,  	  { 		255,    255,    0   	} 	,  	  { 		255,    255,    255   	} } ; int DEM1101; function DEM222(DEM223) { 	for (DEM1101 = 0; 	DEM1101 < 3; 	DEM1101++) { 		set_rgb(data[DEM223][0], data[DEM223][1], data[DEM223][2]); 			} 	} const int8 DEM1404[] = { 	XB1_XBOX,  	  XB1_VIEW,  	  XB1_MENU,  	  PS4_R1,  	  PS4_R2,  	  XB1_RS,  	  PS4_L1,  	  PS4_L2,  	  XB1_LS,  	  PS4_UP,  	  PS4_DOWN,  	  PS4_LEFT,  	  PS4_RIGHT,  	  XB1_Y,  	  XB1_B,  	  XB1_A,  	  XB1_X,  	  XB1_PR1,  	  XB1_PR2,  	  XB1_PL1,  	  XB1_PL2,  	  PS4_TOUCH  } int DEM634 = PS4_L3; define DEM1103 = 1; define DEM1104 = 2; define DEM1105 = 3; define DEM1106 = 2; define DEM1107 = 3; define DEM1108 = 4; define DEM1109 = 5; define DEM1110 = 6; define DEM1111 = 7; define DEM1112 = 8; define DEM1113 = 9; int DEM628 = FALSE; int DEM1115; int DEM1116; int DEM1117; int DEM1118; define DEM1119 = PS4_LX; define DEM1120 = PS4_LY; define DEM1121 = PS4_RX; define DEM1122 = PS4_RY; function DEM224 () { if(DEM362 == 3){ 		if( get_ival(PS4_RY) < -70  && !DEM1115 && !combo_running(DEM0) ) { 			DEM1115 = TRUE; 			DEM479 = FALSE; 			DEM1052 = DEM275; 			            DEM227(DEM275); 		} 		if( get_ival(PS4_RY) >  70  && !DEM1116 && !combo_running(DEM0)) { 			DEM1116 = TRUE; 			DEM479 = TRUE; 			DEM1052 = DEM277; 			           DEM227(DEM277); 		} 		if( get_ival(PS4_RX) < -70  && !DEM1117 && !combo_running(DEM0) ) { 			DEM1117 = TRUE; 			DEM479 = FALSE; 			DEM1052 = DEM278; 			              DEM227(DEM278); 		} 		if( get_ival(PS4_RX) >  70  && !DEM1118 && !combo_running(DEM0) ) { 			DEM1118 = TRUE; 			DEM479 = TRUE; 			DEM1052 = DEM276; 			            DEM227(DEM276); 		} 			set_val(DEM1121,0);              set_val(DEM1122,0);  			} 	else if(DEM362 < 3 && !get_ival(XB1_RS) &&  !get_ival(DEM451) && !get_ival(DEM452) && !get_ival(DEM450)) { 		if( get_ival(PS4_RY) < -70  && !DEM1115 && !combo_running(DEM0) ) { 			DEM1115 = TRUE; 			DEM479 = FALSE; 			DEM1052 = DEM275; 			            DEM227(DEM275); 		} 		if( get_ival(PS4_RY) >  70  && !DEM1116 && !combo_running(DEM0)) { 			DEM1116 = TRUE; 			DEM479 = TRUE; 			DEM1052 = DEM277; 			           DEM227(DEM277); 		} 		if( get_ival(PS4_RX) < -70  && !DEM1117 && !combo_running(DEM0) ) { 			DEM1117 = TRUE; 			DEM479 = FALSE; 			DEM1052 = DEM278; 			              DEM227(DEM278); 		} 		if( get_ival(PS4_RX) >  70  && !DEM1118 && !combo_running(DEM0) ) { 			DEM1118 = TRUE; 			DEM479 = TRUE; 			DEM1052 = DEM276; 			            DEM227(DEM276); 		} 			set_val(DEM1121,0);              set_val(DEM1122,0);  			} 	if(abs(get_ival(PS4_RY))<20  && abs(get_ival(PS4_RX))<20){ 		DEM1115 = 0; 		DEM1116  = 0; 		DEM1117  = 0; 		DEM1118  = 0; 			} 	} function DEM225() { 	if (DEM502 == DEM593) { 		DEM479 = FALSE; 		if (DEM386) DEM227(DEM386); 			} 	if (DEM502 == DEM232(DEM593 + 4)) { 		DEM479 = FALSE; 		if (DEM393) DEM227(DEM393); 			} 	if (DEM502 == DEM232(DEM593 + 1)) { 		DEM479 = TRUE; 		if (DEM388) DEM227(DEM388); 			} 	if (DEM502 == DEM232(DEM593 - 1)) { 		DEM479 = FALSE; 		if (DEM387) DEM227(DEM387); 			} 	if (DEM502 == DEM232(DEM593 + 2)) { 		DEM479 = TRUE; 		if (DEM390) DEM227(DEM390); 			} 	if (DEM502 == DEM232(DEM593 - 2)) { 		DEM479 = FALSE; 		if (DEM389) DEM227(DEM389); 			} 	if (DEM502 == DEM232(DEM593 + 3)) { 		DEM479 = TRUE; 		if (DEM392) DEM227(DEM392); 			} 	if (DEM502 == DEM232(DEM593 - 3)) { 		DEM479 = FALSE; 		if (DEM391) DEM227(DEM391); 			} 	} int DEM1140; int DEM500 = 0; function DEM226() { 	if(DEM1140){ 		DEM500 += get_rtime(); 			} 	if(DEM500 >= 3000){ 		DEM500 = 0; 		DEM1140 = FALSE; 			} 	if(DEM361 == 3) { 			if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DEM503 && !combo_running(DEM0)) { 			DEM503 = TRUE; 			DEM1140 = TRUE; 			DEM500 = 0; 			DEM502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DEM225(); 					} 		set_val(DEM1121, 0); 		set_val(DEM1122, 0); 		} 	else if (!get_ival(XB1_RS) && !get_ival(DEM451) && !get_ival(DEM452) && !get_ival(DEM450) && !get_ival(DEM449)) { 		if ((get_polar(POLAR_RS,POLAR_RADIUS) > 4000) && !DEM503 && !combo_running(DEM0)) { 			DEM503 = TRUE; 			DEM1140 = TRUE; 			DEM500 = 0; 			DEM502 = ((((get_polar(POLAR_RS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8; 			DEM225(); 					} 		set_val(DEM1121, 0); 		set_val(DEM1122, 0); 			} 	if (get_polar(POLAR_RS,POLAR_RADIUS) < 4000) { 		DEM503 = FALSE; 			} 	} function DEM227(DEM228) { 	DEM1052 = DEM228; 	DEM202[-339 + (DEM228 * 3)] = TRUE; 	DEM718 = FALSE; 	block = TRUE; 	if (DEM114 > 7)vm_tctrl(0); 	} int DEM1148; combo DEM81 { 	set_rumble(DEM1148, 100); 	vm_tctrl(0);wait( 200); 	reset_rumble(); 	vm_tctrl(0);wait( 200); 	} function DEM229(DEM128) { 	if (DEM128) DEM1148 = RUMBLE_A; 	else DEM1148 = RUMBLE_B; 	combo_run(DEM81); 	} int DEM1149 = 300; int DEM1150 ; combo DEM82 { 	DEM1150 = TRUE; 	vm_tctrl(0);wait( DEM1149); 	DEM1150 = FALSE; 	} combo DEM83 { 	DEM231(); 	DEM239(0, 0); 	vm_tctrl(0);wait( 20); 	DEM239(0, 0); 	vm_tctrl(0);wait( 100); 	DEM239(0, 0); 	set_val(DEM452, 100); 	DEM239(0, 0); 	vm_tctrl(0);wait( 60); 	DEM239(0, 0); 	vm_tctrl(0);wait( 150); 	DEM718 = TRUE; 	vm_tctrl(0);wait( 350); 	} function DEM231() { 	DEM687 = DEM593  DEM234(DEM687); 	DEM1053 = DEM1154; 	DEM669 = DEM671; 	} combo DEM84 { 	set_val(DEM451, 100); 	set_val(DEM450, 100); 	vm_tctrl(0);wait( 100); 	set_val(DEM451, 100); 	vm_tctrl(0);wait( 100); 	DEM718 = TRUE; 	vm_tctrl(0);wait( 350); 	} const int8 DEM1405[][] = { { 		0,    -100   	} 	,  	  { 		70,    -70  	} 	,  	  { 		100,    0   	} 	,  	  { 		70,    70   	} 	,  	  { 		0,    100   	} 	,  	  { 		-70,    70   	} 	,  	  { 		-100,    0   	} 	,  	  { 		-70,    -70   	} } ; int DEM1154, DEM671, DEM593; int DEM502; int DEM503; int DEM1159; function DEM232(DEM233) { 	DEM1159 = DEM233; 	if (DEM1159 < 0) DEM1159 = 8 - abs(DEM233); 	else if (DEM1159 >= 8) DEM1159 = DEM233 - 8  return DEM1159; 	} function DEM234(DEM235) { 	if (DEM235 < 0) DEM235 = 8 - abs(DEM235); 	else if (DEM235 >= 8) DEM235 = DEM235 - 8; 	DEM1154 = DEM1405[DEM235][0]; 	DEM671 = DEM1405[DEM235][1]; } function DEM236(DEM237, DEM238) { 	set_val(DEM1121, DEM237); 	set_val(DEM1122, DEM238); 	} function DEM239(DEM240, DEM241) { 	set_val(DEM1119, DEM240); 	set_val(DEM1120, DEM241); 	} function DEM242() { 	if (DEM479) { 		set_val(DEM1119, inv(DEM669)); 		set_val(DEM1120, DEM1053); 			} 	else { 		set_val(DEM1119, DEM669); 		set_val(DEM1120, inv(DEM1053)); 			} 	} function DEM243() { 	if (DEM479) { 		set_val(DEM1121, inv(DEM669)); 		set_val(DEM1122, DEM1053); 			} 	else { 		set_val(DEM1121, DEM669); 		set_val(DEM1122, inv(DEM1053)); 			} 	} function DEM244() { 	if (!DEM479) { 		set_val(DEM1121, inv(DEM669)); 		set_val(DEM1122, DEM1053); 			} 	else { 		set_val(DEM1121, DEM669); 		set_val(DEM1122, inv(DEM1053)); 			} 	} function DEM245() { 	set_val(DEM1121, DEM1053); 	set_val(DEM1122, DEM669); 	} function DEM246() { 	set_val(DEM1121, inv(DEM1053)); 	set_val(DEM1122, inv(DEM669)); 	} function DEM247() { 	set_val(DEM1121, 0); 	set_val(DEM1122, 0); 	} int DEM1175; function DEM248() { 	if ((event_press(DEM448)  ) && !combo_running(DEM85) && (DEM1200  <= 0 || (DEM1200 < 3000 && DEM1200 > 1  )) && !get_ival(DEM452) && DEM555 > 500 &&!get_ival(DEM451) &&!get_ival(DEM447) &&!get_ival(DEM450) &&!get_ival(XB1_PR1) &&!get_ival(XB1_PR2) &&!get_ival(XB1_PL1) &&!get_ival(XB1_PL2) &&!(get_polar(POLAR_RS, POLAR_RADIUS) >= 1500)&& !combo_running(DEM85) ) { 		combo_run(DEM85); 			} 	if (combo_running(DEM85) && (        get_ival(DEM452) ||        get_ival(DEM451) ||        get_ival(DEM447) ||        get_ival(DEM450) ||        get_ival(XB1_PR1) ||        get_ival(XB1_PR2) ||        get_ival(XB1_PL1) ||        get_ival(XB1_PL2) ||        get_polar(POLAR_RS,POLAR_RADIUS) >= 1500      )) { 		combo_stop(DEM85); 		DEM1330 = TRUE; 			} 	} combo DEM85 { vm_tctrl(0);wait(750); set_val(DEM449,100); vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); if(DEM1175 == 1 ){ DEM236(100,inv(DEM669));} else{ DEM236(-100,inv(DEM669)); } vm_tctrl(0);wait(50); vm_tctrl(0);wait(50); 	} combo DEM86 { 	vm_tctrl(0);wait( 800); 	DEM1201 = 0; 	} int DEM1178 = 1000; int DEM1179 = 1600; int DEM1180 = 1600; int DEM1181 = TRUE; int DEM1182 = TRUE; int DEM664 = FALSE; int DEM1184 = TRUE; int DEM658 = FALSE; int DEM1186 = TRUE; int DEM660 = FALSE; int DEM1188 = TRUE; int DEM662 = FALSE; function DEM249(){ 	if (get_ival(DEM449)) { 		DEM1190 = 1000; 		DEM1209 = 0; 		DEM555 = 1; 		combo_stop(DEM95); 			} 	if (event_press(DEM451) || event_press(DEM434)) { 		DEM1190 = 4000; 		DEM1209 = 0; 		DEM1178 = 1600; 			} 	if (get_ival(DEM450) && !get_ival(DEM449) ) { 		DEM1190 = 0; 		DEM1209 = 0; 		DEM1178 = 1600; 			} 	else if (get_ival(DEM449)){ 		DEM1190 = 1000; 			} 	if (DEM1190 > 0) { 		DEM1190 -= get_rtime(); 			} 	if (DEM1190 < 0) { 		DEM1190 = 0; 			} 	DEM1269 = DEM428; 	if (event_release(DEM448)) { 		DEM1196 = 1; 		DEM1209 = 0; 		DEM555 = 1; 	} 	if (event_release(DEM454)) { 		DEM1197 = 1; 		DEM1209 = 0; 		DEM555 = 1; 	} 	if (event_release(DEM449)) { 		DEM1179 = 1; 		DEM1209 = 0; 		DEM1178 = 1600; 			} 	if (event_release(DEM450)) { 		DEM1180 = 1; 		DEM1209 = 0; 		DEM1178 = 1600; 			} 	if (event_release(DEM453) || (get_ival(DEM454) && get_ival(DEM449))) { 		DEM1200 = 4000; 		DEM1209 = 0; 	} 	if (get_ival(DEM448) && DEM1200 < 4000 && DEM1200 > 3500) { 		DEM1201 = DEM1200; 		DEM1200 = 0; 			} 	if (DEM1178 < 1510) { 		DEM1178 += get_rtime(); 			} 	if (DEM1179 < 1600) { 		DEM1179 += get_rtime(); 			} 	if (DEM1180 < 1600) { 		DEM1180 += get_rtime(); 			} 	if (DEM1200 > 0) { 		DEM1200 -= get_rtime(); 			} 	if (DEM1200 < 0) { 		DEM1200 = 0; 			} 	if (DEM1196 < 5100) { 		DEM1196 += get_rtime(); 			} 	if (DEM1197 < 4100) { 		DEM1197 += get_rtime(); 			} 	if (DEM1209 > 0) { 		DEM1209 -= get_rtime(); 			} 	if (DEM1209 < 0) { 		DEM1209 = 0; 			} 	if (abs(get_ival(PS4_RX)) > 30 || abs(get_ival(PS4_RY)) > 30) { 		DEM1178 = 1; 		DEM1209 = 0; 			} 	if (combo_running(DEM92)) { 		set_val(DEM448, 0); 		if(get_ival(DEM448)){ 			DEM1212 = 0; 			combo_stop(DEM87); 			set_val(DEM448, 0); 			combo_stop(DEM92); 			combo_run(DEM49); 					} 			} 	if ((combo_running(DEM97) || combo_running(DEM88))) { 		set_val(DEM448, 0); 		if(get_ival(DEM448)){ 			DEM555 = 1; 			DEM1212 = 0; 			combo_stop(DEM87); 			set_val(DEM448, 0); 			combo_stop(DEM97); 			combo_stop(DEM88); 			combo_run(DEM49); 					} 			} 	if (event_press(DEM447)) { 		combo_run(DEM86); 			} 	if (DEM555 > 1500) { 		if (DEM1179 < 1500) { 			DEM1214 = 120; 					} 		if (DEM1180 < 1500) { 			DEM1214 = 228; 					} 		else { 			DEM1214 = 200; 					} 			} 	if (DEM555 < 1500) { 		DEM1214 = 450; 			} 	if (DEM555 > 2700) { 		DEM1218 = 920; 			} 	else if (DEM555 >= 0 && DEM555 < 2700) { 		DEM1218 = 725; 			} 	} function DEM250() { 	if (DEM1181) { 		if ((DEM555 <= 600 || (DEM1178 <= 1500 && DEM1178 > 1) || ( DEM1179 <= 150 || DEM1180 <= 150)) && event_press(DEM447) ) { 			if (!get_ival(DEM450) && !get_ival(DEM449) && !get_ival(DEM451) && !get_ival(DEM452)) { 				set_val(DEM447, 0); 				if (DEM1200 < 4000 && DEM1200 > 1) { 					set_val(DEM447, 0); 					combo_run(DEM90); 									} 				else { 					set_val(DEM447, 0); 					combo_run(DEM88); 					DEM1209 = 9000; 				} 							} 					} 			} 	if (DEM1188) { 		if (DEM555 > 1000 && !DEM1209 && (!get_ival(DEM450) && !get_ival(PS4_L3) && event_press(DEM447)) &&  DEM1179 > 150 &&  DEM1180 > 150) { 			if (!get_ival(DEM449) && !get_ival(DEM451)) { 				set_val(DEM447, 0); 				if (((DEM1197 > 1 && DEM1197 <= 2500) || (DEM1196 > 1 && DEM1196 <= 3000)) &&  DEM1178 != 1600) { 					set_val(DEM447, 0); 					combo_run(DEM89); 					DEM1209 = 9000; 									} 				else if (((DEM1197 > 2500 && DEM1197 <= 4000) || (DEM1196 > 3000 && DEM1196 <= 3500))  &&  DEM1178 != 1600) { 					set_val(DEM447, 0); 					combo_run(DEM88); 					DEM1209 = 9000; 									} 				else if ((DEM1200 < 4000 && DEM1200 > 1)) { 					set_val(DEM447, 0); 					combo_run(DEM90); 					DEM1209 = 9000; 									} 				else { 					set_val(DEM447, 0); 					DEM254(); 					DEM1209 = 9000; 									} 				DEM1209 = 9000; 							} 					} 			} 	if (DEM1182) { 		if (get_ival(DEM449) && get_ival(DEM450)) { 			if (!get_ival(DEM451) && !get_ival(DEM452) && (DEM1200 && DEM1196 > 1 && DEM1196 <= 1500) || (!DEM1200 && DEM1196 > 1 && DEM1196 <= 1500) || (DEM1196 > 1500 && !DEM1200) && !DEM1209) { 				if (event_press(DEM447)) { 					set_val(DEM447, 0); 					combo_run(DEM98); 					DEM1209 = 9000; 									} 							} 					} 			} 	if (DEM1186) { 		if (!get_ival(DEM452) && !get_ival(DEM449) && !get_ival(DEM450)) { 			if (get_ival(DEM451) && get_ival(DEM447)) { 				DEM255(); 				set_val(DEM447, 0); 				DEM1209 = 9000; 							} 					} 			} 	if (DEM1184) { 		if (get_ival(DEM450) && !get_ival(DEM449) && !DEM1190) { 			if (!get_ival(DEM451) && !get_ival(DEM452) && !DEM1209) { 				if (get_ival(DEM447) && DEM555 >= 1000) { 					set_val(DEM447, 0); 					combo_run(DEM95); 					DEM1209 = 9000; 									} 				if (get_ival(DEM447) && DEM555 < 1000 && !DEM1190) { 					set_val(DEM447, 0); 					combo_run(DEM96); 									} 							} 					} 			} 	if(combo_running(DEM90)){ 		DEM1212 = 0; 		combo_stop(DEM87)   	} 	if (get_ival(DEM449) || DEM1190 > 0) { 		combo_stop(DEM95); 		combo_stop(DEM97); 		combo_stop(DEM96); 			} 	if (combo_running(DEM88) || combo_running(DEM92) || combo_running(DEM97) || combo_running(DEM98) || combo_running(DEM95)) { 		if (get_ival(DEM448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DEM452) > 30) { 			combo_stop(DEM92); 			combo_stop(DEM97); 			combo_stop(DEM98); 			combo_stop(DEM95); 			combo_stop(DEM88); 			DEM1212 = 0; 			combo_stop(DEM87)     		} 			} 	if (combo_running(DEM88) || combo_running(DEM89)) { 		if (get_ival(DEM448) || (get_ival(PS4_L2) && get_ival(PS4_R2)) || get_ival(DEM452)) { 			combo_stop(DEM90); 			combo_stop(DEM89); 			combo_stop(DEM88); 			DEM1212 = 0; 			combo_stop(DEM87)     		} 			} 	if (event_press(DEM447) && DEM1209 > 100 && DEM1209 < 8990) { 		set_val(DEM447, 0); 		combo_stop(DEM92); 		combo_stop(DEM97); 		combo_stop(DEM98); 		combo_stop(DEM95); 		combo_stop(DEM88); 		DEM1212 = 0; 		combo_stop(DEM87)    combo_run(DEM91); 			} 	if (!DEM664) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DEM98); 					} 			} 	if (!DEM658) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DEM95); 					} 			} 	if (!DEM660) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DEM92); 			DEM1212 = 0; 			combo_stop(DEM87)     		} 			} 	if (!DEM662) { 		if (get_ival(XB1_PL1) || get_ival(XB1_PL2) || get_ival(XB1_PR1) || get_ival(XB1_PR2) || get_ival(XB1_RS)) { 			combo_stop(DEM97); 					} 			} 	if ((get_ival(DEM452) || get_ival(DEM448)) && !DEM365) { 		combo_stop(DEM4); 		combo_stop(DEM47); 		combo_stop(DEM33); 			} 	} define DEM1222 = 15; define DEM1223 = 15; int DEM1224 = 0; define DEM1225 = 8000; define DEM1226 = 4; define DEM1227 = 2000; int DEM1212 = 0; const int16 DEM1406[] = { 	15, 16, 17 ,18,19    ,165,166 , 167, 168,169 ,    195, 196,197, 198,199,    340  ,341, 342, 344,345 } ; const int16 DEM1407[] = { 	55, 60, 65 , 70  ,    110 , 115, 120,125 ,    235, 240,245, 250,  290, 295, 300,305 } ; int DEM1229 = FALSE; int DEM1230; int DEM1231; int DEM1232; int DEM1233; function DEM251 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DEM1232 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DEM1229 = FALSE; 		for ( DEM979 = 0; 		DEM979 < sizeof(DEM1407) / sizeof(DEM1407[0]); 		DEM979++) { 			if (DEM1232 == DEM1407[DEM979]) { 				DEM1229 = TRUE; 				break; 							} 					} 		if (!DEM1229) { 			DEM1230 = DEM1407[0]; 			DEM1231 = abs(DEM1232 - DEM1407[0]); 			for ( DEM979 = 1; 			DEM979 < sizeof(DEM1407) / sizeof(DEM1407[0]); 			DEM979++) { 				DEM1233 = abs(DEM1232 - DEM1407[DEM979]); 				if (DEM1233 < DEM1231) { 					DEM1230 = DEM1407[DEM979]; 					DEM1231 = DEM1233; 									} 							} 			set_polar(POLAR_LS, DEM1230, 32767); 					} 			} } function DEM252 () { 	if (get_polar(POLAR_LS, POLAR_RADIUS) >= 3000) { 		DEM1232 = 360 - get_polar(POLAR_LS, POLAR_ANGLE); 		DEM1229 = FALSE; 		for ( DEM979 = 0; 		DEM979 < sizeof(DEM1406) / sizeof(DEM1406[0]); 		DEM979++) { 			if (DEM1232 == DEM1406[DEM979]) { 				DEM1229 = TRUE; 				break; 							} 					} 		if (!DEM1229) { 			DEM1230 = DEM1406[0]; 			DEM1231 = abs(DEM1232 - DEM1406[0]); 			for ( DEM979 = 1; 			DEM979 < sizeof(DEM1406) / sizeof(DEM1406[0]); 			DEM979++) { 				DEM1233 = abs(DEM1232 - DEM1406[DEM979]); 				if (DEM1233 < DEM1231) { 					DEM1230 = DEM1406[DEM979]; 					DEM1231 = DEM1233; 									} 							} 			set_polar(POLAR_LS, DEM1230, 32767); 					} 			} } int DEM1246; function DEM253() { 	if (combo_running(DEM87) && ( event_press(DEM447)    ||   get_ival(DEM452) ||         get_ival(DEM448) ||        get_ival(DEM453) ||        get_ival(DEM454) ||        get_ival(DEM449)      )) { 		combo_stop(DEM87); 		DEM1212 = 0; 			} 	if (DEM1224 == 0) { 		if ( ( DEM1200 == 0 && !combo_running(DEM90) && !combo_running(DEM98) && get_polar(POLAR_LS, POLAR_RADIUS) >= 3000 ) && ( get_ival(DEM447) || (DEM1212 == 1 ||     combo_running(DEM96) || combo_running(DEM94)|| combo_running(DEM92) ||  combo_running(DEM95)     || combo_running(DEM88) || combo_running(DEM97) || combo_running(DEM89) ||  combo_running(DEM91)  ))     ) { 			if(DEM437)DEM252 (); 			else if (DEM438)DEM251 (); 			combo_stop(DEM103); 			combo_run(DEM87); 					} 			} 	else{ 		combo_stop(DEM87); 			} 	} combo DEM87 { 	if(DEM437)DEM252 (); 	else if (DEM438)DEM251 (); 	combo_stop(DEM103); 	vm_tctrl(0); 	wait(4000); 	DEM1212 = 0; 	} combo DEM88 { 	set_val(DEM449,0); 	set_val(DEM447, 100); 	vm_tctrl(0);wait( random(210, 215) + DEM430); 	set_val(DEM447, 0); 	vm_tctrl(0);wait(600); 	vm_tctrl(0);wait( 2000); 	} function DEM254() { 	if (DEM555 > 600 && DEM555 <= 800) { 		DEM1247 = 240; 			} 	if (DEM555 > 800 && DEM555 <= 1000) { 		DEM1247 = 230; 			} 	if (DEM555 > 1000 && DEM555 <= 1500) { 		DEM1247 = 225; 			} 	if (DEM555 > 1500 && DEM555 <= 2000) { 		DEM1247 = 235; 			} 	if (DEM555 > 2000) { 		DEM1247 = 218; 			} 	combo_run(DEM97); 	} combo DEM89 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait( random(170, 190)); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 500); 	} combo DEM90 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait( 205); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 300); 	} combo DEM91 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait( 190); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 400); 	} int DEM1252; int DEM1253; int DEM29; int DEM476; int DEM474; int DEM1257; int DEM1258; combo DEM92 { 	if (DEM1253) { 		set_val(DEM447, 0); 		DEM1257 = 350; 			} 	else { 		DEM1257 = 0; 			} 	if (DEM1253) { 		DEM243(); 		DEM239(0, 0); 			} 	vm_tctrl(0); 	wait(DEM1257); 	if (DEM1253) { 		set_val(DEM450, 0); 		DEM1257 = 60; 			} 	else { 		set_val(DEM451, 0); 		DEM1257 = 60; 			} 	set_val(DEM447,0); 	vm_tctrl(0);wait(DEM1257); 	set_val(DEM451, 0); 	set_val(DEM450, 0); 	set_val(DEM447,0); 	vm_tctrl(0);wait(DEM1257); 	if (DEM1253) { 		DEM1257 = 0; 			} 	else { 		DEM1257 = 60; 			} 	set_val(DEM450, 0); 	set_val(DEM451, 0); 	set_val(DEM447,0); 	vm_tctrl(0);wait(DEM1257); 	set_val(DEM447, 100); 	set_val(DEM451, 100); 	vm_tctrl(0);wait(random(265, 268) +   DEM429 ); 	set_val(DEM451, 100); 	set_val(DEM447, 0); 	if (DEM1253) { 		DEM1257 = 15; 			} 	else { 		DEM1257 = 30; 			} 	vm_tctrl(0);wait(random(0,2) + DEM1270 + DEM1269 + DEM1257 ); 	set_val(DEM451, 100); 	set_val(DEM447, 100); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DEM447, 0); 	set_val(DEM451, 100); 	vm_tctrl(0);wait(random(0,2) + 80); 	set_val(DEM451, 100); 	vm_tctrl(0);wait(2500); 	} int DEM1197; int DEM1200; int DEM555; int DEM1196; int DEM1269; int DEM1270 = 111; int DEM1190; int DEM1201; int DEM1273; function DEM255() { 	DEM1273 = get_polar(POLAR_LS, POLAR_ANGLE); 	if ((DEM1273 > 5 && DEM1273 < 40) ) {         DEM479 = FALSE; 			} 	if ((DEM1273 > 40 && DEM1273 < 85)){ 		DEM479 = TRUE; 		    } 	if ((DEM1273 > 95 && DEM1273 < 130) ) { 		DEM479 = FALSE; 		    } 	 if((DEM1273 > 130 && DEM1273 < 175)) { 		DEM479 = TRUE; 		} 			if ((DEM1273 > 185 && DEM1273 < 220) ) {        DEM479 = FALSE; 			} 	if ((DEM1273 > 220 && DEM1273 < 265)){        DEM479 = TRUE; 		    } 	if ((DEM1273 > 275 && DEM1273 < 310) ) {        DEM479 = FALSE; 		    } 	 if((DEM1273 > 310 && DEM1273 < 355)) { 		DEM479 = TRUE; 		} 	if (DEM1200 == 0 && (DEM555 >= 750 || ((DEM1201 > 3000 && DEM1196 > 1 && DEM1196 < 5000)))) { 		if (DEM555 <= 2000 && DEM1196 > 1500) { 			set_val(DEM447, 0); 			DEM1270 = 170; 		} 		if (DEM555 <= 2000 && DEM1196 > 1 && DEM1196 <= 1500) { 			set_val(DEM447, 0); 			DEM1270 = 202; 					} 		if (DEM555 > 2000 || (DEM1196 > 1 && DEM1196 <= 1500)) { 			set_val(DEM447, 0); 			DEM1270 = 151; 					} 		if ((DEM555 > 2000 && DEM1196 > 1500) || DEM1201 > 1 && DEM1196 > 1) { 			set_val(DEM447, 0); 			DEM1270 = 152; 					} 		if ((DEM555 < 2000 && DEM1196 > 1500) || DEM1200 > 1 && DEM1196 > 1) { 			set_val(DEM447, 0); 			DEM1270 = 149; 					} 		if (DEM1196 > 1500) { 			set_val(DEM447, 0); 			DEM1270 = 148; 					} 		if (!DEM555 > 2000 && DEM1201 > 1 && DEM1196 > 1 && DEM1196 <= 1500) { 			set_val(DEM447, 0); 			DEM1270 = 147; 					} 		set_val(DEM447, 0); 		combo_stop(DEM97); 		combo_stop(DEM98); 		combo_stop(DEM95); 		combo_stop(DEM88); 		combo_stop(DEM94); 		combo_stop(DEM91); 		combo_stop(DEM90); 		combo_run(DEM92); 			} 	else { 		if (DEM1200) { 			set_val(DEM447, 0); 			combo_run(DEM93); 					} 		else { 			if (DEM555 < 750) { 				set_val(DEM447, 0); 				combo_run(DEM94); 							} 					} 			} } combo DEM93 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait(random(0, 6) + random(200, 205)); 	set_val(DEM447, 0); 	vm_tctrl(0);wait(random(0, 6) + 700); 	} combo DEM94 { 	set_val(DEM447, 100); 	vm_tctrl(0);wait( random(200, 205) + DEM429 )  set_val(DEM447, 0); 	vm_tctrl(0);wait( 700); 	} int DEM1283 = 246; int DEM1214 = 150; int DEM1285 = 0; combo DEM95 { 	set_val(DEM451, 100); 	set_val(DEM450, 0); 	set_val(DEM447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	set_val(DEM451, 0); 	set_val(DEM450, 0); 	set_val(DEM447,0); 	vm_tctrl(0);wait(random(0,2) + 60); 	DEM1285 = DEM428; 	set_val(DEM450, 100); 	set_val(DEM447,0); 	vm_tctrl(0);wait( 60); 	set_val(DEM450, 100); 	set_val(DEM447, 100); 	vm_tctrl(0);wait( DEM1283 + 10 + random(-2, 2) +  DEM432); 	set_val(DEM450, 100); 	set_val(DEM447, 0); 	DEM1252 = DEM1214; 	vm_tctrl(0);wait( DEM1214 + DEM1285 - 58 + DEM457 ); 	set_val(DEM450, 100); 	if(DEM1246)set_val(DEM447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DEM450, 100); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 3000); 	} combo DEM96 { 	set_val(DEM450, 100); 	set_val(DEM447, 100); 	vm_tctrl(0);wait( 160 + DEM432 ); 	set_val(DEM450, 100); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 3000); 	} int DEM1209; int DEM1287 = 220; int DEM1247; int DEM1289 = 0; combo DEM97 { 	DEM1289 = DEM428; 	set_val(DEM447, 100); 	vm_tctrl(0);wait( DEM1287 + DEM430); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( DEM1247 + (DEM1289) + 22 + DEM458); 	if(DEM1246)set_val(DEM447, 100); 	vm_tctrl(0);wait( 60); 	set_val(DEM447, 0); 	vm_tctrl(0);wait( 2000); 	} int DEM1291 = TRUE; int DEM1218; int DEM1293 = 245; int DEM1294 = 0; combo DEM98 { 	set_val(DEM449, 100); 	set_val(DEM450, 100); 	if (DEM1291) { 		DEM1294 = DEM428; 			} 	else { 		DEM1294 = 0   	} 	set_val(DEM447, 100); 	vm_tctrl(0);wait( DEM1293); 	vm_tctrl(0);wait( DEM1218 + DEM1294 + 40)  } int DEM1297; int DEM1298 = 145; int DEM651; int DEM650; int DEM653; int DEM652; combo DEM99 { 	set_val(DEM454, 0); 	vm_tctrl(0);wait( 30); 	set_val(DEM454, 100); 	vm_tctrl(0);wait( 60); 	} int DEM654; int DEM655; combo DEM100 { 	set_val(DEM453, 100); 	vm_tctrl(0);wait( DEM655); 	} define DEM1305 = TRUE; define DEM1306 = 95; define DEM1307 = 10; define DEM1308 = 70; define DEM1309 = FALSE; define DEM1310 = 50; define DEM1311 = 95; define DEM1312 = XB1_LT; define DEM1313 = XB1_RT; define DEM1314 = XB1_LX; define DEM1315 = XB1_LY; define DEM1316 = POLAR_LS; int DEM1317; function DEM256() { 	if ( get_ival(DEM452) > 30 &&    (get_ival(DEM451) || get_ival(DEM448)) &&    (!get_ival(DEM453) || !get_ival(DEM447))  ) { set_val(DEM452, 0); 		if(!get_ival(DEM447)){ 			set_val(DEM451,100); 					} 		else{ 			set_val(DEM451,0); 			set_val(DEM452,100); 					} 		  combo_run(DEM102); 		  if(DEM431 == TRUE){ 		  if(!get_ival(DEM450)){combo_run(DEM101);} 		  } 			} 	else { 		combo_stop(DEM102); 		combo_stop(DEM101); 			} 	} combo DEM101 { set_val(DEM450, 100); vm_tctrl(0); wait(45); set_val(DEM450,0); vm_tctrl(0); wait(200); 	} combo DEM102 { set_val(DEM452, 0); vm_tctrl(0); wait(35); 	if(!get_ival(DEM447)){ 		set_val(DEM451,100); 			} 	else{ 		set_val(DEM451,0); 			} 	set_val(DEM452, 100); 	vm_tctrl(0);wait(DEM600); 	if(!get_ival(DEM447)){ 		set_val(DEM451,100); 			} 	else{ 		set_val(DEM451,0); 			}     set_val(DEM452, 0); 	wait(1100); 	} int DEM1318; int DEM1319 ; function DEM257(DEM240) { return DEM259(DEM240 + 8192); } function DEM259(DEM240) {   DEM240 = (DEM240 % 32767) << 17;   if((DEM240 ^ (DEM240 * 2)) < 0) { DEM240 = (-2147483648) - DEM240; }   DEM240 = DEM240 >> 17;   return DEM240 * ((98304) - (DEM240 * DEM240) >> 11) >> 14; } int DEM1322, DEM1323; function DEM261(DEM121, DEM122, DEM123, DEM265){   DEM265 = (DEM265 * 32767) / 100;   DEM123 = (DEM123 * 32767) / 10000;   DEM122 = (360 - DEM122) * 91;   DEM1323 = DEM259(DEM122); DEM1322 = DEM257(DEM122);   DEM122 = 32767 - DEM257(abs(abs(DEM1323) - abs(DEM1322)));   DEM123 = DEM123 * (32767 - ((DEM122 * DEM265) >> 15)) >> 15;   set_val(42 + DEM121, clamp((DEM123 * DEM1322) >> 15, -32767, 32767));   set_val(43 + DEM121, clamp((DEM123 * DEM1323) >> 15, -32767, 32767));   return; } int DEM1328, DEM1329; int DEM1330 = TRUE; int DEM1331; int DEM1332; int DEM1333; int DEM1334; int DEM1335; function DEM266() {    if (!get_ival(XB1_LS)  && !get_ival(XB1_RS) && !get_ival(DEM451) && !get_ival(XB1_PR1) && !get_ival(XB1_PR2)  && !get_ival(XB1_PL1) && !get_ival(XB1_PL2) && !combo_running(DEM104)     && !get_ival(DEM449) &&  !get_ival(DEM452) && !get_ival(DEM448) && !get_ival(DEM450) && !get_ival(DEM454) && !get_ival(DEM447)){        combo_run(DEM103);    }else{    combo_stop(DEM103);    } } int DEM1336; int DEM1337; function DEM267(){   stickize(POLAR_LX, POLAR_LY, 32767);   DEM1328 = get_ipolar(POLAR_LS, POLAR_RADIUS);   DEM1329 = get_ipolar(POLAR_LS, POLAR_ANGLE);   DEM261(POLAR_LS,  DEM1329,  DEM1328, DEM455 + random(1,7));   } combo DEM103 {  vm_tctrl(0); wait(400); DEM267(); vm_tctrl(0); wait(500); 	} combo DEM104 { }  combo DEM105 { } combo DEM106{ } combo DEM107 { 	set_val(DEM448,100); 	vm_tctrl(0);wait( DEM651); 	set_val(DEM448,  0); 	vm_tctrl(0);wait( 30); 	if(DEM401){ 		set_val(DEM450,100); 			} 	vm_tctrl(0);wait( 60); 	} combo DEM108 { 	set_val(DEM450,  100); 	vm_tctrl(0);wait( 60);     wait( 60);     vm_tctrl(0);wait( 600);     set_val(DEM451,  100);     wait(60);     wait(60); 	} combo DEM109 { 	set_val(DEM454,100); 	vm_tctrl(0);wait( DEM653); 	set_val(DEM454,  0); 	vm_tctrl(0);wait( 30); 	if(DEM398){ 		set_val(DEM454,100); 			} 	vm_tctrl(0);wait( 60); 	} int DEM1002 int DEM1342 combo DEM110 { 	combo_suspend(DEM110) 	vm_tctrl(0);wait(361) } function DEM268 (){ 	if(DEM1002[DEM1342] != 361){ 	    DEM1342-- 	} 	else{ 		if(inv(DEM1342) != 297){ 			DEM268(); 		} 	} } int DEM1345; combo DEM111 { set_val(PS4_L3,100); wait(2000); wait(1000); } int DEM176; function DEM269(DEM270, DEM271, DEM272) {   DEM176 = get_ipolar(DEM270, POLAR_RADIUS);   if(DEM271) {     if(DEM176 <= DEM271) DEM176 = (DEM176 * 5000) / DEM271;     else DEM176 = ((5000 * (DEM176 - DEM271)) / (10000 - DEM271)) + 5000;   }   if (DEM272) DEM176 = (DEM176 * DEM272) / 10000;   set_polar2(DEM270, get_ipolar(DEM270, POLAR_ANGLE), min(DEM176, 14142));   if (DEM270 == POLAR_RS) stickize(ANALOG_RX, ANALOG_RY, 14142);   else stickize(ANALOG_LX, ANALOG_LY, 14142);   return; } combo DEM112{ vm_tctrl(-7); wait(1000); } 