// Elliptical boundary with dynamic rotation memory
// LT + RT uses last movement direction for ellipse orientation

// Constants for stick identification
define stickX = XB1_LX;
define stickY = XB1_LY;

// Ellipse ratios
define X_RATIO = 100;
define Y_RATIO = 40;

// Auto-rotation settings
define ROTATION_SPEED = 25;
define ROTATION_RADIUS = 100;

int x, y;
int last_x = 0;    // Remember last stick position X
int last_y = 0;    // Remember last stick position Y
int last_step = 0; // Remember last direction step
int rotation_step = 0;
int rotation_counter = 0;
int cos_a;
int sin_a;
int rx;
int ry;

// Add zone-based rotation matrix components
const int8 cos_table[] = {100, 71, 0, -71, -100, -71, 0, 71};
const int8 sin_table[] = {0, 71, 100, 71, 0, -71, -100, -71};

// Function to determine which zone (0-7) the stick position falls into
function get_zone(int x, int y) {
    if (abs(x) > abs(y) * 2) {
        if (x > 0) return 0;  // Right
        return 4;             // Left
    } 
    else if (abs(y) > abs(x) * 2) {
        if (y > 0) return 2;  // Down
        return 6;             // Up
    }
    else {
        if (x > 0 && y > 0) return 1;      // Down-Right
        if (x < 0 && y > 0) return 3;      // Down-Left
        if (x < 0 && y < 0) return 5;      // Up-Left
        if (x > 0 && y < 0) return 7;      // Up-Right
        return 0;  // Default to right for any other case (including center)
    }
    return 0;  // Fallback return for compiler
}
int zone;
main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    // Update last position when stick is moved without triggers
    if ((abs(x) > 20 || abs(y) > 20) && 
       (get_val(XB1_LT) < 95 || get_val(XB1_RT) < 95)) 

 

       {
       
        // Get current zone (0-7) using GPS zones logic
        zone = get_zone(x, y);  // From gps_zones_v2
        last_step = zone;  // Store zone instead of direction step
        
        // Determine which of 8 directions we're closest to
        if (abs(x) > abs(y) * 2) {
            if (x > 0) {
                last_step = 0;
            } else {
                last_step = 4;
            }
        } else if (abs(y) > abs(x) * 2) {
            if (y > 0) {
                last_step = 2;
            } else {
                last_step = 6;
            }
        } else {
            if (x > 0 && y > 0) last_step = 1;
            if (x < 0 && y > 0) last_step = 3;
            if (x < 0 && y < 0) last_step = 5;
            if (x > 0 && y < 0) last_step = 7;
        }
        last_x = x;
        last_y = y;
        
        // Normal circular movement (no ellipse)
        set_val(stickX, x);
        set_val(stickY, y);
    }
    
    // Elliptical movement only when both triggers are pressed
    else if ((get_val(XB1_LT) > 95) && (get_val(XB1_RT) > 95)) {
        if (x == 0 && y == 0) {
            // Auto-rotation code with stored direction
            // Determine which of 8 directions we're closest to
            if (abs(x) > abs(y) * 2) {
                if (x > 0) {
                    last_step = 0;
                } else {
                    last_step = 4;
                }
            } else if (abs(y) > abs(x) * 2) {
                if (y > 0) {
                    last_step = 2;
                } else {
                    last_step = 6;
                }
            } else {
                if (x > 0 && y > 0) last_step = 1;
                if (x < 0 && y > 0) last_step = 3;
                if (x < 0 && y < 0) last_step = 5;
                if (x > 0 && y < 0) last_step = 7;
            }
            last_x = x;
            last_y = y;
            rotation_step = last_step;
            // Use a simple 8-direction rotation pattern
            if (rotation_step == 0) {
                x = ROTATION_RADIUS;
                y = 0;
            } else if (rotation_step == 1) {
                x = ROTATION_RADIUS * 71 / 100;
                y = ROTATION_RADIUS * 71 / 100;
            } else if (rotation_step == 2) {
                x = 0;
                y = ROTATION_RADIUS;
            } else if (rotation_step == 3) {
                x = -ROTATION_RADIUS * 71 / 100;
                y = ROTATION_RADIUS * 71 / 100;
            } else if (rotation_step == 4) {
                x = -ROTATION_RADIUS;
                y = 0;
            } else if (rotation_step == 5) {
                x = -ROTATION_RADIUS * 71 / 100;
                y = -ROTATION_RADIUS * 71 / 100;
            } else if (rotation_step == 6) {
                x = 0;
                y = -ROTATION_RADIUS;
            } else if (rotation_step == 7) {
                x = ROTATION_RADIUS * 71 / 100;
                y = -ROTATION_RADIUS * 71 / 100;
            }
            // Update rotation counter and step
            rotation_counter += ROTATION_SPEED;
            if (rotation_counter >= 100) {
                rotation_counter -= 100;
                rotation_step = (rotation_step + 1) % 8;
            }
        }
        // Apply elliptical scaling only when triggers are pressed
        if (abs(x) > 0 || abs(y) > 0) {
            // REPLACE OLD SCALING LOGIC WITH THIS:
            zone = get_zone(x, y);
            cos_a = cos_table[zone];
            sin_a = sin_table[zone];
            
            // Rotate, scale, then rotate back
            rx = (x * cos_a + y * sin_a) / 100;
            ry = (-x * sin_a + y * cos_a) / 100;
            rx = (rx * X_RATIO) / 100;
            ry = (ry * Y_RATIO) / 100;
            x = (rx * cos_a - ry * sin_a) / 100;
            y = (rx * sin_a + ry * cos_a) / 100;
            set_val(stickX, x);
            set_val(stickY, y);
        }
    }
} 