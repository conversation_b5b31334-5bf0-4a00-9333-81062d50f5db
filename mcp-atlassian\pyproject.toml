[project]
name = "mcp-atlassian"
version = "0.1.8"
description = "The Model Context Protocol (MCP) Atlassian integration is an open-source implementation that bridges Atlassian products (Jira and Confluence) with AI language models following Anthropic's MCP specification. This project enables secure, contextual AI interactions with Atlassian tools while maintaining data privacy and security. Key features include:"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "atlassian-python-api>=3.41.16",
    "beautifulsoup4>=4.12.3",
    "httpx>=0.28.0",
    "mcp>=1.0.0",
    "python-dotenv>=1.0.1",
    "markdownify>=0.11.6"
]
[[project.authors]]
name = "sooperset"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
mcp-atlassian = "mcp_atlassian:main"

[project.optional-dependencies]
dev = [
    "uv>=0.1.0"
]
