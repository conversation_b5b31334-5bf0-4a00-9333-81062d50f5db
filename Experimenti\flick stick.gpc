int flickValue = 100;
int leftFlickDuration = 20;
int rightFlickDuration = 20;

main {
    if(event_press(PS5_R3))
        combo_run(flickRight);

    if(event_press(PS5_L3))
        combo_run(flickLeft);
}

combo flickRight {
    set_val(PS5_RY, flickValue);
    wait(rightFlickDuration);
    set_val(PS5_RY, -flickValue);
    wait(rightFlickDuration);
}

combo flickLeft {
    set_val(PS5_RY, -flickValue);
    wait(leftFlickDuration);
    set_val(PS5_RY, flickValue);
    wait(leftFlickDuration);
}