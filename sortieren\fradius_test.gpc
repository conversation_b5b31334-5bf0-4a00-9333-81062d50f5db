  define DEADZONE = 1000;
int radius;
int angle;
main {
  // Get the current radius of the left stick

  radius = get_polar (POLAR_LS,POLAR_RADIUS);
  angle = get_polar(POLAR_LS,POLAR_ANGLE);
  
  if(radius > DEADZONE) {
    // If stick is moved beyond deadzone, set to max radius
    set_polar2(POLAR_LS,get_polar(POLAR_LS,POLAR_ANGLE),10000);
  } else {
    // If stick is within deadzone, set radius to 0
    set_polar2(POLAR_LS,get_polar(POLAR_LS,POLAR_ANGLE),0);
  }
}
