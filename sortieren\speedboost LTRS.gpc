// Threshold value for right stick magnitude to trigger LB action
define Stick_Press_LB_Threshold = 95;

// Variable to track stick swap state
int stick_swap_toggle;

// Variable to store the magnitude of right stick movement
int rightStickMagnitude;

main {
    // Check if left stick button is pressed
    if(get_val(XB1_LS)){
        // Calculate the magnitude of right stick movement using Pythagorean theorem
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        
        // If left stick is pushed far enough (radius >= 1500)
        if(get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500) {
        //set_val(XB1_LT, 100);  // Commented out code that would set left trigger to 100%
            
            // Swap left stick input to right stick output
            set_val(POLAR_RX, get_val(POLAR_LX));
            set_val(POLAR_RY, get_val(POLAR_LY));
        }
        
        // Check if right stick magnitude exceeds threshold AND was previously below threshold
        // This detects a "flick" or sudden movement of the right stick
        if(rightStickMagnitude >= Stick_Press_LB_Threshold && isqrt(pow(get_lval(XB1_RX), 2) + pow(get_lval(XB1_RY), 2)) < Stick_Press_LB_Threshold)
        {
            // Add your code here for what should happen when this condition is true
            // This would typically trigger an action when right stick is quickly pushed past threshold
        }
    }
}