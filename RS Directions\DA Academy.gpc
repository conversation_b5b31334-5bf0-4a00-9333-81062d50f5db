// This part of script defines constants for various skill moves and button mappings for a game controller.

// Define constants for skill moves
define None                              = 0;
define HEEL_TO_HEEL_FLICK_SKILL          = 1;
define LA_CROQUETA_SKILL                 = 2;
define ROULETTE_SKILL                    = 3;
define BALL_ROLL_SKILL                   = 4;

// These numerical values serve as identifiers, which can be leveraged in future enhancements
// to accommodate more advanced conditions or actions associated with specific skills.

// Define button mappings for the alternative layout
define ShotBtn       = XB1_B     	  ; // Shot Btn         (default B/CIRCLE)
define PassBtn       = XB1_A     	  ; // Short Pass Btn   (default A/CROSS)
define PlayerRun     = XB1_LB         ; // Player Run       (default L1/LB)
define FinesseShot   = XB1_RB         ; // Finesse Shot     (default R1/RB)
define PaceCtrol     = XB1_LT         ; // Protect Ball     (default L2/LT)
define SprintBtn     = XB1_RT         ; // Sprint Btn       (default R2/RT)
define CrossBtn      = XB1_X		  ; // Cross Btn        (default X/SQUARE)
define ThroughBall   = XB1_Y          ; // Through Ball Btn (default Y/TRIANGLE)

// This section informs the script about our game controller layout. It is crucial that
// these assignments precisely match our configured input settings in the game.

//====================================================
// Define constants for analog stick and skill stick input
//====================================================

define MOVE_X        = XB1_LX;        // X-axis input for movement
define MOVE_Y        = XB1_LY;        // Y-axis input for movement
define SKILL_STICK_X = XB1_RX;        // X-axis input for skill stick
define SKILL_STICK_Y = XB1_RY;        // Y-axis input for skill stick

// These defined constants will be utilized in the main functions for skills and zone operations.
// A deeper understanding of their usage will become apparent as you progress through
// subsequent sections of the script.

     
//====================================================
main { // main block start here
if (Get_LS_Output) { 
// We use this variable to signal the script to scan the current left stick zone
// among the 8 equally divided zones.

	   
    if ((get_ipolar(POLAR_LS, POLAR_RADIUS) > 5100)) {
       // Check if the left stick is being used and its polar radius is greater than 5100 .
	   // This ensures that the user is actively using the left stick.
	   
        zone_p = ((((get_ipolar(POLAR_LS, POLAR_ANGLE) + 23) % 360) / -45) + 10) % 8;    
        // Calculate the zone based on the left stick's polar angle.
        // This line divides the circle of the left stick into 8 equal parts (zones).
		// These zones will be utilized to determine the current player-facing angle.
		// This allows us to discern all relative directions around the player.
		
		
		// Obtain the values for LX and LY from the ZONE_P[][] matrix based on the calculated zone.
		LX = ZONE_P[zone_p][0];
		LY = ZONE_P[zone_p][1];
		
		// These values will be used to perform skills by implementing them in the right stick, 
		// using Analog functions.

    }
    //======================================================
}

           if(!get_ival(XB1_RS) &&  !get_ival(PaceCtrol) && !get_ival(SprintBtn) && !get_ival(FinesseShot)) { 
			// Set the condition to perform skills. The script will execute skills only when
			// no action is taken on the right stick (R3 - moving GK), no protect ball button (PaceCtrol - jockey button),
			// no sprint button (SprintBtn), and no finesse shot button (FinesseShot).
			// This condition ensures that executing skills is allowed, preventing unintended RS player switching
			// during defensive actions, also when moving your goalkeeper.


// Check if the Right Stick is pushed UP
if (get_ival(XB1_RY) < -70 && !flick_up) {
    flick_up = TRUE;                // Flag that UP flick is initiated
    right_on = FALSE;               
// The 'right_on' flag determines the direction in which the skill will exit:
// If 'right_on' is FALSE, the skill will exit to the left side of the player's facing direction.
// If 'right_on' is TRUE, the skill will exit to the right side of the player's facing direction.
    ACTIVE = ROULETTE_SKILL;         // Set the active skill to Roulette
    combo_run(ROULETTE);             // Execute the Roulette skill combo
    Get_LS_Output = FALSE;           // Deactivate Left Stick zones scan
}

if (get_ival(XB1_RY) > 70 && !flick_d ) {
    flick_d = TRUE;
    right_on = TRUE;
    vm_tctrl(0);
    ACTIVE = HEEL_TO_HEEL_FLICK_SKILL; combo_run(HEELtoHEEL); Get_LS_Output = FALSE;
}

if (get_ival(XB1_RX) < -70 && !flick_l ) {
    flick_l = TRUE;
    right_on = FALSE;
    vm_tctrl(0);
    ACTIVE = LA_CROQUETA_SKILL; combo_run(LA_CROQUETA); Get_LS_Output = FALSE;
}

if (get_ival(XB1_RX) > 70 && !flick_r ) {
    flick_r = TRUE;
    right_on = TRUE;
    vm_tctrl(0);
    ACTIVE = BALL_ROLL_SKILL; combo_run(BALL_ROLL);   Get_LS_Output = FALSE;
}

if (abs(get_ival(SKILL_STICK_X)) < 20 && abs(get_ival(SKILL_STICK_Y)) < 20) {
    // Check if the Skill Stick is at its neutral position,
    // used to ensure script executes skills only once per flick.
    flick_up = 0;                   // Reset UP flick flag
    flick_d = 0;                    // Reset DOWN flick flag
    flick_l = 0;                    // Reset LEFT flick flag
    flick_r = 0;                    // Reset RIGHT flick flag
}


// Reset the Skill Stick values to zero (Force RS to center) to allow skill combos to execute actions seamlessly
set_val(SKILL_STICK_X, 0);
set_val(SKILL_STICK_Y, 0);


                           
            }// RS SKILLS end
                                                
} // main block end here
int flick_up; 
int flick_d;  
int flick_l;  
int flick_r; 
int Get_LS_Output = TRUE;
int ACTIVE;                                     
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick = 50; // Waiting time per action in skills combos.
             

///////////////////////////////////////////////////
// ZONE FUNCTION
int zone_p;
const int ZONE_P [][] = {
//  X,  Y   
{   0,-100 },//0 UP
{ 100,-100 },//1 Up-Right
{ 100,   0 },//2 Right
{ 100, 100 },//3 Down right
{   0, 100 },//4 Down
{-100, 100 },//5 Down Left
{-100,   0 },//6 Left
{-100,-100 } //7 Left Up 
}

// Combo for executing the Heel to Heel skill
combo HEELtoHEEL {
    RA_UP();             // Move the Right Stick UP
    wait(w_rstick);
    RA_ZERO();           // Reset the Right Stick to its neutral position
    wait(w_rstick);
    RA_DOWN();           // Move the Right Stick DOWN
    wait(w_rstick);
    Get_LS_Output = TRUE; // Allow the script to scan zones again.
}

// Combo for executing the La Croqueta skill
combo LA_CROQUETA {
    set_val(PlayerRun, 100);
    RA_L_R();            // Move the Right Stick to the Left or Right
    wait(500);
    Get_LS_Output = TRUE;
}

// Combo for executing the Roulette skill
combo ROULETTE {
    RA_DOWN();           // Move the Right Stick DOWN
    wait(w_rstick);
    RA_L_R();            // Move the Right Stick to the Left or Right
    wait(w_rstick);
    RA_UP();             // Move the Right Stick UP
    wait(w_rstick);
    Get_LS_Output = TRUE;
}

// Combo for executing the Ball Roll skill
combo BALL_ROLL {
    RA_L_R();            // Move the Right Stick to the Left or Right
    set_val(SprintBtn, 0);
    wait(310);
    wait(100);
    Get_LS_Output = TRUE;
}


//--------------------------------------------------------------
//      Analog Functions
//--------------------------------------------------------------

// Function for moving the Right Analog Stick to the Left or Right
function RA_L_R() {
    if (right_on) {
        // Move to the right
        set_val(SKILL_STICK_X, inv(LY));
        set_val(SKILL_STICK_Y, LX);
    } else {
        // Move to the left
        set_val(SKILL_STICK_X, LY);
        set_val(SKILL_STICK_Y, inv(LX));
    }
}

// Function for moving the Right Analog Stick UP
function RA_UP() {
    set_val(SKILL_STICK_X, LX);
    set_val(SKILL_STICK_Y, LY);
}

// Function for moving the Right Analog Stick DOWN
function RA_DOWN() {
    set_val(SKILL_STICK_X, inv(LX));
    set_val(SKILL_STICK_Y, inv(LY));
}

// Function to set the Right Analog Stick to its neutral position
function RA_ZERO() {
    set_val(SKILL_STICK_X, 0);
    set_val(SKILL_STICK_Y, 0);
}
