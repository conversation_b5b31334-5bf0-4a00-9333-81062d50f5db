int rightStickMagnitude;
int leftStickAngle;
int oppositeAngle;

main {
    if(get_val(XB1_RS)) {
        rightStickMagnitude = isqrt(pow(get_ival(XB1_RX), 2) + pow(get_ival(XB1_RY), 2));
        
        if(get_ipolar(POLAR_LS, POLAR_RADIUS) >= 1500) {
            leftStickAngle = get_polar(POLAR_LS, POLAR_ANGLE);
            
            if(rightStickMagnitude > 1500) {
                // Mirror left stick movement
                set_val(POLAR_RX, get_val(POLAR_LX));
                set_val(POLAR_RY, get_val(POLAR_LY));
            } else {
                // Move in opposite direction
                oppositeAngle = (leftStickAngle + 180) % 360;
                set_polar(POLAR_RS, oppositeAngle, get_ipolar(POLAR_LS, POLAR_RADIUS));
            }
        }
    }
} 