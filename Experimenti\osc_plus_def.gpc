int radius;
int current_angle;
int max_allowed_radius;
int max_possible;

int AngleInterval = 8;
int AI_VALUES[5];
int AI_VALUES_COUNT;
int current_index = 0;

int phase_counter = 0;
int oscillation_factor;
define PHASE_MULTIPLIER = 10;
int phase_speed = 80;
int actual_phase;
int current_base;
int oscillation_range;

// Variables for random radius control
int random_update_timer;
int current_random_radius;
define REDUCED_RADIUS = 32767;
define AngleInterval_2 = 12;
define MAX_RADIUS = 32767;

int lt_counter;    // For LT oscillation
int going_up;      // For LT direction
int combo_active;  // Track if LT+RT is pressed
int rt_timer;      // For RT spam
int rb_timer;      // For RB timing

init {
    lt_counter = 50;  // Start at 50 for LT
    going_up = 1;
    combo_active = 0;
    rt_timer = 0;
    rb_timer = 0;
}


main {
    defend();
    if (get_val(XB1_LT) == 0 && get_val(XB1_RT) == 0) {
        pass();
    }
}

function pass() {
    radius = get_polar(POLAR_LS, POLAR_RADIUS);
    
    if (radius < 2400) {
        set_polar(POLAR_LS, 0, 0);
        set_val(XB1_LB, 0);
    } else if (radius >= 2400 && radius < 8000){
    set_val(XB1_LB, 100);
        current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval);
        
        phase_counter = (phase_counter + phase_speed) % (360 * PHASE_MULTIPLIER);
        actual_phase = phase_counter / PHASE_MULTIPLIER;
        
        oscillation_factor = actual_phase;
        if (oscillation_factor > 180) {
            oscillation_factor = 360 - oscillation_factor;
        }
        oscillation_factor = ((oscillation_factor * 200) / 180) - 100;  // Convert to -100 to +100 range
        
        // Calculate 14% of current radius for oscillation range
        oscillation_range = (radius * 66) / 100;
        
        // Oscillate between current radius and (current radius - 14%)
        current_random_radius = radius * 3 - ((oscillation_range * (oscillation_factor - 220)) / 200);
        //current_random_radius = (radius * 3) + ((oscillation_range * oscillation_factor) / 100);
        
        if (current_random_radius > MAX_RADIUS) {
            current_random_radius = MAX_RADIUS;
        }
        if (current_random_radius < 0) {
            current_random_radius = 0;
        }
        
        set_polar(POLAR_LS, current_angle, current_random_radius);
    }

    else if (radius >= 8000){
        current_angle = quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2);
        
        phase_counter = (phase_counter + phase_speed) % (360 * PHASE_MULTIPLIER);
        actual_phase = phase_counter / PHASE_MULTIPLIER;
        
        oscillation_factor = actual_phase;
        if (oscillation_factor > 180) {
            oscillation_factor = 360 - oscillation_factor;
        }
        oscillation_factor = ((oscillation_factor * 200) / 180) - 100;  // Convert to -100 to +100 range
        
        // Calculate 14% of current radius for oscillation range
        oscillation_range = (radius * 14) / 100;
        
        // Oscillate between current radius and (current radius - 14%)
        current_random_radius = radius * 3 - ((oscillation_range * (oscillation_factor - 220)) / 200);
        //current_random_radius = (radius * 3) + ((oscillation_range * oscillation_factor) / 100);
        
        if (current_random_radius > MAX_RADIUS) {
            current_random_radius = MAX_RADIUS;
        }
        if (current_random_radius < 0) {
            current_random_radius = 0;
        }
        
        set_polar(POLAR_LS, current_angle, current_random_radius);
    }
}

// Function to quantize angle into discrete zones
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

function defend(){

    // Check for LT+RT combo
    if(get_val(XB1_LT) > 0 && get_val(XB1_RT) > 0) {
        combo_active = 1;
    } else {
        combo_active = 0;
        // Reset everything when combo is released
        set_val(XB1_LT, get_val(XB1_LT));
        set_val(XB1_RT, get_val(XB1_RT));
        set_val(XB1_LB, get_val(XB1_LB));
        lt_counter = 50;
        rt_timer = 0;
        rb_timer = 0;
    }

    if(combo_active) {
        // Handle LT oscillation (50-100)
        set_val(XB1_LT, lt_counter);
        if(going_up) {
            lt_counter = lt_counter + 1.5;  // Much slower oscillation
            if(lt_counter >= 100) {
                going_up = 0;
            }
        } else {
            lt_counter = lt_counter - 1.5;  // Much slower oscillation
            if(lt_counter <= 50) {
                going_up = 1;
            }
        }

        // Handle RT timing (1 second on, 500ms off)
        rt_timer = rt_timer + 1;
        if(rt_timer <= 60) {  // 1 second on (100 ticks)
            set_val(XB1_RT, 100);
        } else if(rt_timer <= 80) {  // 500ms off (50 ticks)
            set_val(XB1_RT, 0);
        } else {
            rt_timer = 0;  // Reset cycle
        }

        // Handle RB timing (3 seconds on, 1 second off)
        rb_timer = rb_timer + 1;
        if(rb_timer <= 210) {  // 3 seconds on (300 ticks)
            set_val(XB1_LB, 100);
        } else if(rb_timer <= 220) {  // 1 second off (100 ticks)
            set_val(XB1_LB, 0);
        } else {
            rb_timer = 0;  // Reset cycle
        }
    }
    } 