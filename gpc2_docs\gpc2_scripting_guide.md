# User's Manual and Documentation

### Table of Contents

- GPC Scripting

Introduction
Basic GPC IDE usage

Create your script file
Compile your script
Testing your script
Installing your script
- Introduction
- Basic GPC IDE usage

Create your script file
Compile your script
Testing your script
Installing your script
- Create your script file
- Compile your script
- Testing your script
- Installing your script

- Introduction
- Basic GPC IDE usage

Create your script file
Compile your script
Testing your script
Installing your script
- Create your script file
- Compile your script
- Testing your script
- Installing your script

- Create your script file
- Compile your script
- Testing your script
- Installing your script

# GPC Scripting

## Introduction

An easy way to get into GPC scripting is by starting with some basic existing code examples. 
Spend close attention to the keywords used and looking them up in the GPC Language Reference to get to know them.

The reference lists information of what a keyword does along with some example code and reference to other keywords doing something similar.

Here you will find some code examples by actions you may want to do.

The GPC scripting language is very similar to C.
For information about control statements like if / if else / for / while.. search the web for c programming flow control.

```
if / if else / for / while..
```

Like most programming languages GPC requires you to be careful to use the right syntax.
Any character at a wrong place can cause errors.
Especially a missing or misplaced character of , ; { } [ ] ( ) can make it hard to find the real position of the error.

```
, ; { } [ ] ( )
```

## Basic GPC IDE usage

I assume you have GTuner IV installed already: 

Having unpacked the .zip archive to a path where you have write access and no special characters in the path.

Please do setup your “Working Directory” if you haven't done that already.
This directory (and subdirectories) will be used to save your own scripts and downloaded stuff from the “Online Resource”.

To configure the Working directory:

### Create your script file

Start GTuner IV now and create a New Minimum GPC Script:

Top Menu: File→New→Minimum GPC Script  or press Ctrl + N , name your file

Your created script file will be opened in the GPC Script IDE automatically.

I have added the init { } section to the script file.

```
File→New→Minimum GPC Script
```

```
Ctrl + N
```

```
init { }
```

```
#pragma METAINFO("Script Name", 1, 0, "Script Author")
 
init {
 
}
 
main {
 
}
```

- The first line #pragma METAINFO(“Script Name”, 1, 0, “Script Author”) defines the information that will be displayed via GTuner IV when the script is installed to a slot or parsed when uploaded to the Online Resource.
- The init {} section is run once on start of the script.
- The main {} section is run each 1ms (and faster, so you can't rely on it that 1ms has passed since the last run).

```
#pragma METAINFO(“Script Name”, 1, 0, “Script Author”)
```

```
init {}
```

```
main {}
```

### Compile your script

Top Menu: Compiler→Compile or press F7

```
Compiler→Compile
```

```
F7
```

Check the Output Panel for Error and Warning message and more information.

Double clicking on a line will jump to the line where the Error occurred.

The real error may be in that line or a line above that one if there is a missing ; or something similar.
If you can't spot the source of the problem easily check your latest modifications since your last successful compile. 

If you still can't spot it you probably haven't followed the tips closely enough.

### Testing your script

You can run your script from within GTuner IV directly, without needing to install it into a slot of your Titan Two by clicking the green arrow/“Run” button or press F5

```
F5
```

Using the “Device Monitor” will allow you to see your input (left side) and the actions performed by the script  (right side).

If you drag a label from the left side INPUT to one of the 4 Channels fields (where the big rotary knob Horizontal is) you will only see the input recognized by the Titan Two for that button/input. To see the output you have to drag one from the list on the right side OUTPUT.

```
INPUT
```

```
Channels
```

```
Horizontal
```

```
OUTPUT
```

Hit the RUN button to start/stop the writing to the graph.

```
RUN
```

### Installing your script

To install your script to a slot of your Titan Two open the script in the “GPC Script IDE” and make it the active tab if you have multiple script open.

Install it via Menu: File→Install To→Memory Slot Nr - Select one available slot number. 1)

```
File→Install To→Memory Slot Nr
```

To use the script select the slot you have installed it to. Lot of options to do so:

- via the buttons on the Titan Two shell
- via GTuner IV “Device Memory Slots” Pane

 by using the buttons of “Memory Slots Control”

 by right-click context menu on the slot of “Memory Slots”

 by clicking on the bold number of “Memory Slots”
- by using the buttons of “Memory Slots Control”
- by right-click context menu on the slot of “Memory Slots”
- by clicking on the bold number of “Memory Slots”

- by using the buttons of “Memory Slots Control”
- by right-click context menu on the slot of “Memory Slots”
- by clicking on the bold number of “Memory Slots”

