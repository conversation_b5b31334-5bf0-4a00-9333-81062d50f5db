// Script was generated with < FIFA Series Skills Generator > ver. 14.2 Date :08.01.21 Time: 23:20:25
//------------------------------------------------------------------------
/********************************************************************************************** 
This Script was made and intended for www.cronusmax.com & CronusMAX ONLY.                     * 
UNLESS permission is given by the creator and/or copywritee,                                  * 
All rights reserved. This material may not be reproduced, displayed,                          * 
modified or distributed without the express prior written permission of the                   * 
copyright holder. For permission, contact CronusMax.                                          * 
    __  ____   ___   ____   __ __  _____ ___ ___   ____  __ __                                * 
   /  ]|    \ /   \ |    \ |  |  |/ ___/|   |   | /    ||  |  |                               * 
  /  / |  D  )     ||  _  ||  |  (   \_ | _   _ ||  o  ||  |  |                               * 
 /  /  |    /|  O  ||  |  ||  |  |\__  ||  \_/  ||     ||_   _|                               * 
/   \_ |    \|     ||  |  ||  :  |/  \ ||   |   ||  _  ||     |                               * 
\     ||  .  \     ||  |  ||     |\    ||   |   ||  |  ||  |  |                               * 
 \____||__|\_|\___/ |__|__| \__,_| \___||___|___||__|__||__|__|                               * 
                                                                                              * 
***********************************************************************************************/ 
                                                                       
                                                                       
/***********************************************************************
                                                                       
  $$$$$$$$\ $$$$$$\ $$$$$$$$\  $$$$$$\         $$$$$$\    $$\           
  $$  _____|\_$$  _|$$  _____|$$  __$$\       $$  __$$\ $$$$ |          
  $$ |        $$ |  $$ |      $$ /  $$ |      \__/  $$ |\_$$ |          
  $$$$$\      $$ |  $$$$$\    $$$$$$$$ |       $$$$$$  |  $$ |          
  $$  __|     $$ |  $$  __|   $$  __$$ |      $$  ____/   $$ |         
  $$ |        $$ |  $$ |      $$ |  $$ |      $$ |        $$ |         
  $$ |      $$$$$$\ $$ |      $$ |  $$ |      $$$$$$$$\ $$$$$$\        
  \__|      \______|\__|      \__|  \__|      \________|\______|       
                                                                       
*************************************************************************/
                                                                         
//*************************************************************************/
                                                                         
//***************  XBOX ONE Elite Controller   **************************\\
//==\\//==\\//==        ONLY PADDLES           ==//==\\//==\\//==
//-----------------------------------------------------------------------
                                                                         
///\ Paddle Right 1 : 0. None.//\
///\ Paddle Right 2 : 0. None.//\
///\ Paddle Left  1 : 0. None.//\
///\ Paddle Left  2 : 0. None.//\
//-------------------------------------------------------------- 
// UNMAPING                                                 
//-------------------------------------------------------------- 
unmap 24;  // Paddle Right 1
unmap 25;  // Paddle Right 2
unmap 26;  // Paddle Left  1
unmap 27;  // Paddle Left  2
//-------------------------------------------------------------- 
// DECLARATIONS                                                  
//-------------------------------------------------------------- 
define DOnotUSE      =   1;
define DelayNormal   =  80;
define DelayRnbwHOLD = 160;
define DelayRest     = 200;
define DelayLeft_move= 500;
define time_to_dblclick     = 300; // Time to Double click     
//////////////////////////////////////////////////////////////////
// YOUR BUTTON LAYOUT 
define PaceCtrol     = XB1_LT; // Pace Control
define FinesseShot   = XB1_LB; // Finesse Shot
define PlayerRun     = XB1_RB; // Player Run  
define ShotBtn       = XB1_B; // Shot Btn  
define SprintBtn     = XB1_RT; // Sprint Btn 
define PassBtn       = XB1_A; // Pass Btn 
define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;        
//-------------------------------------------------------------- 
// SKILLS LIST                                                   
//-------------------------------------------------------------- 
define FAKE_SHOT_SKILL                 = 1;// Fake Shot                    
define HEEL_TO_HEEL_FLICK_SKILL        = 2;// Heel to Heel                 
define HEEL_FLICK_TURN_SKILL           = 3;// R1 + Heel to Heel            
define RAINBOW_SKILL                   = 4;// Simple Rainbow               
define DRAG_BACK_SOMBRERO_SKILL        = 5;// Drag Back + R3 press         
define FAKE_PASS_SKILL                 = 6;// R2 + Fake Shot               
define DRAG_BACK_UNIVERSAL_SKILL       = 7;// hold R1, LS down              
define STEP_OVER_FEINT_SKILL           = 8;//                               
define DRAG_TO_DRAG_SKILL              = 9;// L2 + Fake Shot + LS zero      
define HOCUS_POCUS_SKILL               =10;// RS D to L, half circle L to R 
define TRIPLE_ELASTICO_SKILL           =11;// RS D to R, half circle R to L 
define ELASTICO_SKILL                  =12;// half circle from R to L       
define REVERSE_ELASTICO_SKILL          =13;// half sircle from L to R       
define CRUYFF_TURN_SKILL               =14;// Fake Shot + LS down           
//--------------------------------------------------------------            
define LA_CROQUETA_SKILL               =15;// L1 + RS                       
define RONALDO_CHOP_SKILL              =16;// L2 + Fake Shot                 
define ROULETTE_SKILL                  =17;// Roulette                       
define FLAIR_ROULETTE_SKILL            =18;// L1 + Roulette                  
define BALL_ROLL_SKILL                 =19;// Ball Roll                     
define BERBA_MCGEADY_SPIN_SKILL        =20;// RS Up , <-/->                  
define BOLASIE_FLICK_SKILL             =21;// R1 + BERBA Spin                 
define TORNADO_SKILL                   =22;// L1 + Berba Spin                  
define THREE_TOUCH_ROULETTE_SKILL      =23;// L2 + RS Down, <-/->               
define ALTERNATIVE_ELASTICO_CHOP_SKILL =24;// R1 + RS Down, <-/->                 
define BALL_ROLL_CHOP_SKILL            =25;// hold RS <-/ hold RS ->                
define FEINT_AND_EXIT_SKILL            =26;// hold L1 + RS half circle from L to R / from R to L 
define FEINT_L_EXIT_R_SKILL            =27;// RS half circle from L to R / from R to L 
define LATERAL_HEEL_TO_HEEL_SKILL      =28;//  L1 + RS <-/->
define WAKA_WAKA_SKILL                 =29;// WAKA WAKA 
define BODY_FEINT_SKILL                =30;// BODY FEINT 
define DRAG_TO_HEEL                    =31;// Drag to Heel move  
define BALL_ROLL_FAKE_TURN             =32;// Ball Roll Fake Turn : L2 + Berba Spin
define FEINT_FORWARD_AND_TURN          =33;// Feint Forward & Turn : RS double down
define TURN_BACK                       =34;// Turn Back                          
define ADVANCED_CROQUETA               =35;// Advanced Croqueta                  
define CANCELED_THREE_TOUCH_ROULETTE_SKILL=36;                              
define REVERSE_STEP_OVER_SKILL         =37;// Reverse Step Over         
define FAKE_DRAG_BACK_SKILL            =38;// Fake Drag Back
define RAINBOW_TO_SCORPION_KICK_SKILL  =39;  
define STEP_OVER_BOOST_SKILL           =40;  
define CANCEL_SHOOT_SKILL              =41;  
define DIRECTIONAL_NUTMEG_SKILL        =42;  
define CANCELED_BERBA_SPIN_SKILL      =43;   
define CANCELED_BERBA_SPIN_WITH_DIRECTION =44;
define BALL_ROLL_TO_SCOOP_TURN_SKILL  =45;
define DRIBBLING_SKILL                =46;
//--------------------------------------------------------------   
define UP         = 0; 
define UP_RIGHT   = 1; 
define RIGHT      = 2; 
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int dTemp, dStart, dMid, dEnd;
                                               
                                               
int ACTIVE;                                     
int SKILL ;  
int LX, LY;          // Direction of Left Stick         
int right_on ;                            
int w_rstick  = 50;                        
int OnOffMods = TRUE;                            
int Sombrero;     
int hold_btn = 200;
int Shot_wait ; 
int ShotBoxWait ;                                              
                                               
//--------------------------------------------------------------
// MAIN BLOCK                                                   
//--------------------------------------------------------------
main {                                         
                                                

                                                
        ///////////////////////////////////////////////////////////// 
        //                                                           
        if(abs(get_val(MOVE_X))> 60 || abs(get_val(MOVE_Y))> 60){   
	            LX = get_val(MOVE_X);                                      
	            LY = get_val(MOVE_Y);                                      
             calc_zone ();                                              
        }                                                           
        //----------------------------------------------------------- 
                                      
if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn)){
 
		if( get_val(ShotBtn ) ){
		    set_val(ShotBtn,0);
			
			ShotBoxWait += get_ptime(ShotBtn);
			if (ShotBoxWait > 350)  { Shot_wait =  240; combo_run(Inside_Box_Finishing); }
		
		
			if (ShotBoxWait < 350 )  { Shot_wait =  220; combo_run(Inside_Box_Finishing) }		
	}
	
	}                                   

                                                                     
   //--------------------------------------------------------------
} // end of main block                          
                                                
////////////////////////////////////////////////////////////////
// COMBO BLOCKS   //////////////////////////////////////////////
////////////////////////////////////////////////////////////////
                                                                
int tap;
combo ONE_TAP {                                    
    tap = TRUE;                                    
    wait(time_to_dblclick); // wait for second tap 
    tap = FALSE;                                  
}  

combo Shooting_Setup {
set_val(ShotBtn, 0);
  set_val(PlayerRun,100);
  RA_L_R () ; 
  wait(30);//
  set_val(ShotBtn, 0);
  set_val(PaceCtrol,100);
  set_val(SprintBtn,100);
  wait(30);//     
}  

combo Inside_Box_Finishing {
AUTO_EXIT()
call (Shooting_Setup )
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(Shot_wait);
    set_val(SprintBtn,100);
    set_val(PS4_R3,100);
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(160);
    set_val(PS4_R3,100);
    set_val(PS4_R1,100);
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,100);
    set_val(ShotBtn,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(600);  
    ShotBoxWait=0
} 




function AUTO_EXIT() {
     
   
    // Moving to the UP - RIGHT -->
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
	
		 
	}
	      
	// Moving to the DOWN - RIGHT -->      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
	
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 

	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE

		  
		 
	}
	}
	


 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }                                          
///////////////////////////////////////////////////
// ZONE FUNCTION
data
(  0, 100, 100, 100,   0, 156, 156, 156, 
 156, 156,   0, 100, 100, 100,   0, 156
);

int move_lx, move_ly, zone_p;

function calc_zone(){
    if(get_val(XB1_LX) >= 50) move_lx = 100;
    else if(get_val(XB1_LX) <= -50) move_lx = -100;
    else move_lx = 0;
    if(get_val(XB1_LY) >= 50) move_ly = 100;
    else if(get_val( XB1_LY) <= -50) move_ly = -100;
    else move_ly = 0;
    
    
    if(move_lx != 0 || move_ly != 0) {
        zone_p = 0; while(zone_p < 8) {
            if(dchar(zone_p) == move_lx && dchar(8 + zone_p) == move_ly) {
                break;
            } zone_p += 1;
        }
    }    
        
}
function calc_relative_xy(d) {
    
        //zone_p += d;
        if(d < 0 ) d = 7;
        else if(d >= 8) d = d - 8;
        move_lx = dchar(d);
        move_ly = dchar(8 + d);   
}
//--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		set_val(MOVE_X,inv(LY));  
		set_val(MOVE_Y,LX);       
	}                           
	else {       //  left       
	    set_val(MOVE_X,LY );    
	    set_val(MOVE_Y,inv(LX)); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		set_val(SKILL_STICK_X,inv(LY));
		set_val(SKILL_STICK_Y,LX);      
	}                                  
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
	set_val(SKILL_STICK_X,LX );        
	set_val(SKILL_STICK_Y,LY );        
}                                   
function RA_DOWN () {               
	set_val(SKILL_STICK_X,inv(LX) );   
	set_val(SKILL_STICK_Y,inv(LY) );   
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}                             
//--------------------------------------------------------------
//SKILLS LED INDICATION                                         
//--------------------------------------------------------------
function colorled(a,b,c,d) { 
set_led(LED_1,a);            
set_led(LED_2,b);            
set_led(LED_3,c);            
set_led(LED_4,d);            
}// func end                             