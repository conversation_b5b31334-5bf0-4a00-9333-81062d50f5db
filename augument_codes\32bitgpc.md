# 32bit 

GPC scripting is available in both 16-bit and 32-bit flavors. Developing using 32-bit allows for a much larger address space, which means more memory can be accessed and used by the GamePack. 


16-bit has a maximum of 2^16 possible values and 32bit has a maximum of 2^32 possible values. 

16-bit: 0000 0000 0000 0000

32-bit: 0000 0000 0000 0000 0000 0000 0000 0000


What is 32-bit?



32-bit scripting support allows for vastly greater calculations, enabling more precision, more accuracy, and more automation - especially for M.O.D.s such as Aim Assist
 and Anti-Recoil
.

Previously, the variable calculation range with 16-bit (-32768 to 32767), but now with 32-bit that range has increased to a massive (−2,147,483,648 to 2,147,483,647), allowing for much more complex calculations that can be scaled up then back down while retaining a high level of accuracy, enabling a degree of precision not previously available.

Size


Minimum Value


Maximum Value


8-bits


-(2^8) = -128


2^8 - 1 = 127


16-bits


-(2^16) = -32,768


2^16 - 1 = 32,767


32-bits


-(2^32) = -2,147,483,648


2^32 - 1 = 2,147,483,647







New Functions



We've also added three new incredibly powerful GPC functions, get_polar, get_ipolar and get_info.

⚙ get_ipolar

get_ipolar() is the latest brand new function that can be used to gain information to retrieve the unmodified input value of the analog stick. It can be used with both left and right analog sticks, get_ipolar() uses 2 parameters in the function, 1st is the stick you want to use and the 2nd parameter is either Angle or Radius. Only Angle or Radius can be retrieved at once. 

The POLAR_ANGLE can be within the range of 0 - 359 and this depends on the current direction the stick has been pushed.
The POLAR_RADIUS has a range of 0 - 32767 and this represents how far from the center the stick is pushed.



GPC


1main {
2    get_ipolar(POLAR_LS,POLAR_RADIUS);   // retrieves current unmodified radius of an analog stick
3    get_ipolar(POLAR_RS,POLAR_ANGLE);    // retrieves current unmodified angle of an analog stick
4
5}

main {
    get_ipolar(POLAR_LS,POLAR_RADIUS);   // retrieves current unmodified radius of an analog stick
    get_ipolar(POLAR_RS,POLAR_ANGLE);    // retrieves current unmodified angle of an analog stick

}







🔴 Syntax

get_ipolar(POLAR_LS,POLAR_RADIUS);
get_ipolar(POLAR_RS,POLAR_ANGLE);
⚪ Parameters

<POLAR_ANGLE> Returns the unmodified Angle on an analog stick POLAR_LS or POLAR_RS (0-359).

<POLAR_RADIUS> Returns the unmodified radius of an analog stick POLAR_LS or POLAR_RS (0 - 32767).

🔵 Returns

Either the unmodified angle or radius will be returned depending on the given parameter.



⚙ get_polar

get_polar is a brand-new function that can be used to gain information on the current angle or radius of the analog stick. It can be used with both left and right analog sticks, get_polar() uses 2 parameters in the function, 1st is the stick you want to use, and the 2nd parameter is either Angle or Radius. Only Angle or Radius can be retrieved at once. 

The POLAR_ANGLE can be within the range of 0 - 359 and this depends on the current direction the stick has been pushed.
The POLAR_RADIUS has a range of 0 - 32767 and this represents how far from the center the stick is pushed.



GPC


1main {
2    get_polar(POLAR_LS,POLAR_RADIUS);   // retrieves current radius of an analog stick
3    
4    get_polar(POLAR_RS,POLAR_ANGLE);    // retrieves current angle of an analog stick
5}

main {
    get_polar(POLAR_LS,POLAR_RADIUS);   // retrieves current radius of an analog stick
    
    get_polar(POLAR_RS,POLAR_ANGLE);    // retrieves current angle of an analog stick
}







🔴 Syntax

get_polar(POLAR_LS,POLAR_RADIUS);
get_polar(POLAR_RS,POLAR_ANGLE);
⚪ Parameters

<POLAR_ANGLE> Returns the Angle on an analog stick POLAR_LS or POLAR_RS (0-359).

<POLAR_RADIUS> Returns the radius of an analog stick POLAR_LS or POLAR_RS (0 - 32767).

🔵 Returns

Either the angle or radius will be returned depending on the given parameter.



⚙ set_polar2

set_polar2is a virtual stick that you can use to quickly calculate values without the hassle of time-consuming trigonometry functions.

POLAR_GHOST is the virtual stick where an angle and radius can be assigned using set_polar2. 
ANALOG_GHOSTX is the virtual X AXIS to be used with both math and trigonometry functions.
ANALOG_GHOSTY is the virtual Y AXIS to be used with both math and trigonometry functions.



GPC


main {
	set_val(TRACE_1, PolarToCartesian(60, 10000, X_AXIS)); // 4999
	set_val(TRACE_2, PolarToCartesian(60, 10000, Y_AXIS)); // -8660
}
define X_AXIS = 0;
define Y_AXIS = 1;
function PolarToCartesian(angle, radius, axis) {
  set_polar2(POLAR_GHOST, angle, radius);
  if (axis == X_AXIS) {
  	return get_val(ANALOG_GHOSTX);
  }
  return get_val(ANALOG_GHOSTY);
}

main {
	set_val(TRACE_1, PolarToCartesian(60, 10000, X_AXIS)); // 4999
	set_val(TRACE_2, PolarToCartesian(60, 10000, Y_AXIS)); // -8660
}
define X_AXIS = 0;
define Y_AXIS = 1;
function PolarToCartesian(angle, radius, axis) {
  set_polar2(POLAR_GHOST, angle, radius);
  if (axis == X_AXIS) {
  	return get_val(ANALOG_GHOSTX);
  }
  return get_val(ANALOG_GHOSTY);
}




⚙ get_info

get_info is a new function that checks the active state of the MK Profile. An MK Profile has up to 6 different states as follows (0 = HIP, 1 = ADS, 2 = AUX1, 3 = AUX2, 4 = AUX3, 5 = AUX4). With this information, you are able to script for any specific state and take action accordingly. 

For example, you can have mods automatically toggle ON/OFF based on if you are HIP firing, Aiming Down Sights (ADS), or using any of the 4 Auxiliary MK profiles. Think of the possibilities...


GPC


1// Show usage for PENDING_CONFIG and ACTIVE_CONFIG
2// Check traces with mouse and keyboard, click activator to see the trace values
3
4// 0 = HIP, 1 = ADS, 2 = AUX1, 3 = AUX2, 4 = AUX3, 5 = AUX4
5// If a config has delay activation it will show up in the traces after the activation time
6
7main {
8    set_val(TRACE_1, get_info(PENDING_CONFIG))
9    set_val(TRACE_2, get_info(ACTIVE_CONFIG))
10
11    if(get_info(ACTIVE_CONFIG) == 1 && get_ival(XB1_LT)){ //If aiming down sights and Left Trigger is held
12        set_val(XB1_LS,100);                              //Hold LS / L3 
13    }
14}

// Show usage for PENDING_CONFIG and ACTIVE_CONFIG
// Check traces with mouse and keyboard, click activator to see the trace values

// 0 = HIP, 1 = ADS, 2 = AUX1, 3 = AUX2, 4 = AUX3, 5 = AUX4
// If a config has delay activation it will show up in the traces after the activation time

main {
    set_val(TRACE_1, get_info(PENDING_CONFIG))
    set_val(TRACE_2, get_info(ACTIVE_CONFIG))

    if(get_info(ACTIVE_CONFIG) == 1 && get_ival(XB1_LT)){ //If aiming down sights and Left Trigger is held
        set_val(XB1_LS,100);                              //Hold LS / L3 
    }
}







🔴 Syntax

get_info(PENDING_CONFIG);
get_info(ACTIVE_CONFIG);
⚪ Parameters

<PENDING_CONFIG> Shows usage for the pending config.

<ACTIVE_CONFIG> Shows the usage for the active config.

🔵 Returns

Active profile in use by MK Settings:

Return


Profile


0


HIP


1


ADS


2


AUX1


3


AUX2


4


AUX3


5


AUX4







⚙ int32




int32 is a brand new 32-bit function that is used to create an array that holds values greater than 32767 and less than -32768.


GPC


1const int32 array [][] = {
2	{ 111111, 222222, 333333},		
3	{-444444,-555555,-666666},
4	{ 777777, 888888, 999999}
5};
6main {
7    set_val(TRACE_1,array[0][1]);
8}

const int32 array [][] = {
	{ 111111, 222222, 333333},		
	{-444444,-555555,-666666},
	{ 777777, 888888, 999999}
};
main {
    set_val(TRACE_1,array[0][1]);
}







⚙ 32-bit Traces



Traces are commonly used for debugging a script and viewing information from a script or game.

There are six TRACE debug fields found in Device Monitor that can be used by a GPC script for feedback information, flow control checking, and variables monitoring. TRACE_1 to TRACE_3 is for 32-bit values, TRACE_4 to TRACE_6 is for 16-bit values.

Trace Number


Integer Value


TRACE_1


32-bit


TRACE_2


32-bit


TRACE_3


32-bit


TRACE_4


16-bit


TRACE_5


16-bit


TRACE_6


16-bit







For example, if you wanted to see the current value of a variable that was over the 16-bit limit you could set a trace using TRACE_1, TRACE_2, or TRACE_3 for the variable to see its value:


GPC


1int value;
2main {
3    set_val(TRACE_1,value);
4    value = 1000 * get_ival(XB1_RT);
5}

int value;
main {
    set_val(TRACE_1,value);
    value = 1000 * get_ival(XB1_RT);
}

/**
 * Reset the text fill color so that placeholder is visible
 */
.npm__react-simple-code-editor__textarea:empty {
  -webkit-text-fill-color: inherit !important;
}

/**
 * Hack to apply on some CSS on IE10 and IE11
 */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /**
    * IE doesn't support '-webkit-text-fill-color'
    * So we use 'color: transparent' to make the text transparent on IE
    * Unlike other browsers, it doesn't affect caret color in IE
    */
  .npm__react-simple-code-editor__textarea {
    color: transparent !important;
  }

  .npm__react-simple-code-editor__textarea::selection {
    background-color: #accef7 !important;
    color: transparent !important;
  }
}





⚙ 32bit SPVARS



With the use of int32 and the 32-bit update, you can save much higher values to SPVAR than ever before.  

Each 32-bit SPVAR can hold 1x32-bit (4 bytes) value of −2,147,483,648 to 2,147,483,647. Whereas the previous 16-bit SPVARs were only 16-bit (2 bytes) of -32,767 to 32,767. This can be extremely useful if you need to scale and save values for more advanced movements such as Anti-Recoil
 and Aim Assist
.

Additionally, advanced developers who use bit packing in their GPC scripts are now able to store twice as much information in a SPVAR as they were before. With 16-bit you were only able to store 2x8-bit values with a range of -32768 to 32767; with 32bit you can store double that amount with 4x8-bit values.


💥 Bit-Packing

To optimize memory usage, you can use the Bit-Packing SPVARs
 guide. This technique packs variables more densely, so is especially useful when you're trying to fit more code into the 64K space, which is particularly important for complex scripts that require a large amount of data storage. 

Bit-Packing allows you to store multiple values within a single variable by utilizing bitwise operations, ensuring you make the most out of the available space.


32bit Examples



⚙ get_polar

Here is an example of a Strafe Mod not being possible using 16-bit. The new get_polar function is used to return the current radius of the left analog stick. This value is then multiplied by a set number or 50 in this example, then once more by 100 to scale input. Then to scale the value back down it is divided by 20000. The resulting value is then used as a strength that is applied to the left stick to strafe left and right. 


Info: While GPC does not support float, a far higher degree of accuracy is achieved by first scaling up and then back down.



GPC


1int strafe_value = 50;
2int strafe_val;
3main {
4    strafe_val = (get_polar(POLAR_LS,POLAR_RADIUS) * strafe_value * 100) / 20000;
5    if(get_val(XB1_LT)){
6        combo_run(strafe);
7    }
8
9}
10combo strafe{
11    set_val(POLAR_LX, inv(strafe_val));
12    wait(250);
13    set_val(POLAR_LX, strafe_val);
14    wait(250);
15}

int strafe_value = 50;
int strafe_val;
main {
    strafe_val = (get_polar(POLAR_LS,POLAR_RADIUS) * strafe_value * 100) / 20000;
    if(get_val(XB1_LT)){
        combo_run(strafe);
    }

}
combo strafe{
    set_val(POLAR_LX, inv(strafe_val));
    wait(250);
    set_val(POLAR_LX, strafe_val);
    wait(250);
}





Scaling with 32-bit

Here's an example of scaling a recoil value though this time it is for 32-bit. Here the value is much higher which leads to much greater accuracy when calculating a value. The value seen in TRACE_3 could be applied to either stick as a polar value leading to much greater results. In the example, the recoil value is scaled up based on sensitivity, deadzone, and a modifier (accounts for attachments).


GPC


int original_recoil_value = 20;
int mod_sensitivity,mod_deadzone,part_1,part_2,applied_recoil,val;
int rightStickDeadzone = 5;
int rightStickSensitivity = 20;
int recoil_adjustment = 25;
main {
    set_val(TRACE_1,part_1);
    set_val(TRACE_2,part_2);
    set_val(TRACE_3,applied_recoil);
    
    if(event_press(XB1_UP)){
    	rightStickDeadzone++;
    }
    if(event_press(XB1_DOWN)){
    	rightStickDeadzone--;
    }
    if(event_press(XB1_LEFT)){
    	rightStickSensitivity++;
    }
    if(event_press(XB1_RIGHT)){
    	rightStickSensitivity--;
    }
	part_1 = (((((original_recoil_value * 327) + (mod_sensitivity + mod_deadzone)) * 327) / 327));
	part_2 = part_1 * (100 + recoil_adjustment);
	applied_recoil = part_2 / 100;
	mod_deadzone = ((((original_recoil_value * 327) * 25) * (rightStickDeadzone - 5)) / 1000);
	mod_sensitivity = ((((original_recoil_value * 327) * (25 + val)) * (20 - rightStickSensitivity)) / 1000);
	if(rightStickSensitivity >= 5){
	   	val = 0;
	}
    if(rightStickSensitivity < 5){
	    val = ((rightStickSensitivity - 5) * -4);
	}

}

int original_recoil_value = 20;
int mod_sensitivity,mod_deadzone,part_1,part_2,applied_recoil,val;
int rightStickDeadzone = 5;
int rightStickSensitivity = 20;
int recoil_adjustment = 25;
main {
    set_val(TRACE_1,part_1);
    set_val(TRACE_2,part_2);
    set_val(TRACE_3,applied_recoil);
    
    if(event_press(XB1_UP)){
    	rightStickDeadzone++;
    }
    if(event_press(XB1_DOWN)){
    	rightStickDeadzone--;
    }
    if(event_press(XB1_LEFT)){
    	rightStickSensitivity++;
    }
    if(event_press(XB1_RIGHT)){
    	rightStickSensitivity--;
    }
	part_1 = (((((original_recoil_value * 327) + (mod_sensitivity + mod_deadzone)) * 327) / 327));
	part_2 = part_1 * (100 + recoil_adjustment);
	applied_recoil = part_2 / 100;
	mod_deadzone = ((((original_recoil_value * 327) * 25) * (rightStickDeadzone - 5)) / 1000);
	mod_sensitivity = ((((original_recoil_value * 327) * (25 + val)) * (20 - rightStickSensitivity)) / 1000);
	if(rightStickSensitivity >= 5){
	   	val = 0;
	}
    if(rightStickSensitivity < 5){
	    val = ((rightStickSensitivity - 5) * -4);
	}

}



