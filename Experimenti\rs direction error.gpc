// Variables
int lsAngle;
int rsAngle;
int relativeAngle;
int currentZone = -1; // Initialize to an invalid zone

// Define zone boundaries (degrees)
 int UP_START = 45;
 int UP_END = 135;
 int LEFT_START = 135;
 int LEFT_END = 225;
 int DOWN_START = 225;
 int DOWN_END = 315;
 int RIGHT_START = 315;
 int RIGHT_END = 45;
int newZone;
// Main function for handling right stick skills
function handleRightStickSkills() {
    // Check if right stick button is not pressed and no other inputs are active
    if (!get_val(XB1_RS) && !get_val(XB1_LT) && 
        !get_val(XB1_RT) && !get_val(XB1_RB) && 
        !get_val(XB1_LB)) {
        
        // Check if right stick is pushed beyond threshold
        if (get_polar(POLAR_RS, POLAR_RADIUS) > 2000) {
            
            // Get the angle of the left stick directly without inversion
            lsAngle = get_ipolar(POLAR_LS, POLAR_ANGLE);
            
            // Get the angle of the right stick directly without inversion
            rsAngle = get_ipolar(POLAR_RS, POLAR_ANGLE);
            
            // Calculate the relative angle between RS and LS
            relativeAngle = (rsAngle - lsAngle + 360) % 360;
            
            // Determine which zone the right stick is in relative to the left stick

            if (relativeAngle >= RIGHT_START || relativeAngle < RIGHT_END) {
                newZone = 3; // Right
            }
            else if (relativeAngle >= UP_START && relativeAngle < UP_END) {
                newZone = 0; // Up
            }
            else if (relativeAngle >= LEFT_START && relativeAngle < LEFT_END) {
                newZone = 1; // Left
            }
            else { // Must be DOWN
                newZone = 2; // Down
            }
            
            // If the zone has changed, update the right stick's angle
            if (newZone != currentZone) {
                if (newZone == 0) {
                    move_up();
                }
                else if (newZone == 1) {
                    move_left();
                }
                else if (newZone == 2) {
                    move_down();
                }
                else if (newZone == 3) {
                    move_right();
                }
                currentZone = newZone;
            }
        }
        else {
            // If RS is not pushed beyond threshold, reset currentZone
            currentZone = -1;
        }
    }
    else {
        // If any button is pressed, reset currentZone
        currentZone = -1;
    }
}

// Movement functions adjusted to use lsAngle
function move_up() {
    // Sets RS to lsAngle -90° (opposite direction)
    set_polar(POLAR_RS, (lsAngle - 90 + 360) % 360, 32767);
    // Optional: Trigger a vibration or LED to indicate "Up" action
    // Example:
    // set_vibration(VIBRATOR_1, 100, 100);
}

function move_left() {
    // Sets RS to lsAngle -180° (opposite direction)
    set_polar(POLAR_RS, (lsAngle - 180 + 360) % 360, 32767);
    // Optional: Trigger a vibration or LED to indicate "Left" action
    // Example:
    // set_vibration(VIBRATOR_2, 100, 100);
}

function move_down() {
    // Sets RS to lsAngle -270° (opposite direction)
    set_polar(POLAR_RS, (lsAngle - 270 + 360) % 360, 32767);
    // Optional: Trigger a vibration or LED to indicate "Down" action
    // Example:
    // set_vibration(VIBRATOR_3, 100, 100);
}

function move_right() {
    // Sets RS to lsAngle -0° (same direction)
    set_polar(POLAR_RS, lsAngle % 360, 32767);
    // Optional: Trigger a vibration or LED to indicate "Right" action
    // Example:
    // set_vibration(VIBRATOR_4, 100, 100);
}

// Main loop
main {
    handleRightStickSkills();
}