int dx; 
int dy; 
int a;
int r;
int max_allowed;
int final_r;
// New function: map_circular_octagon_lookup
// Uses the same technique as set_polar2(…, min(radius, 100 * anglesMax[angle % 45]))
function map_circular_octagon_lookup(x, y) {
    // Step 1: Apply the deadzone to each axis.
    // (This is identical to your current technique.)
    dx = apply_one_axis_deadzone(x);
    dy = apply_one_axis_deadzone(y);

    // If both axes are zero, there’s nothing to map.
    if(dx == 0 && dy == 0) {
        set_val(XB1_LX, 0);
        set_val(XB1_LY, 0);
        return;
    }


    // Step 2: Convert the deadzoned values into polar coordinates.
    // (Assuming that get_ipolar(POLAR_LS, …) works on the current left stick state.)
    angle = get_ipolar(POLAR_LS, POLAR_ANGLE);  // Angle in degrees (0–359)
    radius = get_ipolar(POLAR_LS, POLAR_RADIUS);   // Radius (typically 0–MAX_INPUT)

    // Step 3: Determine the maximum allowed radius for this angle.
    // The lookup table "anglesMax" is indexed by (angle mod 45).
    // Multiplying by 100 scales the table to the same range as r.
    max_allowed = 100 * anglesMax[a % 45];

    // Step 4: Clamp the radius to the allowed value.
    if (r > max_allowed) {
        final_r = max_allowed;
    } else {
        final_r = r;
    }

    // Step 5: Output the adjusted values.
    // set_polar2 converts the polar values back to Cartesian outputs.
    set_polar2(POLAR_LS, a, final_r);
}
int LX;
int LY;

int angle;  
int radius;
int anglesMax;
main {
    LX = get_val(XB1_LX);  // Get raw left stick X
    LY = get_val(XB1_LY);  // Get raw left stick Y

    // Use octagon mapping with the polar technique if XB1_LS is active
    if (get_val(XB1_LS)) {
        map_circular_octagon_lookup(LX, LY);
    } else {
        // Otherwise use your existing polar mode
        angle  = get_ipolar(POLAR_LS, POLAR_ANGLE);
        radius = get_ipolar(POLAR_LS, POLAR_RADIUS);
        set_polar2(POLAR_LS, angle, min(radius, 100 * anglesMax[angle % 45]));
    }
}

function intSqrt(value) {
    iSqrtValue = value;
    iSqrtRes = 0;
    iSqrtBit = 1 << 14; // The second-to-top bit is set: 1L<<30 for long

    // "bit" starts at the highest power of four <= the argument.
    while(iSqrtBit > iSqrtValue) {
        iSqrtBit >>= 2;
    }

    while(iSqrtBit != 0) {
        if(iSqrtValue >= iSqrtRes + iSqrtBit) {
            iSqrtValue -= iSqrtRes + iSqrtBit;
            iSqrtRes = (iSqrtRes >> 1) + iSqrtBit;
        } else {
            iSqrtRes >>= 1;
        }
        iSqrtBit >>= 2;
    }
    return iSqrtRes;
}
int sign;
////////////////////////////////////////////////////////////////////////////////
// Function: apply_one_axis_deadzone(val)
//   - Applies a square deadzone on a single axis: [DEADZONE..100] -> [0..100]
////////////////////////////////////////////////////////////////////////////////
function apply_one_axis_deadzone(val) {
    if(val >= 0) {
        sign = 1;
    } else {
        sign = -1;
    }
    abs_val = abs(val);

    // If within ±DEADZONE, zero it out
    if(abs_val <= DEADZONE) return 0;

    // Clamp raw input above 100
    if(abs_val > MAX_INPUT) abs_val = MAX_INPUT;

    // Scale from (DEADZONE..MAX_INPUT) -> (0..MAX_INPUT)
    output = ((abs_val - DEADZONE) * MAX_INPUT) / (MAX_INPUT - DEADZONE);
    return sign * output;
}
