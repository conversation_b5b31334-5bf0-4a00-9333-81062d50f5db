// Square Dead Zone Mapping for (Left Stick)

int LX, LY;  // Left stick X and Y input values
int scaled_x, scaled_y;  // Variables to hold scaled X and Y values
int magnitude, scale;  // Variables for magnitude and scale factor
int DEADZONE = 80;  // Size of deadzone
int MAX_INPUT = 100;  // Max input value 

// Main function
main {
    // Get the stick inputs (for left joystick X and Y axes)
    LX = get_val(PS4_LX);  // Left stick X-axis input
    LY = get_val(PS4_LY);  // Left stick Y-axis input

    // Apply square stick mapping and capture the returned values
    map_to_square(LX, LY);

    // Set the adjusted values back to the controller
    set_val(PS4_LX, scaled_x);
    set_val(PS4_LY, scaled_y);
}

// Function to map stick inputs to a square dead zone area
function map_to_square(int x, int y) {
    // Calculate the magnitude of the input 
    magnitude = isqrt(x * x + y * y);  // Using proper magnitude calculation

    // If the magnitude is greater than the dead zone threshold, apply the scaling logic
    if (magnitude > DEADZONE) {  // Apply the dead zone threshold
        // Calculate scale factor based on maximum input range
        if (magnitude > MAX_INPUT) {
            scale = MAX_INPUT;
        } else {
            scale = magnitude;
        }
        
        // Apply deadzone compensation
        scale = (scale - DEADZONE) * MAX_INPUT / (MAX_INPUT - DEADZONE);
        
        // Scale both X and Y values proportionally
        if (magnitude > 0) {  // Prevent division by zero
            scaled_x = (x * scale) / magnitude;
            scaled_y = (y * scale) / magnitude;
        }
    } else {
        // If magnitude is within the dead zone, set scaled values to 0 (no movement)
        scaled_x = 0;
        scaled_y = 0;
    }
}