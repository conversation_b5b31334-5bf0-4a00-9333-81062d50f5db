// Star of Lakshmi Movement Script
// Eight-pointed star with equal points

// Configuration
define MAX_VAL = 100          // Maximum stick value
define DEADZONE = 10          // Stick deadzone
define ANGLE_STEP = 6         // Movement speed (not used in this version)
define POINT_COUNT = 8       // Number of points
define INNER_RADIUS = 60      // Inner radius (valley between points)
define OUTER_RADIUS = 100     // Outer radius (point tips)

// Global variables
int stick_x;
int stick_y;
int out_x;
int out_y;
int current_angle; // Not used in this version
int magnitude;
int segment;
int progress;
int base_angle;
int segment_angle;
int segment_progress;
int radius;
int angle_rad; // Used for GPC angle format in calc_star_position

// Global variables moved from functions (keeping them global as per previous feedback)
int angle; // Not used in this version
int abs_x; // Not used in this version
int abs_y; // Not used in this version
int input_angle_gpc; // Not used in this version


// Cosine function using sine with phase shift
function icos(x) { return isin(x + 8192); }

// Fixed-point sine function
function isin(x) {
   x = (x % 32767) << 17;
   if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
   x = x >> 17;
   return x * ((98304) - (x * x) >> 11) >> 14;
}

// Function to calculate position on the star based on angle (0-360) and magnitude (0-100)
function calc_star_position(int angle_deg, int mag) {
   // Calculate base angle within 360 degrees
   base_angle = angle_deg % 360;
   if(base_angle < 0) base_angle += 360;
   
   // Calculate segment (0-7) and progress within segment
   segment = (base_angle * POINT_COUNT) / 360;
   segment_angle = base_angle - (segment * 360 / POINT_COUNT);
   
   // Calculate radius based on angle within segment
   segment_progress = (segment_angle * 100) / (360 / POINT_COUNT);
   
   if(segment_progress <= 50) {
       // First half - going from outer to inner
       radius = OUTER_RADIUS - ((OUTER_RADIUS - INNER_RADIUS) * segment_progress * 2) / 100;
   } else {
       // Second half - going from inner to outer
       segment_progress = segment_progress - 50;
       radius = INNER_RADIUS + ((OUTER_RADIUS - INNER_RADIUS) * segment_progress * 2) / 100;
   }
   
   // Scale radius by input magnitude
   radius = (radius * mag) / 100;
   
   // Convert angle to GPC's angle format (0-32767 represents 0-2π)
   angle_rad = (base_angle * 32767) / 360;
   
   // Convert polar coordinates to cartesian using fixed-point trig
   out_x = (radius * icos(angle_rad)) / 32767;
   out_y = (radius * isin(angle_rad)) / 32767;
}

// Removed get_stick_angle_gpc as asin is not defined


init {
   current_angle = 0; // Not used in this version
}
int input_angle_deg;
main {
   // Get stick input
   stick_x = get_val(XB1_LX);
   stick_y = get_val(XB1_LY);
   
   // Calculate magnitude
   magnitude = isqrt(stick_x * stick_x + stick_y * stick_y);
   
   if(magnitude > DEADZONE) {
       // Normalize magnitude to 0-100 range
       magnitude = (magnitude * 100) / MAX_VAL;
       if(magnitude > 100) magnitude = 100;
       
       // Calculate input stick angle in degrees (0-360) based on octant
       input_angle_deg = 0;
       if (stick_x > 0 && stick_y == 0) input_angle_deg = 0; // Right
       else if (stick_x > 0 && stick_y > 0) input_angle_deg = 45; // Up-Right
       else if (stick_x == 0 && stick_y > 0) input_angle_deg = 90; // Up
       else if (stick_x < 0 && stick_y > 0) input_angle_deg = 135; // Up-Left
       else if (stick_x < 0 && stick_y == 0) input_angle_deg = 180; // Left
       else if (stick_x < 0 && stick_y < 0) input_angle_deg = 225; // Down-Left
       else if (stick_x == 0 && stick_y < 0) input_angle_deg = 270; // Down
       else if (stick_x > 0 && stick_y < 0) input_angle_deg = 315; // Down-Right
       // If stick is exactly at (0,0), angle remains 0, handled by deadzone
       
       // Calculate position on star based on input angle and magnitude
       calc_star_position(input_angle_deg, magnitude);
       
       // Apply calculated position
       set_val(XB1_LX, out_x);
       set_val(XB1_LY, out_y);
   } else {
       // Center stick when in deadzone
       set_val(XB1_LX, 0);
       set_val(XB1_LY, 0);
   }
}