define Angle_Time = 10;
define Angle_Speed_Multipler = 3;
define HOLD_TIME = 1000;

int final_angle;
int direction;
int radius;
int cur_angle;
int angle_timer;
int angle_out;
int hold_timer;

main {
if(event_press(XB1_LB) && get_ipolar(POLAR_LS,POLAR_RADIUS) >= 1500) {
    cur_angle = atan2_angle(get_ival(POLAR_LX),get_ival(POLAR_LY));
    if ((cur_angle >= 0 && cur_angle < 90) || (cur_angle >= 180 && cur_angle < 270)) {
      final_angle = (cur_angle + 90) % 360;
      direction = Angle_Speed_Multipler;
    } else {
      final_angle = cur_angle - 90;
      if (final_angle < 0) final_angle += 360;
      direction = -Angle_Speed_Multipler;
    }

    radius = min(isqrt(pow(get_ival(POLAR_LX),2) + pow(get_ival(POLAR_LY),2)),32767);
  }

  if (hold_timer < HOLD_TIME) {
    hold_timer += get_rtime();
    set_polar(POLAR_LS, cur_angle, radius);
  } 
  else {
    if(abs(cur_angle - final_angle) >= Angle_Speed_Multipler) {
      angle_timer += get_rtime();
      if(angle_timer >= Angle_Time) {
        cur_angle += direction;
        if(cur_angle < 0)
          cur_angle += 360;
        if(cur_angle >= 360)
          cur_angle -= 360;
        angle_timer = 0;

        if (abs(cur_angle - final_angle) < Angle_Speed_Multipler) {
          hold_timer = 0;
        }
      }
      set_polar(POLAR_LS,cur_angle,radius);
    }
  }
}
function atan2_angle(f_x,f_y) {
  if(f_x >= 0 && f_y > 0)
    angle_out = -90000;
  else if(f_x < 0 && f_y >= 0)
    angle_out = 90000;
  else if(f_x <= 0 && f_y < 0)
    angle_out = -270000;
  else angle_out = 270000;
  f_x = abs(f_x);
  f_y = abs(f_y);
  if(f_x < f_y)
    angle_out += (f_x * 45000 / f_y);
  else if(f_x > f_y)
    angle_out += 90000 - (f_y * 45000 / f_x);
  else angle_out += 45000;
  return abs(angle_out) / 1000;
} 