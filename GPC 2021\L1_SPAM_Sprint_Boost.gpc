

int L1_SPboost_Trigger =      //USER Defined <PERSON>tton TO be hold//

main {


  if(get_val(L1_SPboost_Trigger) ){
    set_val(SprintBtn,100);
    combo_run(L1_Spam_Sprint_boost);  
  }
   
  if( event_release(L1_SPboost_Trigger)){combo_stop(L1_Spam_Sprint_boost); }
 
 
  }
  
  combo L1_Spam_Sprint_boost {
sensitivity(PS4_LX, NOT_USE, 110);
sensitivity(PS4_LY, NOT_USE, 110);
set_val(L1_SPboost_Trigger,0);
set_val(PlayerRun,100):
wait(300);
sensitivity(PS4_LX, NOT_USE, 110);
sensitivity(PS4_LY, NOT_USE, 110);
set_val(L1_SPboost_Trigger,0);
set_val(PlayerRun,0):
wait(120);
}