
int Where_am_I; // Variable to store the polar angle value
int previous_direction = 0; // Variable to store the previous direction (0: none, 1: left, 2: right)
define LEFT_ai = 1; // Constant to represent the left direction
define RIGHT_ai = 2; // Constant to represent the right direction

const string INFO1  = "GAMESIR4ELITE";
const string INFO2 = "feint RB LB";
const string LEFTP  = "PING";
const string RIGHTP = "VM SPEED";
int modMenu,editMenu; 
int modNameIdx,valNameIdx;
int case_indic = 0;

/* Display Variables / ScreenSaver / Strings/Text  */
int screenSaver,blankScreen;
int displayTitle = TRUE;
int updateDisplay;

define Off = 0;
define Dim_Blue = 1;
define Dim_Red = 2;
define Dim_Green = 3;
define Dim_Pink = 4;
define Dim_SkyBlue = 5;
define Dim_Yellow = 6;
define Dim_White = 7;
define Blue = 8;
define Red = 9;
define Green = 10;
define Pink = 11;
define SkyBlue = 12;
define Yellow = 13;
define White = 14;
define Bright_Blue = 15;
define Bright_Red = 16;
define Bright_Green = 17;
define Bright_Pink = 18;
define Bright_SkyBlue = 19;
define Bright_Yellow = 20;
define Bright_White = 21;

define MOVE_X        = PS4_LX;        
define MOVE_Y        = PS4_LY;        
define SKILL_STICK_X = PS4_RX;        
define SKILL_STICK_Y = PS4_RY;  

data (
 
0,0,0,0, // Off
1,0,0,0, // Dim Blue
0,1,0,0, // Dim Red
0,0,1,0, // Dim Green
0,0,0,1, // Dim Pink
1,0,1,0, // Dim SkyBlue
0,1,1,0, // Dim Yellow
1,1,1,1, // Dim White
2,0,0,0, // Blue
0,2,0,0, // Red
0,0,2,0, // Green
0,0,0,2, // Pink
2,0,2,0, // SkyBlue
0,2,2,0, // Yellow
2,2,2,2, // White
3,0,0,0, // Bright Blue
0,3,0,0, // Bright Red
0,0,3,0, // Bright Green
0,0,0,3, // Bright Pink
3,0,3,0, // Bright SkyBlue
0,3,3,0, // Bright Yellow
3,3,3,3  // Bright white
 
); 

//define AngleInterval = 8; // (360 / 15 Degrees) = 24
//define AngleInterval_2 = 12;

define Stick_Press_LB_Threshold = 95;
int stick_swap_toggle;
int rightStickMagnitude;
int UltimatePower;
int DYN_Acc;

int virtmach = -6;
int VM_VALUES[5];
int VM_VALUES_COUNT;
int current_index = 0;

init {
    VM_VALUES[0] = -9;
    VM_VALUES[1] = -8;
    VM_VALUES[2] = -6;
    VM_VALUES[3] = -2;
    VM_VALUES[4] = 0;
    VM_VALUES_COUNT = 5;
}

init {
    VM_VALUES_COUNT = sizeof(VM_VALUES) / sizeof(VM_VALUES[0]);
}

int tbp_value = 380;
int fs_value = 20;
int dd_value = 35;
//const string dd = "dd_value"; 

function icos(x) { return isin(x + 8192); }
function isin(x) {
	x = (x % 32767) << 17;
	if((x ^ (x * 2)) < 0) { x = (-2147483648) - x;}
	x = x >> 17;
	return x * ((98304) - (x * x) >> 11) >> 14;
}

int toggle_active;
int cos_angle, sin_angle;
	// Increase the frequency of peaks and dips
	int multiplier = 24;
function set_polar_dd(stick, angle, radius, dd_factor){
	dd_factor = (dd_factor * 32767) / 80;
	radius = (radius * 32767) / 10000;
	angle = (360 - angle) * 91;
	sin_angle = isin(angle); 
	cos_angle = icos(angle);
	angle = (angle * multiplier) % 32767;
	// Adjusted radius calculation for more peaks and dips
	radius = radius * (32767 - ((angle * dd_factor) >> 15)) >> 15;
	set_val(42 + stick, clamp((radius * cos_angle) >> 15, -32767, 32767));
	set_val(43 + stick, clamp((radius * sin_angle) >> 15, -32767, 32767));
	return;
}

function getPolar(Stick, AngleOrRadius) {
  if (AngleOrRadius) return 360 - get_polar(Stick, POLAR_ANGLE);
  return isqrt((get_val(Stick + 42) * get_val(Stick + 42)) + (get_val(Stick + 43) * get_val(Stick + 43)));   
}

int r, a, dd;

int Stop_Detector;
int Stop_Ran = 0;  // Integer flag to check if combo has already been run
int leftStickRadius;
int rightStickRadius;
int targetRadius;
int baseRadius = 28701;
int randomVariation;
int fullRadius = 32767;
int turboBtn;

define ShotBtn       = XB1_B; // Shot Btn         (default B/CIRCLE 
define PassBtn       = XB1_A; // Short Pass Btn   (default A/CROSS)
define PlayerRun     = XB1_RB; // Player Run       (default L1/LB) 
define FinesseShot   = XB1_LB; // Finesse Shot     (default R1/RB)
define PaceCtrol     = XB1_LT; // Protect Ball     (default L2/LT)
define SprintBtn     = XB1_RT; // Sprint Btn       (default R2/RT)
define CrossBtn      = XB1_X; // Cross Btn        (default X/SQUARE)
define ThroughBall   = XB1_Y; // Through Ball Btn (default Y/TRIANGLE) 
int LS_Angle;

int combo_active;

define AngleInterval = 8; // Changed from 24 to 30 for 12 zones
define AngleInterval_2 = 12; // Changed from 12 to 30 for 12 zones
define RADIUS_THRESHOLD_1 = 3000;
define RADIUS_THRESHOLD_2 = 6000;
define RADIUS_THRESHOLD_2A = 9000;
define RADIUS_THRESHOLD_3 = 11000;
define MAX_RADIUS = 32767;
int radius;


function pass() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);

set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
            min(calculate_radius(), MAX_RADIUS)
        );
}


function messi() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);

    // Condition 1: when radius < RADIUS_THRESHOLD_1
    if (radius < RADIUS_THRESHOLD_1) {
        set_val(XB1_LS, 100);set_val(XB1_RB, 100);
        //set_val(XB1_LT, 100);
        //set_polar(POLAR_LS, 0, 0);
    }
    // Condition 2: when RADIUS_THRESHOLD_1 <= radius < RADIUS_THRESHOLD_2
    else if (radius < RADIUS_THRESHOLD_2) {
        set_val(XB1_LS, 100)
        set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval),
            min(calculate_radius(), MAX_RADIUS)
        );
    }
    // Condition 3: when radius >= RADIUS_THRESHOLD_2
    else {
    set_val(XB1_LS, 100)
        set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
            min(calculate_radius(), MAX_RADIUS)
        );
    }
}
function messi2() {
    // Store the radius value to avoid repeated function calls
    radius = get_polar(POLAR_LS, POLAR_RADIUS);

    // Condition 1: when radius < RADIUS_THRESHOLD_1
    if (radius < RADIUS_THRESHOLD_1) {
    set_val(XB1_LS, 100)
        set_val(XB1_RB, 100);
        //set_val(XB1_LT, 100);
        //set_polar(POLAR_LS, 0, 0);
    }
    // Condition 2: when RADIUS_THRESHOLD_1 <= radius < RADIUS_THRESHOLD_2
    else if (radius < RADIUS_THRESHOLD_2) {
    set_val(XB1_LS, 100)
        set_val(XB1_RB, 100);set_val(XB1_LB, 100);
        set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval),
            min(calculate_radius(), MAX_RADIUS)
        );
    }
    // Condition 2A: when RADIUS_THRESHOLD_1 <= radius < RADIUS_THRESHOLD_2
    else if (radius < RADIUS_THRESHOLD_2A) {
    set_val(XB1_LS, 100)
        set_val(XB1_LB, 100);
        set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval),
            min(calculate_radius(), MAX_RADIUS)
        );
    }
    // Condition 3: when radius >= RADIUS_THRESHOLD_2
    else {
    set_val(XB1_LS, 100)
        set_polar(POLAR_LS, 
            quantize_angle(get_polar(POLAR_LS, POLAR_ANGLE), AngleInterval_2),
            min(calculate_radius(), MAX_RADIUS)
        );
    }
}

// Helper function to quantize the angle
function quantize_angle(angle, interval) {
    return (((inv(angle) * interval) / 360) * 360) / interval;
}

// Helper function to calculate the radius
function calculate_radius() {
    return isqrt(pow(get_val(POLAR_LX), 2) + pow(get_val(POLAR_LY), 2));
}

int pressTime = 0;
int comboStage = 0;


main {

    // Get the polar angle value of the left joystick and normalize it to 0-360 degrees
    Where_am_I = (get_ipolar(POLAR_LS, POLAR_ANGLE) + 360) % 360;

    // Check if the joystick is moved significantly in any direction
    if (abs(get_val(PS4_LX)) > 50 || abs(get_val(PS4_LY)) > 50) {
        // Determine direction based on polar angle
        if (Where_am_I > 90 && Where_am_I < 270) {
            // Current direction is left
            if (previous_direction != LEFT_ai) {
                // Check if none of the specified buttons are pressed
                if (!get_val(XB1_LB) && !get_val(XB1_RB) && !get_val(XB1_RT) && !get_val(XB1_LT)) {
                    combo_run(R2_at_direction_change);
                    previous_direction = LEFT_ai;
                }
            }
        }
        else {
            // Current direction is right
            if (previous_direction != RIGHT_ai) {
                // Check if none of the specified buttons are pressed
                if (!get_val(XB1_LB) && !get_val(XB1_RB) && !get_val(XB1_RT) && !get_val(XB1_LT)) {
                    combo_run(R2_at_direction_change);
                    previous_direction = RIGHT_ai;
                }
            }
        }
    }
    else {
        // Joystick is not being significantly used
        previous_direction = 0;
    }

    // Optional: For debugging purposes, you can trace the current direction
    set_val(TRACE_1, previous_direction);

set_val(XB1_SHARE,0);
 pass();
    if(get_val(XB1_A)){set_val(XB1_LS, 100);
        pass();
    }
    
        if(get_val(XB1_Y)){set_val(XB1_LS, 100);
        pass();
    }

        if(get_val(XB1_X)){set_val(XB1_LS, 100);
        pass();
    }

    if(get_val(XB1_PR1)){
        messi();
    }

 //   if(get_val(XB1_PR2)){ messi2();}
    
    
     if(get_val(XB1_B)){
         set_val(XB1_LS, 100);
    }   
   
/*
    if (get_val(XB1_PR2)) {
        messi2();
    } else {
        if (get_val(XB1_A)) {
            pressTime += get_rtime();



            if (pressTime <= 250) {
                set_val(XB1_A, 100);
                set_val(XB1_LB, 0);
            } 
            else if (pressTime > 250 && pressTime <= 380) {
                set_val(XB1_A, 100);
                set_val(XB1_LB, 100);
            }
            else {
                set_val(XB1_A, 0);
                set_val(XB1_LB, 0);
            }
            
            if (comboStage == 0 && pressTime > 0) {
                comboStage = 1;
                combo_run(ButtonPress);
            }
        } else {
            pressTime = 0;
            comboStage = 0;
            set_val(XB1_A, 0);
            set_val(XB1_LB, 0);
        }
    }

    if (get_val(XB1_PR1)) {
        messi();
    } else {
        if (get_val(XB1_A)) {
            pressTime += get_rtime();
            
            if (pressTime <= 250) {
                set_val(XB1_A, 100);
                set_val(XB1_LB, 0);
            } 
            else if (pressTime > 250 && pressTime <= 380) {
                set_val(XB1_A, 100);
                set_val(XB1_LB, 100);
            }
            else {
                set_val(XB1_A, 0);
                set_val(XB1_LB, 0);
            }
            
            if (comboStage == 0 && pressTime > 0) {
                comboStage = 1;
                combo_run(ButtonPress);
            }
        } else {
            pressTime = 0;
            comboStage = 0;
            set_val(XB1_A, 0);
            set_val(XB1_LB, 0);
        }
    }
 */   
    
    /*
    if(get_val(XB1_PR2)){
        messi2();
    }
*/
    if (get_val(XB1_RT)) {
        set_val(XB1_RB, 0);
    }

if(get_ival(XB1_X)) {
  if(get_ptime(XB1_X) < 250) {
    // Keep XB1_X active for presses under 250ms
    set_val(XB1_X, 100);set_val(XB1_LS, 100);
  }
  else if(get_ptime(XB1_X) >= 250 && get_ptime(XB1_X) <= 380) {
    // For presses between 250ms and 380ms, activate both XB1_X and XB1_RB
    set_val(XB1_X, 100);
    set_val(XB1_RB, 100);set_val(XB1_LS, 100);
  }
  else {
    // For presses over 380ms, deactivate both buttons
    set_val(XB1_X, 0);
    set_val(XB1_RB, 0);
  }
}
else {
  // When XB1_X is released, reset buttons
  set_val(XB1_X, 0);
  //set_val(XB1_RB, 0);
}

if(get_ival(XB1_Y)) {
  if(get_ptime(XB1_Y) < 250) {
    // Keep XB1_X active for presses under 250ms
    set_val(XB1_Y, 100);set_val(XB1_LS, 100);
  }
  else if(get_ptime(XB1_Y) >= 250 && get_ptime(XB1_Y) <= 380) {
    // For presses between 250ms and 380ms, activate both XB1_X and XB1_RB
    set_val(XB1_Y, 100);
    set_val(XB1_RB, 100);set_val(XB1_LS, 100);
  }
  else {
    // For presses over 380ms, deactivate both buttons
    set_val(XB1_Y, 0);
    set_val(XB1_RB, 0);
  }
}
else {
  // When XB1_X is released, reset buttons
  set_val(XB1_Y, 0);
  //set_val(XB1_RB, 0);
}





if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_LEFT)) {
             load_slot (2);
      }
      set_val(XB1_LEFT,0);
	}
	if (get_val(XB1_VIEW) ) {
      if(event_release(XB1_RIGHT)) {
             load_slot (2);
      }
      set_val(XB1_RIGHT,0);
	}

  if(get_val(XB1_PR2)) {set_val(XB1_PR2,0);combo_run(TEST_RL);}
if(get_val(XB1_PL2)) {set_val(XB1_PL2,0);combo_run(TEST_LR);}
if(get_val(XB1_RS)) {set_val(XB1_RS,0);combo_run(TEST);}

  //if(get_val(XB1_PL1)){set_val(XB1_LB,100);set_val(XB1_LS, 100);}
//if(get_val(XB1_RS)){combo_run(pressing);
	





   if (get_val(XB1_PR1)  && get_ptime(XB1_PR1) < 300) {
    set_val(XB1_RB,100);
    }
    else if (get_val(XB1_PR1)  && get_ptime(XB1_PR1) >= 300) {
        //set_val(XB1_LB,100);
    }


/*===============================================================
 XB1_PL1 
=================================================================
*/


   

//	if(get_val(XB1_PL2)) {combo_run(FAKE_SHOT);set_val(XB1_LB,100);set_val(XB1_LS, 100);}

 
    if (get_polar(POLAR_LS, POLAR_RADIUS) < 2400) {
        set_polar(POLAR_LS, 0, 0);
    }
    else {
    // LS_Angle = (360 - get_polar(POLAR_LS,POLAR_ANGLE)); //get LS
 //set_polar(POLAR_LS,LS_Angle ,32767); // set RS
        leftStickRadius = getPolar(POLAR_LS, POLAR_ANGLE);
        set_val(TRACE_1, leftStickRadius);

        // Check for different button conditions
        if (get_val(XB1_LB)) {
            // Generate a random variation between 1% and 5% for baseRadius
            randomVariation = random(287, 1435); // 1% to 5% of 28701
            
            // Randomly decide whether to add or subtract the variation
            if (random(0, 1) == 0) {
                targetRadius = baseRadius + randomVariation;
            } else {
                targetRadius = baseRadius - randomVariation;
            }
            
            // Ensure targetRadius stays within reasonable bounds
            targetRadius = clamp(targetRadius, 27266, 30136); // ±5% of 28701
            
            // Only modify XB1_RT if it's also being pressed
            if (get_val(XB1_RT)) {
                set_val(XB1_RT, 80);
            }
        }
        else if (get_val(XB1_RT)) {
            // Full radius when XB1_LT alone is pressed
            targetRadius = fullRadius;
        }
        else {
            // Default case: Apply 1-5% reduction to fullRadius
            randomVariation = random(983, 9294); // 3% to 7% of 32767
            targetRadius = fullRadius - randomVariation;
        }

        set_polar(POLAR_LS, leftStickRadius, targetRadius);
    }
    
    
/*===============================================================
 RIGHT STICK set LS off  when >< 2400
=================================================================

/*
   if (get_polar(POLAR_RS, POLAR_RADIUS) > 2400) {
    set_polar(POLAR_LS, 0, 0);
    set_val(XB1_LB, 0);
   }
  if (get_polar(POLAR_RS, POLAR_RADIUS) < 2400) {
    set_polar(POLAR_RS, 0, 0);
   }
    rightStickRadius  = getPolar(POLAR_RS,POLAR_ANGLE);
    set_val(TRACE_2,rightStickRadius);
    set_polar(POLAR_RS,rightStickRadius,32767);
   
/*===============================================================
 toogle mod menu
=================================================================
*/  

   if(toggle_active)        set_ds4_led(Green); 
   else if(!toggle_active)   set_ds4_led(Red);
   

    if(!modMenu && !editMenu){
        // Display The Title Screen When we Are NOT in any Menu s
        if(displayTitle){ 
            cls_oled(0);
            displayTitle = FALSE;
            screenSaver  = TRUE;
            print(centerPosition(getStringLength(INFO1[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , INFO1[0]);
            print(centerPosition(getStringLength(INFO2[0]) ,OLED_FONT_SMALL_WIDTH), 18  ,OLED_FONT_SMALL , OLED_WHITE , INFO2[0]);
        }
        }
        
vm_tctrl(virtmach)

   // LED_Color(Blue);
 if(time_to_clear_screen){
    time_to_clear_screen -= get_rtime();
    if(time_to_clear_screen <= 0)combo_run(CLEAR_SCREEN);
 }
    if(get_ival(XB1_RT)){
        if(event_press(XB1_RIGHT)) {
            current_index = (current_index + 1) % VM_VALUES_COUNT;
            virtmach = VM_VALUES[current_index];
            print(centerPosition(getStringLength(RIGHTP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, RIGHTP[0]);
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), virtmach[0], virtmach);
        }
        if(event_press(XB1_LEFT)) {
            current_index = (current_index - 1 + VM_VALUES_COUNT) % VM_VALUES_COUNT;
            virtmach = VM_VALUES[current_index];
            print(centerPosition(getStringLength(RIGHTP[0]), OLED_FONT_SMALL_WIDTH), 6, OLED_FONT_SMALL, OLED_WHITE, RIGHTP[0]);
            on_the_fly_display(centerPosition(1, OLED_FONT_MEDIUM_WIDTH), virtmach[0], virtmach);
        }
        set_val(PS4_RIGHT, 0);
        set_val(PS4_LEFT, 0);
    }

            if(get_ival(XB1_LT)){                              
	            if(event_press(XB1_RIGHT)) {
	                fs_value +=1;
	                print(centerPosition(getStringLength( LEFTP[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFTP[0]);
	                on_the_fly_display(centerPosition(sizeof(fs_value)- 1,OLED_FONT_MEDIUM_WIDTH),fs_value[0],fs_value);
            	}
            	if(event_press(XB1_LEFT)) {
	                fs_value -=1;
	                print(centerPosition(getStringLength( LEFTP[0]) ,OLED_FONT_SMALL_WIDTH), 6  ,OLED_FONT_SMALL , OLED_WHITE , LEFTP[0]);
	                on_the_fly_display(centerPosition(sizeof(fs_value)- 1,OLED_FONT_MEDIUM_WIDTH),fs_value[0],fs_value);
                }
                set_val(PS4_RIGHT,0);
                set_val(PS4_LEFT ,0);
            }
            
            if(get_ival(XB1_LB)){                              
	            if(event_press(XB1_RIGHT)) {
	                tbp_value +=10;
	                on_the_fly_display(centerPosition(sizeof(tbp_value)- 10,OLED_FONT_MEDIUM_WIDTH),tbp_value[0],tbp_value);
            	}
            	if(event_press(XB1_LEFT)) {
	                tbp_value -=10;
	                on_the_fly_display(centerPosition(sizeof(tbp_value)- 10,OLED_FONT_MEDIUM_WIDTH),tbp_value[0],tbp_value);
                }
                set_val(PS4_RIGHT,0);
                set_val(PS4_LEFT ,0);
            }

/*===============================================================
 PASSES, SHOTS
=================================================================
*/
  
	// Finesse LB+B
	if(get_val(XB1_LB) && get_val(XB1_B)) {
			combo_run(finesse);set_val(XB1_LS, 100);
		}

	// Finesse with R3
	if (!get_val(XB1_LT)) {
	if(get_val(XB1_RS)){
	set_val(XB1_RS,0);
	UltimatePower = random(265,270);
	//DYN_Acc = random(130,135);
	set_val(XB1_B,0);
	combo_run(OutSideBox_Finishing_cmb);
		}
	}

  




/*
  if(get_ival(XB1_X) && get_ptime(XB1_X) >= 380) {
    set_val(XB1_X, 0);set_val(XB1_RB, 100);
    combo_run(Turbo_CROSS);
  }
  else combo_stop(Turbo_CROSS);
*/
	// Shots with B
	if (!get_val(XB1_LT)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB);
	
	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 250) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB);set_val(XB1_LS, 100);
		}
	}
	// Shots with RB+B
	if (get_val(XB1_RB)) {
	if (event_release(XB1_B) && get_ptime(XB1_B) <= 99) {
		combo_run(TapB180);set_val(XB1_LS, 100);

	} else if (get_ival(XB1_B) && get_ptime(XB1_B) >= 180) {	
		set_val(XB1_B,0);
		if (event_press(XB1_B)) combo_run(PressB99);set_val(XB1_LS, 100);
		}
	}
/*
	if(get_val(XB1_LT) && get_val(XB1_RT)) {
        combo_run(pressing);
    } else if(combo_running(pressing)) {
        combo_stop(pressing);
    }
*/
//}
   // else {LED_Color(Green); }
   
/*===============================================================
 END OF MAIN
=================================================================
*/   
}	// End of main

combo FAKE_SHOT { 

	set_val(ShotBtn,100);
	  vm_tctrl(0);wait(40);              
	set_val(ShotBtn,100);  
	set_val(PassBtn,100); 
	  vm_tctrl(0);wait(60);             
	set_val(ShotBtn,0);  
	set_val(PassBtn,100);
	  vm_tctrl(0);wait(60);           
    Get_LS_Output = TRUE;
    vm_tctrl(0);wait(600); 
}  

combo FAKESHOTCANCEL {
	// 52
	set_val(PS5_CIRCLE, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(PS5_CIRCLE, 100);
	set_val(PS5_CROSS, 100);
	vm_tctrl(0);
	wait( 60);
	set_val(PS5_CIRCLE, 0);
	set_val(PS5_CROSS, 100);
	vm_tctrl(0);
	wait( 60);
	vm_tctrl(0);
	wait( 140);
	set_val(PS5_L2, 100);
	set_val(PS5_R2, 100);
	vm_tctrl(0);
	wait( 100);
	  
	vm_tctrl(0);
	wait( 350);
	}

combo Turbo_THRUPASS {
  set_val(XB1_Y, 0);
  wait(20);
  wait(20 - get_rtime());
}

combo Turbo_CROSS {

  set_val(XB1_X, 0);
  wait(20);
  wait(20 - get_rtime());
}

combo glichshot {
	set_val(XB1_Y, 100);
	//wait(tbp_value);
	wait(360);
	set_val(XB1_Y, 100);
	set_val(XB1_LB, 100);

	wait(60);

	set_val(XB1_Y, 0);
	set_val(XB1_LB, 0);
	wait(20);
}

// Optional: A combo to provide some form of feedback that the toggle has occurred
combo Feedback {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(400);
    reset_rumble();
}

combo Feedback2 {
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    set_rumble(RUMBLE_A, 0);  // Vibrate controller briefly
    wait(100);
    set_rumble(RUMBLE_A, 100);  // Vibrate controller briefly
    wait(200);
    reset_rumble();
}

combo banks {
	set_val(XB1_A, 100);
	combo_run(pressing);
}

combo PressB {
	set_val(XB1_B, 100);set_val(XB1_LS, 100);
	wait(100);
}

combo TapB {
	set_val(XB1_B, 0);
	wait(250);
}

// Combo definition
combo hold_x {

  set_val(XB1_LB, 0);
  set_val(XB1_RB, 100);
  set_val(XB1_X, 100);

  // Wait for 400ms
  wait(390);
  wait(50);
  // Release XB1_X

}

combo finesse {
	set_val(XB1_B, 100);
	wait(250);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo pressing {
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(50);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 0);
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 65);
	set_val(XB1_LB, 100);
	wait(440);
	set_val(XB1_LB, 100);
	wait(80);
	set_val(XB1_LB, 0);
	wait(40);
	set_val(XB1_LB, 100);
	wait(300);
	set_val(XB1_LB, 0);
	wait(250);  
}

	combo FAKE_SHOT_CANCEL_cmb {        
    set_val(ShotBtn,100);  
      vm_tctrl(0);wait(40);              
    set_val(ShotBtn,100);  
    set_val(PassBtn,100); 
      vm_tctrl(0);wait(60);             
    set_val(ShotBtn,0);  
    set_val(PassBtn,100);
      vm_tctrl(0);wait(60);
      vm_tctrl(0);wait(140);
    set_val(PS4_L2,100);
    set_val(PS4_R2,100);
      vm_tctrl(0);wait(100);
    //Get_LS_Output = TRUE;
} 

combo CHIP_SHOT {
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	set_val(PS4_L3,100);
	wait( 80);
	set_val(XB1_B,100);
	set_val(XB1_RB,100);
	wait(100);    
}

combo PressB99 {
	set_val(XB1_B, 100);
	set_val(PS4_L3,100);
	wait(100);
}

combo TapB180 {
	set_val(XB1_B, 0);
	set_val(PS4_L3,100);
	wait(180);
}

combo Press_LB {
	set_val(XB1_LB, 100);
	set_val(XB1_RB, 100);
	wait(100);
	wait(20);
	set_val(XB1_A, 100);
	wait(200);
	wait(20);
}

combo OutSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo PS_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	//set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	//set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 220 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo InSideBox_Finishing_cmb { 
	set_val(XB1_B, 0);
	set_val(XB1_LT, 0);
	set_val(PS4_L3,0);
	INSIDE_BOX_AIM(37,100);
	wait(160);
	INSIDE_BOX_AIM(37,100);
	set_val(PS4_L3,0);
	//set_val(XB1_LB, 100);
	set_val(XB1_B, 100); 
	wait(UltimatePower); ///// 
	INSIDE_BOX_AIM(37,100);
	//set_val(XB1_LB, 100);
	set_val(XB1_B, 0);
	wait( 170 + fs_value);
	set_val(XB1_B, 100);
	wait(70);
}

combo Stop_my {
set_val(TRACE_1, 100);
	set_val(XB1_RT, 100);
	set_val(XB1_LX, 0);
	set_val(XB1_LY, 0);
		wait(100);
	set_val(XB1_RT, 0);
	set_val(XB1_LX, 0);
	set_val(XB1_LY, 0);
		wait(60);
	set_val(XB1_RT, 100);
	set_val(XB1_LX, 0);
	set_val(XB1_LY, 0);
		wait(100);
		//set_val(XB1_LT, 100);
		//wait(500);
}

combo REVERSE_STEP_OVER {      
	RA_L_R ();       // <-/->   
	  vm_tctrl(0);wait(w_rstick);             
     RA_UP ();         // up   
	  vm_tctrl(0);wait(w_rstick);             
    Get_LS_Output = TRUE;
}   

combo STEP_OVER_FEINT {      
    RA_UP ();         // up   
	  vm_tctrl(0);wait(w_rstick);             
	RA_L_R ();       // <-/->   
	  vm_tctrl(0);wait(w_rstick); 
	  //vm_tctrl(0);wait(300);
    Get_LS_Output = TRUE;
}    

combo BAL_ROLL_CUT_180_cmb  {  
	set_val(PlayerRun,100);   
	RA_DOWN ();    // down
	  vm_tctrl(0);wait(w_rstick);      
	set_val(PlayerRun,100);   
	RA_ZERO ();    // zero
	  vm_tctrl(0);wait(w_rstick);      
	set_val(PlayerRun,100);   
	RA_DOWN ();    // down
	  vm_tctrl(0);wait(w_rstick);     
    Get_LS_Output = TRUE;
} 

combo BOOSTED_STEPOVER_OLD  {
	if (right_on) dEnd = zone_p + 1;
    else dEnd = zone_p - 1;
	calc_relative_xy(dEnd);
	set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
	  vm_tctrl(0);wait(w_rstick);
	 RA_L_R();
	//set_polar(POLAR_RS, 270 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
	LA_DOWN();
	//LA (move_lx,move_ly);
	  vm_tctrl(0);wait(w_rstick);
	  LA_DOWN();
	  
	//LA (move_lx,move_ly);
	  vm_tctrl(0);wait(1000);
    Get_LS_Output = TRUE;
}

combo test {
    // First movement (same as left stick)
    RA_UP();
    vm_tctrl(0);
    wait(w_rstick);    

    // Second movement (opposite of left stick)
    RA_DOWN();
    vm_tctrl(0);
    wait(w_rstick);    

    vm_tctrl(0);
    wait(600);
}

combo BOOSTED_STEPOVER {
    // Calculate the current zone and set right_on
    zone_p = calc_zone();
    set_right_or_left();

    // Save the current zone
    zone_saver();

    // Calculate the end direction
    if (right_on) dEnd = calc_temp_zone(zone_p + 1);
    else dEnd = calc_temp_zone(zone_p - 1);
    calc_relative_xy(dEnd);

    // First movement (same as left stick)
    RA_UP();
    vm_tctrl(0);
    wait(w_rstick);    

    // Second movement (opposite of left stick)
    RA_DOWN();
    //LA(move_lx, move_ly);
    vm_tctrl(0);
    wait(w_rstick);    

    // Continue holding the left stick
    //LA(move_lx, move_ly);
    vm_tctrl(0);
    wait(1000);
}

int AIM_X;
int AIM_Y;
function INSIDE_BOX_AIM(f_LX, f_LY) {
	if(get_ival(PS4_LX) >= 12) AIM_X = f_LX;
	else if(get_ival(PS4_LX) <= -12)  AIM_X = inv(f_LX);

	if(get_ival(PS4_LY) >= 12) AIM_Y = f_LY;
	else if(get_ival( PS4_LY) <= -12) AIM_Y = inv(f_LY);
}

function set_ds4_led(colour) {
 
    set_led(LED_1, duint8 (colour * 4));
    set_led(LED_2, duint8 ((colour * 4) + 1));
    set_led(LED_3, duint8 ((colour * 4) + 2));
    set_led(LED_4, duint8 ((colour * 4) + 3));
 
}

int KS_EntireScript = FALSE;
function f_set_notify (f_val){
    if(f_val)Vibrate_type = RUMBLE_A;
    else     Vibrate_type = RUMBLE_B;
    combo_run(NOTIFY_cmb);
}
function LED_Color(color) {  
    for( data_indx = 0; data_indx < 3; data_indx++ ) {
        set_led(data_indx,duint8 ((color * 3) + data_indx));
    }
}

int time_to_clear_screen = 3000;
function center_x(f_chars,f_font) {                                                                 
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);  
} 
const string OFF   = "OFF";       
const string ON    = "ON";

   //=======================================
   //  DISPLAY EDIT VALUE ON THE FLY        
   //=======================================
function on_the_fly_display (f_string, f_print, f_val){
    cls_oled(0);  
    line_oled(1,18,127,18,1,1);
    print(f_string, 0, OLED_FONT_MEDIUM, OLED_WHITE, f_print);  
    NumberToString(f_val, FindDigits(f_val));
    time_to_clear_screen  = 2000;
} 


combo CLEAR_SCREEN {     
    wait(20);     
    cls_oled(0); 
}  

combo TURBO {
    set_val(turboBtn, 100);
    wait(5);
    set_val(turboBtn, 0);
    wait(200);
}

combo TURBO2 {
    set_val(XB1_LB, 100);
    wait(600);
    set_val(XB1_LB, 0);
    wait(100);
}

/*=================================================================
 Center X Function (Made By Batts) 
=================================================================
*/
function centerPosition(f_chars,f_font) {
    return (OLED_WIDTH / 2) - ((f_chars * f_font) / 2);
}


int RumblePower = 100;
int Vibrate_type;
combo NOTIFY_cmb {
    set_rumble(Vibrate_type,100);
    wait(300);
    reset_rumble();
    wait(20);
}

int data_indx;

/*
=================================================================
  NumberToString () (Made By Batts)                                                                                                                     
=================================================================
*/   
int bufferIndex;
int charIndex,digitIndex;
function NumberToString(f_val,f_digits) {
    bufferIndex = 1;  
    digitIndex = 10000;
    if(f_val < 0) {                    //--neg numbers
         putc_oled(bufferIndex,45);    //--add leading "-"
         bufferIndex += 1;
         f_val = abs(f_val);
    } 
    for(charIndex = 5; charIndex >= 1; charIndex--) {
        if(f_digits >= charIndex) {
            putc_oled(bufferIndex,(f_val / digitIndex) + 48);
            f_val %= digitIndex;
            bufferIndex ++; 
            if(charIndex == 4) {
                putc_oled(bufferIndex,44);//--add ","
                bufferIndex ++;
            }
        }
        digitIndex /= 10;
    } 
    puts_oled(centerPosition(bufferIndex - 1,OLED_FONT_MEDIUM_WIDTH),38,OLED_FONT_MEDIUM,bufferIndex - 1,OLED_WHITE);
} 
int logVal;
function FindDigits(num) {
   logVal = 0;
   do {
      num /= 10;
      logVal++;
   } while (num);
   return logVal;
}

int stringLength;
function getStringLength(offset) { 
    stringLength = 0;
    do { 
        offset++;
        stringLength++;
    } while (duint8(offset));
    return stringLength;
 }

/*
=================================================================
  Zones RS                                                                                                                    
=================================================================
*/  

const int ZONE_P[][] = {
    //  X,    Y
    {   0, -100 }, // 0 UP
    { 70, -70   }, // 1 Up-Right
    { 100,    0 }, // 2 Right
    { 70,  70   }, // 3 Down right
    {   0,  100 }, // 4 Down
    {-100,  100 }, // 5 Down Left
    {-100,    0 }, // 6 Left
    {-70, -70   }  // 7 Left Up
};  

int move_lx, move_ly, zone_p;
int RS_X, RS_Y, zone_RS;
int rs_val = 35;
int polar_LS;

int zoneRange = 50; // Customizable range for zones 0, 2, 4, and 6

int ACTIVE;                                      
int LX, LY;          // Direction of Left Stick         
int right_on 
int w_rstick = 50;
int Get_LS_Output = TRUE;

define UP         = 0;  
define UP_RIGHT   = 1;  
define RIGHT      = 2;  
define DOWN_RIGHT = 3;  
define DOWN       = 4;  
define DOWN_LEFT  = 5;  
define LEFT       = 6;  
define UP_LEFT    = 7;  
int run;
int dEnd;

const int order[] = {2, 1, 0, 7, 6, 5, 4, 3};
int index;
int sector;
function calc_zone() {
    polar_LS = get_ipolar(POLAR_LS, POLAR_ANGLE);
    index = ((polar_LS + 22.5) % 360) / 45;
    zone_p = order[index];
    return zone_p;
    }
int polar_angle;
function calc_RS() {
    polar_angle = get_ipolar(POLAR_RS, POLAR_ANGLE);
    index = ((polar_angle + 22.5) % 360) / 45;
    zone_RS = order[index];
    return zone_RS;
}

int flick_rs;
int temp_zone;
function calc_temp_zone(user_zone) {
    temp_zone = user_zone;
    if (temp_zone < 0) temp_zone = 8 - abs(user_zone);
    else if (temp_zone >= 8) temp_zone = user_zone - 8
        return temp_zone;
}

function calc_relative_xy(d) {
    if(d < 0 ) d = 8 - abs(d);
    else if(d >= 8) d = d - 8;
    move_lx = ZONE_P [d][0];// X
    move_ly = ZONE_P [d][1];// Y
}

 //--------------------------------------------------------------
//      Analog Functions                                        
//--------------------------------------------------------------
int LS_Sens_Corect;  
function RA (xx,yy){ 
	set_val(SKILL_STICK_X,xx);
	set_val(SKILL_STICK_Y,yy);
}  
function LA_DOWN () {               
		set_polar(POLAR_LS, 180 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767); 
} 
function LA (x,y){ 
	set_val(MOVE_X,x);
	set_val(MOVE_Y,y);
}                  
function LA_L_R() {          
	if(right_on) {// right      
		  set_polar(POLAR_LS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);    
	}                           
	else {       //  left       
		set_polar(POLAR_LS, 270 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767); 
    }                         
}                              
function RA_L_R() {             
	if(right_on) {// right          
		  set_polar(POLAR_RS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);    
	}                           
	else {       //  left       
		set_polar(POLAR_RS, 270 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767); 
    }                                
}                                    
function RA_OPP() {                  
	if(!right_on) {// right             
		set_val(SKILL_STICK_X,inv(LY));   
		set_val(SKILL_STICK_Y,LX);        
	}                                   
	else {       //  left               
	    set_val(SKILL_STICK_X,LY );     
	    set_val(SKILL_STICK_Y,inv(LX)); 
    }                                
}                                    
function RA_UP () {                  
		set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);       
}                                   
function RA_DOWN () {               
		set_polar(POLAR_RS, 180 -  get_ipolar(POLAR_LS, POLAR_ANGLE), 32767); 
}                                  
function RA_ZERO () {             
	set_val(SKILL_STICK_X,0 );      
	set_val(SKILL_STICK_Y,0 );     
}

function set_right_or_left () {
    right_on = FALSE; 
    if (zone_p == 4 || zone_p == 3 || zone_p == 7 ) { 
        right_on = TRUE; 
    } /// 
}

function zone_saver() {
dEnd = zone_p
calc_relative_xy(dEnd);
LX = move_lx;
LY = move_ly;
}

combo ButtonPress {
    wait(250);
    comboStage = 2;
    wait(130);
    comboStage = 3;
}

function move_down() {
        set_polar(POLAR_RS, 180 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);

}

function move_up() {
        set_polar(POLAR_RS, 360 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_left() {
        set_polar(POLAR_RS, 270 - get_ipolar(POLAR_LS, POLAR_ANGLE), 32767);
}

function move_right() {
        set_polar(POLAR_RS, 90 - get_polar(POLAR_LS, POLAR_ANGLE), 32767);
}

combo TEST {      
    move_down ();         // up   
	wait(100);
	wait(50);
	move_down ();
	wait(100); 
	wait(50);
	set_val(XB1_LT, 100);
	set_val(XB1_RT, 100);
	wait(200);
//move_left ();
//wait(100);
//move_down ();
//wait(100);

}  


combo TEST_LR { 
	move_left ();
	wait(300);
	//wait(50);
    move_down ();   
	wait(100);
	//wait(50);
	move_right ();
	wait(300); 
	wait(50);
	//wait(100);
}  

combo TEST_RL { 
	move_right ();
	wait(300);
	//wait(50);
    move_down ();   
	wait(100);
	//wait(50);
	move_left ();
	wait(300); 
	wait(50);
	//wait(100);
}  

combo R2_at_direction_change {
    set_val(PS4_L1, 100);set_val(PS4_R1, 100); // Activate R2 button
    wait(60); // Wait for 60 ms
    set_val(PS4_L1, 0);set_val(PS4_R1, 0); // Release R2 button
    wait(100); // Wait for 100 ms before allowing the next activation
} 