
  // ============  HOW it WORKs ============// : 
//1 - the script triggered by pressing Shot button  while nothing else pressed , like when no sprnt or L2 or L1 ..etc .
//2-  it will generate fixed power thaat suitable for inside 18 yards box finishing ,,
//    so whenever you press shot button whatever the duration of your press the power is stay the same .
//3 - it well lock the corner of where you aim to grant a perfect aim as possible .
  
  //==================================================// 
   // MAKE SURE NO Shooting SCRIPTS Generated From FIFA Generator // 
   
   //============ COPY THis TO MAIN SECTION ============//
    
      if( event_press(ShotBtn ) && !get_val(FinesseShot)   && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn)  ){                      
    	
    	 
      set_val(ShotBtn,0);
     
	  combo_restart(Inside_Box_Finishing); 
	  }
  

  // ============ END OF COPY TO MAIN SECTION ============ //
  
  
  
  
   // ============ COPY THis TO COMBOS SECTION ============ //
    

 
  // ============ END OF COPY TO COMBOS SECTION ============//
   
combo Inside_Box_Finishing {
    CORNER_FIX_MOVE()
    RA_L_R() ; 
    set_val(ShotBtn, 0);
    wait(100);
    set_val(PS4_L3,100);
    set_val(ShotBtn, 100);  
    wait(116);
    LA ((LX),inv(LY)); 
    set_val(ShotBtn, 100);
    wait(50);
    set_val(ShotBtn, 100);
    set_val(SprintBtn,100);
    LA ((LX),0);
    wait(35);
    INSIDE_BOX_AIM();
    set_val(ShotBtn, 0);
    set_val(SprintBtn,100);
    set_val(PaceCtrol,100);
    wait(80)
    set_val(PaceCtrol,0);
    set_val(SprintBtn,100);
    set_val(PS4_L3,100);
    INSIDE_BOX_AIM() ;
    wait(450);     
} 


 function INSIDE_BOX_AIM() { 
     
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
              
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 
 }

function CORNER_FIX_MOVE() {
     
   
   
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right Corner
	{   right_on = FALSE
		set_val(PS4_LX,  100);
		set_val(PS4_LY,  -16);
		 
	}
	      
	      
	if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right Corner
	{   right_on = TRUE
		set_val(PS4_LX, 100); 
		set_val(PS4_LY,  16);
		 
	}
	
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left Corner
	{   right_on = TRUE 
		 
		set_val(PS4_LX, -100); 
		set_val(PS4_LY,  -16);
	}
	
	if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left Corner
	{   right_on = FALSE
	set_val(PS4_LX, -100);
	set_val(PS4_LY,  16);
		  
		 
	}
	}
  
  
  
  