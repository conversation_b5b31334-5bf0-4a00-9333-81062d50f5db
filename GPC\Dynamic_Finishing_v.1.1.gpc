 
  
    //========================================
    // *** ULTIMATE FINISHING ***
    //========================================
    //MAIN Section//
          if ( combo_running(Inside_Box_Finishing) && get_val(PassBtn) ) {  /// FakeShot Support avoid Conflictions
    combo_stop(Inside_Box_Finishing);
    }  
    
    
    if(event_release(CrossBtn)){
        after_cross_timer = 4000;
    }
    
     if(event_release(SprintBtn)){
        after_sprint_timer = 1000;
    }
    
    if(after_cross_timer){
        after_cross_timer -=get_rtime();
    }
    
      if(after_sprint_timer){
        after_sprint_timer -=get_rtime();
    }
                  
     if(!get_val(FinesseShot) && !get_val(PaceCtrol) && !get_val(PlayerRun) && !get_val(SprintBtn) ){
 
         if( event_press(ShotBtn) && after_cross_timer <= 0 ){
       
         set_val(ShotBtn,0);
         INSIDE_BOX_AIM();     
            
                  if( after_sprint_timer > 900  ) {
            UltimatePower = 205 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                  if( after_sprint_timer < 900 && after_sprint_timer > 800  ) {
            UltimatePower = 200 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
            
                    if( after_sprint_timer < 800 && after_sprint_timer > 700  ) {
            UltimatePower = 195 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                     if( after_sprint_timer < 700 && after_sprint_timer > 600 ) {
            UltimatePower = 190 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                       if( after_sprint_timer < 700 && after_sprint_timer > 600 ) {
            UltimatePower = 185 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                         if( after_sprint_timer < 600 && after_sprint_timer > 500 ) {
            UltimatePower = 180 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                            if( after_sprint_timer < 500 && after_sprint_timer > 250 ) {
            UltimatePower = 175 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
                              if( after_sprint_timer < 250 && after_sprint_timer > 0 ) {
            UltimatePower = 170 ;
            DrivenShot = 0 ;
            drval=0;
            combo_restart(Inside_Box_Finishing); 
            }
            
            
                            if( after_sprint_timer <= 0  ) {
            UltimatePower = 200 ;
            DrivenShot = 17 ;
            drval=100;
            combo_restart(Inside_Box_Finishing); 
        
            }
          
        }
    } 
  

//COMBO SECTION//
////////////////
///////////////

int after_cross_timer;  
int after_sprint_timer; 
int UltimatePower;
int DrivenShot;
int drval;


 combo Inside_Box_Finishing {
    set_val(ShotBtn, 100);
    INSIDE_BOX_AIM();
    wait(UltimatePower); 
    set_val(ShotBtn, 100);
    set_val(PlayerRun, drval);
    set_val(FinesseShot, drval);
    INSIDE_BOX_AIM();
    wait(DrivenShot);  
    set_val(ShotBtn, 0);
    INSIDE_BOX_AIM();
    wait(150);
    set_val(PS4_R3,100);
    set_val(SprintBtn,100);
    INSIDE_BOX_AIM();
    wait(50);
    set_val(PS4_R3,0);
    set_val(SprintBtn,0);
    set_val(PaceCtrol,100);
    INSIDE_BOX_AIM() ;
    drval=0;
    UltimatePower=0;
    wait(600); 
} 
    
  
  
  
 function INSIDE_BOX_AIM() { 
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) < -15 ) // TOP Right 
     {  
         LA (100, -100);
     }
     if(get_val(PS4_LX) > 15 && get_val(PS4_LY) > 15) // Bottom Right 
     { 
         LA (100, 100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) < -15) // TOP Left 
     { 
         LA (-100 , -100);
     }
     if(get_val(PS4_LX) < -15 && get_val(PS4_LY) > 15) // Bottom Left 
     {
         LA (-100 ,  100);
     }
 }