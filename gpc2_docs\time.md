# Time Functions

system_time
system_time - Get current system time

Description
uint32 system_time();
Get the time as the number of milli-seconds since the device has power-up, or last reset.

The system time overflows to zero after 49 days, 17 hours, 2 minutes, 47 seconds and 295 milli-seconds.
Return Value
Returns an uint32 value representing the number of milli-seconds that has being passed since power-up, or last reset.

Examples
Example #1 system_time() example
init {
    printf("Current system time: %ldms", system_time());
}

elapsed_time
elapsed_time - Get elapsed time

Description
uint32 elapsed_time();
Get the elapsed time as the number of milli-seconds since previous interaction of main.

Return Value
Returns the number of milli-seconds that has being passed since previous interaction.

Examples
Example #1 elapsed_time() example
uint32 time_counter;
 
main {
    time_counter += elapsed_time();
    if(time_counter >= 10000) {
        time_counter = 0;
        combo_run(PressButton16);
    }
}
 
combo PressButton16 {
    set_val(BUTTON_16, 100.0);
    wait(100);
}

output_time
output_time - Get output elapsed time

Description
uint32 output_time();
Get the elapsed time as the number of milli-seconds since the last output data packet was sent.

This time information can help synchronize inputs, combos and macros with the game frame rate.
Return Value
Returns the number of milli-seconds that has being passed since the last output data packet was sent.

Examples
Example #1 output_time() example
main {
    if(get_val(BUTTON_2)) {
        if(output_time() == 0) {
            macro_run("Example.gmk");
        }
    }
}