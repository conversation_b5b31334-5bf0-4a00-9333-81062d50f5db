////////////////////////////////////////////////////////////////////////////////
// LS_Dribbling_CoPilot.gpc
// Enhanced left stick dribbling script with improved mathematics and mechanics
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// GLOBAL VARIABLES
////////////////////////////////////////////////////////////////////////////////
int LX, LY;                  // Raw stick inputs
int scaled_x, scaled_y;      // Final outputs after processing
int prev_x, prev_y;         // Previous frame values for smoothing
int velocity_x, velocity_y; // Velocity tracking for acceleration

// Tunable Constants
int DEADZONE = 5;          // Inner deadzone threshold (0-100)
int MAX_INPUT = 70;        // Maximum stick value
int SMOOTH_FACTOR = 0;     // Input smoothing (0-100)
int ACCEL_THRESHOLD = 10;   // Speed threshold for acceleration (0-100)
int SENSITIVITY = 60;      // Base sensitivity percentage (100 = normal)

// Octagon shaping constants
int INV_SQRT2_NUM = 700;    // For octagonal corners (700-725 range)
int INV_SQRT2_DEN = 1024;   // Denominator for diagonal scaling

// Processing variables
int sign_x, sign_y, abs_x, abs_y;
int L_inf, L_1, octNorm;
int accel_factor;
int smooth_x, smooth_y;
int scaled_L1;              // For octagonal shape calculation
int sensitivity_scaled;      // For sensitivity calculations
int scaledNorm;             // For proper stick scaling
int raw_scaled_x, raw_scaled_y; // Pre-smoothing values

////////////////////////////////////////////////////////////////////////////////
// MAIN LOOP
////////////////////////////////////////////////////////////////////////////////
main {
    // Read raw inputs
    LX = get_val(PS4_LX);
    LY = get_val(PS4_LY);

    // Process stick input through our enhanced pipeline
    process_stick_input();

    // Write final values back
    set_val(PS4_LX, scaled_x);
    set_val(PS4_LY, scaled_y);

    // Store current values for next frame
    prev_x = scaled_x;
    prev_y = scaled_y;
}

////////////////////////////////////////////////////////////////////////////////
// PROCESSING PIPELINE
////////////////////////////////////////////////////////////////////////////////
function process_stick_input() {
    // 1. Determine signs and absolute values
    if(LX >= 0) sign_x = 1; else sign_x = -1;
    if(LY >= 0) sign_y = 1; else sign_y = -1;
    abs_x = abs(LX);
    abs_y = abs(LY);

    // 2. Apply octagonal mapping
    L_inf = max(abs_x, abs_y);
    L_1 = abs_x + abs_y;
    
    // Scale L_1 for octagonal shape (improved scaling)
    scaled_L1 = (L_1 * 1024) / (1024 + INV_SQRT2_NUM);
    octNorm = max(L_inf, scaled_L1);

    // 3. Apply deadzone
    if(octNorm <= DEADZONE) {
        scaled_x = 0;
        scaled_y = 0;
        return;
    }

    // 4. Clamp maximum input
    if(octNorm > MAX_INPUT) {
        octNorm = MAX_INPUT;
    }

    // 5. Scale from [DEADZONE..MAX_INPUT] -> [0..MAX_INPUT]
    scaledNorm = (octNorm - DEADZONE) * MAX_INPUT / (MAX_INPUT - DEADZONE);

    // 6. Calculate acceleration factor
    accel_factor = calc_acceleration(scaledNorm);

    // 7. Apply sensitivity and acceleration
    sensitivity_scaled = (SENSITIVITY * accel_factor) / 100;
    
    // 8. Compute properly scaled outputs maintaining the octagonal shape
    raw_scaled_x = (abs_x * scaledNorm / octNorm) * sensitivity_scaled / 100;
    raw_scaled_y = (abs_y * scaledNorm / octNorm) * sensitivity_scaled / 100;

    // 9. Apply smoothing
    smooth_x = apply_smoothing(raw_scaled_x, prev_x);
    smooth_y = apply_smoothing(raw_scaled_y, prev_y);

    // 10. Restore signs and clamp to MAX_INPUT
    scaled_x = clamp(smooth_x * sign_x, -MAX_INPUT, MAX_INPUT);
    scaled_y = clamp(smooth_y * sign_y, -MAX_INPUT, MAX_INPUT);
}

////////////////////////////////////////////////////////////////////////////////
// HELPER FUNCTIONS
////////////////////////////////////////////////////////////////////////////////
function calc_acceleration(int speed) {
    if(speed <= ACCEL_THRESHOLD) {
        return 100; // No acceleration below threshold
    }
    // Progressive acceleration above threshold
    return 100 + ((speed - ACCEL_THRESHOLD) * 50) / (MAX_INPUT - ACCEL_THRESHOLD);
}

function apply_smoothing(int current, int previous) {
    return (current * (100 - SMOOTH_FACTOR) + previous * SMOOTH_FACTOR) / 100;
} 