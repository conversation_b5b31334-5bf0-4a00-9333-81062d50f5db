int lt_counter2;    // For LT oscillation (multiplied by 10 for smooth movement)
int going_up2;      // For LT direction
int combo_active2;  // Track if LT+RT is pressed
int rt_timer2;      // For RT spam
int rb_timer2;      // For RB timing

init {
    lt_counter2 = 500;  // Start at 50 (500/10) for LT
    going_up2 = 1;
    combo_active2 = 0;
    rt_timer2 = 0;
    rb_timer2 = 0;
}

main {

	if(get_val(XB1_RT)){
      set_val(XB1_RT, lt_counter2/10);  // Divide by 10 to get actual trigger value
        if(going_up2) {
            lt_counter2 = lt_counter2 + 60;  // Increment by 5 (0.5 in actual trigger value)
            if(lt_counter2 >= 1000) {  // 100 * 10
                going_up2 = 0;
            }
        } else {
            lt_counter2 = lt_counter2 - 60;  // Decrement by 5 (0.5 in actual trigger value)
            if(lt_counter2 <= 500) {  // 50 * 10
                going_up2 = 1;
            }
        }
	 }
	 
	 if(get_val(XB1_LT)){
        set_val(XB1_LT, lt_counter2/10);  // Divide by 10 to get actual trigger value
        if(going_up2) {
            lt_counter2 = lt_counter2 + 60;  // Increment by 5 (0.5 in actual trigger value)
            if(lt_counter2 >= 1000) {  // 100 * 10
                going_up2 = 0;
            }
        } else {
            lt_counter2 = lt_counter2 - 60;  // Decrement by 5 (0.5 in actual trigger value)
            if(lt_counter2 <= 500) {  // 50 * 10
                going_up2 = 1;
            }
        }
    }
} 