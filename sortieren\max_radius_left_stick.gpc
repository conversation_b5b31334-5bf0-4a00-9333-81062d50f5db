// <PERSON>dius Left Stick Script for Xbox
// This script sets the left stick to maximum radius when moved

// Constants
define PI = 314159;  // Scaled by 100000 to avoid floating point
define PI_HALF = 157080;  // PI/2 scaled
define TWO_PI = 628318;  // 2*PI scaled
define MAX_RADIUS = 32767;  // Maximum radius value
define SCALE = 100000;  // Scaling factor for fixed-point math
define PI_SCALE = 314;  // PI/1000 for built-in trig functions
define PI_HALF_SCALE = 157;  // (PI/2)/1000 for built-in trig functions
define TWO_PI_SCALE = 628;  // (2*PI)/1000 for built-in trig functions

// Global variables
define currentX = 0;
define currentY = 0;
define angle = 0;
define newX = 0;
define newY = 0;
define angle_out = 0;
define temp_angle = 0;
define cos_val = 0;
define sin_val = 0;
define scaled_angle = 0;

// Fixed-point trigonometric functions
function _COS_
    // Normalize angle to [0, 2π]
    temp_angle = arg0 + TWO_PI
    while(temp_angle >= TWO_PI) 
        temp_angle = temp_angle - TWO_PI
    end
    
    if(temp_angle < 0) 
        temp_angle = temp_angle + TWO_PI
    end
    
    // Scale down for built-in cos function
    temp_angle = temp_angle / SCALE
    
    // Use lookup table approximation for better performance
    if(temp_angle <= PI_HALF_SCALE) 
        return cos temp_angle
    end
    if(temp_angle <= PI_SCALE) 
        temp_angle = PI_SCALE - temp_angle
        return -cos temp_angle
    end
    if(temp_angle <= PI_SCALE + PI_HALF_SCALE) 
        temp_angle = temp_angle - PI_SCALE
        return -cos temp_angle
    end
    temp_angle = TWO_PI_SCALE - temp_angle
    return cos temp_angle
end

function _SIN_() {
    temp_angle = PI_HALF - arg0;
    return _COS_ temp_angle;
}

function atan2_angle() {
    if(arg0 >= 0 && arg1 > 0) {
        angle_out = -90000;
    }
    else if(arg0 < 0 && arg1 >= 0) {
        angle_out = 90000;
    }
    else if(arg0 <= 0 && arg1 < 0) {
        angle_out = -270000;
    }
    else {
        angle_out = 270000;
    }
    
    temp_angle = abs arg0;
    arg0 = temp_angle;
    temp_angle = abs arg1;
    arg1 = temp_angle;
    
    if(arg0 < arg1) {
        temp_angle = arg0 * 45000;
        angle_out = angle_out + temp_angle / arg1;
    }
    else if(arg0 > arg1) {
        temp_angle = arg1 * 45000;
        angle_out = angle_out + 90000 - temp_angle / arg0;
    }
    else {
        angle_out = angle_out + 45000;
    }
    
    temp_angle = abs angle_out;
    return temp_angle / 1000;
}

main {
    // Get current stick values
    currentX = get_val XB1_LX;
    currentY = get_val XB1_LY;
    
    // Check if left stick is being moved
    temp_angle = abs currentX;
    if(temp_angle > 0) {
        // Calculate angle and apply maximum radius
        angle = atan2_angle currentY currentX;
        
        // Convert angle to radians and scale
        temp_angle = angle * PI;
        scaled_angle = temp_angle / 180000;
        
        // Calculate sine and cosine
        cos_val = _COS_ scaled_angle;
        sin_val = _SIN_ scaled_angle;
        
        // Set the stick to maximum radius while maintaining the angle
        temp_angle = cos_val * MAX_RADIUS;
        newX = temp_angle / SCALE;
        temp_angle = sin_val * MAX_RADIUS;
        newY = temp_angle / SCALE;
        
        // Apply the new values to the left stick
        set_val XB1_LX newX;
        set_val XB1_LY newY;
        return;
    }
    
    temp_angle = abs currentY;
    if(temp_angle > 0) {
        // Calculate angle and apply maximum radius
        angle = atan2_angle currentY currentX;
        
        // Convert angle to radians and scale
        temp_angle = angle * PI;
        scaled_angle = temp_angle / 180000;
        
        // Calculate sine and cosine
        cos_val = _COS_ scaled_angle;
        sin_val = _SIN_ scaled_angle;
        
        // Set the stick to maximum radius while maintaining the angle
        temp_angle = cos_val * MAX_RADIUS;
        newX = temp_angle / SCALE;
        temp_angle = sin_val * MAX_RADIUS;
        newY = temp_angle / SCALE;
        
        // Apply the new values to the left stick
        set_val XB1_LX newX;
        set_val XB1_LY newY;
    }
}
