// Octagonal boundary for analog stick
// Creates 8-directional movement with precise angles

// Constants for stick identification
define stickX = XB1_LX;  // Left stick X axis
define stickY = XB1_LY;  // Left stick Y axis

// Constants for octagon shape
define MAX_VAL = 100;    // Maximum stick value
define RATIO = 60;       // Ratio for flat sides (percentage)

int x, y;
int abs_x, abs_y;
int max_component;
int scale;

main {
    // Get current stick values
    x = get_val(stickX);
    y = get_val(stickY);
    
    if(abs(x) > 0 || abs(y) > 0) {
        // Get absolute values
        abs_x = abs(x);
        abs_y = abs(y);
        
        // Find the larger component
        if(abs_x > abs_y) {
            max_component = abs_x;
        } else {
            max_component = abs_y;
        }
        
        // Calculate scale based on max component
        scale = MAX_VAL;
        if(max_component > 0) {
            // If we're closer to cardinal direction, reduce scale
            if(abs_x > abs_y * 2 || abs_y > abs_x * 2) {
                scale = RATIO;
            }
        }
        
        // Apply scaling to maintain direction
        x = (x * scale) / MAX_VAL;
        y = (y * scale) / MAX_VAL;
        
        // Set modified values
        set_val(stickX, x);
        set_val(stickY, y);
    }
}