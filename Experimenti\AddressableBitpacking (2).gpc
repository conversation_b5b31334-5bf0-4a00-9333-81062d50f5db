
// ADRESSABLE BITPACKING SAMPLE
// ----------------------------
// In this example, we write values to addresses in memory, read them, then display in TRACE.


// VALUE 1
define address1 =  0;       // The address where the value is stored (0 -> 2048) (64 x 32 = 2048)
define value1   =  33;      // The value you are storing
define min1     = -100;     // The smallest your value could be
define max1     =  100;     // the largest your value could be
define def1     =  0;       // The default value to be returned if there is an error while reading

// VALUE 2
define address2 =  8;       // -100 -> 100 takes 8 bits, so next value needs to be at address 8
define value2   = -21;
define min2     = -100;
define max2     =  100;
define def2     =  0;


init
{
	// Write to memory
	write_eeprom(address1, value1, min1, max1);
	write_eeprom(address2, value2, min2, max2);
	
	// Read from memory and display in TRACE
	set_val(TRACE_1, read_eeprom(address1, min1, max1, def1));
	set_val(TRACE_2, read_eeprom(address2, min2, max2, def2));
}






 // ADDRESSABLE BIT PACKING //
//=========================//

// NOTE: this is an adapted version of Swizzy's bit packing.
//       Adapted by YuFow

// WRITE_EEPROM() - write a value between min and max at a bit address in eeprom memory
function write_eeprom(int address, int val, int min, int max) {
	// INITIALISE
	spvar_current_slot = SPVAR_1 + address / 32;
	spvar_current_bit = address % 32;
	spvar_bits = get_bit_count2(min, max);
	spvar_current_value = read_spvar_slot(spvar_current_slot) & make_full_mask(spvar_current_bit);

	// PACK
	val = clamp(val, min, max); 
	if (is_signed2(min, max)) {
		val = pack_i(val, spvar_bits); 
	}
	
	// HANDLE OVERFLOW
	val = val & make_full_mask(spvar_bits); 
	if (spvar_bits > 32 - spvar_current_bit) {
		spvar_current_value = spvar_current_value | (val << spvar_current_bit);
		set_pvar(spvar_current_slot, spvar_current_value); 
		spvar_current_slot++;
		spvar_bits -= (32 - spvar_current_bit); 
		val = val >> (32 - spvar_current_bit); 
		spvar_current_bit = 0; 
		spvar_current_value = 0; 
	}
	
	// WRITE
	spvar_current_value = read_spvar_slot(spvar_current_slot) & make_save_mask(spvar_current_bit, spvar_bits);
	spvar_current_value = spvar_current_value | (val << spvar_current_bit); 
	set_pvar(spvar_current_slot, spvar_current_value); 
}


// READ_EEPROM() - Read a value between min and max at a bit address in eeprom memory
function read_eeprom(int address, int min, int max, int def) {
	// INITIALISE
	spvar_current_slot = SPVAR_1 + address / 32;
	spvar_current_bit = address % 32;
	spvar_bits = get_bit_count2(min, max);
	spvar_current_value = (read_spvar_slot(spvar_current_slot) >> spvar_current_bit) & make_full_mask(spvar_bits); 
	
	// READ OVERFLOW FROM NEXT SPVAR
	if (spvar_bits > 32 - spvar_current_bit) {
		spvar_tmp = read_spvar_slot(spvar_current_slot + 1);
		spvar_tmp = spvar_tmp & make_full_mask(spvar_bits - (32 - spvar_current_bit));
		spvar_tmp = spvar_tmp << (32 - spvar_current_bit);
		spvar_current_value = (spvar_current_value & make_full_mask(32 - spvar_current_bit)) | spvar_tmp;
	}
	
	// UNPACK
	spvar_current_bit += spvar_bits; 
	spvar_current_value = spvar_current_value & make_full_mask(spvar_bits); 	
	if (is_signed2(min, max)) {
		spvar_current_value = unpack_i(spvar_current_value, spvar_bits);
	}
	if (spvar_current_value < min || spvar_current_value > max) {
		return def; 
	}
	
	return spvar_current_value;
}


// BIT FUNCTIONS

int spvar_current_bit,
	spvar_current_slot,	
	spvar_current_value,
	spvar_tmp,
	spvar_bits;

function get_bit_count(val) {
	spvar_tmp = 0; 
	while (val) { 
		spvar_tmp++; 
		val = abs(val >> 1); 
	}
	return spvar_tmp;
}

function get_bit_count2(val1, val2) {	
	spvar_tmp = max(get_bit_count(val1), get_bit_count(val2)); 
	if (is_signed2(val1, val2)) { 
		spvar_tmp++; 
	}
	return spvar_tmp;
}

function is_signed2(val1, val2) { return val1 < 0 || val2 < 0; }

function make_sign(bits) { return 1 << clamp(bits - 1, 0, 31); }

function make_full_mask(bits) {	
	if (bits == 32) {
		return -1;
	}
	return 0x7FFFFFFF >> (31 - bits); 
}

function make_sign_mask(bits) { return make_full_mask(bits - 1); }

function pack_i(val, bits) {
	if (val < 0) { 
		return (abs(val) & make_sign_mask(bits)) | make_sign(bits); 
	}
	return val & make_sign_mask(bits); 
}

function unpack_i(val, bits) {
	if (val & make_sign(bits)) { 
		return 0 - (val & make_sign_mask(bits)); 
	}
	return val & make_sign_mask(bits);
}

function read_spvar_slot(slot) { return get_pvar(slot, 0x80000000, 0x7FFFFFFF, 0); }

function make_save_mask(int spvar_current_bit, int spvar_bits) { return ~(make_full_mask(spvar_bits) << (spvar_current_bit)); }

 // END OF BIT PACKING //
//====================//
